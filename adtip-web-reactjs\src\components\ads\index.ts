// Advertisement Components and Utilities Export
// Web implementation of ad system similar to React Native app

// Import components for default export
import BannerAdComponent from './BannerAdComponent';
import RectangleAdComponent from './RectangleAdComponent';

// Ad Components
export { BannerAdComponent, RectangleAdComponent };

// Ad Tracking
export {
  adTracker,
  trackAdLoaded,
  trackAdFailed,
  trackAdOpened,
  trackAdClosed,
  trackAdClicked,
  trackAdImpression,
  trackAdReward
} from './AdTracker';

// Import adTracker for default export
import { adTracker } from './AdTracker';

// Types
export type { AdEvent, AdMetrics } from './AdTracker';

// Ad Configuration
export const AD_CONFIG = {
  // Google AdSense configuration
  ADSENSE_CLIENT_ID: import.meta.env.VITE_GOOGLE_ADSENSE_CLIENT_ID || 'ca-app-pub-3940256099942544',
  
  // Test ad unit IDs (replace with real ones in production)
  TEST_UNITS: {
    BANNER: 'ca-app-pub-3940256099942544/6300978111',
    RECTANGLE: 'ca-app-pub-3940256099942544/6300978111',
    LEADERBOARD: 'ca-app-pub-3940256099942544/6300978111',
  },
  
  // Production ad unit IDs (set via environment variables)
  PRODUCTION_UNITS: {
    BANNER: import.meta.env.VITE_AD_UNIT_BANNER,
    RECTANGLE: import.meta.env.VITE_AD_UNIT_RECTANGLE,
    LEADERBOARD: import.meta.env.VITE_AD_UNIT_LEADERBOARD,
  },
  
  // Ad placement configuration
  PLACEMENT: {
    // Show rectangle ad after every N posts
    RECTANGLE_POST_INTERVAL: 3,
    // Show banner ad at top/bottom of pages
    BANNER_POSITIONS: ['top', 'bottom'],
    // Minimum time between ad impressions (seconds)
    MIN_IMPRESSION_INTERVAL: 30,
  },
  
  // Viewability settings
  VIEWABILITY: {
    // Minimum percentage of ad that must be visible
    THRESHOLD: 0.5,
    // Minimum time ad must be visible (milliseconds)
    MIN_DURATION: 1000,
  },
} as const;

// Utility functions
export const getAdUnitId = (adType: 'banner' | 'rectangle' | 'leaderboard'): string => {
  const isProduction = import.meta.env.PROD;
  
  if (isProduction) {
    switch (adType) {
      case 'banner':
        return AD_CONFIG.PRODUCTION_UNITS.BANNER || AD_CONFIG.TEST_UNITS.BANNER;
      case 'rectangle':
        return AD_CONFIG.PRODUCTION_UNITS.RECTANGLE || AD_CONFIG.TEST_UNITS.RECTANGLE;
      case 'leaderboard':
        return AD_CONFIG.PRODUCTION_UNITS.LEADERBOARD || AD_CONFIG.TEST_UNITS.LEADERBOARD;
      default:
        return AD_CONFIG.TEST_UNITS.BANNER;
    }
  }
  
  // Use test units in development
  return AD_CONFIG.TEST_UNITS[adType.toUpperCase() as keyof typeof AD_CONFIG.TEST_UNITS];
};

export const shouldShowAd = (postIndex: number, adType: 'rectangle' | 'banner'): boolean => {
  switch (adType) {
    case 'rectangle':
      // Show rectangle ad after every 3rd post (starting from post 2)
      return (postIndex + 1) % AD_CONFIG.PLACEMENT.RECTANGLE_POST_INTERVAL === 0 && postIndex > 0;
    case 'banner':
      // Show banner ads on specific positions
      return true; // Let the component decide based on position
    default:
      return false;
  }
};

// Initialize AdSense
export const initializeAdSense = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if already loaded
    if (typeof window !== 'undefined' && (window as any).adsbygoogle) {
      resolve();
      return;
    }

    // Check if script already exists
    if (document.querySelector('script[src*="adsbygoogle.js"]')) {
      // Wait for it to load
      const checkLoaded = () => {
        if ((window as any).adsbygoogle) {
          resolve();
        } else {
          setTimeout(checkLoaded, 100);
        }
      };
      checkLoaded();
      return;
    }

    // Load AdSense script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${AD_CONFIG.ADSENSE_CLIENT_ID}`;
    script.crossOrigin = 'anonymous';
    
    script.onload = () => {
      // Initialize adsbygoogle array
      (window as any).adsbygoogle = (window as any).adsbygoogle || [];
      resolve();
    };
    
    script.onerror = () => {
      reject(new Error('Failed to load AdSense script'));
    };
    
    document.head.appendChild(script);
  });
};

export default {
  BannerAdComponent,
  RectangleAdComponent,
  adTracker,
  AD_CONFIG,
  getAdUnitId,
  shouldShowAd,
  initializeAdSense,
};
