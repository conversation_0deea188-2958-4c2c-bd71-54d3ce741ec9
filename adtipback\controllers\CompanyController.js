let companyService = require("../services/CompanyService");
const utils = require("../utils/utils");

module.exports = {
  //Post Function
  //checkCompanyNameExist
  //addCompanyGst
  //updateCompanyNew
  updateCompanyNew: (req, res, next) => {
    companyService
      .updateCompanyNew(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addCompanyGst: (req, res, next) => {
    companyService
      .addCompanyGst(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  checkCompanyNameExist: (req, res, next) => {
    companyService
      .checkCompanyNameExist(req.params.name)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  savePost: (req, res, next) => {
    companyService
      .savePost(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  createCompany: (req, res, next) => {
    companyService
      .createCompany(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveCompany: (req, res, next) => {
    companyService
      .saveCompany(req.body, req.files)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            if (data.coverImage != null)
              data.coverImage = `${req.headers.host}/api/photo/${data.coverImage}`;
            if (data.profileImage != null)
              data.profileImage = `${req.headers.host}/api/photo/${data.profileImage}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getCompanyList: (req, res, next) => {
    companyService
      .getCompanyList(req.params.userId)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            if (data.coverFilename != null)
              data.coverImage = `${req.headers.host}/api/photo/${data.coverFilename}`;
            if (data.profileFilename != null)
              data.profileImage = `${req.headers.host}/api/photo/${data.profileFilename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getCompanyPost: (req, res, next) => {
    companyService
      .getCompanyPost(req.params.companyId)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getPostDetails: (req, res, next) => {
    return companyService
      .getPostDetails(req.params.postId)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getOtherCompanyList: (req, res, next) => {
    companyService
      .getOtherCompanyList(req.params.userId, req.params.isfollow)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.is_follow =
              data.createdby === req.params.userId && data.is_follow == 1
                ? 1
                : data.is_follow;
            if (data.coverFilename != null)
              data.coverImage = `${req.headers.host}/api/photo/${data.coverFilename}`;
            if (data.profileFilename != null)
              data.profileImage = `${req.headers.host}/api/photo/${data.profileFilename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAllCompanyList: (req, res, next) => {
    companyService
      .getAllCompanyList(req.params.userId)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.is_follow =
              data.createdby === req.params.userId && data.is_follow == 1
                ? 1
                : data.is_follow;
            if (data.coverFilename != null)
              data.coverImage = `${req.headers.host}/api/photo/${data.coverFilename}`;
            if (data.profileFilename != null)
              data.profileImage = `${req.headers.host}/api/photo/${data.profileFilename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getcompany: (req, res, next) => {
    companyService
      .getCompany(req.params.id, req.params.userId)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            if (data.coverFilename != null)
              data.coverImage = `${req.headers.host}/api/photo/${data.coverFilename}`;
            if (data.profileFilename != null)
              data.profileImage = `${req.headers.host}/api/photo/${data.profileFilename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  updateCompany: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    companyService
      .updateCompany(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          // result.data.forEach(data => {
          //     if(data.coverImage != null)data.coverImage=`${req.headers.host}/api/photo/${data.coverImage}`
          //     if(data.profileImage != null)data.profileImage=`${req.headers.host}/api/photo/${data.profileImage}`
          // });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  updateCompanyDetails: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    companyService
      .updateCompanyDetails(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          // result.data.forEach(data => {
          //     if(data.coverImage != null)data.coverImage=`${req.headers.host}/api/photo/${data.coverImage}`
          //     if(data.profileImage != null)data.profileImage=`${req.headers.host}/api/photo/${data.profileImage}`
          // });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  deletecompany: (req, res, next) => {
    companyService
      .deleteCompany(req.body.id)
      .then((result) => {
        if (result && result.status === 200) {
          3;
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deletePost: (req, res, next) => {
    companyService
      .deletePost(req.body.id)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  generateQRCode: (req, res, next) => {
    companyService
      .generateQRCode(generateQRCodedata)
      .then((result) => {
        return utils.generateQRCode(generateQRCodedata);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAllCompanyPosts: (req, res, next) => {
    companyService
      .getAllCompanyPosts(req.params.userId)
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        console.log("err", err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getPremium: (req, res) => {
    companyService
      .getPremium()
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getPremiumPlans: (req, res) => {
    companyService
      .getPremiumPlans()
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  paymentCallback: async (req, res) => {
    try {
      const paymentDetails = req.body; // Assuming payment gateway sends the body with payment data
      const result = await companyService.processPayment(paymentDetails);

      if (result.status === "success") {
        console.log("Payment Success:", paymentDetails);
        res.status(200).json({
          status: true,
          message: "Payment Successful",
        });
      } else {
        console.log("Payment Failed:", paymentDetails);
        res.status(400).json({
          status: false,
          message: "Payment Failed",
        });
      }
    } catch (error) {
      console.error("Error processing payment:", error);
      res.status(500).json({
        status: false,
        message: "Internal Server Error",
      });
    }
  },
  getMediaFiles: async (req, res) => {
    try {
      const { file_type, user_id = 0 } = req.query;
      const mediaFiles = await companyService.getMediaFiles(file_type, user_id);
      console.log("mediaFiles", mediaFiles);

      if (mediaFiles.error) {
        return res
          .status(400)
          .json({ status: false, message: mediaFiles.error });
      }
      return res.status(200).json({ status: true, data: mediaFiles });
    } catch (error) {
      console.error("Error fetching media files:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },
  getMediaFilesIndia: async (req, res) => {
    try {
      const { file_type, user_id = 0 } = req.query;
      const mediaFiles = await companyService.getMediaFilesIndia(file_type, user_id);
      console.log("mediaFiles", mediaFiles);

      if (mediaFiles.error) {
        return res
          .status(400)
          .json({ status: false, message: mediaFiles.error });
      }
      return res.status(200).json({ status: true, data: mediaFiles });
    } catch (error) {
      console.error("Error fetching media files:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  getMediaFilesIndiaTipCall: async (req, res) => {
    try {
      const { file_type, user_id = 0 } = req.query;
      const mediaFiles = await companyService.getMediaFilesIndiaTipCall(file_type, user_id);
      console.log("mediaFiles", mediaFiles);

      if (mediaFiles.error) {
        return res
          .status(400)
          .json({ status: false, message: mediaFiles.error });
      }
      return res.status(200).json({ status: true, data: mediaFiles });
    } catch (error) {
      console.error("Error fetching media files:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },



  



  getCategories: async (req, res) => {
    try {
      const result = await companyService.getAllCategories();
      if (result.error) {
        return res.status(400).json({ status: false, message: result.error });
      }
      return res
        .status(200)
        .json({ status: true, message: result.message, data: result.data });
    } catch (error) {
      console.error("Controller Error:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },
};
