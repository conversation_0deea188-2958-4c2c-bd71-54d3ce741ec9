# months
M(a)_1=led
M(a)_2=úno
M(a)_3=b<PERSON>e
M(a)_4=dub
M(a)_5=kvě
M(a)_6=čvn
M(a)_7=čvc
M(a)_8=srp
M(a)_9=zář
M(a)_10=říj
M(a)_11=lis
M(a)_12=pro

M(w)_1=ledna
M(w)_2=února
M(w)_3=března
M(w)_4=dubna
M(w)_5=května
M(w)_6=června
M(w)_7=července
M(w)_8=srpna
M(w)_9=z<PERSON><PERSON><PERSON>
M(w)_10=října
M(w)_11=listopadu
M(w)_12=prosince

M(A)_1=led
M(A)_2=úno
M(A)_3=bře
M(A)_4=dub
M(A)_5=kvě
M(A)_6=čvn
M(A)_7=čvc
M(A)_8=srp
M(A)_9=<PERSON><PERSON><PERSON>
<PERSON>(A)_10=říj
M(A)_11=lis
M(A)_12=pro

M(W)_1=leden
M(W)_2=únor
M(W)_3=březen
M(W)_4=duben
M(W)_5=květen
M(W)_6=červen
M(W)_7=červenec
M(W)_8=srpen
M(W)_9=září
M(W)_10=říjen
M(W)_11=listopad
M(W)_12=prosinec

# weekdays
D(a)_1=po
D(a)_2=út
D(a)_3=st
D(a)_4=čt
D(a)_5=pá
D(a)_6=so
D(a)_7=ne

D(n)_1=P
D(n)_2=Ú
D(n)_3=S
D(n)_4=Č
D(n)_5=P
D(n)_6=S
D(n)_7=N

D(s)_1=po
D(s)_2=út
D(s)_3=st
D(s)_4=čt
D(s)_5=pá
D(s)_6=so
D(s)_7=ne

D(w)_1=pondělí
D(w)_2=úterý
D(w)_3=středa
D(w)_4=čtvrtek
D(w)_5=pátek
D(w)_6=sobota
D(w)_7=neděle

D(A)_1=po
D(A)_2=út
D(A)_3=st
D(A)_4=čt
D(A)_5=pá
D(A)_6=so
D(A)_7=ne

D(N)_1=P
D(N)_2=Ú
D(N)_3=S
D(N)_4=Č
D(N)_5=P
D(N)_6=S
D(N)_7=N

D(S)_1=po
D(S)_2=út
D(S)_3=st
D(S)_4=čt
D(S)_5=pá
D(S)_6=so
D(S)_7=ne

D(W)_1=pondělí
D(W)_2=úterý
D(W)_3=středa
D(W)_4=čtvrtek
D(W)_5=pátek
D(W)_6=sobota
D(W)_7=neděle

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. čtvrtletí
Q(w)_2=2. čtvrtletí
Q(w)_3=3. čtvrtletí
Q(w)_4=4. čtvrtletí

Q(A)_1=Q1
Q(A)_2=Q2
Q(A)_3=Q3
Q(A)_4=Q4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1. čtvrtletí
Q(W)_2=2. čtvrtletí
Q(W)_3=3. čtvrtletí
Q(W)_4=4. čtvrtletí

# day-period-rules
T0400=morning1
T0900=morning2
T1200=afternoon1
T1800=evening1
T2200=night1

# day-period-translations
P(a)_midnight=půln.
P(a)_am=dop.
P(a)_noon=pol.
P(a)_pm=odp.
P(a)_morning1=r.
P(a)_morning2=dop.
P(a)_afternoon1=odp.
P(a)_evening1=več.
P(a)_night1=v n.

P(n)_midnight=půl.
P(n)_am=dop.
P(n)_noon=pol.
P(n)_pm=odp.
P(n)_morning1=r.
P(n)_morning2=d.
P(n)_afternoon1=o.
P(n)_evening1=v.
P(n)_night1=n.

P(w)_midnight=půlnoc
P(w)_am=dop.
P(w)_noon=poledne
P(w)_pm=odp.
P(w)_morning1=ráno
P(w)_morning2=dopoledne
P(w)_afternoon1=odpoledne
P(w)_evening1=večer
P(w)_night1=v noci

P(A)_midnight=půlnoc
P(A)_am=dop.
P(A)_noon=poledne
P(A)_pm=odp.
P(A)_morning1=ráno
P(A)_morning2=dopoledne
P(A)_afternoon1=odpoledne
P(A)_evening1=večer
P(A)_night1=noc

P(N)_midnight=půl.
P(N)_am=dop.
P(N)_noon=pol.
P(N)_pm=odp.
P(N)_morning1=ráno
P(N)_morning2=dop.
P(N)_afternoon1=odp.
P(N)_evening1=več.
P(N)_night1=noc

P(W)_midnight=půlnoc
P(W)_am=dop.
P(W)_noon=poledne
P(W)_pm=odp.
P(W)_morning1=ráno
P(W)_morning2=dopoledne
P(W)_afternoon1=odpoledne
P(W)_evening1=večer
P(W)_night1=noc

# eras
E(w)_0=před naším letopočtem
E(w)_1=našeho letopočtu

E(a)_0=př. n. l.
E(a)_1=n. l.

E(n)_0=př.n.l.
E(n)_1=n.l.

# format patterns
F(f)_d=EEEE d. MMMM y
F(l)_d=d. MMMM y
F(m)_d=d. M. y
F(s)_d=dd.MM.yy

F(alt)=H:mm:ss

F(f)_t=H:mm:ss zzzz
F(l)_t=H:mm:ss z
F(m)_t=H:mm:ss
F(s)_t=H:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=H
F_hm=h:mm a
F_Hm=H:mm
F_hms=h:mm:ss a
F_Hms=H:mm:ss

F_Md=d. M.
F_MMMd=d. M.
F_MMMMd=d. MMMM
F_y=y
F_yM=M/y
F_yMMM=LLLL y
F_yMMMM=LLLL y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=w. 'týden' 'roku' Y

I={0} – {1}

# labels of elements
L_era=letopočet
L_year=rok
L_quarter=čtvrtletí
L_month=měsíc
L_week=týden
L_day=den
L_weekday=den v týdnu
L_dayperiod=část dne
L_hour=hodina
L_minute=minuta
L_second=sekunda
L_zone=časové pásmo
