// adtipback/scripts/directStreamMigration.js
// Direct migration script that works with existing Stream inventory

const CloudflareStreamService = require('../services/CloudflareStreamService');

class DirectStreamMigrator {
  constructor() {
    this.streamService = CloudflareStreamService;
  }

  /**
   * Test Cloudflare Stream connection and get inventory
   */
  async testAndGetStreamInventory() {
    console.log('🔍 Testing Cloudflare Stream connection...');
    
    try {
      // Test connection
      const connectionTest = await this.streamService.testConnection();
      if (!connectionTest.success) {
        throw new Error(`Stream connection failed: ${connectionTest.error}`);
      }

      console.log('✅ Cloudflare Stream connection successful');
      console.log('📊 Connection details:', {
        accountId: this.streamService.accountId,
        customerCode: this.streamService.customerCode,
        status: connectionTest.status
      });

      // Get all Stream videos
      console.log('\n📹 Fetching Stream video inventory...');
      let allVideos = [];
      let hasMore = true;
      let after = null;
      let page = 1;

      while (hasMore) {
        console.log(`Fetching page ${page}...`);
        
        const options = { limit: 100 };
        if (after) options.after = after;

        const result = await this.streamService.listVideos(options);
        
        if (!result.success) {
          throw new Error(`Failed to list videos: ${result.error}`);
        }

        allVideos = allVideos.concat(result.videos);
        console.log(`Page ${page}: Found ${result.videos.length} videos`);
        
        hasMore = result.videos.length === 100;
        if (hasMore && result.videos.length > 0) {
          after = result.videos[result.videos.length - 1].uid;
        }
        page++;
      }

      console.log(`\n📊 Total Stream videos found: ${allVideos.length}`);

      // Analyze video status
      const analysis = {
        total: allVideos.length,
        ready: 0,
        processing: 0,
        error: 0,
        pending: 0
      };

      allVideos.forEach(video => {
        const status = video.status?.state || 'unknown';
        if (status === 'ready') analysis.ready++;
        else if (status === 'inprogress') analysis.processing++;
        else if (status === 'error') analysis.error++;
        else analysis.pending++;
      });

      console.log('\n📈 Stream Video Status Analysis:');
      console.log(`✅ Ready: ${analysis.ready}`);
      console.log(`🔄 Processing: ${analysis.processing}`);
      console.log(`❌ Error: ${analysis.error}`);
      console.log(`⏳ Pending/Unknown: ${analysis.pending}`);

      // Show sample ready videos
      const readyVideos = allVideos.filter(v => v.status?.state === 'ready');
      console.log('\n🎬 Sample Ready Videos (first 10):');
      readyVideos.slice(0, 10).forEach((video, index) => {
        console.log(`${index + 1}. UID: ${video.uid}`);
        console.log(`   Filename: ${video.filename || 'N/A'}`);
        console.log(`   Duration: ${video.duration || 'N/A'}s`);
        console.log(`   Size: ${video.size ? (video.size / (1024 * 1024)).toFixed(2) + ' MB' : 'N/A'}`);
        console.log(`   Created: ${video.created || 'N/A'}`);
        if (video.meta && Object.keys(video.meta).length > 0) {
          console.log(`   Metadata: ${JSON.stringify(video.meta)}`);
        }
        console.log('');
      });

      return {
        success: true,
        total: allVideos.length,
        ready: analysis.ready,
        videos: allVideos,
        readyVideos
      };

    } catch (error) {
      console.error('❌ Stream inventory check failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate SQL statements for database migration
   */
  generateMigrationSQL(streamVideos) {
    console.log('\n📝 Generating SQL migration statements...');
    
    const readyVideos = streamVideos.filter(v => v.status?.state === 'ready');
    const sqlStatements = [];

    console.log(`\n🔧 Generating SQL for ${readyVideos.length} ready videos...`);

    readyVideos.forEach((video, index) => {
      const streamVideoId = video.uid;
      const manifestUrl = `https://customer-${this.streamService.customerCode}.cloudflarestream.com/${streamVideoId}/manifest/video.m3u8`;
      
      // Generate UPDATE statement that can be used to update videos
      // This is a template - you'll need to match videos based on your criteria
      const updateSQL = `
-- Update for Stream Video ${index + 1}: ${video.filename || video.uid}
UPDATE reels SET 
  stream_video_id = '${streamVideoId}',
  stream_status = 'ready',
  adaptive_manifest_url = '${manifestUrl}',
  stream_ready_at = NOW()
WHERE 
  -- Add your matching criteria here, for example:
  -- name LIKE '%${video.filename ? video.filename.replace(/\.[^/.]+$/, "").replace(/'/g, "\\'") : ''}%'
  -- OR video_link LIKE '%${streamVideoId}%'
  -- OR id = [SPECIFIC_VIDEO_ID]
  id = 0; -- PLACEHOLDER - Replace with actual matching criteria
`;

      sqlStatements.push(updateSQL);
    });

    // Save SQL to file
    const fs = require('fs');
    const sqlContent = `-- Cloudflare Stream Migration SQL
-- Generated on: ${new Date().toISOString()}
-- Total ready videos: ${readyVideos.length}

${sqlStatements.join('\n')}

-- Verification queries:
SELECT COUNT(*) as total_videos FROM reels WHERE stream_video_id IS NOT NULL;
SELECT COUNT(*) as ready_videos FROM reels WHERE stream_status = 'ready';
SELECT stream_status, COUNT(*) as count FROM reels WHERE stream_video_id IS NOT NULL GROUP BY stream_status;
`;

    const filename = `stream_migration_${Date.now()}.sql`;
    fs.writeFileSync(filename, sqlContent);
    
    console.log(`\n💾 SQL migration file saved: ${filename}`);
    console.log(`📄 Contains ${readyVideos.length} UPDATE statements`);
    
    return {
      filename,
      statements: sqlStatements.length,
      readyVideos: readyVideos.length
    };
  }

  /**
   * Generate Stream URL mapping for frontend
   */
  generateStreamMapping(streamVideos) {
    console.log('\n🗺️  Generating Stream URL mapping...');
    
    const readyVideos = streamVideos.filter(v => v.status?.state === 'ready');
    const mapping = {};

    readyVideos.forEach(video => {
      const streamVideoId = video.uid;
      mapping[streamVideoId] = {
        uid: streamVideoId,
        hlsUrl: `https://customer-${this.streamService.customerCode}.cloudflarestream.com/${streamVideoId}/manifest/video.m3u8`,
        mp4Url: `https://customer-${this.streamService.customerCode}.cloudflarestream.com/${streamVideoId}/downloads/default.mp4`,
        thumbnailUrl: video.thumbnail || `https://customer-${this.streamService.customerCode}.cloudflarestream.com/${streamVideoId}/thumbnails/thumbnail.jpg`,
        filename: video.filename,
        duration: video.duration,
        size: video.size,
        created: video.created,
        meta: video.meta
      };
    });

    // Save mapping to JSON file
    const fs = require('fs');
    const mappingContent = {
      generated: new Date().toISOString(),
      customerCode: this.streamService.customerCode,
      totalVideos: readyVideos.length,
      mapping
    };

    const filename = `stream_mapping_${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(mappingContent, null, 2));
    
    console.log(`\n💾 Stream mapping saved: ${filename}`);
    console.log(`🔗 Contains ${Object.keys(mapping).length} video mappings`);
    
    return { filename, mapping, total: Object.keys(mapping).length };
  }

  /**
   * Run complete analysis and generate migration files
   */
  async runComplete() {
    console.log('🚀 CLOUDFLARE STREAM DIRECT MIGRATION');
    console.log('=====================================\n');

    try {
      // Step 1: Get Stream inventory
      const streamResult = await this.testAndGetStreamInventory();
      
      if (!streamResult.success) {
        throw new Error(`Stream inventory failed: ${streamResult.error}`);
      }

      // Step 2: Generate SQL migration
      const sqlResult = this.generateMigrationSQL(streamResult.videos);
      
      // Step 3: Generate Stream mapping
      const mappingResult = this.generateStreamMapping(streamResult.videos);

      console.log('\n✅ MIGRATION PREPARATION COMPLETE!');
      console.log('==================================');
      console.log(`📊 Total Stream videos: ${streamResult.total}`);
      console.log(`✅ Ready for migration: ${streamResult.ready}`);
      console.log(`📄 SQL file: ${sqlResult.filename}`);
      console.log(`🗺️  Mapping file: ${mappingResult.filename}`);
      
      console.log('\n📋 NEXT STEPS:');
      console.log('1. Review the generated SQL file');
      console.log('2. Update the WHERE clauses to match your videos');
      console.log('3. Execute the SQL statements in your database');
      console.log('4. Use the mapping file for frontend integration');
      console.log('5. Test video playback with Stream URLs');

      return {
        success: true,
        streamTotal: streamResult.total,
        readyVideos: streamResult.ready,
        sqlFile: sqlResult.filename,
        mappingFile: mappingResult.filename
      };

    } catch (error) {
      console.error('\n❌ Migration preparation failed:', error);
      throw error;
    }
  }
}

// Run if called directly
if (require.main === module) {
  const migrator = new DirectStreamMigrator();
  migrator.runComplete()
    .then(result => {
      console.log('\n🎉 Success! Migration files generated.');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Failed:', error.message);
      process.exit(1);
    });
}

module.exports = DirectStreamMigrator;
