const mysql = require("mysql");
require("dotenv").config();
const dbconfig = require("../config/appenvconfig.js").database;

// Enhanced database configuration with proper pooling settings
const enhancedDbConfig = {
  ...dbconfig,
  // Connection pool settings for high traffic
  connectionLimit: 50, // Maximum number of connections in the pool
  acquireTimeout: 60000, // 60 seconds to acquire a connection
  timeout: 60000, // 60 seconds query timeout
  reconnect: true, // Automatically reconnect if connection is lost
  charset: 'utf8mb4',
  
  // Connection settings
  connectTimeout: 60000, // 60 seconds to establish connection
  acquireTimeout: 60000, // 60 seconds to acquire connection from pool
  timeout: 60000, // 60 seconds query timeout
  reconnect: true, // Enable automatic reconnection
  
  // Pool management
  queueLimit: 0, // No limit on queued requests
  waitForConnections: true, // Wait for available connections
  
  // Connection health check
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
  
  // SSL settings (if needed)
  ssl: process.env.DB_SSL === 'true' ? {
    rejectUnauthorized: false
  } : false,
  
  // Debug settings (disable in production)
  debug: process.env.NODE_ENV === 'development' ? ['ComQueryPacket'] : false,
  
  // Multiple statement support
  multipleStatements: false,
  
  // Date handling
  dateStrings: true,
  timezone: '+05:30', // IST timezone
};

// Create connection pool with enhanced configuration
const connectionPool = mysql.createPool(enhancedDbConfig);

// Connection pool event handlers
connectionPool.on('connection', (connection) => {
  console.log('New database connection established');
  
  // Set session variables for this connection
  connection.query('SET SESSION sql_mode = "NO_AUTO_VALUE_ON_ZERO"');
  connection.query('SET SESSION time_zone = "+05:30"');
  
  // Handle connection errors
  connection.on('error', (err) => {
    console.error('Database connection error:', err);
    if (err.code === 'PROTOCOL_CONNECTION_LOST') {
      console.log('Database connection was lost. Reconnecting...');
    } else if (err.code === 'ER_CON_COUNT_ERROR') {
      console.error('Database has too many connections');
    } else if (err.code === 'ECONNREFUSED') {
      console.error('Database connection was refused');
    } else if (err.code === 'ETIMEDOUT') {
      console.error('Database connection timed out');
    }
  });
});

connectionPool.on('acquire', (connection) => {
  console.log('Connection %d acquired', connection.threadId);
});

connectionPool.on('release', (connection) => {
  console.log('Connection %d released', connection.threadId);
});

connectionPool.on('enqueue', () => {
  console.log('Waiting for available connection slot');
});

// Health check function
const checkConnectionHealth = () => {
  return new Promise((resolve, reject) => {
    connectionPool.getConnection((err, connection) => {
      if (err) {
        console.error('Health check failed:', err);
        reject(err);
        return;
      }
      
      connection.ping((pingErr) => {
        connection.release();
        if (pingErr) {
          console.error('Database ping failed:', pingErr);
          reject(pingErr);
        } else {
          console.log('Database health check passed');
          resolve(true);
        }
      });
    });
  });
};

// Retry mechanism with exponential backoff
const retryWithBackoff = async (operation, maxRetries = 3, baseDelay = 1000) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Check if error is retryable
      const isRetryable = error.code === 'ETIMEDOUT' || 
                         error.code === 'ECONNRESET' || 
                         error.code === 'PROTOCOL_CONNECTION_LOST' ||
                         error.code === 'ER_CON_COUNT_ERROR' ||
                         error.code === 'ENOTFOUND';
      
      if (!isRetryable) {
        throw error;
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.log(`Database operation failed (attempt ${attempt}/${maxRetries}). Retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

module.exports = {
  // Get a connection from the pool with retry logic
  getConnection: function () {
    return retryWithBackoff(() => {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Connection acquisition timeout'));
        }, 30000); // 30 second timeout for getting connection
        
        connectionPool.getConnection((err, connection) => {
          clearTimeout(timeout);
          if (err) {
            console.error("get-connection error:", { 
              code: err.code, 
              errno: err.errno, 
              sqlMessage: err.sqlMessage,
              sqlState: err.sqlState 
            });
            reject(err);
          } else {
            // Add connection metadata
            connection.threadId = connection.threadId || Date.now();
            connection.acquiredAt = Date.now();
            resolve(connection);
          }
        });
      });
    });
  },

  // Execute query with enhanced error handling and retry logic
  executeQuery: function (query, params = []) {
    return retryWithBackoff(async () => {
      return new Promise((resolve, reject) => {
        this.getConnection()
          .then((connection) => {
            const queryTimeout = setTimeout(() => {
              connection.release();
              reject(new Error('Query execution timeout'));
            }, 60000); // 60 second query timeout
            
            connection.query(query, params, (err, results) => {
              clearTimeout(queryTimeout);
              try {
                if (err) {
                  console.error("query execution error:", { 
                    code: err.code, 
                    errno: err.errno, 
                    sqlMessage: err.sqlMessage,
                    sqlState: err.sqlState,
                    query: query.substring(0, 100) + '...' // Log first 100 chars of query
                  });
                  reject(err);
                } else {
                  resolve(results);
                }
              } finally {
                // Ensure the connection is released back to the pool
                if (connection) {
                  const connectionTime = Date.now() - connection.acquiredAt;
                  if (connectionTime > 5000) { // Log slow connections
                    console.warn(`Slow connection usage: ${connectionTime}ms`);
                  }
                  connection.release();
                }
              }
            });
          })
          .catch((err) => {
            reject(err);
          });
      });
    });
  },

  // Execute transaction with automatic rollback on error
  executeTransaction: function (queries) {
    return retryWithBackoff(async () => {
      return new Promise((resolve, reject) => {
        this.getConnection()
          .then((connection) => {
            connection.beginTransaction((err) => {
              if (err) {
                connection.release();
                reject(err);
                return;
              }

              const executeQueries = async (index = 0) => {
                if (index >= queries.length) {
                  connection.commit((commitErr) => {
                    if (commitErr) {
                      connection.rollback(() => {
                        connection.release();
                        reject(commitErr);
                      });
                    } else {
                      connection.release();
                      resolve('Transaction committed successfully');
                    }
                  });
                  return;
                }

                const { query, params = [] } = queries[index];
                connection.query(query, params, (queryErr, results) => {
                  if (queryErr) {
                    connection.rollback(() => {
                      connection.release();
                      reject(queryErr);
                    });
                  } else {
                    executeQueries(index + 1);
                  }
                });
              };

              executeQueries();
            });
          })
          .catch((err) => {
            reject(err);
          });
      });
    });
  },

  // Health check method
  healthCheck: checkConnectionHealth,

  // Get pool statistics
  getPoolStats: function () {
    return {
      threadId: connectionPool.threadId,
      connectionLimit: connectionPool.config.connectionLimit,
      queueLimit: connectionPool.config.queueLimit,
      acquireTimeout: connectionPool.config.acquireTimeout,
      timeout: connectionPool.config.timeout,
    };
  },

  // Close the pool gracefully
  closePool: function () {
    return new Promise((resolve, reject) => {
      connectionPool.end((err) => {
        if (err) {
          console.error("Error closing pool:", err);
          reject(err);
        } else {
          console.log("Pool closed successfully.");
          resolve();
        }
      });
    });
  },

  // Emergency pool reset (use with caution)
  resetPool: function () {
    console.warn('Emergency pool reset initiated');
    return this.closePool().then(() => {
      // Recreate the pool
      Object.assign(connectionPool, mysql.createPool(enhancedDbConfig));
      console.log('Pool reset completed');
    });
  },
};
