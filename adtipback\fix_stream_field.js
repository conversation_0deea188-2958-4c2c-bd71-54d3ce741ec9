const dbQuery = require('./dbConfig/queryRunner');

async function addMissingField() {
  try {
    console.log('Adding missing stream_video_id field...');
    
    const sql = 'ALTER TABLE reels ADD COLUMN stream_video_id VARCHAR(255) NULL COMMENT "Cloudflare Stream video ID"';
    
    try {
      await dbQuery.queryRunner(sql);
      console.log('✅ Successfully added stream_video_id field');
    } catch (error) {
      if (error.message.includes('Duplicate column')) {
        console.log('⚠️  stream_video_id field already exists');
      } else {
        throw error;
      }
    }
    
    // Add index
    try {
      await dbQuery.queryRunner('CREATE INDEX idx_reels_stream_video_id ON reels(stream_video_id)');
      console.log('✅ Successfully added index for stream_video_id');
    } catch (error) {
      if (error.message.includes('Duplicate key')) {
        console.log('⚠️  Index already exists');
      } else {
        console.log('❌ Index error:', error.message);
      }
    }
    
    // Verify all Stream fields
    console.log('\nVerifying Stream fields...');
    const result = await dbQuery.queryRunner('DESCRIBE reels');
    const streamFields = ['stream_video_id', 'stream_status', 'adaptive_manifest_url', 'stream_ready_at'];
    const existingFields = result.map(field => field.Field);
    
    streamFields.forEach(field => {
      if (existingFields.includes(field)) {
        console.log('✅', field);
      } else {
        console.log('❌', field, '- MISSING');
      }
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

addMissingField();
