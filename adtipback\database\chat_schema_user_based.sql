-- =====================================================
-- USER-BASED CHAT SYSTEM DATABASE SCHEMA
-- =====================================================
-- Simplified chat system using direct user-to-user mapping
-- Eliminates complex conversation/participant relationships
-- Uses chat_userId1_userId2 format for direct mapping

-- =====================================================
-- 1. MESSAGES TABLE (USER-BASED)
-- =====================================================
-- Stores all chat messages with direct user mapping
CREATE TABLE IF NOT EXISTS messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    chat_id VARCHAR(255) NOT NULL COMMENT 'Format: chat_userId1_userId2 (sorted)',
    sender_id INT NOT NULL COMMENT 'User who sent the message',
    recipient_id INT NOT NULL COMMENT 'User who receives the message',
    sender_name VARCHAR(255) NOT NULL COMMENT 'Sender display name',
    sender_avatar VARCHAR(500) NULL COMMENT 'Sender profile image URL',
    
    -- Message content and metadata
    content TEXT NOT NULL COMMENT 'Message text content',
    message_type ENUM('text', 'image', 'video', 'audio', 'file', 'system') DEFAULT 'text',
    
    -- File attachments (for media messages)
    file_url VARCHAR(500) NULL COMMENT 'URL for media/file messages',
    file_size INT NULL COMMENT 'File size in bytes',
    file_name VARCHAR(255) NULL COMMENT 'Original file name',
    file_mime_type VARCHAR(100) NULL COMMENT 'MIME type of the file',
    thumbnail_url VARCHAR(500) NULL COMMENT 'Thumbnail URL for images/videos',
    
    -- Message relationships
    reply_to_message_id INT NULL COMMENT 'ID of message being replied to',
    
    -- Sync and tracking fields
    external_id VARCHAR(255) NULL COMMENT 'External ID from FCM or other systems',
    temp_id VARCHAR(255) NULL COMMENT 'Temporary ID from client for sync tracking',
    
    -- Message status and timestamps
    status ENUM('sending', 'sent', 'delivered', 'read', 'failed') DEFAULT 'sent',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Soft delete support
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL,
    deleted_by INT NULL COMMENT 'User who deleted the message',
    
    -- Message editing support
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP NULL,
    original_content TEXT NULL COMMENT 'Original content before editing',
    
    -- Performance indexes
    INDEX idx_chat_id_created (chat_id, created_at DESC),
    INDEX idx_sender_recipient (sender_id, recipient_id),
    INDEX idx_sender_created (sender_id, created_at DESC),
    INDEX idx_recipient_created (recipient_id, created_at DESC),
    INDEX idx_external_id (external_id),
    INDEX idx_temp_id (temp_id),
    INDEX idx_status (status),
    INDEX idx_deleted (is_deleted, deleted_at),
    INDEX idx_reply_to (reply_to_message_id),
    
    -- Foreign key constraints
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reply_to_message_id) REFERENCES messages(id) ON DELETE SET NULL,
    FOREIGN KEY (deleted_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. MESSAGES_LOCAL_SYNC TABLE (USER-BASED)
-- =====================================================
-- Tracks synchronization between local WatermelonDB and backend
CREATE TABLE IF NOT EXISTS messages_local_sync (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL COMMENT 'Reference to messages table',
    chat_id VARCHAR(255) NOT NULL COMMENT 'Chat ID for grouping',
    local_message_id VARCHAR(255) NULL COMMENT 'Local/temp message ID from client',
    local_timestamp TIMESTAMP NOT NULL COMMENT 'Timestamp when message was created locally',
    server_timestamp TIMESTAMP NULL COMMENT 'Timestamp when message was synced to server',
    sync_status ENUM('pending', 'synced', 'conflict', 'failed') DEFAULT 'pending',
    sync_attempts INT DEFAULT 0 COMMENT 'Number of sync attempts',
    last_sync_attempt TIMESTAMP NULL COMMENT 'Last time sync was attempted',
    error_message TEXT NULL COMMENT 'Error details if sync failed',
    client_version VARCHAR(50) NULL COMMENT 'Client app version for debugging',
    
    -- Indexes for performance
    INDEX idx_message_id (message_id),
    INDEX idx_chat_id (chat_id),
    INDEX idx_local_message_id (local_message_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_sync_attempts (sync_attempts),
    INDEX idx_last_sync_attempt (last_sync_attempt),
    
    -- Foreign key constraints
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. USER_CHAT_METADATA TABLE
-- =====================================================
-- Stores chat-level metadata for each user (unread counts, settings, etc.)
CREATE TABLE IF NOT EXISTS user_chat_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    chat_id VARCHAR(255) NOT NULL COMMENT 'Format: chat_userId1_userId2',
    other_user_id INT NOT NULL COMMENT 'The other participant in the chat',
    other_user_name VARCHAR(255) NOT NULL COMMENT 'Display name of other user',
    other_user_avatar VARCHAR(500) NULL COMMENT 'Profile image of other user',
    
    -- Chat state
    unread_count INT DEFAULT 0,
    last_read_message_id INT NULL,
    last_message_id INT NULL,
    last_message_content TEXT NULL,
    last_message_time TIMESTAMP NULL,
    
    -- User preferences for this chat
    is_muted BOOLEAN DEFAULT FALSE,
    is_blocked BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    is_pinned BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique chat per user
    UNIQUE KEY unique_user_chat (user_id, chat_id),
    
    -- Indexes for performance
    INDEX idx_user_id (user_id),
    INDEX idx_chat_id (chat_id),
    INDEX idx_other_user_id (other_user_id),
    INDEX idx_last_activity (last_activity_at DESC),
    INDEX idx_unread_count (unread_count),
    INDEX idx_user_activity (user_id, last_activity_at DESC),
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (other_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (last_read_message_id) REFERENCES messages(id) ON DELETE SET NULL,
    FOREIGN KEY (last_message_id) REFERENCES messages(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. MESSAGE_DELIVERY_STATUS TABLE
-- =====================================================
-- Tracks delivery and read status for each message per recipient
CREATE TABLE IF NOT EXISTS message_delivery_status (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL,
    recipient_id INT NOT NULL,
    status ENUM('sent', 'delivered', 'read') NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fcm_message_id VARCHAR(255) NULL COMMENT 'FCM message ID for tracking',
    device_info TEXT NULL COMMENT 'Device information for debugging',
    
    -- Ensure unique status per message per recipient
    UNIQUE KEY unique_message_recipient_status (message_id, recipient_id, status),
    
    -- Indexes for performance
    INDEX idx_message_id (message_id),
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_status (status),
    INDEX idx_timestamp (timestamp),
    INDEX idx_fcm_message_id (fcm_message_id),
    
    -- Foreign key constraints
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Trigger to update user_chat_metadata when new message is added
DELIMITER $$
CREATE TRIGGER update_user_chat_metadata_on_message
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    DECLARE sender_display_name VARCHAR(255);
    DECLARE recipient_display_name VARCHAR(255);

    -- Get sender and recipient names from users table
    SELECT name INTO sender_display_name FROM users WHERE id = NEW.sender_id;
    SELECT name INTO recipient_display_name FROM users WHERE id = NEW.recipient_id;

    -- Update metadata for sender
    INSERT INTO user_chat_metadata (
        user_id, chat_id, other_user_id, other_user_name,
        last_message_id, last_message_content, last_message_time, last_activity_at
    ) VALUES (
        NEW.sender_id, NEW.chat_id, NEW.recipient_id, COALESCE(recipient_display_name, 'Unknown User'),
        NEW.id, NEW.content, NEW.created_at, NEW.created_at
    ) ON DUPLICATE KEY UPDATE
        last_message_id = NEW.id,
        last_message_content = NEW.content,
        last_message_time = NEW.created_at,
        last_activity_at = NEW.created_at,
        updated_at = CURRENT_TIMESTAMP;

    -- Update metadata for recipient (increment unread count)
    INSERT INTO user_chat_metadata (
        user_id, chat_id, other_user_id, other_user_name,
        unread_count, last_message_id, last_message_content, last_message_time, last_activity_at
    ) VALUES (
        NEW.recipient_id, NEW.chat_id, NEW.sender_id, COALESCE(sender_display_name, 'Unknown User'),
        1, NEW.id, NEW.content, NEW.created_at, NEW.created_at
    ) ON DUPLICATE KEY UPDATE
        unread_count = unread_count + 1,
        last_message_id = NEW.id,
        last_message_content = NEW.content,
        last_message_time = NEW.created_at,
        last_activity_at = NEW.created_at,
        updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- Trigger to update delivery status
DELIMITER $$
CREATE TRIGGER create_delivery_status_on_message 
AFTER INSERT ON messages 
FOR EACH ROW 
BEGIN
    -- Create initial delivery status
    INSERT INTO message_delivery_status (message_id, recipient_id, status)
    VALUES (NEW.id, NEW.recipient_id, 'sent');
END$$
DELIMITER ;

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to generate chat_id from user IDs
DELIMITER $$
CREATE FUNCTION GenerateChatId(user_id_1 INT, user_id_2 INT) 
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE chat_id VARCHAR(255);
    
    IF user_id_1 < user_id_2 THEN
        SET chat_id = CONCAT('chat_', user_id_1, '_', user_id_2);
    ELSE
        SET chat_id = CONCAT('chat_', user_id_2, '_', user_id_1);
    END IF;
    
    RETURN chat_id;
END$$
DELIMITER ;

-- =====================================================
-- STORED PROCEDURES
-- =====================================================

-- Procedure to mark messages as read
DELIMITER $$
CREATE PROCEDURE MarkMessagesAsRead(
    IN p_user_id INT,
    IN p_chat_id VARCHAR(255),
    IN p_last_read_message_id INT
)
BEGIN
    -- Update user chat metadata
    UPDATE user_chat_metadata 
    SET unread_count = 0,
        last_read_message_id = p_last_read_message_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id AND chat_id = p_chat_id;
    
    -- Update delivery status for all unread messages
    INSERT INTO message_delivery_status (message_id, recipient_id, status)
    SELECT m.id, p_user_id, 'read'
    FROM messages m
    WHERE m.chat_id = p_chat_id 
      AND m.recipient_id = p_user_id
      AND m.id <= p_last_read_message_id
      AND NOT EXISTS (
          SELECT 1 FROM message_delivery_status mds 
          WHERE mds.message_id = m.id 
            AND mds.recipient_id = p_user_id 
            AND mds.status = 'read'
      );
END$$
DELIMITER ;

-- Procedure to get chat messages with pagination
DELIMITER $$
CREATE PROCEDURE GetChatMessages(
    IN p_chat_id VARCHAR(255),
    IN p_limit INT DEFAULT 50,
    IN p_offset INT DEFAULT 0,
    IN p_before_message_id INT DEFAULT NULL
)
BEGIN
    SELECT 
        m.*,
        mds.status as delivery_status,
        mds.timestamp as delivery_timestamp
    FROM messages m
    LEFT JOIN message_delivery_status mds ON m.id = mds.message_id
    WHERE m.chat_id = p_chat_id
      AND m.is_deleted = FALSE
      AND (p_before_message_id IS NULL OR m.id < p_before_message_id)
    ORDER BY m.created_at DESC, m.id DESC
    LIMIT p_limit OFFSET p_offset;
END$$
DELIMITER ;

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for user chat list with latest message info
CREATE VIEW user_chat_list AS
SELECT 
    ucm.*,
    u.name as other_user_display_name,
    u.profile_image as other_user_profile_image,
    u.is_online as other_user_online_status,
    m.content as last_message_preview,
    m.message_type as last_message_type,
    m.created_at as last_message_timestamp
FROM user_chat_metadata ucm
JOIN users u ON ucm.other_user_id = u.id
LEFT JOIN messages m ON ucm.last_message_id = m.id
WHERE ucm.is_archived = FALSE
ORDER BY ucm.last_activity_at DESC;

-- View for message details with sender info
CREATE VIEW message_details AS
SELECT 
    m.*,
    sender.name as sender_display_name,
    sender.profile_image as sender_profile_image,
    recipient.name as recipient_display_name,
    recipient.profile_image as recipient_profile_image,
    reply_msg.content as reply_to_content,
    reply_sender.name as reply_to_sender_name
FROM messages m
JOIN users sender ON m.sender_id = sender.id
JOIN users recipient ON m.recipient_id = recipient.id
LEFT JOIN messages reply_msg ON m.reply_to_message_id = reply_msg.id
LEFT JOIN users reply_sender ON reply_msg.sender_id = reply_sender.id
WHERE m.is_deleted = FALSE;
