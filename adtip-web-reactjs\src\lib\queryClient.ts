import { QueryClient } from '@tanstack/react-query';
import { logError, logWarn } from '../utils/ProductionLogger';

// Create a query client with optimized defaults
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Global query defaults
      staleTime: 5 * 60 * 1000, // 5 minutes - data is fresh for 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes - keep in cache for 10 minutes (formerly cacheTime)
      refetchOnWindowFocus: false, // Don't refetch on window focus by default
      refetchOnMount: true, // Refetch on component mount if data is stale
      refetchOnReconnect: true, // Refetch when network reconnects
      retry: (failureCount, error: any) => {
        // Don't retry on client errors (4xx)
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          logWarn('QueryClient', 'Client error, not retrying', { 
            status: error.response.status,
            failureCount 
          });
          return false;
        }
        
        // Don't retry on authentication errors
        if (error?.message?.includes('authentication') || 
            error?.message?.includes('unauthorized') ||
            error?.response?.status === 401) {
          logWarn('QueryClient', 'Authentication error, not retrying', { 
            message: error.message,
            failureCount 
          });
          return false;
        }
        
        // Retry up to 3 times for server errors (5xx) and network errors
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => {
        // Exponential backoff with jitter
        const baseDelay = Math.min(1000 * 2 ** attemptIndex, 30000);
        const jitter = Math.random() * 0.1 * baseDelay;
        return baseDelay + jitter;
      },
    },
    mutations: {
      // Global mutation defaults
      retry: (failureCount, error: any) => {
        // Don't retry mutations on client errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        // Retry once for server errors
        return failureCount < 1;
      },
      retryDelay: 1000, // 1 second delay for mutation retries
    },
  },
});

// Global error handler for queries
queryClient.setQueryDefaults(['*'], {
  onError: (error: any) => {
    logError('QueryClient', 'Query failed', error, {
      status: error?.response?.status,
      message: error?.message,
    });
  },
});

// Global error handler for mutations
queryClient.setMutationDefaults(['*'], {
  onError: (error: any) => {
    logError('QueryClient', 'Mutation failed', error, {
      status: error?.response?.status,
      message: error?.message,
    });
  },
});

// Query key factories for consistent cache management
export const queryKeys = {
  // User-related queries
  user: {
    all: ['user'] as const,
    profile: (userId: string) => [...queryKeys.user.all, 'profile', userId] as const,
    wallet: (userId: string) => [...queryKeys.user.all, 'wallet', userId] as const,
  },
  
  // Posts-related queries
  posts: {
    all: ['posts'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.posts.all, 'list', filters] as const,
    detail: (postId: string) => [...queryKeys.posts.all, 'detail', postId] as const,
    premium: () => [...queryKeys.posts.all, 'premium'] as const,
  },
  
  // Categories
  categories: {
    all: ['categories'] as const,
    popular: () => [...queryKeys.categories.all, 'popular'] as const,
  },
  
  // Marketplace
  marketplace: {
    all: ['marketplace'] as const,
    products: (filters: Record<string, any>) => [...queryKeys.marketplace.all, 'products', filters] as const,
    product: (productId: string) => [...queryKeys.marketplace.all, 'product', productId] as const,
  },
} as const;

// Utility functions for cache management
export const cacheUtils = {
  // Invalidate all user-related queries
  invalidateUser: (userId?: string) => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.user.wallet(userId) });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.all });
    }
  },
  
  // Invalidate posts queries
  invalidatePosts: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.posts.all });
  },
  
  // Prefetch user wallet
  prefetchWallet: async (userId: string, accessToken: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.user.wallet(userId),
      queryFn: async () => {
        const response = await fetch(`https://api.adtip.in/api/getfunds/${userId}`, {
          headers: { Authorization: `Bearer ${accessToken}` },
        });
        if (!response.ok) throw new Error('Failed to fetch wallet');
        return response.json();
      },
      staleTime: 5 * 60 * 1000,
    });
  },
  
  // Clear all cache
  clearAll: () => {
    queryClient.clear();
  },
  
  // Remove specific query from cache
  removeQuery: (queryKey: any[]) => {
    queryClient.removeQueries({ queryKey });
  },
};

export default queryClient;
