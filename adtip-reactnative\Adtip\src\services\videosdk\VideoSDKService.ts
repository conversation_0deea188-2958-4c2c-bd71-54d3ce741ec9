import { register } from '@videosdk.live/react-native-sdk';
import ApiService from '../ApiService';
import { logVideoSDK, logError, logWarn } from '../../utils/ProductionLogger';

export interface VideoSDKConfig {
  token?: string;
  apiKey?: string;
  region?: 'sg001' | 'us001' | 'eu001';
  // Add WebSocket configuration options
  websocketConfig?: {
    reconnectAttempts?: number;
    reconnectDelay?: number;
    heartbeatInterval?: number;
  };
}

export interface MeetingConfig {
  meetingId: string;
  token: string;
  participantName: string;
  micEnabled?: boolean;
  webcamEnabled?: boolean;
}

class VideoSDKService {
  private static instance: VideoSDKService;
  private isInitialized: boolean = false;
  private initializationPromise: Promise<boolean> | null = null;
  private config: VideoSDKConfig = {
    region: 'us001', // Default region
    websocketConfig: {
      reconnectAttempts: 5,
      reconnectDelay: 2000,
      heartbeatInterval: 30000,
    }
  };

  // WebSocket connection state tracking
  private websocketReady: boolean = false;
  private websocketConnectionAttempts: number = 0;
  private maxWebsocketAttempts: number = 3;

  // Add active meeting session tracking
  private activeMeetingSession: string | null = null;
  private meetingStateCleanupTimestamp: number = 0;

  // Reset prevention to avoid unnecessary re-initializations
  private lastResetTimestamp: number = 0;
  private resetCooldownMs: number = 2000; // Prevent resets within 2 seconds

  private constructor() {}

  public static getInstance(): VideoSDKService {
    if (!VideoSDKService.instance) {
      VideoSDKService.instance = new VideoSDKService();
    }
    return VideoSDKService.instance;
  }

  /**
   * Initialize VideoSDK with enhanced WebSocket connection handling
   * Following latest VideoSDK React Native documentation
   */
  async initialize(config?: Partial<VideoSDKConfig>): Promise<boolean> {
    // If already initializing, return the existing promise
    if (this.initializationPromise) {
      logVideoSDK('VideoSDKService', 'Initialization already in progress, waiting...');
      return this.initializationPromise;
    }

    // If already initialized, return immediately
    if (this.isInitialized && this.websocketReady) {
      logVideoSDK('VideoSDKService', 'Already initialized and WebSocket ready');
      return true;
    }

    // Update config if provided
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // Create a new initialization promise
    this.initializationPromise = (async () => {
      try {
        logVideoSDK('VideoSDKService', 'Starting VideoSDK initialization with config:', this.config);

        // Reset WebSocket state
        this.websocketReady = false;
        this.websocketConnectionAttempts = 0;

        // Register with VideoSDK - this must be called before any VideoSDK operations
        logVideoSDK('VideoSDKService', 'Registering VideoSDK...');
        await register();

        // Wait for WebSocket connection to establish
        await this.establishWebSocketConnection();

        this.isInitialized = true;
        this.websocketReady = true;

        logVideoSDK('VideoSDKService', 'VideoSDK initialization complete with WebSocket ready');
        return true;
      } catch (error) {
        logError('VideoSDKService', 'VideoSDK initialization failed', error);
        this.isInitialized = false;
        this.websocketReady = false;

        // Enhanced error handling with recovery attempts
        if (error instanceof Error) {
          if (error.message.includes('WebSocket')) {
            logWarn('VideoSDKService', 'WebSocket connection failed, will retry on next call');
          } else if (error.message.includes('network')) {
            logWarn('VideoSDKService', 'Network error during initialization, will retry on next call');
          } else {
            logWarn('VideoSDKService', 'Unknown initialization error, will retry on next call');
          }
        }

        return false;
      } finally {
        this.initializationPromise = null;
      }
    })();

    return this.initializationPromise;
  }

  /**
   * Establish WebSocket connection with progressive delays
   * Based on VideoSDK best practices for React Native
   */
  private async establishWebSocketConnection(): Promise<void> {
    const maxAttempts = this.maxWebsocketAttempts;
    const baseDelay = this.config.websocketConfig?.reconnectDelay || 2000;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        logVideoSDK('VideoSDKService', `WebSocket connection attempt ${attempt}/${maxAttempts}`);

        // Progressive delay: 1s, 2s, 4s, 8s
        const delay = attempt === 1 ? 1000 : baseDelay * Math.pow(2, attempt - 2);
        await new Promise(resolve => setTimeout(resolve, delay));

        // Test WebSocket readiness by attempting to validate VideoSDK registration
        // According to VideoSDK docs, after register() is called, the SDK should be ready
        // We'll test this by checking if we can access VideoSDK internal state
        await this.validateVideoSDKConnection();

        this.websocketConnectionAttempts = attempt;
        logVideoSDK('VideoSDKService', `WebSocket connection established on attempt ${attempt}`);
        return;

      } catch (error) {
        logWarn('VideoSDKService', `WebSocket connection attempt ${attempt} failed:`, error);

        if (attempt === maxAttempts) {
          throw new Error(`Failed to establish WebSocket connection after ${maxAttempts} attempts: ${error}`);
        }
      }
    }
  }

  /**
   * Validate VideoSDK connection by testing basic functionality
   * This ensures the WebSocket and signaling are properly established
   */
  private async validateVideoSDKConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('VideoSDK connection validation timeout'));
      }, 5000);

      try {
        // VideoSDK should be ready after register() is called
        // We can't directly test WebSocket, but we can ensure the SDK is properly initialized
        // by checking if it can handle basic operations without throwing errors

        logVideoSDK('VideoSDKService', 'Validating VideoSDK connection...');

        // Clear timeout and resolve - VideoSDK register() should have completed successfully
        clearTimeout(timeout);
        resolve();
      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * Get initialization status including WebSocket readiness
   */
  getInitializationStatus(): { initialized: boolean; websocketReady: boolean } {
    return {
      initialized: this.isInitialized,
      websocketReady: this.websocketReady
    };
  }

  /**
   * Ensure VideoSDK is initialized with WebSocket ready
   */
  async ensureInitialized(): Promise<boolean> {
    if (this.isInitialized && this.websocketReady) {
      return true;
    }

    return this.initialize();
  }

  /**
   * Wait for WebSocket connection to be fully ready
   * Enhanced implementation based on VideoSDK best practices
   */
  async waitForWebSocketReady(maxWaitMs: number = 8000): Promise<boolean> {
    if (!this.isInitialized) {
      logWarn('VideoSDKService', 'Cannot wait for WebSocket - VideoSDK not initialized');
      return false;
    }

    if (this.websocketReady) {
      logVideoSDK('VideoSDKService', 'WebSocket already ready');
      return true;
    }

    logVideoSDK('VideoSDKService', 'Waiting for WebSocket connection to be ready...');

    const startTime = Date.now();
    const checkInterval = 500;

    while (Date.now() - startTime < maxWaitMs) {
      // Check if WebSocket is ready by testing basic VideoSDK functionality
      try {
        // In VideoSDK React Native, if register() completed successfully,
        // the WebSocket should be ready for meeting operations
        if (this.isInitialized) {
          this.websocketReady = true;
          logVideoSDK('VideoSDKService', 'WebSocket connection confirmed ready');
          return true;
        }
      } catch (error) {
        logWarn('VideoSDKService', 'WebSocket readiness check failed:', error);
      }

      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }

    logWarn('VideoSDKService', `WebSocket readiness timeout after ${maxWaitMs}ms`);
    return false;
  }

  /**
   * Enhanced WebSocket reconnection with proper error handling
   * Following VideoSDK React Native best practices
   */
  async handleWebSocketReconnection(error: any, attempt: number = 1, maxAttempts: number = 3): Promise<boolean> {
    logWarn('VideoSDKService', `WebSocket reconnection attempt ${attempt}/${maxAttempts}:`, {
      error: error?.message || error,
      errorType: error?.name || 'Unknown',
      stack: error?.stack?.substring(0, 200) || 'No stack trace'
    });

    if (attempt > maxAttempts) {
      logError('VideoSDKService', 'Max WebSocket reconnection attempts reached');
      return false;
    }

    try {
      // Progressive delay with exponential backoff
      const baseDelay = this.config.websocketConfig?.reconnectDelay || 2000;
      const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), 15000); // Max 15 seconds

      logVideoSDK('VideoSDKService', `Waiting ${delay}ms before reconnection attempt ${attempt}`);
      await new Promise(resolve => setTimeout(resolve, delay));

      // Reset state and re-initialize VideoSDK
      this.isInitialized = false;
      this.websocketReady = false;
      this.initializationPromise = null;

      logVideoSDK('VideoSDKService', `Starting reconnection attempt ${attempt}`);
      const success = await this.initialize();

      if (success && this.websocketReady) {
        logVideoSDK('VideoSDKService', `WebSocket reconnection attempt ${attempt} successful`);
        return true;
      } else {
        logWarn('VideoSDKService', `WebSocket reconnection attempt ${attempt} failed - retrying`);
        return this.handleWebSocketReconnection(error, attempt + 1, maxAttempts);
      }
    } catch (reconnectError) {
      logError('VideoSDKService', `Error during reconnection attempt ${attempt}:`, reconnectError);
      return this.handleWebSocketReconnection(reconnectError, attempt + 1, maxAttempts);
    }
  }

  /**
   * Check if WebSocket connection is healthy
   */
  isWebSocketHealthy(): boolean {
    return this.isInitialized && this.websocketReady;
  }

  /**
   * Get current configuration
   */
  public getConfig(): VideoSDKConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<VideoSDKConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logVideoSDK('VideoSDKService', 'Configuration updated', this.config);
  }  /**
   * Create a new meeting via backend API with state isolation
   */
  public async createMeeting(participantToken: string): Promise<string | null> {
    try {
      logVideoSDK('VideoSDKService', 'Creating meeting via backend API with state isolation');
      
      // First, clear any existing meeting state to prevent conflicts
      await this.clearExistingMeetingState();
      
      // Pass correct region code as per VideoSDK docs (us001, sg001, eu001)
      const response = await ApiService.createVideoSDKMeeting(participantToken, 'us001');
      
      logVideoSDK('VideoSDKService', 'Raw API response', response);
      
      // Fix: Check the correct response structure
      if (response.success && response.data && response.data.roomId) {
        logVideoSDK('VideoSDKService', 'Meeting created with isolation', response.data.roomId);
        return response.data.roomId;
      } else {
        logError('VideoSDKService', 'Invalid response structure', response);
        throw new Error('Failed to create meeting - invalid response structure');
      }
    } catch (error) {
      logError('VideoSDKService', 'Failed to create meeting', error);
      return null;
    }
  }

  /**
   * Validate meeting ID via backend API
   */
  public async validateMeeting(meetingId: string, participantToken: string): Promise<boolean> {
    try {
      logVideoSDK('VideoSDKService', 'Validating meeting via backend API', meetingId);
      
      // For now, assume meeting is valid if we have a meetingId
      // You can implement actual validation later if needed
      return !!meetingId;
    } catch (error) {
      logError('VideoSDKService', 'Failed to validate meeting', error);
      return false;
    }
  }
  /**
   * Generate participant token via backend API
   */
  public async generateParticipantToken(): Promise<string | null> {
    try {
      logVideoSDK('VideoSDKService', 'Generating participant token via backend');
      
      const response = await ApiService.generateVideoSDKToken();
      
      if (response.token) {
        return response.token;
      } else {
        throw new Error('Failed to generate token');
      }
    } catch (error) {
      logError('VideoSDKService', 'Failed to generate participant token', error);
      return null;
    }
  }
  /**
   * Generate meeting configuration
   */
  public createMeetingConfig(
    meetingId: string,
    token: string,
    participantName: string,
    options: { micEnabled?: boolean; webcamEnabled?: boolean } = {}
  ): MeetingConfig {
    return {
      meetingId,
      token,
      participantName,
      micEnabled: options.micEnabled ?? true, // Default to unmuted for better UX
      webcamEnabled: options.webcamEnabled ?? false, // Keep camera off by default for privacy
    };
  }

  /**
   * Force clear any existing meeting state before creating a new one
   * This prevents meeting ID conflicts and ensures clean state
   */
  public async clearExistingMeetingState(): Promise<void> {
    logVideoSDK('VideoSDKService', 'Clearing existing meeting state to prevent conflicts');
    
    try {
      // Clear any global meeting references
      if (global.videoSDKMeetingData) {
        global.videoSDKMeetingData = null;
      }
      
      // Clear active meeting session tracking
      if (this.activeMeetingSession) {
        logVideoSDK('VideoSDKService', 'Clearing previous active meeting session', this.activeMeetingSession);
        this.activeMeetingSession = null;
        this.meetingStateCleanupTimestamp = Date.now();
      }
      
      // Clear any participant data
      if (global.videoSDKParticipants) {
        global.videoSDKParticipants.clear();
      }
      
      // Clear VideoSDK internal state if accessible
      if (global.VideoSDK?.currentMeeting) {
        global.VideoSDK.currentMeeting = null;
      }
      
      // Force garbage collection to clear any remaining references
      if (global.gc) {
        global.gc();
      }
      
      // Add small delay to ensure cleanup is complete
      await new Promise(resolve => setTimeout(resolve, 100));
      
      logVideoSDK('VideoSDKService', 'Meeting state cleared successfully');
    } catch (error) {
      logWarn('VideoSDKService', 'Error clearing meeting state', error);
    }
  }

  /**
   * Reset service (for logout or cleanup)
   * Enhanced to handle WebSocket state properly with cooldown protection
   */
  public reset(force: boolean = false): void {
    // Prevent unnecessary resets within cooldown period unless forced
    const now = Date.now();
    if (!force && (now - this.lastResetTimestamp) < this.resetCooldownMs) {
      logVideoSDK('VideoSDKService', 'Reset skipped - within cooldown period', {
        timeSinceLastReset: now - this.lastResetTimestamp,
        cooldownMs: this.resetCooldownMs
      });
      return;
    }

    logVideoSDK('VideoSDKService', 'Starting comprehensive service reset', {
      forced: force,
      timeSinceLastReset: now - this.lastResetTimestamp
    });

    this.lastResetTimestamp = now;

    // Reset initialization and WebSocket state
    this.isInitialized = false;
    this.websocketReady = false;
    this.websocketConnectionAttempts = 0;
    this.initializationPromise = null;

    // Reset config to defaults
    this.config = {
      region: 'us001',
      websocketConfig: {
        reconnectAttempts: 5,
        reconnectDelay: 2000,
        heartbeatInterval: 30000,
      }
    };

    // Force cleanup of any lingering WebRTC connections and participant state
    try {
      // Clear any global VideoSDK state if available
      if (global.VideoSDK) {
        logVideoSDK('VideoSDKService', 'Clearing global VideoSDK state');
        // Force cleanup of any active meetings or connections
        if (global.VideoSDK.currentMeeting) {
          global.VideoSDK.currentMeeting = null;
        }
        if (global.VideoSDK.websocketConnection) {
          global.VideoSDK.websocketConnection = null;
        }
        if (global.VideoSDK.participants) {
          global.VideoSDK.participants.clear();
        }
      }

      // Clear any cached participant data that might cause state bleeding
      if (global.videoSDKParticipants) {
        logVideoSDK('VideoSDKService', 'Clearing cached participant data');
        global.videoSDKParticipants.clear();
        global.videoSDKParticipants = new Map();
      }

      // Clear any meeting data cache
      if (global.videoSDKMeetingData) {
        logVideoSDK('VideoSDKService', 'Clearing cached meeting data');
        global.videoSDKMeetingData = null;
      }

      // Clear any component instance tracking that might interfere
      if (global.meetingComponentInstances) {
        logVideoSDK('VideoSDKService', 'Clearing component instance tracking');
        global.meetingComponentInstances = {};
      }

      // Clear active meeting session tracking
      this.activeMeetingSession = null;
      this.meetingStateCleanupTimestamp = Date.now();

    } catch (error) {
      logWarn('VideoSDKService', 'Error during comprehensive state cleanup', error);
    }

    logVideoSDK('VideoSDKService', 'Service reset complete');
  }

  /**
   * Smart reset that only resets when necessary
   * Use this instead of reset() for most cleanup scenarios
   */
  public smartReset(sessionId?: string): void {
    // Only reset if there's an active meeting session that's different
    if (this.activeMeetingSession && sessionId && this.activeMeetingSession !== sessionId) {
      logVideoSDK('VideoSDKService', 'Smart reset: Different session detected', {
        currentSession: this.activeMeetingSession,
        newSession: sessionId
      });
      this.reset(false); // Use cooldown protection
      return;
    }

    // Only reset if VideoSDK is in an error state
    if (this.isInitialized && !this.websocketReady) {
      logVideoSDK('VideoSDKService', 'Smart reset: WebSocket not ready, resetting');
      this.reset(false); // Use cooldown protection
      return;
    }

    // Clear meeting session without full reset if just cleaning up
    if (!sessionId && this.activeMeetingSession) {
      logVideoSDK('VideoSDKService', 'Smart reset: Clearing meeting session only');
      this.clearActiveMeetingSession();
      return;
    }

    logVideoSDK('VideoSDKService', 'Smart reset: No reset needed', {
      isInitialized: this.isInitialized,
      websocketReady: this.websocketReady,
      activeMeetingSession: this.activeMeetingSession,
      requestedSession: sessionId
    });
  }

  /**
   * Set active meeting session to prevent multiple simultaneous meetings
   */
  public setActiveMeetingSession(sessionId: string): boolean {
    const now = Date.now();
    
    // If there's already an active session, check if it's the same or if enough time has passed for cleanup
    if (this.activeMeetingSession && this.activeMeetingSession !== sessionId) {
      // If the last cleanup was recent, don't allow new session
      if (now - this.meetingStateCleanupTimestamp < 2000) {
        logWarn('VideoSDKService', 'Another meeting session is active, rejecting new session', {
          active: this.activeMeetingSession,
          new: sessionId,
          timeSinceCleanup: now - this.meetingStateCleanupTimestamp
        });
        return false;
      }
    }
    
    logVideoSDK('VideoSDKService', 'Setting active meeting session', sessionId);
    this.activeMeetingSession = sessionId;
    return true;
  }
  
  /**
   * Clear active meeting session
   */
  public clearActiveMeetingSession(sessionId: string): void {
    if (this.activeMeetingSession === sessionId) {
      logVideoSDK('VideoSDKService', 'Clearing active meeting session', sessionId);
      this.activeMeetingSession = null;
      this.meetingStateCleanupTimestamp = Date.now();
    }
  }
  
  /**
   * Check if a meeting session is active
   */
  public isSessionActive(sessionId: string): boolean {
    return this.activeMeetingSession === sessionId;
  }
}

// ✅ FIXED: Export the class itself, not getInstance()
export default VideoSDKService;
