# months
M(a)_1=ມ.ກ.
M(a)_2=ກ.ພ.
M(a)_3=ມ.ນ.
M(a)_4=ມ.ສ.
M(a)_5=ພ.ພ.
M(a)_6=ມິ.ຖ.
M(a)_7=ກ.ລ.
M(a)_8=ສ.ຫ.
M(a)_9=ກ.ຍ.
M(a)_10=ຕ.ລ.
M(a)_11=ພ.ຈ.
M(a)_12=ທ.ວ.

M(n)_1=1
M(n)_2=2
M(n)_3=3
M(n)_4=4
M(n)_5=5
M(n)_6=6
M(n)_7=7
M(n)_8=8
M(n)_9=9
M(n)_10=10
M(n)_11=11
M(n)_12=12

M(w)_1=ມັງກອນ
M(w)_2=ກຸມພາ
M(w)_3=ມີນາ
M(w)_4=ເມສາ
M(w)_5=ພຶດສະພາ
M(w)_6=ມິຖຸນາ
M(w)_7=ກໍລະກົດ
M(w)_8=ສິງຫາ
M(w)_9=ກັນຍາ
M(w)_10=ຕຸລາ
M(w)_11=ພະຈິກ
M(w)_12=ທັນວາ

M(A)_1=ມ.ກ.
M(A)_2=ກ.ພ.
M(A)_3=ມ.ນ.
M(A)_4=ມ.ສ.
M(A)_5=ພ.ພ.
M(A)_6=ມິ.ຖ.
M(A)_7=ກ.ລ.
M(A)_8=ສ.ຫ.
M(A)_9=ກ.ຍ.
M(A)_10=ຕ.ລ.
M(A)_11=ພ.ຈ.
M(A)_12=ທ.ວ.

M(N)_1=1
M(N)_2=2
M(N)_3=3
M(N)_4=4
M(N)_5=5
M(N)_6=6
M(N)_7=7
M(N)_8=8
M(N)_9=9
M(N)_10=10
M(N)_11=11
M(N)_12=12

M(W)_1=ມັງກອນ
M(W)_2=ກຸມພາ
M(W)_3=ມີນາ
M(W)_4=ເມສາ
M(W)_5=ພຶດສະພາ
M(W)_6=ມິຖຸນາ
M(W)_7=ກໍລະກົດ
M(W)_8=ສິງຫາ
M(W)_9=ກັນຍາ
M(W)_10=ຕຸລາ
M(W)_11=ພະຈິກ
M(W)_12=ທັນວາ

# weekdays
D(a)_1=ຈັນ
D(a)_2=ອັງຄານ
D(a)_3=ພຸດ
D(a)_4=ພະຫັດ
D(a)_5=ສຸກ
D(a)_6=ເສົາ
D(a)_7=ອາທິດ

D(n)_1=ຈ
D(n)_2=ອ
D(n)_3=ພ
D(n)_4=ພຫ
D(n)_5=ສຸ
D(n)_6=ສ
D(n)_7=ອາ

D(s)_1=ຈ.
D(s)_2=ອ.
D(s)_3=ພ.
D(s)_4=ພຫ.
D(s)_5=ສຸ.
D(s)_6=ສ.
D(s)_7=ອາ.

D(w)_1=ວັນຈັນ
D(w)_2=ວັນອັງຄານ
D(w)_3=ວັນພຸດ
D(w)_4=ວັນພະຫັດ
D(w)_5=ວັນສຸກ
D(w)_6=ວັນເສົາ
D(w)_7=ວັນອາທິດ

D(A)_1=ຈັນ
D(A)_2=ອັງຄານ
D(A)_3=ພຸດ
D(A)_4=ພະຫັດ
D(A)_5=ສຸກ
D(A)_6=ເສົາ
D(A)_7=ອາທິດ

D(N)_1=ຈ
D(N)_2=ອ
D(N)_3=ພ
D(N)_4=ພຫ
D(N)_5=ສຸ
D(N)_6=ສ
D(N)_7=ອາ

D(S)_1=ຈ.
D(S)_2=ອ.
D(S)_3=ພ.
D(S)_4=ພຫ.
D(S)_5=ສຸ.
D(S)_6=ສ.
D(S)_7=ອາ.

D(W)_1=ວັນຈັນ
D(W)_2=ວັນອັງຄານ
D(W)_3=ວັນພຸດ
D(W)_4=ວັນພະຫັດ
D(W)_5=ວັນສຸກ
D(W)_6=ວັນເສົາ
D(W)_7=ວັນອາທິດ

# quarters
Q(a)_1=ຕມ1
Q(a)_2=ຕມ2
Q(a)_3=ຕມ3
Q(a)_4=ຕມ4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=ໄຕຣມາດ 1
Q(w)_2=ໄຕຣມາດ 2
Q(w)_3=ໄຕຣມາດ 3
Q(w)_4=ໄຕຣມາດ 4

Q(A)_1=ຕ1
Q(A)_2=ຕ2
Q(A)_3=ຕ3
Q(A)_4=ຕ4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=ໄຕຣມາດ 1
Q(W)_2=ໄຕຣມາດ 2
Q(W)_3=ໄຕຣມາດ 3
Q(W)_4=ໄຕຣມາດ 4

# day-period-rules
T0500=morning1
T1200=afternoon1
T1600=evening1
T2000=night1

# day-period-translations
P(a)_midnight=ທ່ຽງຄືນ
P(a)_am=ກ່ອນທ່ຽງ
P(a)_noon=ຕອນທ່ຽງ
P(a)_pm=ຫຼັງທ່ຽງ
P(a)_morning1=ຕອນເຊົ້າ
P(a)_afternoon1=ຕອນບ່າຍ
P(a)_evening1=ຕອນແລງ
P(a)_night1=ກາງຄືນ

P(n)_midnight=ທຄ
P(n)_am=ກທ
P(n)_noon=ທ
P(n)_pm=ຫຼທ
P(n)_morning1=ຕອນເຊົ້າ
P(n)_afternoon1=ຕອນທ່ຽງ
P(n)_evening1=ຕອນແລງ
P(n)_night1=ກາງຄືນ

P(w)_midnight=ທ່ຽງຄືນ
P(w)_am=ກ່ອນທ່ຽງ
P(w)_noon=ຕອນທ່ຽງ
P(w)_pm=ຫຼັງທ່ຽງ
P(w)_morning1=ຕອນເຊົ້າ
P(w)_afternoon1=ຕອນບ່າຍ
P(w)_evening1=ຕອນແລງ
P(w)_night1=ຕອນກາງຄືນ

P(A)_midnight=ທ່ຽງ​ຄືນ
P(A)_am=ກ່ອນທ່ຽງ
P(A)_noon=ທ່ຽງ
P(A)_pm=ຫຼັງທ່ຽງ
P(A)_morning1=​ເຊົ້າ
P(A)_afternoon1=ສວຍ
P(A)_evening1=ແລງ
P(A)_night1=​ກາງ​ຄືນ

P(N)_midnight=ທຄ
P(N)_am=ກທ
P(N)_noon=ຕອນທ່ຽງ
P(N)_pm=ຫຼທ
P(N)_morning1=ຊ
P(N)_afternoon1=ສ
P(N)_evening1=ລ
P(N)_night1=ກຄ

P(W)_midnight=ທ່ຽງຄືນ
P(W)_am=ກ່ອນທ່ຽງ
P(W)_noon=ຕອນທ່ຽງ
P(W)_pm=ຫຼັງທ່ຽງ
P(W)_morning1=​ເຊົ້າ
P(W)_afternoon1=ສວຍ
P(W)_evening1=ແລງ
P(W)_night1=​ກາງ​ຄືນ

# eras
E(w)_0=ກ່ອນຄຣິດສັກກະລາດ
E(w|alt)_0=ກ່ອນສາກົນສັກກະລາດ
E(w)_1=ຄຣິດສັກກະລາດ
E(w|alt)_1=ສາກົນສັກກະລາດ

E(a)_0=ກ່ອນ ຄ.ສ.
E(a|alt)_0=ກ່ອນຍຸກ ຄ.ສ
E(a)_1=ຄ.ສ.
E(a|alt)_1=ຍຸກ ຄ.ສ

# format patterns
F(f)_d=EEEE ທີ d MMMM G y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=d/M/y

F(alt)=H ໂມງ m ນາທີ ss ວິນາທີ

F(f)_t=H ໂມງ m ນາທີ ss ວິນາທີ zzzz
F(l)_t=H ໂມງ m ນາທີ ss ວິນາທີ z
F(m)_t=H:mm:ss
F(s)_t=H:mm

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h ໂມງa
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=MMMM d
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=ອາທິດທີ່ w ຂອງປີ Y

I={0} – {1}

# labels of elements
L_era=ສະໄໝ
L_year=ປີ
L_quarter=ໄຕຣມາດ
L_month=ເດືອນ
L_week=ອາທິດ
L_day=ມື້
L_weekday=ມື້ຂອງອາທິດ
L_dayperiod=ກ່ອນທ່ຽງ/ຫຼັງທ່ຽງ
L_hour=ຊົ່ວໂມງ
L_minute=ນາທີ
L_second=ວິນາທີ
L_zone=ເຂດເວລາ
