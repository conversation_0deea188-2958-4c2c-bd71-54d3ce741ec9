# months
M(a)_1=meskerem
M(a)_2=tekemt
M(a)_3=hedar
M(a)_4=tahsas
M(a)_5=ter
M(a)_6=yekatit
M(a)_7=megabit
M(a)_8=miazia
M(a)_9=genbot
M(a)_10=sene
M(a)_11=hamle
M(a)_12=nehasse
M(a)_13=pagumen

M(n)_1=1
M(n)_2=2
M(n)_3=3
M(n)_4=4
M(n)_5=5
M(n)_6=6
M(n)_7=7
M(n)_8=8
M(n)_9=9
M(n)_10=10
M(n)_11=11
M(n)_12=12
M(n)_13=13

M(w)_1=meskerem
M(w)_2=tekemt
M(w)_3=hedar
M(w)_4=tahsas
M(w)_5=ter
M(w)_6=yekatit
M(w)_7=megabit
M(w)_8=miazia
M(w)_9=genbot
M(w)_10=sene
M(w)_11=hamle
M(w)_12=nehasse
M(w)_13=pagumen

M(A)_1=meskerem
M(A)_2=tekemt
M(A)_3=hedar
M(A)_4=tahsas
M(A)_5=ter
M(A)_6=yekatit
M(A)_7=megabit
M(A)_8=miazia
M(A)_9=genbot
M(A)_10=sene
M(A)_11=hamle
M(A)_12=nehasse
M(A)_13=pagumen

M(N)_1=1
M(N)_2=2
M(N)_3=3
M(N)_4=4
M(N)_5=5
M(N)_6=6
M(N)_7=7
M(N)_8=8
M(N)_9=9
M(N)_10=10
M(N)_11=11
M(N)_12=12
M(N)_13=13

M(W)_1=meskerem
M(W)_2=tekemt
M(W)_3=hedar
M(W)_4=tahsas
M(W)_5=ter
M(W)_6=yekatit
M(W)_7=megabit
M(W)_8=miazia
M(W)_9=genbot
M(W)_10=sene
M(W)_11=hamle
M(W)_12=nehasse
M(W)_13=pagumen

# format patterns
F(f)=EEEE d. MMMM y G
F(l)=d. MMMM y G
F(m)=d. MMM y G
