# months
M(a)_1=jaan
M(a)_2=veebr
M(a)_3=märts
M(a)_4=apr
M(a)_5=mai
M(a)_6=juuni
M(a)_7=juuli
M(a)_8=aug
M(a)_9=sept
M(a)_10=okt
M(a)_11=nov
M(a)_12=dets

M(n)_1=J
M(n)_2=V
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=jaanuar
M(w)_2=veebruar
M(w)_3=märts
M(w)_4=aprill
M(w)_5=mai
M(w)_6=juuni
M(w)_7=juuli
M(w)_8=august
M(w)_9=september
M(w)_10=oktoober
M(w)_11=november
M(w)_12=detsember

M(A)_1=jaan
M(A)_2=veebr
M(A)_3=märts
M(A)_4=apr
M(A)_5=mai
M(A)_6=juuni
M(A)_7=juuli
M(A)_8=aug
M(A)_9=sept
M(A)_10=okt
M(A)_11=nov
M(A)_12=dets

M(N)_1=J
M(N)_2=V
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=jaanuar
M(W)_2=veebruar
M(W)_3=märts
M(W)_4=aprill
M(W)_5=mai
M(W)_6=juuni
M(W)_7=juuli
M(W)_8=august
M(W)_9=september
M(W)_10=oktoober
M(W)_11=november
M(W)_12=detsember

# weekdays
D(a)_1=E
D(a)_2=T
D(a)_3=K
D(a)_4=N
D(a)_5=R
D(a)_6=L
D(a)_7=P

D(n)_1=E
D(n)_2=T
D(n)_3=K
D(n)_4=N
D(n)_5=R
D(n)_6=L
D(n)_7=P

D(s)_1=E
D(s)_2=T
D(s)_3=K
D(s)_4=N
D(s)_5=R
D(s)_6=L
D(s)_7=P

D(w)_1=esmaspäev
D(w)_2=teisipäev
D(w)_3=kolmapäev
D(w)_4=neljapäev
D(w)_5=reede
D(w)_6=laupäev
D(w)_7=pühapäev

D(A)_1=E
D(A)_2=T
D(A)_3=K
D(A)_4=N
D(A)_5=R
D(A)_6=L
D(A)_7=P

D(N)_1=E
D(N)_2=T
D(N)_3=K
D(N)_4=N
D(N)_5=R
D(N)_6=L
D(N)_7=P

D(S)_1=E
D(S)_2=T
D(S)_3=K
D(S)_4=N
D(S)_5=R
D(S)_6=L
D(S)_7=P

D(W)_1=esmaspäev
D(W)_2=teisipäev
D(W)_3=kolmapäev
D(W)_4=neljapäev
D(W)_5=reede
D(W)_6=laupäev
D(W)_7=pühapäev

# quarters
Q(a)_1=K1
Q(a)_2=K2
Q(a)_3=K3
Q(a)_4=K4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. kvartal
Q(w)_2=2. kvartal
Q(w)_3=3. kvartal
Q(w)_4=4. kvartal

Q(A)_1=K1
Q(A)_2=K2
Q(A)_3=K3
Q(A)_4=K4

Q(N)_1=1.
Q(N)_2=2.
Q(N)_3=3.
Q(N)_4=4.

Q(W)_1=1. kvartal
Q(W)_2=2. kvartal
Q(W)_3=3. kvartal
Q(W)_4=4. kvartal

# day-period-rules
T0500=morning1
T1200=afternoon1
T1800=evening1
T2300=night1

# day-period-translations
P(a)_midnight=keskööl
P(a)_am=AM
P(a)_noon=keskpäeval
P(a)_pm=PM
P(a)_morning1=hommikul
P(a)_afternoon1=pärastlõunal
P(a)_evening1=õhtul
P(a)_night1=öösel

P(n)_midnight=keskööl
P(n)_am=AM
P(n)_noon=keskpäeval
P(n)_pm=PM
P(n)_morning1=hommikul
P(n)_afternoon1=pärastlõunal
P(n)_evening1=õhtul
P(n)_night1=öösel

P(w)_midnight=keskööl
P(w)_am=AM
P(w)_noon=keskpäeval
P(w)_pm=PM
P(w)_morning1=hommikul
P(w)_afternoon1=pärastlõunal
P(w)_evening1=õhtul
P(w)_night1=öösel

P(A)_midnight=kesköö
P(A)_am=AM
P(A)_noon=keskpäev
P(A)_pm=PM
P(A)_morning1=hommik
P(A)_afternoon1=pärastlõuna
P(A)_evening1=õhtu
P(A)_night1=öö

P(N)_midnight=kesköö
P(N)_am=AM
P(N)_noon=keskpäev
P(N)_pm=PM
P(N)_morning1=hommik
P(N)_afternoon1=pärastlõuna
P(N)_evening1=õhtu
P(N)_night1=öö

P(W)_midnight=kesköö
P(W)_am=AM
P(W)_noon=keskpäev
P(W)_pm=PM
P(W)_morning1=hommik
P(W)_afternoon1=pärastlõuna
P(W)_evening1=õhtu
P(W)_night1=öö

# eras
E(w)_0=enne Kristust
E(w|alt)_0=enne meie ajaarvamist
E(w)_1=pärast Kristust
E(w|alt)_1=meie ajaarvamise järgi

E(a)_0=eKr
E(a|alt)_0=e.m.a
E(a)_1=pKr
E(a|alt)_1=m.a.j

E(n)_0=eKr
E(n|alt)_0=e.m.a
E(n)_1=pKr
E(n|alt)_1=m.a.j

# format patterns
F(f)_d=EEEE, d. MMMM y
F(l)_d=d. MMMM y
F(m)_d=d. MMM y
F(s)_d=dd.MM.yy

F(alt)=H:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.M
F_MMMd=d. MMM
F_MMMMd=d. MMMM
F_y=y
F_yM=M.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=w. 'nädal' (Y)

I={0} – {1}

# labels of elements
L_era=ajastu
L_year=aasta
L_quarter=kvartal
L_month=kuu
L_week=nädal
L_day=päev
L_weekday=nädalapäev
L_dayperiod=enne/pärast lõunat
L_hour=tund
L_minute=minut
L_second=sekund
L_zone=ajavöönd
