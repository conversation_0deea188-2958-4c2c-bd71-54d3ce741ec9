# months
M(a)_1=janv.
M(a)_2=févr.
M(a)_3=mars
M(a)_4=avr.
M(a)_5=mai
M(a)_6=juin
M(a)_7=juil.
M(a)_8=août
M(a)_9=sept.
M(a)_10=oct.
M(a)_11=nov.
M(a)_12=déc.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=janvier
M(w)_2=février
M(w)_3=mars
M(w)_4=avril
M(w)_5=mai
M(w)_6=juin
M(w)_7=juillet
M(w)_8=août
M(w)_9=septembre
M(w)_10=octobre
M(w)_11=novembre
M(w)_12=décembre

M(A)_1=janv.
M(A)_2=févr.
M(A)_3=mars
M(A)_4=avr.
M(A)_5=mai
M(A)_6=juin
M(A)_7=juil.
M(A)_8=août
M(A)_9=sept.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=déc.

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=janvier
M(W)_2=février
M(W)_3=mars
M(W)_4=avril
M(W)_5=mai
M(W)_6=juin
M(W)_7=juillet
M(W)_8=août
M(W)_9=septembre
M(W)_10=octobre
M(W)_11=novembre
M(W)_12=décembre

# weekdays
D(a)_1=lun.
D(a)_2=mar.
D(a)_3=mer.
D(a)_4=jeu.
D(a)_5=ven.
D(a)_6=sam.
D(a)_7=dim.

D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

D(s)_1=lu
D(s)_2=ma
D(s)_3=me
D(s)_4=je
D(s)_5=ve
D(s)_6=sa
D(s)_7=di

D(w)_1=lundi
D(w)_2=mardi
D(w)_3=mercredi
D(w)_4=jeudi
D(w)_5=vendredi
D(w)_6=samedi
D(w)_7=dimanche

D(A)_1=lun.
D(A)_2=mar.
D(A)_3=mer.
D(A)_4=jeu.
D(A)_5=ven.
D(A)_6=sam.
D(A)_7=dim.

D(N)_1=L
D(N)_2=M
D(N)_3=M
D(N)_4=J
D(N)_5=V
D(N)_6=S
D(N)_7=D

D(S)_1=lu
D(S)_2=ma
D(S)_3=me
D(S)_4=je
D(S)_5=ve
D(S)_6=sa
D(S)_7=di

D(W)_1=lundi
D(W)_2=mardi
D(W)_3=mercredi
D(W)_4=jeudi
D(W)_5=vendredi
D(W)_6=samedi
D(W)_7=dimanche

# quarters
Q(a)_1=T1
Q(a)_2=T2
Q(a)_3=T3
Q(a)_4=T4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1er trimestre
Q(w)_2=2e trimestre
Q(w)_3=3e trimestre
Q(w)_4=4e trimestre

Q(A)_1=T1
Q(A)_2=T2
Q(A)_3=T3
Q(A)_4=T4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1er trimestre
Q(W)_2=2e trimestre
Q(W)_3=3e trimestre
Q(W)_4=4e trimestre

# day-period-rules
T0000=night1
T0400=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=minuit
P(a)_am=AM
P(a)_noon=midi
P(a)_pm=PM
P(a)_morning1=mat.
P(a)_afternoon1=ap.m.
P(a)_evening1=soir
P(a)_night1=nuit

P(n)_midnight=minuit
P(n)_am=AM
P(n)_noon=midi
P(n)_pm=PM
P(n)_morning1=mat.
P(n)_afternoon1=ap.m.
P(n)_evening1=soir
P(n)_night1=nuit

P(w)_midnight=minuit
P(w)_am=AM
P(w)_noon=midi
P(w)_pm=PM
P(w)_morning1=du matin
P(w)_afternoon1=de l’après-midi
P(w)_evening1=du soir
P(w)_night1=de nuit

P(A)_midnight=minuit
P(A)_am=AM
P(A)_noon=midi
P(A)_pm=PM
P(A)_morning1=mat.
P(A)_afternoon1=ap.m.
P(A)_evening1=soir
P(A)_night1=nuit

P(N)_midnight=minuit
P(N)_am=AM
P(N)_noon=midi
P(N)_pm=PM
P(N)_morning1=mat.
P(N)_afternoon1=ap.m.
P(N)_evening1=soir
P(N)_night1=nuit

P(W)_midnight=minuit
P(W)_am=AM
P(W)_noon=midi
P(W)_pm=PM
P(W)_morning1=matin
P(W)_afternoon1=après-midi
P(W)_evening1=soir
P(W)_night1=nuit

# eras
E(w)_0=avant Jésus-Christ
E(w|alt)_0=avant l’ère commune
E(w)_1=après Jésus-Christ
E(w|alt)_1=de l’ère commune

E(a)_0=av. J.-C.
E(a|alt)_0=AEC
E(a)_1=ap. J.-C.
E(a|alt)_1=EC

E(n)_0=av. J.-C.
E(n|alt)_0=AEC
E(n)_1=ap. J.-C.
E(n|alt)_1=EC

# format patterns
F(f)_d=EEEE d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd/MM/y

F(alt)=HH' h 'mm

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'à' {0}
F(l)_dt={1} 'à' {0}
F(m)_dt={1} 'à' {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH 'h'
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd/MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='semaine' w 'de' Y

I={0} – {1}

# labels of elements
L_era=ère
L_year=année
L_quarter=trimestre
L_month=mois
L_week=semaine
L_day=jour
L_weekday=jour de la semaine
L_dayperiod=cadran
L_hour=heure
L_minute=minute
L_second=seconde
L_zone=fuseau horaire

# spanish era
E(n)_2=ère
E(a)_2=ère de César
E(w)_2=ère de César
