# months
M(a)_1=ጃንዩ
M(a)_2=ፌብሩ
M(a)_3=ማርች
M(a)_4=ኤፕሪ
M(a)_5=ሜይ
M(a)_6=ጁን
M(a)_7=ጁላይ
M(a)_8=ኦገስ
M(a)_9=ሴፕቴ
M(a)_10=ኦክቶ
M(a)_11=ኖቬም
M(a)_12=ዲሴም

M(n)_1=ጃ
M(n)_2=ፌ
M(n)_3=ማ
M(n)_4=ኤ
M(n)_5=ሜ
M(n)_6=ጁ
M(n)_7=ጁ
M(n)_8=ኦ
M(n)_9=ሴ
M(n)_10=ኦ
M(n)_11=ኖ
M(n)_12=ዲ

M(w)_1=ጃንዩወሪ
M(w)_2=ፌብሩወሪ
M(w)_3=ማርች
M(w)_4=ኤፕሪል
M(w)_5=ሜይ
M(w)_6=ጁን
M(w)_7=ጁላይ
M(w)_8=ኦገስት
M(w)_9=ሴፕቴምበር
M(w)_10=ኦክቶበር
M(w)_11=ኖቬምበር
M(w)_12=ዲሴምበር

M(A)_1=ጃንዩ
M(A)_2=ፌብሩ
M(A)_3=ማርች
M(A)_4=ኤፕሪ
M(A)_5=ሜይ
M(A)_6=ጁን
M(A)_7=ጁላይ
M(A)_8=ኦገስ
M(A)_9=ሴፕቴ
M(A)_10=ኦክቶ
M(A)_11=ኖቬም
M(A)_12=ዲሴም

M(N)_1=ጃ
M(N)_2=ፌ
M(N)_3=ማ
M(N)_4=ኤ
M(N)_5=ሜ
M(N)_6=ጁ
M(N)_7=ጁ
M(N)_8=ኦ
M(N)_9=ሴ
M(N)_10=ኦ
M(N)_11=ኖ
M(N)_12=ዲ

M(W)_1=ጃንዩወሪ
M(W)_2=ፌብሩወሪ
M(W)_3=ማርች
M(W)_4=ኤፕሪል
M(W)_5=ሜይ
M(W)_6=ጁን
M(W)_7=ጁላይ
M(W)_8=ኦገስት
M(W)_9=ሴፕቴምበር
M(W)_10=ኦክቶበር
M(W)_11=ኖቬምበር
M(W)_12=ዲሴምበር

# weekdays
D(a)_1=ሰኞ
D(a)_2=ማክሰ
D(a)_3=ረቡዕ
D(a)_4=ሐሙስ
D(a)_5=ዓርብ
D(a)_6=ቅዳሜ
D(a)_7=እሑድ

D(n)_1=ሰ
D(n)_2=ማ
D(n)_3=ረ
D(n)_4=ሐ
D(n)_5=ዓ
D(n)_6=ቅ
D(n)_7=እ

D(s)_1=ሰ
D(s)_2=ማ
D(s)_3=ረ
D(s)_4=ሐ
D(s)_5=ዓ
D(s)_6=ቅ
D(s)_7=እ

D(w)_1=ሰኞ
D(w)_2=ማክሰኞ
D(w)_3=ረቡዕ
D(w)_4=ሐሙስ
D(w)_5=ዓርብ
D(w)_6=ቅዳሜ
D(w)_7=እሑድ

D(A)_1=ሰኞ
D(A)_2=ማክሰ
D(A)_3=ረቡዕ
D(A)_4=ሐሙስ
D(A)_5=ዓርብ
D(A)_6=ቅዳሜ
D(A)_7=እሑድ

D(N)_1=ሰ
D(N)_2=ማ
D(N)_3=ረ
D(N)_4=ሐ
D(N)_5=ዓ
D(N)_6=ቅ
D(N)_7=እ

D(S)_1=ሰ
D(S)_2=ማ
D(S)_3=ረ
D(S)_4=ሐ
D(S)_5=ዓ
D(S)_6=ቅ
D(S)_7=እ

D(W)_1=ሰኞ
D(W)_2=ማክሰኞ
D(W)_3=ረቡዕ
D(W)_4=ሐሙስ
D(W)_5=ዓርብ
D(W)_6=ቅዳሜ
D(W)_7=እሑድ

# quarters
Q(a)_1=ሩብ1
Q(a)_2=ሩብ2
Q(a)_3=ሩብ3
Q(a)_4=ሩብ4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1ኛው ሩብ
Q(w)_2=2ኛው ሩብ
Q(w)_3=3ኛው ሩብ
Q(w)_4=4ኛው ሩብ

Q(A)_1=ሩብ1
Q(A)_2=ሩብ2
Q(A)_3=ሩብ3
Q(A)_4=ሩብ4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1ኛው ሩብ
Q(W)_2=2ኛው ሩብ
Q(W)_3=3ኛው ሩብ
Q(W)_4=4ኛው ሩብ

# day-period-rules
T0000=night1
T0600=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=እኩለ ሌሊት
P(a)_am=ጥዋት
P(a)_noon=ቀትር
P(a)_pm=ከሰዓት
P(a)_morning1=ጥዋት
P(a)_afternoon1=ከሰዓት
P(a)_evening1=ማታ
P(a)_night1=ሌሊት

P(n)_midnight=እኩለ ሌሊት
P(n)_am=ጠ
P(n)_noon=ቀ
P(n)_pm=ከ
P(n)_morning1=ጥዋት
P(n)_afternoon1=ከሰዓት
P(n)_evening1=ማታ
P(n)_night1=ሌሊት

P(w)_midnight=እኩለ ሌሊት
P(w)_am=ጥዋት
P(w)_noon=ቀትር
P(w)_pm=ከሰዓት
P(w)_morning1=ጥዋት
P(w)_afternoon1=ከሰዓት
P(w)_evening1=ማታ
P(w)_night1=ሌሊት

P(A)_midnight=እኩለ ሌሊት
P(A)_am=ጥዋት
P(A)_noon=ቀትር
P(A)_pm=ከሰዓት
P(A)_morning1=ጥዋት
P(A)_afternoon1=ከሰዓት በኋላ
P(A)_evening1=ማታ
P(A)_night1=ሌሊት

P(N)_midnight=እኩለ ሌሊት
P(N)_am=ጠ
P(N)_noon=ቀትር
P(N)_pm=ከ
P(N)_morning1=ጥዋት
P(N)_afternoon1=ከሰዓት በኋላ
P(N)_evening1=ማታ
P(N)_night1=ሌሊት

P(W)_midnight=እኩለ ሌሊት
P(W)_am=ጥዋት
P(W)_noon=ቀትር
P(W)_pm=ከሰዓት
P(W)_morning1=ጥዋት
P(W)_afternoon1=ከሰዓት በኋላ
P(W)_evening1=ማታ
P(W)_night1=ሌሊት

# eras
E(w)_0=ዓመተ ዓለም
E(w|alt)_0=ዓ/ዓ
E(w)_1=ዓመተ ምሕረት
E(w|alt)_1=ዓ/ም

E(a)_0=ዓ/ዓ
E(a)_1=ዓ/ም

E(n)_0=ዓ/ዓ
E(n)_1=ዓ/ም

# format patterns
F(f)_d=y MMMM d, EEEE
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd/MM/y

F(alt)=h:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=H
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=M/d
F_MMMd=MMM d
F_MMMMd=MMMM d
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y

I={0} – {1}

# labels of elements
L_era=ዘመን
L_year=ዓመት
L_quarter=ሩብ
L_month=ወር
L_week=ሳምንት
L_day=ቀን
L_weekday=አዘቦት
L_dayperiod=ጥዋት/ከሰዓት
L_hour=ሰዓት
L_minute=ደቂቃ
L_second=ሰከንድ
L_zone=የሰዓት ሰቅ
