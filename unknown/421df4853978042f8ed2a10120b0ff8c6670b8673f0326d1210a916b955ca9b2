
# weekdays
D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

# quarters
Q(w)_1=1.er trimestre
Q(w)_2=2.º trimestre
Q(w)_3=3.er trimestre
Q(w)_4=4.º trimestre

Q(W)_1=1.er trimestre
Q(W)_2=2.º trimestre
Q(W)_3=3.er trimestre
Q(W)_4=4.º trimestre

# day-period-translations
P(a)_am=a. m.
P(a)_noon=mediodía
P(a)_pm=p. m.
P(a)_morning1=madrugada
P(a)_morning2=mañana
P(a)_evening1=tarde
P(a)_night1=noche

P(w)_am=a. m.
P(w)_noon=mediodía
P(w)_pm=p. m.
P(w)_morning1=madrugada
P(w)_morning2=mañana
P(w)_evening1=tarde
P(w)_night1=noche

P(A)_am=a. m.
P(A)_pm=p. m.

P(N)_am=a. m.
P(N)_noon=m.
P(N)_pm=p. m.

P(W)_am=a. m.
P(W)_pm=p. m.

# format patterns
F_hms=hh:mm:ss
F_yM=M-y
F_yMMM=MMM y

I={0} a el {1}

# labels of elements
L_dayperiod=a. m./p. m.
