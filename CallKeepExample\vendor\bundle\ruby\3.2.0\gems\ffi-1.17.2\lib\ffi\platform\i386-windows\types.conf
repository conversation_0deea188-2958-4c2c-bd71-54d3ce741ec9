rbx.platform.typedef.__time32_t = long
rbx.platform.typedef.__time64_t = long_long
rbx.platform.typedef._dev_t = uint
rbx.platform.typedef._ino_t = ushort
rbx.platform.typedef._mode_t = ushort
rbx.platform.typedef._off64_t = long_long
rbx.platform.typedef._off_t = long
rbx.platform.typedef._pid_t = int
rbx.platform.typedef._sigset_t = ulong
rbx.platform.typedef.dev_t = uint
rbx.platform.typedef.errno_t = int
rbx.platform.typedef.ino_t = ushort
rbx.platform.typedef.int16_t = short
rbx.platform.typedef.int32_t = int
rbx.platform.typedef.int64_t = long_long
rbx.platform.typedef.int8_t = char
rbx.platform.typedef.int_fast16_t = short
rbx.platform.typedef.int_fast32_t = int
rbx.platform.typedef.int_fast64_t = long_long
rbx.platform.typedef.int_fast8_t = char
rbx.platform.typedef.int_least16_t = short
rbx.platform.typedef.int_least32_t = int
rbx.platform.typedef.int_least64_t = long_long
rbx.platform.typedef.int_least8_t = char
rbx.platform.typedef.intmax_t = long_long
rbx.platform.typedef.intptr_t = int
rbx.platform.typedef.mode_t = ushort
rbx.platform.typedef.off32_t = long
rbx.platform.typedef.off64_t = long_long
rbx.platform.typedef.off_t = long_long
rbx.platform.typedef.pid_t = int
rbx.platform.typedef.ptrdiff_t = int
rbx.platform.typedef.rsize_t = uint
rbx.platform.typedef.size_t = uint
rbx.platform.typedef.ssize_t = int
rbx.platform.typedef.time_t = long
rbx.platform.typedef.uint16_t = ushort
rbx.platform.typedef.uint64_t = ulong_long
rbx.platform.typedef.uint8_t = uchar
rbx.platform.typedef.uint_fast16_t = ushort
rbx.platform.typedef.uint_fast32_t = uint
rbx.platform.typedef.uint_fast64_t = ulong_long
rbx.platform.typedef.uint_fast8_t = uchar
rbx.platform.typedef.uint_least16_t = ushort
rbx.platform.typedef.uint_least64_t = ulong_long
rbx.platform.typedef.uint_least8_t = uchar
rbx.platform.typedef.uintmax_t = ulong_long
rbx.platform.typedef.uintptr_t = uint
rbx.platform.typedef.useconds_t = uint
rbx.platform.typedef.wchar_t = ushort
rbx.platform.typedef.wctype_t = ushort
rbx.platform.typedef.wint_t = ushort
