﻿# months
M(a)_1=Jan
M(a)_2=Feb
M(a)_3=Mär
M(a)_4=Apr
M(a)_5=Mai
M(a)_6=Jun
M(a)_7=Jul
M(a)_8=Aug
M(a)_9=Sep
M(a)_10=Okt
M(a)_11=Nov
M(a)_12=Dez

M(w)_1=Januar
M(w)_2=Februar
M(w)_3=März
M(w)_4=April
M(w)_5=Mai
M(w)_6=Juni
M(w)_7=Juli
M(w)_8=Auguscht
M(w)_9=Septämber
M(w)_10=Oktoober
M(w)_11=Novämber
M(w)_12=Dezämber

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

# weekdays
D(a)_1=Mä.
D(a)_2=Zi.
D(a)_3=Mi.
D(a)_4=Du.
D(a)_5=Fr.
D(a)_6=Sa.
D(a)_7=Su.

D(w)_1=Määntig
D(w)_2=Ziischtig
D(w)_3=Mittwuch
D(w)_4=Dunschtig
D(w)_5=Friitig
D(w)_6=Samschtig
D(w)_7=Sunntig

D(N)_1=M
D(N)_2=D
D(N)_3=M
D(N)_4=D
D(N)_5=F
D(N)_6=S
D(N)_7=S

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(w)_1=1. Quartal
Q(w)_2=2. Quartal
Q(w)_3=3. Quartal
Q(w)_4=4. Quartal

# day-period-rules
T0000=night1
T0500=morning1
T1200=afternoon1
T1400=afternoon2
T1800=evening1

# day-period-translations
P(a)_midnight=Mitternacht
P(a)_am=vorm.
P(a)_pm=nam.
P(a)_morning1=am Morge
P(a)_afternoon1=zmittag
P(a)_afternoon2=am Namittag
P(a)_evening1=zaabig
P(a)_night1=znacht

P(w)_midnight=Mitternacht
P(w)_am=am Vormittag
P(w)_pm=am Namittag
P(w)_morning1=am Morge
P(w)_afternoon1=zmittag
P(w)_afternoon2=am Namittag
P(w)_evening1=zaabig
P(w)_night1=znacht

P(W)_midnight=Mitternacht
P(W)_am=Vormittag
P(W)_pm=Namittag
P(W)_morning1=Morge
P(W)_afternoon1=Mittag
P(W)_afternoon2=Namittag
P(W)_evening1=Aabig
P(W)_night1=Nacht

# eras
E(w)_0=v. Chr.
E(w|alt)_0=vor der gewöhnlichen Zeitrechnung
E(w)_1=n. Chr.
E(w|alt)_1=der gewöhnlichen Zeitrechnung

E(a)_0=v. Chr.
E(a|alt)_0=v. d. Z.
E(a)_1=n. Chr.
E(a|alt)_1=d. Z.

E(n)_0=v. Chr.
E(n|alt)_0=vdZ
E(n)_1=n. Chr.
E(n|alt)_1=dZ

# format patterns
F(f)_d=EEEE, d. MMMM y
F(l)_d=d. MMMM y
F(m)_d=dd.MM.y
F(s)_d=dd.MM.yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm
F_H=H
F_Hm=HH:mm
F_Hms=HH:mm:ss

F_Md=d.M.
F_MMd=d.MM.
F_MMMd=d. MMM
F_MMMMd=d. MMMM
F_y=y
F_yM=y-M
F_yMM=MM.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y

I={0} – {1}

# labels of elements
L_era=Epoche
L_year=Jaar
L_month=Monet
L_week=Wuche
L_day=Tag
L_weekday=Wuchetag
L_dayperiod=Tageshälfti
L_hour=Schtund
L_minute=Minuute
L_second=Sekunde
L_zone=Zone
