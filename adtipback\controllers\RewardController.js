// RewardController.js - Handles reward-related API endpoints
const dbQuery = require('../dbConfig/queryRunner');

class RewardController {
  
  /**
   * Get reward history for a user with pagination
   * @route GET /api/reward/history
   * @query { page, limit }
   * @headers { Authorization: Bearer token }
   */
  static async getRewardHistory(req, res) {
    try {
      const userId = req.user?.user_id || req.user?.id || req.userId; // Get from auth middleware
      const { page = 1, limit = 20 } = req.query;

      if (!userId) {
        return res.status(401).json({
          status: 401,
          message: "User not authenticated",
          data: []
        });
      }
      
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;
      
      // Get reward transactions from wallet table
      // Include ad rewards, tip call earnings, and other reward types
      const rewardQuery = `
        SELECT 
          id,
          amount,
          gross_amount,
          platform_commission,
          commission_rate,
          transaction_type,
          description,
          transaction_date,
          createddate,
          CASE 
            WHEN transaction_type LIKE '%ad-reward%' THEN 'Ad Reward'
            WHEN transaction_type LIKE '%tip-call%' THEN 'Tip Call Earning'
            WHEN transaction_type LIKE '%video-call%' THEN 'Video Call Earning'
            WHEN transaction_type LIKE '%pubscale%' THEN 'Offerwall Reward'
            WHEN transaction_type LIKE '%promoted-post%' THEN 'Promoted Post View'
            ELSE 'Other Reward'
          END as reward_type,
          CASE 
            WHEN gross_amount IS NOT NULL AND platform_commission IS NOT NULL 
            THEN JSON_OBJECT(
              'gross_amount', gross_amount,
              'platform_commission', platform_commission,
              'net_amount', amount,
              'commission_rate', commission_rate
            )
            ELSE NULL
          END as commission_details
        FROM wallet 
        WHERE createdby = ? 
        AND transaction_status = 1 
        AND amount > 0
        AND (
          transaction_type LIKE '%reward%' OR 
          transaction_type LIKE '%earning%' OR 
          transaction_type LIKE '%tip-call%' OR 
          transaction_type LIKE '%video-call%' OR 
          transaction_type LIKE '%pubscale%' OR
          transaction_type LIKE '%promoted-post%'
        )
        ORDER BY transaction_date DESC, id DESC
        LIMIT ? OFFSET ?
      `;
      
      const rewards = await dbQuery.queryRunner(rewardQuery, [userId, limitNum, offset]);
      
      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM wallet 
        WHERE createdby = ? 
        AND transaction_status = 1 
        AND amount > 0
        AND (
          transaction_type LIKE '%reward%' OR 
          transaction_type LIKE '%earning%' OR 
          transaction_type LIKE '%tip-call%' OR 
          transaction_type LIKE '%video-call%' OR 
          transaction_type LIKE '%pubscale%' OR
          transaction_type LIKE '%promoted-post%'
        )
      `;
      
      const [countResult] = await dbQuery.queryRunner(countQuery, [userId]);
      const totalRecords = countResult.total;
      const totalPages = Math.ceil(totalRecords / limitNum);
      
      // Format the response
      const formattedRewards = rewards.map(reward => ({
        id: reward.id,
        amount: parseFloat(reward.amount),
        rewardType: reward.reward_type,
        transactionType: reward.transaction_type,
        description: reward.description,
        date: reward.transaction_date || reward.createddate,
        commissionDetails: reward.commission_details ? JSON.parse(reward.commission_details) : null
      }));
      
      return res.status(200).json({
        status: 200,
        message: "Reward history retrieved successfully",
        data: formattedRewards,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalRecords,
          hasNextPage: pageNum < totalPages,
          hasPreviousPage: pageNum > 1
        }
      });
      
    } catch (error) {
      console.error('[RewardController] Error getting reward history:', error);
      return res.status(500).json({
        status: 500,
        message: "Internal server error",
        data: []
      });
    }
  }
  
  /**
   * Get reward balance for a user
   * @route GET /api/reward/balance
   * @headers { Authorization: Bearer token }
   */
  static async getRewardBalance(req, res) {
    try {
      const userId = req.user?.user_id || req.user?.id || req.userId;

      if (!userId) {
        return res.status(401).json({
          status: 401,
          message: "User not authenticated",
          data: { balance: 0 }
        });
      }
      
      // Get current wallet balance
      const balanceQuery = `
        SELECT totalBalance 
        FROM wallet 
        WHERE createdby = ? 
        ORDER BY id DESC 
        LIMIT 1
      `;
      
      const [balanceResult] = await dbQuery.queryRunner(balanceQuery, [userId]);
      const balance = balanceResult ? parseFloat(balanceResult.totalBalance) : 0;
      
      return res.status(200).json({
        status: 200,
        message: "Reward balance retrieved successfully",
        data: {
          balance,
          coins: Math.floor(balance * 100) // Convert to coins (1 INR = 100 coins)
        }
      });
      
    } catch (error) {
      console.error('[RewardController] Error getting reward balance:', error);
      return res.status(500).json({
        status: 500,
        message: "Internal server error",
        data: { balance: 0 }
      });
    }
  }
  
  /**
   * Get reward summary/stats for a user
   * @route GET /api/reward/summary
   * @headers { Authorization: Bearer token }
   */
  static async getRewardSummary(req, res) {
    try {
      const userId = req.user?.user_id || req.user?.id || req.userId;

      if (!userId) {
        return res.status(401).json({
          status: 401,
          message: "User not authenticated",
          data: {}
        });
      }
      
      // Get reward summary
      const summaryQuery = `
        SELECT 
          COUNT(*) as total_rewards,
          COALESCE(SUM(amount), 0) as total_earned,
          COALESCE(SUM(gross_amount), 0) as total_gross_earned,
          COALESCE(SUM(platform_commission), 0) as total_commission_paid,
          COALESCE(SUM(CASE WHEN DATE(transaction_date) = CURDATE() THEN amount ELSE 0 END), 0) as today_earned,
          COALESCE(SUM(CASE WHEN transaction_date >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN amount ELSE 0 END), 0) as week_earned,
          COALESCE(SUM(CASE WHEN transaction_date >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN amount ELSE 0 END), 0) as month_earned
        FROM wallet 
        WHERE createdby = ? 
        AND transaction_status = 1 
        AND amount > 0
        AND (
          transaction_type LIKE '%reward%' OR 
          transaction_type LIKE '%earning%' OR 
          transaction_type LIKE '%tip-call%' OR 
          transaction_type LIKE '%video-call%' OR 
          transaction_type LIKE '%pubscale%' OR
          transaction_type LIKE '%promoted-post%'
        )
      `;
      
      const [summary] = await dbQuery.queryRunner(summaryQuery, [userId]);
      
      return res.status(200).json({
        status: 200,
        message: "Reward summary retrieved successfully",
        data: {
          totalRewards: parseInt(summary.total_rewards),
          totalEarned: parseFloat(summary.total_earned),
          totalGrossEarned: parseFloat(summary.total_gross_earned),
          totalCommissionPaid: parseFloat(summary.total_commission_paid),
          todayEarned: parseFloat(summary.today_earned),
          weekEarned: parseFloat(summary.week_earned),
          monthEarned: parseFloat(summary.month_earned)
        }
      });
      
    } catch (error) {
      console.error('[RewardController] Error getting reward summary:', error);
      return res.status(500).json({
        status: 500,
        message: "Internal server error",
        data: {}
      });
    }
  }
}

module.exports = RewardController;
