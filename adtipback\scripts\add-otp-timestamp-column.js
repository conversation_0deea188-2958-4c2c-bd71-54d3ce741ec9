const { queryRunner } = require('../dbConfig/queryRunner');

async function addOtpTimestampColumn() {
  try {
    console.log('Adding otp_created_at column to users table...');
    
    // Check if column already exists
    const checkColumnQuery = `
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'users' 
      AND COLUMN_NAME = 'otp_created_at'
    `;
    
    const columnExists = await queryRunner(checkColumnQuery);
    
    if (columnExists && columnExists.length > 0) {
      console.log('Column otp_created_at already exists. Skipping...');
      return;
    }
    
    // Add the column
    const addColumnQuery = `
      ALTER TABLE users 
      ADD COLUMN otp_created_at TIMESTAMP NULL DEFAULT NULL 
      COMMENT 'Timestamp when OTP was created for expiration tracking'
    `;
    
    await queryRunner(addColumnQuery);
    console.log('✅ Successfully added otp_created_at column to users table');
    
    // Add index for better performance on OTP queries
    const addIndexQuery = `
      CREATE INDEX idx_users_otp_created_at 
      ON users(otp_created_at)
    `;
    
    try {
      await queryRunner(addIndexQuery);
      console.log('✅ Successfully added index on otp_created_at column');
    } catch (indexError) {
      console.log('⚠️  Index creation failed (might already exist):', indexError.message);
    }
    
  } catch (error) {
    console.error('❌ Error adding otp_created_at column:', error);
    throw error;
  }
}

// Run the migration
if (require.main === module) {
  addOtpTimestampColumn()
    .then(() => {
      console.log('✅ Database migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Database migration failed:', error);
      process.exit(1);
    });
}

module.exports = { addOtpTimestampColumn }; 