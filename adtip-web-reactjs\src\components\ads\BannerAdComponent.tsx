// Banner Advertisement Component for Web Application
// Web implementation using Google AdSense or similar ad networks

import React, { useEffect, useRef, useState, memo } from 'react';
import { trackAdLoaded, trackAdFailed, trackAdImpression, trackAdClicked } from './AdTracker';
import { logDebug, logError } from '../../utils/ProductionLogger';

interface BannerAdProps {
  adUnitId?: string;
  size?: 'banner' | 'leaderboard' | 'large-banner';
  className?: string;
  style?: React.CSSProperties;
  onAdLoaded?: () => void;
  onAdFailed?: (error: any) => void;
  onAdClicked?: () => void;
}

// Ad unit configurations
const AD_SIZES = {
  banner: { width: 320, height: 50 },
  'large-banner': { width: 320, height: 100 },
  leaderboard: { width: 728, height: 90 },
} as const;

// Test/Demo ad unit IDs (replace with real ones in production)
const TEST_AD_UNITS = {
  banner: 'ca-app-pub-3940256099942544/6300978111',
  'large-banner': 'ca-app-pub-3940256099942544/6300978111',
  leaderboard: 'ca-app-pub-3940256099942544/6300978111',
} as const;

const BannerAdComponent = memo(({
  adUnitId,
  size = 'banner',
  className = '',
  style,
  onAdLoaded,
  onAdFailed,
  onAdClicked,
}: BannerAdProps) => {
  const adRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [adId] = useState(() => `banner-ad-${Math.random().toString(36).substr(2, 9)}`);

  // Use test ad unit if none provided
  const currentAdUnitId = adUnitId || TEST_AD_UNITS[size];
  const adSize = AD_SIZES[size];

  useEffect(() => {
    loadAd();
    
    // Set up intersection observer for viewability tracking
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isVisible) {
            setIsVisible(true);
            trackAdImpression('banner', currentAdUnitId, {
              size,
              viewabilityThreshold: entry.intersectionRatio,
            });
            logDebug('BannerAd', 'Ad impression tracked', { adUnitId: currentAdUnitId, size });
          }
        });
      },
      { threshold: 0.5 } // Track when 50% of ad is visible
    );

    if (adRef.current) {
      observer.observe(adRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [currentAdUnitId, size, isVisible]);

  const loadAd = async () => {
    try {
      logDebug('BannerAd', 'Loading banner ad', { adUnitId: currentAdUnitId, size });

      // Check if Google AdSense is available
      if (typeof window !== 'undefined' && (window as any).adsbygoogle) {
        // Initialize AdSense ad
        try {
          // Check if this ad slot already has an ad
          const adElement = document.getElementById(adId);
          if (adElement && !adElement.getAttribute('data-ad-status')) {
            ((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
          }

          setIsLoaded(true);
          setError(null);

          trackAdLoaded('banner', currentAdUnitId, { size });
          onAdLoaded?.();

          logDebug('BannerAd', 'Banner ad loaded successfully', { adUnitId: currentAdUnitId, adId });
        } catch (adError) {
          throw new Error(`AdSense initialization failed: ${adError}`);
        }
      } else {
        // Fallback: Load AdSense script if not available
        await loadAdSenseScript();
        // Retry loading after script is loaded
        setTimeout(() => loadAd(), 1000);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logError('BannerAd', 'Failed to load banner ad', error as Error);
      
      setError(errorMessage);
      setIsLoaded(false);
      
      trackAdFailed('banner', error, currentAdUnitId, { size });
      onAdFailed?.(error);
    }
  };

  const loadAdSenseScript = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      // Check if script already exists
      if (document.querySelector('script[src*="adsbygoogle.js"]')) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';
      script.crossOrigin = 'anonymous';
      
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load AdSense script'));
      
      document.head.appendChild(script);
    });
  };

  const handleAdClick = () => {
    trackAdClicked('banner', currentAdUnitId, { size });
    onAdClicked?.();
    logDebug('BannerAd', 'Banner ad clicked', { adUnitId: currentAdUnitId });
  };

  // Render placeholder if in development mode or if ad fails to load
  const renderPlaceholder = () => (
    <div
      className={`bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center text-gray-500 text-sm ${className}`}
      style={{
        width: adSize.width,
        height: adSize.height,
        ...style,
      }}
    >
      {import.meta.env.DEV ? (
        <div className="text-center">
          <div>Banner Ad Placeholder</div>
          <div className="text-xs mt-1">{adSize.width}x{adSize.height}</div>
          {error && <div className="text-red-500 text-xs mt-1">Error: {error}</div>}
        </div>
      ) : (
        <div>Advertisement</div>
      )}
    </div>
  );

  // In development, always show placeholder
  if (import.meta.env.DEV) {
    return (
      <div ref={adRef} onClick={handleAdClick}>
        {renderPlaceholder()}
      </div>
    );
  }

  // In production, show real ad or placeholder on error
  return (
    <div
      ref={adRef}
      className={`banner-ad-container ${className}`}
      style={style}
      onClick={handleAdClick}
    >
      {error ? (
        renderPlaceholder()
      ) : (
        <ins
          id={adId}
          className="adsbygoogle"
          style={{
            display: 'inline-block',
            width: adSize.width,
            height: adSize.height,
          }}
          data-ad-client={import.meta.env.VITE_GOOGLE_ADSENSE_CLIENT_ID || 'ca-app-pub-3940256099942544'}
          data-ad-slot={currentAdUnitId}
          data-ad-format="auto"
          data-full-width-responsive="true"
        />
      )}
    </div>
  );
});

BannerAdComponent.displayName = 'BannerAdComponent';

export default BannerAdComponent;
