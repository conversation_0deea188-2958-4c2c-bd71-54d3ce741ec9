CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[2/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


CXX compiler IPO check failed with the following output:
Change Dir: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64/CMakeFiles/_CMakeLTOTest-CXX/bin

Run Build Command(s):/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja && [1/4] Building CXX object CMakeFiles/foo.dir/foo.cpp.o
[2/4] Building CXX object CMakeFiles/boo.dir/main.cpp.o
[3/4] Linking CXX static library libfoo.a
[4/4] Linking CXX executable boo
FAILED: boo 
: && /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=armv7-none-linux-androideabi21 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold CMakeFiles/boo.dir/main.cpp.o -o boo  libfoo.a  -latomic -lm && :
clang++: error: invalid linker name in argument '-fuse-ld=gold'
clang++: error: invalid linker name in argument '-fuse-ld=gold'
ninja: build stopped: subcommand failed.


