// adtipback/scripts/generateReadyMigrationSQL.js
// Generate ready-to-execute SQL for Stream migration with intelligent matching

const fs = require('fs');

async function generateReadyMigrationSQL() {
  console.log('🚀 GENERATING READY-TO-EXECUTE STREAM MIGRATION SQL');
  console.log('==================================================\n');

  try {
    // Load the generated mapping file
    const mappingFiles = fs.readdirSync('.').filter(f => f.startsWith('stream_mapping_') && f.endsWith('.json'));
    if (mappingFiles.length === 0) {
      throw new Error('No stream mapping file found. Please run generateStreamMigrationSQL.js first.');
    }

    const latestMappingFile = mappingFiles.sort().pop();
    console.log(`📂 Loading mapping file: ${latestMappingFile}`);
    
    const mappingData = JSON.parse(fs.readFileSync(latestMappingFile, 'utf8'));
    const streamVideos = mappingData.videos;
    
    console.log(`📊 Found ${streamVideos.length} ready Stream videos`);

    // Generate SQL statements with intelligent matching
    console.log('\n📝 Generating intelligent SQL migration statements...');
    
    const sqlStatements = [];
    const customerCode = mappingData.customerCode;

    // Header
    sqlStatements.push(`-- CLOUDFLARE STREAM MIGRATION SQL`);
    sqlStatements.push(`-- Generated: ${new Date().toISOString()}`);
    sqlStatements.push(`-- Total Stream videos: ${streamVideos.length}`);
    sqlStatements.push(`-- Customer Code: ${customerCode}`);
    sqlStatements.push(`-- Account ID: ${mappingData.accountId}`);
    sqlStatements.push('');
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- MIGRATION STRATEGY');
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- This migration uses multiple matching strategies:');
    sqlStatements.push('-- 1. Exact filename matching (highest priority)');
    sqlStatements.push('-- 2. Numeric ID matching for files like "**********.mp4"');
    sqlStatements.push('-- 3. Stream UID matching in video_link field');
    sqlStatements.push('-- 4. Partial name matching');
    sqlStatements.push('');
    sqlStatements.push('-- IMPORTANT: Review and test these statements before execution!');
    sqlStatements.push('');

    // Strategy 1: Exact filename matching
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- STRATEGY 1: EXACT FILENAME MATCHING');
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('');

    let strategy1Count = 0;
    streamVideos.forEach((video, index) => {
      if (video.filename) {
        const cleanFilename = video.filename.replace(/\.[^/.]+$/, ""); // Remove extension
        const manifestUrl = `https://customer-${customerCode}.cloudflarestream.com/${video.uid}/manifest/video.m3u8`;
        
        sqlStatements.push(`-- Video ${index + 1}: ${video.filename} (${video.duration || 'N/A'}s, ${video.size ? (video.size / (1024 * 1024)).toFixed(2) + ' MB' : 'N/A'})`);
        sqlStatements.push(`UPDATE reels SET`);
        sqlStatements.push(`  stream_video_id = '${video.uid}',`);
        sqlStatements.push(`  stream_status = 'ready',`);
        sqlStatements.push(`  adaptive_manifest_url = '${manifestUrl}',`);
        sqlStatements.push(`  stream_ready_at = NOW()`);
        sqlStatements.push(`WHERE name = '${cleanFilename}' AND stream_video_id IS NULL;`);
        sqlStatements.push('');
        strategy1Count++;
      }
    });

    console.log(`✅ Strategy 1 (Exact filename): ${strategy1Count} statements`);

    // Strategy 2: Numeric ID matching
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- STRATEGY 2: NUMERIC ID MATCHING');
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- For videos with numeric IDs like "**********.mp4"');
    sqlStatements.push('');

    let strategy2Count = 0;
    streamVideos.forEach((video, index) => {
      if (video.filename) {
        const numericMatch = video.filename.match(/(\d{7,})/); // 7+ digit numbers
        if (numericMatch) {
          const numericId = numericMatch[1];
          const manifestUrl = `https://customer-${customerCode}.cloudflarestream.com/${video.uid}/manifest/video.m3u8`;
          
          sqlStatements.push(`-- Video ${index + 1}: ${video.filename} -> Numeric ID: ${numericId}`);
          sqlStatements.push(`UPDATE reels SET`);
          sqlStatements.push(`  stream_video_id = '${video.uid}',`);
          sqlStatements.push(`  stream_status = 'ready',`);
          sqlStatements.push(`  adaptive_manifest_url = '${manifestUrl}',`);
          sqlStatements.push(`  stream_ready_at = NOW()`);
          sqlStatements.push(`WHERE name LIKE '%${numericId}%' AND stream_video_id IS NULL;`);
          sqlStatements.push('');
          strategy2Count++;
        }
      }
    });

    console.log(`✅ Strategy 2 (Numeric ID): ${strategy2Count} statements`);

    // Strategy 3: Stream UID in video_link
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- STRATEGY 3: STREAM UID IN VIDEO_LINK');
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- For videos that already reference Stream UIDs');
    sqlStatements.push('');

    let strategy3Count = 0;
    streamVideos.forEach((video, index) => {
      const manifestUrl = `https://customer-${customerCode}.cloudflarestream.com/${video.uid}/manifest/video.m3u8`;
      
      sqlStatements.push(`-- Video ${index + 1}: ${video.filename || video.uid}`);
      sqlStatements.push(`UPDATE reels SET`);
      sqlStatements.push(`  stream_video_id = '${video.uid}',`);
      sqlStatements.push(`  stream_status = 'ready',`);
      sqlStatements.push(`  adaptive_manifest_url = '${manifestUrl}',`);
      sqlStatements.push(`  stream_ready_at = NOW()`);
      sqlStatements.push(`WHERE video_link LIKE '%${video.uid}%' AND stream_video_id IS NULL;`);
      sqlStatements.push('');
      strategy3Count++;
    });

    console.log(`✅ Strategy 3 (UID in video_link): ${strategy3Count} statements`);

    // Strategy 4: Bulk update for remaining videos (manual review needed)
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- STRATEGY 4: BULK UPDATE TEMPLATE');
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- MANUAL REVIEW REQUIRED: Update specific video IDs');
    sqlStatements.push('-- Use this template for videos that need manual matching');
    sqlStatements.push('');

    // Show first 10 videos as examples
    const exampleVideos = streamVideos.slice(0, 10);
    exampleVideos.forEach((video, index) => {
      const manifestUrl = `https://customer-${customerCode}.cloudflarestream.com/${video.uid}/manifest/video.m3u8`;
      
      sqlStatements.push(`-- Example ${index + 1}: ${video.filename || video.uid} (${video.duration || 'N/A'}s)`);
      sqlStatements.push(`-- UPDATE reels SET`);
      sqlStatements.push(`--   stream_video_id = '${video.uid}',`);
      sqlStatements.push(`--   stream_status = 'ready',`);
      sqlStatements.push(`--   adaptive_manifest_url = '${manifestUrl}',`);
      sqlStatements.push(`--   stream_ready_at = NOW()`);
      sqlStatements.push(`-- WHERE id = [REPLACE_WITH_SPECIFIC_VIDEO_ID];`);
      sqlStatements.push('');
    });

    // Verification queries
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- VERIFICATION QUERIES');
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('');
    sqlStatements.push('-- Check migration progress');
    sqlStatements.push('SELECT ');
    sqlStatements.push('  COUNT(*) as total_videos,');
    sqlStatements.push('  SUM(CASE WHEN stream_video_id IS NOT NULL THEN 1 ELSE 0 END) as videos_with_stream,');
    sqlStatements.push('  SUM(CASE WHEN stream_status = "ready" THEN 1 ELSE 0 END) as ready_videos,');
    sqlStatements.push('  ROUND(SUM(CASE WHEN stream_video_id IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as migration_percentage');
    sqlStatements.push('FROM reels WHERE is_active = 1;');
    sqlStatements.push('');
    sqlStatements.push('-- Sample migrated videos');
    sqlStatements.push('SELECT id, name, stream_video_id, stream_status, adaptive_manifest_url');
    sqlStatements.push('FROM reels ');
    sqlStatements.push('WHERE stream_status = "ready" ');
    sqlStatements.push('ORDER BY stream_ready_at DESC ');
    sqlStatements.push('LIMIT 10;');
    sqlStatements.push('');
    sqlStatements.push('-- Videos still needing migration');
    sqlStatements.push('SELECT id, name, video_link, total_views');
    sqlStatements.push('FROM reels ');
    sqlStatements.push('WHERE is_active = 1 AND stream_video_id IS NULL ');
    sqlStatements.push('ORDER BY total_views DESC ');
    sqlStatements.push('LIMIT 20;');

    // Save the SQL file
    const sqlContent = sqlStatements.join('\n');
    const sqlFilename = `ready_stream_migration_${Date.now()}.sql`;
    fs.writeFileSync(sqlFilename, sqlContent);

    console.log(`\n💾 Ready migration SQL saved: ${sqlFilename}`);
    console.log(`📄 Contains ${strategy1Count + strategy2Count + strategy3Count} UPDATE statements`);

    // Generate execution summary
    const summaryContent = {
      generated: new Date().toISOString(),
      totalStreamVideos: streamVideos.length,
      strategies: {
        exactFilename: strategy1Count,
        numericId: strategy2Count,
        uidInVideoLink: strategy3Count
      },
      sqlFile: sqlFilename,
      customerCode,
      accountId: mappingData.accountId,
      sampleUrls: streamVideos.slice(0, 5).map(v => ({
        uid: v.uid,
        filename: v.filename,
        manifestUrl: `https://customer-${customerCode}.cloudflarestream.com/${v.uid}/manifest/video.m3u8`
      }))
    };

    const summaryFilename = `migration_summary_${Date.now()}.json`;
    fs.writeFileSync(summaryFilename, JSON.stringify(summaryContent, null, 2));

    console.log(`📊 Migration summary saved: ${summaryFilename}`);

    console.log('\n📋 EXECUTION INSTRUCTIONS:');
    console.log('1. Review the generated SQL file carefully');
    console.log('2. Test with a few UPDATE statements first');
    console.log('3. Execute Strategy 1 (exact filename matching) first');
    console.log('4. Execute Strategy 2 (numeric ID matching) second');
    console.log('5. Execute Strategy 3 (UID in video_link) third');
    console.log('6. Use verification queries to check progress');
    console.log('7. Manually handle remaining videos using Strategy 4 template');

    console.log('\n🔗 SAMPLE STREAM URLS FOR TESTING:');
    summaryContent.sampleUrls.forEach((video, index) => {
      console.log(`${index + 1}. ${video.uid} (${video.filename || 'No filename'})`);
      console.log(`   ${video.manifestUrl}`);
    });

    console.log('\n✅ READY-TO-EXECUTE MIGRATION SQL GENERATED!');
    console.log(`📊 Total strategies: 3`);
    console.log(`📄 SQL file: ${sqlFilename}`);
    console.log(`📊 Summary: ${summaryFilename}`);

    return {
      success: true,
      sqlFile: sqlFilename,
      summaryFile: summaryFilename,
      totalStatements: strategy1Count + strategy2Count + strategy3Count,
      strategies: summaryContent.strategies
    };

  } catch (error) {
    console.error('\n❌ SQL generation failed:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  generateReadyMigrationSQL()
    .then(result => {
      console.log('\n🎉 Success! Ready-to-execute SQL generated.');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Failed:', error.message);
      process.exit(1);
    });
}

module.exports = { generateReadyMigrationSQL };
