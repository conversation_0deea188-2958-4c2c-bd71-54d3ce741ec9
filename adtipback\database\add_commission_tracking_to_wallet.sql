-- Add commission tracking fields to wallet table
-- This migration adds support for tracking platform commission on earnings
-- MySQL 8 compatible version using prepared statements

-- Add gross_amount column if it doesn't exist
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'wallet'
        AND COLUMN_NAME = 'gross_amount'
        AND TABLE_SCHEMA = DATABASE()
    ),
    'SELECT "gross_amount column already exists" as message',
    'ALTER TABLE wallet ADD COLUMN gross_amount DECIMAL(10,2) DEFAULT NULL COMMENT "Gross earning amount before commission"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add platform_commission column if it doesn't exist
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'wallet'
        AND COLUMN_NAME = 'platform_commission'
        AND TABLE_SCHEMA = DATABASE()
    ),
    'SELECT "platform_commission column already exists" as message',
    'ALTER TABLE wallet ADD COLUMN platform_commission DECIMAL(10,2) DEFAULT NULL COMMENT "Platform commission deducted"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add commission_rate column if it doesn't exist
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'wallet'
        AND COLUMN_NAME = 'commission_rate'
        AND TABLE_SCHEMA = DATABASE()
    ),
    'SELECT "commission_rate column already exists" as message',
    'ALTER TABLE wallet ADD COLUMN commission_rate DECIMAL(5,4) DEFAULT NULL COMMENT "Commission rate applied (0.30 for premium, 0.60 for regular)"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add description column if it doesn't exist
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'wallet'
        AND COLUMN_NAME = 'description'
        AND TABLE_SCHEMA = DATABASE()
    ),
    'SELECT "description column already exists" as message',
    'ALTER TABLE wallet ADD COLUMN description TEXT DEFAULT NULL COMMENT "Transaction description"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create index for better performance on commission queries (MySQL 8 compatible)
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM INFORMATION_SCHEMA.STATISTICS
        WHERE TABLE_NAME = 'wallet'
        AND INDEX_NAME = 'idx_wallet_commission'
        AND TABLE_SCHEMA = DATABASE()
    ),
    'SELECT "Index idx_wallet_commission already exists" as message',
    'CREATE INDEX idx_wallet_commission ON wallet(createdby, gross_amount, platform_commission)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show completion message
SELECT 'Commission tracking migration completed successfully' as status;

-- Verify the changes
SELECT
  COLUMN_NAME,
  DATA_TYPE,
  IS_NULLABLE,
  COLUMN_DEFAULT,
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'wallet'
AND COLUMN_NAME IN ('gross_amount', 'platform_commission', 'commission_rate', 'description')
AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
