{"enabled": true, "prefabPath": "F:\\R17DevTools\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar", "packages": ["F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vision-camera\\android\\build\\intermediates\\prefab_package\\release\\prefab", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\intermediates\\prefab_package\\release\\prefab", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\release\\prefab", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\b72e92ab978b0513249f06adacda07ce\\transformed\\jetified-hermes-android-0.79.2-release\\prefab", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab"]}