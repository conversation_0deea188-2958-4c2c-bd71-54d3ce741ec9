AllCops:
  Include:
    - ./Rakefile
    - ./Gemfile
    - ./*.gemspec
  Exclude:
    - ./spec/fixtures/**/*
    - ./vendor/bundle/**/*

# At the moment not ready to be used
# https://github.com/bbatsov/rubocop/issues/947
Documentation:
  Enabled: false

#- CocoaPods -----------------------------------------------------------------#

# We adopted raise instead of fail.
SignalException:
  EnforcedStyle: only_raise

# They are idiomatic
AssignmentInCondition:
  Enabled: false

# Allow backticks
AsciiComments:
  Enabled: false

# Indentation clarifies logic branches in implementations
IfUnlessModifier:
  Enabled: false

# No enforced convention here.
SingleLineBlockParams:
  Enabled: false

# We only add the comment when needed.
Encoding:
  Enabled: false

# Having these make it easier to *not* forget to add one when adding a new
# value and you can simply copy the previous line.
Style/TrailingCommaInArguments:
  EnforcedStyleForMultiline: comma

Style/TrailingCommaInLiteral:
  EnforcedStyleForMultiline: comma

Style/MultilineOperationIndentation:
  EnforcedStyle: indented

# Clashes with CLAide Command#validate!
GuardClause:
  Enabled: false

# Not always desirable: lib/claide/command/plugins_helper.rb:12:15
Next:
  Enabled: false

# Autocorrect makes this cop much more useful, taking away needless guessing
Lint/EndAlignment:
  AutoCorrect: true


# Arbitrary max lengths for classes simply do not work and enabling this will
# lead to a never ending stream of annoyance and changes.
Metrics/ClassLength:
  Enabled: false

# Arbitrary max lengths for modules simply do not work and enabling this will
# lead to a never ending stream of annoyance and changes.
Metrics/ModuleLength:
  Enabled: false

# Arbitrary max lengths for methods simply do not work and enabling this will
# lead to a never ending stream of annoyance and changes.
Metrics/MethodLength:
  Enabled: false

# No enforced convention here.
Metrics/BlockNesting:
  Enabled: false

# It will be obvious which code is complex, Rubocop should only lint simple
# rules for us.
Metrics/AbcSize:
  Enabled: false

# It will be obvious which code is complex, Rubocop should only lint simple
# rules for us.
Metrics/CyclomaticComplexity:
  Enabled: false

# It will be obvious which code is complex, Rubocop should only lint simple
# rules for us.
Metrics/PerceivedComplexity:
  Enabled: false

#- CocoaPods support for Ruby 1.8.7 ------------------------------------------#

HashSyntax:
  EnforcedStyle: hash_rockets

Lambda:
  Enabled: false

DotPosition:
  EnforcedStyle: trailing

EachWithObject:
  Enabled: false

Style/SpecialGlobalVars:
  Enabled: false

#- CocoaPods specs -----------------------------------------------------------#

# Allow for `should.match /regexp/`.
AmbiguousRegexpLiteral:
  Exclude:
    - spec/**/*

Performance/RedundantMatch:
  Exclude:
    - spec/**/*

# Allow `object.should == object` syntax.
Void:
  Exclude:
    - spec/**/*

ClassAndModuleChildren:
  Exclude:
    - spec/**/*

UselessComparison:
  Exclude:
    - spec/**/*
