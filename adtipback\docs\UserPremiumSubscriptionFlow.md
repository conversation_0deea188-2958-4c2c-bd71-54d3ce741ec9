# User Premium Subscription Flow Documentation

## Overview

This document describes the new user premium subscription system that follows a "pay-first, store-later" approach. Only successful payments are stored in the database, ensuring data integrity and reducing unnecessary database entries.

## Key Changes

### 1. Database Structure
- **Only successful payments are stored**: No pre-creation of subscription records
- **Indian timezone support**: All timestamps use IST (+05:30)
- **Simplified table structure**: Uses only `user_subscriptions` table, no transaction table dependency

### 2. Flow Changes
- **Frontend**: Creates subscription on Razorpay first, then processes payment
- **Backend**: Webhook handles database storage only after successful payment
- **User Experience**: Immediate feedback, no pending states

## Database Tables

### `user_subscriptions`
Stores only successful subscription payments.

```sql
CREATE TABLE user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    razorpay_plan_id VARCHAR(255) NOT NULL,
    razorpay_subscription_id VARCHAR(255) NOT NULL UNIQUE,
    status ENUM('active', 'cancelled', 'expired', 'completed') DEFAULT 'active',
    total_count INT DEFAULT 60,
    paid_count INT DEFAULT 1,
    current_start_at TIMESTAMP NULL,
    current_end_at TIMESTAMP NULL,
    charge_at TIMESTAMP NULL,
    start_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    customer_notify TINYINT DEFAULT 1,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_subscription_id (razorpay_subscription_id),
    INDEX idx_status (status),
    INDEX idx_current_end_at (current_end_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### `razorpay_plans`
Caches Razorpay plan details for better performance.

```sql
CREATE TABLE razorpay_plans (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'INR',
    period INT NOT NULL,
    interval VARCHAR(20) NOT NULL,
    billing_cycle VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Users Table Updates
Added two new columns to track premium status:
- `is_premium`: BOOLEAN DEFAULT FALSE
- `premium_expires_at`: TIMESTAMP NULL

## Available Plans

1. **1 Month Plan** - `plan_Qjrw31WPrhunxz` - ₹99/month
2. **6 Months Plan** - `plan_Qjrx1d0dlYu8dI` - ₹499/6 months
3. **12 Months Plan** - `plan_QjrxzWRWoYC5bl` - ₹899/12 months

## API Endpoints

### Get Available Plans
```
GET /api/subscription-plans
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "plans": [
    {
      "id": "plan_Qjrw31WPrhunxz",
      "name": "Premium - 1 Month",
      "amount": 99,
      "currency": "INR",
      "period": 1,
      "interval": "monthly",
      "description": "Access to premium features for 1 month"
    }
  ]
}
```

### Create Subscription
```
POST /api/subscriptions/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "plan_id": "plan_Qjrw31WPrhunxz",
  "user_id": 123
}
```

**Response:**
```json
{
  "status": true,
  "subscription_id": "sub_ABC123",
  "key_id": "rzp_test_..."
}
```

### Get Subscription Status
```
GET /api/subscriptions/status/:userId
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "data": {
    "status": "active",
    "current_end_at": "2024-02-15T10:30:00.000Z",
    "plan_name": "Premium - 1 Month",
    "amount": 99,
    "is_active": true
  }
}
```

### Cancel Subscription
```
POST /api/subscriptions/cancel
Authorization: Bearer <token>
Content-Type: application/json

{
  "user_id": 123
}
```

**Response:**
```json
{
  "status": true,
  "message": "Subscription cancellation initiated. It will be fully cancelled at the end of the current billing cycle."
}
```

### Get Razorpay Details
```
GET /api/razorpay-details
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "api_key": "rzp_test_..."
}
```

## Frontend Integration

### 1. Subscription Screen Flow
```typescript
const handlePayment = async () => {
  // 1. Create subscription on Razorpay (no DB storage yet)
  const subResponse = await ApiService.createSubscription(plan_id, user_id);
  
  // 2. Get Razorpay key
  const razorpayDetails = await ApiService.getRazorpayDetails();
  
  // 3. Open Razorpay Checkout
  RazorpayCheckout.open({
    key: razorpayDetails.api_key,
    subscription_id: subResponse.subscription_id,
    // ... other options
  });
  
  // 4. Webhook handles database storage on success
};
```

### 2. Premium User Screen
New screen added to sidebar navigation that shows:
- Current subscription status
- Subscription details
- Premium benefits
- Cancel/Renew options

### 3. Navigation Updates
Added "Premium Status" menu item to sidebar navigation.

## Webhook Handling

### Subscription Charged Event
```javascript
if (event === "subscription.charged") {
  const sub = payload.subscription.entity;
  const payment = payload.payment.entity;

  if (payment.status === 'captured') {
    // Check if subscription already exists
    const existingSub = await queryRunner(
      "SELECT id FROM user_subscriptions WHERE razorpay_subscription_id = ?", 
      [sub.id]
    );

    if (existingSub.length === 0) {
      // First successful payment - create new subscription record
      await queryRunner(insertQuery, [/* subscription data */]);
    } else {
      // Subsequent payment - update existing subscription
      await queryRunner(updateQuery, [/* updated data */]);
    }

    // Update user's premium status
    await queryRunner(updateUserQuery, [sub.current_end, sub.notes.user_id]);
  }
}
```

### Subscription Cancelled Event
```javascript
if (event === 'subscription.cancelled') {
  const sub = payload.subscription.entity;
  
  // Update subscription status
  await queryRunner(updateQuery, [sub.status, sub.ended_at, sub.id]);
  
  // Update user's premium status to expired
  if (sub.notes && sub.notes.user_id) {
    await queryRunner(updateUserQuery, [sub.notes.user_id]);
  }
}
```

## Benefits of New Flow

### 1. Data Integrity
- Only successful payments are stored
- No orphaned subscription records
- Clean database state

### 2. Better User Experience
- Immediate feedback on payment attempts
- No confusing pending states
- Clear success/failure messages

### 3. Simplified Backend
- No transaction table dependency
- Single source of truth for subscriptions
- Easier to maintain and debug

### 4. Indian Timezone Support
- All timestamps use IST (+05:30)
- Consistent date/time handling
- Better for Indian users

## Migration Steps

### 1. Database Setup
```bash
# Run the setup script
mysql -u username -p database_name < scripts/setup_subscription_tables.sql
```

### 2. Backend Deployment
- Deploy updated `SubscriptionController.js`
- Update API routes
- Configure webhook URL in Razorpay dashboard

### 3. Frontend Deployment
- Deploy updated `SubscriptionScreen.tsx`
- Deploy new `PremiumUserScreen.tsx`
- Update navigation types and MainNavigator

### 4. Testing
- Test subscription creation flow
- Test payment processing
- Test webhook handling
- Test cancellation flow
- Test premium user screen

## Error Handling

### Frontend Errors
- Network failures during subscription creation
- Payment failures in Razorpay
- Invalid plan selection

### Backend Errors
- Webhook signature verification failures
- Database connection issues
- Invalid subscription data

### Recovery Mechanisms
- Retry logic for failed API calls
- Fallback to cached plan data
- Graceful degradation for webhook failures

## Monitoring and Analytics

### Key Metrics
- Subscription creation success rate
- Payment success rate
- Cancellation rate
- Webhook processing time

### Logging
- All subscription events logged
- Payment processing details
- Error tracking and alerting

## Security Considerations

### Webhook Security
- Signature verification for all webhooks
- HTTPS-only webhook endpoints
- Rate limiting on webhook endpoints

### Data Protection
- Encrypted storage of sensitive data
- Secure API key management
- User data privacy compliance

## Future Enhancements

### Planned Features
- Subscription upgrade/downgrade
- Prorated billing
- Multiple payment methods
- Subscription analytics dashboard

### Scalability
- Database optimization for large datasets
- Caching strategies for plan data
- Load balancing for webhook processing 