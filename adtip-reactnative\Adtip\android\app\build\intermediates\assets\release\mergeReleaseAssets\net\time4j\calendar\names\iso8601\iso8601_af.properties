# months
M(a)_1=Jan.
M(a)_2=Feb.
M(a)_3=Mrt.
M(a)_4=Apr.
M(a)_5=<PERSON>
M(a)_6=Jun.
M(a)_7=Jul.
M(a)_8=Aug.
M(a)_9=Sep.
M(a)_10=Okt.
M(a)_11=Nov.
M(a)_12=Des.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=<PERSON><PERSON>rie
M(w)_2=<PERSON>ruarie
M(w)_3=<PERSON><PERSON>
M(w)_4=April
M(w)_5=Mei
M(w)_6=Junie
M(w)_7=<PERSON>
M(w)_8=Augustus
M(w)_9=September
M(w)_10=Oktober
M(w)_11=November
M(w)_12=Desember

M(A)_1=Jan.
M(A)_2=Feb.
M(A)_3=Mrt.
M(A)_4=Apr.
M(A)_5=Mei
M(A)_6=Jun.
M(A)_7=Jul.
M(A)_8=Aug.
M(A)_9=Sep.
M(A)_10=Okt.
M(A)_11=Nov.
M(A)_12=Des.

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Januarie
M(W)_2=Februarie
M(W)_3=Maart
M(W)_4=April
M(W)_5=Mei
M(W)_6=Junie
M(W)_7=Julie
M(W)_8=Augustus
M(W)_9=September
M(W)_10=Oktober
M(W)_11=November
M(W)_12=Desember

# weekdays
D(a)_1=Ma.
D(a)_2=Di.
D(a)_3=Wo.
D(a)_4=Do.
D(a)_5=Vr.
D(a)_6=Sa.
D(a)_7=So.

D(n)_1=M
D(n)_2=D
D(n)_3=W
D(n)_4=D
D(n)_5=V
D(n)_6=S
D(n)_7=S

D(s)_1=Ma.
D(s)_2=Di.
D(s)_3=Wo.
D(s)_4=Do.
D(s)_5=Vr.
D(s)_6=Sa.
D(s)_7=So.

D(w)_1=Maandag
D(w)_2=Dinsdag
D(w)_3=Woensdag
D(w)_4=Donderdag
D(w)_5=Vrydag
D(w)_6=Saterdag
D(w)_7=Sondag

D(A)_1=Ma.
D(A)_2=Di.
D(A)_3=Wo.
D(A)_4=Do.
D(A)_5=Vr.
D(A)_6=Sa.
D(A)_7=So.

D(N)_1=M
D(N)_2=D
D(N)_3=W
D(N)_4=D
D(N)_5=V
D(N)_6=S
D(N)_7=S

D(S)_1=Ma.
D(S)_2=Di.
D(S)_3=Wo.
D(S)_4=Do.
D(S)_5=Vr.
D(S)_6=Sa.
D(S)_7=So.

D(W)_1=Maandag
D(W)_2=Dinsdag
D(W)_3=Woensdag
D(W)_4=Donderdag
D(W)_5=Vrydag
D(W)_6=Saterdag
D(W)_7=Sondag

# quarters
Q(a)_1=K1
Q(a)_2=K2
Q(a)_3=K3
Q(a)_4=K4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1ste kwartaal
Q(w)_2=2de kwartaal
Q(w)_3=3de kwartaal
Q(w)_4=4de kwartaal

Q(A)_1=K1
Q(A)_2=K2
Q(A)_3=K3
Q(A)_4=K4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1ste kwartaal
Q(W)_2=2de kwartaal
Q(W)_3=3de kwartaal
Q(W)_4=4de kwartaal

# day-period-rules
T0000=night1
T0500=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=middernag
P(a)_am=vm.
P(a)_pm=nm.
P(a)_morning1=die oggend
P(a)_afternoon1=die middag
P(a)_evening1=die aand
P(a)_night1=die nag

P(n)_midnight=mn
P(n)_am=v
P(n)_pm=n
P(n)_morning1=o
P(n)_afternoon1=m
P(n)_evening1=a
P(n)_night1=n

P(w)_midnight=middernag
P(w)_am=vm.
P(w)_pm=nm.
P(w)_morning1=die oggend
P(w)_afternoon1=die middag
P(w)_evening1=die aand
P(w)_night1=die nag

P(A)_midnight=middernag
P(A)_am=vm.
P(A)_pm=nm.
P(A)_morning1=oggend
P(A)_afternoon1=middag
P(A)_evening1=aand
P(A)_night1=nag

P(N)_midnight=mn
P(N)_am=v
P(N)_pm=n
P(N)_morning1=o
P(N)_afternoon1=m
P(N)_evening1=a
P(N)_night1=n

P(W)_midnight=middernag
P(W)_am=vm.
P(W)_pm=nm.
P(W)_morning1=oggend
P(W)_afternoon1=middag
P(W)_evening1=aand
P(W)_night1=nag

# eras
E(w)_0=voor Christus
E(w|alt)_0=voor die gewone jaartelling
E(w)_1=na Christus
E(w|alt)_1=gewone jaartelling

E(a)_0=v.C.
E(a|alt)_0=v.g.j.
E(a)_1=n.C.
E(a|alt)_1=g.j.

E(n)_0=v.C.
E(n|alt)_0=vgj
E(n)_1=n.C.
E(n|alt)_1=gj

# format patterns
F(f)_d=EEEE dd MMMM y
F(l)_d=dd MMMM y
F(m)_d=dd MMM y
F(s)_d=y-MM-dd

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd-MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM-y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='week' w 'van' Y

I={0} – {1}

# labels of elements
L_era=era
L_year=jaar
L_quarter=kwartaal
L_month=maand
L_week=week
L_day=dag
L_weekday=dag van die week
L_dayperiod=vm./nm.
L_hour=uur
L_minute=minuut
L_second=sekonde
L_zone=tydsone
