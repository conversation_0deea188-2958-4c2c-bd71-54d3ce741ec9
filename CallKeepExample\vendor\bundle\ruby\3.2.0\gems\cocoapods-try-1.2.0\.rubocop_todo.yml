# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2020-04-16 22:06:47 -0700 using RuboCop version 0.50.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 1
# Cop supports --auto-correct.
Layout/EmptyLineAfterMagicComment:
  Exclude:
    - 'cocoapods-try.gemspec'

# Offense count: 2
# Configuration parameters: ContextCreatingMethods, MethodCreatingMethods.
Lint/UselessAccessModifier:
  Exclude:
    - 'lib/pod/command/try.rb'

# Offense count: 37
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Metrics/LineLength:
  Max: 108

# Offense count: 3
# Configuration parameters: ExpectMatchingDefinition, Regex, IgnoreExecutableScripts, AllowedAcronyms.
# AllowedAcronyms: CLI, DSL, ACL, API, ASCII, CPU, CSS, DNS, EOF, GUID, HTML, HTTP, HTTPS, ID, IP, JSON, LHS, QPS, RAM, RHS, RPC, SLA, SMTP, SQL, SSH, TCP, TLS, TTL, UDP, UI, UID, UUID, URI, URL, UTF8, VM, XML, XMPP, XSRF, XSS
Naming/FileName:
  Exclude:
    - 'Gemfile'
    - 'Rakefile'
    - 'cocoapods-try.gemspec'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: AutoCorrect, EnforcedStyle, SupportedStyles.
# SupportedStyles: predicate, comparison
Style/NumericPredicate:
  Exclude:
    - 'spec/**/*'
    - 'lib/pod/command/try.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: PreferredDelimiters.
Style/PercentLiteralDelimiters:
  Exclude:
    - 'cocoapods-try.gemspec'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: use_perl_names, use_english_names
Style/SpecialGlobalVars:
  Exclude:
    - 'cocoapods-try.gemspec'

# Offense count: 8
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles, ConsistentQuotesInMultiline.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiterals:
  Exclude:
    - 'cocoapods-try.gemspec'

# Offense count: 1
# Cop supports --auto-correct.
Style/UnneededPercentQ:
  Exclude:
    - 'cocoapods-try.gemspec'
