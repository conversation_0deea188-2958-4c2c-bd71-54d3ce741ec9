const razorpay = require("razorpay");
const { queryRunner } = require("../dbConfig/queryRunner");
const crypto = require("crypto");
const { addGSTToPlans } = require("../utils/gstCalculator");
require("dotenv").config();

const razorpayInstance = new razorpay({
  key_id: process.env.RAZOR_PAY_KEY_ID,
  key_secret: process.env.RAZOR_PAY_KEY_SECRET,
});

const razorpayInstanceTest = new razorpay({
  key_id: process.env.RAZOR_PAY_TEST_KEY_ID,
  key_secret: process.env.RAZOR_PAY_TEST_KEY_SECRET,
});

// Get available subscription plans from database (cached)
exports.getSubscriptionPlans = async (req, res) => {
  try {
    const query = `
      SELECT 
        id,
        name,
        description,
        amount,
        currency,
        period,
        plan_interval,
        billing_cycle
      FROM razorpay_plans 
      WHERE is_active = true 
      ORDER BY amount ASC
    `;
    
    const plans = await queryRunner(query);
    
    const formattedPlans = plans.map((plan) => ({
      id: plan.id,
      name: plan.name,
      amount: parseFloat(plan.amount),
      currency: plan.currency,
      period: plan.period,
      interval: plan.plan_interval,
      description: plan.description,
    }));

    res.status(200).json({ status: true, plans: formattedPlans });
  } catch (error) {
    console.error("Error fetching subscription plans:", error);
    res.status(500).json({ status: false, message: "Error fetching plans" });
  }
};

exports.getSubscriptionPlansTest = async (req, res) => {
  try {
    const query = `
      SELECT
        id,
        name,
        description,
        amount,
        currency,
        period,
        plan_interval,
        billing_cycle
      FROM razorpay_plans_test
      WHERE is_active = true
      ORDER BY amount ASC
    `;

    const plans = await queryRunner(query);

    const formattedPlans = plans.map((plan) => ({
      id: plan.id,
      name: plan.name,
      amount: parseFloat(plan.amount),
      currency: plan.currency,
      period: plan.period,
      interval: plan.plan_interval,
      description: plan.description,
    }));

    res.status(200).json({ status: true, plans: formattedPlans });
  } catch (error) {
    console.error("Error fetching subscription plans:", error);
    res.status(500).json({ status: false, message: "Error fetching plans" });
  }
};

// Get subscription plans with GST for Razorpay checkout
exports.getSubscriptionPlansWithGST = async (req, res) => {
  try {
    const query = `
      SELECT
        id,
        name,
        description,
        amount,
        currency,
        period,
        plan_interval,
        billing_cycle
      FROM razorpay_plans
      WHERE is_active = true
      ORDER BY amount ASC
    `;

    const plans = await queryRunner(query);

    const formattedPlans = plans.map((plan) => ({
      id: plan.id,
      name: plan.name,
      amount: parseFloat(plan.amount),
      currency: plan.currency,
      period: plan.period,
      interval: plan.plan_interval,
      description: plan.description,
    }));

    // Add GST calculations to all plans
    const plansWithGST = addGSTToPlans(formattedPlans);

    res.status(200).json({ status: true, plans: plansWithGST });
  } catch (error) {
    console.error("Error fetching subscription plans with GST:", error);
    res.status(500).json({ status: false, message: "Error fetching plans with GST" });
  }
};

// Get test subscription plans with GST for Razorpay checkout
exports.getSubscriptionPlansTestWithGST = async (req, res) => {
  try {
    const query = `
      SELECT
        id,
        name,
        description,
        amount,
        currency,
        period,
        plan_interval,
        billing_cycle
      FROM razorpay_plans_test
      WHERE is_active = true
      ORDER BY amount ASC
    `;

    const plans = await queryRunner(query);

    const formattedPlans = plans.map((plan) => ({
      id: plan.id,
      name: plan.name,
      amount: parseFloat(plan.amount),
      currency: plan.currency,
      period: plan.period,
      interval: plan.plan_interval,
      description: plan.description,
    }));

    // Add GST calculations to all plans
    const plansWithGST = addGSTToPlans(formattedPlans);

    res.status(200).json({ status: true, plans: plansWithGST });
  } catch (error) {
    console.error("Error fetching test subscription plans with GST:", error);
    res.status(500).json({ status: false, message: "Error fetching test plans with GST" });
  }
};
// Create a subscription on Razorpay (no database storage until payment success)
exports.createSubscription = async (req, res) => {
  const { plan_id, user_id } = req.body;

  if (!plan_id || !user_id) {
    return res.status(400).json({ status: false, message: "plan_id and user_id are required" });
  }

  try {
    // Create subscription on Razorpay
    const subscription = await razorpayInstance.subscriptions.create({
      plan_id: plan_id,
      customer_notify: 1,
      total_count: 60, // Total number of billing cycles
      notes: {
        user_id: user_id.toString(),
        subscription_type: 'user_premium'
      }
    });

    // Return subscription ID for frontend to proceed with payment
    res.status(201).json({ 
      status: true, 
      subscription_id: subscription.id, 
      key_id: process.env.RAZOR_PAY_KEY_ID 
    });

  } catch (error) {
    console.error("Error creating subscription:", error);
    res.status(500).json({ 
      status: false, 
      message: "Error creating subscription", 
      error: error.message 
    });
  }
};

exports.createSubscriptionTest = async (req, res) => {
  const { plan_id, user_id } = req.body;

  if (!plan_id || !user_id) {
    return res.status(400).json({ status: false, message: "plan_id and user_id are required" });
  }

  try {
    // Create subscription on Razorpay
    const subscription = await razorpayInstanceTest.subscriptions.create({
      plan_id: plan_id,
      customer_notify: 1,
      total_count: 60, // Total number of billing cycles
      notes: {
        user_id: user_id.toString(),
        subscription_type: 'user_premium'
      }
    });

    // Return subscription ID for frontend to proceed with payment
    res.status(201).json({ 
      status: true, 
      subscription_id: subscription.id, 
      key_id: process.env.RAZOR_PAY_KEY_ID 
    });

  } catch (error) {
    console.error("Error creating subscription:", error);
    res.status(500).json({ 
      status: false, 
      message: "Error creating subscription", 
      error: error.message 
    });
  }
};

// Cancel a subscription
exports.cancelSubscription = async (req, res) => {
  const { user_id } = req.body;

  if (!user_id) {
    return res.status(400).json({ status: false, message: "user_id is required" });
  }

  try {
    // Find the active subscription for the user
    const findQuery = "SELECT razorpay_subscription_id FROM user_subscriptions WHERE user_id = ? AND status = 'active'";
    const subscriptions = await queryRunner(findQuery, [user_id]);

    if (subscriptions.length === 0) {
      return res.status(404).json({ status: false, message: "No active subscription found for this user." });
    }

    const subId = subscriptions[0].razorpay_subscription_id;

    // Cancel on Razorpay
    await razorpayInstance.subscriptions.cancel(subId, { cancel_at_cycle_end: true });

    // Update database status to cancelled
    const updateQuery = "UPDATE user_subscriptions SET status = 'cancelled', updated_at = NOW() WHERE razorpay_subscription_id = ?";
    await queryRunner(updateQuery, [subId]);

    // Update user's premium status
    const updateUserQuery = "UPDATE users SET is_premium = FALSE, premium_expires_at = NULL WHERE id = ?";
    await queryRunner(updateUserQuery, [user_id]);

    res.status(200).json({ 
      status: true, 
      message: "Subscription cancellation initiated. It will be fully cancelled at the end of the current billing cycle." 
    });

  } catch (error) {
    console.error("Error cancelling subscription:", error);
    res.status(500).json({ 
      status: false, 
      message: "Error cancelling subscription", 
      error: error.message 
    });
  }
};

exports.cancelSubscriptionTest = async (req, res) => {
  const { user_id } = req.body;

  if (!user_id) {
    return res.status(400).json({ status: false, message: "user_id is required" });
  }

  try {
    // Find the active subscription for the user
    const findQuery = "SELECT razorpay_subscription_id FROM user_subscriptions WHERE user_id = ? AND status = 'active'";
    const subscriptions = await queryRunner(findQuery, [user_id]);

    if (subscriptions.length === 0) {
      return res.status(404).json({ status: false, message: "No active subscription found for this user." });
    }

    const subId = subscriptions[0].razorpay_subscription_id;

    // Cancel on Razorpay
    await razorpayInstanceTest.subscriptions.cancel(subId, { cancel_at_cycle_end: true });

    // Update database status to cancelled
    const updateQuery = "UPDATE user_subscriptions SET status = 'cancelled', updated_at = NOW() WHERE razorpay_subscription_id = ?";
    await queryRunner(updateQuery, [subId]);

    // Update user's premium status
    const updateUserQuery = "UPDATE users SET is_premium = FALSE, premium_expires_at = NULL WHERE id = ?";
    await queryRunner(updateUserQuery, [user_id]);

    res.status(200).json({ 
      status: true, 
      message: "Subscription cancellation initiated. It will be fully cancelled at the end of the current billing cycle." 
    });

  } catch (error) {
    console.error("Error cancelling subscription:", error);
    res.status(500).json({ 
      status: false, 
      message: "Error cancelling subscription", 
      error: error.message 
    });
  }
};

// Get user's subscription status
exports.getSubscriptionStatus = async (req, res) => {
  const { userId } = req.params;
  if (!userId) {
    return res.status(400).json({ status: false, message: "User ID is required" });
  }

  // Fallback plans (should match /api/subscription-plans)
  const fallbackPlans = [
    // Production plans
    {
      id: "plan_Qjrw31WPrhunxz",
      name: "Premium - 1 Month",
      amount: 200,
      currency: "INR",
      period: 1,
      interval: "monthly",
      description: "Access to premium features for 1 month",
      billing_cycle: "1 month"
    },
    {
      id: "plan_Qjrx1d0dlYu8dI",
      name: "Premium - 6 Months",
      amount: 1200,
      currency: "INR",
      period: 6,
      interval: "monthly",
      description: "Access to premium features for 6 months",
      billing_cycle: "6 months"
    },
    {
      id: "plan_QjrxzWRWoYC5bl",
      name: "Premium - 12 Months",
      amount: 2400,
      currency: "INR",
      period: 12,
      interval: "monthly",
      description: "Access to premium features for 12 months",
      billing_cycle: "12 months"
    },
    // Test plans
    {
      id: "plan_Qo7B8jHsnyanTW",
      name: "Premium - 1 Month",
      amount: 200,
      currency: "INR",
      period: 1,
      interval: "monthly",
      description: "Access to premium features for 1 month",
      billing_cycle: "1 month"
    },
    {
      id: "plan_Qo7BRAP5OFxO1O",
      name: "Premium - 6 Months",
      amount: 1200,
      currency: "INR",
      period: 6,
      interval: "monthly",
      description: "Access to premium features for 6 months",
      billing_cycle: "6 months"
    },
    {
      id: "plan_Qo7BgEHYrUvCIP",
      name: "Premium - 12 Months",
      amount: 2400,
      currency: "INR",
      period: 12,
      interval: "monthly",
      description: "Access to premium features for 12 months",
      billing_cycle: "12 months"
    }
  ];

  try {
    const query = `
      SELECT 
        s.status, 
        s.current_end_at,
        s.razorpay_plan_id,
        s.razorpay_subscription_id,
        s.paid_count,
        s.total_count,
        s.start_at,
        s.ended_at,
        s.created_at,
        p.name as plan_name,
        p.description as plan_description,
        p.amount,
        p.currency,
        p.plan_interval,
        p.billing_cycle,
        u.is_premium,
        u.premium_expires_at
      FROM user_subscriptions s
      LEFT JOIN razorpay_plans p ON s.razorpay_plan_id = p.id
      LEFT JOIN users u ON s.user_id = u.id
      WHERE s.user_id = ?
      ORDER BY s.created_at DESC
      LIMIT 1
    `;
    const result = await queryRunner(query, [userId]);
    
    if (result.length > 0) {
      // Check if subscription is still active
      const subscription = result[0];
      const isActive = subscription.status === 'active' && 
                      subscription.current_end_at && 
                      new Date(subscription.current_end_at) > new Date();

      // Fallback if plan details are missing
      if (!subscription.plan_name || !subscription.amount || !subscription.billing_cycle) {
        const plan = fallbackPlans.find(p => p.id === subscription.razorpay_plan_id);
        if (plan) {
          subscription.plan_name = plan.name;
          subscription.plan_description = plan.description;
          subscription.amount = plan.amount;
          subscription.currency = plan.currency;
          subscription.plan_interval = plan.interval;
          subscription.billing_cycle = plan.billing_cycle;
        }
      }

      res.status(200).json({ 
        status: true, 
        data: {
          ...subscription,
          is_active: isActive
        }
      });
    } else {
      // Return 200 with no subscription data instead of 404
      res.status(200).json({ 
        status: false, 
        message: "No subscription found",
        data: null
      });
    }
  } catch (error) {
    console.error("Error getting subscription status:", error);
    console.error("Error details:", {
      userId,
      errorMessage: error.message,
      errorStack: error.stack
    });
    res.status(500).json({ 
      status: false, 
      message: "Error fetching subscription status",
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Razorpay webhook for handling subscription events
exports.handleWebhook = async (req, res) => {
  const secret = process.env.RAZORPAY_WEBHOOK_SECRET || 'adtip_webhook_2024_secret';
  const signature = req.headers["x-razorpay-signature"];

  try {
    // Log every webhook event for debugging
    console.log('Received Razorpay webhook event:', req.body.event, JSON.stringify(req.body, null, 2));

    // Verify webhook signature
    const shasum = crypto.createHmac("sha256", secret);
    shasum.update(JSON.stringify(req.body));
    const digest = shasum.digest("hex");

    if (digest !== signature) {
      return res.status(400).json({ status: "Signature is not valid" });
    }

    const event = req.body.event;
    const payload = req.body.payload;

    if (event === "subscription.charged") {
      const sub = payload.subscription.entity;
      const payment = payload.payment.entity;

      // Only store in database if this is a successful payment
      if (payment.status === 'captured') {
        // Check if subscription already exists
        const checkQuery = "SELECT id FROM user_subscriptions WHERE razorpay_subscription_id = ?";
        const existingSub = await queryRunner(checkQuery, [sub.id]);

        if (existingSub.length === 0) {
          // First successful payment - create new subscription record
          const insertQuery = `
            INSERT INTO user_subscriptions (
              user_id, razorpay_plan_id, razorpay_subscription_id, status, 
              total_count, paid_count, current_start_at, current_end_at, 
              charge_at, start_at, customer_notify, notes
            )
            VALUES (?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?), FROM_UNIXTIME(?), FROM_UNIXTIME(?), FROM_UNIXTIME(?), ?, ?)
          `;
          await queryRunner(insertQuery, [
            sub.notes.user_id,
            sub.plan_id,
            sub.id,
            sub.status,
            sub.total_count,
            sub.paid_count,
            sub.current_start,
            sub.current_end,
            sub.charge_at,
            sub.start_at,
            sub.customer_notify,
            JSON.stringify(sub.notes)
          ]);
        } else {
          // Subsequent payment - update existing subscription
          // Always update paid_count and current_end_at for recurring payments
          const updateQuery = `
            UPDATE user_subscriptions 
            SET status = ?, paid_count = ?, current_start_at = FROM_UNIXTIME(?), 
                current_end_at = FROM_UNIXTIME(?), charge_at = FROM_UNIXTIME(?), updated_at = NOW()
            WHERE razorpay_subscription_id = ?
          `;
          await queryRunner(updateQuery, [
            sub.status,
            sub.paid_count,
            sub.current_start,
            sub.current_end,
            sub.charge_at,
            sub.id
          ]);
        }

        // Update user's premium status
        const updateUserQuery = `
          UPDATE users 
          SET is_premium = TRUE, 
              premium_expires_at = FROM_UNIXTIME(?)
          WHERE id = ?
        `;
        await queryRunner(updateUserQuery, [sub.current_end, sub.notes.user_id]);
      }

      // Note: Razorpay will only charge up to total_count (e.g., 60 months = 5 years)
      // After that, no more auto-payments will be attempted.

    } else if (event === 'subscription.cancelled' || event === 'subscription.halted' || event === 'subscription.completed') {
      const sub = payload.subscription.entity;
      // Update subscription status
      const updateQuery = `
        UPDATE user_subscriptions 
        SET status = ?, ended_at = FROM_UNIXTIME(?), updated_at = NOW()
        WHERE razorpay_subscription_id = ?
      `;
      await queryRunner(updateQuery, [sub.status, sub.ended_at, sub.id]);
      // Update user's premium status to expired
      if (sub.notes && sub.notes.user_id) {
        const updateUserQuery = "UPDATE users SET is_premium = FALSE, premium_expires_at = NULL WHERE id = ?";
        await queryRunner(updateUserQuery, [sub.notes.user_id]);
      }
    }

    res.status(200).json({ status: "ok" });

  } catch (error) {
    console.error("Webhook handling error:", error);
    res.status(500).json({ status: "error" });
  }
};

// Get Razorpay key for frontend
exports.getRazorpayDetails = async (req, res) => {
  try {
    res.status(200).json({ 
      status: true, 
      api_key: process.env.RAZOR_PAY_KEY_ID 
    });
  } catch (error) {
    console.error("Error getting Razorpay details:", error);
    res.status(500).json({ status: false, message: "Error fetching Razorpay details" });
  }
}; 