{"cells": [{"cell_type": "code", "execution_count": null, "id": "cf7c47d0", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["hi vivek1"]}, {"cell_type": "markdown", "id": "06b21f55", "metadata": {}, "source": ["# razor pay"]}, {"cell_type": "code", "execution_count": null, "id": "002e797a", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{{base_url}}/api/razorpay-order"]}, {"cell_type": "code", "execution_count": null, "id": "75b78a97", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{\n", "  \"amount\": 200,\n", "  \"currency\": \"INR\",\n", "  \"user_id\": 51521\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "2e0aeeac", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{\n", "    \"status\": true,\n", "    \"data\": {\n", "        \"amount\": 20000,\n", "        \"amount_due\": 20000,\n", "        \"amount_paid\": 0,\n", "        \"attempts\": 0,\n", "        \"created_at\": 1745409899,\n", "        \"currency\": \"INR\",\n", "        \"entity\": \"order\",\n", "        \"id\": \"order_QMUbIEoATuSAVi\",\n", "        \"notes\": [],\n", "        \"offer_id\": null,\n", "        \"receipt\": \"receipt_1745409899210_51521\",\n", "        \"status\": \"created\",\n", "        \"user_id\": 51521\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "e8dc1a31", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# it will create data in :\n", "\n", "select * from orders where user_id=\"50816\";\n"]}, {"cell_type": "code", "execution_count": null, "id": "aec7bacf", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{{local_url}}/api/razorpay-verification"]}, {"cell_type": "code", "execution_count": null, "id": "1fb7ee63", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{\n", "  \"order_id\": \"order_123456789\",\n", "  \"razorpay_payment_id\": \"pay_987654321\",\n", "  \"razorpay_signature\": \"e2f8a9b7c3d1e4f5a6b7c8d9e0f1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9\",\n", "  \"amount\": 200,\n", "  \"currency\": \"INR\",\n", "  \"user_id\": 51521,\n", "  \"payment_status\": \"success\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "0f42ac3c", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["key_id\tkey_secret\n", "rzp_test_Wpj489Mi7DAY7c\t12byYKVqIjGXTeQobfqVyjQh\n"]}, {"cell_type": "markdown", "id": "d19b3a34", "metadata": {}, "source": ["# test keys "]}, {"cell_type": "code", "execution_count": null, "id": "34bfe921", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["RAZOR_PAY_KEY_ID = \"rzp_test_ojNkCSTYuUL3w9\"\n", "RAZOR_PAY_KEY_SECRET = \"yhXya3G3LlpTfq4gBUyhSEPr\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "f01427cb", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "060f192f", "metadata": {}, "source": ["# live keys"]}, {"cell_type": "code", "execution_count": null, "id": "64c6a0bf", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["RAZOR_PAY_KEY_ID = \"***********************\"\n", "RAZOR_PAY_KEY_SECRET = \"H3voq5jxtB5GfvmIHUVZQfMy\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "f37704a2", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# it will creata data in tarnsactions :\n", "select * from transactions where user_id=\"50816\";\n"]}, {"cell_type": "markdown", "id": "0983f6ab", "metadata": {}, "source": ["# testing :"]}, {"cell_type": "code", "execution_count": null, "id": "c818d828", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["Step 2: Simulate Other Payment Methods (Optional)\n", "\n", "If you want to test other payment methods like UPI, Wallet, or Netbanking:\n", "\n", "    UPI:\n", "        Use a test VPA like success@razorpay to simulate a successful payment.\n", "        Use failure@razorpay to simulate a failed payment.\n", "        After entering the VPA, Ra<PERSON>pay will simulate the payment flow.\n", "    Wallet (e.g., Freecharge):\n", "        Select a wallet like Freecharge.\n", "        Use test credentials (e.g., phone number **********, OTP 123456).\n", "    Netbanking:\n", "        Select a bank (e.g., HDFC).\n", "        Razorpay will show a mock bank page. Choose \"Success\" to simulate a successful payment."]}, {"cell_type": "code", "execution_count": null, "id": "2ee3ebb0", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["success@razorpay"]}, {"cell_type": "code", "execution_count": null, "id": "a114dbd5", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.CP2hoyHw7dOjB8A6uIifbdfNsztf0Pt1BSw8pEdM92Q"]}, {"cell_type": "code", "execution_count": null, "id": "dd2ab0c1", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["Payment ID: pay_QMWWvvsj2pSsSA\n", "Order ID: order_QMWWgHBUXm9ou0\n", "Signature: dd0cf76637aefe0d3742c56aa2caee741f9bc961f57d2181a454affd451f9438"]}, {"cell_type": "markdown", "id": "161f551f", "metadata": {}, "source": ["# upgrade premium :"]}, {"cell_type": "code", "execution_count": null, "id": "cb89bbe5", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8315da28", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["#it will update here in premium plans\n", "select * from user_premium_plans where user_id=\"50816\";\n"]}, {"cell_type": "code", "execution_count": null, "id": "20a58223", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{{local_url}}/api/upgrade-premium"]}, {"cell_type": "code", "execution_count": null, "id": "10c3e1ad", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{\n", "  \"payment_status\": \"success\",\n", "  \"user_id\": 51521,\n", "  \"plan_id\": 1,\n", "  \"order_id\": \"order_QMWWgHBUXm9ou0\",\n", "  \"payment_id\": \"pay_QMWWvvsj2pSsSA\",\n", "  \"coupon_code\": \"\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "1ef7f858", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{\n", "    \"status\": true,\n", "    \"message\": \"User upgraded to premium successfully\"\n", "}"]}, {"cell_type": "markdown", "id": "270f922a", "metadata": {}, "source": ["# Add Funds:"]}, {"cell_type": "code", "execution_count": null, "id": "cf5089df", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{{base_url}}/api/getfunds/50816"]}, {"cell_type": "code", "execution_count": null, "id": "c33b7eed", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["{\n", "  \"createdby\": 50816,\n", "  \"amount\": 10,\n", "  \"transactionStatus\": \"1\",\n", "  \"transaction_type\": \"Deposite\",\n", "  \"order_id\" : \"\",\n", "  \"payment_id\" : \"\",\n", "  \"status\" :\"\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "b502e83f", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}