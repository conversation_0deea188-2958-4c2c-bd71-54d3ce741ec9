const ContactService = require('../services/ContactService');

const ContactController = {
  // Submit contact form
  submitContactForm: (req, res, next) => {
    console.log('[ContactController] Contact form submission received:', req.body);
    
    // Validate required fields
    const { name, email, subject, message } = req.body;
    
    if (!name || !email || !subject || !message) {
      return res.status(400).send({
        status: 400,
        message: "Name, email, subject, and message are required fields.",
        data: []
      });
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).send({
        status: 400,
        message: "Please provide a valid email address.",
        data: []
      });
    }

    // Prepare submission data
    const submissionData = {
      user_id: req.user?.user_id || null, // From JWT token if authenticated
      name: name.trim(),
      email: email.trim().toLowerCase(),
      phone: req.body.phone ? req.body.phone.trim() : null,
      subject: subject.trim(),
      message: message.trim(),
      category: req.body.category || 'general',
      priority: req.body.priority || 'medium',
      ip_address: req.ip || req.connection.remoteAddress,
      user_agent: req.get('User-Agent'),
      app_version: req.body.app_version || null,
      device_info: req.body.device_info ? JSON.stringify(req.body.device_info) : null,
      attachments: req.body.attachments ? JSON.stringify(req.body.attachments) : null
    };

    return ContactService.submitContactForm(submissionData)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.error('[ContactController] Error submitting contact form:', err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Failed to submit contact form. Please try again.",
          data: []
        });
      });
  },

  // Get contact submissions for admin (with pagination)
  getContactSubmissions: (req, res, next) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const status = req.query.status || null;
    const category = req.query.category || null;
    const priority = req.query.priority || null;

    const filters = {
      status,
      category,
      priority,
      page,
      limit
    };

    return ContactService.getContactSubmissions(filters)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.error('[ContactController] Error fetching contact submissions:', err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Failed to fetch contact submissions.",
          data: []
        });
      });
  },

  // Get specific contact submission by ID
  getContactSubmissionById: (req, res, next) => {
    const submissionId = parseInt(req.params.id);
    
    if (!submissionId || isNaN(submissionId)) {
      return res.status(400).send({
        status: 400,
        message: "Invalid submission ID.",
        data: []
      });
    }

    return ContactService.getContactSubmissionById(submissionId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.error('[ContactController] Error fetching contact submission:', err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Failed to fetch contact submission.",
          data: []
        });
      });
  },

  // Update contact submission status (admin only)
  updateContactSubmissionStatus: (req, res, next) => {
    const submissionId = parseInt(req.params.id);
    const { status, admin_notes, resolved_by } = req.body;
    
    if (!submissionId || isNaN(submissionId)) {
      return res.status(400).send({
        status: 400,
        message: "Invalid submission ID.",
        data: []
      });
    }

    if (!status) {
      return res.status(400).send({
        status: 400,
        message: "Status is required.",
        data: []
      });
    }

    const updateData = {
      status,
      admin_notes: admin_notes || null,
      resolved_by: resolved_by || req.user?.user_id || null,
      resolved_at: status === 'resolved' || status === 'closed' ? new Date() : null
    };

    return ContactService.updateContactSubmissionStatus(submissionId, updateData)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.error('[ContactController] Error updating contact submission:', err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Failed to update contact submission.",
          data: []
        });
      });
  },

  // Add response to contact submission (admin only)
  addContactResponse: (req, res, next) => {
    const submissionId = parseInt(req.params.id);
    const { response_message, is_internal_note } = req.body;
    
    if (!submissionId || isNaN(submissionId)) {
      return res.status(400).send({
        status: 400,
        message: "Invalid submission ID.",
        data: []
      });
    }

    if (!response_message || !response_message.trim()) {
      return res.status(400).send({
        status: 400,
        message: "Response message is required.",
        data: []
      });
    }

    const responseData = {
      submission_id: submissionId,
      admin_id: req.user?.user_id || null,
      response_message: response_message.trim(),
      is_internal_note: is_internal_note || false
    };

    return ContactService.addContactResponse(responseData)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.error('[ContactController] Error adding contact response:', err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Failed to add response.",
          data: []
        });
      });
  }
};

module.exports = ContactController;
