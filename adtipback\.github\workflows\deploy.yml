name: Deploy to EC2

on:
  push:
    branches:
      - staging

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy to EC2
        env:
          EC2_HOST: ${{ secrets.EC2_HOST }}
          EC2_USER: ${{ secrets.EC2_USER }}
          EC2_KEY: ${{ secrets.EC2_KEY }}
        run: |
          echo "EC2_HOST is: $EC2_HOST"
          echo "EC2_USER is: $EC2_USER"
          echo "$EC2_KEY" > private_key.pem
          chmod 600 private_key.pem
          ssh -o StrictHostKeyChecking=no -i private_key.pem ${EC2_USER}@${EC2_HOST} '
            cd /home/<USER>/ci-cd-adtip-backend/adtipback || exit 1 &&
            echo "Cleaning repository state..." &&
            git reset --hard || echo "Reset failed, continuing..." &&
            git checkout staging &&
            git fetch origin &&
            # Stash any local changes to avoid conflicts
            git stash || echo "No local changes to stash" &&
            git pull --rebase origin staging || {
              echo "Rebase failed, aborting and forcing pull...";
              git rebase --abort;
              git fetch origin;
              git reset --hard origin/staging;
            } &&
            echo "Installing dependencies..." &&
            npm install || {
              echo "npm install failed, forcing clean install...";
              rm -rf node_modules package-lock.json;
              npm install;
            } &&
            echo "Restarting application with PM2..." &&
            pm2 restart adtip-server --env staging || {
              echo "PM2 restart failed, attempting to start...";
              pm2 start adtip-server --env staging;
            } &&
            echo "Cleaning up..." &&
            pm2 save &&
            git stash pop || echo "No stashed changes to pop"
          '
          rm private_key.pem
        shell: /usr/bin/bash -e {0}