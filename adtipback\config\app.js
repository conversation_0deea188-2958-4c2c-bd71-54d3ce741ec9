const express = require("express");
const app = express();
const path = require("path");
const bodyParser = require("body-parser");
const helmet = require("helmet");
const cors = require("cors");
const { performanceMonitor, startPerformanceMonitoring } = require("../middleware/performanceMonitor");

// Add content-type for apple-app-site-association (iOS Universal Links)
app.use('/.well-known/apple-app-site-association', (req, res, next) => {
  res.type('application/json');
  next();
});

app.use(express.static(path.join(__dirname, "../public")));
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json({ limit: "50mb", extended: true }));
app.use(helmet());
app.use(cors());

// Add performance monitoring middleware
app.use(performanceMonitor);

// Start performance monitoring
startPerformanceMonitoring();

// CORS
app.all("/*", (req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept"
  );
  next();
});

module.exports = app;
