
#ifndef BOOST_MPL_AUX_HAS_BEGIN_HPP_INCLUDED
#define BOOST_MPL_AUX_HAS_BEGIN_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2002-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

#include <boost/mpl/has_xxx.hpp>

namespace boost { namespace mpl { namespace aux {
BOOST_MPL_HAS_XXX_TRAIT_NAMED_DEF(has_begin, begin, true)
}}}

#endif // BOOST_MPL_AUX_HAS_BEGIN_HPP_INCLUDED
