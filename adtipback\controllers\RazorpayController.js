const {
  createOrder,
  verifyAndUpsertTransaction,
} = require("../services/Razorpay");

const razorpayOrderCreation = async (req, res) => {
  try {
    const { amount, currency, user_id } = req.body;

    if (!amount || typeof amount !== "number" || amount <= 0) {
      return res.status(400).json({
        status: false,
        message: "Invalid amount provided.",
      });
    }

    const order = await createOrder(amount, currency, user_id);
    res.status(200).json({
      status: true,
      data: order.orderData,
    });
  } catch (error) {
    console.error("Error in create-order controller:", error);
    res.status(500).json({
      status: false,
      message: error.message || "Failed to create order",
      error,
    });
  }
};

const razorpayPaymentSignatue = async (req, res) => {
  try {
    const {
      order_id,
      razorpay_payment_id,
      razorpay_signature,
      amount,
      currency,
      user_id,
      payment_status,
      transaction_for, // <-- Accept from body
    } = req.body;

    if (
      !order_id ||
      !razorpay_payment_id ||
      !razorpay_signature ||
      !amount ||
      !user_id ||
      !payment_status ||
      !transaction_for  // <-- Validate this also
    ) {
      return res.status(400).json({
        status: false,
        message:
          "Missing required fields: order_id, razorpay_payment_id, razorpay_signature, amount, user_id, payment_status, transaction_for.",
      });
    }

    if (payment_status !== "success") {
      return res.status(400).json({
        status: false,
        message: "Payment not successful. Transaction cannot be processed.",
      });
    }

    const result = await verifyAndUpsertTransaction({
      order_id,
      razorpay_payment_id,
      razorpay_signature,
      amount,
      currency,
      user_id,
      payment_status,
      transaction_for, // <-- pass to service
    });

    if (result.statusCode !== 200) {
      return res.status(400).json({
        status: false,
        message: result.message,
      });
    }

    return res.status(200).json({
      status: true,
      message: result.message,
      is_verified: result.is_verified,
      transactionId: result.transactionId,
    });
  } catch (error) {
    console.error("Error in verifyPaymentController:", error);
    res.status(error.status || 500).json({
      status: false,
      message: error.message || "Failed to verify payment.",
    });
  }
};

module.exports = { razorpayOrderCreation, razorpayPaymentSignatue };