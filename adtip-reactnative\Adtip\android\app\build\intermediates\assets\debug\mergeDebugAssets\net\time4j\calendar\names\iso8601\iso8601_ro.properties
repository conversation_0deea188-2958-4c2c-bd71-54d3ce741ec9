# months
M(a)_1=ian.
M(a)_2=feb.
M(a)_3=mar.
M(a)_4=apr.
M(a)_5=mai
M(a)_6=iun.
M(a)_7=iul.
M(a)_8=aug.
M(a)_9=sept.
M(a)_10=oct.
M(a)_11=nov.
M(a)_12=dec.

M(n)_1=I
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=I
M(n)_7=I
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=ianuarie
M(w)_2=februarie
M(w)_3=martie
M(w)_4=aprilie
M(w)_5=mai
M(w)_6=iunie
M(w)_7=iulie
M(w)_8=august
M(w)_9=septembrie
M(w)_10=octombrie
M(w)_11=noiembrie
M(w)_12=decembrie

M(A)_1=ian.
M(A)_2=feb.
M(A)_3=mar.
M(A)_4=apr.
M(A)_5=mai
M(A)_6=iun.
M(A)_7=iul.
M(A)_8=aug.
M(A)_9=sept.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=dec.

M(N)_1=I
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=I
M(N)_7=I
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=ianuarie
M(W)_2=februarie
M(W)_3=martie
M(W)_4=aprilie
M(W)_5=mai
M(W)_6=iunie
M(W)_7=iulie
M(W)_8=august
M(W)_9=septembrie
M(W)_10=octombrie
M(W)_11=noiembrie
M(W)_12=decembrie

# weekdays
D(a)_1=lun.
D(a)_2=mar.
D(a)_3=mie.
D(a)_4=joi
D(a)_5=vin.
D(a)_6=sâm.
D(a)_7=dum.

D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

D(s)_1=lu.
D(s)_2=ma.
D(s)_3=mi.
D(s)_4=joi
D(s)_5=vi.
D(s)_6=sâ.
D(s)_7=du.

D(w)_1=luni
D(w)_2=marți
D(w)_3=miercuri
D(w)_4=joi
D(w)_5=vineri
D(w)_6=sâmbătă
D(w)_7=duminică

D(A)_1=lun.
D(A)_2=mar.
D(A)_3=mie.
D(A)_4=joi
D(A)_5=vin.
D(A)_6=sâm.
D(A)_7=dum.

D(N)_1=L
D(N)_2=M
D(N)_3=M
D(N)_4=J
D(N)_5=V
D(N)_6=S
D(N)_7=D

D(S)_1=lu.
D(S)_2=ma.
D(S)_3=mi.
D(S)_4=joi
D(S)_5=vi.
D(S)_6=sâ.
D(S)_7=du.

D(W)_1=luni
D(W)_2=marți
D(W)_3=miercuri
D(W)_4=joi
D(W)_5=vineri
D(W)_6=sâmbătă
D(W)_7=duminică

# quarters
Q(a)_1=trim. I
Q(a)_2=trim. II
Q(a)_3=trim. III
Q(a)_4=trim. IV

Q(n)_1=I
Q(n)_2=II
Q(n)_3=III
Q(n)_4=IV

Q(w)_1=trimestrul I
Q(w)_2=trimestrul al II-lea
Q(w)_3=trimestrul al III-lea
Q(w)_4=trimestrul al IV-lea

Q(A)_1=trim. I
Q(A)_2=trim. II
Q(A)_3=trim. III
Q(A)_4=trim. IV

Q(N)_1=I
Q(N)_2=II
Q(N)_3=III
Q(N)_4=IV

Q(W)_1=trimestrul I
Q(W)_2=trimestrul al II-lea
Q(W)_3=trimestrul al III-lea
Q(W)_4=trimestrul al IV-lea

# day-period-rules
T0500=morning1
T1200=afternoon1
T1800=evening1
T2200=night1

# day-period-translations
P(a)_midnight=miezul nopții
P(a)_am=a.m.
P(a)_noon=amiază
P(a)_pm=p.m.
P(a)_morning1=dimineața
P(a)_afternoon1=după-amiaza
P(a)_evening1=seara
P(a)_night1=noaptea

P(n)_midnight=miezul nopții
P(n)_am=a.m.
P(n)_noon=la amiază
P(n)_pm=p.m.
P(n)_morning1=dimineața
P(n)_afternoon1=după-amiaza
P(n)_evening1=seara
P(n)_night1=noaptea

P(w)_midnight=la miezul nopții
P(w)_am=a.m.
P(w)_noon=la amiază
P(w)_pm=p.m.
P(w)_morning1=dimineața
P(w)_afternoon1=după-amiaza
P(w)_evening1=seara
P(w)_night1=noaptea

P(A)_midnight=miezul nopții
P(A)_am=a.m.
P(A)_noon=amiază
P(A)_pm=p.m.
P(A)_morning1=dimineața
P(A)_afternoon1=după-amiaza
P(A)_evening1=seara
P(A)_night1=noaptea

P(N)_midnight=miezul nopții
P(N)_am=a.m.
P(N)_noon=amiază
P(N)_pm=p.m.
P(N)_morning1=dimineața
P(N)_afternoon1=după-amiaza
P(N)_evening1=seara
P(N)_night1=noaptea

P(W)_midnight=la miezul nopții
P(W)_am=a.m.
P(W)_noon=la amiază
P(W)_pm=p.m.
P(W)_morning1=dimineața
P(W)_afternoon1=după-amiaza
P(W)_evening1=seara
P(W)_night1=noaptea

# eras
E(w)_0=înainte de Hristos
E(w|alt)_0=înaintea erei noastre
E(w)_1=după Hristos
E(w|alt)_1=era noastră

E(a)_0=î.Hr.
E(a|alt)_0=î.e.n
E(a)_1=d.Hr.
E(a|alt)_1=e.n.

E(n)_0=î.Hr.
E(n|alt)_0=î.e.n
E(n)_1=d.Hr.
E(n|alt)_1=e.n.

# format patterns
F(f)_d=EEEE, d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd.MM.y

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd.MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM.y
F_yMM=MM.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='săptămâna' w 'din' Y

I={0} – {1}

# labels of elements
L_era=eră
L_year=an
L_quarter=trimestru
L_month=lună
L_week=săptămână
L_day=zi
L_weekday=ziua din săptămână
L_dayperiod=a.m/p.m.
L_hour=oră
L_minute=minut
L_second=secundă
L_zone=fus orar
