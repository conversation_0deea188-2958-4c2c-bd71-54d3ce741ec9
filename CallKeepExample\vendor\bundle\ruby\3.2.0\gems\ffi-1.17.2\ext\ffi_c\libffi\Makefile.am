## Process this with automake to create Makefile.in

AUTOMAKE_OPTIONS = foreign subdir-objects

ACLOCAL_AMFLAGS = -I m4

SUBDIRS = include testsuite man
if BUILD_DOCS
## This hack is needed because it doesn't seem possible to make a
## conditional info_TEXINFOS in Automake.  At least Automake 1.14
## either gives errors -- if this attempted in the most
## straightforward way -- or simply unconditionally tries to build the
## info file.
SUBDIRS += doc
endif

EXTRA_DIST = LICENSE ChangeLog.old					\
	m4/libtool.m4 m4/lt~obsolete.m4					\
	 m4/ltoptions.m4 m4/ltsugar.m4 m4/ltversion.m4			\
	 m4/ltversion.m4 src/debug.c msvcc.sh				\
	generate-darwin-source-and-headers.py				\
	libffi.xcodeproj/project.pbxproj				\
        src/powerpc/t-aix                                               \
	libtool-ldflags libtool-version configure.host README.md        \
	libffi.map.in LICENSE-BUILDTOOLS msvc_build make_sunver.pl

# local.exp is generated by configure
DISTCLEANFILES = local.exp

# Subdir rules rely on $(FLAGS_TO_PASS)
FLAGS_TO_PASS = $(AM_MAKEFLAGS)

MAKEOVERRIDES=

pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = libffi.pc

toolexeclib_LTLIBRARIES = libffi.la
noinst_LTLIBRARIES = libffi_convenience.la

libffi_la_SOURCES = src/prep_cif.c src/types.c \
		src/raw_api.c src/java_raw_api.c src/closures.c \
		src/tramp.c

if FFI_DEBUG
libffi_la_SOURCES += src/debug.c
endif

noinst_HEADERS = src/aarch64/ffitarget.h src/aarch64/internal.h		\
	src/alpha/ffitarget.h src/alpha/internal.h			\
	src/arc/ffitarget.h src/arm/ffitarget.h src/arm/internal.h	\
	src/avr32/ffitarget.h src/bfin/ffitarget.h			\
	src/cris/ffitarget.h src/csky/ffitarget.h src/frv/ffitarget.h	\
	src/ia64/ffitarget.h src/ia64/ia64_flags.h			\
	src/m32r/ffitarget.h src/m68k/ffitarget.h			\
	src/m88k/ffitarget.h src/metag/ffitarget.h			\
	src/microblaze/ffitarget.h src/mips/ffitarget.h			\
	src/moxie/ffitarget.h \
	src/or1k/ffitarget.h src/pa/ffitarget.h				\
	src/powerpc/ffitarget.h src/powerpc/asm.h			\
	src/powerpc/ffi_powerpc.h src/powerpc/internal.h		\
	src/riscv/ffitarget.h						\
	src/s390/ffitarget.h src/s390/internal.h src/sh/ffitarget.h	\
	src/sh64/ffitarget.h src/sparc/ffitarget.h			\
	src/sparc/internal.h src/tile/ffitarget.h src/vax/ffitarget.h	\
	src/wasm32/ffitarget.h \
	src/x86/ffitarget.h src/x86/internal.h src/x86/internal64.h	\
	src/x86/asmnames.h src/xtensa/ffitarget.h src/dlmalloc.c	\
	src/kvx/ffitarget.h src/kvx/asm.h				\
	src/loongarch64/ffitarget.h

EXTRA_libffi_la_SOURCES = src/aarch64/ffi.c src/aarch64/sysv.S		\
	src/aarch64/win64_armasm.S src/alpha/ffi.c src/alpha/osf.S	\
	src/arc/ffi.c src/arc/arcompact.S src/arm/ffi.c			\
	src/arm/sysv.S src/arm/ffi.c src/arm/sysv_msvc_arm32.S		\
	src/avr32/ffi.c src/avr32/sysv.S src/bfin/ffi.c			\
	src/bfin/sysv.S src/cris/ffi.c src/cris/sysv.S src/frv/ffi.c	\
	src/csky/ffi.c src/csky/sysv.S src/frv/eabi.S src/ia64/ffi.c	\
	src/ia64/unix.S src/m32r/ffi.c src/m32r/sysv.S src/m68k/ffi.c	\
	src/m68k/sysv.S src/m88k/ffi.c src/m88k/obsd.S			\
	src/metag/ffi.c src/metag/sysv.S src/microblaze/ffi.c		\
	src/microblaze/sysv.S src/mips/ffi.c src/mips/o32.S		\
	src/mips/n32.S src/moxie/ffi.c src/moxie/eabi.S			\
	src/or1k/ffi.c			\
	src/or1k/sysv.S src/pa/ffi.c src/pa/linux.S src/pa/hpux32.S	\
	src/pa/hpux64.S src/powerpc/ffi.c src/powerpc/ffi_sysv.c	\
	src/powerpc/ffi_linux64.c src/powerpc/sysv.S			\
	src/powerpc/linux64.S src/powerpc/linux64_closure.S		\
	src/powerpc/ppc_closure.S src/powerpc/aix.S			\
	src/powerpc/darwin.S src/powerpc/aix_closure.S			\
	src/powerpc/darwin_closure.S src/powerpc/ffi_darwin.c		\
	src/riscv/ffi.c src/riscv/sysv.S src/s390/ffi.c			\
	src/s390/sysv.S src/sh/ffi.c src/sh/sysv.S src/sh64/ffi.c	\
	src/sh64/sysv.S src/sparc/ffi.c src/sparc/ffi64.c		\
	src/sparc/v8.S src/sparc/v9.S src/tile/ffi.c src/tile/tile.S	\
	src/vax/ffi.c src/vax/elfbsd.S src/x86/ffi.c src/x86/sysv.S	\
	src/wasm32/ffi.c \
	src/x86/ffiw64.c src/x86/win64.S src/x86/ffi64.c		\
	src/x86/unix64.S src/x86/sysv_intel.S src/x86/win64_intel.S	\
	src/xtensa/ffi.c src/xtensa/sysv.S src/kvx/ffi.c		\
	src/kvx/sysv.S src/loongarch64/ffi.c src/loongarch64/sysv.S

TARGET_OBJ = @TARGET_OBJ@
libffi_la_LIBADD = $(TARGET_OBJ)

libffi_convenience_la_SOURCES = $(libffi_la_SOURCES)
EXTRA_libffi_convenience_la_SOURCES = $(EXTRA_libffi_la_SOURCES)
libffi_convenience_la_LIBADD = $(libffi_la_LIBADD)
libffi_convenience_la_DEPENDENCIES = $(libffi_la_DEPENDENCIES)
nodist_libffi_convenience_la_SOURCES = $(nodist_libffi_la_SOURCES)

LTLDFLAGS = $(shell $(SHELL) $(top_srcdir)/libtool-ldflags $(LDFLAGS))

AM_CFLAGS =
if FFI_DEBUG
# Build debug. Define FFI_DEBUG on the commandline so that, when building with
# MSVC, it can link against the debug CRT.
AM_CFLAGS += -DFFI_DEBUG
endif

if LIBFFI_BUILD_VERSIONED_SHLIB
if LIBFFI_BUILD_VERSIONED_SHLIB_GNU
libffi_version_script = -Wl,--version-script,libffi.map
libffi_version_dep = libffi.map
endif
if LIBFFI_BUILD_VERSIONED_SHLIB_SUN
libffi_version_script = -Wl,-M,libffi.map-sun
libffi_version_dep = libffi.map-sun
libffi.map-sun : libffi.map $(top_srcdir)/make_sunver.pl \
		 $(libffi_la_OBJECTS) $(libffi_la_LIBADD)
	perl $(top_srcdir)/make_sunver.pl libffi.map \
	 `echo $(libffi_la_OBJECTS) $(libffi_la_LIBADD) | \
	    sed 's,\([^/        ]*\)\.l\([ao]\),.libs/\1.\2,g'` \
	 > $@ || (rm -f $@ ; exit 1)
endif
else
libffi_version_script =
libffi_version_dep =
endif
libffi_version_info = -version-info `grep -v '^\#' $(srcdir)/libtool-version`

libffi.map: $(top_srcdir)/libffi.map.in
	$(COMPILE) -D$(TARGET) -DGENERATE_LIBFFI_MAP \
	 -E -x assembler-with-cpp -o $@ $(top_srcdir)/libffi.map.in

libffi_la_LDFLAGS = -no-undefined $(libffi_version_info) $(libffi_version_script) $(LTLDFLAGS) $(AM_LTLDFLAGS)
libffi_la_DEPENDENCIES = $(libffi_la_LIBADD) $(libffi_version_dep)

AM_CPPFLAGS = -I. -I$(top_srcdir)/include -Iinclude -I$(top_srcdir)/src
AM_CCASFLAGS = $(AM_CPPFLAGS)

dist-hook:
	d=`(cd $(distdir); pwd)`; (cd doc; make pdf; cp *.pdf $$d/doc)
	if [ -d $(top_srcdir)/.git ] ; then (cd $(top_srcdir); git log --no-decorate) ; else echo 'See git log for history.' ; fi > $(distdir)/ChangeLog
	s=`awk '/was released on/{ print NR; exit}' $(top_srcdir)/README.md`; tail -n +$$(($$s-1)) $(top_srcdir)/README.md > $(distdir)/README.md

# target overrides
-include $(tmake_file)
