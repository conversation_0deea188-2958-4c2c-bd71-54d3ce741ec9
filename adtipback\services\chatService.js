const dbQuery = require("../dbConfig/queryRunner");
const _ = require('lodash');

const ChatService = {
  savemessages: (userData) =>
    new Promise((resolve, reject) => {
      try {
        let message = userData.message.replace(/'/g, "''"); // Better escaping
        userData.chat_type = (userData.chat_type !== undefined && userData.chat_type !== null && userData.chat_type !== '') ? userData.chat_type : 'text';
        userData.chat_type_id_value = (userData.chat_type_id_value !== undefined && userData.chat_type_id_value !== null) ? userData.chat_type_id_value : 0;
        
        // Use direct INSERT instead of stored procedure - match exact table structure
        let sql = `INSERT INTO user_chat (
          message, sender, receiver, chat_type, chat_type_id_value, 
          is_seen, is_receiver_seen, is_active, 
          active_for_sender, active_for_reciever, 
          createddate, updateddate
        ) VALUES (
          '${message}', ${userData.userId}, ${userData.receiverId}, '${userData.chat_type}', ${userData.chat_type_id_value}, 
          0, 0, 1, 
          1, 1, 
          NOW(), NOW()
        )`;
        
        console.log('Saving message with SQL:', sql);
        
        dbQuery
          .queryRunner(sql)
          .then((result) => {
            if (result && result.insertId) {
              userData.enum = 6;
              userData.messageId = result.insertId;
              resolve({
                status: 200,
                message: "Send message successfully.",
                data: [userData],
              });
            } else {
              console.error('No insertId in result:', result);
              reject({
                status: 400,
                message: "Failed to save message.",
                data: [],
              });
            }
          })
          .catch((err) => {
            console.error('Database error saving message:', err);
            let message = "";
            if (err.message.includes("ER_DUP_ENTRY"))
              message = "Duplicate message.";
            else if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
              message = "Invalid userId.";
            else
              message = err.message;
            
            reject({
              status: 500,
              message: message,
              data: [],
            });
          });
      } catch (error) {
        console.error('Error in savemessages:', error);
        reject({
          status: 500,
          message: error.message || "Internal server error.",
          data: [],
        });
      }
    }),

  saveticks: (userData) =>
    new Promise((resolve, reject) => {
      // Assuming getMessage and updateTicks are defined elsewhere
      return getMessage(userData.id)
        .then((result) => {
          if (result && result.status === 200) {
            return updateTicks(userData);
          } else {
            reject(result);
          }
        })
        .then((result) => {
          if (result && result.status === 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Internal server error.",
            data: [],
          });
        });
    }),

  getMessages: (userId, chattinguserid) =>
    new Promise((resolve, reject) => {
      let messageData = "",
        sql = "";
      if (chattinguserid == null) {
        // Get latest messages for all conversations
        sql = `SELECT uc.*, 
               CASE 
                 WHEN uc.sender = ${userId} THEN uc.receiver 
                 ELSE uc.sender 
               END as other_user_id
               FROM user_chat uc 
               WHERE (uc.sender = ${userId} OR uc.receiver = ${userId}) 
               AND uc.is_active = 1 
               ORDER BY uc.createddate DESC`;
      } else {
        // Get messages between specific users
        sql = `SELECT * FROM user_chat 
               WHERE ((sender = ${userId} AND receiver = ${chattinguserid}) 
                   OR (sender = ${chattinguserid} AND receiver = ${userId})) 
               AND is_active = 1 
               ORDER BY createddate ASC`;
      }

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length !== 0) {
            messageData = result;
            let receiverUserData = _.map(result, "receiver");
            let senderUserData = _.map(result, "sender");
            let userData = receiverUserData.concat(senderUserData);
            userData = _.uniq(userData);
            let d = _.without(userData, parseInt(userId));
            let userSql = `select u.id,u.profile_image,u.firstName,u.name,IFNULL(ucd.is_block,0) as is_block,
                    IFNULL(ucd.is_mute,0) as is_mute,ud.is_rating_profile,ud.rating from users u
                    LEFT JOIN user_chat_details ucd ON ucd.user_id=u.id and ucd.createdby=${userId} 
                    LEFT JOIN user_details ud ON ud.user_id=u.id and ud.created_by=${userId}                    
                    where u.id in (${d.toString()});`;
            return dbQuery.queryRunner(userSql);
          } else {
            resolve({
              status: 200,
              message: "Message not found.",
              data: [],
            });
          }
        })
        .then((result) => {
          if (result && result.length !== 0) {
            messageData.forEach((message) => {
              result.forEach((user) => {
                if (message && message.receiver === user.id) {
                  message.receiver_profile_image = user.profile_image;
                  message.receiver_id = user.id;
                  message.receiver_name = user.name;
                  message.is_block = user.is_block;
                  message.is_mute = user.is_mute;
                  message.is_rating_profile = user.is_rating_profile;
                  message.rating = user.rating;
                }
                if (message && message.sender === user.id) {
                  message.sender_profile_image = user.profile_image;
                  message.sender_id = user.id;
                  message.sender_name = user.name;
                  message.is_block = user.is_block;
                  message.is_mute = user.is_mute;
                  message.is_rating_profile = user.is_rating_profile;
                  message.rating = user.rating;
                }
              });
            });
            resolve({
              status: 200,
              message: "Fetch data successfully.",
              data: messageData,
            });
          } else {
            resolve(result);
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Internal server error.",
            data: [],
          });
        });
    }),

  getUnreadMessageCount: (userId) =>
    new Promise((resolve, reject) => {
      let sql = `SELECT COUNT(*) as count FROM user_chat 
                 WHERE receiver = ${userId} AND is_seen = 0 AND is_active = 1`;
      
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length !== 0) {
            resolve({
              status: 200,
              message: "Unread count fetched successfully.",
              data: {
                unreadCount: parseInt(result[0].count) || 0
              },
            });
          } else {
            resolve({
              status: 200,
              message: "No unread messages.",
              data: {
                unreadCount: 0
              },
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Internal server error.",
            data: [],
          });
        });
    }),

  markMessagesAsRead: (userId, senderId) =>
    new Promise((resolve, reject) => {
      let sql = `UPDATE user_chat SET is_seen = 1, updateddate = NOW() 
                 WHERE receiver = ${userId} AND sender = ${senderId} AND is_seen = 0 AND is_active = 1`;
      
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          resolve({
            status: 200,
            message: "Messages marked as read successfully.",
            data: {
              affected_rows: result.affectedRows || 0
            },
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Internal server error.",
            data: [],
          });
        });
    }),
};

// DBA NOTE: Ensure this index exists for fast cleanup:
// CREATE INDEX idx_createddate ON user_chat(createddate);

module.exports = ChatService;