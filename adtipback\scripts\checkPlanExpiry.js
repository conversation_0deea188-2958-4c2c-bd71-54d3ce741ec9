const dbQuery = require("../dbConfig/queryRunner");
const moment = require("moment");

const checkPlanExpiry = async () => {
  try {
    const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    // Find expired active plans
    const expiredPlansQuery = `
      SELECT id, user_id, plan_id 
      FROM user_premium_plans 
      WHERE status = 'active' AND end_time < '${currentTime}'
    `;
    const expiredPlans = await dbQuery.queryRunner(expiredPlansQuery);

    for (const plan of expiredPlans) {
      // Mark plan as expired
      await dbQuery.queryRunner(`
        UPDATE user_premium_plans 
        SET status = 'expired', is_active = 0 
        WHERE id = ${plan.id}
      `);

      // Check for queued plans
      const queuedPlanQuery = `
        SELECT upp.id, upp.plan_id, sp.duration_in_months
        FROM user_premium_plans upp
        JOIN subscription_plans sp ON upp.plan_id = sp.id
        WHERE upp.user_id = ${plan.user_id} AND upp.status = 'queued'
        ORDER BY upp.created_at ASC
        LIMIT 1
      `;
      const queuedPlan = await dbQuery.queryRunner(queuedPlanQuery);

      if (queuedPlan.length) {
        // Activate the next queued plan
        const endTime = moment()
          .add(queuedPlan[0].duration_in_months, "months")
          .utcOffset(330)
          .format("YYYY-MM-DD HH:mm:ss");
        await dbQuery.queryRunner(`
          UPDATE user_premium_plans 
          SET status = 'active', is_active = 1, start_time = '${currentTime}', end_time = '${endTime}'
          WHERE id = ${queuedPlan[0].id}
        `);

        // Update users table
        await dbQuery.queryRunner(`
          UPDATE users SET premium = 1, premium_plan_id = ${queuedPlan[0].plan_id} WHERE id = ${plan.user_id}
        `);
      } else {
        // No queued plans, downgrade to non-premium
        await dbQuery.queryRunner(`
          UPDATE users SET premium = 0, premium_plan_id = 0 WHERE id = ${plan.user_id}
        `);
      }
    }

    console.log("Plan expiry check completed successfully");
  } catch (error) {
    console.error("Error in checkPlanExpiry:", error);
  }
};

module.exports = { checkPlanExpiry };