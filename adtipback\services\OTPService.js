const { queryRunner, queryRunnerWithTransaction } = require('../dbConfig/queryRunner');
const logger = require('../utils/logger');

class OTPService {
  constructor() {
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
  }

  // Generate OTP with proper transaction handling
  async generateOTP(mobileNumber, messageId) {
    const otp = this.generateRandomOTP();
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        // Use transaction to ensure atomicity
        const result = await this.updateOTPWithTransaction(mobileNumber, otp, messageId);
        logger.info(`OTP generated successfully for ${mobileNumber} (attempt ${attempt})`);
        return {
          success: true,
          otp: otp,
          messageId: messageId,
          data: result
        };
      } catch (error) {
        logger.warn(`OTP generation attempt ${attempt} failed for ${mobileNumber}:`, error.message);
        
        if (attempt === this.maxRetries) {
          logger.error(`OTP generation failed after ${this.maxRetries} attempts for ${mobileNumber}:`, error);
          throw {
            status: 500,
            error: 'OTP Generation Failed',
            message: 'Unable to generate OTP. Please try again.'
          };
        }
        
        // Wait before retry with exponential backoff
        await this.delay(this.retryDelay * Math.pow(2, attempt - 1));
      }
    }
  }

  // Update OTP with transaction and deadlock prevention
  async updateOTPWithTransaction(mobileNumber, otp, messageId) {
    const queries = [
      {
        query: `
          UPDATE users 
          SET otp = ?, 
              message_id = ?, 
              is_first_time = true,
              otp_created_at = NOW()
          WHERE mobile_number = ?
        `,
        params: [otp, messageId, mobileNumber]
      }
    ];

    return await queryRunnerWithTransaction(queries);
  }

  // Update OTP with existing OTP (for when SMS is sent first)
  async updateOTPWithExistingOTP(mobileNumber, otp, messageId) {
    const queries = [
      {
        query: `
          UPDATE users 
          SET otp = ?, 
              message_id = ?, 
              is_first_time = true,
              otp_created_at = NOW()
          WHERE mobile_number = ?
        `,
        params: [otp, messageId, mobileNumber]
      }
    ];

    return await queryRunnerWithTransaction(queries);
  }

  // Verify OTP with proper error handling
  async verifyOTP(mobileNumber, otp) {
    try {
      // First, check if OTP exists and is not expired
      const checkQuery = `
        SELECT id, otp, otp_created_at, is_first_time 
        FROM users 
        WHERE mobile_number = ? 
        AND otp IS NOT NULL 
        AND otp != ''
        AND otp_created_at > (NOW() - INTERVAL 10 MINUTE)
      `;
      
      const userResult = await queryRunner(checkQuery, [mobileNumber]);
      
      if (!userResult || userResult.length === 0) {
        return {
          success: false,
          message: 'OTP not found or expired. Please request a new OTP.'
        };
      }

      const user = userResult[0];
      
      // Debug logging
      console.log(`Debug - Stored OTP: "${user.otp}" (type: ${typeof user.otp})`);
      console.log(`Debug - Input OTP: "${otp}" (type: ${typeof otp})`);
      console.log(`Debug - Comparison: ${user.otp} !== ${otp} = ${user.otp !== otp}`);
      
      // Verify OTP
      if (user.otp !== otp) {
        return {
          success: false,
          message: 'Invalid OTP. Please check and try again.'
        };
      }

      // Clear OTP after successful verification
      await this.clearOTP(mobileNumber);
      
      logger.info(`OTP verified successfully for ${mobileNumber}`);
      
      return {
        success: true,
        message: 'OTP verified successfully',
        isFirstTime: user.is_first_time,
        userId: user.id
      };
      
    } catch (error) {
      logger.error(`OTP verification failed for ${mobileNumber}:`, error);
      throw {
        status: 500,
        error: 'OTP Verification Failed',
        message: 'Unable to verify OTP. Please try again.'
      };
    }
  }

  // Clear OTP after verification
  async clearOTP(mobileNumber) {
    try {
      const clearQuery = `
        UPDATE users 
        SET otp = NULL, 
            message_id = NULL,
            otp_created_at = NULL
        WHERE mobile_number = ?
      `;
      
      await queryRunner(clearQuery, [mobileNumber]);
      logger.info(`OTP cleared for ${mobileNumber}`);
    } catch (error) {
      logger.error(`Failed to clear OTP for ${mobileNumber}:`, error);
      // Don't throw error here as OTP verification was successful
    }
  }

  // Generate random 6-digit OTP
  generateRandomOTP() {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Delay function for retry logic
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Check if user exists
  async checkUserExists(mobileNumber) {
    try {
      const query = 'SELECT id FROM users WHERE mobile_number = ? LIMIT 1';
      const result = await queryRunner(query, [mobileNumber]);
      return result && result.length > 0;
    } catch (error) {
      logger.error(`Error checking user existence for ${mobileNumber}:`, error);
      return false;
    }
  }

  // Create user if doesn't exist (for first-time users)
  async createUserIfNotExists(mobileNumber, otp, messageId) {
    try {
      const userExists = await this.checkUserExists(mobileNumber);
      
      if (!userExists) {
        const createQuery = `
          INSERT INTO users (mobile_number, otp, message_id, is_first_time, otp_created_at, createddate) 
          VALUES (?, ?, ?, true, NOW(), NOW())
        `;
        
        await queryRunner(createQuery, [mobileNumber, otp, messageId]);
        logger.info(`New user created for ${mobileNumber}`);
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error(`Error creating user for ${mobileNumber}:`, error);
      throw error;
    }
  }

  // Resend OTP with rate limiting
  async resendOTP(mobileNumber, messageId) {
    try {
      // Check if OTP was recently sent (rate limiting)
      const recentOTPQuery = `
        SELECT otp_created_at 
        FROM users 
        WHERE mobile_number = ? 
        AND otp_created_at > (NOW() - INTERVAL 1 MINUTE)
      `;
      
      const recentResult = await queryRunner(recentOTPQuery, [mobileNumber]);
      
      if (recentResult && recentResult.length > 0) {
        return {
          success: false,
          message: 'Please wait 1 minute before requesting a new OTP.'
        };
      }

      // Generate new OTP
      return await this.generateOTP(mobileNumber, messageId);
      
    } catch (error) {
      logger.error(`Resend OTP failed for ${mobileNumber}:`, error);
      throw {
        status: 500,
        error: 'Resend OTP Failed',
        message: 'Unable to resend OTP. Please try again.'
      };
    }
  }
}

// Create singleton instance
const otpService = new OTPService();

module.exports = otpService; 