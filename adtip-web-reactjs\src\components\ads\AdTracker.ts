// Advertisement Tracking Service for Web Application
// Based on React Native AdTracker implementation

export interface AdEvent {
  adType: 'banner' | 'rectangle' | 'native' | 'interstitial' | 'rewarded' | 'video';
  eventType: 'loaded' | 'failed' | 'opened' | 'closed' | 'clicked' | 'impression' | 'earned_reward';
  timestamp: number;
  platform: 'web';
  adUnitId?: string;
  error?: any;
  reward?: any;
  metadata?: Record<string, any>;
}

export interface AdMetrics {
  totalLoaded: number;
  totalFailed: number;
  totalOpened: number;
  totalClicked: number;
  totalImpressions: number;
  totalRewards: number;
  fillRate: number; // (loaded / (loaded + failed)) * 100
  clickThroughRate: number; // (clicked / impressions) * 100
}

class AdTracker {
  private events: AdEvent[] = [];
  private readonly maxEvents = 1000;

  /**
   * Track an ad event
   */
  trackEvent(
    adType: AdEvent['adType'],
    eventType: AdEvent['eventType'],
    adUnitId?: string,
    error?: any,
    reward?: any,
    metadata?: Record<string, any>
  ) {
    const event: AdEvent = {
      adType,
      eventType,
      timestamp: Date.now(),
      platform: 'web',
      adUnitId,
      error,
      reward,
      metadata,
    };

    this.events.push(event);

    // Keep only the last maxEvents
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log the event
    console.log(`[AdTracker] ${adType} - ${eventType}`, {
      timestamp: new Date(event.timestamp).toISOString(),
      platform: event.platform,
      adUnitId: event.adUnitId,
      error: event.error,
      reward: event.reward,
      metadata: event.metadata,
    });

    // Send to analytics service
    this.sendToAnalytics(event);
  }

  /**
   * Get metrics for a specific ad type
   */
  getAdMetrics(adType: AdEvent['adType']): AdMetrics {
    const adEvents = this.events.filter(event => event.adType === adType);
    
    const totalLoaded = adEvents.filter(e => e.eventType === 'loaded').length;
    const totalFailed = adEvents.filter(e => e.eventType === 'failed').length;
    const totalOpened = adEvents.filter(e => e.eventType === 'opened').length;
    const totalClicked = adEvents.filter(e => e.eventType === 'clicked').length;
    const totalImpressions = adEvents.filter(e => e.eventType === 'impression').length;
    const totalRewards = adEvents.filter(e => e.eventType === 'earned_reward').length;

    const fillRate = totalLoaded + totalFailed > 0 
      ? (totalLoaded / (totalLoaded + totalFailed)) * 100 
      : 0;
    
    const clickThroughRate = totalImpressions > 0 
      ? (totalClicked / totalImpressions) * 100 
      : 0;

    return {
      totalLoaded,
      totalFailed,
      totalOpened,
      totalClicked,
      totalImpressions,
      totalRewards,
      fillRate,
      clickThroughRate,
    };
  }

  /**
   * Get all events
   */
  getEvents(): AdEvent[] {
    return [...this.events];
  }

  /**
   * Get events for a specific ad type
   */
  getEventsByType(adType: AdEvent['adType']): AdEvent[] {
    return this.events.filter(event => event.adType === adType);
  }

  /**
   * Get events from a specific time range
   */
  getEventsInTimeRange(startTime: number, endTime: number): AdEvent[] {
    return this.events.filter(
      event => event.timestamp >= startTime && event.timestamp <= endTime
    );
  }

  /**
   * Clear all events
   */
  clearEvents(): void {
    this.events = [];
  }

  /**
   * Export events as JSON
   */
  exportEvents(): string {
    return JSON.stringify(this.events, null, 2);
  }

  /**
   * Send event to analytics service
   */
  private async sendToAnalytics(event: AdEvent): Promise<void> {
    try {
      // Send to your analytics service
      // Example: Google Analytics, Mixpanel, etc.
      
      // For now, just store in localStorage for debugging
      if (import.meta.env.DEV) {
        const storedEvents = localStorage.getItem('adtip_ad_events');
        const events = storedEvents ? JSON.parse(storedEvents) : [];
        events.push(event);
        
        // Keep only last 100 events in localStorage
        if (events.length > 100) {
          events.splice(0, events.length - 100);
        }
        
        localStorage.setItem('adtip_ad_events', JSON.stringify(events));
      }

      // TODO: Send to actual analytics service
      // await fetch('/api/analytics/ad-event', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event),
      // });
    } catch (error) {
      console.error('Failed to send ad event to analytics:', error);
    }
  }

  /**
   * Get summary statistics
   */
  getSummaryStats(): Record<string, AdMetrics> {
    const adTypes: AdEvent['adType'][] = ['banner', 'rectangle', 'native', 'interstitial', 'rewarded', 'video'];
    const summary: Record<string, AdMetrics> = {};

    adTypes.forEach(adType => {
      summary[adType] = this.getAdMetrics(adType);
    });

    return summary;
  }

  /**
   * Get recent events (last hour)
   */
  getRecentEvents(): AdEvent[] {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    return this.getEventsInTimeRange(oneHourAgo, Date.now());
  }
}

// Create a singleton instance
export const adTracker = new AdTracker();

// Helper functions for easy tracking
export const trackAdLoaded = (adType: AdEvent['adType'], adUnitId?: string, metadata?: Record<string, any>) => {
  adTracker.trackEvent(adType, 'loaded', adUnitId, undefined, undefined, metadata);
};

export const trackAdFailed = (adType: AdEvent['adType'], error: any, adUnitId?: string, metadata?: Record<string, any>) => {
  adTracker.trackEvent(adType, 'failed', adUnitId, error, undefined, metadata);
};

export const trackAdOpened = (adType: AdEvent['adType'], adUnitId?: string, metadata?: Record<string, any>) => {
  adTracker.trackEvent(adType, 'opened', adUnitId, undefined, undefined, metadata);
};

export const trackAdClosed = (adType: AdEvent['adType'], adUnitId?: string, metadata?: Record<string, any>) => {
  adTracker.trackEvent(adType, 'closed', adUnitId, undefined, undefined, metadata);
};

export const trackAdClicked = (adType: AdEvent['adType'], adUnitId?: string, metadata?: Record<string, any>) => {
  adTracker.trackEvent(adType, 'clicked', adUnitId, undefined, undefined, metadata);
};

export const trackAdImpression = (adType: AdEvent['adType'], adUnitId?: string, metadata?: Record<string, any>) => {
  adTracker.trackEvent(adType, 'impression', adUnitId, undefined, undefined, metadata);
};

export const trackAdReward = (adType: AdEvent['adType'], reward: any, adUnitId?: string, metadata?: Record<string, any>) => {
  adTracker.trackEvent(adType, 'earned_reward', adUnitId, undefined, reward, metadata);
};

export default adTracker;
