{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-41e1c86cfb70f825c82e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-89cfbcf428fef7f615b2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-c572d1045032e31b7342.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-89cfbcf428fef7f615b2.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-c572d1045032e31b7342.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-41e1c86cfb70f825c82e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}