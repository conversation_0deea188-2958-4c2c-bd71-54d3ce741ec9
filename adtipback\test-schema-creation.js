const mysql = require('mysql2/promise');
require('dotenv').config();

const config = require('./config/appenvconfig.js');

async function testSchemaCreation() {
  let connection;
  
  try {
    console.log('Testing schema creation...');
    
    connection = await mysql.createConnection({
      host: config.database.host,
      user: config.database.user,
      password: config.database.password,
      database: config.database.database,
      port: parseInt(config.database.port) || 3306,
      charset: 'utf8mb4'
    });
    
    console.log('✅ Connected to database');
    
    // Test creating a simple table first
    console.log('🔄 Testing simple table creation...');
    
    const simpleTableSQL = `
      CREATE TABLE IF NOT EXISTS test_conversations (
        id INT PRIMARY KEY AUTO_INCREMENT,
        type ENUM('direct', 'group') DEFAULT 'direct',
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.query(simpleTableSQL);
    console.log('✅ Simple table created successfully');
    
    // Test with foreign key
    console.log('🔄 Testing table with foreign key...');
    
    const fkTableSQL = `
      CREATE TABLE IF NOT EXISTS test_messages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        conversation_id INT NOT NULL,
        content TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (conversation_id) REFERENCES test_conversations(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `;
    
    await connection.query(fkTableSQL);
    console.log('✅ Table with foreign key created successfully');
    
    // Clean up test tables
    await connection.query('DROP TABLE IF EXISTS test_messages');
    await connection.query('DROP TABLE IF EXISTS test_conversations');
    console.log('✅ Test tables cleaned up');
    
    console.log('✅ Schema creation test passed!');
    
  } catch (error) {
    console.error('❌ Schema creation test failed:', error.message);
    console.error('Error details:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testSchemaCreation();
