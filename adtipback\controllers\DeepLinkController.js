// Deep Link Controller - handles deep link generation, validation, and metadata

/**
 * Deep Link Controller
 * Handles deep link generation, validation, and metadata for sharing
 */

// Deep link configuration
const DEEP_LINK_CONFIG = {
  schemes: ['adtip://'],
  domains: ['adtip.in', 'www.adtip.in', 'app.adtip.in'],
  appStoreUrl: 'https://apps.apple.com/app/adtip/id123456789', // Replace with actual App Store URL
  playStoreUrl: 'https://play.google.com/store/apps/details?id=com.adtip.app.adtip_app&hl=en_IN',
};

/**
 * Generate deep link for content
 * POST /api/deeplink/generate
 */
const generateDeepLink = async (req, res) => {
  try {
    const { type, id, params = {} } = req.body;

    if (!type || !id) {
      return res.status(400).json({
        success: false,
        message: 'Type and ID are required'
      });
    }

    // Generate different types of deep links
    let path = '';
    switch (type) {
      case 'post':
        path = `/post/${id}`;
        break;
      case 'user':
        path = `/user/${id}`;
        break;
      case 'short':
        path = `/short/${id}`;
        break;
      case 'video':
        path = `/video/${id}`;
        break;
      case 'product':
        path = `/shop/product/${id}`;
        break;
      case 'call':
        path = `/call/${id}`;
        break;
      case 'chat':
        path = `/chat/${id}`;
        break;
      case 'game':
        path = `/ludo/${id}`;
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid deep link type'
        });
    }

    // Add query parameters if provided
    const queryParams = new URLSearchParams(params).toString();
    if (queryParams) {
      path += `?${queryParams}`;
    }

    const deepLink = `adtip://${path}`;
    const universalLink = `https://adtip.in${path}`;

    res.json({
      success: true,
      data: {
        deepLink,
        universalLink,
        type,
        id,
        params
      }
    });

  } catch (error) {
    console.error('Error generating deep link:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate deep link'
    });
  }
};

/**
 * Validate deep link
 * POST /api/deeplink/validate
 */
const validateDeepLink = async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL is required'
      });
    }

    // Check if URL matches our deep link patterns
    const isValidScheme = DEEP_LINK_CONFIG.schemes.some(scheme => url.startsWith(scheme));
    const isValidDomain = DEEP_LINK_CONFIG.domains.some(domain => 
      url.startsWith(`https://${domain}`) || url.startsWith(`http://${domain}`)
    );

    const isValid = isValidScheme || isValidDomain;

    if (isValid) {
      // Parse the URL to extract information
      const parsedInfo = parseDeepLinkUrl(url);
      
      res.json({
        success: true,
        data: {
          isValid: true,
          url,
          parsed: parsedInfo
        }
      });
    } else {
      res.json({
        success: true,
        data: {
          isValid: false,
          url,
          reason: 'URL does not match app deep link patterns'
        }
      });
    }

  } catch (error) {
    console.error('Error validating deep link:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate deep link'
    });
  }
};

/**
 * Get metadata for sharing
 * GET /api/deeplink/metadata/:type/:id
 */
const getShareMetadata = async (req, res) => {
  try {
    const { type, id } = req.params;
    const { queryRunner } = req;

    let metadata = {
      title: 'Adtip',
      description: 'Join me on Adtip - The ultimate social platform!',
      image: 'https://adtip.in/assets/logo.png',
      url: `https://adtip.in/${type}/${id}`
    };

    // Get specific metadata based on content type
    switch (type) {
      case 'post':
        const postData = await getPostMetadata(queryRunner, id);
        if (postData) {
          metadata = {
            title: `${postData.user_name}'s Post - Adtip`,
            description: postData.description || 'Check out this amazing post on Adtip!',
            image: postData.media_url || metadata.image,
            url: `https://adtip.in/post/${id}`
          };
        }
        break;

      case 'user':
        const userData = await getUserMetadata(queryRunner, id);
        if (userData) {
          metadata = {
            title: `${userData.name} - Adtip Profile`,
            description: userData.bio || `Check out ${userData.name}'s profile on Adtip!`,
            image: userData.profile_image || metadata.image,
            url: `https://adtip.in/user/${id}`
          };
        }
        break;

      case 'short':
        const shortData = await getShortMetadata(queryRunner, id);
        if (shortData) {
          metadata = {
            title: `${shortData.user_name}'s Short Video - Adtip`,
            description: shortData.description || 'Watch this amazing short video on Adtip!',
            image: shortData.thumbnail_url || metadata.image,
            url: `https://adtip.in/short/${id}`
          };
        }
        break;

      case 'product':
        const productData = await getProductMetadata(queryRunner, id);
        if (productData) {
          metadata = {
            title: `${productData.name} - Adtip Shop`,
            description: productData.description || 'Check out this amazing product on Adtip Shop!',
            image: productData.image_url || metadata.image,
            url: `https://adtip.in/shop/product/${id}`
          };
        }
        break;
    }

    res.json({
      success: true,
      data: metadata
    });

  } catch (error) {
    console.error('Error getting share metadata:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get share metadata'
    });
  }
};

/**
 * Handle universal link redirects
 * GET /post/:id, /user/:id, etc.
 */
const handleUniversalLink = async (req, res) => {
  try {
    const userAgent = req.get('User-Agent') || '';
    const isBot = /bot|crawler|spider|crawling/i.test(userAgent);
    
    if (isBot) {
      // Serve metadata for social media crawlers
      await getShareMetadata(req, res);
      return;
    }

    // Check if user is on mobile
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    
    if (isMobile) {
      // Try to open the app, fallback to app store
      const deepLink = `adtip://${req.path}`;
      const fallbackUrl = /iPhone|iPad|iPod/i.test(userAgent) 
        ? DEEP_LINK_CONFIG.appStoreUrl 
        : DEEP_LINK_CONFIG.playStoreUrl;

      res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Opening Adtip...</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
        </head>
        <body>
          <script>
            // Try to open the app
            window.location = "${deepLink}";
            
            // Fallback to app store after a delay
            setTimeout(function() {
              window.location = "${fallbackUrl}";
            }, 2000);
          </script>
          <p>Opening Adtip app...</p>
          <p>If the app doesn't open, <a href="${fallbackUrl}">download it here</a>.</p>
        </body>
        </html>
      `);
    } else {
      // Desktop users - redirect to web version or show download page
      res.redirect('https://adtip.in/download');
    }

  } catch (error) {
    console.error('Error handling universal link:', error);
    res.status(500).send('Error processing link');
  }
};

// Helper functions for getting metadata
async function getPostMetadata(queryRunner, postId) {
  try {
    const query = `
      SELECT p.*, u.name as user_name, u.profile_image as user_profile_image
      FROM posts p
      JOIN users u ON p.user_id = u.id
      WHERE p.id = ?
    `;
    const result = await queryRunner(query, [postId]);
    return result[0] || null;
  } catch (error) {
    console.error('Error getting post metadata:', error);
    return null;
  }
}

async function getUserMetadata(queryRunner, userId) {
  try {
    const query = `
      SELECT id, name, bio, profile_image
      FROM users
      WHERE id = ?
    `;
    const result = await queryRunner(query, [userId]);
    return result[0] || null;
  } catch (error) {
    console.error('Error getting user metadata:', error);
    return null;
  }
}

async function getShortMetadata(queryRunner, shortId) {
  try {
    const query = `
      SELECT s.*, u.name as user_name
      FROM shorts s
      JOIN users u ON s.user_id = u.id
      WHERE s.id = ?
    `;
    const result = await queryRunner(query, [shortId]);
    return result[0] || null;
  } catch (error) {
    console.error('Error getting short metadata:', error);
    return null;
  }
}

async function getProductMetadata(queryRunner, productId) {
  try {
    const query = `
      SELECT id, name, description, image_url, price
      FROM products
      WHERE id = ?
    `;
    const result = await queryRunner(query, [productId]);
    return result[0] || null;
  } catch (error) {
    console.error('Error getting product metadata:', error);
    return null;
  }
}

// Helper function to parse deep link URLs
function parseDeepLinkUrl(url) {
  try {
    let path = url;
    
    // Remove scheme
    if (url.startsWith('adtip://')) {
      path = url.replace('adtip://', '');
    } else {
      // Extract path from HTTP URL
      const urlObj = new URL(url);
      path = urlObj.pathname.substring(1); // Remove leading slash
    }

    const segments = path.split('/');
    const type = segments[0];
    const id = segments[1];

    return {
      type,
      id,
      path,
      segments
    };
  } catch (error) {
    console.error('Error parsing deep link URL:', error);
    return null;
  }
}

module.exports = {
  generateDeepLink,
  validateDeepLink,
  getShareMetadata,
  handleUniversalLink
};
