const dbQuery = require('../dbConfig/queryRunner');
const moment = require('moment');

const AnalyticsService = {
  /**
   * Track an analytics event
   */
  trackEvent: async (eventData) => {
    try {
      const {
        eventName,
        userId,
        data = {},
        timestamp = Date.now()
      } = eventData;

      if (!eventName) {
        throw new Error('Event name is required');
      }

      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      
      // Insert analytics event into database
      const insertQuery = `
        INSERT INTO analytics_events (
          event_name, user_id, event_data, event_timestamp, created_at
        ) VALUES (?, ?, ?, ?, ?)
      `;

      await dbQuery.queryRunner(insertQuery, [
        eventName,
        userId || null,
        JSON.stringify(data),
        new Date(timestamp),
        currentTime
      ]);

      // Handle specific event types for additional processing
      if (eventName === 'video_view') {
        await this.processVideoViewEvent(data, userId);
      }

      console.log(`[AnalyticsService] Successfully tracked event: ${eventName} for user: ${userId}`);
      
      return {
        eventName,
        success: true,
        timestamp: currentTime
      };

    } catch (error) {
      console.error('[AnalyticsService] Error tracking event:', error);
      throw error;
    }
  },

  /**
   * Process video view events for additional analytics
   */
  processVideoViewEvent: async (eventData, userId) => {
    try {
      const { videoId, videoType, paymentType } = eventData;

      if (!videoId) {
        return;
      }

      // Update video analytics summary
      const updateQuery = `
        INSERT INTO video_analytics_summary (
          video_id, total_views, paid_views, normal_views, last_updated
        ) VALUES (?, 1, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
          total_views = total_views + 1,
          paid_views = paid_views + ?,
          normal_views = normal_views + ?,
          last_updated = NOW()
      `;

      const isPaidView = videoType === 'paid' ? 1 : 0;
      const isNormalView = videoType === 'normal' ? 1 : 0;

      await dbQuery.queryRunner(updateQuery, [
        videoId,
        isPaidView,
        isNormalView,
        isPaidView,
        isNormalView
      ]);

      console.log(`[AnalyticsService] Updated video analytics summary for video ${videoId}`);

    } catch (error) {
      console.error('[AnalyticsService] Error processing video view event:', error);
      // Don't throw error here to avoid breaking the main tracking
    }
  },

  /**
   * Get analytics data
   */
  getAnalytics: async (params) => {
    try {
      const { type, id, userId, startDate, endDate } = params;

      let query = '';
      let queryParams = [];

      if (type === 'video') {
        query = `
          SELECT 
            v.video_id,
            v.total_views,
            v.paid_views,
            v.normal_views,
            r.name as video_title,
            r.is_paid_promotional,
            r.promotional_price
          FROM video_analytics_summary v
          LEFT JOIN reels r ON v.video_id = r.id
          WHERE v.video_id = ?
        `;
        queryParams = [id];
      } else if (type === 'user') {
        query = `
          SELECT 
            event_name,
            COUNT(*) as event_count,
            DATE(created_at) as event_date
          FROM analytics_events
          WHERE user_id = ?
        `;
        queryParams = [id];

        if (startDate) {
          query += ' AND created_at >= ?';
          queryParams.push(startDate);
        }
        if (endDate) {
          query += ' AND created_at <= ?';
          queryParams.push(endDate);
        }

        query += ' GROUP BY event_name, DATE(created_at) ORDER BY event_date DESC';
      }

      const results = await dbQuery.queryRunner(query, queryParams);
      
      return {
        type,
        id,
        data: results || [],
        generatedAt: moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss")
      };

    } catch (error) {
      console.error('[AnalyticsService] Error getting analytics:', error);
      throw error;
    }
  }
};

module.exports = AnalyticsService;
