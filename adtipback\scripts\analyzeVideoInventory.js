// adtipback/scripts/analyzeVideoInventory.js
// Script to analyze current video inventory and Cloudflare Stream data

const dbQuery = require('../dbConfig/queryRunner');
const CloudflareStreamService = require('../services/CloudflareStreamService');

class VideoInventoryAnalyzer {
  constructor() {
    this.streamService = CloudflareStreamService;
  }

  /**
   * Analyze current database video inventory
   */
  async analyzeDatabaseVideos() {
    console.log('\n=== ANALYZING DATABASE VIDEO INVENTORY ===');
    
    try {
      // Get all videos from database
      const sql = `
        SELECT 
          id,
          name,
          video_link,
          video_Thumbnail,
          video_desciption,
          total_views,
          total_likes,
          video_channel,
          stream_video_id,
          stream_status,
          adaptive_manifest_url,
          stream_ready_at,
          createddate,
          is_shot
        FROM reels 
        WHERE is_active = 1 
        ORDER BY id DESC
        LIMIT 100
      `;

      const videos = await dbQuery.queryRunner(sql);
      
      const analysis = {
        total: videos.length,
        withStreamId: 0,
        streamReady: 0,
        streamPending: 0,
        streamError: 0,
        noStreamId: 0,
        validVideoLink: 0,
        shots: 0,
        regularVideos: 0
      };

      const videoDetails = [];

      videos.forEach(video => {
        const detail = {
          id: video.id,
          name: video.name,
          hasStreamId: !!video.stream_video_id,
          streamStatus: video.stream_status,
          hasValidVideoLink: video.video_link && video.video_link.includes('theadtip.in'),
          isShot: video.is_shot === 1,
          views: video.total_views,
          likes: video.total_likes
        };

        videoDetails.push(detail);

        // Update counters
        if (video.stream_video_id) {
          analysis.withStreamId++;
          if (video.stream_status === 'ready') analysis.streamReady++;
          else if (video.stream_status === 'pending') analysis.streamPending++;
          else if (video.stream_status === 'error') analysis.streamError++;
        } else {
          analysis.noStreamId++;
        }

        if (video.video_link && video.video_link.includes('theadtip.in')) {
          analysis.validVideoLink++;
        }

        if (video.is_shot === 1) analysis.shots++;
        else analysis.regularVideos++;
      });

      console.log('\n📊 DATABASE ANALYSIS RESULTS:');
      console.log(`Total videos analyzed: ${analysis.total}`);
      console.log(`Videos with Stream ID: ${analysis.withStreamId}`);
      console.log(`Stream ready: ${analysis.streamReady}`);
      console.log(`Stream pending: ${analysis.streamPending}`);
      console.log(`Stream error: ${analysis.streamError}`);
      console.log(`No Stream ID: ${analysis.noStreamId}`);
      console.log(`Valid video links: ${analysis.validVideoLink}`);
      console.log(`Shots: ${analysis.shots}`);
      console.log(`Regular videos: ${analysis.regularVideos}`);

      return { analysis, videoDetails };

    } catch (error) {
      console.error('Error analyzing database videos:', error);
      throw error;
    }
  }

  /**
   * Analyze Cloudflare Stream inventory
   */
  async analyzeStreamInventory() {
    console.log('\n=== ANALYZING CLOUDFLARE STREAM INVENTORY ===');
    
    try {
      // Test connection first
      const connectionTest = await this.streamService.testConnection();
      if (!connectionTest.success) {
        throw new Error(`Stream connection failed: ${connectionTest.error}`);
      }

      console.log('✅ Cloudflare Stream connection successful');

      // Get all videos from Stream
      let allStreamVideos = [];
      let hasMore = true;
      let after = null;

      while (hasMore) {
        const options = { limit: 100 };
        if (after) options.after = after;

        const result = await this.streamService.listVideos(options);
        
        if (!result.success) {
          throw new Error(`Failed to list Stream videos: ${result.error}`);
        }

        allStreamVideos = allStreamVideos.concat(result.videos);
        
        // Check if there are more videos
        hasMore = result.videos.length === 100;
        if (hasMore && result.videos.length > 0) {
          after = result.videos[result.videos.length - 1].uid;
        }
      }

      const streamAnalysis = {
        total: allStreamVideos.length,
        ready: 0,
        processing: 0,
        error: 0,
        withMeta: 0,
        totalSize: 0
      };

      const streamDetails = allStreamVideos.map(video => {
        // Update counters
        if (video.status?.state === 'ready') streamAnalysis.ready++;
        else if (video.status?.state === 'inprogress') streamAnalysis.processing++;
        else if (video.status?.state === 'error') streamAnalysis.error++;

        if (video.meta && Object.keys(video.meta).length > 0) {
          streamAnalysis.withMeta++;
        }

        if (video.size) {
          streamAnalysis.totalSize += video.size;
        }

        return {
          uid: video.uid,
          filename: video.filename,
          status: video.status?.state,
          duration: video.duration,
          size: video.size,
          created: video.created,
          meta: video.meta,
          preview: video.preview
        };
      });

      console.log('\n📊 CLOUDFLARE STREAM ANALYSIS RESULTS:');
      console.log(`Total Stream videos: ${streamAnalysis.total}`);
      console.log(`Ready videos: ${streamAnalysis.ready}`);
      console.log(`Processing videos: ${streamAnalysis.processing}`);
      console.log(`Error videos: ${streamAnalysis.error}`);
      console.log(`Videos with metadata: ${streamAnalysis.withMeta}`);
      console.log(`Total size: ${(streamAnalysis.totalSize / (1024 * 1024 * 1024)).toFixed(2)} GB`);

      return { streamAnalysis, streamDetails };

    } catch (error) {
      console.error('Error analyzing Stream inventory:', error);
      throw error;
    }
  }

  /**
   * Generate comprehensive migration report
   */
  async generateMigrationReport() {
    console.log('\n🔍 GENERATING COMPREHENSIVE MIGRATION REPORT...\n');

    try {
      const [dbResult, streamResult] = await Promise.all([
        this.analyzeDatabaseVideos(),
        this.analyzeStreamInventory()
      ]);

      const report = {
        timestamp: new Date().toISOString(),
        database: dbResult,
        stream: streamResult,
        migration: {
          videosNeedingStreamId: dbResult.analysis.noStreamId,
          readyStreamVideos: streamResult.streamAnalysis.ready,
          potentialMatches: 0,
          recommendations: []
        }
      };

      // Generate recommendations
      if (dbResult.analysis.noStreamId > 0) {
        report.migration.recommendations.push(
          `${dbResult.analysis.noStreamId} videos need Stream IDs assigned`
        );
      }

      if (streamResult.streamAnalysis.ready > dbResult.analysis.streamReady) {
        const unmappedStream = streamResult.streamAnalysis.ready - dbResult.analysis.streamReady;
        report.migration.recommendations.push(
          `${unmappedStream} ready Stream videos are not mapped to database videos`
        );
      }

      if (dbResult.analysis.streamPending > 0) {
        report.migration.recommendations.push(
          `${dbResult.analysis.streamPending} videos are still processing in Stream`
        );
      }

      console.log('\n📋 MIGRATION RECOMMENDATIONS:');
      report.migration.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });

      // Save report to file
      const fs = require('fs');
      const reportPath = `./migration_report_${Date.now()}.json`;
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n💾 Report saved to: ${reportPath}`);

      return report;

    } catch (error) {
      console.error('Error generating migration report:', error);
      throw error;
    }
  }
}

// Run analysis if called directly
if (require.main === module) {
  const analyzer = new VideoInventoryAnalyzer();
  analyzer.generateMigrationReport()
    .then(() => {
      console.log('\n✅ Analysis complete!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Analysis failed:', error);
      process.exit(1);
    });
}

module.exports = VideoInventoryAnalyzer;
