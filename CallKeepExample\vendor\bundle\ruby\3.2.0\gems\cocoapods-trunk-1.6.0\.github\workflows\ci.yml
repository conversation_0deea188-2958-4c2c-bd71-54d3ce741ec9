name: Specs

jobs:
  specs:
    strategy:
      fail-fast: false
      matrix:
        task: [SPECS]
        ruby: [2.6, 2.7]
        os: [ubuntu-16.04]
        include:
          - task: SPECS
            os: macos-10.15
            ruby: system

    name: ${{ matrix.task }} / ${{ matrix.os }} / Ruby ${{ matrix.ruby }}
    runs-on: ${{ matrix.os }}

    steps:
      - name: Set build image var
        run: echo "ImageVersion=$ImageVersion"  >> $GITHUB_ENV

      - name: Checkout git
        uses: actions/checkout@v1

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        if: ${{ matrix.ruby != 'system' }}
        with:
          ruby-version: ${{ matrix.ruby }}

      - name: Update git submodules
        run: git submodule update --init

      - uses: actions/cache@v2
        with:
          path: vendor/bundle
          key: gems@v1-${{ matrix.os }}-${{ env.ImageVersion }}-Ruby${{ matrix.ruby }}-${{ hashFiles('Gemfile.lock') }}
          restore-keys: |
            gems@v1-${{ matrix.os }}-${{ env.ImageVersion }}-Ruby${{ matrix.ruby }}-
      - name: Run bundle install
        run: |
          gem install bundler -v "~> 1.17"
          bundle config path vendor/bundle
          bundle install --jobs 4 --retry 3 --without debugging documentation
      - name: Set up git identity
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "CocoaPods Tests"

      - name: Run Tests
        run: bundle exec rake spec
        env:
          COCOAPODS_CI_TASKS: ${{ matrix.task }}

on:
  push:
    branches:
      - "master"
      - "*-stable"
  pull_request:
    branches:
      - master
      - "*-stable"
