{"name": "React-RCTImage", "version": "0.80.1", "summary": "A React component for displaying different types of images.", "homepage": "https://reactnative.dev/", "documentation_url": "https://reactnative.dev/docs/image", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "compiler_flags": "-Wno-nullability-completeness", "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.1"}, "source_files": "*.{m,mm}", "preserve_paths": ["package.json", "LICENSE", "LICENSE-docs"], "header_dir": "RCTImage", "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": ["\"${PODS_ROOT}/Headers/Public/ReactCodegen/react/renderer/components\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/React_RCTFBReactNativeSpec.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"]}, "frameworks": ["Accelerate", "UIKit", "QuartzCore", "ImageIO", "CoreGraphics"], "dependencies": {"RCTTypeSafety": [], "React-jsi": [], "React-Core/RCTImageHeaders": [], "React-RCTNetwork": [], "React-RCTFBReactNativeSpec": [], "ReactCommon": [], "React-NativeModulesApple": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}