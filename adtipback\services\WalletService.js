const dbQuery = require("../dbConfig/queryRunner");
const userService = require("./UsersService");
const fcm = require("../utils/fcm");
const utilsConstants = require("../utils/constants");
const mysqlConnection = require("../dbConfig/dbconnection");
const CommissionService = require("./CommissionService");
const moment = require("moment");

const getFunds = (userId) =>
  new Promise((resolve, reject) => {
    const query = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
    console.log(`getFunds called with userId: ${userId}`);
    dbQuery
      .queryRunner(query, [userId])
      .then((result) => {
        console.log(`getFunds result: ${JSON.stringify(result)}`);
        resolve({
          status: 200,
          data: result.length > 0 ? [{ totalBalance: result[0].totalBalance }] : [],
        });
      })
      .catch((err) => {
        reject({ status: 500, message: err.message || "Error fetching funds." });
      });
  });

// services/walletService.js
const saveFunds = (fundData) =>
  new Promise((resolve, reject) => {
    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    console.log(`saveFunds called with fundData: ${JSON.stringify(fundData)}`);
    console.log(`fundData.createdby: ${fundData.createdby}`);
    console.log(`fundData.transaction_type: ${fundData.transaction_type}`);

    return getFunds(fundData.createdby)
      .then((result) => {
        if (result && result.status === 200) {
          if (result.data && result.data.length > 0) {
            const isDeposit =
              fundData.transaction_type === "Deposite" ||
              fundData.transaction_type === "call_deposit";
            fundData.totalBalance = isDeposit
              ? parseFloat(result.data[0].totalBalance) + parseFloat(fundData.amount)
              : parseFloat(result.data[0].totalBalance) - parseFloat(fundData.amount);
            console.log(
              `isDeposit: ${isDeposit}, Previous balance: ${result.data[0].totalBalance}, New amount: ${fundData.amount}, New totalBalance: ${fundData.totalBalance}`
            );
          } else {
            fundData.totalBalance = parseFloat(fundData.amount);
            console.log(`No previous balance found, setting totalBalance to: ${fundData.totalBalance}`);
          }

          const insertQuery = `
            INSERT INTO wallet (amount, transaction_type, createdby, paid_status, call_transaction_id, createddate, totalBalance, order_id, payment_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;
          return dbQuery.queryRunner(insertQuery, [
            fundData.amount,
            fundData.transaction_type,
            fundData.createdby,
            "Paid",
            fundData.call_transaction_id || null,
            updatedTime,
            fundData.totalBalance,
            fundData.order_id,
            fundData.payment_id,
          ]);
        } else {
          reject(result);
        }
      })
      .then((result) => {
        if (result && result.insertId) {
          fundData.id = result.insertId;
          resolve({
            status: 200,
            message: "Funds updated successfully.",
            data: [fundData],
          });
        } else {
          reject({ status: 400, message: "Update failed." });
        }
      })
      .catch((err) => {
        let message = "Internal server error.";
        if (err.message && err.message.includes("ER_DUP_ENTRY")) {
          message = "Duplicate entry not allowed.";
        } else if (err.message && err.message.includes("ER_NO_REFERENCED_ROW_2")) {
          message = "Invalid userId.";
        } else if (err.message && err.message.includes("NOT NULL")) {
          message = "Required field missing.";
        } else if (err.message) {
          message = err.message;
        }
        reject({
          status: 500,
          message,
          data: [],
        });
      });
  });
    
let withdrawFunds = (fundData) =>
  new Promise(async (resolve, reject) => {
    try {
      // --- New Withdrawal Logic ---
      // Fetch user premium status
      const userRows = await dbQuery.queryRunner(`SELECT is_premium FROM users WHERE id = ${fundData.userId}`);
      const isPremium = userRows.length && userRows[0].is_premium == 1;
      const minAmount = isPremium ? 1000 : 5000;
      if (fundData.withdraw_req_amount < minAmount) {
        return reject({
          status: 400,
          message: `Minimum withdrawal amount is ₹${minAmount}`
        });
      }
      // Calculate platform fee and GST
      const platformRate = isPremium ? 0.3 : 0.6;
      const platformFee = fundData.withdraw_req_amount * platformRate;
      const gst = platformFee * 0.18;
      const netAmount = fundData.withdraw_req_amount - platformFee - gst;
      // Check wallet balance
      const walletRows = await dbQuery.queryRunner(`SELECT totalBalance FROM wallet WHERE createdby = ${fundData.userId} ORDER BY id DESC LIMIT 1`);
      const currentBalance = walletRows.length ? parseFloat(walletRows[0].totalBalance) : 0;
      if (fundData.withdraw_req_amount > currentBalance) {
        return reject({
          status: 400,
          message: `Insufficient balance. Available: ₹${currentBalance}`
        });
      }
      // Deduct from wallet immediately
      const newBalance = currentBalance - fundData.withdraw_req_amount;
      await dbQuery.queryRunner(`INSERT INTO wallet (totalBalance, transaction_status, amount, withdraw_req_amount, transaction_type, transaction_method, paid_status, bankName, accountNumber, IFSC, mobileNumber, UPI_ID, transaction_date, createdby, gross_amount, platform_commission, gst_amount, net_amount, createddate) VALUES ('${newBalance}', 4, '${fundData.amount}',${fundData.withdraw_req_amount}, '${fundData.transaction_type}','${fundData.transaction_method}','${fundData.status}','${fundData.bankName}','${fundData.accountNumber}','${fundData.IFSC}','${fundData.mobileNumber}','${fundData.UPI_ID}',NOW(),${fundData.userId},${fundData.withdraw_req_amount},${platformFee},${gst},${netAmount},NOW())`);
      // Update user's wallet balance
      await dbQuery.queryRunner(`UPDATE wallet SET totalBalance = '${newBalance}' WHERE createdby = ${fundData.userId}`);
      resolve({
        status: 200,
        message: "Withdrawal request submitted successfully.",
        data: {
          gross_amount: fundData.withdraw_req_amount,
          platform_fee: platformFee,
          gst_amount: gst,
          net_amount: netAmount,
          remaining_balance: newBalance
        }
      });
    } catch (err) {
      reject({
        status: 500,
        message: err.message || err,
        data: [],
      });
    }
  });

let UpdateWithdrawFunds = (fundData) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO wallet (totalbalance, transaction_status, amount, withdraw_req_amount,
            transaction_type,transaction_method,paid_status,
            bankName,accountNumber,IFSC,
            mobileNumber,UPI_ID,
            transaction_date,createdby, withdraw_request_id,
            createddate)
        VALUES('${fundData.totalBalance}', 4, '${fundData.amount}',${fundData.withdraw_req_amount}, 
            '${fundData.transaction_type}','${fundData.transaction_method}','${fundData.paid_status}',
            '${fundData.bankName}','${fundData.accountNumber}','${fundData.IFSC}',
            '${fundData.mobileNumber}','${fundData.UPI_ID}'
        ,NOW(),${fundData.userId},${fundData.request_id},NOW())`;

    let updateSql = `Update wallet set paid_status='${fundData.paid_status}' Where id = ${fundData.request_id}`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          fundData.id = result.insertId;
          dbQuery.queryRunner(updateSql);
          resolve({
            status: 200,
            message: "Fund added successfully.",
            data: [fundData],
          });
        } else {
          reject({
            status: 400,
            message: "Fund not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate entry not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });
let UpdateWithdrawFunds2 = (fundData) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO wallet (totalbalance, transaction_status, amount, withdraw_req_amount,
            transaction_type,transaction_method,paid_status,
            bankName,accountNumber,IFSC,
            mobileNumber,UPI_ID,
            transaction_date,createdby, withdraw_request_id,
            createddate)
        VALUES('${fundData.totalBalance}', 4, '${fundData.amount}',${fundData.withdraw_req_amount}, 
            '${fundData.transaction_type}','${fundData.transaction_method}','${fundData.paid_status}',
            '${fundData.bankName}','${fundData.accountNumber}','${fundData.IFSC}',
            '${fundData.mobileNumber}','${fundData.UPI_ID}'
        ,NOW(),${fundData.userId},${fundData.request_id},NOW())`;

    let updateSql = `Update wallet set paid_status='${fundData.paid_status}' Where id = ${fundData.id}`;
    let userSql = `SELECT * from users where id=${fundData.userId}`;

    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(userSql)])
      .then((result) => {
        if (result && result.length != 0) {
          fundData.id = result[0].insertId;
          dbQuery.queryRunner(updateSql);
          var deviceToken = result[1][0].device_token;
          fcm.sendNotification({
            title: `${utilsConstants.moneyCreditedTitle}`,
            body: `₹${fundData.withdraw_req_amount} ${utilsConstants.moneyCreditedSubtitle}`,
            token: deviceToken,
          });
          userService.saveNotifications({
            title: `${utilsConstants.moneyCreditedTitle}`,
            subtitle: `₹${fundData.withdraw_req_amount} ${utilsConstants.moneyCreditedSubtitle}`,
            image: `${utilsConstants.adtiplogo}`,
            userId: fundData.userId,
          });

          resolve({
            status: 200,
            message: "Fund added successfully.",
            data: [fundData],
          });
        } else {
          reject({
            status: 400,
            message: "Fund not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate entry not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });
  
  /*
  let getFunds = (id) =>
    new Promise((resolve, reject) => {
      const query = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      console.log("Get funds query:", query, "with params:", [id]); // Debug log
      dbQuery.queryRunner(query, [id])
        .then((result) => {
          const walletData = result[0] || {};
          const balance = walletData.totalBalance || 0;
          resolve({
            status: 200,
            message: "Fetched latest balance successfully.",
            availableBalance: parseFloat(balance).toFixed(2),
          });
        })
        .catch((error) => {
          console.error("Error in getFunds:", error);
          reject({
            status: 400,
            message: error.message || "Server Error",
            data: [],
          });
        });
    });

    */    


  let getPaidFunds = () =>
  new Promise((resolve, reject) => {
    let sql = `select * from wallet where paid_status='Paid' AND withdraw_req_amount>0 order by createdDate desc;`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch fund successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 201,
            message: "Fund not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        console.log("error", err);
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
let getLatestFund = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select totalbalance, transaction_status, amount,transaction_date as transactionDate,createdBy, createdDate from wallet where createdby=${userId} and transaction_status != 6 order by createdDate desc Limit 1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch fund successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Fund not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

  /**
   * Credit ad reward to user's wallet WITHOUT commission (commission applied only on withdrawal)
   * @param {number} userId - User ID
   * @param {number} amount - Full reward amount to credit
   * @returns {Promise<Object>} Result
   */
  const creditAdReward = (userId, amount) =>
    new Promise(async (resolve, reject) => {
      try {
        console.log(`[WalletService] Crediting ad reward for user ${userId}, full amount: ${amount} (no commission deducted)`);

        // Get current wallet balance
        const balanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
        const [balanceData] = await dbQuery.queryRunner(balanceQuery, [userId]);
        const currentBalance = parseFloat(balanceData?.totalBalance || 0);
        const newBalance = currentBalance + parseFloat(amount);

        // Insert wallet transaction without commission deduction
        const walletQuery = `
          INSERT INTO wallet (
            totalBalance, transaction_status, amount, transaction_date, transaction_type,
            createdby, createddate, updateddate, description
          ) VALUES (?, ?, ?, NOW(), ?, ?, NOW(), NOW(), ?)
        `;

        const description = `Ad reward - ${amount} INR (commission will be applied on withdrawal)`;

        const result = await dbQuery.queryRunner(walletQuery, [
          newBalance,
          1, // transaction_status (success)
          parseFloat(amount),
          'ad-reward',
          userId,
          description
        ]);

        console.log(`[WalletService] Ad reward credited successfully without commission`);

        resolve({
          status: 200,
          message: 'Ad reward credited successfully (commission will be applied on withdrawal)',
          data: {
            userId,
            creditedAmount: parseFloat(amount),
            newBalance,
            transactionId: result.insertId,
            note: 'Commission will be applied when withdrawing funds'
          }
        });
      } catch (err) {
        console.error(`[WalletService] Error crediting ad reward:`, err);
        reject({ status: 500, message: err.message || 'Error crediting ad reward.' });
      }
    });

  module.exports = {
    saveFunds,
      
  withdrawFund: (fundData) =>
    new Promise((resolve, reject) => {
      return withdrawFunds(fundData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getPaidFunds: () => {
    return getPaidFunds()
      .then((result) => {
        console.log("result ", result);
        if (result && result.status == 200) {
          console.log("resolved");
          return result; // Returning the result here
        } else {
          return Promise.reject(result);
        }
      })
      .catch((err) => {
        return Promise.reject(err);
      });
  },
  changeStatusPaid: (fundData) => {
    return UpdateWithdrawFunds2(fundData)
      .then((result) => {
        console.log("result ", result);
        if (result && result.status == 200) {
          console.log("resolved");
          return result; // Returning the result here
        } else {
          return Promise.reject(result);
        }
      })
      .catch((err) => {
        return Promise.reject(err);
      });
  },
  updateWithdrawFund: (fundData) =>
    new Promise((resolve, reject) => {
      return getLatestFund(fundData.userId)
        .then((result) => {
          console.log("result", result);
          if (result && result.status == 200) {
            if (result.data && result.data.length != 0) {
              fundData.totalBalance =
                result.data[0].totalbalance - fundData.withdraw_req_amount;

              if (fundData.check_bal_flag == "CHECK_BAL") {
                resolve({
                  status: 200,
                  message:
                    "Available Balance in Wallet : [" +
                    result.data[0].totalbalance +
                    "], Requested Amount is : [" +
                    fundData.withdraw_req_amount +
                    "]",
                  data: result,
                });
              } else {
                if (fundData.totalBalance >= 0) {
                  return UpdateWithdrawFunds(fundData);
                } else {
                  resolve({
                    status: 200,
                    message: "Insufficient Balance in Wallet",
                    data: result,
                  });
                }
              }
            } else {
              if (fundData.check_bal_flag == "CHECK_BAL") {
                resolve({
                  status: 200,
                  message:
                    "Available Balance in Wallet : [" +
                    0 +
                    "], Requested Amount is : [" +
                    0 +
                    "]",
                  data: {
                    status: 200,
                    message: "no withdraw requests.empty",
                    data: [
                      {
                        totalbalance: 0,
                        transaction_status: 4,
                        amount: 0,
                      },
                    ],
                  },
                });
              } else {
                reject({
                  status: 400,
                  message: "Insufficient Balance in Wallet",
                  data: [],
                });
              }
            }
          } else {
            resolve(result);
          }
        })
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getFunds: (id) =>
    new Promise((resolve, reject) => {
      userService
        .getUser(id)
        .then((result) => {
          if (result.status === 200) {
            return getFunds(id);
          } else {
            reject(result);
          }
        })
        .then((result) => {
          if (result && result.status === 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  /**
   * Update or Insert User Wallet Balance
   * @param {Number} userId - ID of the user
   * @param {Number} amount - Amount to add to the wallet
   * @param {Number|null} referralUserId - ID of the user who referred, if applicable
   * @returns {Promise<Object>} Result of wallet update/insert
   */
  updateOrInsertWallet: async (userId, amount, referralUserId) => {
    const connection = await mysqlConnection.getConnection(); // Assuming you are using a connection pool
    try {
      // Start a transaction
      await connection.beginTransaction();

      // Step 1: Use INSERT ... ON DUPLICATE KEY UPDATE to add or update the user's wallet
      const insertOrUpdateWalletQuery = `
      INSERT INTO user_wallet (user_id, balance) 
      VALUES ('${userId}', ${amount})
      ON DUPLICATE KEY UPDATE balance = balance + ${amount}
    `;
      await connection.query(insertOrUpdateWalletQuery);

      // Step 2: Update the total_earnings for the current user
      const updateTotalEarningsQuery = `
      UPDATE users 
      SET referal_earnings = referal_earnings + ${amount} 
      WHERE id = ${userId}
    `;
      await connection.query(updateTotalEarningsQuery);

      // Step 2: Handle referral (if referralUserId is provided)
      if (referralUserId) {
        const insertOrUpdateReferralWalletQuery = `
        INSERT INTO user_wallet (user_id, balance) 
        VALUES ('${referralUserId}', ${amount})
        ON DUPLICATE KEY UPDATE balance = balance + ${amount}
      `;
        await connection.query(insertOrUpdateReferralWalletQuery);

        const updateReferralEarningsQuery = `
        UPDATE users 
        SET referal_earnings = referal_earnings + ${amount} 
        WHERE id = ${referralUserId}
      `;
        await connection.query(updateReferralEarningsQuery);
      }
      // Commit transaction
      await connection.commit();

      return {
        status: 200,
        message: "Wallet updated successfully.",
        data: {
          userId,
          addedBalance: amount,
          referralBonus: referralUserId ? amount : 0,
        },
      };
    } catch (error) {
      // Rollback transaction on error
      await connection.rollback();
      throw new Error("Error updating wallet: " + error.message);
    } finally {
      // Release the connection back to the pool
      await connection.release();
    }
  },

  // Insert transaction for  call Invoice *** function
  insertTransaction: async (transactionData) => {
    try {
      // Validate the input transaction data
      if (
        !transactionData ||
        !transactionData.user_id ||
        !transactionData.transaction_type ||
        !transactionData.amount ||
        !transactionData.createdby
      ) {
        throw new Error("Missing required transaction data");
      }

      // Extract values from transactionData
      const { user_id, transaction_type, amount, createdby } = transactionData;

      // 1. Get 'referal_earnings' from users table
      const referralQuery = `
      SELECT referal_earnings 
      FROM users 
      WHERE id = ${user_id}`;
      const referralResult = await dbQuery.queryRunner(referralQuery);

      if (referralResult.length === 0) {
        throw new Error("User not found");
      }

      const referalEarnings = referralResult[0].referal_earnings;

      // 2. Get the latest transaction for 'createdby' to calculate the balance
      const latestTransactionQuery = `
      SELECT totalBalance 
      FROM wallet 
      WHERE createdby = ${createdby} 
      ORDER BY created_at DESC 
      LIMIT 1`;
      const walletResult = await dbQuery.queryRunner(latestTransactionQuery);

      // 3. Determine previous balance or set to 0 if none found
      const previousBalance =
        walletResult.length > 0 ? walletResult[0].totalBalance : 0;

      // 4. Handle transaction based on transaction type
      let updatedBalance;

      if (transaction_type === "Deposite") {
        updatedBalance = previousBalance + amount;

        // Insert transaction for deposit
        const insertDepositQuery = `
        INSERT INTO wallet (transaction_type, amount, totalBalance, createdby, created_at) 
        VALUES (
          'Deposite', 
          ${amount}, 
          ${updatedBalance}, 
          ${user_id}, 
          NOW())`;

        await dbQuery.queryRunner(insertDepositQuery);
      } else if (transaction_type === "Withdrawal") {
        updatedBalance = previousBalance - amount;

        // Insert transaction for withdrawal
        const insertWithdrawalQuery = `
        INSERT INTO wallet (transaction_type, amount, totalBalance, createdby, created_at) 
        VALUES (
          'Withdrawal', 
          ${amount}, 
          ${updatedBalance}, 
          ${user_id}, 
          NOW())`;

        await dbQuery.queryRunner(insertWithdrawalQuery);
      } else {
        throw new Error("Invalid transaction type");
      }

      // Return success
      return {
        success: true,
        message: "Transaction successfully processed",
      };
    } catch (error) {
      // Handle errors and return failure message
      return {
        success: false,
        message: error.message,
      };
    }
  },

  createReferralTransaction: async (referrer_code, referred_user_id) => {
    try {
      // Get current time and add 5 hours 30 minute
      const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      // Step 1: Validate referred_user_id
      const userCheckQuery = `SELECT id FROM users WHERE id = ${referred_user_id}`;
      const userResult = await dbQuery.queryRunner(userCheckQuery);
      if (userResult.length === 0) throw new Error("Invalid referred_user_id");

      // Step 2: Validate referrer_code
      const referrerQuery = `SELECT id FROM users WHERE referal_code = '${referrer_code}'`;
      const referrerResult = await dbQuery.queryRunner(referrerQuery);
      if (referrerResult.length === 0) throw new Error("Invalid referral code");

      const referrer_user_id = referrerResult[0].id;
      if (referrer_user_id === referred_user_id)
        throw new Error("A user cannot refer themselves");

      // Step 3: Check if referral already exists
      const referralExistsQuery = `SELECT id FROM user_referrals WHERE referred_user_id = ${referred_user_id} AND referral_code = '${referrer_code}'`;
      const referralExists = await dbQuery.queryRunner(referralExistsQuery);
      if (referralExists.length > 0) return referralExists[0];

      // Step 4: Create referral entry
      const createReferralQuery = `INSERT INTO user_referrals (referrer_user_id, referred_user_id, referral_code, createdat) VALUES (${referrer_user_id}, ${referred_user_id}, '${referrer_code}', '${updatedTime}')`;
      const referralResult = await dbQuery.queryRunner(createReferralQuery);
      const referralId = referralResult.insertId;

      // Step 5: Ensure wallets exist for both referrer and referred user
      const rewardAmount = 3;

      const checkWallet = async (user_id) => {
        const walletCheckQuery = `SELECT id, balance FROM user_referral_wallet WHERE user_id = ${user_id}`;
        const walletResult = await dbQuery.queryRunner(walletCheckQuery);
        if (walletResult.length > 0) return walletResult[0];

        // Create new wallet
        const createWalletQuery = `INSERT INTO user_referral_wallet (user_id, balance, createdat) VALUES (${user_id}, 0, '${updatedTime}')`;
        const walletInsertResult = await dbQuery.queryRunner(createWalletQuery);
        return { id: walletInsertResult.insertId, balance: 0 };
      };

      const referrerWallet = await checkWallet(referrer_user_id);
      const referredWallet = await checkWallet(referred_user_id);

      // Step 6: Add referral transaction entries
      const newReferrerBalance = referrerWallet.balance + rewardAmount;
      const newReferredBalance = referredWallet.balance + rewardAmount;

      const transactionQuery = `
      INSERT INTO user_referral_wallet_transactions 
      (referrer_wallet_id, user_id, transaction_type, amount, balance, paid_status, created_at) 
      VALUES 
      (${referrerWallet.id}, ${referrer_user_id}, 'deposit', ${rewardAmount}, ${newReferrerBalance}, 'completed', '${updatedTime}'),
      (${referredWallet.id}, ${referred_user_id}, 'deposit', ${rewardAmount}, ${newReferredBalance}, 'completed', '${updatedTime}')
    `;
      await dbQuery.queryRunner(transactionQuery);

      // Step 7: Update wallet balances for both users
      const updateWalletQuery = `
      UPDATE user_referral_wallet 
      SET balance = CASE 
        WHEN id = ${referrerWallet.id} THEN ${newReferrerBalance} 
        WHEN id = ${referredWallet.id} THEN ${newReferredBalance} 
      END
      WHERE id IN (${referrerWallet.id}, ${referredWallet.id})
    `;
      await dbQuery.queryRunner(updateWalletQuery);

      return {
        referral_id: referralId,
        referrer_user_id,
        referred_user_id,
        code: referrer_code,
        referrer_wallet_id: referrerWallet.id,
        referred_wallet_id: referredWallet.id,
        referrer_balance: newReferrerBalance,
        referred_balance: newReferredBalance,
        reward_amount: rewardAmount,
        paid_status: "pending",
      };
    } catch (error) {
      console.error("Error during referral process:", error.message);
      throw error;
    }
  },

  createCouponTransaction: async (coupon_code, applied_user_id, plan_id) => {
    try {
      const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

      // Step 1: Fetch coupon details from the coupons table
      const couponCheckQuery = `SELECT * FROM coupons WHERE code = '${coupon_code}'`;
      const couponResult = await dbQuery.queryRunner(couponCheckQuery);

      if (couponResult.length === 0) {
        return {
          statusCode: 400,
          message: "Invalid coupon code",
        };
      }

      // Extract coupon details
      const { coupon_id, owner_user_id } = couponResult[0];

      // Step 2: Check if the applied user exists
      const userCheckQuery = `SELECT * FROM users WHERE id = ${applied_user_id}`;
      const userResult = await dbQuery.queryRunner(userCheckQuery);
      if (userResult.length === 0) {
        return { statusCode: 400, message: "Invalid user ID" };
      }

      // Step 3: Determine coupon type (Normal or Referral)
      if ([null, undefined, 0, ""].includes(owner_user_id)) {
        // NORMAL COUPON PROCESS

        // Check if the user has already used this coupon
        const normalCouponCheckQuery = `
        SELECT * FROM user_coupon WHERE applied_by_user = ${applied_user_id} AND code_id = ${coupon_id}
      `;
        const normalCouponResult = await dbQuery.queryRunner(
          normalCouponCheckQuery
        );
        if (normalCouponResult.length > 0) {
          return {
            statusCode: 400,
            message: "This coupon has already been used",
          };
        }

        // Insert into user_coupons for normal coupon
        const insertNormalCouponQuery = `
        INSERT INTO user_coupon (applied_by_user, code_id, used_at, referred_user_id, referrer_user_id) 
        VALUES (${applied_user_id}, ${coupon_id}, '${updatedTime}', NULL, NULL)
      `;
        await dbQuery.queryRunner(insertNormalCouponQuery);

        return {
          coupon_id,
          coupon_code,
          message: "Normal coupon applied successfully",
        };
      } else {
        // REFERRAL COUPON PROCESS

        // Check if the referral coupon has already been used by referred_user_id
        const referralCouponCheckQuery = `
        SELECT * FROM user_coupon 
        WHERE referred_user_id = ${owner_user_id} AND code_id = ${coupon_id}
      `;
        const referralCouponResult = await dbQuery.queryRunner(
          referralCouponCheckQuery
        );
        if (referralCouponResult.length > 0) {
          return {
            statusCode: 400,
            message: "This referral coupon has already been used",
          };
        }

        // Insert into user_coupons for referral tracking
        const insertReferralCouponQuery = `
        INSERT INTO user_coupon (code_id, referred_user_id, referrer_user_id, used_at) 
        VALUES (${coupon_id}, ${applied_user_id},${owner_user_id},'${updatedTime}')
      `;
        await dbQuery.queryRunner(insertReferralCouponQuery);

        // Step 4: Fetch referral bonus from subscription_plans using `plan_id`
        const planQuery = `
        SELECT referral_amount FROM subscription_plans WHERE id = ${plan_id}
      `;
        const planResult = await dbQuery.queryRunner(planQuery);
        const referralAmount = planResult.length
          ? planResult[0].referral_amount
          : 0;

        if (referralAmount > 0) {
          // Step 5: Check if the wallet exists for the referrer
          const walletCheckQuery = `
          SELECT id FROM user_coupon_wallet WHERE user_id = ${owner_user_id}
        `;
          const walletResult = await dbQuery.queryRunner(walletCheckQuery);

          let walletId;
          if (walletResult.length > 0) {
            walletId = walletResult[0].id;
          } else {
            // Create wallet if it does not exist
            const createWalletQuery = `
            INSERT INTO user_coupon_wallet (user_id, balance) VALUES (${owner_user_id}, 0)
          `;
            const walletInsertResult = await dbQuery.queryRunner(
              createWalletQuery
            );
            walletId = walletInsertResult.insertId;
          }

          // Step 6: Insert transaction into user_wallet_transactions
          const insertTransactionQuery = `
          INSERT INTO user_coupon_wallet_transactions (coupon_wallet_id, user_id, transaction_type, amount, created_at,paid_status) 
          VALUES (${walletId}, ${applied_user_id}, 'credit', ${referralAmount},'${updatedTime}','completed')
        `;
          await dbQuery.queryRunner(insertTransactionQuery);

          // Step 7: Update referrer's wallet balance
          const updateWalletQuery = `
          UPDATE user_coupon_wallet 
          SET balance = balance + ${referralAmount} 
          WHERE id = ${walletId}
        `;
          await dbQuery.queryRunner(updateWalletQuery);
        }

        return {
          coupon_id,
          coupon_code,
          referrer_id: applied_user_id,
          referred_user_id: owner_user_id,
          discount: referralAmount,
          wallet_balance: referralAmount,
        };
      }
    } catch (error) {
      return {
        statusCode: 400,
        message: "Error during coupon transaction: " + error.message,
      };
    }
  },
};

module.exports.creditAdReward = creditAdReward;
