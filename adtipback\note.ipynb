{"cells": [{"cell_type": "code", "execution_count": null, "id": "6b37ae36", "metadata": {}, "outputs": [], "source": ["npm i\n", "npm run server"]}, {"cell_type": "code", "execution_count": 1, "id": "43df945b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n"]}], "source": ["x = lambda a,b: a+b\n", "print(x(5,1))"]}, {"cell_type": "code", "execution_count": 2, "id": "b0d4776a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<function <lambda> at 0x000002AB9E38D900>\n"]}], "source": ["print(lambda a,b: a+b(5,1))"]}], "metadata": {"kernelspec": {"display_name": "video", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}