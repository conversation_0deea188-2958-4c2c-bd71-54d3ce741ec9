// adtipback/controllers/StreamWebhookController.js
// Handles Cloudflare Stream webhook notifications for video encoding status
// Reference: https://developers.cloudflare.com/stream/manage-video-library/using-webhooks/

const dbQuery = require('../dbConfig/queryRunner');
const CloudflareStreamService = require('../services/CloudflareStreamService');
const crypto = require('crypto');

class StreamWebhookController {
  /**
   * Verify webhook signature for security
   * @param {string} payload - Raw webhook payload
   * @param {string} signature - Webhook signature header
   * @param {string} secret - Webhook secret
   */
  static verifyWebhookSignature(payload, signature, secret) {
    if (!secret || !signature) {
      console.warn('[StreamWebhook] Webhook signature verification skipped - no secret configured');
      return true; // Allow for development, but should be required in production
    }

    try {
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(payload)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      console.error('[StreamWebhook] Signature verification error:', error);
      return false;
    }
  }

  /**
   * Handle Cloudflare Stream webhook notifications
   * Called when video encoding status changes (uploading -> ready/error)
   */
  static async handleStreamWebhook(req, res) {
    try {
      // Verify webhook signature if secret is configured
      const webhookSecret = process.env.CLOUDFLARE_WEBHOOK_SECRET;
      const signature = req.headers['cf-webhook-signature'];

      if (webhookSecret && !StreamWebhookController.verifyWebhookSignature(
        JSON.stringify(req.body),
        signature,
        webhookSecret
      )) {
        console.error('[StreamWebhook] Invalid webhook signature');
        return res.status(401).json({ error: 'Invalid signature' });
      }

      const { uid, status, meta, input, playback, readyToStream, readyToStreamAt } = req.body;

      console.log('[StreamWebhook] Received notification:', {
        uid,
        status,
        readyToStream,
        timestamp: new Date().toISOString()
      });

      // Validate required fields
      if (!uid) {
        console.error('[StreamWebhook] Missing required field: uid');
        return res.status(400).json({
          success: false,
          error: 'Missing required field: uid'
        });
      }

      // Process based on status
      switch (status) {
        case 'ready':
          await StreamWebhookController.handleVideoReady(uid, { meta, input, playback });
          break;
          
        case 'error':
          await StreamWebhookController.handleVideoError(uid, { meta, input });
          break;
          
        case 'inprogress':
          await StreamWebhookController.handleVideoInProgress(uid);
          break;
          
        default:
          console.log('[StreamWebhook] Unknown status:', status);
      }

      res.status(200).json({ 
        success: true, 
        message: 'Webhook processed successfully',
        uid,
        status 
      });

    } catch (error) {
      console.error('[StreamWebhook] Processing error:', error);
      res.status(500).json({ 
        success: false, 
        error: 'Webhook processing failed',
        details: error.message 
      });
    }
  }

  /**
   * Handle video ready status - update database with Stream URLs
   */
  static async handleVideoReady(uid, { meta, input, playback }) {
    try {
      console.log('[StreamWebhook] Processing ready video:', uid);

      // Generate manifest URL for adaptive streaming
      const manifestUrl = CloudflareStreamService.getManifestUrl(uid, 'hls');
      
      // Extract video metadata
      const duration = input?.duration || null;
      const sizeBytes = input?.size || null;

      // Update database with Stream ready status
      const updateSql = `
        UPDATE reels SET 
          stream_status = 'ready',
          stream_ready_at = NOW(),
          adaptive_manifest_url = ?,
          stream_duration = ?,
          stream_size_bytes = ?
        WHERE stream_video_id = ?
      `;

      const result = await dbQuery.queryRunner(updateSql, [
        manifestUrl,
        duration,
        sizeBytes,
        uid
      ]);

      if (result.affectedRows > 0) {
        console.log('[StreamWebhook] Video marked as ready:', {
          uid,
          manifestUrl,
          duration,
          affectedRows: result.affectedRows
        });

        // Optional: Trigger any post-processing tasks
        await StreamWebhookController.triggerPostProcessing(uid);
      } else {
        console.warn('[StreamWebhook] No records updated for uid:', uid);
      }

    } catch (error) {
      console.error('[StreamWebhook] Error handling ready video:', error);
      throw error;
    }
  }

  /**
   * Handle video error status
   */
  static async handleVideoError(uid, { meta, input }) {
    try {
      console.log('[StreamWebhook] Processing error video:', uid);

      const updateSql = `
        UPDATE reels SET 
          stream_status = 'error'
        WHERE stream_video_id = ?
      `;

      const result = await dbQuery.queryRunner(updateSql, [uid]);

      if (result.affectedRows > 0) {
        console.log('[StreamWebhook] Video marked as error:', {
          uid,
          affectedRows: result.affectedRows
        });
      } else {
        console.warn('[StreamWebhook] No records updated for error uid:', uid);
      }

    } catch (error) {
      console.error('[StreamWebhook] Error handling error video:', error);
      throw error;
    }
  }

  /**
   * Handle video in progress status
   */
  static async handleVideoInProgress(uid) {
    try {
      console.log('[StreamWebhook] Processing in-progress video:', uid);

      const updateSql = `
        UPDATE reels SET 
          stream_status = 'uploading'
        WHERE stream_video_id = ? AND stream_status = 'pending'
      `;

      const result = await dbQuery.queryRunner(updateSql, [uid]);

      console.log('[StreamWebhook] Video status updated to uploading:', {
        uid,
        affectedRows: result.affectedRows
      });

    } catch (error) {
      console.error('[StreamWebhook] Error handling in-progress video:', error);
      throw error;
    }
  }

  /**
   * Trigger any post-processing tasks after video is ready
   */
  static async triggerPostProcessing(uid) {
    try {
      // Optional: Add any post-processing logic here
      // Examples:
      // - Generate thumbnails
      // - Send notifications to users
      // - Update search indexes
      // - Analytics tracking

      console.log('[StreamWebhook] Post-processing completed for:', uid);
    } catch (error) {
      console.error('[StreamWebhook] Post-processing error:', error);
      // Don't throw - post-processing failures shouldn't fail the webhook
    }
  }

  /**
   * Get Stream status for a video
   */
  static async getStreamStatus(req, res) {
    try {
      const { videoId } = req.params;

      if (!videoId) {
        return res.status(400).json({
          success: false,
          error: 'Video ID is required'
        });
      }

      // Get status from database
      const sql = `
        SELECT 
          id,
          name,
          stream_video_id,
          stream_status,
          adaptive_manifest_url,
          stream_created_at,
          stream_ready_at,
          stream_duration
        FROM reels 
        WHERE stream_video_id = ? OR id = ?
      `;

      const result = await dbQuery.queryRunner(sql, [videoId, videoId]);

      if (result.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Video not found'
        });
      }

      const video = result[0];

      // If video is ready, also get latest status from Stream API
      let streamApiStatus = null;
      if (video.stream_video_id && video.stream_status === 'ready') {
        streamApiStatus = await CloudflareStreamService.getVideoStatus(video.stream_video_id);
      }

      res.json({
        success: true,
        video: {
          id: video.id,
          name: video.name,
          streamId: video.stream_video_id,
          status: video.stream_status,
          manifestUrl: video.adaptive_manifest_url,
          createdAt: video.stream_created_at,
          readyAt: video.stream_ready_at,
          duration: video.stream_duration,
          streamApiStatus: streamApiStatus?.success ? streamApiStatus.data : null
        }
      });

    } catch (error) {
      console.error('[StreamWebhook] Get status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get stream status',
        details: error.message
      });
    }
  }
}

module.exports = StreamWebhookController;
