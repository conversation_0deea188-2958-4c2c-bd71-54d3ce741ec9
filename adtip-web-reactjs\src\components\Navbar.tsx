import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import { useState, useEffect, memo, useCallback } from "react";
import {
  User,
  Search,
  Wallet,
  Bell,
  Home,
  Video,
  Plus,
  PhoneCall,
  ToggleLeft,
  ToggleRight,
  Menu,
} from "lucide-react";
import { useLocation } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useSidebar } from "../contexts/SidebarContext";
import AdTipSidebar from "@/components/ui/AdTipSidebar";
import { logApi, logError } from "../utils/ProductionLogger";
import { queryKeys } from "../lib/queryClient";

const Navbar = memo(() => {
  const { user, updateUserProfile } = useAuth();
  const { toggleSidebar, openMobile, setOpenMobile } = useSidebar();
  const [searchQuery, setSearchQuery] = useState("");
  const [isToggleOn, setIsToggleOn] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  // Memoize the isActive function to prevent unnecessary re-renders
  const isActive = useCallback((path: string) => {
    return location.pathname === path;
  }, [location.pathname]);

  // Define the type for the API response
  interface BalanceResponse {
    status: number;
    message: string;
    availableBalance: string;
  }

  // Fetch wallet balance using react-query with optimized caching
  const { data: balanceData, isLoading, isSuccess, error } = useQuery<BalanceResponse, Error>({
    queryKey: user?.id ? queryKeys.user.wallet(user.id.toString()) : ["walletBalance"],
    queryFn: async () => {
      if (!user?.id || !user?.accessToken) {
        throw new Error("User ID or access token missing");
      }
      const response = await axios.get(`https://api.adtip.in/api/getfunds/${user.id}`, {
        headers: { Authorization: `Bearer ${user.accessToken}` },
      });
      logApi("Navbar", `/getfunds/${user.id}`, null, {
        status: response.data.status,
        hasBalance: !!response.data.availableBalance,
      });
      return response.data;
    },
    enabled: !!user?.id && !!user?.accessToken, // Only run if user.id and accessToken exist
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - data is fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes - keep in cache for 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (error.message.includes("authentication") || error.message.includes("401")) {
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });

  // Update user profile when balance is successfully fetched
  useEffect(() => {
    if (isSuccess && balanceData) {
      const balance = parseFloat(balanceData.availableBalance) || 0;
      updateUserProfile({ wallet: balance });
    }
  }, [isSuccess, balanceData, updateUserProfile]);

  // Handle error case
  useEffect(() => {
    if (error) {
      logError("Navbar", "Failed to fetch wallet balance", error);
      updateUserProfile({ wallet: 0 }); // Fallback to 0 on error
    }
  }, [error, updateUserProfile]);

  return (
    <>
      {/* Mobile Sidebar Overlay */}
      <div className="md:hidden">
        {openMobile && (
          <div className="fixed inset-0 z-50">
            <div className="fixed inset-0 bg-black/40" onClick={() => setOpenMobile(false)} />
            <div className="fixed inset-y-0 left-0 w-64 bg-white shadow-lg z-50">
              <div className="h-full overflow-y-auto">
                <AdTipSidebar />
              </div>
            </div>
          </div>
        )}
      </div>

      <nav className="w-full bg-white">
        <div className="max-w-screen-2xl mx-auto flex items-center justify-between px-3 sm:px-4 md:px-6 py-2 md:py-3">
        {/* Left: Hamburger and Logo */}
        <div className="flex items-center gap-2 sm:gap-4">
          <button
            onClick={toggleSidebar}
            className="p-1 sm:p-1.5 hover:bg-gray-100 rounded-lg transition-colors"
            aria-label="Toggle sidebar"
          >
            <Menu className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600" />
          </button>
          <Link to="/home" className="flex items-center">
            <img src="/logo.png" alt="AdTip Logo" className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8" />
            <span className="text-base sm:text-lg md:text-xl font-bold text-adtip-teal ml-1.5 sm:ml-2">AdTip</span>
          </Link>
        </div>

        {/* Center: Search Bar */}
        <div className="flex-1 max-w-3xl mx-2 sm:mx-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search users or content..."
              className="w-full px-3 sm:px-4 md:px-5 py-2 sm:py-2.5 text-sm md:text-base rounded-full border border-gray-300 focus:outline-none focus:border-adtip-teal"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute right-3 sm:right-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 sm:h-5 sm:w-5" />
          </div>
        </div>

        {/* Right: Icons, Toggle, and Profile */}
        <div className="flex items-center gap-2 sm:gap-3 md:gap-4">
          <Link
            to="/notifications"
            className="text-gray-500 hover:text-adtip-teal transition-colors p-1"
          >
            <Bell className="h-5 w-5 sm:h-6 sm:w-6" />
          </Link>
          {/* Toggle Button */}
          <button
            onClick={() => {
              if (!user) {
                navigate("/login");
              } else if (!user.is_premium) {
                navigate("/pricingoffers");
              } else {
                setIsToggleOn(!isToggleOn);
              }
            }}
            className="flex items-center bg-gray-50 border border-gray-200 rounded-full p-1 sm:p-1.5 transition-colors hover:bg-gray-100"
            aria-label="Toggle notifications"
          >
            {isToggleOn ? (
              <ToggleRight className="h-8 w-8 sm:h-9 sm:w-9 md:h-10 md:w-10 text-green-500" />
            ) : (
              <ToggleLeft className="h-8 w-8 sm:h-9 sm:w-9 md:h-10 md:w-10 text-gray-400" />
            )}
          </button>
          <Link
            to={user ? "/wallet" : "/login"}
            className="flex items-center text-gray-700 hover:text-adtip-teal transition-colors bg-gray-50 border border-gray-200 rounded-full px-3 py-1 mr-1"
            style={{ minWidth: 70 }}
          >
            <Wallet className="h-5 w-5 sm:h-6 sm:w-6 mr-1" />
            <span className="text-sm sm:text-base font-medium tabular-nums">
              {isLoading
                ? "..."
                : balanceData && typeof balanceData.availableBalance === "string"
                  ? `₹${parseFloat(balanceData.availableBalance).toFixed(2)}`
                  : "₹0.00"}
            </span>
          </Link>
          <Link to="/profile" className="flex items-center ml-1 sm:ml-2">
            {user?.profile_image ? (
              <img
                src={user.profile_image}
                alt="Profile"
                className="h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 rounded-full object-cover border-2 border-gray-200"
              />
            ) : (
              <div className="h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9 rounded-full bg-gray-200 flex items-center justify-center border-2 border-gray-200">
                <User className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
              </div>
            )}
          </Link>
        </div>
        </div>
      </nav>

      {/* Mobile Bottom Navigation */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white shadow-md border-t border-gray-200 z-40 safe-area-pb">
        <div className="flex justify-around items-center px-2 py-2 sm:py-3">
          <Link
            to="/home"
            className={`flex flex-col items-center ${isActive("/home") ? "text-adtip-teal" : "text-gray-500"}`}
          >
            <Home className="h-5 w-5 sm:h-6 sm:w-6" />
            <span className="text-[10px] sm:text-xs mt-1">Home</span>
          </Link>
          <Link
            to="/tiptube"
            className={`flex flex-col items-center ${isActive("/tiptube") ? "text-adtip-teal" : "text-gray-500"}`}
          >
            <Video className="h-5 w-5 sm:h-6 sm:w-6" />
            <span className="text-[10px] sm:text-xs mt-1">TipTube</span>
          </Link>
          <Link
            to="/create-post"
            className="flex flex-col items-center justify-center"
          >
            <div className="h-10 w-10 sm:h-12 sm:w-12 rounded-full teal-gradient flex items-center justify-center">
              <Plus className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </div>
          </Link>
          <Link
            to="/tipcall"
            className={`flex flex-col items-center ${isActive("/tipcall") ? "text-adtip-teal" : "text-gray-500"}`}
          >
            <PhoneCall className="h-5 w-5 sm:h-6 sm:w-6" />
            <span className="text-[10px] sm:text-xs mt-1">TipCall</span>
          </Link>
          <Link
            to="/profile"
            className={`flex flex-col items-center ${isActive("/profile") ? "text-adtip-teal" : "text-gray-500"}`}
          >
            <User className="h-5 w-5 sm:h-6 sm:w-6" />
            <span className="text-[10px] sm:text-xs mt-1">Profile</span>
          </Link>
        </div>
      </nav>
    </>
  );
});

export default Navbar;