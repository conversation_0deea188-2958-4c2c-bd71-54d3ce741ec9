const { queryRunner } = require('../dbConfig/queryRunner');
const moment = require('moment');

function getUnixTimestamp(date) {
  return Math.floor(new Date(date).getTime() / 1000);
}

exports.manualCreateUserPremium = async (userId, planId, duration, note) => {
  // 1. Calculate start and end time
  const now = moment().utcOffset(330);
  const startTime = now.format('YYYY-MM-DD HH:mm:ss');
  const endTime = now.clone().add(duration, 'days').format('YYYY-MM-DD HH:mm:ss');
  const unixStart = getUnixTimestamp(startTime);
  const unixEnd = getUnixTimestamp(endTime);
  // 2. Insert new user_subscriptions row
  const insertQuery = `
    INSERT INTO user_subscriptions (
      user_id, razorpay_plan_id, razorpay_subscription_id, status, 
      total_count, paid_count, current_start_at, current_end_at, 
      charge_at, start_at, customer_notify, notes
    )
    VALUES (?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?), FROM_UNIXTIME(?), FROM_UNIXTIME(?), FROM_UNIXTIME(?), ?, ?)
  `;
  const fakeSubId = 'manual_' + userId + '_' + Date.now();
  await queryRunner(insertQuery, [
    userId,
    planId,
    fakeSubId,
    'active',
    1,
    1,
    unixStart,
    unixEnd,
    unixStart,
    unixStart,
    0,
    JSON.stringify({ manual: true, note }),
  ]);
  // 3. Update users table
  await queryRunner('UPDATE users SET is_premium = TRUE, premium_expires_at = ? WHERE id = ?', [endTime, userId]);
  return { userId, planId, startTime, endTime, note, razorpay_subscription_id: fakeSubId };
};

exports.manualCancelUserPremium = async (userId, note) => {
  // 1. Set all active subscriptions to cancelled
  const findQuery = "SELECT razorpay_subscription_id FROM user_subscriptions WHERE user_id = ? AND status = 'active'";
  const subs = await queryRunner(findQuery, [userId]);
  for (const sub of subs) {
    await queryRunner(
      "UPDATE user_subscriptions SET status = 'cancelled', updated_at = NOW(), notes = ? WHERE razorpay_subscription_id = ?",
      [JSON.stringify({ manual_cancel: true, note }), sub.razorpay_subscription_id]
    );
  }
  // 2. Update users table
  await queryRunner('UPDATE users SET is_premium = FALSE, premium_expires_at = NULL WHERE id = ?', [userId]);
  return { userId, note, cancelled_subscriptions: subs.map(s => s.razorpay_subscription_id) };
}; 