# Makefile.in generated by automake 1.16.5 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2021 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@



VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
@BUILD_DOCS_TRUE@am__append_1 = doc
@FFI_DEBUG_TRUE@am__append_2 = src/debug.c
# Build debug. Define FFI_DEBUG on the commandline so that, when building with
# MSVC, it can link against the debug CRT.
@FFI_DEBUG_TRUE@am__append_3 = -DFFI_DEBUG
subdir = .
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/asmcfi.m4 \
	$(top_srcdir)/m4/ax_cc_maxopt.m4 \
	$(top_srcdir)/m4/ax_cflags_warn_all.m4 \
	$(top_srcdir)/m4/ax_check_compile_flag.m4 \
	$(top_srcdir)/m4/ax_compiler_vendor.m4 \
	$(top_srcdir)/m4/ax_configure_args.m4 \
	$(top_srcdir)/m4/ax_enable_builddir.m4 \
	$(top_srcdir)/m4/ax_gcc_archflag.m4 \
	$(top_srcdir)/m4/ax_gcc_x86_cpuid.m4 \
	$(top_srcdir)/m4/ax_prepend_flag.m4 \
	$(top_srcdir)/m4/ax_require_defined.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/acinclude.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(top_srcdir)/configure \
	$(am__configure_deps) $(noinst_HEADERS) $(am__DIST_COMMON)
am__CONFIG_DISTCLEAN_FILES = config.status config.cache config.log \
 configure.lineno config.status.lineno
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = fficonfig.h
CONFIG_CLEAN_FILES = libffi.pc
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(toolexeclibdir)" \
	"$(DESTDIR)$(pkgconfigdir)"
LTLIBRARIES = $(noinst_LTLIBRARIES) $(toolexeclib_LTLIBRARIES)
am__DEPENDENCIES_1 =
am__libffi_la_SOURCES_DIST = src/prep_cif.c src/types.c src/raw_api.c \
	src/java_raw_api.c src/closures.c src/tramp.c src/debug.c
am__dirstamp = $(am__leading_dot)dirstamp
@FFI_DEBUG_TRUE@am__objects_1 = src/debug.lo
am_libffi_la_OBJECTS = src/prep_cif.lo src/types.lo src/raw_api.lo \
	src/java_raw_api.lo src/closures.lo src/tramp.lo \
	$(am__objects_1)
libffi_la_OBJECTS = $(am_libffi_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libffi_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libffi_la_LDFLAGS) $(LDFLAGS) -o $@
am__DEPENDENCIES_2 = $(am__DEPENDENCIES_1)
am__libffi_convenience_la_SOURCES_DIST = src/prep_cif.c src/types.c \
	src/raw_api.c src/java_raw_api.c src/closures.c src/tramp.c \
	src/debug.c
am__objects_2 = src/prep_cif.lo src/types.lo src/raw_api.lo \
	src/java_raw_api.lo src/closures.lo src/tramp.lo \
	$(am__objects_1)
am_libffi_convenience_la_OBJECTS = $(am__objects_2)
nodist_libffi_convenience_la_OBJECTS =
libffi_convenience_la_OBJECTS = $(am_libffi_convenience_la_OBJECTS) \
	$(nodist_libffi_convenience_la_OBJECTS)
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = src/$(DEPDIR)/closures.Plo \
	src/$(DEPDIR)/debug.Plo src/$(DEPDIR)/java_raw_api.Plo \
	src/$(DEPDIR)/prep_cif.Plo src/$(DEPDIR)/raw_api.Plo \
	src/$(DEPDIR)/tramp.Plo src/$(DEPDIR)/types.Plo \
	src/aarch64/$(DEPDIR)/ffi.Plo src/aarch64/$(DEPDIR)/sysv.Plo \
	src/aarch64/$(DEPDIR)/win64_armasm.Plo \
	src/alpha/$(DEPDIR)/ffi.Plo src/alpha/$(DEPDIR)/osf.Plo \
	src/arc/$(DEPDIR)/arcompact.Plo src/arc/$(DEPDIR)/ffi.Plo \
	src/arm/$(DEPDIR)/ffi.Plo src/arm/$(DEPDIR)/sysv.Plo \
	src/arm/$(DEPDIR)/sysv_msvc_arm32.Plo \
	src/avr32/$(DEPDIR)/ffi.Plo src/avr32/$(DEPDIR)/sysv.Plo \
	src/bfin/$(DEPDIR)/ffi.Plo src/bfin/$(DEPDIR)/sysv.Plo \
	src/cris/$(DEPDIR)/ffi.Plo src/cris/$(DEPDIR)/sysv.Plo \
	src/csky/$(DEPDIR)/ffi.Plo src/csky/$(DEPDIR)/sysv.Plo \
	src/frv/$(DEPDIR)/eabi.Plo src/frv/$(DEPDIR)/ffi.Plo \
	src/ia64/$(DEPDIR)/ffi.Plo src/ia64/$(DEPDIR)/unix.Plo \
	src/kvx/$(DEPDIR)/ffi.Plo src/kvx/$(DEPDIR)/sysv.Plo \
	src/loongarch64/$(DEPDIR)/ffi.Plo \
	src/loongarch64/$(DEPDIR)/sysv.Plo src/m32r/$(DEPDIR)/ffi.Plo \
	src/m32r/$(DEPDIR)/sysv.Plo src/m68k/$(DEPDIR)/ffi.Plo \
	src/m68k/$(DEPDIR)/sysv.Plo src/m88k/$(DEPDIR)/ffi.Plo \
	src/m88k/$(DEPDIR)/obsd.Plo src/metag/$(DEPDIR)/ffi.Plo \
	src/metag/$(DEPDIR)/sysv.Plo src/microblaze/$(DEPDIR)/ffi.Plo \
	src/microblaze/$(DEPDIR)/sysv.Plo src/mips/$(DEPDIR)/ffi.Plo \
	src/mips/$(DEPDIR)/n32.Plo src/mips/$(DEPDIR)/o32.Plo \
	src/moxie/$(DEPDIR)/eabi.Plo src/moxie/$(DEPDIR)/ffi.Plo \
	src/or1k/$(DEPDIR)/ffi.Plo src/or1k/$(DEPDIR)/sysv.Plo \
	src/pa/$(DEPDIR)/ffi.Plo src/pa/$(DEPDIR)/hpux32.Plo \
	src/pa/$(DEPDIR)/hpux64.Plo src/pa/$(DEPDIR)/linux.Plo \
	src/powerpc/$(DEPDIR)/aix.Plo \
	src/powerpc/$(DEPDIR)/aix_closure.Plo \
	src/powerpc/$(DEPDIR)/darwin.Plo \
	src/powerpc/$(DEPDIR)/darwin_closure.Plo \
	src/powerpc/$(DEPDIR)/ffi.Plo \
	src/powerpc/$(DEPDIR)/ffi_darwin.Plo \
	src/powerpc/$(DEPDIR)/ffi_linux64.Plo \
	src/powerpc/$(DEPDIR)/ffi_sysv.Plo \
	src/powerpc/$(DEPDIR)/linux64.Plo \
	src/powerpc/$(DEPDIR)/linux64_closure.Plo \
	src/powerpc/$(DEPDIR)/ppc_closure.Plo \
	src/powerpc/$(DEPDIR)/sysv.Plo src/riscv/$(DEPDIR)/ffi.Plo \
	src/riscv/$(DEPDIR)/sysv.Plo src/s390/$(DEPDIR)/ffi.Plo \
	src/s390/$(DEPDIR)/sysv.Plo src/sh/$(DEPDIR)/ffi.Plo \
	src/sh/$(DEPDIR)/sysv.Plo src/sh64/$(DEPDIR)/ffi.Plo \
	src/sh64/$(DEPDIR)/sysv.Plo src/sparc/$(DEPDIR)/ffi.Plo \
	src/sparc/$(DEPDIR)/ffi64.Plo src/sparc/$(DEPDIR)/v8.Plo \
	src/sparc/$(DEPDIR)/v9.Plo src/tile/$(DEPDIR)/ffi.Plo \
	src/tile/$(DEPDIR)/tile.Plo src/vax/$(DEPDIR)/elfbsd.Plo \
	src/vax/$(DEPDIR)/ffi.Plo src/wasm32/$(DEPDIR)/ffi.Plo \
	src/x86/$(DEPDIR)/ffi.Plo src/x86/$(DEPDIR)/ffi64.Plo \
	src/x86/$(DEPDIR)/ffiw64.Plo src/x86/$(DEPDIR)/sysv.Plo \
	src/x86/$(DEPDIR)/sysv_intel.Plo src/x86/$(DEPDIR)/unix64.Plo \
	src/x86/$(DEPDIR)/win64.Plo src/x86/$(DEPDIR)/win64_intel.Plo \
	src/xtensa/$(DEPDIR)/ffi.Plo src/xtensa/$(DEPDIR)/sysv.Plo
am__mv = mv -f
CPPASCOMPILE = $(CCAS) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CCASFLAGS) $(CCASFLAGS)
LTCPPASCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CCAS) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CCASFLAGS) $(CCASFLAGS)
AM_V_CPPAS = $(am__v_CPPAS_@AM_V@)
am__v_CPPAS_ = $(am__v_CPPAS_@AM_DEFAULT_V@)
am__v_CPPAS_0 = @echo "  CPPAS   " $@;
am__v_CPPAS_1 = 
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(libffi_la_SOURCES) $(EXTRA_libffi_la_SOURCES) \
	$(libffi_convenience_la_SOURCES) \
	$(EXTRA_libffi_convenience_la_SOURCES) \
	$(nodist_libffi_convenience_la_SOURCES)
DIST_SOURCES = $(am__libffi_la_SOURCES_DIST) \
	$(EXTRA_libffi_la_SOURCES) \
	$(am__libffi_convenience_la_SOURCES_DIST) \
	$(EXTRA_libffi_convenience_la_SOURCES)
RECURSIVE_TARGETS = all-recursive check-recursive cscopelist-recursive \
	ctags-recursive dvi-recursive html-recursive info-recursive \
	install-data-recursive install-dvi-recursive \
	install-exec-recursive install-html-recursive \
	install-info-recursive install-pdf-recursive \
	install-ps-recursive install-recursive installcheck-recursive \
	installdirs-recursive pdf-recursive ps-recursive \
	tags-recursive uninstall-recursive
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
DATA = $(pkgconfig_DATA)
HEADERS = $(noinst_HEADERS)
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
am__recursive_targets = \
  $(RECURSIVE_TARGETS) \
  $(RECURSIVE_CLEAN_TARGETS) \
  $(am__extra_recursive_targets)
AM_RECURSIVE_TARGETS = $(am__recursive_targets:-recursive=) TAGS CTAGS \
	cscope distdir distdir-am dist dist-all distcheck
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP) \
	fficonfig.h.in
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
DIST_SUBDIRS = include testsuite man doc
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/fficonfig.h.in \
	$(srcdir)/libffi.pc.in README.md compile config.guess \
	config.sub depcomp install-sh ltmain.sh missing
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
distdir = $(PACKAGE)-$(VERSION)
top_distdir = $(distdir)
am__remove_distdir = \
  if test -d "$(distdir)"; then \
    find "$(distdir)" -type d ! -perm -200 -exec chmod u+w {} ';' \
      && rm -rf "$(distdir)" \
      || { sleep 5 && rm -rf "$(distdir)"; }; \
  else :; fi
am__post_remove_distdir = $(am__remove_distdir)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
DIST_ARCHIVES = $(distdir).tar.gz
GZIP_ENV = --best
DIST_TARGETS = dist-gzip
# Exists only to be overridden by the user if desired.
AM_DISTCHECK_DVI_TARGET = dvi
distuninstallcheck_listfiles = find . -type f -print
am__distuninstallcheck_listfiles = $(distuninstallcheck_listfiles) \
  | sed 's|^\./|$(prefix)/|' | grep -v '$(infodir)/dir$$'
distcleancheck_listfiles = find . -type f -print
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AM_LTLDFLAGS = @AM_LTLDFLAGS@
AM_RUNTESTFLAGS = @AM_RUNTESTFLAGS@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCAS = @CCAS@
CCASDEPMODE = @CCASDEPMODE@
CCASFLAGS = @CCASFLAGS@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPPFLAGS = @CPPFLAGS@
CSCOPE = @CSCOPE@
CTAGS = @CTAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
ETAGS = @ETAGS@
EXEEXT = @EXEEXT@
FFI_EXEC_TRAMPOLINE_TABLE = @FFI_EXEC_TRAMPOLINE_TABLE@
FGREP = @FGREP@
FILECMD = @FILECMD@
GREP = @GREP@
HAVE_LONG_DOUBLE = @HAVE_LONG_DOUBLE@
HAVE_LONG_DOUBLE_VARIANT = @HAVE_LONG_DOUBLE_VARIANT@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAINT = @MAINT@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OPT_LDFLAGS = @OPT_LDFLAGS@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PRTDIAG = @PRTDIAG@
RANLIB = @RANLIB@
READELF = @READELF@
SECTION_LDFLAGS = @SECTION_LDFLAGS@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
TARGET = @TARGET@
TARGETDIR = @TARGETDIR@
TARGET_OBJ = @TARGET_OBJ@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
ac_ct_READELF = @ac_ct_READELF@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
ax_enable_builddir_sed = @ax_enable_builddir_sed@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
runstatedir = @runstatedir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sys_symbol_underscore = @sys_symbol_underscore@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
tmake_file = @tmake_file@
toolexecdir = @toolexecdir@
toolexeclibdir = @toolexeclibdir@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
AUTOMAKE_OPTIONS = foreign subdir-objects
ACLOCAL_AMFLAGS = -I m4
SUBDIRS = include testsuite man $(am__append_1)
EXTRA_DIST = LICENSE ChangeLog.old					\
	m4/libtool.m4 m4/lt~obsolete.m4					\
	 m4/ltoptions.m4 m4/ltsugar.m4 m4/ltversion.m4			\
	 m4/ltversion.m4 src/debug.c msvcc.sh				\
	generate-darwin-source-and-headers.py				\
	libffi.xcodeproj/project.pbxproj				\
        src/powerpc/t-aix                                               \
	libtool-ldflags libtool-version configure.host README.md        \
	libffi.map.in LICENSE-BUILDTOOLS msvc_build make_sunver.pl


# local.exp is generated by configure
DISTCLEANFILES = local.exp

# Subdir rules rely on $(FLAGS_TO_PASS)
FLAGS_TO_PASS = $(AM_MAKEFLAGS)
MAKEOVERRIDES = 
pkgconfigdir = $(libdir)/pkgconfig
pkgconfig_DATA = libffi.pc
toolexeclib_LTLIBRARIES = libffi.la
noinst_LTLIBRARIES = libffi_convenience.la
libffi_la_SOURCES = src/prep_cif.c src/types.c src/raw_api.c \
	src/java_raw_api.c src/closures.c src/tramp.c $(am__append_2)
noinst_HEADERS = src/aarch64/ffitarget.h src/aarch64/internal.h		\
	src/alpha/ffitarget.h src/alpha/internal.h			\
	src/arc/ffitarget.h src/arm/ffitarget.h src/arm/internal.h	\
	src/avr32/ffitarget.h src/bfin/ffitarget.h			\
	src/cris/ffitarget.h src/csky/ffitarget.h src/frv/ffitarget.h	\
	src/ia64/ffitarget.h src/ia64/ia64_flags.h			\
	src/m32r/ffitarget.h src/m68k/ffitarget.h			\
	src/m88k/ffitarget.h src/metag/ffitarget.h			\
	src/microblaze/ffitarget.h src/mips/ffitarget.h			\
	src/moxie/ffitarget.h \
	src/or1k/ffitarget.h src/pa/ffitarget.h				\
	src/powerpc/ffitarget.h src/powerpc/asm.h			\
	src/powerpc/ffi_powerpc.h src/powerpc/internal.h		\
	src/riscv/ffitarget.h						\
	src/s390/ffitarget.h src/s390/internal.h src/sh/ffitarget.h	\
	src/sh64/ffitarget.h src/sparc/ffitarget.h			\
	src/sparc/internal.h src/tile/ffitarget.h src/vax/ffitarget.h	\
	src/wasm32/ffitarget.h \
	src/x86/ffitarget.h src/x86/internal.h src/x86/internal64.h	\
	src/x86/asmnames.h src/xtensa/ffitarget.h src/dlmalloc.c	\
	src/kvx/ffitarget.h src/kvx/asm.h				\
	src/loongarch64/ffitarget.h

EXTRA_libffi_la_SOURCES = src/aarch64/ffi.c src/aarch64/sysv.S		\
	src/aarch64/win64_armasm.S src/alpha/ffi.c src/alpha/osf.S	\
	src/arc/ffi.c src/arc/arcompact.S src/arm/ffi.c			\
	src/arm/sysv.S src/arm/ffi.c src/arm/sysv_msvc_arm32.S		\
	src/avr32/ffi.c src/avr32/sysv.S src/bfin/ffi.c			\
	src/bfin/sysv.S src/cris/ffi.c src/cris/sysv.S src/frv/ffi.c	\
	src/csky/ffi.c src/csky/sysv.S src/frv/eabi.S src/ia64/ffi.c	\
	src/ia64/unix.S src/m32r/ffi.c src/m32r/sysv.S src/m68k/ffi.c	\
	src/m68k/sysv.S src/m88k/ffi.c src/m88k/obsd.S			\
	src/metag/ffi.c src/metag/sysv.S src/microblaze/ffi.c		\
	src/microblaze/sysv.S src/mips/ffi.c src/mips/o32.S		\
	src/mips/n32.S src/moxie/ffi.c src/moxie/eabi.S			\
	src/or1k/ffi.c			\
	src/or1k/sysv.S src/pa/ffi.c src/pa/linux.S src/pa/hpux32.S	\
	src/pa/hpux64.S src/powerpc/ffi.c src/powerpc/ffi_sysv.c	\
	src/powerpc/ffi_linux64.c src/powerpc/sysv.S			\
	src/powerpc/linux64.S src/powerpc/linux64_closure.S		\
	src/powerpc/ppc_closure.S src/powerpc/aix.S			\
	src/powerpc/darwin.S src/powerpc/aix_closure.S			\
	src/powerpc/darwin_closure.S src/powerpc/ffi_darwin.c		\
	src/riscv/ffi.c src/riscv/sysv.S src/s390/ffi.c			\
	src/s390/sysv.S src/sh/ffi.c src/sh/sysv.S src/sh64/ffi.c	\
	src/sh64/sysv.S src/sparc/ffi.c src/sparc/ffi64.c		\
	src/sparc/v8.S src/sparc/v9.S src/tile/ffi.c src/tile/tile.S	\
	src/vax/ffi.c src/vax/elfbsd.S src/x86/ffi.c src/x86/sysv.S	\
	src/wasm32/ffi.c \
	src/x86/ffiw64.c src/x86/win64.S src/x86/ffi64.c		\
	src/x86/unix64.S src/x86/sysv_intel.S src/x86/win64_intel.S	\
	src/xtensa/ffi.c src/xtensa/sysv.S src/kvx/ffi.c		\
	src/kvx/sysv.S src/loongarch64/ffi.c src/loongarch64/sysv.S

libffi_la_LIBADD = $(TARGET_OBJ)
libffi_convenience_la_SOURCES = $(libffi_la_SOURCES)
EXTRA_libffi_convenience_la_SOURCES = $(EXTRA_libffi_la_SOURCES)
libffi_convenience_la_LIBADD = $(libffi_la_LIBADD)
libffi_convenience_la_DEPENDENCIES = $(libffi_la_DEPENDENCIES)
nodist_libffi_convenience_la_SOURCES = $(nodist_libffi_la_SOURCES)
LTLDFLAGS = $(shell $(SHELL) $(top_srcdir)/libtool-ldflags $(LDFLAGS))
AM_CFLAGS = $(am__append_3)
@LIBFFI_BUILD_VERSIONED_SHLIB_FALSE@libffi_version_script = 
@LIBFFI_BUILD_VERSIONED_SHLIB_GNU_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@libffi_version_script = -Wl,--version-script,libffi.map
@LIBFFI_BUILD_VERSIONED_SHLIB_SUN_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@libffi_version_script = -Wl,-M,libffi.map-sun
@LIBFFI_BUILD_VERSIONED_SHLIB_FALSE@libffi_version_dep = 
@LIBFFI_BUILD_VERSIONED_SHLIB_GNU_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@libffi_version_dep = libffi.map
@LIBFFI_BUILD_VERSIONED_SHLIB_SUN_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@libffi_version_dep = libffi.map-sun
libffi_version_info = -version-info `grep -v '^\#' $(srcdir)/libtool-version`
libffi_la_LDFLAGS = -no-undefined $(libffi_version_info) $(libffi_version_script) $(LTLDFLAGS) $(AM_LTLDFLAGS)
libffi_la_DEPENDENCIES = $(libffi_la_LIBADD) $(libffi_version_dep)
AM_CPPFLAGS = -I. -I$(top_srcdir)/include -Iinclude -I$(top_srcdir)/src
AM_CCASFLAGS = $(AM_CPPFLAGS)
all: fficonfig.h
	$(MAKE) $(AM_MAKEFLAGS) all-recursive

.SUFFIXES:
.SUFFIXES: .S .c .lo .o .obj
am--refresh: Makefile
	@:
$(srcdir)/Makefile.in: @MAINTAINER_MODE_TRUE@ $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      echo ' cd $(srcdir) && $(AUTOMAKE) --foreign'; \
	      $(am__cd) $(srcdir) && $(AUTOMAKE) --foreign \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --foreign Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --foreign Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    echo ' $(SHELL) ./config.status'; \
	    $(SHELL) ./config.status;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	$(SHELL) ./config.status --recheck

$(top_srcdir)/configure: @MAINTAINER_MODE_TRUE@ $(am__configure_deps)
	$(am__cd) $(srcdir) && $(AUTOCONF)
$(ACLOCAL_M4): @MAINTAINER_MODE_TRUE@ $(am__aclocal_m4_deps)
	$(am__cd) $(srcdir) && $(ACLOCAL) $(ACLOCAL_AMFLAGS)
$(am__aclocal_m4_deps):

fficonfig.h: stamp-h1
	@test -f $@ || rm -f stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) stamp-h1

stamp-h1: $(srcdir)/fficonfig.h.in $(top_builddir)/config.status
	@rm -f stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status fficonfig.h
$(srcdir)/fficonfig.h.in: @MAINTAINER_MODE_TRUE@ $(am__configure_deps) 
	($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	rm -f stamp-h1
	touch $@

distclean-hdr:
	-rm -f fficonfig.h stamp-h1
libffi.pc: $(top_builddir)/config.status $(srcdir)/libffi.pc.in
	cd $(top_builddir) && $(SHELL) ./config.status $@

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}

install-toolexeclibLTLIBRARIES: $(toolexeclib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(toolexeclib_LTLIBRARIES)'; test -n "$(toolexeclibdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(toolexeclibdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(toolexeclibdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(toolexeclibdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(toolexeclibdir)"; \
	}

uninstall-toolexeclibLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(toolexeclib_LTLIBRARIES)'; test -n "$(toolexeclibdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(toolexeclibdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(toolexeclibdir)/$$f"; \
	done

clean-toolexeclibLTLIBRARIES:
	-test -z "$(toolexeclib_LTLIBRARIES)" || rm -f $(toolexeclib_LTLIBRARIES)
	@list='$(toolexeclib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
src/$(am__dirstamp):
	@$(MKDIR_P) src
	@: > src/$(am__dirstamp)
src/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/$(DEPDIR)
	@: > src/$(DEPDIR)/$(am__dirstamp)
src/prep_cif.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/types.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/raw_api.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/java_raw_api.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/closures.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/tramp.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/debug.lo: src/$(am__dirstamp) src/$(DEPDIR)/$(am__dirstamp)
src/aarch64/$(am__dirstamp):
	@$(MKDIR_P) src/aarch64
	@: > src/aarch64/$(am__dirstamp)
src/aarch64/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/aarch64/$(DEPDIR)
	@: > src/aarch64/$(DEPDIR)/$(am__dirstamp)
src/aarch64/ffi.lo: src/aarch64/$(am__dirstamp) \
	src/aarch64/$(DEPDIR)/$(am__dirstamp)
src/aarch64/sysv.lo: src/aarch64/$(am__dirstamp) \
	src/aarch64/$(DEPDIR)/$(am__dirstamp)
src/aarch64/win64_armasm.lo: src/aarch64/$(am__dirstamp) \
	src/aarch64/$(DEPDIR)/$(am__dirstamp)
src/alpha/$(am__dirstamp):
	@$(MKDIR_P) src/alpha
	@: > src/alpha/$(am__dirstamp)
src/alpha/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/alpha/$(DEPDIR)
	@: > src/alpha/$(DEPDIR)/$(am__dirstamp)
src/alpha/ffi.lo: src/alpha/$(am__dirstamp) \
	src/alpha/$(DEPDIR)/$(am__dirstamp)
src/alpha/osf.lo: src/alpha/$(am__dirstamp) \
	src/alpha/$(DEPDIR)/$(am__dirstamp)
src/arc/$(am__dirstamp):
	@$(MKDIR_P) src/arc
	@: > src/arc/$(am__dirstamp)
src/arc/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/arc/$(DEPDIR)
	@: > src/arc/$(DEPDIR)/$(am__dirstamp)
src/arc/ffi.lo: src/arc/$(am__dirstamp) \
	src/arc/$(DEPDIR)/$(am__dirstamp)
src/arc/arcompact.lo: src/arc/$(am__dirstamp) \
	src/arc/$(DEPDIR)/$(am__dirstamp)
src/arm/$(am__dirstamp):
	@$(MKDIR_P) src/arm
	@: > src/arm/$(am__dirstamp)
src/arm/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/arm/$(DEPDIR)
	@: > src/arm/$(DEPDIR)/$(am__dirstamp)
src/arm/ffi.lo: src/arm/$(am__dirstamp) \
	src/arm/$(DEPDIR)/$(am__dirstamp)
src/arm/sysv.lo: src/arm/$(am__dirstamp) \
	src/arm/$(DEPDIR)/$(am__dirstamp)
src/arm/sysv_msvc_arm32.lo: src/arm/$(am__dirstamp) \
	src/arm/$(DEPDIR)/$(am__dirstamp)
src/avr32/$(am__dirstamp):
	@$(MKDIR_P) src/avr32
	@: > src/avr32/$(am__dirstamp)
src/avr32/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/avr32/$(DEPDIR)
	@: > src/avr32/$(DEPDIR)/$(am__dirstamp)
src/avr32/ffi.lo: src/avr32/$(am__dirstamp) \
	src/avr32/$(DEPDIR)/$(am__dirstamp)
src/avr32/sysv.lo: src/avr32/$(am__dirstamp) \
	src/avr32/$(DEPDIR)/$(am__dirstamp)
src/bfin/$(am__dirstamp):
	@$(MKDIR_P) src/bfin
	@: > src/bfin/$(am__dirstamp)
src/bfin/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/bfin/$(DEPDIR)
	@: > src/bfin/$(DEPDIR)/$(am__dirstamp)
src/bfin/ffi.lo: src/bfin/$(am__dirstamp) \
	src/bfin/$(DEPDIR)/$(am__dirstamp)
src/bfin/sysv.lo: src/bfin/$(am__dirstamp) \
	src/bfin/$(DEPDIR)/$(am__dirstamp)
src/cris/$(am__dirstamp):
	@$(MKDIR_P) src/cris
	@: > src/cris/$(am__dirstamp)
src/cris/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/cris/$(DEPDIR)
	@: > src/cris/$(DEPDIR)/$(am__dirstamp)
src/cris/ffi.lo: src/cris/$(am__dirstamp) \
	src/cris/$(DEPDIR)/$(am__dirstamp)
src/cris/sysv.lo: src/cris/$(am__dirstamp) \
	src/cris/$(DEPDIR)/$(am__dirstamp)
src/frv/$(am__dirstamp):
	@$(MKDIR_P) src/frv
	@: > src/frv/$(am__dirstamp)
src/frv/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/frv/$(DEPDIR)
	@: > src/frv/$(DEPDIR)/$(am__dirstamp)
src/frv/ffi.lo: src/frv/$(am__dirstamp) \
	src/frv/$(DEPDIR)/$(am__dirstamp)
src/csky/$(am__dirstamp):
	@$(MKDIR_P) src/csky
	@: > src/csky/$(am__dirstamp)
src/csky/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/csky/$(DEPDIR)
	@: > src/csky/$(DEPDIR)/$(am__dirstamp)
src/csky/ffi.lo: src/csky/$(am__dirstamp) \
	src/csky/$(DEPDIR)/$(am__dirstamp)
src/csky/sysv.lo: src/csky/$(am__dirstamp) \
	src/csky/$(DEPDIR)/$(am__dirstamp)
src/frv/eabi.lo: src/frv/$(am__dirstamp) \
	src/frv/$(DEPDIR)/$(am__dirstamp)
src/ia64/$(am__dirstamp):
	@$(MKDIR_P) src/ia64
	@: > src/ia64/$(am__dirstamp)
src/ia64/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/ia64/$(DEPDIR)
	@: > src/ia64/$(DEPDIR)/$(am__dirstamp)
src/ia64/ffi.lo: src/ia64/$(am__dirstamp) \
	src/ia64/$(DEPDIR)/$(am__dirstamp)
src/ia64/unix.lo: src/ia64/$(am__dirstamp) \
	src/ia64/$(DEPDIR)/$(am__dirstamp)
src/m32r/$(am__dirstamp):
	@$(MKDIR_P) src/m32r
	@: > src/m32r/$(am__dirstamp)
src/m32r/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/m32r/$(DEPDIR)
	@: > src/m32r/$(DEPDIR)/$(am__dirstamp)
src/m32r/ffi.lo: src/m32r/$(am__dirstamp) \
	src/m32r/$(DEPDIR)/$(am__dirstamp)
src/m32r/sysv.lo: src/m32r/$(am__dirstamp) \
	src/m32r/$(DEPDIR)/$(am__dirstamp)
src/m68k/$(am__dirstamp):
	@$(MKDIR_P) src/m68k
	@: > src/m68k/$(am__dirstamp)
src/m68k/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/m68k/$(DEPDIR)
	@: > src/m68k/$(DEPDIR)/$(am__dirstamp)
src/m68k/ffi.lo: src/m68k/$(am__dirstamp) \
	src/m68k/$(DEPDIR)/$(am__dirstamp)
src/m68k/sysv.lo: src/m68k/$(am__dirstamp) \
	src/m68k/$(DEPDIR)/$(am__dirstamp)
src/m88k/$(am__dirstamp):
	@$(MKDIR_P) src/m88k
	@: > src/m88k/$(am__dirstamp)
src/m88k/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/m88k/$(DEPDIR)
	@: > src/m88k/$(DEPDIR)/$(am__dirstamp)
src/m88k/ffi.lo: src/m88k/$(am__dirstamp) \
	src/m88k/$(DEPDIR)/$(am__dirstamp)
src/m88k/obsd.lo: src/m88k/$(am__dirstamp) \
	src/m88k/$(DEPDIR)/$(am__dirstamp)
src/metag/$(am__dirstamp):
	@$(MKDIR_P) src/metag
	@: > src/metag/$(am__dirstamp)
src/metag/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/metag/$(DEPDIR)
	@: > src/metag/$(DEPDIR)/$(am__dirstamp)
src/metag/ffi.lo: src/metag/$(am__dirstamp) \
	src/metag/$(DEPDIR)/$(am__dirstamp)
src/metag/sysv.lo: src/metag/$(am__dirstamp) \
	src/metag/$(DEPDIR)/$(am__dirstamp)
src/microblaze/$(am__dirstamp):
	@$(MKDIR_P) src/microblaze
	@: > src/microblaze/$(am__dirstamp)
src/microblaze/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/microblaze/$(DEPDIR)
	@: > src/microblaze/$(DEPDIR)/$(am__dirstamp)
src/microblaze/ffi.lo: src/microblaze/$(am__dirstamp) \
	src/microblaze/$(DEPDIR)/$(am__dirstamp)
src/microblaze/sysv.lo: src/microblaze/$(am__dirstamp) \
	src/microblaze/$(DEPDIR)/$(am__dirstamp)
src/mips/$(am__dirstamp):
	@$(MKDIR_P) src/mips
	@: > src/mips/$(am__dirstamp)
src/mips/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/mips/$(DEPDIR)
	@: > src/mips/$(DEPDIR)/$(am__dirstamp)
src/mips/ffi.lo: src/mips/$(am__dirstamp) \
	src/mips/$(DEPDIR)/$(am__dirstamp)
src/mips/o32.lo: src/mips/$(am__dirstamp) \
	src/mips/$(DEPDIR)/$(am__dirstamp)
src/mips/n32.lo: src/mips/$(am__dirstamp) \
	src/mips/$(DEPDIR)/$(am__dirstamp)
src/moxie/$(am__dirstamp):
	@$(MKDIR_P) src/moxie
	@: > src/moxie/$(am__dirstamp)
src/moxie/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/moxie/$(DEPDIR)
	@: > src/moxie/$(DEPDIR)/$(am__dirstamp)
src/moxie/ffi.lo: src/moxie/$(am__dirstamp) \
	src/moxie/$(DEPDIR)/$(am__dirstamp)
src/moxie/eabi.lo: src/moxie/$(am__dirstamp) \
	src/moxie/$(DEPDIR)/$(am__dirstamp)
src/or1k/$(am__dirstamp):
	@$(MKDIR_P) src/or1k
	@: > src/or1k/$(am__dirstamp)
src/or1k/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/or1k/$(DEPDIR)
	@: > src/or1k/$(DEPDIR)/$(am__dirstamp)
src/or1k/ffi.lo: src/or1k/$(am__dirstamp) \
	src/or1k/$(DEPDIR)/$(am__dirstamp)
src/or1k/sysv.lo: src/or1k/$(am__dirstamp) \
	src/or1k/$(DEPDIR)/$(am__dirstamp)
src/pa/$(am__dirstamp):
	@$(MKDIR_P) src/pa
	@: > src/pa/$(am__dirstamp)
src/pa/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/pa/$(DEPDIR)
	@: > src/pa/$(DEPDIR)/$(am__dirstamp)
src/pa/ffi.lo: src/pa/$(am__dirstamp) src/pa/$(DEPDIR)/$(am__dirstamp)
src/pa/linux.lo: src/pa/$(am__dirstamp) \
	src/pa/$(DEPDIR)/$(am__dirstamp)
src/pa/hpux32.lo: src/pa/$(am__dirstamp) \
	src/pa/$(DEPDIR)/$(am__dirstamp)
src/pa/hpux64.lo: src/pa/$(am__dirstamp) \
	src/pa/$(DEPDIR)/$(am__dirstamp)
src/powerpc/$(am__dirstamp):
	@$(MKDIR_P) src/powerpc
	@: > src/powerpc/$(am__dirstamp)
src/powerpc/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/powerpc/$(DEPDIR)
	@: > src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/ffi.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/ffi_sysv.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/ffi_linux64.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/sysv.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/linux64.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/linux64_closure.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/ppc_closure.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/aix.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/darwin.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/aix_closure.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/darwin_closure.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/powerpc/ffi_darwin.lo: src/powerpc/$(am__dirstamp) \
	src/powerpc/$(DEPDIR)/$(am__dirstamp)
src/riscv/$(am__dirstamp):
	@$(MKDIR_P) src/riscv
	@: > src/riscv/$(am__dirstamp)
src/riscv/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/riscv/$(DEPDIR)
	@: > src/riscv/$(DEPDIR)/$(am__dirstamp)
src/riscv/ffi.lo: src/riscv/$(am__dirstamp) \
	src/riscv/$(DEPDIR)/$(am__dirstamp)
src/riscv/sysv.lo: src/riscv/$(am__dirstamp) \
	src/riscv/$(DEPDIR)/$(am__dirstamp)
src/s390/$(am__dirstamp):
	@$(MKDIR_P) src/s390
	@: > src/s390/$(am__dirstamp)
src/s390/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/s390/$(DEPDIR)
	@: > src/s390/$(DEPDIR)/$(am__dirstamp)
src/s390/ffi.lo: src/s390/$(am__dirstamp) \
	src/s390/$(DEPDIR)/$(am__dirstamp)
src/s390/sysv.lo: src/s390/$(am__dirstamp) \
	src/s390/$(DEPDIR)/$(am__dirstamp)
src/sh/$(am__dirstamp):
	@$(MKDIR_P) src/sh
	@: > src/sh/$(am__dirstamp)
src/sh/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/sh/$(DEPDIR)
	@: > src/sh/$(DEPDIR)/$(am__dirstamp)
src/sh/ffi.lo: src/sh/$(am__dirstamp) src/sh/$(DEPDIR)/$(am__dirstamp)
src/sh/sysv.lo: src/sh/$(am__dirstamp) \
	src/sh/$(DEPDIR)/$(am__dirstamp)
src/sh64/$(am__dirstamp):
	@$(MKDIR_P) src/sh64
	@: > src/sh64/$(am__dirstamp)
src/sh64/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/sh64/$(DEPDIR)
	@: > src/sh64/$(DEPDIR)/$(am__dirstamp)
src/sh64/ffi.lo: src/sh64/$(am__dirstamp) \
	src/sh64/$(DEPDIR)/$(am__dirstamp)
src/sh64/sysv.lo: src/sh64/$(am__dirstamp) \
	src/sh64/$(DEPDIR)/$(am__dirstamp)
src/sparc/$(am__dirstamp):
	@$(MKDIR_P) src/sparc
	@: > src/sparc/$(am__dirstamp)
src/sparc/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/sparc/$(DEPDIR)
	@: > src/sparc/$(DEPDIR)/$(am__dirstamp)
src/sparc/ffi.lo: src/sparc/$(am__dirstamp) \
	src/sparc/$(DEPDIR)/$(am__dirstamp)
src/sparc/ffi64.lo: src/sparc/$(am__dirstamp) \
	src/sparc/$(DEPDIR)/$(am__dirstamp)
src/sparc/v8.lo: src/sparc/$(am__dirstamp) \
	src/sparc/$(DEPDIR)/$(am__dirstamp)
src/sparc/v9.lo: src/sparc/$(am__dirstamp) \
	src/sparc/$(DEPDIR)/$(am__dirstamp)
src/tile/$(am__dirstamp):
	@$(MKDIR_P) src/tile
	@: > src/tile/$(am__dirstamp)
src/tile/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/tile/$(DEPDIR)
	@: > src/tile/$(DEPDIR)/$(am__dirstamp)
src/tile/ffi.lo: src/tile/$(am__dirstamp) \
	src/tile/$(DEPDIR)/$(am__dirstamp)
src/tile/tile.lo: src/tile/$(am__dirstamp) \
	src/tile/$(DEPDIR)/$(am__dirstamp)
src/vax/$(am__dirstamp):
	@$(MKDIR_P) src/vax
	@: > src/vax/$(am__dirstamp)
src/vax/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/vax/$(DEPDIR)
	@: > src/vax/$(DEPDIR)/$(am__dirstamp)
src/vax/ffi.lo: src/vax/$(am__dirstamp) \
	src/vax/$(DEPDIR)/$(am__dirstamp)
src/vax/elfbsd.lo: src/vax/$(am__dirstamp) \
	src/vax/$(DEPDIR)/$(am__dirstamp)
src/x86/$(am__dirstamp):
	@$(MKDIR_P) src/x86
	@: > src/x86/$(am__dirstamp)
src/x86/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/x86/$(DEPDIR)
	@: > src/x86/$(DEPDIR)/$(am__dirstamp)
src/x86/ffi.lo: src/x86/$(am__dirstamp) \
	src/x86/$(DEPDIR)/$(am__dirstamp)
src/x86/sysv.lo: src/x86/$(am__dirstamp) \
	src/x86/$(DEPDIR)/$(am__dirstamp)
src/wasm32/$(am__dirstamp):
	@$(MKDIR_P) src/wasm32
	@: > src/wasm32/$(am__dirstamp)
src/wasm32/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/wasm32/$(DEPDIR)
	@: > src/wasm32/$(DEPDIR)/$(am__dirstamp)
src/wasm32/ffi.lo: src/wasm32/$(am__dirstamp) \
	src/wasm32/$(DEPDIR)/$(am__dirstamp)
src/x86/ffiw64.lo: src/x86/$(am__dirstamp) \
	src/x86/$(DEPDIR)/$(am__dirstamp)
src/x86/win64.lo: src/x86/$(am__dirstamp) \
	src/x86/$(DEPDIR)/$(am__dirstamp)
src/x86/ffi64.lo: src/x86/$(am__dirstamp) \
	src/x86/$(DEPDIR)/$(am__dirstamp)
src/x86/unix64.lo: src/x86/$(am__dirstamp) \
	src/x86/$(DEPDIR)/$(am__dirstamp)
src/x86/sysv_intel.lo: src/x86/$(am__dirstamp) \
	src/x86/$(DEPDIR)/$(am__dirstamp)
src/x86/win64_intel.lo: src/x86/$(am__dirstamp) \
	src/x86/$(DEPDIR)/$(am__dirstamp)
src/xtensa/$(am__dirstamp):
	@$(MKDIR_P) src/xtensa
	@: > src/xtensa/$(am__dirstamp)
src/xtensa/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/xtensa/$(DEPDIR)
	@: > src/xtensa/$(DEPDIR)/$(am__dirstamp)
src/xtensa/ffi.lo: src/xtensa/$(am__dirstamp) \
	src/xtensa/$(DEPDIR)/$(am__dirstamp)
src/xtensa/sysv.lo: src/xtensa/$(am__dirstamp) \
	src/xtensa/$(DEPDIR)/$(am__dirstamp)
src/kvx/$(am__dirstamp):
	@$(MKDIR_P) src/kvx
	@: > src/kvx/$(am__dirstamp)
src/kvx/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/kvx/$(DEPDIR)
	@: > src/kvx/$(DEPDIR)/$(am__dirstamp)
src/kvx/ffi.lo: src/kvx/$(am__dirstamp) \
	src/kvx/$(DEPDIR)/$(am__dirstamp)
src/kvx/sysv.lo: src/kvx/$(am__dirstamp) \
	src/kvx/$(DEPDIR)/$(am__dirstamp)
src/loongarch64/$(am__dirstamp):
	@$(MKDIR_P) src/loongarch64
	@: > src/loongarch64/$(am__dirstamp)
src/loongarch64/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) src/loongarch64/$(DEPDIR)
	@: > src/loongarch64/$(DEPDIR)/$(am__dirstamp)
src/loongarch64/ffi.lo: src/loongarch64/$(am__dirstamp) \
	src/loongarch64/$(DEPDIR)/$(am__dirstamp)
src/loongarch64/sysv.lo: src/loongarch64/$(am__dirstamp) \
	src/loongarch64/$(DEPDIR)/$(am__dirstamp)

libffi.la: $(libffi_la_OBJECTS) $(libffi_la_DEPENDENCIES) $(EXTRA_libffi_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(libffi_la_LINK) -rpath $(toolexeclibdir) $(libffi_la_OBJECTS) $(libffi_la_LIBADD) $(LIBS)

libffi_convenience.la: $(libffi_convenience_la_OBJECTS) $(libffi_convenience_la_DEPENDENCIES) $(EXTRA_libffi_convenience_la_DEPENDENCIES) 
	$(AM_V_CCLD)$(LINK)  $(libffi_convenience_la_OBJECTS) $(libffi_convenience_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f src/*.$(OBJEXT)
	-rm -f src/*.lo
	-rm -f src/aarch64/*.$(OBJEXT)
	-rm -f src/aarch64/*.lo
	-rm -f src/alpha/*.$(OBJEXT)
	-rm -f src/alpha/*.lo
	-rm -f src/arc/*.$(OBJEXT)
	-rm -f src/arc/*.lo
	-rm -f src/arm/*.$(OBJEXT)
	-rm -f src/arm/*.lo
	-rm -f src/avr32/*.$(OBJEXT)
	-rm -f src/avr32/*.lo
	-rm -f src/bfin/*.$(OBJEXT)
	-rm -f src/bfin/*.lo
	-rm -f src/cris/*.$(OBJEXT)
	-rm -f src/cris/*.lo
	-rm -f src/csky/*.$(OBJEXT)
	-rm -f src/csky/*.lo
	-rm -f src/frv/*.$(OBJEXT)
	-rm -f src/frv/*.lo
	-rm -f src/ia64/*.$(OBJEXT)
	-rm -f src/ia64/*.lo
	-rm -f src/kvx/*.$(OBJEXT)
	-rm -f src/kvx/*.lo
	-rm -f src/loongarch64/*.$(OBJEXT)
	-rm -f src/loongarch64/*.lo
	-rm -f src/m32r/*.$(OBJEXT)
	-rm -f src/m32r/*.lo
	-rm -f src/m68k/*.$(OBJEXT)
	-rm -f src/m68k/*.lo
	-rm -f src/m88k/*.$(OBJEXT)
	-rm -f src/m88k/*.lo
	-rm -f src/metag/*.$(OBJEXT)
	-rm -f src/metag/*.lo
	-rm -f src/microblaze/*.$(OBJEXT)
	-rm -f src/microblaze/*.lo
	-rm -f src/mips/*.$(OBJEXT)
	-rm -f src/mips/*.lo
	-rm -f src/moxie/*.$(OBJEXT)
	-rm -f src/moxie/*.lo
	-rm -f src/or1k/*.$(OBJEXT)
	-rm -f src/or1k/*.lo
	-rm -f src/pa/*.$(OBJEXT)
	-rm -f src/pa/*.lo
	-rm -f src/powerpc/*.$(OBJEXT)
	-rm -f src/powerpc/*.lo
	-rm -f src/riscv/*.$(OBJEXT)
	-rm -f src/riscv/*.lo
	-rm -f src/s390/*.$(OBJEXT)
	-rm -f src/s390/*.lo
	-rm -f src/sh/*.$(OBJEXT)
	-rm -f src/sh/*.lo
	-rm -f src/sh64/*.$(OBJEXT)
	-rm -f src/sh64/*.lo
	-rm -f src/sparc/*.$(OBJEXT)
	-rm -f src/sparc/*.lo
	-rm -f src/tile/*.$(OBJEXT)
	-rm -f src/tile/*.lo
	-rm -f src/vax/*.$(OBJEXT)
	-rm -f src/vax/*.lo
	-rm -f src/wasm32/*.$(OBJEXT)
	-rm -f src/wasm32/*.lo
	-rm -f src/x86/*.$(OBJEXT)
	-rm -f src/x86/*.lo
	-rm -f src/xtensa/*.$(OBJEXT)
	-rm -f src/xtensa/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/closures.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/debug.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/java_raw_api.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/prep_cif.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/raw_api.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/tramp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/$(DEPDIR)/types.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/aarch64/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/aarch64/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/aarch64/$(DEPDIR)/win64_armasm.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/alpha/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/alpha/$(DEPDIR)/osf.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/arc/$(DEPDIR)/arcompact.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/arc/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/arm/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/arm/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/arm/$(DEPDIR)/sysv_msvc_arm32.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/avr32/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/avr32/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/bfin/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/bfin/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/cris/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/cris/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/csky/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/csky/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/frv/$(DEPDIR)/eabi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/frv/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/ia64/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/ia64/$(DEPDIR)/unix.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/kvx/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/kvx/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/loongarch64/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/loongarch64/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/m32r/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/m32r/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/m68k/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/m68k/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/m88k/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/m88k/$(DEPDIR)/obsd.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/metag/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/metag/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/microblaze/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/microblaze/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/mips/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/mips/$(DEPDIR)/n32.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/mips/$(DEPDIR)/o32.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/moxie/$(DEPDIR)/eabi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/moxie/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/or1k/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/or1k/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/pa/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/pa/$(DEPDIR)/hpux32.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/pa/$(DEPDIR)/hpux64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/pa/$(DEPDIR)/linux.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/aix.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/aix_closure.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/darwin.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/darwin_closure.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/ffi_darwin.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/ffi_linux64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/ffi_sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/linux64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/linux64_closure.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/ppc_closure.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/powerpc/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/riscv/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/riscv/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/s390/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/s390/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/sh/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/sh/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/sh64/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/sh64/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/sparc/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/sparc/$(DEPDIR)/ffi64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/sparc/$(DEPDIR)/v8.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/sparc/$(DEPDIR)/v9.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/tile/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/tile/$(DEPDIR)/tile.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/vax/$(DEPDIR)/elfbsd.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/vax/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/wasm32/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/x86/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/x86/$(DEPDIR)/ffi64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/x86/$(DEPDIR)/ffiw64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/x86/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/x86/$(DEPDIR)/sysv_intel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/x86/$(DEPDIR)/unix64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/x86/$(DEPDIR)/win64.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/x86/$(DEPDIR)/win64_intel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/xtensa/$(DEPDIR)/ffi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@src/xtensa/$(DEPDIR)/sysv.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.S.o:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCCAS_TRUE@	$(CPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(CPPASCOMPILE) -c -o $@ $<

.S.obj:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCCAS_TRUE@	$(CPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(CPPASCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.S.lo:
@am__fastdepCCAS_TRUE@	$(AM_V_CPPAS)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCCAS_TRUE@	$(LTCPPASCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCCAS_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCCAS_FALSE@	DEPDIR=$(DEPDIR) $(CCASDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCCAS_FALSE@	$(AM_V_CPPAS@am__nodep@)$(LTCPPASCOMPILE) -c -o $@ $<

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf src/.libs src/_libs
	-rm -rf src/aarch64/.libs src/aarch64/_libs
	-rm -rf src/alpha/.libs src/alpha/_libs
	-rm -rf src/arc/.libs src/arc/_libs
	-rm -rf src/arm/.libs src/arm/_libs
	-rm -rf src/avr32/.libs src/avr32/_libs
	-rm -rf src/bfin/.libs src/bfin/_libs
	-rm -rf src/cris/.libs src/cris/_libs
	-rm -rf src/csky/.libs src/csky/_libs
	-rm -rf src/frv/.libs src/frv/_libs
	-rm -rf src/ia64/.libs src/ia64/_libs
	-rm -rf src/kvx/.libs src/kvx/_libs
	-rm -rf src/loongarch64/.libs src/loongarch64/_libs
	-rm -rf src/m32r/.libs src/m32r/_libs
	-rm -rf src/m68k/.libs src/m68k/_libs
	-rm -rf src/m88k/.libs src/m88k/_libs
	-rm -rf src/metag/.libs src/metag/_libs
	-rm -rf src/microblaze/.libs src/microblaze/_libs
	-rm -rf src/mips/.libs src/mips/_libs
	-rm -rf src/moxie/.libs src/moxie/_libs
	-rm -rf src/or1k/.libs src/or1k/_libs
	-rm -rf src/pa/.libs src/pa/_libs
	-rm -rf src/powerpc/.libs src/powerpc/_libs
	-rm -rf src/riscv/.libs src/riscv/_libs
	-rm -rf src/s390/.libs src/s390/_libs
	-rm -rf src/sh/.libs src/sh/_libs
	-rm -rf src/sh64/.libs src/sh64/_libs
	-rm -rf src/sparc/.libs src/sparc/_libs
	-rm -rf src/tile/.libs src/tile/_libs
	-rm -rf src/vax/.libs src/vax/_libs
	-rm -rf src/wasm32/.libs src/wasm32/_libs
	-rm -rf src/x86/.libs src/x86/_libs
	-rm -rf src/xtensa/.libs src/xtensa/_libs

distclean-libtool:
	-rm -f libtool config.lt
install-pkgconfigDATA: $(pkgconfig_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgconfigdir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgconfigdir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgconfigdir)" || exit $$?; \
	done

uninstall-pkgconfigDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgconfig_DATA)'; test -n "$(pkgconfigdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgconfigdir)'; $(am__uninstall_files_from_dir)

# This directory's subdirectories are mostly independent; you can cd
# into them and run 'make' without going through this Makefile.
# To change the values of 'make' variables: instead of editing Makefiles,
# (1) if the variable is set in 'config.status', edit 'config.status'
#     (which will cause the Makefiles to be regenerated when you run 'make');
# (2) otherwise, pass the desired values on the 'make' command line.
$(am__recursive_targets):
	@fail=; \
	if $(am__make_keepgoing); then \
	  failcom='fail=yes'; \
	else \
	  failcom='exit 1'; \
	fi; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-recursive
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-recursive

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscope: cscope.files
	test ! -s cscope.files \
	  || $(CSCOPE) -b -q $(AM_CSCOPEFLAGS) $(CSCOPEFLAGS) -i cscope.files $(CSCOPE_ARGS)
clean-cscope:
	-rm -f cscope.files
cscope.files: clean-cscope cscopelist
cscopelist: cscopelist-recursive

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags
	-rm -f cscope.out cscope.in.out cscope.po.out cscope.files
distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	$(am__remove_distdir)
	test -d "$(distdir)" || mkdir "$(distdir)"
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    $(am__make_dryrun) \
	      || test -d "$(distdir)/$$subdir" \
	      || $(MKDIR_P) "$(distdir)/$$subdir" \
	      || exit 1; \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
	$(MAKE) $(AM_MAKEFLAGS) \
	  top_distdir="$(top_distdir)" distdir="$(distdir)" \
	  dist-hook
	-test -n "$(am__skip_mode_fix)" \
	|| find "$(distdir)" -type d ! -perm -755 \
		-exec chmod u+rwx,go+rx {} \; -o \
	  ! -type d ! -perm -444 -links 1 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -400 -exec chmod a+r {} \; -o \
	  ! -type d ! -perm -444 -exec $(install_sh) -c -m a+r {} {} \; \
	|| chmod -R a+r "$(distdir)"
dist-gzip: distdir
	tardir=$(distdir) && $(am__tar) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).tar.gz
	$(am__post_remove_distdir)

dist-bzip2: distdir
	tardir=$(distdir) && $(am__tar) | BZIP2=$${BZIP2--9} bzip2 -c >$(distdir).tar.bz2
	$(am__post_remove_distdir)

dist-lzip: distdir
	tardir=$(distdir) && $(am__tar) | lzip -c $${LZIP_OPT--9} >$(distdir).tar.lz
	$(am__post_remove_distdir)

dist-xz: distdir
	tardir=$(distdir) && $(am__tar) | XZ_OPT=$${XZ_OPT--e} xz -c >$(distdir).tar.xz
	$(am__post_remove_distdir)

dist-zstd: distdir
	tardir=$(distdir) && $(am__tar) | zstd -c $${ZSTD_CLEVEL-$${ZSTD_OPT--19}} >$(distdir).tar.zst
	$(am__post_remove_distdir)

dist-tarZ: distdir
	@echo WARNING: "Support for distribution archives compressed with" \
		       "legacy program 'compress' is deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	tardir=$(distdir) && $(am__tar) | compress -c >$(distdir).tar.Z
	$(am__post_remove_distdir)

dist-shar: distdir
	@echo WARNING: "Support for shar distribution archives is" \
	               "deprecated." >&2
	@echo WARNING: "It will be removed altogether in Automake 2.0" >&2
	shar $(distdir) | eval GZIP= gzip $(GZIP_ENV) -c >$(distdir).shar.gz
	$(am__post_remove_distdir)

dist-zip: distdir
	-rm -f $(distdir).zip
	zip -rq $(distdir).zip $(distdir)
	$(am__post_remove_distdir)

dist dist-all:
	$(MAKE) $(AM_MAKEFLAGS) $(DIST_TARGETS) am__post_remove_distdir='@:'
	$(am__post_remove_distdir)

# This target untars the dist file and tries a VPATH configuration.  Then
# it guarantees that the distribution is self-contained by making another
# tarfile.
distcheck: dist
	case '$(DIST_ARCHIVES)' in \
	*.tar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).tar.gz | $(am__untar) ;;\
	*.tar.bz2*) \
	  bzip2 -dc $(distdir).tar.bz2 | $(am__untar) ;;\
	*.tar.lz*) \
	  lzip -dc $(distdir).tar.lz | $(am__untar) ;;\
	*.tar.xz*) \
	  xz -dc $(distdir).tar.xz | $(am__untar) ;;\
	*.tar.Z*) \
	  uncompress -c $(distdir).tar.Z | $(am__untar) ;;\
	*.shar.gz*) \
	  eval GZIP= gzip $(GZIP_ENV) -dc $(distdir).shar.gz | unshar ;;\
	*.zip*) \
	  unzip $(distdir).zip ;;\
	*.tar.zst*) \
	  zstd -dc $(distdir).tar.zst | $(am__untar) ;;\
	esac
	chmod -R a-w $(distdir)
	chmod u+w $(distdir)
	mkdir $(distdir)/_build $(distdir)/_build/sub $(distdir)/_inst
	chmod a-w $(distdir)
	test -d $(distdir)/_build || exit 0; \
	dc_install_base=`$(am__cd) $(distdir)/_inst && pwd | sed -e 's,^[^:\\/]:[\\/],/,'` \
	  && dc_destdir="$${TMPDIR-/tmp}/am-dc-$$$$/" \
	  && am__cwd=`pwd` \
	  && $(am__cd) $(distdir)/_build/sub \
	  && ../../configure \
	    $(AM_DISTCHECK_CONFIGURE_FLAGS) \
	    $(DISTCHECK_CONFIGURE_FLAGS) \
	    --srcdir=../.. --prefix="$$dc_install_base" \
	  && $(MAKE) $(AM_MAKEFLAGS) \
	  && $(MAKE) $(AM_MAKEFLAGS) $(AM_DISTCHECK_DVI_TARGET) \
	  && $(MAKE) $(AM_MAKEFLAGS) check \
	  && $(MAKE) $(AM_MAKEFLAGS) install \
	  && $(MAKE) $(AM_MAKEFLAGS) installcheck \
	  && $(MAKE) $(AM_MAKEFLAGS) uninstall \
	  && $(MAKE) $(AM_MAKEFLAGS) distuninstallcheck_dir="$$dc_install_base" \
	        distuninstallcheck \
	  && chmod -R a-w "$$dc_install_base" \
	  && ({ \
	       (cd ../.. && umask 077 && mkdir "$$dc_destdir") \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" install \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" uninstall \
	       && $(MAKE) $(AM_MAKEFLAGS) DESTDIR="$$dc_destdir" \
	            distuninstallcheck_dir="$$dc_destdir" distuninstallcheck; \
	      } || { rm -rf "$$dc_destdir"; exit 1; }) \
	  && rm -rf "$$dc_destdir" \
	  && $(MAKE) $(AM_MAKEFLAGS) dist \
	  && rm -rf $(DIST_ARCHIVES) \
	  && $(MAKE) $(AM_MAKEFLAGS) distcleancheck \
	  && cd "$$am__cwd" \
	  || exit 1
	$(am__post_remove_distdir)
	@(echo "$(distdir) archives ready for distribution: "; \
	  list='$(DIST_ARCHIVES)'; for i in $$list; do echo $$i; done) | \
	  sed -e 1h -e 1s/./=/g -e 1p -e 1x -e '$$p' -e '$$x'
distuninstallcheck:
	@test -n '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: trying to run $@ with an empty' \
	       '$$(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	$(am__cd) '$(distuninstallcheck_dir)' || { \
	  echo 'ERROR: cannot chdir into $(distuninstallcheck_dir)' >&2; \
	  exit 1; \
	}; \
	test `$(am__distuninstallcheck_listfiles) | wc -l` -eq 0 \
	   || { echo "ERROR: files left after uninstall:" ; \
	        if test -n "$(DESTDIR)"; then \
	          echo "  (check DESTDIR support)"; \
	        fi ; \
	        $(distuninstallcheck_listfiles) ; \
	        exit 1; } >&2
distcleancheck: distclean
	@if test '$(srcdir)' = . ; then \
	  echo "ERROR: distcleancheck can only run from a VPATH build" ; \
	  exit 1 ; \
	fi
	@test `$(distcleancheck_listfiles) | wc -l` -eq 0 \
	  || { echo "ERROR: files left in build directory after distclean:" ; \
	       $(distcleancheck_listfiles) ; \
	       exit 1; } >&2
check-am: all-am
check: check-recursive
all-am: Makefile $(LTLIBRARIES) $(DATA) $(HEADERS) fficonfig.h
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(toolexeclibdir)" "$(DESTDIR)$(pkgconfigdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f src/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/$(am__dirstamp)
	-rm -f src/aarch64/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/aarch64/$(am__dirstamp)
	-rm -f src/alpha/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/alpha/$(am__dirstamp)
	-rm -f src/arc/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/arc/$(am__dirstamp)
	-rm -f src/arm/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/arm/$(am__dirstamp)
	-rm -f src/avr32/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/avr32/$(am__dirstamp)
	-rm -f src/bfin/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/bfin/$(am__dirstamp)
	-rm -f src/cris/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/cris/$(am__dirstamp)
	-rm -f src/csky/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/csky/$(am__dirstamp)
	-rm -f src/frv/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/frv/$(am__dirstamp)
	-rm -f src/ia64/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/ia64/$(am__dirstamp)
	-rm -f src/kvx/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/kvx/$(am__dirstamp)
	-rm -f src/loongarch64/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/loongarch64/$(am__dirstamp)
	-rm -f src/m32r/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/m32r/$(am__dirstamp)
	-rm -f src/m68k/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/m68k/$(am__dirstamp)
	-rm -f src/m88k/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/m88k/$(am__dirstamp)
	-rm -f src/metag/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/metag/$(am__dirstamp)
	-rm -f src/microblaze/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/microblaze/$(am__dirstamp)
	-rm -f src/mips/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/mips/$(am__dirstamp)
	-rm -f src/moxie/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/moxie/$(am__dirstamp)
	-rm -f src/or1k/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/or1k/$(am__dirstamp)
	-rm -f src/pa/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/pa/$(am__dirstamp)
	-rm -f src/powerpc/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/powerpc/$(am__dirstamp)
	-rm -f src/riscv/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/riscv/$(am__dirstamp)
	-rm -f src/s390/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/s390/$(am__dirstamp)
	-rm -f src/sh/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/sh/$(am__dirstamp)
	-rm -f src/sh64/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/sh64/$(am__dirstamp)
	-rm -f src/sparc/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/sparc/$(am__dirstamp)
	-rm -f src/tile/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/tile/$(am__dirstamp)
	-rm -f src/vax/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/vax/$(am__dirstamp)
	-rm -f src/wasm32/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/wasm32/$(am__dirstamp)
	-rm -f src/x86/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/x86/$(am__dirstamp)
	-rm -f src/xtensa/$(DEPDIR)/$(am__dirstamp)
	-rm -f src/xtensa/$(am__dirstamp)
	-test -z "$(DISTCLEANFILES)" || rm -f $(DISTCLEANFILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-recursive

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	clean-toolexeclibLTLIBRARIES mostlyclean-am

distclean: distclean-recursive
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
		-rm -f src/$(DEPDIR)/closures.Plo
	-rm -f src/$(DEPDIR)/debug.Plo
	-rm -f src/$(DEPDIR)/java_raw_api.Plo
	-rm -f src/$(DEPDIR)/prep_cif.Plo
	-rm -f src/$(DEPDIR)/raw_api.Plo
	-rm -f src/$(DEPDIR)/tramp.Plo
	-rm -f src/$(DEPDIR)/types.Plo
	-rm -f src/aarch64/$(DEPDIR)/ffi.Plo
	-rm -f src/aarch64/$(DEPDIR)/sysv.Plo
	-rm -f src/aarch64/$(DEPDIR)/win64_armasm.Plo
	-rm -f src/alpha/$(DEPDIR)/ffi.Plo
	-rm -f src/alpha/$(DEPDIR)/osf.Plo
	-rm -f src/arc/$(DEPDIR)/arcompact.Plo
	-rm -f src/arc/$(DEPDIR)/ffi.Plo
	-rm -f src/arm/$(DEPDIR)/ffi.Plo
	-rm -f src/arm/$(DEPDIR)/sysv.Plo
	-rm -f src/arm/$(DEPDIR)/sysv_msvc_arm32.Plo
	-rm -f src/avr32/$(DEPDIR)/ffi.Plo
	-rm -f src/avr32/$(DEPDIR)/sysv.Plo
	-rm -f src/bfin/$(DEPDIR)/ffi.Plo
	-rm -f src/bfin/$(DEPDIR)/sysv.Plo
	-rm -f src/cris/$(DEPDIR)/ffi.Plo
	-rm -f src/cris/$(DEPDIR)/sysv.Plo
	-rm -f src/csky/$(DEPDIR)/ffi.Plo
	-rm -f src/csky/$(DEPDIR)/sysv.Plo
	-rm -f src/frv/$(DEPDIR)/eabi.Plo
	-rm -f src/frv/$(DEPDIR)/ffi.Plo
	-rm -f src/ia64/$(DEPDIR)/ffi.Plo
	-rm -f src/ia64/$(DEPDIR)/unix.Plo
	-rm -f src/kvx/$(DEPDIR)/ffi.Plo
	-rm -f src/kvx/$(DEPDIR)/sysv.Plo
	-rm -f src/loongarch64/$(DEPDIR)/ffi.Plo
	-rm -f src/loongarch64/$(DEPDIR)/sysv.Plo
	-rm -f src/m32r/$(DEPDIR)/ffi.Plo
	-rm -f src/m32r/$(DEPDIR)/sysv.Plo
	-rm -f src/m68k/$(DEPDIR)/ffi.Plo
	-rm -f src/m68k/$(DEPDIR)/sysv.Plo
	-rm -f src/m88k/$(DEPDIR)/ffi.Plo
	-rm -f src/m88k/$(DEPDIR)/obsd.Plo
	-rm -f src/metag/$(DEPDIR)/ffi.Plo
	-rm -f src/metag/$(DEPDIR)/sysv.Plo
	-rm -f src/microblaze/$(DEPDIR)/ffi.Plo
	-rm -f src/microblaze/$(DEPDIR)/sysv.Plo
	-rm -f src/mips/$(DEPDIR)/ffi.Plo
	-rm -f src/mips/$(DEPDIR)/n32.Plo
	-rm -f src/mips/$(DEPDIR)/o32.Plo
	-rm -f src/moxie/$(DEPDIR)/eabi.Plo
	-rm -f src/moxie/$(DEPDIR)/ffi.Plo
	-rm -f src/or1k/$(DEPDIR)/ffi.Plo
	-rm -f src/or1k/$(DEPDIR)/sysv.Plo
	-rm -f src/pa/$(DEPDIR)/ffi.Plo
	-rm -f src/pa/$(DEPDIR)/hpux32.Plo
	-rm -f src/pa/$(DEPDIR)/hpux64.Plo
	-rm -f src/pa/$(DEPDIR)/linux.Plo
	-rm -f src/powerpc/$(DEPDIR)/aix.Plo
	-rm -f src/powerpc/$(DEPDIR)/aix_closure.Plo
	-rm -f src/powerpc/$(DEPDIR)/darwin.Plo
	-rm -f src/powerpc/$(DEPDIR)/darwin_closure.Plo
	-rm -f src/powerpc/$(DEPDIR)/ffi.Plo
	-rm -f src/powerpc/$(DEPDIR)/ffi_darwin.Plo
	-rm -f src/powerpc/$(DEPDIR)/ffi_linux64.Plo
	-rm -f src/powerpc/$(DEPDIR)/ffi_sysv.Plo
	-rm -f src/powerpc/$(DEPDIR)/linux64.Plo
	-rm -f src/powerpc/$(DEPDIR)/linux64_closure.Plo
	-rm -f src/powerpc/$(DEPDIR)/ppc_closure.Plo
	-rm -f src/powerpc/$(DEPDIR)/sysv.Plo
	-rm -f src/riscv/$(DEPDIR)/ffi.Plo
	-rm -f src/riscv/$(DEPDIR)/sysv.Plo
	-rm -f src/s390/$(DEPDIR)/ffi.Plo
	-rm -f src/s390/$(DEPDIR)/sysv.Plo
	-rm -f src/sh/$(DEPDIR)/ffi.Plo
	-rm -f src/sh/$(DEPDIR)/sysv.Plo
	-rm -f src/sh64/$(DEPDIR)/ffi.Plo
	-rm -f src/sh64/$(DEPDIR)/sysv.Plo
	-rm -f src/sparc/$(DEPDIR)/ffi.Plo
	-rm -f src/sparc/$(DEPDIR)/ffi64.Plo
	-rm -f src/sparc/$(DEPDIR)/v8.Plo
	-rm -f src/sparc/$(DEPDIR)/v9.Plo
	-rm -f src/tile/$(DEPDIR)/ffi.Plo
	-rm -f src/tile/$(DEPDIR)/tile.Plo
	-rm -f src/vax/$(DEPDIR)/elfbsd.Plo
	-rm -f src/vax/$(DEPDIR)/ffi.Plo
	-rm -f src/wasm32/$(DEPDIR)/ffi.Plo
	-rm -f src/x86/$(DEPDIR)/ffi.Plo
	-rm -f src/x86/$(DEPDIR)/ffi64.Plo
	-rm -f src/x86/$(DEPDIR)/ffiw64.Plo
	-rm -f src/x86/$(DEPDIR)/sysv.Plo
	-rm -f src/x86/$(DEPDIR)/sysv_intel.Plo
	-rm -f src/x86/$(DEPDIR)/unix64.Plo
	-rm -f src/x86/$(DEPDIR)/win64.Plo
	-rm -f src/x86/$(DEPDIR)/win64_intel.Plo
	-rm -f src/xtensa/$(DEPDIR)/ffi.Plo
	-rm -f src/xtensa/$(DEPDIR)/sysv.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-libtool distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am: install-pkgconfigDATA

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am: install-toolexeclibLTLIBRARIES

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
	-rm -f $(am__CONFIG_DISTCLEAN_FILES)
	-rm -rf $(top_srcdir)/autom4te.cache
		-rm -f src/$(DEPDIR)/closures.Plo
	-rm -f src/$(DEPDIR)/debug.Plo
	-rm -f src/$(DEPDIR)/java_raw_api.Plo
	-rm -f src/$(DEPDIR)/prep_cif.Plo
	-rm -f src/$(DEPDIR)/raw_api.Plo
	-rm -f src/$(DEPDIR)/tramp.Plo
	-rm -f src/$(DEPDIR)/types.Plo
	-rm -f src/aarch64/$(DEPDIR)/ffi.Plo
	-rm -f src/aarch64/$(DEPDIR)/sysv.Plo
	-rm -f src/aarch64/$(DEPDIR)/win64_armasm.Plo
	-rm -f src/alpha/$(DEPDIR)/ffi.Plo
	-rm -f src/alpha/$(DEPDIR)/osf.Plo
	-rm -f src/arc/$(DEPDIR)/arcompact.Plo
	-rm -f src/arc/$(DEPDIR)/ffi.Plo
	-rm -f src/arm/$(DEPDIR)/ffi.Plo
	-rm -f src/arm/$(DEPDIR)/sysv.Plo
	-rm -f src/arm/$(DEPDIR)/sysv_msvc_arm32.Plo
	-rm -f src/avr32/$(DEPDIR)/ffi.Plo
	-rm -f src/avr32/$(DEPDIR)/sysv.Plo
	-rm -f src/bfin/$(DEPDIR)/ffi.Plo
	-rm -f src/bfin/$(DEPDIR)/sysv.Plo
	-rm -f src/cris/$(DEPDIR)/ffi.Plo
	-rm -f src/cris/$(DEPDIR)/sysv.Plo
	-rm -f src/csky/$(DEPDIR)/ffi.Plo
	-rm -f src/csky/$(DEPDIR)/sysv.Plo
	-rm -f src/frv/$(DEPDIR)/eabi.Plo
	-rm -f src/frv/$(DEPDIR)/ffi.Plo
	-rm -f src/ia64/$(DEPDIR)/ffi.Plo
	-rm -f src/ia64/$(DEPDIR)/unix.Plo
	-rm -f src/kvx/$(DEPDIR)/ffi.Plo
	-rm -f src/kvx/$(DEPDIR)/sysv.Plo
	-rm -f src/loongarch64/$(DEPDIR)/ffi.Plo
	-rm -f src/loongarch64/$(DEPDIR)/sysv.Plo
	-rm -f src/m32r/$(DEPDIR)/ffi.Plo
	-rm -f src/m32r/$(DEPDIR)/sysv.Plo
	-rm -f src/m68k/$(DEPDIR)/ffi.Plo
	-rm -f src/m68k/$(DEPDIR)/sysv.Plo
	-rm -f src/m88k/$(DEPDIR)/ffi.Plo
	-rm -f src/m88k/$(DEPDIR)/obsd.Plo
	-rm -f src/metag/$(DEPDIR)/ffi.Plo
	-rm -f src/metag/$(DEPDIR)/sysv.Plo
	-rm -f src/microblaze/$(DEPDIR)/ffi.Plo
	-rm -f src/microblaze/$(DEPDIR)/sysv.Plo
	-rm -f src/mips/$(DEPDIR)/ffi.Plo
	-rm -f src/mips/$(DEPDIR)/n32.Plo
	-rm -f src/mips/$(DEPDIR)/o32.Plo
	-rm -f src/moxie/$(DEPDIR)/eabi.Plo
	-rm -f src/moxie/$(DEPDIR)/ffi.Plo
	-rm -f src/or1k/$(DEPDIR)/ffi.Plo
	-rm -f src/or1k/$(DEPDIR)/sysv.Plo
	-rm -f src/pa/$(DEPDIR)/ffi.Plo
	-rm -f src/pa/$(DEPDIR)/hpux32.Plo
	-rm -f src/pa/$(DEPDIR)/hpux64.Plo
	-rm -f src/pa/$(DEPDIR)/linux.Plo
	-rm -f src/powerpc/$(DEPDIR)/aix.Plo
	-rm -f src/powerpc/$(DEPDIR)/aix_closure.Plo
	-rm -f src/powerpc/$(DEPDIR)/darwin.Plo
	-rm -f src/powerpc/$(DEPDIR)/darwin_closure.Plo
	-rm -f src/powerpc/$(DEPDIR)/ffi.Plo
	-rm -f src/powerpc/$(DEPDIR)/ffi_darwin.Plo
	-rm -f src/powerpc/$(DEPDIR)/ffi_linux64.Plo
	-rm -f src/powerpc/$(DEPDIR)/ffi_sysv.Plo
	-rm -f src/powerpc/$(DEPDIR)/linux64.Plo
	-rm -f src/powerpc/$(DEPDIR)/linux64_closure.Plo
	-rm -f src/powerpc/$(DEPDIR)/ppc_closure.Plo
	-rm -f src/powerpc/$(DEPDIR)/sysv.Plo
	-rm -f src/riscv/$(DEPDIR)/ffi.Plo
	-rm -f src/riscv/$(DEPDIR)/sysv.Plo
	-rm -f src/s390/$(DEPDIR)/ffi.Plo
	-rm -f src/s390/$(DEPDIR)/sysv.Plo
	-rm -f src/sh/$(DEPDIR)/ffi.Plo
	-rm -f src/sh/$(DEPDIR)/sysv.Plo
	-rm -f src/sh64/$(DEPDIR)/ffi.Plo
	-rm -f src/sh64/$(DEPDIR)/sysv.Plo
	-rm -f src/sparc/$(DEPDIR)/ffi.Plo
	-rm -f src/sparc/$(DEPDIR)/ffi64.Plo
	-rm -f src/sparc/$(DEPDIR)/v8.Plo
	-rm -f src/sparc/$(DEPDIR)/v9.Plo
	-rm -f src/tile/$(DEPDIR)/ffi.Plo
	-rm -f src/tile/$(DEPDIR)/tile.Plo
	-rm -f src/vax/$(DEPDIR)/elfbsd.Plo
	-rm -f src/vax/$(DEPDIR)/ffi.Plo
	-rm -f src/wasm32/$(DEPDIR)/ffi.Plo
	-rm -f src/x86/$(DEPDIR)/ffi.Plo
	-rm -f src/x86/$(DEPDIR)/ffi64.Plo
	-rm -f src/x86/$(DEPDIR)/ffiw64.Plo
	-rm -f src/x86/$(DEPDIR)/sysv.Plo
	-rm -f src/x86/$(DEPDIR)/sysv_intel.Plo
	-rm -f src/x86/$(DEPDIR)/unix64.Plo
	-rm -f src/x86/$(DEPDIR)/win64.Plo
	-rm -f src/x86/$(DEPDIR)/win64_intel.Plo
	-rm -f src/xtensa/$(DEPDIR)/ffi.Plo
	-rm -f src/xtensa/$(DEPDIR)/sysv.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-pkgconfigDATA uninstall-toolexeclibLTLIBRARIES

.MAKE: $(am__recursive_targets) all install-am install-strip

.PHONY: $(am__recursive_targets) CTAGS GTAGS TAGS all all-am \
	am--depfiles am--refresh check check-am clean clean-cscope \
	clean-generic clean-libtool clean-noinstLTLIBRARIES \
	clean-toolexeclibLTLIBRARIES cscope cscopelist-am ctags \
	ctags-am dist dist-all dist-bzip2 dist-gzip dist-hook \
	dist-lzip dist-shar dist-tarZ dist-xz dist-zip dist-zstd \
	distcheck distclean distclean-compile distclean-generic \
	distclean-hdr distclean-libtool distclean-tags distcleancheck \
	distdir distuninstallcheck dvi dvi-am html html-am info \
	info-am install install-am install-data install-data-am \
	install-dvi install-dvi-am install-exec install-exec-am \
	install-html install-html-am install-info install-info-am \
	install-man install-pdf install-pdf-am install-pkgconfigDATA \
	install-ps install-ps-am install-strip \
	install-toolexeclibLTLIBRARIES installcheck installcheck-am \
	installdirs installdirs-am maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am uninstall-pkgconfigDATA \
	uninstall-toolexeclibLTLIBRARIES

.PRECIOUS: Makefile

@LIBFFI_BUILD_VERSIONED_SHLIB_SUN_TRUE@@<EMAIL>-sun : libffi.map $(top_srcdir)/make_sunver.pl \
@LIBFFI_BUILD_VERSIONED_SHLIB_SUN_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@		 $(libffi_la_OBJECTS) $(libffi_la_LIBADD)
@LIBFFI_BUILD_VERSIONED_SHLIB_SUN_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@	perl $(top_srcdir)/make_sunver.pl libffi.map \
@LIBFFI_BUILD_VERSIONED_SHLIB_SUN_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@	 `echo $(libffi_la_OBJECTS) $(libffi_la_LIBADD) | \
@LIBFFI_BUILD_VERSIONED_SHLIB_SUN_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@	    sed 's,\([^/        ]*\)\.l\([ao]\),.libs/\1.\2,g'` \
@LIBFFI_BUILD_VERSIONED_SHLIB_SUN_TRUE@@LIBFFI_BUILD_VERSIONED_SHLIB_TRUE@	 > $@ || (rm -f $@ ; exit 1)

libffi.map: $(top_srcdir)/libffi.map.in
	$(COMPILE) -D$(TARGET) -DGENERATE_LIBFFI_MAP \
	 -E -x assembler-with-cpp -o $@ $(top_srcdir)/libffi.map.in

dist-hook:
	d=`(cd $(distdir); pwd)`; (cd doc; make pdf; cp *.pdf $$d/doc)
	if [ -d $(top_srcdir)/.git ] ; then (cd $(top_srcdir); git log --no-decorate) ; else echo 'See git log for history.' ; fi > $(distdir)/ChangeLog
	s=`awk '/was released on/{ print NR; exit}' $(top_srcdir)/README.md`; tail -n +$$(($$s-1)) $(top_srcdir)/README.md > $(distdir)/README.md

# target overrides
-include $(tmake_file)

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
