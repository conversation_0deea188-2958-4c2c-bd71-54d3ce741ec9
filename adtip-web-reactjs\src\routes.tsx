import { create<PERSON><PERSON>erRouter } from "react-router-dom";
import { lazy, Suspense } from "react";
import App from "./App";
import {LoadingSpinner} from "./components/LoadingSpinner";

// Lazy load components for code splitting
const Index = lazy(() => import("./pages/Index"));
const Home = lazy(() => import("./pages/Home"));
const Login = lazy(() => import("./pages/Login"));
const OTPVerification = lazy(() => import("./pages/OTPVerification"));
const Profile = lazy(() => import("./pages/Profile"));
const EditProfile = lazy(() => import("./pages/EditProfile"));
const TipTube = lazy(() => import("./pages/TipTube"));
const TipShorts = lazy(() => import("./pages/TipShorts"));
const NotFound = lazy(() => import("./pages/NotFound"));
const Onboarding = lazy(() => import("./pages/Onboarding"));
const PersonalDetails = lazy(() => import("./pages/PersonalDetails"));
const Interests = lazy(() => import("./pages/Interests"));
const CreatePost = lazy(() => import("./pages/CreatePost"));
const TipCall = lazy(() => import("./pages/TipCall"));
const Refer = lazy(() => import("./pages/Refer"));
const Premium = lazy(() => import("./pages/Premium"));

// Marketplace components (grouped for better chunking)
const TipShop = lazy(() => import("./pages/marketplace/TipShop"));
const BecomeSeller = lazy(() => import("./pages/marketplace/BecomeSeller"));
const ProductDetail = lazy(() => import("./pages/marketplace/ProductDetail"));
const ListProductsPage = lazy(() => import("./pages/ListProductsPage"));
const ListProductFinish = lazy(() => import("./pages/marketplace/ListProductFinish"));
const AddProduct = lazy(() => import("./pages/marketplace/AddProduct"));
const AddService = lazy(() => import("./pages/marketplace/AddService"));
const Checkout = lazy(() => import("./pages/marketplace/Checkout"));
const OrderConfirmation = lazy(() => import("./pages/marketplace/OrderConfirmation"));
const BecomeSellerFullPage = lazy(() => import("./pages/BecomeSellerFullPage"));
const PremiumContent = lazy(() => import("./pages/marketplace/PremiumContent"));
const PostAds = lazy(() => import("./pages/marketplace/PostAds"));
const Analysis = lazy(() => import("./pages/marketplace/Analysis"));
const Cart = lazy(() => import("./pages/marketplace/Cart"));
const MyOrders = lazy(() => import("./pages/marketplace/MyOrders"));
const Favorites = lazy(() => import("./pages/marketplace/Favorites"));

// Settings and info pages
const ContactUs = lazy(() => import("./pages/ContactUs"));
const Settings = lazy(() => import("./pages/Settings"));
const TermsAndConditions = lazy(() => import("./pages/TermsAndConditions"));
const AdsTracker = lazy(() => import("./pages/AdsTracker"));
const HowToEarnCreator = lazy(() => import("./pages/HowToEarnCreator"));
const HowToEarnUser = lazy(() => import("./pages/HowToEarnUser"));
const PrivacyPolicy = lazy(() => import("./pages/PrivacyPolicy"));
const EarnOpportunities = lazy(() => import("./pages/EarnOpportunities"));
const CompleteProfile = lazy(() => import("./pages/CompleteProfile"));
const PricingOffers = lazy(() => import("./pages/PricingOffers"));
const ChoosePlan = lazy(() => import("./pages/ChoosePlan"));
const RazorpayCheckout = lazy(() => import("./pages/RazorpayCheckout"));
const Wallet = lazy(() => import("./components/Wallet"));

// Wrapper component for lazy loading with suspense
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<LoadingSpinner />}>
    {children}
  </Suspense>
);

const router = createBrowserRouter([
	{
		path: "/",
		element: <App />,
		children: [
			{
				path: "",
				element: <LazyWrapper><Index /></LazyWrapper>,
			},
			{
				path: "home",
				element: <LazyWrapper><Home /></LazyWrapper>,
			},
			{
				path: "login",
				element: <LazyWrapper><Login /></LazyWrapper>,
			},
			{
				path: "verify-otp",
				element: <LazyWrapper><OTPVerification /></LazyWrapper>,
			},
			{
				path: "profile",
				element: <LazyWrapper><Profile /></LazyWrapper>,
			},
			{
				path: "edit-profile",
				element: <LazyWrapper><EditProfile /></LazyWrapper>,
			},
			{
				path: "tiptube",
				element: <LazyWrapper><TipTube /></LazyWrapper>,
			},
			{
				path: "tipshort",
				element: <LazyWrapper><TipShorts /></LazyWrapper>,
			},
			{
				path: "onboarding",
				element: <LazyWrapper><Onboarding /></LazyWrapper>,
			},
			{
				path: "otp-verification",
				element: <LazyWrapper><OTPVerification /></LazyWrapper>,
			},
			{
				path: "personal-details",
				element: <LazyWrapper><PersonalDetails /></LazyWrapper>,
			},
			{
				path: "interests",
				element: <LazyWrapper><Interests /></LazyWrapper>,
			},
			{
				path: "create-post",
				element: <LazyWrapper><CreatePost /></LazyWrapper>,
			},
			{
				path: "tipcall",
				element: <LazyWrapper><TipCall /></LazyWrapper>,
			},
			{
				path: "refer",
				element: <LazyWrapper><Refer /></LazyWrapper>,
			},
			{
				path: "premium",
				element: <LazyWrapper><Premium /></LazyWrapper>,
			},
			{
				path: "tip-shop",
				element: <LazyWrapper><TipShop /></LazyWrapper>,
			},
			{
				path: "become-seller",
				element: <LazyWrapper><BecomeSeller /></LazyWrapper>,
			},
			{
				path: "product/:id",
				element: <LazyWrapper><ProductDetail /></LazyWrapper>,
			},
			{
				path: "list-products",
				element: <LazyWrapper><ListProductsPage /></LazyWrapper>,
			},
			{
				path: "list-products/finish",
				element: <LazyWrapper><ListProductFinish /></LazyWrapper>,
			},
			{
				path: "marketplace/add-product",
				element: <LazyWrapper><AddProduct /></LazyWrapper>,
			},
			{
				path: "marketplace/add-service",
				element: <LazyWrapper><AddService /></LazyWrapper>,
			},
			{
				path: "checkout",
				element: <LazyWrapper><Checkout /></LazyWrapper>,
			},
			{
				path: "order-confirmation",
				element: <LazyWrapper><OrderConfirmation /></LazyWrapper>,
			},
			{
				path: "become-seller-full",
				element: <LazyWrapper><BecomeSellerFullPage /></LazyWrapper>,
			},
			{
				path: "premium-content",
				element: <LazyWrapper><PremiumContent /></LazyWrapper>,
			},
			{
				path: "post-ads",
				element: <LazyWrapper><PostAds /></LazyWrapper>,
			},
			{
				path: "analysis",
				element: <LazyWrapper><Analysis /></LazyWrapper>,
			},
			{
				path: "contact-us",
				element: <LazyWrapper><ContactUs /></LazyWrapper>,
			},
			{
				path: "settings",
				element: <LazyWrapper><Settings /></LazyWrapper>,
			},
			{
				path: "terms",
				element: <LazyWrapper><TermsAndConditions /></LazyWrapper>,
			},
			{
				path: "privacy",
				element: <LazyWrapper><PrivacyPolicy /></LazyWrapper>,
			},
			{
				path: "ads-tracker",
				element: <LazyWrapper><AdsTracker /></LazyWrapper>,
			},
			{
				path: "how-to-earn-creator",
				element: <LazyWrapper><HowToEarnCreator /></LazyWrapper>,
			},
			{
				path: "how-to-earn-user",
				element: <LazyWrapper><HowToEarnUser /></LazyWrapper>,
			},
			{
				path: "earn-opportunities",
				element: <LazyWrapper><EarnOpportunities /></LazyWrapper>,
			},
			{
				path: "marketplace/cart",
				element: <LazyWrapper><Cart /></LazyWrapper>,
			},
			{
				path: "marketplace/my-orders",
				element: <LazyWrapper><MyOrders /></LazyWrapper>,
			},
			{
				path: "marketplace/favorites",
				element: <LazyWrapper><Favorites /></LazyWrapper>,
			},
			{
				path: "complete-profile",
				element: <LazyWrapper><CompleteProfile /></LazyWrapper>,
			},
			{
				path: "/pricingoffers",
				element: <LazyWrapper><PricingOffers /></LazyWrapper>,
			},
			{
				path: "chooseplan",
				element: <LazyWrapper><ChoosePlan /></LazyWrapper>,
			},
			{
				path: "/razorpay-checkout",
				element: <LazyWrapper><RazorpayCheckout /></LazyWrapper>,
			},
			{
				path: "wallet",
				element: <LazyWrapper><Wallet /></LazyWrapper>,
			},
			{
				path: "*",
				element: <LazyWrapper><NotFound /></LazyWrapper>,
			},
		],
	},
]);

export default router;
