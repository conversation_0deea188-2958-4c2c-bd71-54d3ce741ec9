-- Add session_id column to user_calls table for FCM message tracking
-- This allows us to track call sessions consistently across FCM messages and database records

ALTER TABLE user_calls 
ADD COLUMN session_id VARCHAR(255) NULL COMMENT 'Session identifier for FCM message tracking' 
AFTER videosdk_token;

-- Add index for session_id for faster lookups
CREATE INDEX idx_user_calls_session_id ON user_calls(session_id);

-- Update existing records to have a session_id based on call_id and timestamp
UPDATE user_calls 
SET session_id = CONCAT('call_', call_id, '_', UNIX_TIMESTAMP(created_at)) 
WHERE session_id IS NULL;
