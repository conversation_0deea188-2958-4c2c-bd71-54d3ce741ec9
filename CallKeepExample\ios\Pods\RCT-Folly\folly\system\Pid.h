/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <folly/portability/SysTypes.h>

namespace folly {

/**
 * Calls getpid() and returns the returned value, with a thread-safe
 * cache in front. The cache is updated in the child after fork().
 *
 * Thread-safety: MT-safe.
 *
 * Async-signal-safety: The first call is AS-unsafe but subsequent
 * calls are AS-safe.
 */
pid_t get_cached_pid();

} // namespace folly
