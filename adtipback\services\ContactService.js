const dbQuery = require('../dbConfig/queryRunner');

const ContactService = {
  // Submit contact form
  submitContactForm: (submissionData) => {
    return new Promise((resolve, reject) => {
      const {
        user_id, name, email, phone, subject, message, category, priority,
        ip_address, user_agent, app_version, device_info, attachments
      } = submissionData;

      const sql = `
        INSERT INTO contact_submissions 
        (user_id, name, email, phone, subject, message, category, priority, 
         ip_address, user_agent, app_version, device_info, attachments, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `;

      const values = [
        user_id, name, email, phone, subject, message, category, priority,
        ip_address, user_agent, app_version, device_info, attachments
      ];

      dbQuery.queryRunner(sql, values)
        .then((result) => {
          if (result && result.insertId) {
            console.log('[ContactService] Contact form submitted successfully:', result.insertId);
            
            // TODO: Send email notification to admin
            // TODO: Send confirmation email to user
            
            resolve({
              status: 200,
              message: "Contact form submitted successfully. We'll get back to you soon!",
              data: {
                submission_id: result.insertId,
                reference_number: `CF${result.insertId.toString().padStart(6, '0')}`
              }
            });
          } else {
            reject({
              status: 500,
              message: "Failed to submit contact form",
              data: []
            });
          }
        })
        .catch((error) => {
          console.error('[ContactService] Error submitting contact form:', error);
          reject({
            status: 500,
            message: `Database error: ${error.message || error}`,
            data: []
          });
        });
    });
  },

  // Get contact submissions with filters and pagination
  getContactSubmissions: (filters) => {
    return new Promise((resolve, reject) => {
      const { status, category, priority, page, limit } = filters;
      const offset = (page - 1) * limit;

      let whereConditions = ['cs.is_deleted = 0'];
      let queryParams = [];

      if (status) {
        whereConditions.push('cs.status = ?');
        queryParams.push(status);
      }

      if (category) {
        whereConditions.push('cs.category = ?');
        queryParams.push(category);
      }

      if (priority) {
        whereConditions.push('cs.priority = ?');
        queryParams.push(priority);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // Count query
      const countSql = `
        SELECT COUNT(*) as total
        FROM contact_submissions cs
        ${whereClause}
      `;

      // Main query with joins
      const sql = `
        SELECT 
          cs.*,
          u.name as user_name,
          u.profile_image as user_avatar,
          a.name as resolved_by_name,
          (SELECT COUNT(*) FROM contact_submission_responses csr WHERE csr.submission_id = cs.id) as response_count
        FROM contact_submissions cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN admin a ON cs.resolved_by = a.id
        ${whereClause}
        ORDER BY cs.created_at DESC
        LIMIT ? OFFSET ?
      `;

      // Execute count query first
      dbQuery.queryRunner(countSql, queryParams)
        .then((countResult) => {
          const total = countResult[0]?.total || 0;
          
          // Execute main query
          return dbQuery.queryRunner(sql, [...queryParams, limit, offset])
            .then((submissions) => {
              resolve({
                status: 200,
                message: "Contact submissions fetched successfully",
                data: {
                  submissions: submissions || [],
                  pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                  }
                }
              });
            });
        })
        .catch((error) => {
          console.error('[ContactService] Error fetching contact submissions:', error);
          reject({
            status: 500,
            message: `Database error: ${error.message || error}`,
            data: []
          });
        });
    });
  },

  // Get specific contact submission by ID
  getContactSubmissionById: (submissionId) => {
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT 
          cs.*,
          u.name as user_name,
          u.profile_image as user_avatar,
          u.emailId as user_email,
          a.name as resolved_by_name
        FROM contact_submissions cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN admin a ON cs.resolved_by = a.id
        WHERE cs.id = ? AND cs.is_deleted = 0
      `;

      // Also get responses
      const responsesSql = `
        SELECT 
          csr.*,
          a.name as admin_name
        FROM contact_submission_responses csr
        LEFT JOIN admin a ON csr.admin_id = a.id
        WHERE csr.submission_id = ?
        ORDER BY csr.created_at ASC
      `;

      dbQuery.queryRunner(sql, [submissionId])
        .then((submissionResult) => {
          if (!submissionResult || submissionResult.length === 0) {
            return reject({
              status: 404,
              message: "Contact submission not found",
              data: []
            });
          }

          const submission = submissionResult[0];

          // Get responses
          return dbQuery.queryRunner(responsesSql, [submissionId])
            .then((responses) => {
              submission.responses = responses || [];
              
              resolve({
                status: 200,
                message: "Contact submission fetched successfully",
                data: submission
              });
            });
        })
        .catch((error) => {
          console.error('[ContactService] Error fetching contact submission:', error);
          reject({
            status: 500,
            message: `Database error: ${error.message || error}`,
            data: []
          });
        });
    });
  },

  // Update contact submission status
  updateContactSubmissionStatus: (submissionId, updateData) => {
    return new Promise((resolve, reject) => {
      const { status, admin_notes, resolved_by, resolved_at } = updateData;

      const sql = `
        UPDATE contact_submissions 
        SET status = ?, admin_notes = ?, resolved_by = ?, resolved_at = ?, updated_at = NOW()
        WHERE id = ? AND is_deleted = 0
      `;

      const values = [status, admin_notes, resolved_by, resolved_at, submissionId];

      dbQuery.queryRunner(sql, values)
        .then((result) => {
          if (result && result.affectedRows > 0) {
            resolve({
              status: 200,
              message: "Contact submission updated successfully",
              data: { submission_id: submissionId }
            });
          } else {
            reject({
              status: 404,
              message: "Contact submission not found or no changes made",
              data: []
            });
          }
        })
        .catch((error) => {
          console.error('[ContactService] Error updating contact submission:', error);
          reject({
            status: 500,
            message: `Database error: ${error.message || error}`,
            data: []
          });
        });
    });
  },

  // Add response to contact submission
  addContactResponse: (responseData) => {
    return new Promise((resolve, reject) => {
      const { submission_id, admin_id, response_message, is_internal_note } = responseData;

      const sql = `
        INSERT INTO contact_submission_responses 
        (submission_id, admin_id, response_message, is_internal_note, created_at)
        VALUES (?, ?, ?, ?, NOW())
      `;

      const values = [submission_id, admin_id, response_message, is_internal_note];

      dbQuery.queryRunner(sql, values)
        .then((result) => {
          if (result && result.insertId) {
            resolve({
              status: 200,
              message: "Response added successfully",
              data: { response_id: result.insertId }
            });
          } else {
            reject({
              status: 500,
              message: "Failed to add response",
              data: []
            });
          }
        })
        .catch((error) => {
          console.error('[ContactService] Error adding contact response:', error);
          reject({
            status: 500,
            message: `Database error: ${error.message || error}`,
            data: []
          });
        });
    });
  }
};

module.exports = ContactService;
