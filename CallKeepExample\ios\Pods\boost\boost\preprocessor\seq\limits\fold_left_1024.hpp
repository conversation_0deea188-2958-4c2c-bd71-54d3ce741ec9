# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_SEQ_FOLD_LEFT_1024_HPP
# define BOOST_PREPROCESSOR_SEQ_FOLD_LEFT_1024_HPP
#
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_513(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_514(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_515(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_516(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_517(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_518(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_519(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_520(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_521(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_522(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_523(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_524(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_525(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_526(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_527(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_528(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_529(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_530(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_531(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_532(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_533(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_534(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_535(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_536(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_537(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_538(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_539(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_540(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_541(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_542(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_543(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_544(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_545(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_546(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_547(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_548(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_549(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_550(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_551(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_552(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_553(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_554(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_555(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_556(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_557(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_558(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_559(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_560(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_561(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_562(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_563(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_564(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_565(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_566(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_567(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_568(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_569(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_570(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_571(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_572(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_573(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_574(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_575(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_576(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_577(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_578(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_579(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_580(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_581(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_582(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_583(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_584(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_585(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_586(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_587(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_588(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_589(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_590(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_591(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_592(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_593(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_594(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_595(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_596(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_597(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_598(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_599(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_600(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_601(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_602(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_603(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_604(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_605(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_606(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_607(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_608(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_609(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_610(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_611(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_612(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_613(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_614(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_615(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_616(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_617(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_618(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_619(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_620(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_621(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_622(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_623(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_624(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_625(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_626(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_627(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_628(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_629(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_630(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_631(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_632(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_633(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_634(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_635(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_636(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_637(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_638(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_639(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_640(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_641(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_642(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_643(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_644(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_645(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_646(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_647(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_648(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_649(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_650(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_651(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_652(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_653(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_654(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_655(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_656(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_657(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_658(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_659(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_660(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_661(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_662(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_663(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_664(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_665(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_666(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_667(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_668(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_669(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_670(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_671(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_672(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_673(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_674(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_675(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_676(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_677(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_678(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_679(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_680(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_681(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_682(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_683(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_684(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_685(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_686(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_687(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_688(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_689(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_690(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_691(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_692(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_693(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_694(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_695(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_696(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_697(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_698(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_699(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_700(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_701(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_702(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_703(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_704(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_705(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_706(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_707(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_708(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_709(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_710(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_711(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_712(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_713(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_714(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_715(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_716(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_717(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_718(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_719(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_720(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_721(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_722(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_723(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_724(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_725(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_726(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_727(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_728(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_729(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_730(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_731(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_732(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_733(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_734(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_735(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_736(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_737(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_738(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_739(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_740(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_741(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_742(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_743(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_744(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_745(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_746(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_747(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_748(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_749(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_750(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_751(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_752(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_753(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_754(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_755(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_756(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_757(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_758(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_759(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_760(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_761(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_762(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_763(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_764(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_765(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_766(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_767(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_768(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_769(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_770(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_771(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_772(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_773(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_774(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_775(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_776(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_777(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_778(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_779(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_780(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_781(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_782(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_783(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_784(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_785(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_786(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_787(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_788(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_789(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_790(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_791(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_792(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_793(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_794(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_795(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_796(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_797(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_798(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_799(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_800(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_801(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_802(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_803(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_804(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_805(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_806(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_807(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_808(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_809(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_810(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_811(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_812(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_813(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_814(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_815(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_816(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_817(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_818(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_819(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_820(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_821(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_822(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_823(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_824(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_825(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_826(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_827(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_828(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_829(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_830(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_831(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_832(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_833(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_834(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_835(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_836(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_837(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_838(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_839(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_840(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_841(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_842(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_843(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_844(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_845(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_846(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_847(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_848(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_849(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_850(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_851(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_852(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_853(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_854(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_855(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_856(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_857(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_858(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_859(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_860(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_861(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_862(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_863(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_864(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_865(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_866(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_867(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_868(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_869(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_870(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_871(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_872(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_873(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_874(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_875(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_876(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_877(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_878(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_879(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_880(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_881(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_882(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_883(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_884(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_885(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_886(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_887(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_888(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_889(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_890(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_891(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_892(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_893(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_894(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_895(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_896(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_897(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_898(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_899(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_900(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_901(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_902(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_903(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_904(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_905(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_906(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_907(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_908(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_909(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_910(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_911(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_912(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_913(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_914(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_915(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_916(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_917(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_918(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_919(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_920(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_921(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_922(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_923(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_924(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_925(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_926(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_927(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_928(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_929(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_930(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_931(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_932(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_933(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_934(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_935(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_936(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_937(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_938(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_939(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_940(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_941(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_942(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_943(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_944(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_945(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_946(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_947(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_948(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_949(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_950(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_951(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_952(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_953(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_954(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_955(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_956(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_957(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_958(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_959(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_960(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_961(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_962(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_963(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_964(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_965(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_966(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_967(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_968(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_969(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_970(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_971(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_972(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_973(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_974(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_975(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_976(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_977(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_978(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_979(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_980(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_981(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_982(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_983(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_984(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_985(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_986(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_987(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_988(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_989(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_990(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_991(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_992(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_993(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_994(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_995(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_996(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_997(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_998(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_999(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1000(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1001(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1002(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1003(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1004(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1005(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1006(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1007(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1008(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1009(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1010(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1011(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1012(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1013(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1014(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1015(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1016(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1017(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1018(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1019(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1020(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1021(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1022(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1023(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_1024(op, st, ss, sz) 0
#
# define BOOST_PP_SEQ_FOLD_LEFT_513(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_513(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_514(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_514(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_515(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_515(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_516(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_516(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_517(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_517(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_518(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_518(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_519(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_519(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_520(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_520(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_521(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_521(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_522(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_522(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_523(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_523(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_524(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_524(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_525(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_525(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_526(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_526(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_527(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_527(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_528(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_528(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_529(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_529(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_530(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_530(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_531(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_531(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_532(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_532(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_533(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_533(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_534(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_534(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_535(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_535(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_536(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_536(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_537(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_537(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_538(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_538(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_539(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_539(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_540(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_540(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_541(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_541(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_542(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_542(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_543(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_543(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_544(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_544(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_545(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_545(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_546(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_546(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_547(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_547(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_548(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_548(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_549(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_549(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_550(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_550(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_551(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_551(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_552(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_552(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_553(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_553(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_554(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_554(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_555(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_555(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_556(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_556(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_557(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_557(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_558(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_558(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_559(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_559(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_560(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_560(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_561(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_561(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_562(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_562(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_563(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_563(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_564(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_564(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_565(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_565(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_566(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_566(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_567(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_567(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_568(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_568(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_569(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_569(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_570(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_570(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_571(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_571(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_572(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_572(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_573(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_573(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_574(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_574(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_575(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_575(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_576(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_576(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_577(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_577(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_578(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_578(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_579(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_579(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_580(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_580(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_581(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_581(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_582(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_582(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_583(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_583(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_584(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_584(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_585(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_585(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_586(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_586(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_587(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_587(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_588(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_588(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_589(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_589(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_590(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_590(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_591(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_591(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_592(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_592(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_593(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_593(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_594(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_594(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_595(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_595(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_596(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_596(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_597(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_597(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_598(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_598(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_599(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_599(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_600(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_600(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_601(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_601(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_602(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_602(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_603(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_603(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_604(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_604(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_605(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_605(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_606(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_606(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_607(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_607(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_608(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_608(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_609(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_609(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_610(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_610(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_611(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_611(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_612(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_612(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_613(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_613(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_614(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_614(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_615(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_615(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_616(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_616(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_617(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_617(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_618(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_618(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_619(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_619(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_620(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_620(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_621(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_621(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_622(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_622(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_623(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_623(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_624(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_624(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_625(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_625(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_626(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_626(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_627(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_627(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_628(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_628(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_629(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_629(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_630(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_630(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_631(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_631(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_632(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_632(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_633(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_633(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_634(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_634(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_635(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_635(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_636(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_636(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_637(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_637(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_638(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_638(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_639(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_639(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_640(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_640(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_641(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_641(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_642(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_642(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_643(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_643(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_644(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_644(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_645(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_645(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_646(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_646(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_647(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_647(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_648(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_648(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_649(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_649(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_650(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_650(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_651(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_651(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_652(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_652(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_653(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_653(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_654(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_654(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_655(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_655(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_656(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_656(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_657(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_657(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_658(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_658(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_659(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_659(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_660(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_660(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_661(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_661(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_662(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_662(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_663(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_663(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_664(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_664(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_665(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_665(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_666(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_666(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_667(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_667(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_668(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_668(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_669(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_669(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_670(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_670(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_671(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_671(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_672(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_672(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_673(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_673(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_674(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_674(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_675(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_675(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_676(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_676(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_677(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_677(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_678(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_678(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_679(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_679(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_680(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_680(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_681(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_681(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_682(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_682(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_683(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_683(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_684(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_684(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_685(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_685(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_686(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_686(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_687(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_687(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_688(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_688(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_689(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_689(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_690(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_690(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_691(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_691(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_692(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_692(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_693(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_693(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_694(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_694(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_695(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_695(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_696(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_696(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_697(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_697(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_698(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_698(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_699(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_699(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_700(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_700(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_701(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_701(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_702(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_702(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_703(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_703(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_704(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_704(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_705(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_705(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_706(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_706(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_707(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_707(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_708(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_708(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_709(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_709(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_710(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_710(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_711(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_711(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_712(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_712(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_713(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_713(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_714(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_714(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_715(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_715(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_716(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_716(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_717(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_717(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_718(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_718(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_719(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_719(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_720(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_720(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_721(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_721(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_722(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_722(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_723(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_723(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_724(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_724(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_725(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_725(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_726(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_726(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_727(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_727(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_728(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_728(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_729(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_729(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_730(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_730(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_731(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_731(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_732(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_732(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_733(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_733(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_734(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_734(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_735(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_735(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_736(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_736(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_737(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_737(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_738(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_738(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_739(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_739(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_740(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_740(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_741(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_741(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_742(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_742(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_743(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_743(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_744(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_744(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_745(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_745(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_746(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_746(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_747(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_747(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_748(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_748(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_749(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_749(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_750(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_750(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_751(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_751(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_752(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_752(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_753(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_753(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_754(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_754(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_755(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_755(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_756(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_756(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_757(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_757(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_758(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_758(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_759(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_759(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_760(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_760(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_761(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_761(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_762(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_762(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_763(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_763(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_764(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_764(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_765(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_765(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_766(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_766(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_767(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_767(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_768(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_768(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_769(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_769(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_770(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_770(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_771(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_771(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_772(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_772(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_773(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_773(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_774(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_774(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_775(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_775(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_776(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_776(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_777(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_777(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_778(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_778(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_779(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_779(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_780(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_780(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_781(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_781(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_782(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_782(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_783(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_783(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_784(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_784(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_785(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_785(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_786(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_786(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_787(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_787(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_788(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_788(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_789(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_789(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_790(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_790(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_791(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_791(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_792(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_792(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_793(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_793(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_794(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_794(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_795(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_795(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_796(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_796(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_797(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_797(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_798(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_798(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_799(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_799(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_800(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_800(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_801(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_801(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_802(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_802(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_803(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_803(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_804(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_804(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_805(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_805(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_806(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_806(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_807(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_807(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_808(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_808(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_809(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_809(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_810(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_810(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_811(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_811(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_812(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_812(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_813(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_813(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_814(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_814(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_815(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_815(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_816(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_816(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_817(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_817(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_818(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_818(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_819(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_819(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_820(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_820(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_821(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_821(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_822(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_822(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_823(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_823(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_824(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_824(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_825(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_825(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_826(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_826(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_827(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_827(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_828(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_828(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_829(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_829(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_830(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_830(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_831(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_831(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_832(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_832(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_833(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_833(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_834(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_834(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_835(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_835(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_836(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_836(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_837(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_837(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_838(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_838(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_839(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_839(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_840(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_840(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_841(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_841(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_842(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_842(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_843(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_843(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_844(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_844(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_845(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_845(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_846(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_846(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_847(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_847(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_848(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_848(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_849(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_849(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_850(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_850(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_851(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_851(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_852(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_852(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_853(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_853(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_854(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_854(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_855(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_855(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_856(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_856(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_857(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_857(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_858(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_858(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_859(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_859(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_860(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_860(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_861(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_861(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_862(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_862(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_863(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_863(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_864(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_864(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_865(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_865(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_866(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_866(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_867(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_867(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_868(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_868(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_869(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_869(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_870(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_870(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_871(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_871(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_872(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_872(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_873(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_873(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_874(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_874(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_875(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_875(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_876(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_876(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_877(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_877(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_878(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_878(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_879(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_879(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_880(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_880(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_881(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_881(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_882(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_882(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_883(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_883(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_884(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_884(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_885(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_885(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_886(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_886(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_887(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_887(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_888(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_888(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_889(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_889(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_890(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_890(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_891(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_891(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_892(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_892(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_893(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_893(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_894(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_894(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_895(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_895(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_896(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_896(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_897(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_897(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_898(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_898(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_899(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_899(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_900(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_900(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_901(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_901(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_902(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_902(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_903(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_903(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_904(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_904(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_905(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_905(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_906(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_906(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_907(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_907(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_908(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_908(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_909(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_909(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_910(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_910(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_911(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_911(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_912(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_912(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_913(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_913(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_914(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_914(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_915(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_915(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_916(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_916(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_917(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_917(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_918(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_918(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_919(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_919(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_920(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_920(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_921(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_921(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_922(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_922(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_923(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_923(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_924(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_924(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_925(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_925(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_926(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_926(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_927(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_927(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_928(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_928(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_929(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_929(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_930(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_930(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_931(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_931(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_932(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_932(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_933(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_933(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_934(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_934(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_935(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_935(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_936(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_936(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_937(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_937(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_938(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_938(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_939(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_939(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_940(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_940(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_941(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_941(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_942(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_942(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_943(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_943(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_944(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_944(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_945(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_945(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_946(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_946(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_947(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_947(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_948(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_948(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_949(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_949(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_950(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_950(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_951(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_951(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_952(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_952(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_953(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_953(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_954(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_954(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_955(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_955(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_956(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_956(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_957(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_957(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_958(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_958(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_959(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_959(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_960(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_960(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_961(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_961(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_962(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_962(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_963(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_963(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_964(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_964(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_965(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_965(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_966(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_966(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_967(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_967(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_968(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_968(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_969(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_969(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_970(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_970(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_971(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_971(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_972(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_972(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_973(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_973(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_974(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_974(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_975(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_975(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_976(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_976(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_977(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_977(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_978(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_978(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_979(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_979(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_980(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_980(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_981(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_981(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_982(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_982(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_983(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_983(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_984(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_984(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_985(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_985(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_986(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_986(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_987(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_987(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_988(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_988(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_989(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_989(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_990(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_990(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_991(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_991(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_992(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_992(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_993(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_993(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_994(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_994(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_995(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_995(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_996(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_996(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_997(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_997(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_998(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_998(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_999(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_999(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1000(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1000(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1001(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1001(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1002(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1002(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1003(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1003(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1004(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1004(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1005(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1005(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1006(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1006(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1007(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1007(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1008(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1008(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1009(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1009(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1010(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1010(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1011(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1011(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1012(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1012(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1013(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1013(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1014(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1014(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1015(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1015(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1016(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1016(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1017(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1017(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1018(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1018(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1019(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1019(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1020(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1020(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1021(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1021(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1022(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1022(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1023(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1023(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_1024(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_1024(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
#
# define BOOST_PP_SEQ_FOLD_LEFT_I_513(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_514, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(514, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_514(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_515, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(515, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_515(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_516, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(516, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_516(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_517, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(517, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_517(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_518, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(518, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_518(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_519, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(519, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_519(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_520, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(520, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_520(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_521, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(521, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_521(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_522, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(522, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_522(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_523, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(523, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_523(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_524, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(524, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_524(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_525, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(525, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_525(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_526, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(526, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_526(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_527, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(527, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_527(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_528, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(528, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_528(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_529, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(529, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_529(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_530, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(530, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_530(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_531, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(531, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_531(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_532, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(532, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_532(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_533, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(533, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_533(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_534, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(534, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_534(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_535, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(535, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_535(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_536, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(536, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_536(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_537, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(537, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_537(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_538, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(538, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_538(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_539, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(539, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_539(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_540, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(540, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_540(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_541, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(541, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_541(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_542, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(542, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_542(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_543, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(543, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_543(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_544, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(544, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_544(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_545, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(545, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_545(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_546, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(546, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_546(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_547, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(547, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_547(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_548, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(548, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_548(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_549, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(549, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_549(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_550, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(550, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_550(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_551, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(551, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_551(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_552, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(552, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_552(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_553, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(553, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_553(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_554, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(554, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_554(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_555, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(555, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_555(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_556, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(556, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_556(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_557, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(557, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_557(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_558, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(558, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_558(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_559, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(559, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_559(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_560, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(560, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_560(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_561, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(561, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_561(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_562, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(562, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_562(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_563, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(563, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_563(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_564, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(564, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_564(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_565, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(565, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_565(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_566, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(566, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_566(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_567, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(567, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_567(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_568, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(568, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_568(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_569, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(569, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_569(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_570, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(570, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_570(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_571, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(571, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_571(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_572, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(572, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_572(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_573, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(573, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_573(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_574, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(574, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_574(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_575, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(575, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_575(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_576, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(576, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_576(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_577, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(577, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_577(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_578, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(578, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_578(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_579, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(579, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_579(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_580, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(580, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_580(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_581, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(581, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_581(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_582, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(582, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_582(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_583, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(583, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_583(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_584, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(584, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_584(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_585, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(585, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_585(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_586, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(586, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_586(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_587, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(587, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_587(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_588, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(588, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_588(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_589, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(589, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_589(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_590, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(590, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_590(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_591, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(591, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_591(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_592, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(592, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_592(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_593, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(593, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_593(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_594, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(594, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_594(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_595, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(595, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_595(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_596, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(596, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_596(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_597, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(597, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_597(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_598, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(598, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_598(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_599, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(599, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_599(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_600, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(600, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_600(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_601, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(601, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_601(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_602, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(602, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_602(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_603, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(603, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_603(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_604, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(604, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_604(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_605, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(605, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_605(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_606, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(606, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_606(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_607, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(607, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_607(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_608, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(608, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_608(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_609, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(609, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_609(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_610, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(610, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_610(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_611, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(611, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_611(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_612, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(612, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_612(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_613, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(613, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_613(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_614, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(614, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_614(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_615, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(615, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_615(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_616, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(616, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_616(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_617, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(617, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_617(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_618, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(618, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_618(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_619, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(619, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_619(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_620, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(620, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_620(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_621, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(621, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_621(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_622, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(622, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_622(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_623, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(623, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_623(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_624, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(624, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_624(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_625, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(625, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_625(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_626, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(626, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_626(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_627, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(627, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_627(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_628, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(628, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_628(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_629, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(629, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_629(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_630, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(630, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_630(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_631, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(631, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_631(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_632, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(632, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_632(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_633, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(633, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_633(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_634, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(634, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_634(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_635, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(635, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_635(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_636, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(636, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_636(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_637, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(637, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_637(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_638, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(638, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_638(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_639, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(639, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_639(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_640, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(640, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_640(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_641, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(641, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_641(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_642, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(642, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_642(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_643, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(643, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_643(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_644, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(644, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_644(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_645, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(645, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_645(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_646, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(646, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_646(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_647, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(647, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_647(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_648, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(648, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_648(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_649, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(649, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_649(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_650, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(650, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_650(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_651, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(651, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_651(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_652, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(652, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_652(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_653, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(653, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_653(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_654, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(654, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_654(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_655, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(655, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_655(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_656, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(656, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_656(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_657, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(657, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_657(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_658, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(658, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_658(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_659, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(659, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_659(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_660, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(660, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_660(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_661, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(661, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_661(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_662, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(662, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_662(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_663, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(663, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_663(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_664, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(664, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_664(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_665, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(665, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_665(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_666, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(666, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_666(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_667, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(667, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_667(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_668, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(668, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_668(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_669, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(669, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_669(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_670, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(670, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_670(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_671, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(671, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_671(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_672, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(672, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_672(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_673, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(673, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_673(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_674, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(674, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_674(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_675, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(675, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_675(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_676, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(676, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_676(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_677, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(677, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_677(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_678, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(678, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_678(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_679, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(679, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_679(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_680, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(680, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_680(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_681, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(681, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_681(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_682, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(682, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_682(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_683, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(683, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_683(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_684, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(684, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_684(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_685, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(685, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_685(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_686, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(686, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_686(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_687, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(687, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_687(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_688, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(688, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_688(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_689, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(689, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_689(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_690, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(690, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_690(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_691, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(691, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_691(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_692, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(692, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_692(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_693, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(693, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_693(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_694, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(694, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_694(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_695, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(695, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_695(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_696, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(696, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_696(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_697, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(697, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_697(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_698, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(698, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_698(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_699, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(699, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_699(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_700, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(700, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_700(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_701, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(701, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_701(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_702, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(702, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_702(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_703, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(703, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_703(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_704, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(704, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_704(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_705, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(705, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_705(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_706, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(706, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_706(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_707, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(707, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_707(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_708, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(708, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_708(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_709, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(709, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_709(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_710, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(710, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_710(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_711, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(711, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_711(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_712, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(712, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_712(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_713, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(713, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_713(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_714, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(714, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_714(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_715, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(715, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_715(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_716, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(716, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_716(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_717, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(717, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_717(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_718, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(718, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_718(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_719, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(719, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_719(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_720, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(720, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_720(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_721, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(721, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_721(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_722, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(722, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_722(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_723, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(723, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_723(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_724, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(724, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_724(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_725, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(725, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_725(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_726, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(726, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_726(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_727, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(727, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_727(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_728, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(728, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_728(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_729, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(729, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_729(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_730, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(730, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_730(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_731, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(731, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_731(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_732, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(732, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_732(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_733, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(733, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_733(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_734, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(734, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_734(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_735, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(735, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_735(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_736, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(736, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_736(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_737, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(737, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_737(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_738, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(738, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_738(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_739, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(739, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_739(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_740, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(740, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_740(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_741, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(741, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_741(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_742, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(742, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_742(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_743, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(743, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_743(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_744, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(744, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_744(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_745, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(745, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_745(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_746, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(746, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_746(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_747, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(747, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_747(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_748, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(748, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_748(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_749, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(749, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_749(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_750, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(750, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_750(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_751, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(751, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_751(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_752, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(752, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_752(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_753, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(753, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_753(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_754, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(754, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_754(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_755, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(755, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_755(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_756, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(756, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_756(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_757, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(757, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_757(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_758, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(758, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_758(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_759, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(759, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_759(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_760, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(760, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_760(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_761, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(761, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_761(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_762, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(762, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_762(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_763, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(763, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_763(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_764, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(764, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_764(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_765, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(765, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_765(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_766, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(766, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_766(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_767, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(767, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_767(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_768, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(768, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_768(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_769, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(769, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_769(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_770, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(770, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_770(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_771, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(771, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_771(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_772, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(772, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_772(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_773, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(773, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_773(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_774, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(774, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_774(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_775, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(775, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_775(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_776, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(776, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_776(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_777, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(777, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_777(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_778, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(778, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_778(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_779, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(779, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_779(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_780, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(780, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_780(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_781, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(781, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_781(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_782, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(782, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_782(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_783, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(783, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_783(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_784, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(784, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_784(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_785, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(785, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_785(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_786, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(786, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_786(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_787, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(787, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_787(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_788, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(788, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_788(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_789, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(789, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_789(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_790, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(790, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_790(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_791, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(791, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_791(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_792, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(792, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_792(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_793, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(793, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_793(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_794, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(794, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_794(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_795, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(795, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_795(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_796, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(796, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_796(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_797, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(797, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_797(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_798, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(798, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_798(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_799, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(799, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_799(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_800, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(800, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_800(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_801, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(801, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_801(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_802, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(802, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_802(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_803, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(803, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_803(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_804, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(804, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_804(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_805, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(805, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_805(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_806, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(806, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_806(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_807, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(807, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_807(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_808, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(808, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_808(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_809, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(809, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_809(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_810, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(810, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_810(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_811, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(811, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_811(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_812, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(812, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_812(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_813, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(813, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_813(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_814, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(814, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_814(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_815, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(815, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_815(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_816, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(816, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_816(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_817, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(817, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_817(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_818, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(818, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_818(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_819, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(819, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_819(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_820, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(820, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_820(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_821, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(821, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_821(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_822, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(822, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_822(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_823, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(823, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_823(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_824, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(824, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_824(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_825, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(825, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_825(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_826, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(826, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_826(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_827, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(827, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_827(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_828, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(828, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_828(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_829, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(829, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_829(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_830, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(830, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_830(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_831, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(831, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_831(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_832, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(832, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_832(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_833, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(833, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_833(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_834, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(834, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_834(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_835, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(835, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_835(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_836, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(836, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_836(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_837, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(837, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_837(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_838, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(838, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_838(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_839, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(839, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_839(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_840, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(840, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_840(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_841, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(841, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_841(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_842, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(842, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_842(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_843, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(843, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_843(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_844, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(844, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_844(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_845, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(845, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_845(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_846, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(846, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_846(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_847, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(847, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_847(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_848, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(848, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_848(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_849, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(849, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_849(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_850, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(850, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_850(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_851, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(851, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_851(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_852, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(852, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_852(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_853, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(853, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_853(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_854, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(854, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_854(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_855, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(855, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_855(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_856, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(856, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_856(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_857, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(857, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_857(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_858, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(858, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_858(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_859, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(859, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_859(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_860, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(860, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_860(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_861, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(861, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_861(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_862, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(862, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_862(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_863, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(863, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_863(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_864, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(864, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_864(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_865, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(865, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_865(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_866, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(866, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_866(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_867, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(867, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_867(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_868, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(868, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_868(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_869, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(869, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_869(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_870, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(870, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_870(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_871, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(871, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_871(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_872, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(872, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_872(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_873, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(873, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_873(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_874, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(874, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_874(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_875, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(875, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_875(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_876, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(876, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_876(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_877, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(877, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_877(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_878, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(878, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_878(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_879, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(879, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_879(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_880, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(880, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_880(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_881, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(881, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_881(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_882, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(882, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_882(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_883, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(883, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_883(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_884, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(884, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_884(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_885, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(885, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_885(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_886, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(886, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_886(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_887, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(887, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_887(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_888, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(888, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_888(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_889, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(889, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_889(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_890, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(890, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_890(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_891, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(891, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_891(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_892, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(892, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_892(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_893, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(893, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_893(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_894, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(894, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_894(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_895, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(895, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_895(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_896, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(896, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_896(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_897, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(897, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_897(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_898, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(898, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_898(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_899, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(899, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_899(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_900, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(900, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_900(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_901, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(901, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_901(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_902, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(902, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_902(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_903, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(903, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_903(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_904, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(904, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_904(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_905, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(905, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_905(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_906, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(906, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_906(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_907, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(907, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_907(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_908, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(908, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_908(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_909, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(909, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_909(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_910, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(910, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_910(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_911, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(911, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_911(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_912, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(912, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_912(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_913, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(913, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_913(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_914, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(914, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_914(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_915, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(915, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_915(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_916, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(916, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_916(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_917, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(917, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_917(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_918, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(918, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_918(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_919, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(919, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_919(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_920, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(920, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_920(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_921, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(921, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_921(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_922, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(922, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_922(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_923, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(923, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_923(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_924, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(924, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_924(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_925, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(925, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_925(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_926, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(926, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_926(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_927, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(927, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_927(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_928, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(928, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_928(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_929, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(929, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_929(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_930, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(930, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_930(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_931, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(931, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_931(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_932, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(932, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_932(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_933, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(933, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_933(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_934, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(934, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_934(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_935, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(935, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_935(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_936, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(936, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_936(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_937, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(937, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_937(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_938, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(938, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_938(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_939, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(939, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_939(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_940, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(940, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_940(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_941, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(941, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_941(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_942, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(942, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_942(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_943, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(943, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_943(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_944, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(944, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_944(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_945, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(945, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_945(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_946, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(946, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_946(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_947, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(947, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_947(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_948, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(948, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_948(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_949, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(949, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_949(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_950, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(950, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_950(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_951, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(951, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_951(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_952, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(952, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_952(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_953, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(953, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_953(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_954, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(954, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_954(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_955, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(955, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_955(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_956, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(956, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_956(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_957, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(957, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_957(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_958, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(958, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_958(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_959, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(959, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_959(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_960, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(960, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_960(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_961, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(961, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_961(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_962, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(962, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_962(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_963, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(963, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_963(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_964, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(964, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_964(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_965, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(965, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_965(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_966, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(966, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_966(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_967, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(967, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_967(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_968, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(968, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_968(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_969, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(969, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_969(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_970, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(970, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_970(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_971, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(971, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_971(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_972, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(972, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_972(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_973, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(973, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_973(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_974, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(974, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_974(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_975, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(975, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_975(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_976, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(976, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_976(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_977, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(977, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_977(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_978, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(978, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_978(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_979, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(979, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_979(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_980, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(980, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_980(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_981, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(981, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_981(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_982, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(982, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_982(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_983, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(983, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_983(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_984, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(984, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_984(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_985, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(985, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_985(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_986, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(986, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_986(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_987, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(987, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_987(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_988, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(988, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_988(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_989, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(989, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_989(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_990, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(990, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_990(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_991, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(991, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_991(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_992, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(992, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_992(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_993, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(993, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_993(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_994, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(994, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_994(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_995, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(995, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_995(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_996, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(996, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_996(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_997, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(997, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_997(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_998, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(998, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_998(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_999, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(999, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_999(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1000, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1000, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1000(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1001, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1001, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1001(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1002, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1002, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1002(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1003, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1003, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1003(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1004, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1004, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1004(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1005, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1005, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1005(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1006, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1006, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1006(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1007, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1007, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1007(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1008, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1008, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1008(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1009, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1009, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1009(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1010, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1010, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1010(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1011, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1011, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1011(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1012, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1012, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1012(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1013, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1013, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1013(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1014, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1014, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1014(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1015, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1015, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1015(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1016, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1016, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1016(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1017, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1017, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1017(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1018, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1018, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1018(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1019, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1019, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1019(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1020, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1020, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1020(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1021, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1021, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1021(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1022, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1022, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1022(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1023, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1023, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1023(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1024, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1024, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_1024(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_1025, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(1025, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#
# endif
