const dbQuery = require('../dbConfig/queryRunner'); // Assuming shared dbQuery utility

const exploreService = {
  getExploreContent: async (loggined_user_id, page = 1, limit) => {
    try {
      const offset = (page - 1) * limit;

      // Fetch images (posts with media_type = 'image')
      const imageQuery = `
        WITH LikeCounts AS (
          SELECT postId, COUNT(id) AS likeCount
          FROM user_post_likes
          GROUP BY postId
        ),
        CommentCounts AS (
          SELECT postId, COUNT(id) AS commentCount
          FROM user_post_comments
          GROUP BY postId
        ),
        UserLiked AS (
          SELECT postId, 1 AS is_liked
          FROM user_post_likes
          WHERE user_id = ${loggined_user_id}
        )
        SELECT 
          p.id, p.user_id, p.title, p.content, p.media_url, p.media_type, 
          p.is_promoted, p.video_category_id, u.name AS user_name, 
          u.profile_image AS user_profile_image, u.address, 
          COALESCE(u.premium_plan_id, 0) AS premium_plan_id,
          pp.id AS post_promotion_id, pp.target_min_age, pp.target_max_age, 
          pp.reach_goal, pp.duration_days, 
          FORMAT(pp.pay_per_view, 2) AS pay_per_view, 
          FORMAT(pp.total_pay, 2) AS total_pay, 
          FORMAT(pp.platform_fee, 2) AS platform_fee,
          COALESCE(lc.likeCount, 0) AS likeCount,
          COALESCE(cc.commentCount, 0) AS commentCount,
          COALESCE(ul.is_liked, 0) AS is_liked,
          'post' AS content_type
        FROM posts p
        LEFT JOIN post_promotions pp ON p.id = pp.post_id
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN video_categories vc ON p.video_category_id = vc.id
        LEFT JOIN LikeCounts lc ON p.id = lc.postId
        LEFT JOIN CommentCounts cc ON p.id = cc.postId
        LEFT JOIN UserLiked ul ON p.id = ul.postId
        WHERE p.is_active = 0 AND p.media_type = 'image'
        ORDER BY
          CASE
            WHEN p.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1
            WHEN p.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 2
            ELSE 3
          END,
          RAND(UNIX_TIMESTAMP() + p.id),
          lc.likeCount DESC,
          p.created_at DESC
        LIMIT ${limit} OFFSET ${offset};
      `;

      // Fetch shots (short videos)
      const shotQuery = `
        SELECT 
          r.id, r.name, r.category_id, r.video_link AS media_url, 
          r.video_desciption AS content, r.total_views, r.total_likes, 
          r.createdby AS user_id, r.video_Thumbnail AS thumbnail, 
          rdl.is_like AS is_liked, cdf.is_follow AS channel_follow, 
          c.name AS user_name, c.profile_image AS user_profile_image, 
          c.id AS channelId, 
          (SELECT COUNT(*) 
           FROM video_comments vc 
           WHERE vc.video_id = r.id) AS total_comments,
          'shot' AS content_type
        FROM reels r  
        LEFT JOIN reel_details_like rdl ON rdl.reel_id = r.id AND rdl.user_id = ${loggined_user_id}
        LEFT JOIN channels c ON r.video_channel = c.id  
        LEFT JOIN channel_details_followers cdf ON cdf.user_id = ${loggined_user_id} AND cdf.channel_id = c.id
        WHERE r.is_shot = 1 AND r.video_link LIKE '%theadtip.in/%'
        ORDER BY
          CASE
            WHEN r.createddate >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1
            WHEN r.createddate >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 2
            ELSE 3
          END,
          RAND(UNIX_TIMESTAMP() + r.id + ${loggined_user_id}),
          r.total_views DESC,
          r.createddate DESC
        LIMIT ${limit} OFFSET ${offset};
      `;

      // Execute both queries concurrently
      const [imageResults, shotResults] = await Promise.all([
        dbQuery.queryRunner(imageQuery),
        dbQuery.queryRunner(shotQuery),
      ]);

      // Ensure is_liked is boolean
      imageResults.forEach((post) => (post.is_liked = !!post.is_liked));
      shotResults.forEach((shot) => (shot.is_liked = !!shot.is_liked));

      // Merge and shuffle results
      const allContent = [...imageResults, ...shotResults];
      const shuffledContent = allContent.sort(() => Math.random() - 0.5);

      // Calculate total counts for pagination
      const countQuery = `
        SELECT 
          (SELECT COUNT(*) FROM posts WHERE is_active = 0 AND media_type = 'image') AS totalImages,
          (SELECT COUNT(*) FROM reels WHERE is_shot = 1 AND video_link LIKE '%theadtip.in/%') AS totalShots
      `;
      const countResults = await dbQuery.queryRunner(countQuery);
      const totalImages = countResults[0]?.totalImages || 0;
      const totalShots = countResults[0]?.totalShots || 0;
      const totalContent = totalImages + totalShots;
      const totalPages = Math.ceil(totalContent / limit);

      return {
        status: 200,
        message: shuffledContent.length === 0 ? 'No content found' : 'Explore content fetched successfully',
        data: shuffledContent,
        pagination: {
          current_page: page,
          total_page: totalPages,
          total_count: totalContent,
        },
      };
    } catch (error) {
      console.error('Explore Service Error:', error);
      return { error: 'Failed to fetch explore content' };
    }
  },
};

module.exports = exploreService;