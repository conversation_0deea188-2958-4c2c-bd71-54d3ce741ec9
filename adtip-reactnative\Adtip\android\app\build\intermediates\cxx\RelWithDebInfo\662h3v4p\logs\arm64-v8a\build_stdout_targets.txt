ninja: Entering directory `F:\A1\adtip-reactnative\Adtip\android\app\.cxx\RelWithDebInfo\662h3v4p\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/arm64-v8a
[0/2] Re-checking globbed directories...
[1/4] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[2/4] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[3/4] Building CXX object CMakeFiles/appmodules.dir/F_/A1/adtip-reactnative/Adtip/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[4/4] Linking CXX shared library F:\A1\adtip-reactnative\Adtip\android\app\build\intermediates\cxx\RelWithDebInfo\662h3v4p\obj\arm64-v8a\libappmodules.so
