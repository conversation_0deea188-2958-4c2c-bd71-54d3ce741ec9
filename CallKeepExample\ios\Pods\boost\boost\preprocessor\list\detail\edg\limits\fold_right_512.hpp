# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_LIST_DETAIL_EDG_FOLD_RIGHT_512_HPP
# define BOOST_PREPROCESSOR_LIST_DETAIL_EDG_FOLD_RIGHT_512_HPP
#
# define BOOST_PP_LIST_FOLD_RIGHT_257(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_257_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_258(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_258_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_259(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_259_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_260(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_260_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_261(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_261_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_262(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_262_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_263(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_263_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_264(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_264_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_265(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_265_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_266(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_266_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_267(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_267_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_268(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_268_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_269(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_269_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_270(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_270_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_271(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_271_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_272(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_272_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_273(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_273_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_274(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_274_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_275(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_275_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_276(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_276_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_277(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_277_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_278(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_278_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_279(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_279_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_280(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_280_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_281(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_281_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_282(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_282_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_283(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_283_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_284(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_284_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_285(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_285_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_286(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_286_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_287(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_287_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_288(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_288_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_289(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_289_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_290(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_290_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_291(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_291_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_292(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_292_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_293(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_293_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_294(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_294_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_295(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_295_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_296(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_296_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_297(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_297_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_298(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_298_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_299(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_299_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_300(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_300_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_301(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_301_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_302(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_302_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_303(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_303_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_304(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_304_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_305(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_305_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_306(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_306_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_307(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_307_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_308(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_308_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_309(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_309_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_310(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_310_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_311(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_311_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_312(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_312_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_313(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_313_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_314(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_314_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_315(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_315_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_316(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_316_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_317(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_317_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_318(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_318_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_319(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_319_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_320(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_320_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_321(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_321_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_322(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_322_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_323(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_323_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_324(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_324_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_325(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_325_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_326(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_326_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_327(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_327_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_328(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_328_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_329(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_329_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_330(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_330_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_331(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_331_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_332(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_332_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_333(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_333_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_334(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_334_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_335(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_335_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_336(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_336_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_337(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_337_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_338(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_338_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_339(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_339_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_340(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_340_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_341(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_341_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_342(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_342_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_343(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_343_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_344(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_344_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_345(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_345_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_346(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_346_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_347(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_347_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_348(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_348_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_349(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_349_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_350(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_350_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_351(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_351_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_352(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_352_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_353(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_353_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_354(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_354_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_355(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_355_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_356(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_356_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_357(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_357_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_358(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_358_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_359(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_359_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_360(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_360_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_361(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_361_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_362(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_362_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_363(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_363_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_364(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_364_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_365(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_365_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_366(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_366_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_367(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_367_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_368(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_368_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_369(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_369_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_370(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_370_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_371(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_371_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_372(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_372_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_373(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_373_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_374(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_374_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_375(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_375_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_376(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_376_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_377(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_377_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_378(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_378_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_379(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_379_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_380(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_380_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_381(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_381_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_382(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_382_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_383(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_383_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_384(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_384_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_385(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_385_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_386(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_386_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_387(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_387_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_388(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_388_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_389(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_389_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_390(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_390_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_391(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_391_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_392(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_392_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_393(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_393_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_394(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_394_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_395(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_395_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_396(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_396_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_397(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_397_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_398(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_398_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_399(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_399_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_400(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_400_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_401(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_401_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_402(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_402_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_403(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_403_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_404(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_404_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_405(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_405_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_406(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_406_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_407(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_407_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_408(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_408_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_409(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_409_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_410(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_410_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_411(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_411_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_412(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_412_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_413(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_413_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_414(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_414_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_415(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_415_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_416(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_416_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_417(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_417_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_418(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_418_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_419(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_419_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_420(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_420_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_421(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_421_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_422(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_422_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_423(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_423_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_424(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_424_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_425(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_425_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_426(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_426_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_427(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_427_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_428(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_428_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_429(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_429_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_430(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_430_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_431(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_431_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_432(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_432_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_433(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_433_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_434(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_434_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_435(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_435_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_436(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_436_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_437(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_437_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_438(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_438_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_439(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_439_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_440(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_440_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_441(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_441_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_442(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_442_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_443(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_443_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_444(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_444_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_445(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_445_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_446(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_446_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_447(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_447_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_448(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_448_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_449(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_449_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_450(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_450_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_451(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_451_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_452(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_452_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_453(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_453_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_454(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_454_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_455(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_455_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_456(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_456_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_457(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_457_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_458(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_458_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_459(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_459_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_460(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_460_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_461(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_461_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_462(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_462_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_463(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_463_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_464(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_464_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_465(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_465_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_466(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_466_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_467(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_467_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_468(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_468_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_469(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_469_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_470(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_470_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_471(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_471_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_472(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_472_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_473(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_473_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_474(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_474_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_475(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_475_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_476(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_476_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_477(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_477_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_478(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_478_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_479(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_479_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_480(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_480_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_481(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_481_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_482(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_482_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_483(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_483_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_484(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_484_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_485(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_485_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_486(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_486_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_487(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_487_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_488(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_488_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_489(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_489_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_490(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_490_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_491(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_491_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_492(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_492_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_493(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_493_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_494(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_494_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_495(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_495_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_496(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_496_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_497(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_497_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_498(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_498_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_499(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_499_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_500(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_500_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_501(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_501_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_502(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_502_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_503(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_503_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_504(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_504_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_505(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_505_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_506(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_506_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_507(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_507_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_508(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_508_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_509(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_509_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_510(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_510_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_511(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_511_D(o, s, l)
# define BOOST_PP_LIST_FOLD_RIGHT_512(o, s, l) BOOST_PP_LIST_FOLD_RIGHT_512_D(o, s, l)
#
# define BOOST_PP_LIST_FOLD_RIGHT_257_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(258, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_258, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_258_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(259, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_259, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_259_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(260, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_260, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_260_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(261, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_261, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_261_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(262, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_262, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_262_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(263, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_263, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_263_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(264, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_264, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_264_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(265, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_265, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_265_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(266, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_266, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_266_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(267, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_267, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_267_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(268, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_268, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_268_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(269, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_269, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_269_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(270, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_270, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_270_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(271, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_271, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_271_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(272, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_272, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_272_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(273, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_273, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_273_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(274, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_274, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_274_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(275, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_275, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_275_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(276, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_276, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_276_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(277, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_277, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_277_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(278, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_278, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_278_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(279, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_279, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_279_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(280, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_280, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_280_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(281, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_281, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_281_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(282, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_282, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_282_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(283, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_283, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_283_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(284, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_284, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_284_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(285, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_285, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_285_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(286, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_286, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_286_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(287, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_287, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_287_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(288, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_288, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_288_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(289, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_289, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_289_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(290, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_290, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_290_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(291, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_291, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_291_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(292, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_292, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_292_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(293, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_293, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_293_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(294, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_294, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_294_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(295, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_295, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_295_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(296, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_296, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_296_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(297, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_297, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_297_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(298, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_298, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_298_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(299, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_299, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_299_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(300, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_300, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_300_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(301, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_301, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_301_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(302, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_302, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_302_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(303, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_303, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_303_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(304, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_304, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_304_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(305, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_305, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_305_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(306, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_306, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_306_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(307, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_307, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_307_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(308, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_308, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_308_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(309, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_309, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_309_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(310, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_310, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_310_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(311, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_311, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_311_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(312, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_312, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_312_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(313, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_313, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_313_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(314, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_314, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_314_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(315, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_315, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_315_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(316, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_316, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_316_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(317, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_317, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_317_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(318, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_318, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_318_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(319, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_319, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_319_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(320, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_320, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_320_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(321, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_321, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_321_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(322, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_322, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_322_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(323, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_323, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_323_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(324, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_324, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_324_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(325, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_325, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_325_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(326, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_326, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_326_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(327, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_327, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_327_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(328, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_328, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_328_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(329, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_329, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_329_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(330, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_330, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_330_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(331, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_331, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_331_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(332, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_332, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_332_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(333, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_333, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_333_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(334, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_334, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_334_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(335, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_335, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_335_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(336, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_336, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_336_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(337, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_337, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_337_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(338, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_338, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_338_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(339, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_339, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_339_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(340, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_340, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_340_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(341, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_341, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_341_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(342, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_342, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_342_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(343, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_343, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_343_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(344, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_344, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_344_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(345, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_345, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_345_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(346, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_346, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_346_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(347, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_347, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_347_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(348, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_348, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_348_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(349, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_349, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_349_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(350, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_350, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_350_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(351, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_351, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_351_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(352, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_352, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_352_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(353, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_353, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_353_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(354, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_354, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_354_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(355, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_355, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_355_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(356, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_356, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_356_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(357, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_357, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_357_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(358, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_358, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_358_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(359, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_359, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_359_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(360, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_360, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_360_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(361, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_361, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_361_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(362, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_362, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_362_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(363, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_363, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_363_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(364, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_364, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_364_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(365, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_365, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_365_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(366, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_366, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_366_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(367, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_367, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_367_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(368, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_368, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_368_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(369, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_369, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_369_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(370, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_370, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_370_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(371, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_371, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_371_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(372, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_372, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_372_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(373, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_373, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_373_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(374, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_374, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_374_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(375, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_375, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_375_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(376, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_376, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_376_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(377, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_377, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_377_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(378, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_378, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_378_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(379, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_379, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_379_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(380, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_380, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_380_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(381, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_381, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_381_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(382, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_382, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_382_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(383, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_383, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_383_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(384, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_384, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_384_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(385, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_385, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_385_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(386, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_386, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_386_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(387, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_387, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_387_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(388, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_388, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_388_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(389, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_389, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_389_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(390, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_390, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_390_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(391, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_391, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_391_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(392, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_392, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_392_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(393, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_393, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_393_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(394, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_394, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_394_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(395, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_395, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_395_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(396, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_396, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_396_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(397, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_397, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_397_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(398, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_398, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_398_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(399, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_399, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_399_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(400, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_400, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_400_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(401, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_401, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_401_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(402, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_402, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_402_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(403, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_403, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_403_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(404, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_404, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_404_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(405, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_405, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_405_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(406, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_406, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_406_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(407, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_407, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_407_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(408, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_408, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_408_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(409, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_409, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_409_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(410, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_410, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_410_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(411, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_411, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_411_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(412, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_412, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_412_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(413, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_413, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_413_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(414, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_414, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_414_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(415, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_415, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_415_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(416, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_416, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_416_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(417, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_417, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_417_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(418, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_418, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_418_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(419, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_419, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_419_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(420, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_420, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_420_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(421, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_421, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_421_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(422, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_422, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_422_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(423, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_423, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_423_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(424, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_424, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_424_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(425, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_425, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_425_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(426, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_426, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_426_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(427, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_427, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_427_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(428, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_428, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_428_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(429, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_429, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_429_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(430, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_430, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_430_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(431, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_431, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_431_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(432, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_432, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_432_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(433, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_433, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_433_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(434, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_434, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_434_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(435, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_435, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_435_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(436, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_436, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_436_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(437, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_437, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_437_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(438, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_438, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_438_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(439, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_439, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_439_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(440, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_440, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_440_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(441, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_441, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_441_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(442, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_442, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_442_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(443, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_443, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_443_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(444, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_444, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_444_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(445, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_445, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_445_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(446, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_446, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_446_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(447, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_447, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_447_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(448, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_448, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_448_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(449, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_449, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_449_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(450, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_450, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_450_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(451, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_451, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_451_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(452, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_452, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_452_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(453, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_453, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_453_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(454, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_454, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_454_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(455, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_455, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_455_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(456, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_456, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_456_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(457, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_457, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_457_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(458, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_458, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_458_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(459, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_459, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_459_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(460, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_460, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_460_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(461, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_461, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_461_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(462, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_462, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_462_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(463, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_463, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_463_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(464, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_464, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_464_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(465, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_465, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_465_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(466, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_466, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_466_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(467, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_467, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_467_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(468, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_468, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_468_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(469, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_469, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_469_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(470, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_470, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_470_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(471, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_471, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_471_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(472, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_472, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_472_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(473, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_473, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_473_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(474, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_474, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_474_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(475, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_475, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_475_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(476, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_476, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_476_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(477, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_477, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_477_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(478, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_478, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_478_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(479, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_479, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_479_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(480, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_480, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_480_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(481, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_481, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_481_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(482, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_482, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_482_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(483, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_483, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_483_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(484, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_484, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_484_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(485, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_485, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_485_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(486, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_486, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_486_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(487, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_487, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_487_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(488, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_488, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_488_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(489, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_489, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_489_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(490, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_490, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_490_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(491, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_491, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_491_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(492, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_492, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_492_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(493, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_493, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_493_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(494, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_494, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_494_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(495, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_495, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_495_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(496, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_496, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_496_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(497, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_497, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_497_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(498, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_498, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_498_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(499, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_499, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_499_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(500, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_500, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_500_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(501, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_501, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_501_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(502, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_502, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_502_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(503, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_503, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_503_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(504, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_504, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_504_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(505, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_505, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_505_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(506, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_506, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_506_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(507, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_507, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_507_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(508, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_508, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_508_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(509, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_509, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_509_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(510, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_510, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_510_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(511, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_511, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_511_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(512, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_512, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
# define BOOST_PP_LIST_FOLD_RIGHT_512_D(o, s, l) BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), o, s BOOST_PP_TUPLE_EAT_3)(513, BOOST_PP_IIF(BOOST_PP_LIST_IS_CONS(l), BOOST_PP_LIST_FOLD_RIGHT_513, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_3)(o, s, BOOST_PP_LIST_REST(l)), BOOST_PP_LIST_FIRST(l))
#
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_257(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_258(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_259(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_260(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_261(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_262(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_263(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_264(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_265(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_266(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_267(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_268(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_269(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_270(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_271(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_272(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_273(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_274(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_275(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_276(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_277(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_278(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_279(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_280(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_281(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_282(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_283(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_284(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_285(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_286(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_287(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_288(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_289(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_290(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_291(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_292(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_293(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_294(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_295(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_296(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_297(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_298(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_299(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_300(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_301(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_302(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_303(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_304(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_305(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_306(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_307(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_308(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_309(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_310(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_311(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_312(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_313(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_314(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_315(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_316(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_317(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_318(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_319(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_320(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_321(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_322(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_323(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_324(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_325(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_326(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_327(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_328(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_329(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_330(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_331(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_332(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_333(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_334(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_335(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_336(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_337(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_338(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_339(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_340(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_341(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_342(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_343(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_344(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_345(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_346(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_347(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_348(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_349(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_350(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_351(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_352(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_353(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_354(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_355(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_356(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_357(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_358(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_359(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_360(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_361(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_362(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_363(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_364(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_365(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_366(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_367(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_368(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_369(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_370(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_371(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_372(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_373(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_374(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_375(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_376(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_377(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_378(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_379(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_380(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_381(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_382(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_383(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_384(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_385(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_386(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_387(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_388(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_389(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_390(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_391(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_392(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_393(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_394(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_395(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_396(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_397(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_398(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_399(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_400(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_401(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_402(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_403(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_404(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_405(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_406(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_407(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_408(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_409(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_410(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_411(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_412(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_413(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_414(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_415(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_416(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_417(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_418(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_419(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_420(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_421(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_422(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_423(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_424(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_425(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_426(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_427(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_428(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_429(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_430(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_431(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_432(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_433(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_434(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_435(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_436(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_437(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_438(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_439(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_440(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_441(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_442(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_443(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_444(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_445(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_446(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_447(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_448(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_449(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_450(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_451(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_452(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_453(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_454(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_455(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_456(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_457(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_458(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_459(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_460(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_461(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_462(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_463(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_464(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_465(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_466(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_467(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_468(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_469(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_470(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_471(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_472(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_473(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_474(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_475(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_476(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_477(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_478(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_479(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_480(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_481(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_482(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_483(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_484(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_485(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_486(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_487(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_488(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_489(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_490(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_491(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_492(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_493(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_494(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_495(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_496(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_497(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_498(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_499(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_500(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_501(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_502(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_503(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_504(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_505(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_506(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_507(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_508(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_509(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_510(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_511(o, s, l) 0
# define BOOST_PP_LIST_FOLD_RIGHT_CHECK_BOOST_PP_LIST_FOLD_RIGHT_512(o, s, l) 0
#
# endif
