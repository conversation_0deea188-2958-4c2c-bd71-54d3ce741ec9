// Cloudflare R2 Upload Service for Web Application
// Web-optimized version based on React Native implementation

import {
  CLOUDFLARE_R2_CONFIG,
  UPLOAD_FOLDERS,
  FILE_SIZE_LIMITS,
  SUPPORTED_FORMATS,
  UPLOAD_CONFIG,
  WEB_UPLOAD_CONFIG,
  CLOUDFLARE_PUBLIC_DOMAIN,
  MIME_TYPES,
  type UploadProgress,
  type UploadResult,
  type PresignedUrlInfo,
} from '../config/cloudflareConfig';
import { logDebug, logError, logWarn } from '../utils/ProductionLogger';

class CloudflareUploadService {
  private readonly endpoint: string;
  private readonly bucketName: string;
  private readonly accessKeyId: string;
  private readonly secretAccessKey: string;

  constructor() {
    this.endpoint = `https://${CLOUDFLARE_R2_CONFIG.accountId}.r2.cloudflarestorage.com`;
    this.bucketName = CLOUDFLARE_R2_CONFIG.bucketName;
    this.accessKeyId = CLOUDFLARE_R2_CONFIG.accessKeyId;
    this.secretAccessKey = CLOUDFLARE_R2_CONFIG.secretAccessKey;
  }

  /**
   * Upload a file to Cloudflare R2
   */
  async uploadFile(
    file: File,
    folder: string,
    fileName?: string,
    userId?: number,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      logDebug('CloudflareUpload', 'Starting file upload', {
        fileName: file.name,
        size: file.size,
        type: file.type,
        folder,
      });

      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        throw new Error(validation.error);
      }

      // Generate file key
      const key = this.generateFileKey(folder, fileName || file.name, userId);
      
      // For large files, use presigned URL method
      if (file.size > UPLOAD_CONFIG.PRESIGNED_THRESHOLD) {
        return this.uploadWithPresignedUrl(file, key, onProgress);
      }

      // Direct upload for smaller files
      return this.uploadDirect(file, key, onProgress);
    } catch (error) {
      logError('CloudflareUpload', 'Upload failed', error as Error);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Upload image with optional compression
   */
  async uploadImage(
    file: File,
    folder: string = UPLOAD_FOLDERS.IMAGES,
    fileName?: string,
    userId?: number,
    compress: boolean = WEB_UPLOAD_CONFIG.ENABLE_IMAGE_COMPRESSION,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      let processedFile = file;

      if (compress && this.isImage(file)) {
        processedFile = await this.compressImage(file);
        logDebug('CloudflareUpload', 'Image compressed', {
          originalSize: file.size,
          compressedSize: processedFile.size,
          compressionRatio: ((file.size - processedFile.size) / file.size * 100).toFixed(2) + '%',
        });
      }

      return this.uploadFile(processedFile, folder, fileName, userId, onProgress);
    } catch (error) {
      logError('CloudflareUpload', 'Image upload failed', error as Error);
      return {
        success: false,
        error: (error as Error).message,
      };
    }
  }

  /**
   * Upload video file
   */
  async uploadVideo(
    file: File,
    folder: string = UPLOAD_FOLDERS.VIDEOS,
    fileName?: string,
    userId?: number,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    if (!this.isVideo(file)) {
      return {
        success: false,
        error: 'File is not a valid video format',
      };
    }

    return this.uploadFile(file, folder, fileName, userId, onProgress);
  }

  /**
   * Generate presigned URL for client-side upload
   */
  async generatePresignedUrl(
    folder: string,
    fileName: string,
    contentType: string,
    userId?: number,
    expiresIn: number = 3600
  ): Promise<PresignedUrlInfo> {
    try {
      const key = this.generateFileKey(folder, fileName, userId);
      
      // Call backend API to generate presigned URL
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/generatePresignedUrl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('UserLoggedIn')}`,
        },
        body: JSON.stringify({
          key,
          contentType,
          expiresIn,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate presigned URL: ${response.statusText}`);
      }

      const data = await response.json();
      
      return {
        uploadUrl: data.uploadUrl,
        downloadUrl: `${CLOUDFLARE_PUBLIC_DOMAIN}/${key}`,
        key,
        expiresIn,
      };
    } catch (error) {
      logError('CloudflareUpload', 'Failed to generate presigned URL', error as Error);
      throw error;
    }
  }

  /**
   * Direct upload to R2 (for smaller files)
   */
  private async uploadDirect(
    file: File,
    key: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Get presigned URL from backend
      const presignedInfo = await this.generatePresignedUrl(
        key.split('/')[0],
        file.name,
        file.type || this.getContentType(file.name)
      );

      // Upload using presigned URL
      return this.uploadWithPresignedUrl(file, key, onProgress, presignedInfo.uploadUrl);
    } catch (error) {
      logError('CloudflareUpload', 'Direct upload failed', error as Error);
      throw error;
    }
  }

  /**
   * Upload using presigned URL
   */
  private async uploadWithPresignedUrl(
    file: File,
    key: string,
    onProgress?: (progress: UploadProgress) => void,
    uploadUrl?: string
  ): Promise<UploadResult> {
    try {
      let presignedUrl = uploadUrl;
      
      if (!presignedUrl) {
        const presignedInfo = await this.generatePresignedUrl(
          key.split('/')[0],
          file.name,
          file.type || this.getContentType(file.name)
        );
        presignedUrl = presignedInfo.uploadUrl;
      }

      // Create XMLHttpRequest for progress tracking
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        const startTime = Date.now();

        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable && onProgress) {
            const elapsed = (Date.now() - startTime) / 1000;
            const speed = event.loaded / elapsed;
            const timeRemaining = (event.total - event.loaded) / speed;

            onProgress({
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100),
              speed,
              timeRemaining,
            });
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            const publicUrl = `${CLOUDFLARE_PUBLIC_DOMAIN}/${key}`;
            
            resolve({
              success: true,
              url: publicUrl,
              key,
              metadata: {
                size: file.size,
                type: file.type,
              },
            });
          } else {
            reject(new Error(`Upload failed with status: ${xhr.status}`));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed due to network error'));
        });

        xhr.addEventListener('timeout', () => {
          reject(new Error('Upload timed out'));
        });

        xhr.open('PUT', presignedUrl);
        xhr.timeout = UPLOAD_CONFIG.UPLOAD_TIMEOUT;
        xhr.setRequestHeader('Content-Type', file.type || this.getContentType(file.name));
        xhr.send(file);
      });
    } catch (error) {
      logError('CloudflareUpload', 'Presigned URL upload failed', error as Error);
      throw error;
    }
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    const isVideo = this.isVideo(file);
    const isImage = this.isImage(file);
    
    if (!isVideo && !isImage) {
      return { valid: false, error: 'Unsupported file format' };
    }

    const maxSize = isVideo 
      ? (file.name.includes('short') ? FILE_SIZE_LIMITS.SHORT_MAX : FILE_SIZE_LIMITS.VIDEO_MAX)
      : FILE_SIZE_LIMITS.IMAGE_MAX;

    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return { valid: false, error: `File size exceeds ${maxSizeMB}MB limit` };
    }

    if (file.size === 0) {
      return { valid: false, error: 'File is empty' };
    }

    return { valid: true };
  }

  /**
   * Generate unique file key
   */
  private generateFileKey(folder: string, fileName: string, userId?: number): string {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const userPrefix = userId ? `user_${userId}` : 'anonymous';
    const cleanFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    
    return `${folder}/${userPrefix}/${timestamp}_${randomId}_${cleanFileName}`;
  }

  /**
   * Get content type from file extension
   */
  private getContentType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase() as keyof typeof MIME_TYPES;
    return MIME_TYPES[extension] || 'application/octet-stream';
  }

  /**
   * Check if file is a video
   */
  private isVideo(file: File): boolean {
    const extension = file.name.split('.').pop()?.toLowerCase();
    return extension ? SUPPORTED_FORMATS.VIDEO.includes(extension) : false;
  }

  /**
   * Check if file is an image
   */
  private isImage(file: File): boolean {
    const extension = file.name.split('.').pop()?.toLowerCase();
    return extension ? SUPPORTED_FORMATS.IMAGE.includes(extension) : false;
  }

  /**
   * Compress image file
   */
  private async compressImage(file: File): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        const maxWidth = WEB_UPLOAD_CONFIG.MAX_IMAGE_WIDTH;
        const maxHeight = WEB_UPLOAD_CONFIG.MAX_IMAGE_HEIGHT;

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              resolve(file); // Fallback to original
            }
          },
          file.type,
          WEB_UPLOAD_CONFIG.IMAGE_COMPRESSION_QUALITY
        );
      };

      img.onerror = () => resolve(file); // Fallback to original
      img.src = URL.createObjectURL(file);
    });
  }
}

// Export singleton instance
export const cloudflareUploadService = new CloudflareUploadService();
export default cloudflareUploadService;
