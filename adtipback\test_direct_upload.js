const DirectUploadService = require('./services/DirectUploadService');

async function testDirectUpload() {
  try {
    console.log('🧪 Testing Cloudflare Stream Direct Upload...');
    
    // Test TipShorts upload URL creation
    console.log('\n1. Testing TipShorts upload URL creation...');
    const shortsResult = await DirectUploadService.createTipShortsUploadUrl({
      userId: 'test-user-123',
      userName: 'Test User',
      channelId: 'test-channel-456'
    });
    
    if (shortsResult.success) {
      console.log('✅ TipShorts upload URL created successfully!');
      console.log('Video ID:', shortsResult.data.videoId);
      console.log('Upload URL:', shortsResult.data.uploadURL.substring(0, 80) + '...');
      console.log('Max Duration:', shortsResult.data.maxDurationSeconds, 'seconds');
    } else {
      console.log('❌ TipShorts upload URL creation failed:', shortsResult.error);
    }
    
    // Test TipTube upload URL creation
    console.log('\n2. Testing TipTube upload URL creation...');
    const tubeResult = await DirectUploadService.createTipTubeUploadUrl({
      userId: 'test-user-123',
      userName: 'Test User',
      channelId: 'test-channel-456'
    });
    
    if (tubeResult.success) {
      console.log('✅ TipTube upload URL created successfully!');
      console.log('Video ID:', tubeResult.data.videoId);
      console.log('Upload URL:', tubeResult.data.uploadURL.substring(0, 80) + '...');
      console.log('Max Duration:', tubeResult.data.maxDurationSeconds, 'seconds');
    } else {
      console.log('❌ TipTube upload URL creation failed:', tubeResult.error);
    }
    
    // Test upload status check (using one of the created video IDs)
    if (shortsResult.success) {
      console.log('\n3. Testing upload status check...');
      const statusResult = await DirectUploadService.getUploadStatus(shortsResult.data.videoId);
      
      if (statusResult.success) {
        console.log('✅ Upload status retrieved successfully!');
        console.log('Status:', statusResult.data?.status || 'pending');
        console.log('Ready to stream:', statusResult.data?.readyToStream || false);
        console.log('UID:', statusResult.data?.uid);
      } else {
        console.log('❌ Upload status check failed:', statusResult.error);
      }
    }
    
    console.log('\n🎉 Direct Upload Test Complete!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testDirectUpload();
