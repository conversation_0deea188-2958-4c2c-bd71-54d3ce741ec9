# months
M(a)_1=јан
M(a)_2=феб
M(a)_3=мар
M(a)_4=апр
M(a)_5=мај
M(a)_6=јун
M(a)_7=јул
M(a)_8=авг
M(a)_9=сеп
M(a)_10=окт
M(a)_11=нов
M(a)_12=дец

M(n)_1=ј
M(n)_2=ф
M(n)_3=м
M(n)_4=а
M(n)_5=м
M(n)_6=ј
M(n)_7=ј
M(n)_8=а
M(n)_9=с
M(n)_10=о
M(n)_11=н
M(n)_12=д

M(w)_1=јануар
M(w)_2=фебруар
M(w)_3=март
M(w)_4=април
M(w)_5=мај
M(w)_6=јун
M(w)_7=јул
M(w)_8=август
M(w)_9=септембар
M(w)_10=октобар
M(w)_11=новембар
M(w)_12=децембар

M(A)_1=јан
M(A)_2=феб
M(A)_3=мар
M(A)_4=апр
M(A)_5=мај
M(A)_6=јун
M(A)_7=јул
M(A)_8=авг
M(A)_9=сеп
M(A)_10=окт
M(A)_11=нов
M(A)_12=дец

M(N)_1=ј
M(N)_2=ф
M(N)_3=м
M(N)_4=а
M(N)_5=м
M(N)_6=ј
M(N)_7=ј
M(N)_8=а
M(N)_9=с
M(N)_10=о
M(N)_11=н
M(N)_12=д

M(W)_1=јануар
M(W)_2=фебруар
M(W)_3=март
M(W)_4=април
M(W)_5=мај
M(W)_6=јун
M(W)_7=јул
M(W)_8=август
M(W)_9=септембар
M(W)_10=октобар
M(W)_11=новембар
M(W)_12=децембар

# weekdays
D(a)_1=пон
D(a)_2=уто
D(a)_3=сре
D(a)_4=чет
D(a)_5=пет
D(a)_6=суб
D(a)_7=нед

D(n)_1=п
D(n)_2=у
D(n)_3=с
D(n)_4=ч
D(n)_5=п
D(n)_6=с
D(n)_7=н

D(s)_1=по
D(s)_2=ут
D(s)_3=ср
D(s)_4=че
D(s)_5=пе
D(s)_6=су
D(s)_7=не

D(w)_1=понедељак
D(w)_2=уторак
D(w)_3=среда
D(w)_4=четвртак
D(w)_5=петак
D(w)_6=субота
D(w)_7=недеља

D(A)_1=пон
D(A)_2=уто
D(A)_3=сре
D(A)_4=чет
D(A)_5=пет
D(A)_6=суб
D(A)_7=нед

D(N)_1=п
D(N)_2=у
D(N)_3=с
D(N)_4=ч
D(N)_5=п
D(N)_6=с
D(N)_7=н

D(S)_1=по
D(S)_2=ут
D(S)_3=ср
D(S)_4=че
D(S)_5=пе
D(S)_6=су
D(S)_7=не

D(W)_1=понедељак
D(W)_2=уторак
D(W)_3=среда
D(W)_4=четвртак
D(W)_5=петак
D(W)_6=субота
D(W)_7=недеља

# quarters
Q(a)_1=К1
Q(a)_2=К2
Q(a)_3=К3
Q(a)_4=К4

Q(n)_1=1.
Q(n)_2=2.
Q(n)_3=3.
Q(n)_4=4.

Q(w)_1=први квартал
Q(w)_2=други квартал
Q(w)_3=трећи квартал
Q(w)_4=четврти квартал

Q(A)_1=К1
Q(A)_2=К2
Q(A)_3=К3
Q(A)_4=К4

Q(N)_1=1.
Q(N)_2=2.
Q(N)_3=3.
Q(N)_4=4.

Q(W)_1=први квартал
Q(W)_2=други квартал
Q(W)_3=трећи квартал
Q(W)_4=четврти квартал

# day-period-rules
T0600=morning1
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=поноћ
P(a)_am=пре подне
P(a)_noon=подне
P(a)_pm=по подне
P(a)_morning1=ујутро
P(a)_afternoon1=по подне
P(a)_evening1=увече
P(a)_night1=ноћу

P(n)_midnight=поноћ
P(n)_am=a
P(n)_noon=подне
P(n)_pm=p
P(n)_morning1=ујутру
P(n)_afternoon1=по подне
P(n)_evening1=увече
P(n)_night1=ноћу

P(w)_midnight=поноћ
P(w)_am=пре подне
P(w)_noon=подне
P(w)_pm=по подне
P(w)_morning1=ујутро
P(w)_afternoon1=по подне
P(w)_evening1=увече
P(w)_night1=ноћу

P(A)_midnight=поноћ
P(A)_am=пре подне
P(A)_noon=подне
P(A)_pm=по подне
P(A)_morning1=јутро
P(A)_afternoon1=поподне
P(A)_evening1=вече
P(A)_night1=ноћ

P(N)_midnight=поноћ
P(N)_am=пре подне
P(N)_noon=подне
P(N)_pm=по подне
P(N)_morning1=јутро
P(N)_afternoon1=поподне
P(N)_evening1=вече
P(N)_night1=ноћ

P(W)_midnight=поноћ
P(W)_am=пре подне
P(W)_noon=подне
P(W)_pm=по подне
P(W)_morning1=јутро
P(W)_afternoon1=поподне
P(W)_evening1=вече
P(W)_night1=ноћ

# eras
E(w)_0=пре нове ере
E(w|alt)_0=BCE
E(w)_1=нове ере
E(w|alt)_1=CE

E(a)_0=п. н. е.
E(a|alt)_0=BCE
E(a)_1=н. е.
E(a|alt)_1=CE

E(n)_0=п.н.е.
E(n)_1=н.е.

# format patterns
F(f)_d=EEEE, dd. MMMM y.
F(l)_d=dd. MMMM y.
F(m)_d=dd.MM.y.
F(s)_d=d.M.yy.

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=hh:mm B
F_Bhms=hh:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.M.
F_MMMd=d. MMM
F_MMMMd=d. MMMM
F_y=y.
F_yM=M.y.
F_yMM=MM.y.
F_yMMM=MMM y.
F_yMMMM=MMMM y.
F_yQQQ=QQQ y.
F_yQQQQ=QQQQ y.
F_yw=w. 'седмица' 'у' Y.

I={0} – {1}

# labels of elements
L_era=ера
L_year=година
L_quarter=квартал
L_month=месец
L_week=недеља
L_day=дан
L_weekday=дан у недељи
L_dayperiod=пре подне/по подне
L_hour=сат
L_minute=минут
L_second=секунд
L_zone=временска зона
