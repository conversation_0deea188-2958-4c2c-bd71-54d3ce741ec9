-- Fix is_active field inconsistency in posts table
-- This script fixes posts that were created with is_active=1 but should be is_active=0
-- Run this script to make existing posts visible in user profiles

-- Set timezone to Indian Standard Time
SET time_zone = '+05:30';

-- Display current state before fix
SELECT 'Current post status distribution:' as message;
SELECT 
    is_active,
    COUNT(*) as post_count,
    CASE 
        WHEN is_active = 0 THEN 'Active (visible in queries)'
        WHEN is_active = 1 THEN 'Inactive/Deleted (hidden from queries)'
        ELSE 'Unknown status'
    END as status_meaning
FROM posts 
GROUP BY is_active
ORDER BY is_active;

-- Show recent posts that might be affected
SELECT 'Recent posts that are currently hidden (is_active=1):' as message;
SELECT 
    id,
    user_id,
    title,
    media_type,
    is_promoted,
    created_at,
    is_active
FROM posts 
WHERE is_active = 1 
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    AND user_id IS NOT NULL 
    AND title IS NOT NULL 
    AND title != ''
    AND media_url IS NOT NULL 
    AND media_url != ''
ORDER BY created_at DESC
LIMIT 20;

-- Fix posts that were incorrectly created with is_active=1
-- These are posts that have valid data but are marked as inactive
UPDATE posts 
SET is_active = 0 
WHERE is_active = 1 
    AND user_id IS NOT NULL 
    AND title IS NOT NULL 
    AND title != ''
    AND media_url IS NOT NULL 
    AND media_url != ''
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY); -- Only fix recent posts to be safe

-- Display results after fix
SELECT 'Posts fixed - now visible in user profiles:' as message;
SELECT ROW_COUNT() as posts_updated;

-- Display updated distribution
SELECT 'Updated post status distribution:' as message;
SELECT 
    is_active,
    COUNT(*) as post_count,
    CASE 
        WHEN is_active = 0 THEN 'Active (visible in queries)'
        WHEN is_active = 1 THEN 'Inactive/Deleted (hidden from queries)'
        ELSE 'Unknown status'
    END as status_meaning
FROM posts 
GROUP BY is_active
ORDER BY is_active;

-- Show the specific post from the API logs (post_id: 1131) if it exists
SELECT 'Checking specific post from API logs (post_id: 1131):' as message;
SELECT 
    id,
    user_id,
    title,
    media_type,
    is_promoted,
    created_at,
    is_active,
    CASE 
        WHEN is_active = 0 THEN 'Now visible in user profile'
        WHEN is_active = 1 THEN 'Still hidden from user profile'
        ELSE 'Unknown status'
    END as visibility_status
FROM posts 
WHERE id = 1131;

-- Display completion message
SELECT 'Post is_active field fix completed successfully!' as message;
SELECT 'New posts will now be created with is_active=0 and be immediately visible.' as note;
