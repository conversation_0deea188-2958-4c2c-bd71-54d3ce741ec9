-- =====================================================
-- MYSQL 8 FRESH CHAT DEPLOYMENT SCRIPT
-- Complete replacement of conversation-based with user-based chat system
-- =====================================================
-- This script drops ALL existing chat tables and creates fresh user-based ones
-- Based on schema analysis from Dump20250718

-- =====================================================
-- STEP 1: DISABLE FOREIGN KEY CHECKS
-- =====================================================
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- =====================================================
-- STEP 2: DROP ALL EXISTING CHAT-RELATED TABLES
-- =====================================================
-- Drop tables in correct order to avoid foreign key conflicts

-- Drop old conversation-based tables
DROP TABLE IF EXISTS `message_status`;
DROP TABLE IF EXISTS `message_delivery_failures`;
DROP TABLE IF EXISTS `message_delivery_stats`;
DROP TABLE IF EXISTS `messages`;
DROP TABLE IF EXISTS `conversation_participants`;
DROP TABLE IF EXISTS `conversations`;

-- Drop old user_chat table (legacy)
DROP TABLE IF EXISTS `user_chat`;
DROP TABLE IF EXISTS `user_chat_details`;

-- Drop any backup tables
DROP TABLE IF EXISTS `user_chat_backup_1752766664455`;
DROP TABLE IF EXISTS `user_chat_backup_1752766739714`;
DROP TABLE IF EXISTS `user_chat_backup_1752766853519`;

-- Drop any existing chat settings
DROP TABLE IF EXISTS `chat_settings`;

-- Drop any existing triggers and functions
DROP TRIGGER IF EXISTS `update_user_chat_metadata_on_message`;
DROP TRIGGER IF EXISTS `create_delivery_status_on_message`;
DROP FUNCTION IF EXISTS `GenerateChatId`;
DROP PROCEDURE IF EXISTS `MarkMessagesAsRead`;
DROP PROCEDURE IF EXISTS `GetChatMessages`;

-- Drop any existing views
DROP VIEW IF EXISTS `user_chat_list`;
DROP VIEW IF EXISTS `message_details`;

SELECT 'All existing chat tables and objects dropped successfully' as status;

-- =====================================================
-- STEP 3: CREATE NEW USER-BASED MESSAGES TABLE
-- =====================================================

CREATE TABLE `messages` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `chat_id` VARCHAR(255) NOT NULL COMMENT 'Format: chat_userId1_userId2 (sorted)',
    `sender_id` INT NOT NULL COMMENT 'User who sent the message',
    `recipient_id` INT NOT NULL COMMENT 'User who receives the message',
    `sender_name` VARCHAR(255) NOT NULL COMMENT 'Sender display name',
    `sender_avatar` VARCHAR(500) NULL COMMENT 'Sender profile image URL',
    
    -- Message content and metadata
    `content` TEXT NOT NULL COMMENT 'Message text content',
    `message_type` ENUM('text', 'image', 'video', 'audio', 'file', 'system') DEFAULT 'text',
    
    -- File attachments (for media messages)
    `file_url` VARCHAR(500) NULL COMMENT 'URL for media/file messages',
    `file_size` INT NULL COMMENT 'File size in bytes',
    `file_name` VARCHAR(255) NULL COMMENT 'Original file name',
    `file_mime_type` VARCHAR(100) NULL COMMENT 'MIME type of the file',
    `thumbnail_url` VARCHAR(500) NULL COMMENT 'Thumbnail URL for images/videos',
    
    -- Message relationships
    `reply_to_message_id` INT NULL COMMENT 'ID of message being replied to',
    
    -- Sync and tracking fields
    `external_id` VARCHAR(255) NULL COMMENT 'External ID from FCM or other systems',
    `temp_id` VARCHAR(255) NULL COMMENT 'Temporary ID from client for sync tracking',
    `fcm_message_id` VARCHAR(255) NULL COMMENT 'FCM message ID for tracking',
    
    -- Message status and timestamps
    `status` ENUM('sending', 'sent', 'delivered', 'read', 'failed') DEFAULT 'sent',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Soft delete support
    `is_deleted` BOOLEAN DEFAULT FALSE,
    `deleted_at` TIMESTAMP NULL,
    `deleted_by` INT NULL COMMENT 'User who deleted the message',
    
    -- Message editing support
    `is_edited` BOOLEAN DEFAULT FALSE,
    `edited_at` TIMESTAMP NULL,
    `original_content` TEXT NULL COMMENT 'Original content before editing',
    
    -- Performance indexes
    INDEX `idx_chat_id_created` (`chat_id`, `created_at` DESC),
    INDEX `idx_sender_recipient` (`sender_id`, `recipient_id`),
    INDEX `idx_sender_created` (`sender_id`, `created_at` DESC),
    INDEX `idx_recipient_created` (`recipient_id`, `created_at` DESC),
    INDEX `idx_external_id` (`external_id`),
    INDEX `idx_temp_id` (`temp_id`),
    INDEX `idx_fcm_message_id` (`fcm_message_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_deleted` (`is_deleted`, `deleted_at`),
    INDEX `idx_reply_to` (`reply_to_message_id`),
    
    -- Foreign key constraints
    CONSTRAINT `fk_messages_sender` FOREIGN KEY (`sender_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_messages_recipient` FOREIGN KEY (`recipient_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_messages_reply_to` FOREIGN KEY (`reply_to_message_id`) REFERENCES `messages`(`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_messages_deleted_by` FOREIGN KEY (`deleted_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SELECT 'Messages table created successfully' as status;

-- =====================================================
-- STEP 4: CREATE MESSAGES LOCAL SYNC TABLE
-- =====================================================

CREATE TABLE `messages_local_sync` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `message_id` INT NOT NULL COMMENT 'Reference to messages table',
    `chat_id` VARCHAR(255) NOT NULL COMMENT 'Chat ID for grouping',
    `local_message_id` VARCHAR(255) NULL COMMENT 'Local/temp message ID from client',
    `local_timestamp` TIMESTAMP NOT NULL COMMENT 'Timestamp when message was created locally',
    `server_timestamp` TIMESTAMP NULL COMMENT 'Timestamp when message was synced to server',
    `sync_status` ENUM('pending', 'synced', 'conflict', 'failed') DEFAULT 'pending',
    `sync_attempts` INT DEFAULT 0 COMMENT 'Number of sync attempts',
    `last_sync_attempt` TIMESTAMP NULL COMMENT 'Last time sync was attempted',
    `error_message` TEXT NULL COMMENT 'Error details if sync failed',
    `client_version` VARCHAR(50) NULL COMMENT 'Client app version for debugging',
    
    -- Indexes for performance
    INDEX `idx_message_id` (`message_id`),
    INDEX `idx_chat_id` (`chat_id`),
    INDEX `idx_local_message_id` (`local_message_id`),
    INDEX `idx_sync_status` (`sync_status`),
    INDEX `idx_sync_attempts` (`sync_attempts`),
    INDEX `idx_last_sync_attempt` (`last_sync_attempt`),
    
    -- Foreign key constraints
    CONSTRAINT `fk_sync_message` FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SELECT 'Messages local sync table created successfully' as status;

-- =====================================================
-- STEP 5: CREATE USER CHAT METADATA TABLE
-- =====================================================

CREATE TABLE `user_chat_metadata` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `chat_id` VARCHAR(255) NOT NULL COMMENT 'Format: chat_userId1_userId2',
    `other_user_id` INT NOT NULL COMMENT 'The other participant in the chat',
    `other_user_name` VARCHAR(255) NOT NULL COMMENT 'Display name of other user',
    `other_user_avatar` VARCHAR(500) NULL COMMENT 'Profile image of other user',
    
    -- Chat state
    `unread_count` INT DEFAULT 0,
    `last_read_message_id` INT NULL,
    `last_message_id` INT NULL,
    `last_message_content` TEXT NULL,
    `last_message_time` TIMESTAMP NULL,
    
    -- User preferences for this chat
    `is_muted` BOOLEAN DEFAULT FALSE,
    `is_blocked` BOOLEAN DEFAULT FALSE,
    `is_archived` BOOLEAN DEFAULT FALSE,
    `is_pinned` BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `last_activity_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique chat per user
    UNIQUE KEY `unique_user_chat` (`user_id`, `chat_id`),
    
    -- Indexes for performance
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_chat_id` (`chat_id`),
    INDEX `idx_other_user_id` (`other_user_id`),
    INDEX `idx_last_activity` (`last_activity_at` DESC),
    INDEX `idx_unread_count` (`unread_count`),
    INDEX `idx_user_activity` (`user_id`, `last_activity_at` DESC),
    
    -- Foreign key constraints
    CONSTRAINT `fk_chat_meta_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_chat_meta_other_user` FOREIGN KEY (`other_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_chat_meta_last_read` FOREIGN KEY (`last_read_message_id`) REFERENCES `messages`(`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_chat_meta_last_message` FOREIGN KEY (`last_message_id`) REFERENCES `messages`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SELECT 'User chat metadata table created successfully' as status;

-- =====================================================
-- STEP 6: CREATE MESSAGE DELIVERY STATUS TABLE
-- =====================================================

CREATE TABLE `message_delivery_status` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `message_id` INT NOT NULL,
    `recipient_id` INT NOT NULL,
    `status` ENUM('sent', 'delivered', 'read') NOT NULL,
    `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `fcm_message_id` VARCHAR(255) NULL COMMENT 'FCM message ID for tracking',
    `device_info` TEXT NULL COMMENT 'Device information for debugging',
    
    -- Ensure unique status per message per recipient
    UNIQUE KEY `unique_message_recipient_status` (`message_id`, `recipient_id`, `status`),
    
    -- Indexes for performance
    INDEX `idx_message_id` (`message_id`),
    INDEX `idx_recipient_id` (`recipient_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_timestamp` (`timestamp`),
    INDEX `idx_fcm_message_id` (`fcm_message_id`),
    
    -- Foreign key constraints
    CONSTRAINT `fk_delivery_message` FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_delivery_recipient` FOREIGN KEY (`recipient_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SELECT 'Message delivery status table created successfully' as status;

-- =====================================================
-- STEP 7: CREATE HELPER FUNCTION
-- =====================================================

DELIMITER $$
CREATE FUNCTION GenerateChatId(user_id_1 INT, user_id_2 INT)
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE chat_id VARCHAR(255);

    IF user_id_1 < user_id_2 THEN
        SET chat_id = CONCAT('chat_', user_id_1, '_', user_id_2);
    ELSE
        SET chat_id = CONCAT('chat_', user_id_2, '_', user_id_1);
    END IF;

    RETURN chat_id;
END$$
DELIMITER ;

SELECT 'GenerateChatId function created successfully' as status;

-- =====================================================
-- STEP 8: CREATE STORED PROCEDURES
-- =====================================================

DELIMITER $$
CREATE PROCEDURE MarkMessagesAsRead(
    IN p_user_id INT,
    IN p_chat_id VARCHAR(255),
    IN p_last_read_message_id INT
)
BEGIN
    -- Update user chat metadata
    UPDATE user_chat_metadata
    SET unread_count = 0,
        last_read_message_id = p_last_read_message_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id AND chat_id = p_chat_id;

    -- Update delivery status for all unread messages
    INSERT INTO message_delivery_status (message_id, recipient_id, status)
    SELECT m.id, p_user_id, 'read'
    FROM messages m
    WHERE m.chat_id = p_chat_id
      AND m.recipient_id = p_user_id
      AND m.id <= p_last_read_message_id
      AND NOT EXISTS (
          SELECT 1 FROM message_delivery_status mds
          WHERE mds.message_id = m.id
            AND mds.recipient_id = p_user_id
            AND mds.status = 'read'
      );
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE GetChatMessages(
    IN p_chat_id VARCHAR(255),
    IN p_limit INT,
    IN p_offset INT,
    IN p_before_message_id INT
)
BEGIN
    -- Set default values if NULL
    SET p_limit = IFNULL(p_limit, 50);
    SET p_offset = IFNULL(p_offset, 0);

    SELECT
        m.*,
        mds.status as delivery_status,
        mds.timestamp as delivery_timestamp
    FROM messages m
    LEFT JOIN message_delivery_status mds ON m.id = mds.message_id
    WHERE m.chat_id = p_chat_id
      AND m.is_deleted = FALSE
      AND (p_before_message_id IS NULL OR m.id < p_before_message_id)
    ORDER BY m.created_at DESC, m.id DESC
    LIMIT p_limit OFFSET p_offset;
END$$
DELIMITER ;

SELECT 'Stored procedures created successfully' as status;

-- =====================================================
-- STEP 9: CREATE TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

DELIMITER $$
CREATE TRIGGER update_user_chat_metadata_on_message
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    DECLARE sender_display_name VARCHAR(255) DEFAULT 'Unknown User';
    DECLARE recipient_display_name VARCHAR(255) DEFAULT 'Unknown User';

    -- Get sender and recipient names from users table (with error handling)
    SELECT COALESCE(name, username, 'Unknown User') INTO sender_display_name
    FROM users WHERE id = NEW.sender_id LIMIT 1;

    SELECT COALESCE(name, username, 'Unknown User') INTO recipient_display_name
    FROM users WHERE id = NEW.recipient_id LIMIT 1;

    -- Update metadata for sender
    INSERT INTO user_chat_metadata (
        user_id, chat_id, other_user_id, other_user_name,
        last_message_id, last_message_content, last_message_time, last_activity_at
    ) VALUES (
        NEW.sender_id, NEW.chat_id, NEW.recipient_id, recipient_display_name,
        NEW.id, NEW.content, NEW.created_at, NEW.created_at
    ) ON DUPLICATE KEY UPDATE
        last_message_id = NEW.id,
        last_message_content = NEW.content,
        last_message_time = NEW.created_at,
        last_activity_at = NEW.created_at,
        updated_at = CURRENT_TIMESTAMP;

    -- Update metadata for recipient (increment unread count)
    INSERT INTO user_chat_metadata (
        user_id, chat_id, other_user_id, other_user_name,
        unread_count, last_message_id, last_message_content, last_message_time, last_activity_at
    ) VALUES (
        NEW.recipient_id, NEW.chat_id, NEW.sender_id, sender_display_name,
        1, NEW.id, NEW.content, NEW.created_at, NEW.created_at
    ) ON DUPLICATE KEY UPDATE
        unread_count = unread_count + 1,
        last_message_id = NEW.id,
        last_message_content = NEW.content,
        last_message_time = NEW.created_at,
        last_activity_at = NEW.created_at,
        updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

DELIMITER $$
CREATE TRIGGER create_delivery_status_on_message
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    -- Create initial delivery status
    INSERT INTO message_delivery_status (message_id, recipient_id, status)
    VALUES (NEW.id, NEW.recipient_id, 'sent');
END$$
DELIMITER ;

SELECT 'Triggers created successfully' as status;

-- =====================================================
-- STEP 10: CREATE HELPFUL VIEWS
-- =====================================================

CREATE VIEW user_chat_list AS
SELECT
    ucm.*,
    u.name as other_user_display_name,
    u.profile_image as other_user_profile_image,
    u.online_status as other_user_online_status,
    m.content as last_message_preview,
    m.message_type as last_message_type,
    m.created_at as last_message_timestamp
FROM user_chat_metadata ucm
JOIN users u ON ucm.other_user_id = u.id
LEFT JOIN messages m ON ucm.last_message_id = m.id
WHERE ucm.is_archived = FALSE
ORDER BY ucm.last_activity_at DESC;

CREATE VIEW message_details AS
SELECT
    m.*,
    sender.name as sender_display_name,
    sender.profile_image as sender_profile_image,
    recipient.name as recipient_display_name,
    recipient.profile_image as recipient_profile_image,
    reply_msg.content as reply_to_content,
    reply_sender.name as reply_to_sender_name
FROM messages m
JOIN users sender ON m.sender_id = sender.id
JOIN users recipient ON m.recipient_id = recipient.id
LEFT JOIN messages reply_msg ON m.reply_to_message_id = reply_msg.id
LEFT JOIN users reply_sender ON reply_msg.sender_id = reply_sender.id
WHERE m.is_deleted = FALSE;

SELECT 'Views created successfully' as status;

-- =====================================================
-- STEP 11: RE-ENABLE FOREIGN KEY CHECKS
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- STEP 12: VERIFICATION AND TESTING
-- =====================================================

SELECT 'Fresh Chat System Deployment Verification' as section;

-- Check all tables exist
SELECT 'Tables created:' as check_type;
SHOW TABLES LIKE '%message%';
SHOW TABLES LIKE '%user_chat%';

-- Check table structures
SELECT 'Table structures verified' as status;
SELECT COUNT(*) as messages_columns FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'messages';
SELECT COUNT(*) as sync_columns FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'messages_local_sync';
SELECT COUNT(*) as metadata_columns FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'user_chat_metadata';
SELECT COUNT(*) as delivery_columns FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'message_delivery_status';

-- Check functions and procedures
SELECT 'Functions and procedures:' as check_type;
SHOW FUNCTION STATUS WHERE Name = 'GenerateChatId';
SHOW PROCEDURE STATUS WHERE Name IN ('MarkMessagesAsRead', 'GetChatMessages');

-- Test chat ID generation
SELECT 'Testing chat ID generation:' as test_type;
SELECT GenerateChatId(1, 2) as chat_id_1_2;
SELECT GenerateChatId(2, 1) as chat_id_2_1; -- Should be same as above
SELECT GenerateChatId(5, 3) as chat_id_5_3;

-- Check triggers
SELECT 'Triggers created:' as check_type;
SHOW TRIGGERS LIKE 'messages';

-- Check views
SELECT 'Views created:' as check_type;
SHOW FULL TABLES WHERE Table_type = 'VIEW';

-- =====================================================
-- STEP 13: SAMPLE DATA INSERTION (OPTIONAL)
-- =====================================================
-- Uncomment to insert sample data for testing

/*
-- Insert sample messages (replace with real user IDs)
INSERT INTO messages (chat_id, sender_id, recipient_id, sender_name, content)
VALUES
    ('chat_1_2', 1, 2, 'User One', 'Hello! How are you?'),
    ('chat_1_2', 2, 1, 'User Two', 'Hi there! I am doing great, thanks!'),
    ('chat_1_3', 1, 3, 'User One', 'Hey User Three!'),
    ('chat_2_3', 2, 3, 'User Two', 'Hello from User Two');

SELECT 'Sample data inserted successfully' as status;
SELECT 'Sample messages:' as data_type;
SELECT * FROM messages ORDER BY created_at DESC LIMIT 5;

SELECT 'Sample chat metadata:' as data_type;
SELECT * FROM user_chat_metadata ORDER BY last_activity_at DESC LIMIT 5;
*/

-- =====================================================
-- FINAL STATUS
-- =====================================================

SELECT '🎉 FRESH CHAT SYSTEM DEPLOYMENT COMPLETED SUCCESSFULLY! 🎉' as final_status;
SELECT 'All old conversation-based tables have been dropped' as cleanup_status;
SELECT 'New user-based chat system is ready for use' as ready_status;
SELECT 'Use chat_userId1_userId2 format for direct user messaging' as usage_info;
