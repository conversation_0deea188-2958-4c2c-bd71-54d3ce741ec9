-- =====================================================
-- MIGRATION SCRIPT: CONVERSATION-BASED TO USER-BASED CHAT SYSTEM
-- =====================================================
-- This script migrates existing conversation-based chat data to the new user-based system
-- Run this script AFTER creating the new user-based schema

-- =====================================================
-- BACKUP EXISTING DATA (SAFETY FIRST)
-- =====================================================

-- Create backup tables with timestamp
SET @backup_suffix = DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s');

-- Backup existing tables
SET @sql = CONCAT('CREATE TABLE conversations_backup_', @backup_suffix, ' AS SELECT * FROM conversations');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE conversation_participants_backup_', @backup_suffix, ' AS SELECT * FROM conversation_participants');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = CONCAT('CREATE TABLE messages_backup_', @backup_suffix, ' AS SELECT * FROM messages WHERE conversation_id IS NOT NULL');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- MIGRATION FUNCTIONS
-- =====================================================

-- Function to generate chat_id from conversation participants
DELIMITER $$
CREATE FUNCTION GetChatIdFromConversation(conv_id INT) 
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE chat_id VARCHAR(255) DEFAULT NULL;
    DECLARE user1_id INT DEFAULT NULL;
    DECLARE user2_id INT DEFAULT NULL;
    DECLARE participant_count INT DEFAULT 0;
    
    -- Check if conversation is direct (2 participants only)
    SELECT COUNT(*) INTO participant_count
    FROM conversation_participants 
    WHERE conversation_id = conv_id AND left_at IS NULL;
    
    IF participant_count = 2 THEN
        -- Get the two participants (sorted)
        SELECT MIN(user_id), MAX(user_id) 
        INTO user1_id, user2_id
        FROM conversation_participants 
        WHERE conversation_id = conv_id AND left_at IS NULL;
        
        -- Generate chat_id
        SET chat_id = CONCAT('chat_', user1_id, '_', user2_id);
    END IF;
    
    RETURN chat_id;
END$$
DELIMITER ;

-- =====================================================
-- STEP 1: MIGRATE MESSAGES FROM CONVERSATIONS
-- =====================================================

-- Add temporary columns to existing messages table for migration
ALTER TABLE messages 
ADD COLUMN temp_chat_id VARCHAR(255) NULL,
ADD COLUMN temp_recipient_id INT NULL,
ADD COLUMN temp_sender_name VARCHAR(255) NULL;

-- Update messages with chat_id and recipient information
UPDATE messages m
JOIN conversations c ON m.conversation_id = c.id
SET m.temp_chat_id = GetChatIdFromConversation(c.id)
WHERE c.type = 'direct' AND m.temp_chat_id IS NULL;

-- Update recipient_id for each message
UPDATE messages m
JOIN conversation_participants cp ON m.conversation_id = cp.conversation_id
SET m.temp_recipient_id = cp.user_id
WHERE cp.user_id != m.sender_id 
  AND cp.left_at IS NULL
  AND m.temp_recipient_id IS NULL;

-- Update sender names
UPDATE messages m
JOIN users u ON m.sender_id = u.id
SET m.temp_sender_name = COALESCE(u.name, u.username, 'Unknown User')
WHERE m.temp_sender_name IS NULL;

-- =====================================================
-- STEP 2: INSERT MIGRATED DATA INTO NEW SCHEMA
-- =====================================================

-- Insert messages into new user-based messages table
INSERT INTO messages (
    chat_id, sender_id, recipient_id, sender_name, sender_avatar,
    content, message_type, file_url, file_size, file_name, file_mime_type,
    thumbnail_url, reply_to_message_id, external_id, status,
    created_at, updated_at, is_deleted, deleted_at, deleted_by
)
SELECT 
    m.temp_chat_id as chat_id,
    m.sender_id,
    m.temp_recipient_id as recipient_id,
    m.temp_sender_name as sender_name,
    u.profile_image as sender_avatar,
    m.content,
    m.message_type,
    m.file_url,
    m.file_size,
    m.file_name,
    m.file_mime_type,
    m.thumbnail_url,
    m.reply_to_message_id,
    m.external_id,
    'sent' as status, -- Default status for migrated messages
    m.created_at,
    m.updated_at,
    m.is_deleted,
    m.deleted_at,
    m.deleted_by
FROM messages m
JOIN users u ON m.sender_id = u.id
WHERE m.temp_chat_id IS NOT NULL 
  AND m.temp_recipient_id IS NOT NULL
  AND m.temp_sender_name IS NOT NULL;

-- =====================================================
-- STEP 3: CREATE USER CHAT METADATA
-- =====================================================

-- Create user_chat_metadata entries for all migrated chats
INSERT INTO user_chat_metadata (
    user_id, chat_id, other_user_id, other_user_name, other_user_avatar,
    last_message_id, last_message_content, last_message_time,
    unread_count, created_at, updated_at, last_activity_at
)
SELECT DISTINCT
    cp1.user_id as user_id,
    GetChatIdFromConversation(c.id) as chat_id,
    cp2.user_id as other_user_id,
    COALESCE(u2.name, u2.username, 'Unknown User') as other_user_name,
    u2.profile_image as other_user_avatar,
    c.last_message_id,
    (SELECT content FROM messages WHERE id = c.last_message_id) as last_message_content,
    c.last_activity_at as last_message_time,
    COALESCE(cp1.unread_count, 0) as unread_count,
    c.created_at,
    c.updated_at,
    c.last_activity_at
FROM conversations c
JOIN conversation_participants cp1 ON c.id = cp1.conversation_id
JOIN conversation_participants cp2 ON c.id = cp2.conversation_id
JOIN users u2 ON cp2.user_id = u2.id
WHERE c.type = 'direct'
  AND cp1.user_id != cp2.user_id
  AND cp1.left_at IS NULL
  AND cp2.left_at IS NULL
  AND GetChatIdFromConversation(c.id) IS NOT NULL;

-- =====================================================
-- STEP 4: MIGRATE MESSAGE DELIVERY STATUS
-- =====================================================

-- Create delivery status entries for migrated messages
INSERT INTO message_delivery_status (message_id, recipient_id, status, timestamp)
SELECT 
    nm.id as message_id,
    nm.recipient_id,
    CASE 
        WHEN ms.status = 'read' THEN 'read'
        WHEN ms.status = 'delivered' THEN 'delivered'
        ELSE 'sent'
    END as status,
    COALESCE(ms.timestamp, nm.created_at) as timestamp
FROM messages nm  -- New messages table
JOIN messages om ON nm.external_id = om.external_id OR nm.temp_id = CAST(om.id AS CHAR) -- Old messages table
LEFT JOIN message_status ms ON om.id = ms.message_id AND nm.recipient_id = ms.user_id
WHERE nm.chat_id IS NOT NULL;

-- =====================================================
-- STEP 5: CREATE SYNC TRACKING ENTRIES
-- =====================================================

-- Create sync tracking entries for all migrated messages
INSERT INTO messages_local_sync (
    message_id, chat_id, local_message_id, local_timestamp,
    server_timestamp, sync_status, sync_attempts
)
SELECT 
    nm.id as message_id,
    nm.chat_id,
    CAST(om.id AS CHAR) as local_message_id, -- Use old message ID as local ID
    nm.created_at as local_timestamp,
    nm.created_at as server_timestamp,
    'synced' as sync_status,
    1 as sync_attempts
FROM messages nm
JOIN messages om ON nm.external_id = om.external_id OR nm.temp_id = CAST(om.id AS CHAR)
WHERE nm.chat_id IS NOT NULL;

-- =====================================================
-- STEP 6: VALIDATION AND CLEANUP
-- =====================================================

-- Validation queries to check migration success
SELECT 'Migration Validation Report' as report_section;

SELECT 
    'Original Messages' as metric,
    COUNT(*) as count
FROM messages 
WHERE conversation_id IS NOT NULL;

SELECT 
    'Migrated Messages' as metric,
    COUNT(*) as count
FROM messages 
WHERE chat_id IS NOT NULL;

SELECT 
    'User Chat Metadata Entries' as metric,
    COUNT(*) as count
FROM user_chat_metadata;

SELECT 
    'Delivery Status Entries' as metric,
    COUNT(*) as count
FROM message_delivery_status;

SELECT 
    'Sync Tracking Entries' as metric,
    COUNT(*) as count
FROM messages_local_sync;

-- Check for any failed migrations
SELECT 
    'Messages with missing chat_id' as issue,
    COUNT(*) as count
FROM messages 
WHERE conversation_id IS NOT NULL AND temp_chat_id IS NULL;

SELECT 
    'Messages with missing recipient' as issue,
    COUNT(*) as count
FROM messages 
WHERE conversation_id IS NOT NULL AND temp_recipient_id IS NULL;

-- =====================================================
-- STEP 7: CLEANUP TEMPORARY COLUMNS (OPTIONAL)
-- =====================================================

-- Remove temporary migration columns (uncomment when migration is verified)
-- ALTER TABLE messages 
-- DROP COLUMN temp_chat_id,
-- DROP COLUMN temp_recipient_id,
-- DROP COLUMN temp_sender_name;

-- Drop migration function
-- DROP FUNCTION GetChatIdFromConversation;

-- =====================================================
-- STEP 8: UPDATE APPLICATION CONFIGURATION
-- =====================================================

-- Create configuration entry to mark migration as complete
INSERT INTO system_config (config_key, config_value, description, created_at)
VALUES (
    'chat_system_migration_completed',
    DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'),
    'Timestamp when conversation-based to user-based chat migration was completed',
    NOW()
) ON DUPLICATE KEY UPDATE
    config_value = DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'),
    updated_at = NOW();

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

SELECT 'Migration completed successfully!' as status,
       'Please verify the data and test the application before removing backup tables' as next_steps;
