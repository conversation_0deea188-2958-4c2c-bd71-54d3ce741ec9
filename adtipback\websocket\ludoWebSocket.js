const LudoService = require("../services/ludoService");
const redisClient = require("../config/redis");
const logger = require("../utils/logger");

const rooms = new Map();
const userConnections = new Map();

const colors = ["R", "B", "G", "Y"];

// Only for socket references
const userSockets = new Map(); // userId -> ws

const setupLudoWebSocket = (ws, req) => {
  console.log("New WebSocket connection established for Ludo");

  // Initialize connection properties
  ws.isAlive = true;
  ws.userId = null;
  ws.roomId = null;

  // Add connection to userSockets immediately for tracking
  ws.on('message', async (message) => {
    try {
      // Ensure message is properly parsed
      if (Buffer.isBuffer(message)) {
        message = message.toString();
      }
      
      const data = JSON.parse(message);
      console.log("Received message:", JSON.stringify(data, null, 2));

      switch (data.type) {
        case "join":
          await handleJoin(ws, data);
          break;
        case "move":
          await handleMove(ws, data);
          break;
        case "quit":
          await handleQuit(ws, data);
          break;
        case "rejoin":
          await handleRejoin(ws, data);
          break;
        default:
          if (ws.readyState === ws.OPEN) {
            ws.send(
              JSON.stringify({
                type: "error",
                message: "Unknown message type.",
              })
            );
          }
      }
    } catch (err) {
      logger.error("WebSocket message error: " + err.message);
      if (ws.readyState === ws.OPEN) {
        try {
          ws.send(
            JSON.stringify({
              type: "error",
              message: err.message || "Invalid message format.",
            })
          );
        } catch (sendErr) {
          logger.error("Error sending error message: " + sendErr.message);
        }
      }
    }
  });

  // Handle pong responses to keep connection alive
  ws.on('pong', () => {
    ws.isAlive = true;
    console.log(`Received pong from user ${ws.userId || 'unknown'}`);
  });

  // Handle ping from client (though usually server sends ping)
  ws.on('ping', (data) => {
    console.log(`Received ping from user ${ws.userId || 'unknown'}`);
    try {
      ws.pong(data); // Echo the ping data back in pong
    } catch (err) {
      logger.error("Error responding to ping: " + err.message);
    }
  });

  ws.on("error", (error) => {
    logger.error("WebSocket connection error: " + error.message);
    console.error("WebSocket connection error:", error);
    
    // Clean up user socket reference on error
    if (ws.userId) {
      userSockets.delete(ws.userId);
    }
  });

  ws.on("close", (code, reason) => {
    console.log(`WebSocket connection closed: ${code} ${reason}`);
    
    // Clean up user socket reference
    if (ws.userId) {
      userSockets.delete(ws.userId);
    }
    
    handleDisconnect(ws);
  });

  // Send initial connection confirmation
  if (ws.readyState === ws.OPEN) {
    try {
      ws.send(JSON.stringify({
        type: "connected",
        message: "Connected to Ludo WebSocket"
      }));
    } catch (err) {
      logger.error("Error sending connection confirmation: " + err.message);
    }
  }
};

const handleJoin = async (ws, data) => {
  const { userId, roomType } = data;
  if (!userId || !roomType) {
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({ type: "error", message: "Missing userId or roomType." }));
    }
    return;
  }
  try {
    const joinResult = await LudoService.joinRoom(userId, roomType);
    ws.userId = userId;
    ws.roomId = joinResult.roomId;
    userSockets.set(userId, ws);
    logger.info(`User ${userId} joined room ${joinResult.roomId}`);
    // Broadcast to all players in room (fetch from DB)
    await broadcastRoomState(joinResult.roomId);
    await tryStartGame(joinResult.roomId);
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({ type: "joined", ...joinResult }));
    }
  } catch (err) {
    logger.error("handleJoin error: " + err.message);
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({ type: "error", message: err.message }));
    }
  }
};

const handleMove = async (ws, data) => {
  const { userId, roomId, tokenId, newPosition, diceRoll } = data;
  if (!userId || !roomId || !tokenId || newPosition === undefined || diceRoll === undefined) {
    ws.send(JSON.stringify({ type: "error", message: "Missing move parameters." }));
    return;
  }
  try {
    // Fetch room and player state from DB
    const roomSql = `SELECT * FROM ludo_rooms WHERE id = ? AND status = 'active'`;
    const roomRows = await LudoService.dbQuery.queryRunner(roomSql, [roomId]);
    if (!roomRows.length) throw new Error("Room not found or not active.");
    const room = roomRows[0];
    // Get all players
    const playersSql = `SELECT * FROM ludo_room_players WHERE room_id = ? AND status IN ('joined', 'active', 'disconnected') ORDER BY id ASC`;
    const players = await LudoService.dbQuery.queryRunner(playersSql, [roomId]);
    // Get current turn
    const turnKey = `ludo:turn:${roomId}`;
    const currentTurn = await redisClient.get(turnKey);
    if (String(userId) !== String(currentTurn)) {
      ws.send(JSON.stringify({ type: "error", message: "Not your turn." }));
    return;
  }
    // Get tokens state from Redis
    const tokensKey = `ludo:tokens:${roomId}`;
    let tokens = await redisClient.get(tokensKey);
    tokens = tokens ? JSON.parse(tokens) : {};
    if (!tokens[userId] || tokens[userId][tokenId] === undefined) {
      ws.send(JSON.stringify({ type: "error", message: "Invalid token." }));
    return;
  }
    // Validate move (basic: must match dice, must not overlap own tokens, etc.)
    const currentPosition = tokens[userId][tokenId];
  let validatedNewPosition = newPosition;
  if (currentPosition === 0 && diceRoll !== 6) {
      ws.send(JSON.stringify({ type: "error", message: "You must roll a 6 to move out of start." }));
    return;
  }
  if (currentPosition === 0 && diceRoll === 6) {
    validatedNewPosition = 1;
  } else {
    const expectedPosition = currentPosition + diceRoll;
    if (expectedPosition !== newPosition) {
        ws.send(JSON.stringify({ type: "error", message: `Invalid move: expected ${expectedPosition}, got ${newPosition}.` }));
      return;
    }
  }
    // No overlap with own tokens
    if (Object.entries(tokens[userId]).some(([tid, pos]) => tid !== tokenId && pos === validatedNewPosition)) {
      ws.send(JSON.stringify({ type: "error", message: "Another token of same color at this position." }));
      return;
    }
    // Handle cut (send opponent back to start)
  let cut = null;
    for (const p of players) {
      if (String(p.user_id) === String(userId)) continue;
      for (const [opponentTokenId, pos] of Object.entries(tokens[p.user_id] || {})) {
        if (pos === validatedNewPosition && validatedNewPosition <= 52) {
          tokens[p.user_id][opponentTokenId] = 0;
          cut = { userId: p.user_id, tokenId: opponentTokenId };
        }
      }
    }
    // Update token position
    tokens[userId][tokenId] = validatedNewPosition;
    await redisClient.set(tokensKey, JSON.stringify(tokens));
    // Log move in DB
    await LudoService.logMove(room.game_id, userId, diceRoll, tokens);
    // Broadcast move
    await broadcastRoomState(roomId);
    // Check win
    const playerWon = Object.values(tokens[userId]).every((pos) => pos >= 53);
    if (playerWon) {
      // Payout
      const winnerAmount = room.challenge_amount * players.length;
      await LudoService.addMoney(userId, winnerAmount, room.game_id);
      // Mark game complete
      const updateRoomSql = `UPDATE ludo_rooms SET status = 'completed' WHERE id = ?`;
      await LudoService.dbQuery.queryRunner(updateRoomSql, [roomId]);
      // Broadcast game over
      for (const p of players) {
        const wsP = userSockets.get(p.user_id);
        if (wsP && wsP.readyState === wsP.OPEN) {
          wsP.send(JSON.stringify({ type: "game-over", winner: userId, winnerAmount }));
  }
      }
      return;
    }
    // Next turn
    let nextIdx = players.findIndex(p => String(p.user_id) === String(userId));
    let attempts = 0;
    let nextTurn = null;
    while (attempts < players.length) {
      nextIdx = (nextIdx + 1) % players.length;
      if (["joined", "active", "disconnected"].includes(players[nextIdx].status)) {
        nextTurn = players[nextIdx].user_id;
        break;
      }
      attempts++;
    }
    await redisClient.set(turnKey, nextTurn);
    // Broadcast next turn
    for (const p of players) {
      const wsP = userSockets.get(p.user_id);
      if (wsP && wsP.readyState === wsP.OPEN) {
        wsP.send(JSON.stringify({ type: "turn", currentTurn: nextTurn }));
      }
    }
    // Start 10s timer for next turn (auto-roll logic can be added here)
    await setTurnTimer(roomId, nextTurn);
  } catch (err) {
    logger.error("handleMove error: " + err.message);
    ws.send(JSON.stringify({ type: "error", message: err.message }));
  }
};

const handleQuit = async (ws, data) => {
  const { userId } = data;
  if (!userId) {
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({ type: "error", message: "Missing userId." }));
    }
    return;
  }
  
  // If user is not in a room, just disconnect gracefully
  if (!ws.roomId) {
    logger.info(`User ${userId} quit without being in a room`);
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({ type: "quit_success", message: "Disconnected successfully." }));
    }
    // Clean up and close connection
    userSockets.delete(userId);
    ws.close(1000, 'User quit');
    return;
  }
  
  try {
    // Import the query runner from LudoService
    const { queryRunner } = require("../dbConfig/dbconnection");
    
    // Mark as quit in DB
    const updateSql = `UPDATE ludo_room_players SET status = 'quit' WHERE user_id = ? AND room_id = ?`;
    await queryRunner(updateSql, [userId, ws.roomId]);
    
    // Remove session from Redis
    await redisClient.del(`ludo:session:${userId}`);
    
    // Check if only one player left
    const playersSql = `SELECT * FROM ludo_room_players WHERE room_id = ? AND status IN ('joined', 'active', 'disconnected')`;
    const players = await queryRunner(playersSql, [ws.roomId]);
    
    if (players.length === 1) {
      // Declare winner and payout
      const winnerId = players[0].user_id;
      const roomSql = `SELECT * FROM ludo_rooms WHERE id = ?`;
      const roomRows = await queryRunner(roomSql, [ws.roomId]);
      const room = roomRows[0];
      const winnerAmount = room.challenge_amount * 4;
      await LudoService.addMoney(winnerId, winnerAmount, room.game_id);
      const updateRoomSql = `UPDATE ludo_rooms SET status = 'completed' WHERE id = ?`;
      await queryRunner(updateRoomSql, [ws.roomId]);
      
      // Broadcast game over
      for (const p of players) {
        const wsP = userSockets.get(p.user_id);
        if (wsP && wsP.readyState === wsP.OPEN) {
          wsP.send(JSON.stringify({ type: "game-over", winner: winnerId, winnerAmount }));
        }
      }
      logger.info(`Player ${winnerId} won the game in room ${ws.roomId} (others quit)`);
      return;
    }
    
    // Broadcast player left
    await broadcastRoomState(ws.roomId);
    logger.info(`User ${userId} quit room ${ws.roomId}`);
    
    // Send quit confirmation
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({ type: "quit_success", message: "Left room successfully." }));
    }
    
    // Clean up and close connection
    userSockets.delete(userId);
    ws.close(1000, 'User quit');
    
  } catch (err) {
    logger.error("handleQuit error: " + err.message);
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({ type: "error", message: err.message }));
    }
  }
};

const handleRejoin = async (ws, data) => {
  const { userId, rejoinToken } = data;
  if (!userId || !rejoinToken) {
    ws.send(JSON.stringify({ type: "error", message: "Missing userId or rejoinToken." }));
    return;
  }
  try {
    // Validate session in Redis
    const sessionStr = await redisClient.get(`ludo:session:${userId}`);
    if (!sessionStr) throw new Error("No session found for rejoin.");
    const session = JSON.parse(sessionStr);
    if (session.rejoinToken !== rejoinToken) throw new Error("Invalid rejoin token.");
    // Mark player as active in DB
    const updateSql = `UPDATE ludo_room_players SET status = 'active' WHERE user_id = ? AND room_id = ?`;
    await LudoService.dbQuery.queryRunner(updateSql, [userId, session.roomId]);
    ws.userId = userId;
    ws.roomId = session.roomId;
    userSockets.set(userId, ws);
    logger.info(`User ${userId} rejoined room ${session.roomId}`);
    await broadcastRoomState(session.roomId);
    ws.send(JSON.stringify({ type: "rejoined", roomId: session.roomId }));
  } catch (err) {
    logger.error("handleRejoin error: " + err.message);
    ws.send(JSON.stringify({ type: "error", message: err.message }));
  }
};

const handleDisconnect = async (ws) => {
  if (!ws.userId || !ws.roomId) return;
  try {
    // Mark as disconnected in DB
    const updateSql = `UPDATE ludo_room_players SET status = 'disconnected', last_active = NOW() WHERE user_id = ? AND room_id = ?`;
    await LudoService.dbQuery.queryRunner(updateSql, [ws.userId, ws.roomId]);
    // Start 2-min timer in Redis
    await redisClient.set(`ludo:disconnect:${ws.userId}`, '1', 'EX', 120);
    logger.info(`User ${ws.userId} disconnected from room ${ws.roomId}`);
    // After 2 min, check if still disconnected, mark as quit
    setTimeout(async () => {
      const stillDisconnected = await redisClient.get(`ludo:disconnect:${ws.userId}`);
      if (stillDisconnected) {
        const quitSql = `UPDATE ludo_room_players SET status = 'quit' WHERE user_id = ? AND room_id = ?`;
        await LudoService.dbQuery.queryRunner(quitSql, [ws.userId, ws.roomId]);
        await redisClient.del(`ludo:disconnect:${ws.userId}`);
        logger.info(`User ${ws.userId} auto-quit after disconnect timeout.`);
        await broadcastRoomState(ws.roomId);
      }
    }, 120 * 1000);
    await broadcastRoomState(ws.roomId);
        } catch (err) {
    logger.error("handleDisconnect error: " + err.message);
  }
};

const broadcastToRoom = (roomId, message) => {
  const room = rooms.get(roomId);
  if (room) {
    room.players.forEach((playerId) => {
      const ws = userConnections.get(playerId);
      if (ws && ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify(message));
      }
    });
  }
};

async function broadcastRoomState(roomId) {
  try {
    // Fetch all players in room
    const sql = `SELECT user_id, username, color, status FROM ludo_room_players WHERE room_id = ?`;
    const players = await LudoService.dbQuery.queryRunner(sql, [roomId]);
    for (const player of players) {
      const ws = userSockets.get(player.user_id);
      if (ws && ws.readyState === ws.OPEN) {
        try {
          ws.send(JSON.stringify({ type: "room_state", roomId, players }));
        } catch (sendErr) {
          logger.error(`Error sending room state to user ${player.user_id}: ${sendErr.message}`);
          // Remove dead connection
          userSockets.delete(player.user_id);
        }
      }
    }
  } catch (err) {
    logger.error("Error in broadcastRoomState: " + err.message);
  }
}

// --- GAME START LOGIC ---
async function tryStartGame(roomId) {
  try {
    // Fetch all players in room
    const playersSql = `SELECT * FROM ludo_room_players WHERE room_id = ? AND status IN ('joined', 'active') ORDER BY id ASC`;
    const players = await LudoService.dbQuery.queryRunner(playersSql, [roomId]);
    if (players.length !== 4) return; // Only start when 4
    // Assign colors (R, B, G, Y)
    const colors = ['R', 'B', 'G', 'Y'];
    for (let i = 0; i < players.length; i++) {
      const updateColorSql = `UPDATE ludo_room_players SET color = ? WHERE id = ?`;
      await LudoService.dbQuery.queryRunner(updateColorSql, [colors[i], players[i].id]);
    }
    // Initialize tokens in Redis
    const tokens = {};
    for (let i = 0; i < players.length; i++) {
      const userId = players[i].user_id;
      const color = colors[i];
      tokens[userId] = {
        [`${color}1`]: 0,
        [`${color}2`]: 0,
        [`${color}3`]: 0,
        [`${color}4`]: 0,
      };
    }
    const tokensKey = `ludo:tokens:${roomId}`;
    await redisClient.set(tokensKey, JSON.stringify(tokens));
    // Create game in DB
    const roomSql = `SELECT * FROM ludo_rooms WHERE id = ?`;
    const roomRows = await LudoService.dbQuery.queryRunner(roomSql, [roomId]);
    const room = roomRows[0];
    const playerIds = players.map(p => p.user_id);
    const gameId = await LudoService.logGame(playerIds, room.challenge_amount);
    // Update room with gameId and set status to active
    const updateRoomSql = `UPDATE ludo_rooms SET game_id = ?, status = 'active' WHERE id = ?`;
    await LudoService.dbQuery.queryRunner(updateRoomSql, [gameId, roomId]);
    // Deduct money for paid games
    if (room.challenge_amount > 0) {
      await LudoService.deductMoneyForGame(gameId);
    }
    // Set turn in Redis (first player)
    const turnKey = `ludo:turn:${roomId}`;
    await redisClient.set(turnKey, playerIds[0]);
    // Broadcast game start
    for (const p of players) {
      const wsP = userSockets.get(p.user_id);
      if (wsP && wsP.readyState === wsP.OPEN) {
        wsP.send(JSON.stringify({
          type: "start",
          roomId,
          gameId,
          players: playerIds,
          challengeAmount: room.challenge_amount,
          tokenPositions: tokens,
          currentTurn: playerIds[0],
          playerColors: colors.reduce((acc, c, i) => { acc[playerIds[i]] = c; return acc; }, {})
        }));
      }
    }
    logger.info(`Game started in room ${roomId} (gameId ${gameId})`);
  } catch (err) {
    logger.error("tryStartGame error: " + err.message);
  }
}

// --- TURN TIMER AND AUTO-ROLL LOGIC ---
async function setTurnTimer(roomId, userId) {
  const timerKey = `ludo:turntimer:${roomId}`;
  // Clear any existing timer
  const oldTimer = await redisClient.get(timerKey);
  if (oldTimer) {
    clearTimeout(Number(oldTimer));
  }
  // Set new timer
  const timer = setTimeout(async () => {
    try {
      // Auto-roll for userId
      logger.info(`Auto-rolling for user ${userId} in room ${roomId}`);
      // Fetch tokens from Redis
      const tokensKey = `ludo:tokens:${roomId}`;
      let tokens = await redisClient.get(tokensKey);
      tokens = tokens ? JSON.parse(tokens) : {};
      // Find a movable token (first at start)
      const userTokens = tokens[userId];
      let tokenId = null;
      for (const [tid, pos] of Object.entries(userTokens)) {
        if (pos === 0) { tokenId = tid; break; }
      }
      if (!tokenId) tokenId = Object.keys(userTokens)[0];
      const diceRoll = Math.floor(Math.random() * 6) + 1;
      // Simulate move
      await handleMove({ userId, roomId, tokenId, newPosition: userTokens[tokenId] + diceRoll, diceRoll, auto: true });
    } catch (err) {
      logger.error("Auto-roll error: " + err.message);
    }
  }, 10000);
  await redisClient.set(timerKey, String(timer));
}

module.exports = setupLudoWebSocket;