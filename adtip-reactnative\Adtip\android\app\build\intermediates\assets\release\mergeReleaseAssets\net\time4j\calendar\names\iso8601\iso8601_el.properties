# months
M(a)_1=Ιαν
M(a)_2=Φεβ
M(a)_3=Μαρ
M(a)_4=Απρ
M(a)_5=Μαΐ
M(a)_6=Ιουν
M(a)_7=Ιουλ
M(a)_8=Αυγ
M(a)_9=Σεπ
M(a)_10=Οκτ
M(a)_11=Νοε
M(a)_12=Δεκ

M(n)_1=Ι
M(n)_2=Φ
M(n)_3=Μ
M(n)_4=Α
M(n)_5=Μ
M(n)_6=Ι
M(n)_7=Ι
M(n)_8=Α
M(n)_9=Σ
M(n)_10=Ο
M(n)_11=Ν
M(n)_12=Δ

M(w)_1=Ιανουαρίου
M(w)_2=Φεβρουαρίου
M(w)_3=Μαρτίου
M(w)_4=Απριλίου
M(w)_5=Μαΐου
M(w)_6=Ιουνίου
M(w)_7=Ιουλίου
M(w)_8=Αυγούστου
M(w)_9=Σεπτεμβρίου
M(w)_10=Οκτωβρίου
M(w)_11=Νοεμβρίου
M(w)_12=Δεκεμβρίου

M(A)_1=Ιαν
M(A)_2=Φεβ
M(A)_3=Μάρ
M(A)_4=Απρ
M(A)_5=Μάι
M(A)_6=Ιούν
M(A)_7=Ιούλ
M(A)_8=Αύγ
M(A)_9=Σεπ
M(A)_10=Οκτ
M(A)_11=Νοέ
M(A)_12=Δεκ

M(N)_1=Ι
M(N)_2=Φ
M(N)_3=Μ
M(N)_4=Α
M(N)_5=Μ
M(N)_6=Ι
M(N)_7=Ι
M(N)_8=Α
M(N)_9=Σ
M(N)_10=Ο
M(N)_11=Ν
M(N)_12=Δ

M(W)_1=Ιανουάριος
M(W)_2=Φεβρουάριος
M(W)_3=Μάρτιος
M(W)_4=Απρίλιος
M(W)_5=Μάιος
M(W)_6=Ιούνιος
M(W)_7=Ιούλιος
M(W)_8=Αύγουστος
M(W)_9=Σεπτέμβριος
M(W)_10=Οκτώβριος
M(W)_11=Νοέμβριος
M(W)_12=Δεκέμβριος

# weekdays
D(a)_1=Δευ
D(a)_2=Τρί
D(a)_3=Τετ
D(a)_4=Πέμ
D(a)_5=Παρ
D(a)_6=Σάβ
D(a)_7=Κυρ

D(n)_1=Δ
D(n)_2=Τ
D(n)_3=Τ
D(n)_4=Π
D(n)_5=Π
D(n)_6=Σ
D(n)_7=Κ

D(s)_1=Δε
D(s)_2=Τρ
D(s)_3=Τε
D(s)_4=Πέ
D(s)_5=Πα
D(s)_6=Σά
D(s)_7=Κυ

D(w)_1=Δευτέρα
D(w)_2=Τρίτη
D(w)_3=Τετάρτη
D(w)_4=Πέμπτη
D(w)_5=Παρασκευή
D(w)_6=Σάββατο
D(w)_7=Κυριακή

D(A)_1=Δευ
D(A)_2=Τρί
D(A)_3=Τετ
D(A)_4=Πέμ
D(A)_5=Παρ
D(A)_6=Σάβ
D(A)_7=Κυρ

D(N)_1=Δ
D(N)_2=Τ
D(N)_3=Τ
D(N)_4=Π
D(N)_5=Π
D(N)_6=Σ
D(N)_7=Κ

D(S)_1=Δε
D(S)_2=Τρ
D(S)_3=Τε
D(S)_4=Πέ
D(S)_5=Πα
D(S)_6=Σά
D(S)_7=Κυ

D(W)_1=Δευτέρα
D(W)_2=Τρίτη
D(W)_3=Τετάρτη
D(W)_4=Πέμπτη
D(W)_5=Παρασκευή
D(W)_6=Σάββατο
D(W)_7=Κυριακή

# quarters
Q(a)_1=Τ1
Q(a)_2=Τ2
Q(a)_3=Τ3
Q(a)_4=Τ4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1ο τρίμηνο
Q(w)_2=2ο τρίμηνο
Q(w)_3=3ο τρίμηνο
Q(w)_4=4ο τρίμηνο

Q(A)_1=Τ1
Q(A)_2=Τ2
Q(A)_3=Τ3
Q(A)_4=Τ4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1ο τρίμηνο
Q(W)_2=2ο τρίμηνο
Q(W)_3=3ο τρίμηνο
Q(W)_4=4ο τρίμηνο

# day-period-rules
T0400=morning1
T1200=afternoon1
T1700=evening1
T2000=night1

# day-period-translations
P(a)_am=π.μ.
P(a)_pm=μ.μ.
P(a)_morning1=πρωί
P(a)_afternoon1=μεσημ.
P(a)_evening1=απόγ.
P(a)_night1=βράδυ

P(n)_am=πμ
P(n)_pm=μμ
P(n)_morning1=πρωί
P(n)_afternoon1=μεσημ.
P(n)_evening1=απόγ.
P(n)_night1=βράδυ

P(w)_am=π.μ.
P(w)_pm=μ.μ.
P(w)_morning1=πρωί
P(w)_afternoon1=μεσημέρι
P(w)_evening1=απόγευμα
P(w)_night1=βράδυ

P(A)_am=π.μ.
P(A)_pm=μ.μ.
P(A)_morning1=πρωί
P(A)_afternoon1=μεσημ.
P(A)_evening1=απόγ.
P(A)_night1=βράδυ

P(N)_am=πμ
P(N)_pm=μμ
P(N)_morning1=πρωί
P(N)_afternoon1=μεσημ.
P(N)_evening1=απόγ.
P(N)_night1=βράδυ

P(W)_am=π.μ.
P(W)_pm=μ.μ.
P(W)_morning1=πρωί
P(W)_afternoon1=μεσημέρι
P(W)_evening1=απόγευμα
P(W)_night1=βράδυ

# eras
E(w)_0=προ Χριστού
E(w|alt)_0=πριν από την Κοινή Χρονολογία
E(w)_1=μετά Χριστόν
E(w|alt)_1=Κοινή Χρονολογία

E(a)_0=π.Χ.
E(a|alt)_0=π.Κ.Χ.
E(a)_1=μ.Χ.
E(a|alt)_1=ΚΧ

# format patterns
F(f)_d=EEEE, d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=d/M/yy

F(alt)=h:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a

F(f)_dt={1} - {0}
F(l)_dt={1} - {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=LLLL y
F_yQQQ=y QQQ
F_yQQQQ=y QQQQ
F_yw=εβδομάδα w του Y

I={0} - {1}

# labels of elements
L_era=περίοδος
L_year=έτος
L_quarter=τρίμηνο
L_month=μήνας
L_week=εβδομάδα
L_day=ημέρα
L_weekday=καθημερινή
L_dayperiod=π.μ./μ.μ.
L_hour=ώρα
L_minute=λεπτό
L_second=δευτερόλεπτο
L_zone=ζώνη ώρας
