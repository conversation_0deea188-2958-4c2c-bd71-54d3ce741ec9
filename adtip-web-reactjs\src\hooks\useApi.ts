import { useQuery, useMutation, useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { contentAPI, userAPI, authAPI } from '../services/api';
import { Post, PostListResponse, WalletResponse } from '../types/api';

// Query Keys
export const queryKeys = {
  posts: {
    all: ['posts'] as const,
    lists: () => [...queryKeys.posts.all, 'list'] as const,
    list: (params: Record<string, unknown>) => [...queryKeys.posts.lists(), params] as const,
    premium: () => [...queryKeys.posts.all, 'premium'] as const,
    single: (id: string) => [...queryKeys.posts.all, 'single', id] as const,
    comments: (postId: string) => [...queryKeys.posts.all, 'comments', postId] as const,
  },
  user: {
    all: ['user'] as const,
    wallet: (userId: string) => [...queryKeys.user.all, 'wallet', userId] as const,
    channel: (userId: string) => [...queryKeys.user.all, 'channel', userId] as const,
    analytics: (channelId: string) => [...queryKeys.user.all, 'analytics', channelId] as const,
  },
  videos: {
    all: ['videos'] as const,
    list: (userId: string, categoryId: string, offset: string) => 
      [...queryKeys.videos.all, 'list', userId, categoryId, offset] as const,
  },
  shorts: {
    all: ['shorts'] as const,
    user: (userId: string) => [...queryKeys.shorts.all, 'user', userId] as const,
    public: () => [...queryKeys.shorts.all, 'public'] as const,
  },
};

// Posts Hooks
export const usePosts = (
  category: number = 0,
  userId?: number,
  options?: { enabled?: boolean }
) => {
  return useInfiniteQuery({
    queryKey: queryKeys.posts.list({ category, userId }),
    queryFn: async ({ pageParam = 1 }) => {
      const response = await contentAPI.listPosts({
        category,
        page: pageParam as number,
        limit: 10,
        loggined_user_id: userId || 0,
      });
      return response.data;
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      if (lastPage?.pagination?.current_page < lastPage?.pagination?.total_page) {
        return lastPage.pagination.current_page + 1;
      }
      return undefined;
    },
    enabled: options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
};

export const usePremiumPosts = (options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: queryKeys.posts.premium(),
    queryFn: async () => {
      const response = await contentAPI.listPremiumPosts({
        page: 1,
        limit: 20,
      });
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: 10 * 60 * 1000, // 10 minutes for premium posts
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
};

export const usePost = (postId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: queryKeys.posts.single(postId),
    queryFn: async () => {
      const response = await contentAPI.getSinglePost(postId);
      return response.data;
    },
    enabled: !!postId && options?.enabled !== false,
    staleTime: 5 * 60 * 1000,
  });
};

export const usePostComments = (postId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: queryKeys.posts.comments(postId),
    queryFn: async () => {
      const response = await contentAPI.getPostComments(postId);
      return response.data;
    },
    enabled: !!postId && options?.enabled !== false,
    staleTime: 2 * 60 * 1000, // 2 minutes for comments
  });
};

// User Hooks
export const useWalletBalance = (userId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: queryKeys.user.wallet(userId),
    queryFn: async () => {
      const response = await userAPI.getWalletBalance(userId);
      return response.data;
    },
    enabled: !!userId && options?.enabled !== false,
    staleTime: 30 * 1000, // 30 seconds for wallet
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

export const useChannel = (userId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: queryKeys.user.channel(userId),
    queryFn: async () => {
      const response = await userAPI.getChannel(userId);
      return response.data;
    },
    enabled: !!userId && options?.enabled !== false,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useAnalytics = (channelId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: queryKeys.user.analytics(channelId),
    queryFn: async () => {
      const response = await userAPI.getAnalytics(channelId);
      return response.data;
    },
    enabled: !!channelId && options?.enabled !== false,
    staleTime: 5 * 60 * 1000,
  });
};

// Videos Hooks
export const useVideos = (
  userId: string,
  categoryId: string = "0",
  offset: string = "1",
  options?: { enabled?: boolean }
) => {
  return useQuery({
    queryKey: queryKeys.videos.list(userId, categoryId, offset),
    queryFn: async () => {
      const response = await contentAPI.getVideos(userId, categoryId, offset);
      return response.data;
    },
    enabled: !!userId && options?.enabled !== false,
    staleTime: 5 * 60 * 1000,
  });
};

// Shorts Hooks
export const useShorts = (userId: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: queryKeys.shorts.user(userId),
    queryFn: async () => {
      const response = await contentAPI.getShorts(userId);
      return response.data;
    },
    enabled: !!userId && options?.enabled !== false,
    staleTime: 5 * 60 * 1000,
  });
};

export const usePublicShorts = (options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: queryKeys.shorts.public(),
    queryFn: async () => {
      const response = await contentAPI.getPublicShorts();
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: 10 * 60 * 1000, // 10 minutes for public content
  });
};

// Mutations
export const useCreatePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: contentAPI.createPost,
    onSuccess: () => {
      // Invalidate and refetch posts
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.all });
    },
    onError: (error) => {
      console.error('Create post error:', error);
    },
  });
};

export const useLikePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: contentAPI.likePost,
    onMutate: async (variables) => {
      // Optimistic update
      const queryKey = queryKeys.posts.all;
      await queryClient.cancelQueries({ queryKey });

      const previousData = queryClient.getQueryData(queryKey);

      // Update the cache optimistically
      queryClient.setQueriesData({ queryKey }, (oldData: any) => {
        if (!oldData) return oldData;
        
        // Handle infinite query data structure
        if (oldData.pages) {
          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              data: page.data?.map((post: Post) => 
                post.id === variables.post_id
                  ? { 
                      ...post, 
                      is_liked: variables.is_liked,
                      total_likes: variables.is_liked 
                        ? (post.total_likes || 0) + 1 
                        : Math.max((post.total_likes || 0) - 1, 0)
                    }
                  : post
              )
            }))
          };
        }

        // Handle regular query data
        if (Array.isArray(oldData.data)) {
          return {
            ...oldData,
            data: oldData.data.map((post: Post) => 
              post.id === variables.post_id
                ? { 
                    ...post, 
                    is_liked: variables.is_liked,
                    total_likes: variables.is_liked 
                      ? (post.total_likes || 0) + 1 
                      : Math.max((post.total_likes || 0) - 1, 0)
                  }
                : post
            )
          };
        }

        return oldData;
      });

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(queryKeys.posts.all, context.previousData);
      }
    },
    onSettled: () => {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.all });
    },
  });
};

export const useCommentOnPost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: contentAPI.commentOnPost,
    onSuccess: (data, variables) => {
      // Invalidate comments for this post
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.posts.comments(variables.post_id.toString()) 
      });
      // Also invalidate posts to update comment count
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.all });
    },
    onError: (error) => {
      console.error('Comment post error:', error);
    },
  });
};
