{"name": "react-native-get-random-values", "version": "1.11.0", "summary": "getRandomValues for React Native", "description": "A small implementation of `getRandomValues` for React Native.", "homepage": "https://github.com/LinusU/react-native-get-random-values", "license": "MIT", "authors": {"Linus Unnebäck": "<EMAIL>"}, "platforms": {"ios": "9.0", "tvos": "9.0", "osx": "10.14"}, "source": {"git": "https://github.com/LinusU/react-native-get-random-values.git", "tag": "v1.11.0"}, "source_files": "ios/**/*.{h,m,swift}", "requires_arc": true, "dependencies": {"React-Core": []}}