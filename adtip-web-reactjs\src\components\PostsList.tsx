import React, { memo, useRef, useEffect } from 'react';
import { Post } from '../types/api';
import PostCard from './PostCard';
import { LoadingSpinner } from './LoadingSpinner';
import { RectangleAdComponent, shouldShowAd } from './ads';

interface PostsListProps {
  posts: Post[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  onPostClick: (id: number) => void;
  onLoadMore: () => void;
}

const PostsList = memo(({ 
  posts, 
  loading, 
  error, 
  hasMore, 
  onPostClick, 
  onLoadMore 
}: PostsListProps) => {
  const observerTarget = useRef<HTMLDivElement>(null);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const currentTarget = observerTarget.current;
    if (!currentTarget) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading && !error) {
          onLoadMore();
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(currentTarget);

    return () => {
      observer.disconnect();
    };
  }, [hasMore, loading, error, onLoadMore]);

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-red-800 mb-2">Something went wrong</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!loading && posts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No posts yet</h3>
          <p className="text-gray-600 mb-6">
            Be the first to share something amazing with the community!
          </p>
          <button className="bg-adtip-teal text-white px-6 py-3 rounded-lg hover:bg-adtip-teal/90 transition-colors">
            Create Post
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 md:space-y-8 max-w-2xl mx-auto px-4">
      {posts.map((post, index) => (
        <React.Fragment key={post.id}>
          <PostCard post={post} onPostClick={onPostClick} />

          {/* Rectangle ad after every 3rd post (starting from post 2) */}
          {shouldShowAd(index, 'rectangle') && (
            <div className="my-8 flex justify-center">
              <RectangleAdComponent />
            </div>
          )}
        </React.Fragment>
      ))}

      {/* Loading indicator and infinite scroll trigger */}
      <div ref={observerTarget} className="flex justify-center py-4">
        {loading && <LoadingSpinner size="md" text="Loading more posts..." />}
        {!hasMore && posts.length > 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500 text-sm">You've reached the end! 🎉</p>
          </div>
        )}
      </div>
    </div>
  );
});

PostsList.displayName = 'PostsList';

export default PostsList;
