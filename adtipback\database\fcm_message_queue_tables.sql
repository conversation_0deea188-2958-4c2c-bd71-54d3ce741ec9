-- =====================================================
-- FCM MESSAGE QUEUE TABLES
-- =====================================================
-- This script creates tables for FCM message queue tracking
-- Compatible with MySQL 8.0 and prepared statement protocol
-- Based on schema from Dump20250717/
-- Run this script to add FCM message queue support
--
-- IMPORTANT: This script is designed to be idempotent and safe to run multiple times
-- It uses IF NOT EXISTS and ADD COLUMN IF NOT EXISTS to prevent errors

-- =====================================================
-- 1. MESSAGE DELIVERY FAILURES TABLE
-- =====================================================
-- Tracks failed message deliveries for analysis and debugging
CREATE TABLE IF NOT EXISTS message_delivery_failures (
    id INT NOT NULL AUTO_INCREMENT,
    message_id INT NOT NULL,
    conversation_id INT NOT NULL,
    recipient_id INT NOT NULL,
    failure_reason VARCHAR(255) COLLATE utf8mb4_unicode_ci NOT NULL,
    attempts INT NOT NULL DEFAULT 0,
    failed_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    resolved TINYINT DEFAULT 0,
    resolved_at TIMESTAMP NULL DEFAULT NULL,

    PRIMARY KEY (id),
    KEY idx_message_id (message_id),
    KEY idx_conversation_id (conversation_id),
    KEY idx_recipient_id (recipient_id),
    KEY idx_failed_at (failed_at),
    KEY idx_resolved (resolved),

    CONSTRAINT fk_mdf_message_id FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    CONSTRAINT fk_mdf_conversation_id FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    CONSTRAINT fk_mdf_recipient_id FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. MESSAGE DELIVERY STATS TABLE
-- =====================================================
-- Tracks message delivery statistics for analytics
CREATE TABLE IF NOT EXISTS message_delivery_stats (
    id INT NOT NULL AUTO_INCREMENT,
    date DATE NOT NULL,
    total_messages INT NOT NULL DEFAULT 0,
    successful_deliveries INT NOT NULL DEFAULT 0,
    failed_deliveries INT NOT NULL DEFAULT 0,
    retry_count INT NOT NULL DEFAULT 0,
    avg_delivery_time_ms INT NULL DEFAULT NULL,

    PRIMARY KEY (id),
    UNIQUE KEY unique_date (date),
    KEY idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. ADD DELIVERY TRACKING COLUMNS TO MESSAGES TABLE
-- =====================================================
-- Add columns to track FCM delivery status (safe to run multiple times)

-- Add delivery_status column (ignore error if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'messages'
     AND table_schema = DATABASE()
     AND column_name = 'delivery_status') > 0,
    'SELECT "delivery_status column already exists" AS info',
    'ALTER TABLE messages ADD COLUMN delivery_status ENUM(''pending'', ''sent'', ''delivered'', ''failed'') COLLATE utf8mb4_unicode_ci DEFAULT ''pending'' AFTER content'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add delivery_attempts column (ignore error if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'messages'
     AND table_schema = DATABASE()
     AND column_name = 'delivery_attempts') > 0,
    'SELECT "delivery_attempts column already exists" AS info',
    'ALTER TABLE messages ADD COLUMN delivery_attempts INT DEFAULT 0 AFTER delivery_status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add last_delivery_attempt column (ignore error if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'messages'
     AND table_schema = DATABASE()
     AND column_name = 'last_delivery_attempt') > 0,
    'SELECT "last_delivery_attempt column already exists" AS info',
    'ALTER TABLE messages ADD COLUMN last_delivery_attempt TIMESTAMP NULL DEFAULT NULL AFTER delivery_attempts'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add delivered_at column (ignore error if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'messages'
     AND table_schema = DATABASE()
     AND column_name = 'delivered_at') > 0,
    'SELECT "delivered_at column already exists" AS info',
    'ALTER TABLE messages ADD COLUMN delivered_at TIMESTAMP NULL DEFAULT NULL AFTER last_delivery_attempt'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add fcm_message_id column (ignore error if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'messages'
     AND table_schema = DATABASE()
     AND column_name = 'fcm_message_id') > 0,
    'SELECT "fcm_message_id column already exists" AS info',
    'ALTER TABLE messages ADD COLUMN fcm_message_id VARCHAR(255) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER delivered_at'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add indexes for delivery status queries (ignore error if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'messages'
     AND table_schema = DATABASE()
     AND index_name = 'idx_delivery_status') > 0,
    'SELECT "idx_delivery_status index already exists" AS info',
    'ALTER TABLE messages ADD INDEX idx_delivery_status (delivery_status)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'messages'
     AND table_schema = DATABASE()
     AND index_name = 'idx_delivery_attempts') > 0,
    'SELECT "idx_delivery_attempts index already exists" AS info',
    'ALTER TABLE messages ADD INDEX idx_delivery_attempts (delivery_attempts)'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 4. ADD FCM TOKEN TRACKING TO USERS TABLE
-- =====================================================
-- Add FCM token platform tracking (safe to run multiple times)
-- Note: Based on schema dump, fcm_token_updation_date already exists in users table

-- Add fcm_token_platform column (ignore error if exists)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'users'
     AND table_schema = DATABASE()
     AND column_name = 'fcm_token_platform') > 0,
    'SELECT "fcm_token_platform column already exists" AS info',
    'ALTER TABLE users ADD COLUMN fcm_token_platform VARCHAR(20) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER fcm_token'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- The fcm_token_updation_date column already exists in the schema as fcm_token_updation_date
-- No need to add it again

-- =====================================================
-- 5. CREATE VIEW FOR MESSAGE DELIVERY ANALYSIS
-- =====================================================
CREATE OR REPLACE VIEW message_delivery_analysis AS
SELECT
    m.id AS message_id,
    m.conversation_id,
    m.sender_id,
    sender.name AS sender_name,
    m.content,
    m.delivery_status,
    m.delivery_attempts,
    m.created_at,
    m.last_delivery_attempt,
    m.delivered_at,
    TIMESTAMPDIFF(SECOND, m.created_at, m.delivered_at) AS delivery_time_seconds,
    mdf.failure_reason,
    mdf.attempts AS failure_attempts,
    mdf.failed_at,
    recipient.id AS recipient_id,
    recipient.name AS recipient_name,
    CASE WHEN recipient.fcm_token IS NOT NULL THEN 1 ELSE 0 END AS has_fcm_token
FROM
    messages m
JOIN
    users sender ON m.sender_id = sender.id
JOIN
    conversation_participants cp ON m.conversation_id = cp.conversation_id
JOIN
    users recipient ON cp.user_id = recipient.id
LEFT JOIN
    message_delivery_failures mdf ON m.id = mdf.message_id AND recipient.id = mdf.recipient_id
WHERE
    recipient.id != m.sender_id
ORDER BY
    m.created_at DESC;

-- =====================================================
-- 6. CREATE PROCEDURE TO CLEAN UP OLD FAILURES
-- =====================================================
DELIMITER $$

DROP PROCEDURE IF EXISTS cleanup_old_message_failures$$

CREATE PROCEDURE cleanup_old_message_failures()
BEGIN
    -- Mark old failures as resolved
    UPDATE message_delivery_failures
    SET resolved = 1, resolved_at = NOW()
    WHERE resolved = 0
    AND failed_at < DATE_SUB(NOW(), INTERVAL 7 DAY);

    -- Delete very old failures (older than 30 days)
    DELETE FROM message_delivery_failures
    WHERE failed_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
END$$

DELIMITER ;

-- Create event to run cleanup procedure daily (drop first if exists)
DROP EVENT IF EXISTS daily_message_failure_cleanup;

CREATE EVENT daily_message_failure_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO CALL cleanup_old_message_failures();

-- =====================================================
-- 7. HELPFUL QUERIES FOR MONITORING
-- =====================================================

-- Query to check FCM message delivery status
-- SELECT delivery_status, COUNT(*) as count FROM messages GROUP BY delivery_status;

-- Query to check recent delivery failures
-- SELECT * FROM message_delivery_failures WHERE failed_at > DATE_SUB(NOW(), INTERVAL 1 DAY) ORDER BY failed_at DESC;

-- Query to check delivery statistics
-- SELECT * FROM message_delivery_stats ORDER BY date DESC LIMIT 7;

-- Query to check users without FCM tokens
-- SELECT COUNT(*) as users_without_fcm FROM users WHERE fcm_token IS NULL;

-- =====================================================
-- SCRIPT COMPLETION
-- =====================================================
SELECT 'FCM Message Queue tables created successfully!' AS status;
