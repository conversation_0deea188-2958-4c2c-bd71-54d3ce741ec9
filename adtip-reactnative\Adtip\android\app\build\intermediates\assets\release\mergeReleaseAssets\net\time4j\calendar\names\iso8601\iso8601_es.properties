# months
M(a)_1=ene.
M(a)_2=feb.
M(a)_3=mar.
M(a)_4=abr.
M(a)_5=may.
M(a)_6=jun.
M(a)_7=jul.
M(a)_8=ago.
M(a)_9=sept.
M(a)_10=oct.
M(a)_11=nov.
M(a)_12=dic.

M(n)_1=E
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=enero
M(w)_2=febrero
M(w)_3=marzo
M(w)_4=abril
M(w)_5=mayo
M(w)_6=junio
M(w)_7=julio
M(w)_8=agosto
M(w)_9=septiembre
M(w)_10=octubre
M(w)_11=noviembre
M(w)_12=diciembre

M(A)_1=ene.
M(A)_2=feb.
M(A)_3=mar.
M(A)_4=abr.
M(A)_5=may.
M(A)_6=jun.
M(A)_7=jul.
M(A)_8=ago.
M(A)_9=sept.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=dic.

M(N)_1=E
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=enero
M(W)_2=febrero
M(W)_3=marzo
M(W)_4=abril
M(W)_5=mayo
M(W)_6=junio
M(W)_7=julio
M(W)_8=agosto
M(W)_9=septiembre
M(W)_10=octubre
M(W)_11=noviembre
M(W)_12=diciembre

# weekdays
D(a)_1=lun.
D(a)_2=mar.
D(a)_3=mié.
D(a)_4=jue.
D(a)_5=vie.
D(a)_6=sáb.
D(a)_7=dom.

D(n)_1=L
D(n)_2=M
D(n)_3=X
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

D(s)_1=LU
D(s)_2=MA
D(s)_3=MI
D(s)_4=JU
D(s)_5=VI
D(s)_6=SA
D(s)_7=DO

D(w)_1=lunes
D(w)_2=martes
D(w)_3=miércoles
D(w)_4=jueves
D(w)_5=viernes
D(w)_6=sábado
D(w)_7=domingo

D(A)_1=lun.
D(A)_2=mar.
D(A)_3=mié.
D(A)_4=jue.
D(A)_5=vie.
D(A)_6=sáb.
D(A)_7=dom.

D(N)_1=L
D(N)_2=M
D(N)_3=X
D(N)_4=J
D(N)_5=V
D(N)_6=S
D(N)_7=D

D(S)_1=LU
D(S)_2=MA
D(S)_3=MI
D(S)_4=JU
D(S)_5=VI
D(S)_6=SA
D(S)_7=DO

D(W)_1=lunes
D(W)_2=martes
D(W)_3=miércoles
D(W)_4=jueves
D(W)_5=viernes
D(W)_6=sábado
D(W)_7=domingo

# quarters
Q(a)_1=T1
Q(a)_2=T2
Q(a)_3=T3
Q(a)_4=T4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1.er trimestre
Q(w)_2=2.º trimestre
Q(w)_3=3.er trimestre
Q(w)_4=4.º trimestre

Q(A)_1=T1
Q(A)_2=T2
Q(A)_3=T3
Q(A)_4=T4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1.er trimestre
Q(W)_2=2.º trimestre
Q(W)_3=3.er trimestre
Q(W)_4=4.º trimestre

# day-period-rules
T0000=morning1
T0600=morning2
T1200=evening1
T2000=night1

# day-period-translations
P(a)_am=a. m.
P(a)_noon=del mediodía
P(a)_pm=p. m.
P(a)_morning1=de la madrugada
P(a)_morning2=de la mañana
P(a)_evening1=de la tarde
P(a)_night1=de la noche

P(n)_am=a. m.
P(n)_noon=del mediodía
P(n)_pm=p. m.
P(n)_morning1=de la madrugada
P(n)_morning2=de la mañana
P(n)_evening1=de la tarde
P(n)_night1=de la noche

P(w)_am=a. m.
P(w)_noon=del mediodía
P(w)_pm=p. m.
P(w)_morning1=de la madrugada
P(w)_morning2=de la mañana
P(w)_evening1=de la tarde
P(w)_night1=de la noche

P(A)_am=a. m.
P(A)_noon=mediodía
P(A)_pm=p. m.
P(A)_morning1=madrugada
P(A)_morning2=mañana
P(A)_evening1=tarde
P(A)_night1=noche

P(N)_am=a. m.
P(N)_noon=mediodía
P(N)_pm=p. m.
P(N)_morning1=madrugada
P(N)_morning2=mañana
P(N)_evening1=tarde
P(N)_night1=noche

P(W)_am=a. m.
P(W)_noon=mediodía
P(W)_pm=p. m.
P(W)_morning1=madrugada
P(W)_morning2=mañana
P(W)_evening1=tarde
P(W)_night1=noche

# eras
E(w)_0=antes de Cristo
E(w|alt)_0=antes de la era común
E(w)_1=después de Cristo
E(w|alt)_1=era común

E(a)_0=a. C.
E(a|alt)_0=a. e. c.
E(a)_1=d. C.
E(a|alt)_1=e. c.

# format patterns
F(f)_d=EEEE, d 'de' MMMM 'de' y
F(l)_d=d 'de' MMMM 'de' y
F(m)_d=d MMM y
F(s)_d=d/M/yy

F(alt)=HH'H'mm''

F(f)_t=H:mm:ss (zzzz)
F(l)_t=H:mm:ss z
F(m)_t=H:mm:ss
F(s)_t=H:mm

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=H
F_hm=h:mm a
F_Hm=H:mm
F_hms=h:mm:ss a
F_Hms=H:mm:ss

F_Md=d/M
F_MMd=d/M
F_MMMd=d MMM
F_MMMMd=d 'de' MMMM
F_y=y
F_yM=M/y
F_yMM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM 'de' y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ 'de' y
F_yw='semana' w 'de' Y

I={0}–{1}

# labels of elements
L_era=era
L_year=año
L_quarter=trimestre
L_month=mes
L_week=semana
L_day=día
L_weekday=día de la semana
L_dayperiod=a. m./p. m.
L_hour=hora
L_minute=minuto
L_second=segundo
L_zone=zona horaria
