{"name": "DoubleConversion", "version": "1.1.6", "license": {"type": "MIT"}, "homepage": "https://github.com/google/double-conversion", "summary": "Efficient binary-decimal and decimal-binary conversion routines for IEEE doubles", "authors": "Google", "prepare_command": "mv src double-conversion", "source": {"git": "https://github.com/google/double-conversion.git", "tag": "v1.1.6"}, "module_name": "DoubleConversion", "header_dir": "double-conversion", "source_files": "double-conversion/*.{h,cc}", "compiler_flags": "-Wno-unreachable-code", "user_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/DoubleConversion\""}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES"}, "platforms": {"ios": "15.1"}}