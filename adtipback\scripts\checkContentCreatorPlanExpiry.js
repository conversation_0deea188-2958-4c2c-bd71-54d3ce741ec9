const dbQuery = require("../dbConfig/queryRunner");
const moment = require("moment");

const checkContentCreatorPlanExpiry = async () => {
  try {
    const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    // Find expired active content premium plans
    const expiredPlansQuery = `
      SELECT id, user_id, plan_id 
      FROM user_content_premium_plans 
      WHERE status = 'active' AND end_time < '${currentTime}'
    `;
    const expiredPlans = await dbQuery.queryRunner(expiredPlansQuery);

    for (const plan of expiredPlans) {
      // Mark plan as expired
      await dbQuery.queryRunner(`
        UPDATE user_content_premium_plans 
        SET status = 'expired', is_active = 0 
        WHERE id = ${plan.id}
      `);

      // Check for queued plans
      const queuedPlanQuery = `
        SELECT ucpp.id, ucpp.plan_id, ccp.duration_in_months
        FROM user_content_premium_plans ucpp
        JOIN content_creator_plans ccp ON ucpp.plan_id = ccp.subscription_id
        WHERE ucpp.user_id = ${plan.user_id} AND ucpp.status = 'queued'
        ORDER BY ucpp.created_at ASC
        LIMIT 1
      `;
      const queuedPlan = await dbQuery.queryRunner(queuedPlanQuery);

      if (queuedPlan.length) {
        // Activate the next queued plan
        const endTime = moment()
          .add(queuedPlan[0].duration_in_months, "months")
          .utcOffset(330)
          .format("YYYY-MM-DD HH:mm:ss");
        await dbQuery.queryRunner(`
          UPDATE user_content_premium_plans 
          SET status = 'active', is_active = 1, start_time = '${currentTime}', end_time = '${endTime}'
          WHERE id = ${queuedPlan[0].id}
        `);

        // Update users table
        await dbQuery.queryRunner(`
          UPDATE users SET content_creator_plan_id = ${queuedPlan[0].plan_id} WHERE id = ${plan.user_id}
        `);
      } else {
        // No queued plans, clear content_creator_plan_id
        await dbQuery.queryRunner(`
          UPDATE users SET content_creator_plan_id = 0 WHERE id = ${plan.user_id}
        `);
      }
    }

    console.log("Content creator plan expiry check completed successfully");
  } catch (error) {
    console.error("Error in checkContentCreatorPlanExpiry:", error);
  }
};

module.exports = { checkContentCreatorPlanExpiry };