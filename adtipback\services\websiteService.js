let dbQuery = require("../dbConfig/queryRunner");
const axios = require("axios");

///AIzaSyB52k6PZDz7O_LnbFRW50xA4VQWbywiink this api <NAME_EMAIL>
///AIzaSyC7-UF5ZiyFhW1nYYLdVTWf8740vAfg0fc this api <NAME_EMAIL>
const googleAPIKey = "AIzaSyB52k6PZDz7O_LnbFRW50xA4VQWbywiink";

let insertQRScanWebData = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO qr_scan_web (company_name, company_id, ad_name, ad_id, created_date, user_name, user_id, mobile_no, upi_id,profession, paid ) VALUES('${data.company_name}', ${data.company_id}, '${data.ad_name}', ${data.ad_id}, NOW(),'${data.user_name}', ${data.user_id}, ${data.mobile_no}, '${data.upi_id}', "${data.profession}", 0)`;
    let sql1 = `SELECT * FROM qr_scan_web WHERE mobile_no=${data.mobile_no} AND ad_id=${data.ad_id}`;
    let sql2 = `SELECT * FROM admodels WHERE id=${data.ad_id}`;

    Promise.all([dbQuery.queryRunner(sql1), dbQuery.queryRunner(sql2)])
      .then((result) => {
        if (result && result.length != 0) {
          var pending_balance = 0;
          let totalViews = 0;
          let totalLikes = 0;

          if (result[1] && result[1].length != 0) {
            pending_balance = result[1][0].pending_ad_balance - 3;
            totalLikes = result[1][0].ad_like ?? 0;
            totalViews = result[1][0].ad_view ?? 0;

            if (pending_balance < 0) {
              dbQuery
                .queryRunner(
                  `Update admodels set is_active=0 WHERE id = ${data.ad_id}`
                )
                .then((result) => {
                  reject({
                    status: 400,
                    message: "Insufficient balance",
                    data: [],
                  });
                })
                .catch((err) => {
                  reject({
                    status: 400,
                    message: "Insufficient balance",
                    data: [],
                  });
                });
              return;
            }
          } else {
            console.log("no ads", pending_balance);
            reject({
              status: 400,
              message: "No ads found",
              data: [],
            });
          }
          if (result[0] && result[0].length != 0) {
            /// user already exist
            reject({
              status: 500,
              message: "You already submitted earlier.",
              data: [],
            });
          } else {
            ///insert into qr_scan_web
            dbQuery
              .queryRunner(sql)
              .then((result) => {
                if (result && result.length != 0) {
                  dbQuery
                    .queryRunner(
                      `Update admodels set ad_view=${totalViews + 1},ad_like=${
                        totalLikes + 1
                      }, pending_ad_balance=${pending_balance} WHERE id = ${
                        data.ad_id
                      }`
                    )
                    .then((result) => {
                      if (result && result.length != 0) {
                        resolve({
                          status: 200,
                          message:
                            "Thank you for submitting, you will get your money within 3 business days",
                          data: [],
                        });
                      }
                    })
                    .catch((err) => {
                      console.log("err", err);
                      reject({
                        status: 500,
                        message: `data not saved ${err}`,
                        data: [],
                      });
                    });
                } else {
                  reject({
                    status: 500,
                    message: "Data not saved",
                    data: [],
                  });
                }
              })
              .catch((err) => {
                reject({
                  status: 500,
                  message: `data not saved ${err}`,
                  data: [],
                });
              });
          }
        } else {
          reject({
            status: 500,
            message: "Data not saved, ",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: `data not saved ${err}`,
          data: [],
        });
      });
  });
};

let saveWebsiteMessage = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO website_message (name, mobile, message,type,createdDate,company_name,country) VALUES('${data.name}', '${data.mobile}', '${data.message}','${data.type}',NOW(),'${data.company_name}', '${data.country}')`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Save data successfully.",
            data: [data],
          });
        } else {
          reject({
            status: 400,
            message: "data not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [data],
        });
      });
  });
};

let getQrAdWebAllForAdmin = () => {
  return new Promise((resolve, reject) => {
    let sql = `select * from qr_scan_web where paid=0`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "data exist",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "data not found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 400,
          message: "something went wrong",
          data: [],
        });
      });
  });
};
let getQrAdWebByAdIdForAdvertiser = (adId) => {
  return new Promise((resolve, reject) => {
    let sql = `select * from qr_scan_web where ad_id=${adId}`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "data exist",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "data not found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 400,
          message: "something went wrong",
          data: [],
        });
      });
  });
};

let checkAdPendingBalance = (data) => {
  return new Promise((resolve, reject) => {
    console.log("ad id", data);
    let sql = `select * from admodels where id=${data}`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          var balance = result[0].pending_ad_balance;
          var adActive = result[0].is_active;
          var adWebsite = result[0].ad_website;

          if (balance <= 0 || adActive === 0) {
            resolve({
              status: 200,
              message: "Out of Ads",
              data: [
                {
                  message: "Out of Ads, download Adtip app from play store",
                  ad_show: 0,
                  pending_ad_balance: balance,
                  ad_active: adActive,
                  ad_website: adWebsite,
                },
              ],
            });
          } else {
            resolve({
              status: 200,
              message: "Ad is live",
              data: [
                {
                  message: "Ad is live",
                  ad_show: 1,
                  pending_ad_balance: balance,
                  ad_active: adActive,
                  ad_website: adWebsite,
                },
              ],
            });
          }
        } else {
          reject({
            status: 400,
            message: "data not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [data],
        });
      });
  });
};

module.exports = {
  getQrAdWebAllForAdmin: () => {
    return new Promise((resolve, reject) => {
      getQrAdWebAllForAdmin()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  getQrAdWebByAdIdForAdvertiser: (adId) => {
    return new Promise((resolve, reject) => {
      getQrAdWebByAdIdForAdvertiser(adId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  insertQRScanWebData: (data) => {
    return new Promise((resolve, reject) => {
      insertQRScanWebData(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  saveWebsiteMessage: (data) => {
    return new Promise((resolve, reject) => {
      saveWebsiteMessage(data)
        .then((result) => {
          console.log("res", result);
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  checkAdPendingBalance: (data) => {
    return new Promise((resolve, reject) => {
      checkAdPendingBalance(data)
        .then((result) => {
          console.log("res", result);
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  googlePlace: (text) => {
    return new Promise((resolve, reject) => {
      try {
        axios
          .get("https://maps.googleapis.com/maps/api/place/autocomplete/json", {
            params: {
              input: text,
              key: googleAPIKey,
            },
          })
          .then((result) => {
            resolve(result.data);
          });
      } catch (error) {
        reject(error);
      }
    });
  },
  googlePlaceDetails: (text) => {
    return new Promise((resolve, reject) => {
      try {
        axios
          .get("https://maps.googleapis.com/maps/api/place/details/json", {
            params: {
              placeid: text,
              key: googleAPIKey,
            },
          })
          .then((result) => {
            resolve(result.data);
          });
      } catch (error) {
        reject(error);
      }
    });
  },
};
