<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="backgroundColor">#F8FAFC</color>
    <color name="black">#000000</color>
    <color name="borderColor">#D1D5DB</color>
    <color name="call_background">#000000</color>
    <color name="call_blue">#2196F3</color>
    <color name="call_green">#4CAF50</color>
    <color name="call_orange">#FF9800</color>
    <color name="call_overlay">#80000000</color>
    <color name="call_red">#F44336</color>
    <color name="cardBackground">#FFFFFF</color>
    <color name="colorAccent">#00C853</color>
    <color name="colorPrimary">#24d05a</color>
    <color name="colorPrimaryDark">#1eb54a</color>
    <color name="iconColor">#374151</color>
    <color name="red">#FF0000</color>
    <color name="rippleColor">#20000000</color>
    <color name="statusBarColor">#FFFFFF</color>
    <color name="textPrimary">#0F172A</color>
    <color name="textSecondary">#374151</color>
    <color name="textTertiary">#6B7280</color>
    <color name="transparent">#00000000</color>
    <color name="white">#FFFFFF</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">Adtip</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="default_web_client_id" translatable="false">333436486029-24e4ub781j4i3fl1k1p2e52amno8pmrp.apps.googleusercontent.com</string>
    <string name="gcm_defaultSenderId" translatable="false">333436486029</string>
    <string name="google_api_key" translatable="false">AIzaSyBamdG5TwWa4VjiVHo1rk4zgyMWHE4JQJc</string>
    <string name="google_app_id" translatable="false">1:333436486029:android:5957954f40f53a01217dc4</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBamdG5TwWa4VjiVHo1rk4zgyMWHE4JQJc</string>
    <string name="google_storage_bucket" translatable="false">adtip-3873c.appspot.com</string>
    <string name="project_id" translatable="false">adtip-3873c</string>
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:windowBackground">@color/backgroundColor</item>
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:enforceNavigationBarContrast">false</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
</resources>