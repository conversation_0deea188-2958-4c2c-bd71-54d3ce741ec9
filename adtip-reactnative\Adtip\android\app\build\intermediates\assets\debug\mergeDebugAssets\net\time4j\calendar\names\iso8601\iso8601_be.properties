# months
M(a)_1=сту
M(a)_2=лют
M(a)_3=сак
M(a)_4=кра
M(a)_5=мая
M(a)_6=чэр
M(a)_7=ліп
M(a)_8=жні
M(a)_9=вер
M(a)_10=кас
M(a)_11=ліс
M(a)_12=сне

M(n)_1=с
M(n)_2=л
M(n)_3=с
M(n)_4=к
M(n)_5=м
M(n)_6=ч
M(n)_7=л
M(n)_8=ж
M(n)_9=в
M(n)_10=к
M(n)_11=л
M(n)_12=с

M(w)_1=студзеня
M(w)_2=лютага
M(w)_3=сакавіка
M(w)_4=красавіка
M(w)_5=мая
M(w)_6=чэрвеня
M(w)_7=ліпеня
M(w)_8=жніўня
M(w)_9=верасня
M(w)_10=кастрычніка
M(w)_11=лістапада
M(w)_12=снежня

M(A)_1=сту
M(A)_2=лют
M(A)_3=сак
M(A)_4=кра
M(A)_5=май
M(A)_6=чэр
M(A)_7=ліп
M(A)_8=жні
M(A)_9=вер
M(A)_10=кас
M(A)_11=ліс
M(A)_12=сне

M(N)_1=с
M(N)_2=л
M(N)_3=с
M(N)_4=к
M(N)_5=м
M(N)_6=ч
M(N)_7=л
M(N)_8=ж
M(N)_9=в
M(N)_10=к
M(N)_11=л
M(N)_12=с

M(W)_1=студзень
M(W)_2=люты
M(W)_3=сакавік
M(W)_4=красавік
M(W)_5=май
M(W)_6=чэрвень
M(W)_7=ліпень
M(W)_8=жнівень
M(W)_9=верасень
M(W)_10=кастрычнік
M(W)_11=лістапад
M(W)_12=снежань

# weekdays
D(a)_1=пн
D(a)_2=аў
D(a)_3=ср
D(a)_4=чц
D(a)_5=пт
D(a)_6=сб
D(a)_7=нд

D(n)_1=п
D(n)_2=а
D(n)_3=с
D(n)_4=ч
D(n)_5=п
D(n)_6=с
D(n)_7=н

D(s)_1=пн
D(s)_2=аў
D(s)_3=ср
D(s)_4=чц
D(s)_5=пт
D(s)_6=сб
D(s)_7=нд

D(w)_1=панядзелак
D(w)_2=аўторак
D(w)_3=серада
D(w)_4=чацвер
D(w)_5=пятніца
D(w)_6=субота
D(w)_7=нядзеля

D(A)_1=пн
D(A)_2=аў
D(A)_3=ср
D(A)_4=чц
D(A)_5=пт
D(A)_6=сб
D(A)_7=нд

D(N)_1=п
D(N)_2=а
D(N)_3=с
D(N)_4=ч
D(N)_5=п
D(N)_6=с
D(N)_7=н

D(S)_1=пн
D(S)_2=аў
D(S)_3=ср
D(S)_4=чц
D(S)_5=пт
D(S)_6=сб
D(S)_7=нд

D(W)_1=панядзелак
D(W)_2=аўторак
D(W)_3=серада
D(W)_4=чацвер
D(W)_5=пятніца
D(W)_6=субота
D(W)_7=нядзеля

# quarters
Q(a)_1=1-шы кв.
Q(a)_2=2-гі кв.
Q(a)_3=3-ці кв.
Q(a)_4=4-ты кв.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1-шы квартал
Q(w)_2=2-гі квартал
Q(w)_3=3-ці квартал
Q(w)_4=4-ты квартал

Q(A)_1=1-шы кв.
Q(A)_2=2-гі кв.
Q(A)_3=3-ці кв.
Q(A)_4=4-ты кв.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1-шы квартал
Q(W)_2=2-гі квартал
Q(W)_3=3-ці квартал
Q(W)_4=4-ты квартал

# day-period-translations
P(a)_am=AM
P(a)_pm=PM

P(n)_am=am
P(n)_pm=pm

P(w)_am=AM
P(w)_pm=PM

P(A)_am=AM
P(A)_pm=PM

P(N)_am=AM
P(N)_pm=PM

P(W)_am=AM
P(W)_pm=PM

# eras
E(w)_0=да нараджэння Хрыстова
E(w|alt)_0=да нашай эры
E(w)_1=ад нараджэння Хрыстова
E(w|alt)_1=нашай эры

E(a)_0=да н.э.
E(a)_1=н.э.

# format patterns
F(f)_d=EEEE, d MMMM y 'г'.
F(l)_d=d MMMM y 'г'.
F(m)_d=d.MM.y
F(s)_d=d.MM.yy

F(alt)=H.mm.ss

F(f)_t=HH:mm:ss, zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'у' {0}
F(l)_dt={1} 'у' {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=hh a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M.y
F_yMMM=LLL y
F_yMMMM=LLLL y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=w 'тыдзень' Y

I={0} – {1}

# labels of elements
L_era=эра
L_year=год
L_quarter=квартал
L_month=месяц
L_week=тыд
L_day=дзень
L_weekday=дзень тыдня
L_dayperiod=AM/PM
L_hour=гадзіна
L_minute=хвіліна
L_second=секунда
L_zone=часавы пояс
