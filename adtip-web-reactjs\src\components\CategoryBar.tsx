import React, { memo } from 'react';

interface Category {
  name: string;
  icon?: string;
}

interface CategoryBarProps {
  categories: Category[];
  selectedCategory: string;
  onCategorySelect: (categoryName: string) => void;
}

const CategoryBar = memo(({ categories, selectedCategory, onCategorySelect }: CategoryBarProps) => (
  <div className="sticky top-0 z-30 bg-white py-3 px-4 overflow-x-auto flex justify-center whitespace-nowrap gap-3 scrollbar-hide shadow-sm border-b border-gray-100">
    <div className="flex gap-3">
      {categories.map((category) => (
        <button
          key={category.name}
          onClick={() => onCategorySelect(category.name)}
          className={`px-4 py-1.5 rounded-full text-sm transition-all whitespace-nowrap ${
            selectedCategory === category.name
              ? "bg-adtip-teal text-white"
              : "bg-gray-100 text-gray-800 hover:bg-gray-200"
          }`}
        >
          {category.icon && <span className="mr-1">{category.icon}</span>}
          {category.name}
        </button>
      ))}
    </div>
  </div>
));

CategoryBar.displayName = 'CategoryBar';

export default CategoryBar;
