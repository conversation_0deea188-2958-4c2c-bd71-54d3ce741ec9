/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake \
  -H/Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup \
  -DCMAKE_SYSTEM_NAME=Android \
  -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
  -DCMAKE_SYSTEM_VERSION=24 \
  -DANDROID_PLATFORM=android-24 \
  -DANDROID_ABI=x86_64 \
  -DCMAKE_ANDROID_ARCH_ABI=x86_64 \
  -DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \
  -DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006 \
  -DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake \
  -DCMA<PERSON>_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja \
  -DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/CallKeepExample/android/app/build/intermediates/cxx/Debug/4n3y682j/obj/x86_64 \
  -DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/CallKeepExample/android/app/build/intermediates/cxx/Debug/4n3y682j/obj/x86_64 \
  -DCMAKE_BUILD_TYPE=Debug \
  -DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/prefab/x86_64/prefab \
  -B/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/x86_64 \
  -GNinja \
  -DPROJECT_BUILD_DIR=/Users/<USER>/Desktop/CallKeepExample/android/app/build \
  -DPROJECT_ROOT_DIR=/Users/<USER>/Desktop/CallKeepExample/android \
  -DREACT_ANDROID_DIR=/Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid \
  -DANDROID_STL=c++_shared \
  -DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
