/**
 * Test script for Chat API endpoints
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:7082/api';

// Test user credentials (you'll need to replace with actual user tokens)
const TEST_USER_1_TOKEN = 'your_test_token_here'; // Replace with actual token
const TEST_USER_2_TOKEN = 'your_test_token_here'; // Replace with actual token

async function testChatAPI() {
  console.log('🧪 Testing Chat API Endpoints');
  console.log('==============================');
  
  try {
    // Test 1: Get conversations (without auth for now, just to test endpoint exists)
    console.log('\n1. Testing GET /api/chat/conversations');
    try {
      const response = await axios.get(`${BASE_URL}/chat/conversations`, {
        headers: {
          'Authorization': `Bearer ${TEST_USER_1_TOKEN}`
        }
      });
      console.log('✅ Conversations endpoint accessible');
      console.log('Response status:', response.status);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Conversations endpoint exists (401 - needs auth)');
      } else {
        console.log('❌ Conversations endpoint error:', error.message);
      }
    }
    
    // Test 2: Test messages endpoint
    console.log('\n2. Testing GET /api/chat/conversations/1/messages');
    try {
      const response = await axios.get(`${BASE_URL}/chat/conversations/1/messages`, {
        headers: {
          'Authorization': `Bearer ${TEST_USER_1_TOKEN}`
        }
      });
      console.log('✅ Messages endpoint accessible');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Messages endpoint exists (401 - needs auth)');
      } else {
        console.log('❌ Messages endpoint error:', error.message);
      }
    }
    
    // Test 3: Test create conversation endpoint
    console.log('\n3. Testing POST /api/chat/conversations');
    try {
      const response = await axios.post(`${BASE_URL}/chat/conversations`, {
        participantId: 2
      }, {
        headers: {
          'Authorization': `Bearer ${TEST_USER_1_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ Create conversation endpoint accessible');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Create conversation endpoint exists (401 - needs auth)');
      } else {
        console.log('❌ Create conversation endpoint error:', error.message);
      }
    }
    
    // Test 4: Test server health
    console.log('\n4. Testing server health');
    try {
      const response = await axios.get(`${BASE_URL}/health`);
      console.log('✅ Server is healthy');
      console.log('Health status:', response.data);
    } catch (error) {
      console.log('⚠️  Health endpoint not available:', error.message);
    }
    
    console.log('\n==============================');
    console.log('🎉 API endpoint tests completed!');
    console.log('Note: Most endpoints require authentication tokens');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Test database connectivity through API
async function testDatabaseConnectivity() {
  console.log('\n🔍 Testing Database Connectivity via API');
  console.log('=========================================');
  
  try {
    // Test if we can reach any endpoint that would hit the database
    const response = await axios.get(`${BASE_URL}/health/database`, {
      timeout: 5000
    });
    console.log('✅ Database connectivity test passed');
    console.log('Response:', response.data);
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running on port 7082');
    } else if (error.response && error.response.status === 404) {
      console.log('⚠️  Database health endpoint not found, but server is running');
    } else {
      console.log('⚠️  Database connectivity test inconclusive:', error.message);
    }
  }
}

// Test Socket.IO connectivity
async function testSocketIOConnectivity() {
  console.log('\n🔌 Testing Socket.IO Connectivity');
  console.log('==================================');
  
  try {
    // Just test if the server responds to HTTP requests
    const response = await axios.get(`http://localhost:7082`, {
      timeout: 5000
    });
    console.log('✅ Server is responding on port 7082');
    console.log('Socket.IO should be available at ws://localhost:7082');
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ Server is not running on port 7082');
      console.log('Please start the server with: npm run server');
    } else {
      console.log('⚠️  Server connectivity test inconclusive:', error.message);
    }
  }
}

async function runAllTests() {
  await testSocketIOConnectivity();
  await testDatabaseConnectivity();
  await testChatAPI();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  testChatAPI,
  testDatabaseConnectivity,
  testSocketIOConnectivity,
  runAllTests
};
