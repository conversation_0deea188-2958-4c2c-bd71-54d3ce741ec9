# future
Y+1=em {0} ano
M+1=em {0} mês
W+1=em {0} semana
D+1=em {0} dia
H+1=em {0} hora
N+1=em {0} minuto
S+1=em {0} segundo

Y+5=em {0} anos
M+5=em {0} meses
W+5=em {0} semanas
D+5=em {0} dias
H+5=em {0} horas
N+5=em {0} minutos
S+5=em {0} segundos

# past
Y-1=há {0} ano
M-1=há {0} mês
W-1=há {0} semana
D-1=há {0} dia
H-1=há {0} hora
N-1=há {0} minuto
S-1=há {0} segundo

Y-5=há {0} anos
M-5=há {0} meses
W-5=há {0} semanas
D-5=há {0} dias
H-5=há {0} horas
N-5=há {0} minutos
S-5=há {0} segundos

# current time
now=agora

# future (short)
y+1=em {0} ano
m+1=em {0} mês
w+1=em {0} sem.
d+1=em {0} dia
h+1=em {0} h
n+1=em {0} min.
s+1=em {0} seg.

y+5=em {0} anos
m+5=em {0} meses
w+5=em {0} sem.
d+5=em {0} dias
h+5=em {0} h
n+5=em {0} min.
s+5=em {0} seg.

# past (short)
y-1=há {0} ano
m-1=há {0} mês
w-1=há {0} sem.
d-1=há {0} dia
h-1=há {0} h
n-1=há {0} min.
s-1=há {0} seg.

y-5=há {0} anos
m-5=há {0} meses
w-5=há {0} sem.
d-5=há {0} dias
h-5=há {0} h
n-5=há {0} min.
s-5=há {0} seg.

# relative day
yesterday=ontem
today=hoje
tomorrow=amanhã

mon-=segunda-feira passada
mon+=próxima segunda-feira
tue-=terça-feira passada
tue+=próxima terça-feira
wed-=quarta-feira passada
wed+=próxima quarta-feira
thu-=quinta-feira passada
thu+=próxima quinta-feira
fri-=sexta-feira passada
fri+=próxima sexta-feira
sat-=sábado passado
sat+=próximo sábado
sun-=domingo passado
sun+=próximo domingo
