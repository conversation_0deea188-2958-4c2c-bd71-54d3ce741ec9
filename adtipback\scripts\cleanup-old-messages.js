#!/usr/bin/env node

/**
 * Message Cleanup Cron Job Script
 * 
 * Automated script for cleaning up old messages and maintaining database health.
 * Designed to be run as a daily cron job for message retention management.
 * 
 * Usage:
 *   node cleanup-old-messages.js [options]
 * 
 * Options:
 *   --retention-days <days>    Number of days to retain messages (default: 7)
 *   --hard-delete-days <days>  Days after which to permanently delete (default: 30)
 *   --dry-run                  Show what would be deleted without actually deleting
 *   --verbose                  Enable verbose logging
 *   --stats-only               Only show statistics without performing cleanup
 */

const path = require('path');
const fs = require('fs');

// Add the parent directory to the module path so we can require our services
const parentDir = path.resolve(__dirname, '..');
require('module').globalPaths.push(path.join(parentDir, 'node_modules'));

// Import required services
const MessageCleanupService = require('../services/MessageCleanupService');
const queryRunner = require('../dbConfig/queryRunner');

// Command line argument parsing
const args = process.argv.slice(2);
const options = {
  retentionDays: 7,
  hardDeleteDays: 30,
  dryRun: false,
  verbose: false,
  statsOnly: false
};

// Parse command line arguments
for (let i = 0; i < args.length; i++) {
  switch (args[i]) {
    case '--retention-days':
      options.retentionDays = parseInt(args[++i]) || 7;
      break;
    case '--hard-delete-days':
      options.hardDeleteDays = parseInt(args[++i]) || 30;
      break;
    case '--dry-run':
      options.dryRun = true;
      break;
    case '--verbose':
      options.verbose = true;
      break;
    case '--stats-only':
      options.statsOnly = true;
      break;
    case '--help':
    case '-h':
      console.log(`
Message Cleanup Script

Usage: node cleanup-old-messages.js [options]

Options:
  --retention-days <days>    Number of days to retain messages (default: 7)
  --hard-delete-days <days>  Days after which to permanently delete (default: 30)
  --dry-run                  Show what would be deleted without actually deleting
  --verbose                  Enable verbose logging
  --stats-only               Only show statistics without performing cleanup
  --help, -h                 Show this help message

Examples:
  node cleanup-old-messages.js                           # Run with default settings
  node cleanup-old-messages.js --retention-days 14      # Keep messages for 14 days
  node cleanup-old-messages.js --dry-run --verbose      # See what would be deleted
  node cleanup-old-messages.js --stats-only             # Show statistics only
      `);
      process.exit(0);
      break;
  }
}

// Logging functions
function log(message, ...args) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`, ...args);
}

function verbose(message, ...args) {
  if (options.verbose) {
    log(`[VERBOSE] ${message}`, ...args);
  }
}

function error(message, ...args) {
  const timestamp = new Date().toISOString();
  console.error(`[${timestamp}] [ERROR] ${message}`, ...args);
}

// Main cleanup function
async function runCleanup() {
  try {
    log('Starting message cleanup script');
    log('Options:', JSON.stringify(options, null, 2));

    // Get cleanup statistics first
    log('Getting cleanup statistics...');
    const stats = await MessageCleanupService.getCleanupStats();
    
    log('Current database statistics:');
    log(`  Total messages: ${stats.totalMessages}`);
    log(`  Active messages: ${stats.activeMessages}`);
    log(`  Deleted messages: ${stats.deletedMessages}`);
    log(`  Messages older than ${options.retentionDays} days: ${stats.oldMessages}`);
    log(`  Sync entries: ${stats.syncEntries}`);
    log(`  Delivery status entries: ${stats.deliveryStatusEntries}`);
    log(`  Database size: ${stats.databaseSizeMB} MB`);

    if (options.statsOnly) {
      log('Stats-only mode enabled. Exiting without performing cleanup.');
      return;
    }

    if (options.dryRun) {
      log('DRY RUN MODE: No actual deletions will be performed');
    }

    // Perform cleanup operations
    const cleanupResults = {
      messageCleanup: null,
      hardDelete: null,
      orphanedCleanup: null,
      totalDuration: 0,
      success: false,
      errors: []
    };

    const startTime = Date.now();

    // 1. Clean up old messages
    if (stats.oldMessages > 0) {
      log(`Cleaning up ${stats.oldMessages} messages older than ${options.retentionDays} days...`);
      
      if (!options.dryRun) {
        try {
          cleanupResults.messageCleanup = await MessageCleanupService.cleanupOldMessages(options.retentionDays);
          log('Message cleanup completed:', cleanupResults.messageCleanup.message);
          verbose('Message cleanup details:', cleanupResults.messageCleanup);
        } catch (err) {
          error('Message cleanup failed:', err.message);
          cleanupResults.errors.push(`Message cleanup failed: ${err.message}`);
        }
      } else {
        log(`[DRY RUN] Would delete ${stats.oldMessages} old messages`);
      }
    } else {
      log('No old messages found to clean up');
    }

    // 2. Hard delete very old messages
    if (options.hardDeleteDays > 0) {
      log(`Checking for messages to permanently delete (older than ${options.hardDeleteDays} days)...`);
      
      if (!options.dryRun) {
        try {
          cleanupResults.hardDelete = await MessageCleanupService.hardDeleteOldMessages(options.hardDeleteDays);
          if (cleanupResults.hardDelete.hardDeletedMessages > 0) {
            log('Hard delete completed:', cleanupResults.hardDelete.message);
            verbose('Hard delete details:', cleanupResults.hardDelete);
          } else {
            log('No messages found for hard deletion');
          }
        } catch (err) {
          error('Hard delete failed:', err.message);
          cleanupResults.errors.push(`Hard delete failed: ${err.message}`);
        }
      } else {
        log('[DRY RUN] Would check for messages to permanently delete');
      }
    }

    // 3. Clean up orphaned entries
    log('Cleaning up orphaned database entries...');
    
    if (!options.dryRun) {
      try {
        const orphanedSync = await MessageCleanupService.cleanupOrphanedSyncEntries();
        const orphanedDelivery = await MessageCleanupService.cleanupOrphanedDeliveryStatus();
        
        cleanupResults.orphanedCleanup = {
          syncEntries: orphanedSync.deletedEntries,
          deliveryEntries: orphanedDelivery.deletedEntries
        };
        
        log(`Cleaned up ${orphanedSync.deletedEntries} orphaned sync entries`);
        log(`Cleaned up ${orphanedDelivery.deletedEntries} orphaned delivery status entries`);
      } catch (err) {
        error('Orphaned cleanup failed:', err.message);
        cleanupResults.errors.push(`Orphaned cleanup failed: ${err.message}`);
      }
    } else {
      log('[DRY RUN] Would clean up orphaned database entries');
    }

    cleanupResults.totalDuration = Date.now() - startTime;
    cleanupResults.success = cleanupResults.errors.length === 0;

    // Final summary
    log('Cleanup script completed');
    log(`Total duration: ${cleanupResults.totalDuration}ms`);
    log(`Success: ${cleanupResults.success}`);
    
    if (cleanupResults.errors.length > 0) {
      log('Errors encountered:');
      cleanupResults.errors.forEach(err => log(`  - ${err}`));
    }

    // Get final statistics
    if (!options.dryRun && !options.statsOnly) {
      log('Getting final statistics...');
      const finalStats = await MessageCleanupService.getCleanupStats();
      log('Final database statistics:');
      log(`  Total messages: ${finalStats.totalMessages}`);
      log(`  Active messages: ${finalStats.activeMessages}`);
      log(`  Deleted messages: ${finalStats.deletedMessages}`);
      log(`  Database size: ${finalStats.databaseSizeMB} MB`);
      
      const sizeDiff = stats.databaseSizeMB - finalStats.databaseSizeMB;
      if (sizeDiff > 0) {
        log(`  Space freed: ${sizeDiff.toFixed(2)} MB`);
      }
    }

    // Exit with appropriate code
    process.exit(cleanupResults.success ? 0 : 1);

  } catch (err) {
    error('Cleanup script failed:', err.message);
    if (options.verbose) {
      error('Stack trace:', err.stack);
    }
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  log('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  log('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  error('Uncaught exception:', err.message);
  if (options.verbose) {
    error('Stack trace:', err.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  error('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the cleanup
if (require.main === module) {
  runCleanup();
}

module.exports = { runCleanup, options };
