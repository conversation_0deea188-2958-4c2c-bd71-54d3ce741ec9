-- Add commission tracking fields to wallet table
-- This migration adds support for tracking platform commission on earnings
-- MySQL 8 compatible version - Simple approach with error handling

-- Note: This script uses ALTER TABLE with IF NOT EXISTS equivalent
-- If columns already exist, MySQL will throw an error but continue with the script

-- Add gross_amount column
ALTER TABLE wallet 
ADD COLUMN gross_amount DECIMAL(10,2) DEFAULT NULL 
COMMENT 'Gross earning amount before commission';

-- Add platform_commission column  
ALTER TABLE wallet 
ADD COLUMN platform_commission DECIMAL(10,2) DEFAULT NULL 
COMMENT 'Platform commission deducted';

-- Add commission_rate column
ALTER TABLE wallet 
ADD COLUMN commission_rate DECIMAL(5,4) DEFAULT NULL 
COMMENT 'Commission rate applied (0.30 for premium, 0.60 for regular)';

-- Add description column
ALTER TABLE wallet 
ADD COLUMN description TEXT DEFAULT NULL 
COMMENT 'Transaction description';

-- Create index for better performance on commission queries
CREATE INDEX idx_wallet_commission ON wallet(createdby, gross_amount, platform_commission);

-- Show completion message
SELECT 'Commission tracking migration completed successfully' as status;

-- Verify the changes
SELECT 
  COLUMN_NAME, 
  DATA_TYPE, 
  IS_NULLABLE, 
  COLUMN_DEFAULT, 
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'wallet' 
AND COLUMN_NAME IN ('gross_amount', 'platform_commission', 'commission_rate', 'description')
AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
