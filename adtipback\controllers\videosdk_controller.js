const VideoSDKService = require('../services/videosdk_service');

class VideoSDKController {
  static async generateToken(req, res) {
    try {
      const token = VideoSDKService.generateToken();
      
      res.status(200).json({
        success: true,
        token,
        message: 'Token generated successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  static async createMeeting(req, res) {
    try {
      const { token, region } = req.body;
      if (!token || !region) {
        return res.status(400).json({
          success: false,
          error: 'Token and region are required'
        });
      }
      const result = await VideoSDKService.createMeeting(token, region);
      
      res.status(200).json({
        success: true,
        data: result,
        message: 'Meeting created successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  static async validateMeeting(req, res) {
    try {
      const { token } = req.body;
      const { meetingId } = req.params;
      if (!token || !meetingId) {
        return res.status(400).json({
          success: false,
          error: 'Token and meetingId are required'
        });
      }
      const result = await VideoSDKService.validateMeeting(token, meetingId);
      
      res.status(200).json({
        success: true,
        data: result,
        message: 'Meeting validated successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  static async deactivateRoom(req, res) {
    try {
      const { token, roomId } = req.body;
      if (!token || !roomId) {
        return res.status(400).json({
          success: false,
          error: 'Token and roomId are required'
        });
      }
      const result = await VideoSDKService.deactivateRoom(token, roomId);
      
      res.status(200).json({
        success: true,
        data: result,
        message: 'Room deactivated successfully'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
}

module.exports = VideoSDKController;