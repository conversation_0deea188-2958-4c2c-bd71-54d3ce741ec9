const LudoService = require("../services/ludoService");

const searchPlayers = async (req, res) => {
  try {
    const { challengeAmount } = req.body;
    if (!challengeAmount || challengeAmount <= 0) {
      return res.status(400).send({
        status: 400,
        message: "Invalid challenge amount.",
        data: [],
      });
    }
    const result = await LudoService.searchPlayers(challengeAmount);
    res.status(200).send({
      status: 200,
      message: "Players fetched successfully.",
      data: result,
    });
  } catch (err) {
    res.status(err.status || 500).send({
      status: err.status || 500,
      message: err.message || "Internal server error.",
      data: [],
    });
  }
};

const deductMoneyForGame = async (req, res) => {
  try {
    const { gameId } = req.body;
    if (!gameId) {
      return res.status(400).send({
        status: 400,
        message: "Game ID is required.",
        data: [],
      });
    }
    const result = await LudoService.deductMoneyForGame(gameId);
    res.status(200).send({
      status: 200,
      message: "Money deducted successfully for all players.",
      data: result,
    });
  } catch (err) {
    res.status(err.status || 500).send({
      status: err.status || 500,
      message: err.message || "Internal server error.",
      data: [],
    });
  }
};

const addMoney = async (req, res) => {
  try {
    const { userId } = req.params;
    const { amount, gameId } = req.body;
    if (!amount || amount <= 0 || !gameId) {
      return res.status(400).send({
        status: 400,
        message: "Invalid amount or game ID.",
        data: [],
      });
    }
    const result = await LudoService.addMoney(userId, amount, gameId);
    res.status(200).send({
      status: 200,
      message: "Money added successfully.",
      data: result,
    });
  } catch (err) {
    res.status(err.status || 500).send({
      status: err.status || 500,
      message: err.message || "Internal server error.",
      data: [],
    });
  }
};

const getGameHistory = async (req, res) => {
  try {
    const { userIds } = req.body;
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).send({
        status: 400,
        message: "Invalid or missing userIds. Must be a non-empty array.",
        data: [],
      });
    }
    const result = await LudoService.getGameHistory(userIds);
    res.status(200).send({
      status: 200,
      message: "Game history fetched successfully.",
      data: result,
    });
  } catch (err) {
    res.status(err.status || 500).send({
      status: err.status || 500,
      message: err.message || "Internal server error.",
      data: [],
    });
  }
};

// New: Get list of all room types, open rooms, and status
const getRoomList = async (req, res) => {
  try {
    const result = await LudoService.getRoomList();
    res.status(200).send({
      status: 200,
      message: "Room list fetched successfully.",
      data: result,
    });
  } catch (err) {
    res.status(err.status || 500).send({
      status: err.status || 500,
      message: err.message || "Internal server error.",
      data: [],
    });
  }
};

// New: Join a room of a given type
const joinRoom = async (req, res) => {
  try {
    const { userId, roomType } = req.body;
    if (!userId || !roomType) {
      return res.status(400).send({
        status: 400,
        message: "Missing userId or roomType.",
        data: [],
      });
    }
    const result = await LudoService.joinRoom(userId, roomType);
    res.status(200).send({
      status: 200,
      message: "Joined room successfully.",
      data: result,
    });
  } catch (err) {
    res.status(err.status || 500).send({
      status: err.status || 500,
      message: err.message || "Internal server error.",
      data: [],
    });
  }
};

// New: Continue a disconnected game
const continueGame = async (req, res) => {
  try {
    const { userId } = req.body;
    if (!userId) {
      return res.status(400).send({
        status: 400,
        message: "Missing userId.",
        data: [],
      });
    }
    const result = await LudoService.continueGame(userId);
    res.status(200).send({
      status: 200,
      message: "Continue game result retrieved successfully.",
      data: result,
    });
  } catch (err) {
    res.status(err.status || 500).send({
      status: err.status || 500,
      message: err.message || "Internal server error.",
      data: [],
    });
  }
};

// Get lobby counts for different room types
const getLobbyCounts = async (req, res) => {
  try {
    const result = await LudoService.getLobbyCounts();
    res.status(200).send({
      status: 200,
      success: true,
      message: "Lobby counts retrieved successfully.",
      data: result,
    });
  } catch (err) {
    res.status(err.status || 500).send({
      status: err.status || 500,
      success: false,
      message: err.message || "Internal server error.",
      data: {},
    });
  }
};

module.exports = {
  searchPlayers,
  deductMoneyForGame,
  addMoney,
  getGameHistory,
  getRoomList,
  joinRoom,
  continueGame,
  getLobbyCounts,
};