/**
 * User-Based Chat Service
 * 
 * Service for handling user-to-user chat operations without conversation tables.
 * Uses direct user mapping with chat_userId1_userId2 format.
 */

const queryRunner = require('../dbConfig/queryRunner');

class UserBasedChatService {
  
  /**
   * Generate chat_id from two user IDs (sorted)
   */
  static generateChatId(userId1, userId2) {
    const [user1, user2] = [parseInt(userId1), parseInt(userId2)].sort((a, b) => a - b);
    return `chat_${user1}_${user2}`;
  }

  /**
   * Create or get a chat between two users
   */
  static async createOrGetChat(userId1, userId2) {
    try {
      const chatId = this.generateChatId(userId1, userId2);

      // Get user information
      const userQuery = 'SELECT id, name, profile_image FROM users WHERE id IN (?, ?)';
      const users = await queryRunner.queryRunner(userQuery, [userId1, userId2]);

      if (users.length !== 2) {
        throw new Error('One or both users not found');
      }

      const user1Data = users.find(u => u.id == userId1);
      const user2Data = users.find(u => u.id == userId2);

      // Create or update user chat metadata for both users
      const metadataQuery = `
        INSERT INTO user_chat_metadata (
          user_id, chat_id, other_user_id, other_user_name, other_user_avatar
        ) VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          other_user_name = VALUES(other_user_name),
          other_user_avatar = VALUES(other_user_avatar),
          updated_at = CURRENT_TIMESTAMP
      `;

      // Create metadata for user1
      await queryRunner.queryRunner(metadataQuery, [
        userId1, chatId, userId2, user2Data.name, user2Data.profile_image
      ]);

      // Create metadata for user2
      await queryRunner.queryRunner(metadataQuery, [
        userId2, chatId, userId1, user1Data.name, user1Data.profile_image
      ]);

      return {
        chatId,
        participants: [user1Data, user2Data]
      };

    } catch (error) {
      console.error('Error creating/getting chat:', error);
      throw error;
    }
  }

  /**
   * Send a message between users
   */
  static async sendMessage(senderId, recipientId, messageData) {
    try {
      const { content, messageType = 'text', replyToMessageId, tempId } = messageData;

      // Prevent self-messaging
      if (senderId.toString() === recipientId.toString()) {
        throw new Error('Cannot send message to yourself');
      }

      // Verify users exist
      const userQuery = 'SELECT id, name, profile_image FROM users WHERE id IN (?, ?)';
      const users = await queryRunner.queryRunner(userQuery, [senderId, recipientId]);

      if (users.length !== 2) {
        throw new Error('Sender or recipient not found');
      }

      const sender = users.find(u => u.id == senderId);
      const recipient = users.find(u => u.id == recipientId);

      // Generate chat_id
      const chatId = this.generateChatId(senderId, recipientId);

      // Insert message
      const messageQuery = `
        INSERT INTO messages (
          chat_id, sender_id, recipient_id, sender_name, sender_avatar,
          content, message_type, reply_to_message_id, temp_id, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'sent')
      `;

      const result = await queryRunner.queryRunner(messageQuery, [
        chatId, senderId, recipientId, sender.name, sender.profile_image,
        content, messageType, replyToMessageId || null, tempId || null
      ]);

      // Get the created message
      const createdMessageQuery = 'SELECT * FROM messages WHERE id = ?';
      const [createdMessage] = await queryRunner.queryRunner(createdMessageQuery, [result.insertId]);

      // Create sync tracking if tempId provided
      if (tempId) {
        const syncQuery = `
          INSERT INTO messages_local_sync (
            message_id, chat_id, local_message_id, local_timestamp,
            server_timestamp, sync_status
          ) VALUES (?, ?, ?, ?, ?, 'synced')
        `;
        await queryRunner.queryRunner(syncQuery, [
          result.insertId, chatId, tempId, new Date(), new Date()
        ]);
      }

      return createdMessage;

    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Get messages for a chat with pagination
   */
  static async getChatMessages(userId, chatId, page = 1, limit = 50) {
    try {
      // Verify user is participant in this chat
      const chatParts = chatId.split('_');
      if (chatParts.length !== 3 || chatParts[0] !== 'chat') {
        throw new Error('Invalid chat ID format');
      }

      const [, userId1, userId2] = chatParts;
      if (userId.toString() !== userId1 && userId.toString() !== userId2) {
        throw new Error('User is not authorized to access this chat');
      }

      const offset = (page - 1) * limit;

      // Get messages
      const messagesQuery = `
        SELECT 
          m.*,
          mds.status as delivery_status,
          mds.timestamp as delivery_timestamp
        FROM messages m
        LEFT JOIN message_delivery_status mds ON m.id = mds.message_id AND mds.recipient_id = ?
        WHERE m.chat_id = ? AND m.is_deleted = FALSE
        ORDER BY m.created_at DESC, m.id DESC
        LIMIT ? OFFSET ?
      `;

      const messages = await queryRunner.queryRunner(messagesQuery, [userId, chatId, limit, offset]);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM messages m
        WHERE m.chat_id = ? AND m.is_deleted = FALSE
      `;
      const [countResult] = await queryRunner.queryRunner(countQuery, [chatId]);

      const totalMessages = countResult.total;
      const totalPages = Math.ceil(totalMessages / limit);

      return {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalMessages,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };

    } catch (error) {
      console.error('Error getting chat messages:', error);
      throw error;
    }
  }

  /**
   * Get user's chats with metadata
   */
  static async getUserChats(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;

      // Get user chats
      const chatsQuery = `
        SELECT 
          ucm.*,
          u.name as other_user_display_name,
          u.profile_image as other_user_profile_image,
          u.is_online as other_user_online_status
        FROM user_chat_metadata ucm
        JOIN users u ON ucm.other_user_id = u.id
        WHERE ucm.user_id = ? AND ucm.is_archived = FALSE
        ORDER BY ucm.last_activity_at DESC
        LIMIT ? OFFSET ?
      `;

      const chats = await queryRunner.queryRunner(chatsQuery, [userId, limit, offset]);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM user_chat_metadata ucm
        WHERE ucm.user_id = ? AND ucm.is_archived = FALSE
      `;
      const [countResult] = await queryRunner.queryRunner(countQuery, [userId]);

      const totalChats = countResult.total;
      const totalPages = Math.ceil(totalChats / limit);

      return {
        chats,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalChats,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };

    } catch (error) {
      console.error('Error getting user chats:', error);
      throw error;
    }
  }

  /**
   * Mark messages as read in a chat
   */
  static async markMessagesAsRead(userId, chatId, lastReadMessageId) {
    try {
      // Verify user is participant in this chat
      const chatParts = chatId.split('_');
      if (chatParts.length !== 3 || chatParts[0] !== 'chat') {
        throw new Error('Invalid chat ID format');
      }

      const [, userId1, userId2] = chatParts;
      if (userId.toString() !== userId1 && userId.toString() !== userId2) {
        throw new Error('User is not authorized to access this chat');
      }

      // Call stored procedure to mark messages as read
      await queryRunner.queryRunner('CALL MarkMessagesAsRead(?, ?, ?)', [
        userId, chatId, lastReadMessageId
      ]);

      return { success: true };

    } catch (error) {
      console.error('Error marking messages as read:', error);
      throw error;
    }
  }

  /**
   * Get unread message count for a user
   */
  static async getUnreadCount(userId) {
    try {
      const query = `
        SELECT SUM(unread_count) as total_unread
        FROM user_chat_metadata
        WHERE user_id = ? AND is_archived = FALSE
      `;

      const [result] = await queryRunner.queryRunner(query, [userId]);
      return result.total_unread || 0;

    } catch (error) {
      console.error('Error getting unread count:', error);
      throw error;
    }
  }

  /**
   * Update message delivery status
   */
  static async updateDeliveryStatus(messageId, recipientId, status) {
    try {
      const query = `
        INSERT INTO message_delivery_status (message_id, recipient_id, status)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE
          status = VALUES(status),
          timestamp = CURRENT_TIMESTAMP
      `;

      await queryRunner.queryRunner(query, [messageId, recipientId, status]);
      return { success: true };

    } catch (error) {
      console.error('Error updating delivery status:', error);
      throw error;
    }
  }

  /**
   * Sync messages from client
   */
  static async syncMessages(userId, messages) {
    try {
      const syncResults = [];

      for (const messageData of messages) {
        try {
          const { tempId, chatId, recipientId, content, messageType, timestamp } = messageData;

          // Check if message already exists
          const existingQuery = 'SELECT id FROM messages WHERE temp_id = ? OR external_id = ?';
          const existing = await queryRunner.queryRunner(existingQuery, [tempId, tempId]);

          if (existing.length > 0) {
            syncResults.push({
              tempId,
              status: 'already_exists',
              serverId: existing[0].id
            });
            continue;
          }

          // Send the message
          const message = await this.sendMessage(userId, recipientId, {
            content,
            messageType,
            tempId
          });

          syncResults.push({
            tempId,
            status: 'synced',
            serverId: message.id
          });

        } catch (error) {
          console.error(`Error syncing message ${messageData.tempId}:`, error);
          syncResults.push({
            tempId: messageData.tempId,
            status: 'failed',
            error: error.message
          });
        }
      }

      return {
        syncResults,
        totalMessages: messages.length,
        successCount: syncResults.filter(r => r.status === 'synced').length,
        failureCount: syncResults.filter(r => r.status === 'failed').length
      };

    } catch (error) {
      console.error('Error syncing messages:', error);
      throw error;
    }
  }
}

module.exports = UserBasedChatService;
