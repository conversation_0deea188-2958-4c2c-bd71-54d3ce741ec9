/**
 * FCM Message Queue Service for Chat Messaging
 * 
 * This service handles reliable message delivery via FCM high priority notifications
 * Features:
 * - Message queuing with retry mechanisms
 * - Proper error handling and dead letter queue
 * - Message deduplication
 * - Batch processing for efficiency
 * - Integration with existing database schema
 */

const FCMService = require('../utils/fcm');
const queryRunner = require('../dbConfig/queryRunner');
const redis = require('../config/redis');

class FCMMessageQueueService {
  constructor() {
    this.messageQueue = [];
    this.processingQueue = false;
    this.retryAttempts = 3;
    this.retryDelay = 1000; // 1 second base delay
    this.maxRetryDelay = 30000; // 30 seconds max delay
    this.deadLetterQueue = [];
    this.batchSize = 10;
    this.processInterval = null;
    
    // Start processing queue
    this.startQueueProcessor();
  }

  /**
   * Add a chat message to the FCM queue
   */
  async queueChatMessage(messageData) {
    try {
      const queueItem = {
        id: this.generateMessageId(),
        type: 'chat_message',
        messageId: messageData.messageId,
        conversationId: messageData.conversationId,
        senderId: messageData.senderId,
        recipientId: messageData.recipientId,
        content: messageData.content,
        senderName: messageData.senderName,
        timestamp: new Date().toISOString(),
        attempts: 0,
        maxAttempts: this.retryAttempts,
        priority: 'high',
        createdAt: Date.now()
      };

      // Check for duplicate messages
      if (await this.isDuplicateMessage(queueItem)) {
        console.log(`[FCMMessageQueue] Duplicate message detected, skipping: ${queueItem.id}`);
        return { success: true, duplicate: true };
      }

      // Add to queue
      this.messageQueue.push(queueItem);
      
      // Store in Redis for persistence
      await this.persistQueueItem(queueItem);
      
      console.log(`[FCMMessageQueue] Message queued: ${queueItem.id} for conversation ${queueItem.conversationId}`);
      
      // Trigger immediate processing if queue was empty
      if (this.messageQueue.length === 1 && !this.processingQueue) {
        this.processQueue();
      }

      return { success: true, queueId: queueItem.id };
    } catch (error) {
      console.error('[FCMMessageQueue] Error queueing message:', error);
      throw error;
    }
  }

  /**
   * Process the message queue
   */
  async processQueue() {
    if (this.processingQueue || this.messageQueue.length === 0) {
      return;
    }

    this.processingQueue = true;
    console.log(`[FCMMessageQueue] Processing queue with ${this.messageQueue.length} messages`);

    try {
      // Process messages in batches
      const batch = this.messageQueue.splice(0, this.batchSize);
      
      for (const queueItem of batch) {
        await this.processQueueItem(queueItem);
      }
    } catch (error) {
      console.error('[FCMMessageQueue] Error processing queue:', error);
    } finally {
      this.processingQueue = false;
      
      // Continue processing if more messages exist
      if (this.messageQueue.length > 0) {
        setTimeout(() => this.processQueue(), 100);
      }
    }
  }

  /**
   * Process individual queue item
   */
  async processQueueItem(queueItem) {
    try {
      queueItem.attempts++;
      console.log(`[FCMMessageQueue] Processing message ${queueItem.id}, attempt ${queueItem.attempts}`);

      // Get recipient FCM token
      const recipientToken = await this.getRecipientFCMToken(queueItem.recipientId);
      
      if (!recipientToken) {
        console.warn(`[FCMMessageQueue] No FCM token found for user ${queueItem.recipientId}`);
        await this.moveToDeadLetterQueue(queueItem, 'No FCM token');
        return;
      }

      // Send FCM notification
      const result = await FCMService.sendChatNotification(
        recipientToken,
        queueItem.senderName,
        queueItem.content,
        queueItem.conversationId,
        queueItem.senderId
      );

      if (result.status === 200) {
        console.log(`[FCMMessageQueue] Message sent successfully: ${queueItem.id}`);
        await this.markMessageDelivered(queueItem);
        await this.removeFromPersistence(queueItem.id);
      } else if (result.status === 404 && result.shouldRemoveToken) {
        console.warn(`[FCMMessageQueue] Invalid FCM token for user ${queueItem.recipientId}`);
        await this.handleInvalidToken(queueItem.recipientId);
        await this.moveToDeadLetterQueue(queueItem, 'Invalid FCM token');
      } else {
        throw new Error(`FCM send failed with status: ${result.status}`);
      }

    } catch (error) {
      console.error(`[FCMMessageQueue] Error processing message ${queueItem.id}:`, error);
      await this.handleFailedMessage(queueItem, error);
    }
  }

  /**
   * Handle failed message delivery
   */
  async handleFailedMessage(queueItem, error) {
    if (queueItem.attempts >= queueItem.maxAttempts) {
      console.error(`[FCMMessageQueue] Message ${queueItem.id} exceeded max attempts, moving to dead letter queue`);
      await this.moveToDeadLetterQueue(queueItem, error.message);
    } else {
      // Calculate exponential backoff delay
      const delay = Math.min(
        this.retryDelay * Math.pow(2, queueItem.attempts - 1),
        this.maxRetryDelay
      );
      
      console.log(`[FCMMessageQueue] Retrying message ${queueItem.id} in ${delay}ms`);
      
      // Re-queue with delay
      setTimeout(() => {
        this.messageQueue.unshift(queueItem);
        this.processQueue();
      }, delay);
    }
  }

  /**
   * Get recipient FCM token from database
   */
  async getRecipientFCMToken(userId) {
    try {
      const result = await queryRunner(
        'SELECT fcm_token FROM users WHERE id = ? AND fcm_token IS NOT NULL',
        [userId]
      );
      
      return result.length > 0 ? result[0].fcm_token : null;
    } catch (error) {
      console.error('[FCMMessageQueue] Error getting FCM token:', error);
      return null;
    }
  }

  /**
   * Mark message as delivered in database
   */
  async markMessageDelivered(queueItem) {
    try {
      await queryRunner(
        'UPDATE messages SET delivery_status = ?, delivered_at = NOW() WHERE id = ?',
        ['delivered', queueItem.messageId]
      );
    } catch (error) {
      console.error('[FCMMessageQueue] Error marking message delivered:', error);
    }
  }

  /**
   * Handle invalid FCM token
   */
  async handleInvalidToken(userId) {
    try {
      await queryRunner(
        'UPDATE users SET fcm_token = NULL, fcm_token_updation_date = NOW() WHERE id = ?',
        [userId]
      );
      console.log(`[FCMMessageQueue] Cleared invalid FCM token for user ${userId}`);
    } catch (error) {
      console.error('[FCMMessageQueue] Error clearing invalid token:', error);
    }
  }

  /**
   * Move message to dead letter queue
   */
  async moveToDeadLetterQueue(queueItem, reason) {
    try {
      const deadLetterItem = {
        ...queueItem,
        failureReason: reason,
        failedAt: new Date().toISOString()
      };
      
      this.deadLetterQueue.push(deadLetterItem);
      
      // Store in database for analysis
      await queryRunner(
        `INSERT INTO message_delivery_failures 
         (message_id, conversation_id, recipient_id, failure_reason, attempts, failed_at) 
         VALUES (?, ?, ?, ?, ?, NOW())`,
        [queueItem.messageId, queueItem.conversationId, queueItem.recipientId, reason, queueItem.attempts]
      );
      
      await this.removeFromPersistence(queueItem.id);
      console.log(`[FCMMessageQueue] Message ${queueItem.id} moved to dead letter queue: ${reason}`);
    } catch (error) {
      console.error('[FCMMessageQueue] Error moving to dead letter queue:', error);
    }
  }

  /**
   * Check for duplicate messages
   */
  async isDuplicateMessage(queueItem) {
    try {
      // Check in current queue
      const existsInQueue = this.messageQueue.some(item => 
        item.messageId === queueItem.messageId && 
        item.conversationId === queueItem.conversationId
      );
      
      if (existsInQueue) return true;
      
      // Check in Redis
      const redisKey = `fcm_queue_msg:${queueItem.messageId}`;
      const exists = await redis.exists(redisKey);
      
      return exists === 1;
    } catch (error) {
      console.error('[FCMMessageQueue] Error checking duplicate:', error);
      return false;
    }
  }

  /**
   * Persist queue item to Redis
   */
  async persistQueueItem(queueItem) {
    try {
      const redisKey = `fcm_queue_msg:${queueItem.messageId}`;
      await redis.setex(redisKey, 3600, JSON.stringify(queueItem)); // 1 hour TTL
    } catch (error) {
      console.error('[FCMMessageQueue] Error persisting queue item:', error);
    }
  }

  /**
   * Remove from persistence
   */
  async removeFromPersistence(queueId) {
    try {
      const redisKey = `fcm_queue_msg:${queueId}`;
      await redis.del(redisKey);
    } catch (error) {
      console.error('[FCMMessageQueue] Error removing from persistence:', error);
    }
  }

  /**
   * Generate unique message ID
   */
  generateMessageId() {
    return `fcm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Start queue processor
   */
  startQueueProcessor() {
    // Process queue every 5 seconds
    this.processInterval = setInterval(() => {
      if (this.messageQueue.length > 0 && !this.processingQueue) {
        this.processQueue();
      }
    }, 5000);
  }

  /**
   * Stop queue processor
   */
  stopQueueProcessor() {
    if (this.processInterval) {
      clearInterval(this.processInterval);
      this.processInterval = null;
    }
  }

  /**
   * Get queue statistics
   */
  getQueueStats() {
    return {
      queueLength: this.messageQueue.length,
      deadLetterQueueLength: this.deadLetterQueue.length,
      isProcessing: this.processingQueue
    };
  }

  /**
   * Retry dead letter queue messages
   */
  async retryDeadLetterQueue() {
    console.log(`[FCMMessageQueue] Retrying ${this.deadLetterQueue.length} dead letter messages`);
    
    const messagesToRetry = this.deadLetterQueue.splice(0);
    
    for (const deadLetterItem of messagesToRetry) {
      // Reset attempts and re-queue
      deadLetterItem.attempts = 0;
      delete deadLetterItem.failureReason;
      delete deadLetterItem.failedAt;
      
      this.messageQueue.push(deadLetterItem);
    }
    
    this.processQueue();
  }
}

// Export singleton instance
module.exports = new FCMMessageQueueService();
