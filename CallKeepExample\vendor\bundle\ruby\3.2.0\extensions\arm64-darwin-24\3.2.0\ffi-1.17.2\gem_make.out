current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/ffi-1.17.2/ext/ffi_c
/Users/<USER>/.rbenv/versions/3.2.2/bin/ruby extconf.rb
checking for ffi_prep_closure_loc() in -lffi... yes
checking for ffi_prep_cif_var()... yes
checking for ffi_raw_call()... yes
checking for ffi_prep_raw_closure()... yes
checking for rb_gc_mark_movable()... yes
checking for whether -pthread is accepted as LDFLAGS... yes
creating extconf.h
creating Makefile

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/ffi-1.17.2/ext/ffi_c
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-w1u42o sitelibdir\=./.gem.20250722-11947-w1u42o clean

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/ffi-1.17.2/ext/ffi_c
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-w1u42o sitelibdir\=./.gem.20250722-11947-w1u42o
compiling AbstractMemory.c
compiling ArrayType.c
compiling Buffer.c
compiling Call.c
compiling ClosurePool.c
compiling DynamicLibrary.c
compiling Function.c
compiling FunctionInfo.c
compiling LastError.c
compiling LongDouble.c
compiling MappedType.c
compiling MemoryPointer.c
compiling MethodHandle.c
compiling Platform.c
compiling Pointer.c
compiling Struct.c
compiling StructByValue.c
compiling StructLayout.c
compiling Thread.c
compiling Type.c
compiling Types.c
compiling Variadic.c
compiling ffi.c
linking shared-object ffi_c.bundle
ld: warning: ignoring duplicate libraries: '-lffi', '-lruby.3.2'

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/ffi-1.17.2/ext/ffi_c
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-w1u42o sitelibdir\=./.gem.20250722-11947-w1u42o install
/usr/bin/install -c -m 0755 ffi_c.bundle ./.gem.20250722-11947-w1u42o

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/ffi-1.17.2/ext/ffi_c
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-w1u42o sitelibdir\=./.gem.20250722-11947-w1u42o clean
