# months
M(a)_1=tammik.
M(a)_2=helmik.
M(a)_3=maalisk.
M(a)_4=huhtik.
M(a)_5=toukok.
M(a)_6=kesäk.
M(a)_7=heinäk.
M(a)_8=elok.
M(a)_9=syysk.
M(a)_10=lokak.
M(a)_11=marrask.
M(a)_12=jouluk.

M(n)_1=T
M(n)_2=H
M(n)_3=M
M(n)_4=H
M(n)_5=T
M(n)_6=K
M(n)_7=H
M(n)_8=E
M(n)_9=S
M(n)_10=L
M(n)_11=M
M(n)_12=J

M(w)_1=tammikuuta
M(w)_2=helmikuuta
M(w)_3=maaliskuuta
M(w)_4=huhtikuuta
M(w)_5=toukokuuta
M(w)_6=kesäkuuta
M(w)_7=heinäkuuta
M(w)_8=elokuuta
M(w)_9=syyskuuta
M(w)_10=lokakuuta
M(w)_11=marraskuuta
M(w)_12=joulukuuta

M(A)_1=tammi
M(A)_2=helmi
M(A)_3=maalis
M(A)_4=huhti
M(A)_5=touko
M(A)_6=kesä
M(A)_7=heinä
M(A)_8=elo
M(A)_9=syys
M(A)_10=loka
M(A)_11=marras
M(A)_12=joulu

M(N)_1=T
M(N)_2=H
M(N)_3=M
M(N)_4=H
M(N)_5=T
M(N)_6=K
M(N)_7=H
M(N)_8=E
M(N)_9=S
M(N)_10=L
M(N)_11=M
M(N)_12=J

M(W)_1=tammikuu
M(W)_2=helmikuu
M(W)_3=maaliskuu
M(W)_4=huhtikuu
M(W)_5=toukokuu
M(W)_6=kesäkuu
M(W)_7=heinäkuu
M(W)_8=elokuu
M(W)_9=syyskuu
M(W)_10=lokakuu
M(W)_11=marraskuu
M(W)_12=joulukuu

# weekdays
D(a)_1=ma
D(a)_2=ti
D(a)_3=ke
D(a)_4=to
D(a)_5=pe
D(a)_6=la
D(a)_7=su

D(n)_1=M
D(n)_2=T
D(n)_3=K
D(n)_4=T
D(n)_5=P
D(n)_6=L
D(n)_7=S

D(s)_1=ma
D(s)_2=ti
D(s)_3=ke
D(s)_4=to
D(s)_5=pe
D(s)_6=la
D(s)_7=su

D(w)_1=maanantaina
D(w)_2=tiistaina
D(w)_3=keskiviikkona
D(w)_4=torstaina
D(w)_5=perjantaina
D(w)_6=lauantaina
D(w)_7=sunnuntaina

D(A)_1=ma
D(A)_2=ti
D(A)_3=ke
D(A)_4=to
D(A)_5=pe
D(A)_6=la
D(A)_7=su

D(N)_1=M
D(N)_2=T
D(N)_3=K
D(N)_4=T
D(N)_5=P
D(N)_6=L
D(N)_7=S

D(S)_1=ma
D(S)_2=ti
D(S)_3=ke
D(S)_4=to
D(S)_5=pe
D(S)_6=la
D(S)_7=su

D(W)_1=maanantai
D(W)_2=tiistai
D(W)_3=keskiviikko
D(W)_4=torstai
D(W)_5=perjantai
D(W)_6=lauantai
D(W)_7=sunnuntai

# quarters
Q(a)_1=1. nelj.
Q(a)_2=2. nelj.
Q(a)_3=3. nelj.
Q(a)_4=4. nelj.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. neljännes
Q(w)_2=2. neljännes
Q(w)_3=3. neljännes
Q(w)_4=4. neljännes

Q(A)_1=1. nelj.
Q(A)_2=2. nelj.
Q(A)_3=3. nelj.
Q(A)_4=4. nelj.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1. neljännes
Q(W)_2=2. neljännes
Q(W)_3=3. neljännes
Q(W)_4=4. neljännes

# day-period-rules
T0500=morning1
T1000=morning2
T1200=afternoon1
T1800=evening1
T2300=night1

# day-period-translations
P(a)_midnight=keskiyöllä
P(a)_am=ap.
P(a)_noon=keskip.
P(a)_pm=ip.
P(a)_morning1=aamulla
P(a)_morning2=aamup.
P(a)_afternoon1=iltap.
P(a)_evening1=illalla
P(a)_night1=yöllä

P(n)_midnight=ky.
P(n)_am=ap.
P(n)_noon=kp.
P(n)_pm=ip.
P(n)_morning1=aamulla
P(n)_morning2=ap.
P(n)_afternoon1=ip.
P(n)_evening1=illalla
P(n)_night1=yöllä

P(w)_midnight=keskiyöllä
P(w)_am=ap.
P(w)_noon=keskipäivällä
P(w)_pm=ip.
P(w)_morning1=aamulla
P(w)_morning2=aamupäivällä
P(w)_afternoon1=iltapäivällä
P(w)_evening1=illalla
P(w)_night1=yöllä

P(A)_midnight=keskiyö
P(A)_am=ap.
P(A)_noon=keskip.
P(A)_pm=ip.
P(A)_morning1=aamu
P(A)_morning2=aamup.
P(A)_afternoon1=iltap.
P(A)_evening1=ilta
P(A)_night1=yö

P(N)_midnight=ky.
P(N)_am=ap.
P(N)_noon=kp.
P(N)_pm=ip.
P(N)_morning1=aamu
P(N)_morning2=ap.
P(N)_afternoon1=ip.
P(N)_evening1=ilta
P(N)_night1=yö

P(W)_midnight=keskiyö
P(W)_am=ap.
P(W)_noon=keskipäivä
P(W)_pm=ip.
P(W)_morning1=aamu
P(W)_morning2=aamupäivä
P(W)_afternoon1=iltapäivä
P(W)_evening1=ilta
P(W)_night1=yö

# eras
E(w)_0=ennen Kristuksen syntymää
E(w|alt)_0=ennen ajanlaskun alkua
E(w)_1=jälkeen Kristuksen syntymän
E(w|alt)_1=jälkeen ajanlaskun alun

E(a)_0=eKr.
E(a|alt)_0=eaa.
E(a)_1=jKr.
E(a|alt)_1=jaa.

E(n)_0=eKr
E(n|alt)_0=eaa
E(n)_1=jKr
E(n|alt)_1=jaa

# format patterns
F(f)_d=cccc d. MMMM y
F(l)_d=d. MMMM y
F(m)_d=d.M.y
F(s)_d=d.M.y

F(alt)=H.mm.ss

F(f)_t=H.mm.ss zzzz
F(l)_t=H.mm.ss z
F(m)_t=H.mm.ss
F(s)_t=H.mm

F(f)_dt={1} 'klo' {0}
F(l)_dt={1} 'klo' {0}
F(m)_dt={1} 'klo' {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h.mm B
F_Bhms=h.mm.ss B
F_h=h a
F_H=H
F_hm=h.mm a
F_Hm=H.mm
F_hms=h.mm.ss a
F_Hms=H.mm.ss

F_Md=d.M.
F_MMMd=d. MMM
F_MMMMd=d. MMMM
F_y=y
F_yM=L.y
F_yMM=M.y
F_yMMM=LLL y
F_yMMMM=LLLL y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='vuoden' Y 'viikko' w

I={0}–{1}

# labels of elements
L_era=aikakausi
L_year=vuosi
L_quarter=neljännesvuosi
L_month=kuukausi
L_week=viikko
L_day=päivä
L_weekday=viikonpäivä
L_dayperiod=vuorokaudenaika
L_hour=tunti
L_minute=minuutti
L_second=sekunti
L_zone=aikavyöhyke
