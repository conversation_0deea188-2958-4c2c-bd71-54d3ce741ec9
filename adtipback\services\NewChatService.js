/**
 * New Chat Service
 * 
 * This service handles all chat-related business logic for the new chat system
 * using the optimized database schema.
 */

const queryRunner = require('../dbConfig/queryRunner');
const FCMService = require('../utils/fcm');

class NewChatService {
  
  /**
   * Get user's conversations with pagination
   */
  async getUserConversations(userId, page = 1, limit = 20) {
    try {
      const offset = (page - 1) * limit;
      
      const sql = `
        SELECT 
          c.id as conversation_id,
          c.type,
          c.title,
          c.last_activity_at,
          cp.unread_count,
          cp.is_muted,
          cp.is_blocked,
          m.content as last_message_content,
          m.message_type as last_message_type,
          m.created_at as last_message_time,
          sender.id as last_message_sender_id,
          sender.name as last_message_sender_name,
          sender.profile_image as last_message_sender_avatar,
          -- Get other participant info for direct chats
          other_user.id as other_user_id,
          other_user.name as other_user_name,
          other_user.profile_image as other_user_avatar,
          other_presence.status as other_user_status,
          other_presence.last_seen_at as other_user_last_seen
        FROM conversations c
        JOIN conversation_participants cp ON c.id = cp.conversation_id
        LEFT JOIN messages m ON c.last_message_id = m.id
        LEFT JOIN users sender ON m.sender_id = sender.id
        -- Get other participant for direct chats
        LEFT JOIN conversation_participants other_cp ON (
          c.id = other_cp.conversation_id 
          AND other_cp.user_id != ? 
          AND other_cp.left_at IS NULL
          AND c.type = 'direct'
        )
        LEFT JOIN users other_user ON other_cp.user_id = other_user.id
        LEFT JOIN user_presence other_presence ON other_user.id = other_presence.user_id
        WHERE cp.user_id = ? 
          AND cp.left_at IS NULL 
          AND c.is_active = TRUE
        ORDER BY c.last_activity_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const conversations = await queryRunner.queryRunner(sql, [userId, userId, limit, offset]);
      
      // Get total count for pagination
      const countSql = `
        SELECT COUNT(*) as total
        FROM conversations c
        JOIN conversation_participants cp ON c.id = cp.conversation_id
        WHERE cp.user_id = ? AND cp.left_at IS NULL AND c.is_active = TRUE
      `;
      
      const countResult = await queryRunner.queryRunner(countSql, [userId]);
      const total = countResult[0].total;
      
      return {
        conversations,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasMore: offset + conversations.length < total
        }
      };
      
    } catch (error) {
      console.error('Error in getUserConversations:', error);
      throw error;
    }
  }

  /**
   * Get messages for a conversation with pagination
   */
  async getConversationMessages(userId, conversationId, page = 1, limit = 50) {
    try {
      // Verify user is participant
      const isParticipant = await this.verifyConversationParticipant(userId, conversationId);
      if (!isParticipant) {
        throw new Error('User is not a participant in this conversation');
      }
      
      const offset = (page - 1) * limit;
      
      const sql = `
        SELECT
          m.id,
          m.conversation_id,
          m.sender_id,
          m.message_type,
          m.content,
          m.reply_to_message_id,
          m.created_at,
          m.updated_at,
          sender.name as sender_name,
          sender.profile_image as sender_avatar,
          reply_msg.content as reply_to_content,
          reply_sender.name as reply_to_sender_name,
          -- Get delivery status
          (SELECT COUNT(*) FROM message_status ms
           WHERE ms.message_id = m.id AND ms.status = 'delivered') as delivered_count,
          (SELECT COUNT(*) FROM message_status ms
           WHERE ms.message_id = m.id AND ms.status = 'read') as read_count,
          -- Check if current user has read this message
          (SELECT ms.timestamp FROM message_status ms
           WHERE ms.message_id = m.id AND ms.user_id = ? AND ms.status = 'read') as read_by_user
        FROM messages m
        JOIN users sender ON m.sender_id = sender.id
        LEFT JOIN messages reply_msg ON m.reply_to_message_id = reply_msg.id
        LEFT JOIN users reply_sender ON reply_msg.sender_id = reply_sender.id
        WHERE m.conversation_id = ?
        ORDER BY m.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const messages = await queryRunner.queryRunner(sql, [
        userId, conversationId, limit, offset
      ]);
      
      // Reverse to show oldest first
      messages.reverse();
      
      return {
        messages,
        pagination: {
          page,
          limit,
          hasMore: messages.length === limit
        }
      };
      
    } catch (error) {
      console.error('Error in getConversationMessages:', error);
      throw error;
    }
  }

  /**
   * Create or get existing conversation between users
   */
  async createOrGetConversation(userId, participantId, type = 'direct', title = null) {
    try {
      // For direct chats, check if conversation already exists
      if (type === 'direct') {
        const existingSql = `
          SELECT c.id 
          FROM conversations c
          JOIN conversation_participants cp1 ON c.id = cp1.conversation_id
          JOIN conversation_participants cp2 ON c.id = cp2.conversation_id
          WHERE c.type = 'direct'
            AND cp1.user_id = ? AND cp1.left_at IS NULL
            AND cp2.user_id = ? AND cp2.left_at IS NULL
        `;
        
        const existing = await queryRunner.queryRunner(existingSql, [userId, participantId]);
        
        if (existing && existing.length > 0) {
          return { conversationId: existing[0].id, isNew: false };
        }
      }
      
      // Create new conversation
      const createSql = `
        INSERT INTO conversations (type, title, created_by, created_at, last_activity_at)
        VALUES (?, ?, ?, NOW(), NOW())
      `;
      
      const result = await queryRunner.queryRunner(createSql, [type, title, userId]);
      const conversationId = result.insertId;
      
      // Add participants
      const participantsSql = `
        INSERT INTO conversation_participants (conversation_id, user_id, joined_at)
        VALUES (?, ?, NOW()), (?, ?, NOW())
      `;
      
      await queryRunner.queryRunner(participantsSql, [
        conversationId, userId, conversationId, participantId
      ]);
      
      return { conversationId, isNew: true };
      
    } catch (error) {
      console.error('Error in createOrGetConversation:', error);
      throw error;
    }
  }

  /**
   * Send a message
   */
  async sendMessage(senderId, conversationId, messageData) {
    try {
      const { content, messageType = 'text', replyToMessageId } = messageData;

      // Verify sender is participant
      const isParticipant = await this.verifyConversationParticipant(senderId, conversationId);
      if (!isParticipant) {
        throw new Error('Sender is not a participant in this conversation');
      }

      // Insert message
      const sql = `
        INSERT INTO messages (
          conversation_id, sender_id, message_type, content,
          reply_to_message_id, created_at
        )
        VALUES (?, ?, ?, ?, ?, NOW())
      `;

      const result = await queryRunner.queryRunner(sql, [
        conversationId, senderId, messageType, content,
        replyToMessageId
      ]);
      
      const messageId = result.insertId;
      
      // Update conversation last activity
      await this.updateConversationActivity(conversationId, messageId);
      
      // Update unread counts for other participants
      await this.updateUnreadCounts(conversationId, senderId);
      
      // Get the complete message data
      const messageSql = `
        SELECT m.*, u.name as sender_name, u.profile_image as sender_avatar
        FROM messages m
        JOIN users u ON m.sender_id = u.id
        WHERE m.id = ?
      `;
      
      const messageResult = await queryRunner.queryRunner(messageSql, [messageId]);
      
      return messageResult[0];
      
    } catch (error) {
      console.error('Error in sendMessage:', error);
      throw error;
    }
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(userId, conversationId, messageId = null) {
    try {
      let sql, params;
      
      if (messageId) {
        // Mark specific message as read
        sql = `
          INSERT INTO message_status (message_id, user_id, status, timestamp)
          VALUES (?, ?, 'read', NOW())
          ON DUPLICATE KEY UPDATE timestamp = NOW()
        `;
        params = [messageId, userId];
      } else {
        // Mark all unread messages as read
        sql = `
          INSERT INTO message_status (message_id, user_id, status, timestamp)
          SELECT m.id, ?, 'read', NOW()
          FROM messages m
          LEFT JOIN message_status ms ON (
            m.id = ms.message_id 
            AND ms.user_id = ? 
            AND ms.status = 'read'
          )
          WHERE m.conversation_id = ? 
            AND m.sender_id != ?
            AND ms.id IS NULL
          ON DUPLICATE KEY UPDATE timestamp = NOW()
        `;
        params = [userId, userId, conversationId, userId];
      }
      
      await queryRunner.queryRunner(sql, params);
      
      // Reset unread count
      const resetSql = `
        UPDATE conversation_participants 
        SET unread_count = 0
        WHERE conversation_id = ? AND user_id = ?
      `;
      
      await queryRunner.queryRunner(resetSql, [conversationId, userId]);
      
      return true;
      
    } catch (error) {
      console.error('Error in markMessagesAsRead:', error);
      throw error;
    }
  }

  /**
   * Get user presence status
   */
  async getUsersPresence(userIds) {
    try {
      if (!userIds || userIds.length === 0) {
        return [];
      }
      
      const placeholders = userIds.map(() => '?').join(',');
      const sql = `
        SELECT 
          u.id,
          u.name,
          u.profile_image,
          COALESCE(up.status, 'offline') as status,
          up.last_seen_at
        FROM users u
        LEFT JOIN user_presence up ON u.id = up.user_id
        WHERE u.id IN (${placeholders})
      `;
      
      const users = await queryRunner.queryRunner(sql, userIds);
      return users;
      
    } catch (error) {
      console.error('Error in getUsersPresence:', error);
      throw error;
    }
  }

  /**
   * Helper: Verify if user is participant in conversation
   */
  async verifyConversationParticipant(userId, conversationId) {
    try {
      const sql = `
        SELECT 1 FROM conversation_participants 
        WHERE user_id = ? AND conversation_id = ? AND left_at IS NULL
      `;
      const result = await queryRunner.queryRunner(sql, [userId, conversationId]);
      return result && result.length > 0;
    } catch (error) {
      console.error('Error in verifyConversationParticipant:', error);
      return false;
    }
  }

  /**
   * Helper: Update conversation last activity
   */
  async updateConversationActivity(conversationId, lastMessageId) {
    try {
      const sql = `
        UPDATE conversations 
        SET last_message_id = ?, last_activity_at = NOW(), updated_at = NOW()
        WHERE id = ?
      `;
      await queryRunner.queryRunner(sql, [lastMessageId, conversationId]);
    } catch (error) {
      console.error('Error in updateConversationActivity:', error);
    }
  }

  /**
   * Helper: Update unread counts for participants
   */
  async updateUnreadCounts(conversationId, senderId) {
    try {
      const sql = `
        UPDATE conversation_participants 
        SET unread_count = unread_count + 1
        WHERE conversation_id = ? AND user_id != ? AND left_at IS NULL
      `;
      await queryRunner.queryRunner(sql, [conversationId, senderId]);
    } catch (error) {
      console.error('Error in updateUnreadCounts:', error);
    }
  }
}

module.exports = new NewChatService();
