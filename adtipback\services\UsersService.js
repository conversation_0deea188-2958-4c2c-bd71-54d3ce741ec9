const _ = require("underscore");
const utils = require("../utils/utils");
const enums = require("../utils/enums");
const dbQuery = require("../dbConfig/queryRunner");
const jwt = require("jsonwebtoken");
let appenvconfig = require("../config/appenvconfig.js");
require("dotenv").config();
const { v4: uuidv4 } = require("uuid");
const WalletService = require("../services/WalletService.js");
const crypto = require("crypto");
const moment = require("moment");
const { log } = require("console");
const { RtcTokenBuilder, RtcRole } = require('agora-token');
const otpService = require("./OTPService");
//const { wsServer } = require('../index'); // Import WebSocket server
//const { wssAgora } = require('../index'); // Import Agora WebSocket server from the main file
//const { parsePhoneNumberFromString } = require('libphonenumber-js');
const admin = require('../config/firebase'); // Import Firebase Admin SDK
//const _ = require('lodash');

// Dynamically import wssAgora to avoid circular dependency
const getWssAgora = () => {
  try {
    const { wssAgora } = require('../index');
    return wssAgora;
  } catch (error) {
    console.error('Error importing wssAgora:', error);
    return null;
  }
};

// Hardcoded Agora values provided by the frontend
const AGORA_APP_ID = "********************************";
const AGORA_TOKEN = "********************************";
const AGORA_CHANNEL = "call_id";



const updateUserSocialLinks = async (userId, socialLinks) => {
  try {
    // Get current time and add 5 hours 30 minute
    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
    await dbQuery.queryRunner(
      `DELETE FROM user_social_links WHERE user_id=${userId}`
    );

    let values = socialLinks
      .map(
        (link) =>
          `(${userId}, '${link.platform}', '${link.link}','${updatedTime}')`
      )
      .join(", ");

    if (values.length > 0) {
      await dbQuery.queryRunner(
        `INSERT INTO user_social_links (user_id, platform, link,created_at) VALUES ${values}`
      );
    }
  } catch (error) {
    console.error("Error updating social links:", error);
    throw error;
  }
};

// Function to get the latest wallet balance
const getLatestBalance = async (userId) => {
  const balanceQuery = `
        SELECT totalBalance FROM wallet 
        WHERE createdby = ${userId} 
        ORDER BY id DESC LIMIT 1
    `;
  console.log("balanceQuery.....", balanceQuery);

  try {
    const [balanceResult] = await dbQuery.queryRunner(balanceQuery);
    console.log("balanceResult", balanceResult);

    // Check if the query returned a result
    if (balanceResult.totalBalance) {
      let totalBalance = parseFloat(balanceResult.totalBalance);
      if (!isNaN(totalBalance)) {
        // Round to the nearest integer
        totalBalance = Math.round(totalBalance);
        console.log("Returning rounded balance:", totalBalance);
        return totalBalance;
      } else {
        console.error(
          "Invalid totalBalance value:",
          balanceResult[0].totalBalance
        );
        return 0;
      }
    } else {
      console.log("No balance record found for user:", userId);
      return 0;
    }
  } catch (error) {
    console.error("Error fetching balance:", error);
    return 0;
  }
};
const deleteStatus = (statusId) => {
  return new Promise((resolve, reject) => {
    const sql = `Update status set is_active=0,updated_date=NOW() where id=${statusId}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: `deleted`,
            data: [],
          });
        } else {
          reject({
            status: 400,
            message: `Something went wrong`,
            data: [],
          });
        }
      })
      .catch((error) => {
        reject({
          status: 400,
          message: `Something went wrong: ${error}`,
          data: [],
        });
      });
  });
};

const getUserInterestAndLanguages = async (userId) => {
  try {
    const sql = `
      SELECT
        l.id AS language_id,
        l.name AS language_name,
        ul.is_primary AS is_language_primary,
        i.id AS interest_id,
        i.name AS interest_name,
        ui.is_primary AS is_interest_primary
      FROM user_languages ul
      LEFT JOIN languages l ON l.id = ul.language_id
      LEFT JOIN user_interests ui ON ui.user_id = ul.user_id
      LEFT JOIN interest i ON i.id = ui.interest_id
      WHERE ul.user_id = ${userId}
    `;

    const results = await dbQuery.queryRunner(sql);

    if (results && results.length > 0) {
      const languageMap = new Map();
      const interestMap = new Map();

      results.forEach((row) => {
        // Process languages
        if (row.language_id && !languageMap.has(row.language_id)) {
          languageMap.set(row.language_id, {
            id: row.language_id,
            name: row.language_name,
            isPrimary: !!row.is_language_primary,
          });
        }

        // Process interests
        if (row.interest_id && !interestMap.has(row.interest_id)) {
          interestMap.set(row.interest_id, {
            id: row.interest_id,
            name: row.interest_name,
            isPrimary: !!row.is_interest_primary,
          });
        }
      });

      return {
        userId: userId,
        languages: Array.from(languageMap.values()),
        interests: Array.from(interestMap.values()),
      };
    } else {
      return {
        status: 404,
        message: "User not found.",
      };
    }
  } catch (error) {
    console.error("Error fetching user details:", error);
    throw {
      status: 500,
      message: "Internal server error.",
      error: error.message,
    };
  }
};

const getStatusViewersList = (statusId) => {
  return new Promise((resolve, reject) => {
    const sql = `select sv.name,sv.profile_image from status_viewers sv where status_id=${statusId} order by created_date desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "status viewers found",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "status viewers not found",
            data: [],
          });
        }
      })
      .catch((error) => {
        reject({
          status: 400,
          message: `Something went wrong ${error}`,
          data: [],
        });
      });
  });
};

const getCurrentUserStatusList = (userId) => {
  return new Promise((resolve, reject) => {
    const sql = `select s.id,s.content,s.content_type,s.created_date from status s where created_by=${userId} and is_active=1 order by created_date asc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "status found",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "status not found",
            data: [],
          });
        }
      })
      .catch((error) => {
        reject({
          status: 400,
          message: `Something went wrong ${error}`,
          data: [],
        });
      });
  });
};

const addStatusViewers = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO status_viewers (status_id,created_by,name,profile_image,is_active,created_date,updated_date) Values(
    ${data.status_id},${data.created_by},"${data.name}","${data.profile_image}",1,NOW(),NOW())`;

    let sql1 = `SELECT COUNT(*) as totalCount from status_viewers where status_id=${data.status_id} and created_by=${data.created_by}`;
    //let sql2 = `UPDATE status set total_people_pending= total_people_pending-1,total_amount_pending=total_amount_pending-1, updated_date=now() where id=${data.status_id}`;
    let sql3 = `SELECT content_type, total_people_pending,total_amount_pending from status where id =${data.status_id}`;
    ///let sql5 = `INSERT INTO wallet (totalBalance, transaction_status, amount,transaction_date,createdby, createddate) VALUES (totalBalance+0.7,  4, 0.7,NOW(),${data.created_by},NOW());`;
    let sql4 = `SELECT totalBalance FROM wallet where createdby= ${data.created_by}  order by id desc limit 1;`;
    let sql6 = `UPDATE status set is_active=0  and updated_date=now() where id =${data.status_id}`;

    dbQuery
      .queryRunner(sql3)
      .then((result2) => {
        let contentType;
        let amountPending;
        if (
          result2 &&
          result2[0].total_people_pending > 1 &&
          result2[0].total_amount_pending > 1
        ) {
          contentType = result2[0].content_type;
          amountPending = result2[0].total_amount_pending;
          dbQuery
            .queryRunner(sql1)
            .then((result1) => {
              console.log("result1", result1);
              if (result1 && result1[0].totalCount > 0) {
                resolve({
                  status: 400,
                  message: "status viewer details alreday added",
                  data: [],
                });
              } else {
                dbQuery
                  .queryRunner(sql4)
                  .then((result4) => {
                    let userBalance;
                    if (result4 && result4.length > 0) {
                      userBalance = result4[0].totalBalance ?? 0;
                      console.log("User balance:", userBalance);
                    } else {
                      userBalance = 0;
                    }
                    let amountToDeduct;
                    console.log("content ", contentType);
                    if (contentType === "Text") {
                      amountToDeduct = 0.21;
                    } else if (contentType === "Image") {
                      amountToDeduct = 0.35;
                    } else {
                      amountToDeduct = 0.7;
                    }
                    Promise.all([
                      dbQuery.queryRunner(sql),
                      dbQuery.queryRunner(
                        `UPDATE status set total_people_pending= total_people_pending-1,total_amount_pending=total_amount_pending-${amountToDeduct}, updated_date=now() where id=${data.status_id}`
                      ),
                      dbQuery.queryRunner(
                        `INSERT INTO wallet (totalBalance, transaction_status, amount,transaction_date,createdby, createddate) VALUES (${
                          userBalance + amountToDeduct
                        },  4, 0.7,NOW(),${data.created_by},NOW());`
                      ),
                    ])
                      .then((result) => {
                        if (result && result.length != 0) {
                          resolve({
                            status: 200,
                            message: "status viewer details added",
                            data: [{ amount: amountToDeduct }],
                          });
                        } else {
                          resolve({
                            status: 400,
                            message: "status viewer details not added",
                            data: [],
                          });
                        }
                      })
                      .catch((error) => {
                        reject({
                          status: 400,
                          message: `Something went wrong ${error}`,
                          data: [],
                        });
                      });
                  })
                  .catch((error) => {
                    reject({
                      status: 400,
                      message: `Something went wrong ${error}`,
                      data: [],
                    });
                  });
              }
            })
            .catch((error) => {
              reject({
                status: 400,
                message: `Something went wrong ${error}`,
                data: [],
              });
            });
        } else {
          dbQuery.queryRunner(sql6);
          reject({
            status: 400,
            message: "status viewer details quota completed",
            data: [],
          });
        }
      })
      .catch((error) => {
        reject({
          status: 400,
          message: `Something went wrong ${error}`,
          data: [],
        });
      });
  });
};

const addStatus = (data) => {
  return new Promise((resolve, reject) => {
    const query = `
      INSERT INTO status (
        created_by, 
        full_name,
        profile_image, 
        campaign_name, 
        target_age, 
        target_location, 
        target_marital_status, 
        target_gender, 
        total_people, 
        total_people_pending, 
        total_amount, 
        total_amount_pending, 
        content_type, 
        content, 
        is_active, 
        created_date, 
        updated_date
      ) VALUES (${data.created_by}, "${data.full_name}","${
      data.profile_image
    }", "${data.campaign_name}", "${data.target_age}", '${JSON.stringify(
      data.target_location
    )}', "${data.target_marital_status}", "${data.target_gender}", ${
      data.total_people
    }, ${data.total_people}, ${data.total_amount}, ${data.total_amount}, "${
      data.content_type
    }", "${data.content}", 1, NOW(), NOW())
    `;

    dbQuery
      .queryRunner(query)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "status added",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "status not added",
            data: [],
          });
        }
      })
      .catch((error) => {
        reject({
          status: 400,
          message: `Something went wrong ${error}`,
          data: [],
        });
      });
  });
};

const createOrUpdateUserPlanMapping = async (user_id, planId) => {
  const planDetailsQuery = `SELECT * FROM subscription_plans WHERE id = ${planId} AND is_active = 1`;
  const getPlanDetails = await dbQuery.queryRunner(planDetailsQuery);

  if (getPlanDetails.length == 0) {
    return { message: "Plan not found" };
  }
  const durationInDays = Math.round(
    getPlanDetails[0].duration_in_months * 30.44
  );
  const startDate = moment().format("YYYY-MM-DD HH:mm:ss");
  const endDate = moment()
    .add(durationInDays, "days")
    .format("YYYY-MM-DD HH:mm:ss"); // Calculate end date

  // Check if there is already an active plan for this user
  const checkQuery = `SELECT * FROM user_plans WHERE user_id = ${user_id} AND status = 'active'`;
  const existingPlan = await dbQuery.queryRunner(checkQuery);

  if (existingPlan.length > 0) {
    // If active plan exists, update it
    const updateQuery = `
            UPDATE user_plans
            SET plan_id = ${planId}, start_date = '${startDate}', end_date = '${endDate}', updated_at = '${moment().format(
      "YYYY-MM-DD HH:mm:ss"
    )}'
            WHERE id = ${existingPlan[0].id}
        `;
    await dbQuery.queryRunner(updateQuery);
    return { message: "user premium plan updated" };
  } else {
    // If no active plan exists, create a new mapping
    const insertQuery = `
            INSERT INTO user_plans (user_id, plan_id, start_date, end_date, status)
            VALUES (${user_id}, ${planId}, '${startDate}', '${endDate}', 'active')
        `;
    await dbQuery.queryRunner(insertQuery);
    return { message: "user premium plan inserted" };
  }
};

// Create an entry in the transaction table
const createTransactionEntry = async (data) => {
  const query = `
        INSERT INTO transactions (user_id, plan_id, phonepe_transaction_id, status,api_response,amount)
        VALUES (${data.user_id}, ${data.planId}, '${data.phonepe_transaction_id}', '${data.status}','${data.api_response}',${data.amount})
    `;

  try {
    const result = await dbQuery.queryRunner(query);
    console.log("result", result);
    return { message: "transaction inserted!" };
  } catch (error) {
    throw new Error("Error creating transaction entry: " + error.message);
  }
};

const upgradeUserPlan = async (user_id, planId) => {
  const query = `
        UPDATE users
        SET premium_plan_id = ${planId}
        WHERE id = ${user_id}
    `;

  try {
    await dbQuery.queryRunner(query);
    return { message: "update the plan of user" };
  } catch (error) {
    throw new Error("Error upgrading user plan: " + error.message);
  }
};

const handleUserDataWithDeleteInsert = async (
  userData,
  tableName,
  fieldId,
  fieldData
) => {
  if (!Array.isArray(fieldData) || fieldData.length === 0) return;

  const seen = new Set();
  const uniqueData = fieldData
    .map((item) => {
      if (typeof item === "number") {
        return { id: item, isPrimary: false };
      } else if (item && typeof item.id === "number") {
        return { id: item.id, isPrimary: !!item.isPrimary };
      } else {
        return null;
      }
    })
    .filter((item) => item && !seen.has(item.id) && seen.add(item.id));

  const deleteSql = `DELETE FROM ${tableName} WHERE user_id = ${userData.id}`;
  await dbQuery.queryRunner(deleteSql);

  const insertValues = uniqueData
    .map(
      (item) =>
        `(${userData.id}, ${item.id}, ${item.isPrimary ? 1 : 0})`
    )
    .join(", ");

  const insertSql = `
    INSERT INTO ${tableName} (user_id, ${fieldId}, is_primary)
    VALUES ${insertValues};
  `;

  await dbQuery.queryRunner(insertSql);
};


let checkReferalCodeValid = (data) => {
  return new Promise((resolve, reject) => {
    if (!data.referalCode || data.referalCode.length < 7) {
      resolve({
        status: 200,
        message: "Referal Code is Invalid",
        data: [
          {
            isValid: 0,
            referalCode: data.referalCode,
            referalCreator: null,
          },
        ],
      });
    } else {
      let sql = `SELECT id from users where referal_code="${data.referalCode}"`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            console.log("result valid of referal code", result);
            var creatorId = result[0].id;

            dbQuery
              .queryRunner(
                `SELECT * from referal_details where applied_by=${data.userId} and referal_code="${data.referalCode}"`
              )
              .then((result) => {
                if (result && result.length != 0) {
                  resolve({
                    status: 200,
                    message: "Referal Code used by user",
                    data: [
                      {
                        isValid: 0,
                        referalCode: data.referalCode,
                        referalCreator: creatorId,
                      },
                    ],
                  });
                } else {
                  resolve({
                    status: 200,
                    message: "Referal Code is valid",
                    data: [
                      {
                        isValid: 1,
                        referalCode: data.referalCode,
                        referalCreator: creatorId,
                      },
                    ],
                  });
                }
              });
          } else {
            resolve({
              status: 200,
              message: "Referal Code is Invalid",
              data: [
                {
                  isValid: 0,
                  referalCode: data.referalCode,
                  referalCreator: null,
                },
              ],
            });
          }
        })
        .catch((error) => {
          reject({
            status: 400,
            message: "Something went wrong generating referral",
            data: [],
          });
        });
    }
  });
};
let getTransactionsOfReferalWithdrawRequests = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from referal_withdraw_request where user_id=${data.user_id} and status="${data.status}" order by time desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Data found",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Data not found",
            data: [],
          });
        }
      })
      .catch((e) => {
        reject({
          status: 400,
          message: `Data not found ${e}`,
          data: [],
        });
      });
  });
};
let saveReferWithdrawRequest = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO referal_withdraw_request(name,mobile,withdraw_amount,
    time,status,upi_id,user_id)
    VALUES("${data.name}","${data.mobile}",${data.withdraw_amount},NOW(),"Unpaid","${data.upi_id}",${data.user_id})`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let sql1 = `UPDATE users set referal_earnings=referal_earnings-${data.withdraw_amount} where id=${data.user_id}`;
          dbQuery.queryRunner(sql1);
          resolve({
            status: 200,
            message: "Withdraw request added",
            data: [data],
          });
        } else {
          reject({
            status: 400,
            message: "Unable to send Withdraw request.",
            data: [],
          });
        }
      })
      .catch((err) => {
        console.log("error", err);
        reject({
          status: 400,
          message: `Unable to send Withdraw request. ${err}`,
          data: [],
        });
      });
  });
};

const generateReferalId = (userId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT referal_code, referal_earnings FROM users WHERE id = ${userId}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result[0]) {
          let referalCode = result[0].referal_code;

          // If referral code doesn't exist or is an empty string
          if (!referalCode || referalCode === "") {
            const uuid = uuidv4(); // Assuming uuidv4 is already imported
            const code = `ADTIP${userId}${uuid.substring(0, 2).toUpperCase()}`;

            // Generate and update the referral code in the database
            let updateSql = `UPDATE users SET referal_code="${code}" WHERE id=${userId}`;
            dbQuery
              .queryRunner(updateSql)
              .then((updateResult) => {
                if (updateResult) {
                  resolve({
                    status: 200,
                    message: "Referral code generated",
                    data: [
                      {
                        referal_code: code,
                        total_earnings: 0, // Assuming 0 is the initial earnings
                      },
                    ],
                  });
                } else {
                  reject({
                    status: 400,
                    message: "Unable to generate referral code",
                    data: [],
                  });
                }
              })
              .catch((error) => {
                reject({
                  status: 400,
                  message: `Error updating referral code: ${error.message}`,
                  data: [],
                });
              });
          } else {
            resolve({
              status: 200,
              message: "Referral code exists",
              data: result,
            });
          }
        } else {
          reject({
            status: 400,
            message: "User not found or invalid userId",
            data: [],
          });
        }
      })
      .catch((error) => {
        reject({
          status: 400,
          message: `Something went wrong: ${error.message}`,
          data: [],
        });
      });
  });
};

let updateOtpUser = (userData) =>
  new Promise(async (resolve, reject) => {
    try {
      // Use the OTP that was already sent via SMS, don't generate a new one
      const result = await otpService.updateOTPWithExistingOTP(userData.mobileNumber, userData.otp, userData.messageId);
      
      // Transform data to match expected format
      let userMobile = userData.mobileNumber;
      delete userData.mobileNumber;
      let userType = userData.userType;
      delete userData.userType;
      userData.mobile_number = userMobile;
      userData.user_type = userType;
      userData.otp = "";
      userData.messageId = "";
      userData.isOtpVerified = 0;
      userData.is_first_time = true;

      resolve({
        status: 200,
        message: "OTP sent on registered mobile number.",
        data: [userData],
      });
    } catch (error) {
      reject({
        status: error.status || 500,
        message: error.message || "OTP not sent.",
        data: [],
      });
    }
  });

let getUserById = (id) =>
  new Promise((resolve, reject) => {
    let sql = `select * from users where id=${id};`;
    dbQuery
      .queryRunner(sql)
      .then(async (result) => {
        if (result.length == 0) {
          return reject({
            status: 400,
            message: "User not found",
          });
        }

        const userLanguagesAndInterestsQuery = `
            SELECT 
                CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('{',
                  '"id":', l.id, ',',
                  '"name":"', l.name, '",',
                  '"isPrimary":', IF(ul.is_primary = 1, 'true', 'false'),
                  '}')), ']') AS languages,
                CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('{',
                  '"id":', i.id, ',',
                  '"name":"', i.name, '",',
                  '"isPrimary":', IF(ui.is_primary = 1, 'true', 'false'),
                  '}')), ']') AS interests
            FROM users u
            LEFT JOIN user_languages ul ON u.id = ul.user_id
            LEFT JOIN languages l ON ul.language_id = l.id
            LEFT JOIN user_interests ui ON u.id = ui.user_id
            LEFT JOIN interest i ON ui.interest_id = i.id
            WHERE u.id=${result[0].id}
          `;

        const getUserInterestAndLanguages = await dbQuery.queryRunner(
          userLanguagesAndInterestsQuery
        );

        getUserInterestAndLanguages.map((row) => ({
          languages: row.languages ? JSON.parse(row.languages) : [],
          interests: row.interests ? JSON.parse(row.interests) : [],
        }));

        // Extracting languages and interests from the response
        if (getUserInterestAndLanguages.length > 0) {
          const { languages, interests } = getUserInterestAndLanguages[0];

          result[0] = Object.assign(result[0], {
            languages: JSON.parse(languages),
            interests: JSON.parse(interests),
            is_available: result[0].is_available === 1,
            online_status: result[0].online_status === 1,
            dnd: result[0].dnd === 1,
          });
        }

        if (result && result.length != 0) {
          return resolve({
            status: 200,
            message: "Fetch user successfully.",
            data: result,
          });
        } else {
          return reject({
            status: 400,
            message: "User not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        return reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

// let updateUser = (userData) =>
//   new Promise((resolve, reject) => {
//     const sql = `update users set name='${userData.name || null}',gender='${
//       userData.gender || null
//     }',dob='${userData.dob || ""}',profile_image="${userData.profile_image}",
//     profession='${userData.profession || null}',maternal_status='${
//       userData.maternal_status || null
//     }',address='${userData.address || null}',emailId="${
//       userData.emailId || null
//     }",
//     longitude='${userData.longitude || null}',latitude='${
//       userData.latitude || null
//     }',pincode='${userData.pincode || null}',isSaveUserDetails=1 where id=${
//       userData.id
//     }`;

//     dbQuery
//       .queryRunner(sql)
//       .then((result) => {
//         if (result && result.length != 0) {
//           resolve({
//             status: 200,
//             message: "update user successfully.",
//             data: result,
//           });
//         } else {
//           reject({
//             status: 400,
//             message: "User not updated.",
//             data: result,
//           });
//         }
//       })
//       .catch((err) => {
//         reject({
//           status: 500,
//           message: err,
//           data: [],
//         });
//       });
//   });

const generateUserCoupon = async (id) => {
  try {
    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
    console.log("Generating coupon for user:", id);

    // Function to generate a unique coupon code
    const generateCouponCode = (user_id) => {
      const idHash = crypto
        .createHash("md5")
        .update(String(user_id))
        .digest("hex");
      const shortHash = idHash.slice(0, 2).toUpperCase();
      const randomChars = Math.random()
        .toString(36)
        .substring(2, 4)
        .toUpperCase();
      return `SAVE${user_id}${shortHash}${randomChars}50`;
    };

    const couponCode = generateCouponCode(id);

    // Fetch plan IDs from subscription_plans table
    const plans = await dbQuery.queryRunner(
      "SELECT id, discount_normal,discount_referral,actual_price FROM subscription_plans;"
    );
    const planDetails = plans.map((plan) => ({
      plan_id: plan.id,
      discount_value: plan.discount_referral,
      discount_normal: plan.discount_normal,
      actual_price: plan.actual_price,
      discount_type: "amount",
    }));

    // Check if a coupon already exists for the user
    const existingCoupon = await dbQuery.queryRunner(
      `SELECT coupon_id, description FROM coupons WHERE owner_user_id = ${id} AND is_active = true;`
    );

    if (existingCoupon.length > 0) {
      const coupon_id = existingCoupon[0].coupon_id;

      const updatedCouponDetails = planDetails.map((plan) => ({
        coupon_id,
        code: couponCode,
        plan_id: plan.plan_id,
        expiry_date: null,
        is_active: 1,
        created_at: updatedTime,
        actual_price: plan.actual_price,
        description: `Welcome Coupon - ₹${plan.discount_value} off`,
        discount_type: plan.discount_type,
        discount_value: plan.discount_value,
        max_uses: null,
        used_count: 0,
        owner_user_id: id,
        updated_at: updatedTime,
      }));

      await dbQuery.queryRunner(
        `UPDATE coupons SET description = '${JSON.stringify(
          updatedCouponDetails
        )}', updated_at = '${updatedTime}' WHERE coupon_id = ${coupon_id};`
      );

      return {
        status: true,
        message: "Coupon updated successfully",
        couponCode,
      };
    }

    // Insert a new coupon first
    const insertQuery = `
      INSERT INTO coupons (code, expiry_date, is_active, created_at, description, max_uses, used_count, owner_user_id, updated_at)
      VALUES ('${couponCode}', NULL, 1, '${updatedTime}', '',  NULL, 0, ${id}, '${updatedTime}');
    `;

    await dbQuery.queryRunner(insertQuery);
    const getCouponIdQuery = `SELECT coupon_id FROM coupons WHERE owner_user_id = ${id} ORDER BY created_at DESC LIMIT 1;`;
    const couponData = await dbQuery.queryRunner(getCouponIdQuery);
    const coupon_id = couponData[0]?.coupon_id;

    // Prepare the coupon details with the retrieved coupon_id
    const initialCouponDetails = planDetails.map((plan) => ({
      coupon_id,
      code: couponCode,
      plan_id: plan.plan_id,
      expiry_date: null,
      is_active: 1,
      created_at: updatedTime,
      actual_price: plan.actual_price,
      description: `Welcome Coupon - ₹${plan.discount_value} off`,
      discount_type: plan.discount_type,
      discount_value: plan.discount_value,
      max_uses: null,
      used_count: 0,
      owner_user_id: id,
      updated_at: updatedTime,
    }));

    // Update the coupon with the correct JSON description
    await dbQuery.queryRunner(
      `UPDATE coupons SET description = '${JSON.stringify(
        initialCouponDetails
      )}' WHERE coupon_id = ${coupon_id};`
    );

    return {
      status: true,
      message: "Referral coupon generated successfully",
      couponCode,
    };
  } catch (error) {
    console.error("Error generating coupon:", error);
    return { error: "Failed to generate coupon" };
  }
};

const updateUser = (userData) =>
  new Promise(async (resolve, reject) => {
    console.log("userData", userData);

    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
    // Dynamically build the SET part of the SQL query
    let setValues = [];

    // Check if the standard fields are present
    if (userData.name) setValues.push(`name='${userData.name}'`);
    if (userData.firstname) setValues.push(`firstname='${userData.firstname}'`);
    if (userData.lastname) setValues.push(`lastname='${userData.lastname}'`);
    if (userData.gender) setValues.push(`gender='${userData.gender}'`);
    if (userData.dob) setValues.push(`dob='${userData.dob}'`);
    if (userData.profile_image)
      setValues.push(`profile_image="${userData.profile_image}"`);
    if (userData.profession)
      setValues.push(`profession='${userData.profession}'`);
    if (userData.maternal_status)
      setValues.push(`maternal_status='${userData.maternal_status}'`);
    if (userData.address) setValues.push(`address='${userData.address}'`);
    if (userData.emailId) setValues.push(`emailId="${userData.emailId}"`);
    if (userData.longitude) setValues.push(`longitude='${userData.longitude}'`);
    if (userData.latitude) setValues.push(`latitude='${userData.latitude}'`);
    if (userData.pincode) setValues.push(`pincode='${userData.pincode}'`);

    // If a new referral code is generated, add it to the update statement
    // if (userData.referal_code)
    //   setValues.push(
    //     `referal_code='${userData.referal_code ? userData.referal_code : ""}'`
    //   );

    // Add a condition for the "isSaveUserDetails" flag if at least one field is updated
    if (setValues.length > 0) {
      setValues.push("isSaveUserDetails=1");
    }

    // If no standard fields are provided, check for languages or interests
    if (setValues.length === 0) {
      if (userData.languages && userData.languages.length > 0) {
        setValues.push("isSaveUserDetails=1");
      } else if (userData.interests && userData.interests.length > 0) {
        setValues.push("isSaveUserDetails=1");
      }
    }

    // If no fields are provided for update (including languages or interests)
    if (setValues.length === 0) {
      return reject({
        status: 400,
        message: "No valid fields provided for update.",
        data: [],
      });
    }

    // Join all set values and construct the final SQL query
    const userSql = `UPDATE users SET ${setValues.join(
      ", "
    )}, updated_date='${updatedTime}' WHERE id=${userData.id}`;

    try {
      // Update user details
      const userResult = await dbQuery.queryRunner(userSql);

      if (!userResult || userResult.affectedRows === 0) {
        return reject({
          status: 400,
          message: "User not updated.",
          data: [],
        });
      }

      try {
        // Handle languages and interests update (if provided)
        await handleUserDataWithDeleteInsert(
          userData,
          "user_languages",
          "language_id",
          userData.languages
        );
        await handleUserDataWithDeleteInsert(
          userData,
          "user_interests",
          "interest_id",
          userData.interests
        );
      } catch (error) {
        console.error(error.message);
        throw error;
      }

      if (![undefined, null, ""].includes(userData.referal_code)) {
        console.log("I m deoo s");
        //here we are adding bonus to referrer wallet if someone came from his link
        const referringBonus = await WalletService.createReferralTransaction(
          userData.referal_code,
          userData.id
        );
        console.log("referringBonus", referringBonus);
      }

      resolve({
        status: 200,
        message: "User updated successfully.",
        data: userResult,
      });
    } catch (err) {
      reject({
        status: 500,
        message: err.message || "Internal Server Error",
        data: [],
      });
    }
  });

let checkUserExists = (userData) =>
  new Promise((resolve, reject) => {
    let sql = `
    SELECT * FROM users WHERE id=${userData.id}
    `;
    dbQuery
      .queryRunner(sql)
      .then(async (result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "OTP verified successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Invalid id.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getUserByMobileNumber = (mobileNumber) =>
  new Promise((resolve, reject) => {
    let sql = `select * from users where mobile_number='${mobileNumber}'`;
    return dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "User fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 400,
            message: "User not found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let updateOtpStatus = (userData) =>
  new Promise((resolve, reject) => {
    let sql = "";
    if (
      userData.userType.toString() === "1" ||
      userData.userType.toString() === "2"
    ) {
      sql = `update users set isOtpVerified=${userData.isOtpVerified} where id=${userData.id} and user_type=${userData.userType}`;
    } else {
      sql = `update users set isOtpVerified=${userData.isOtpVerified}, online_status=1 where id=${userData.id} and user_type=${userData.userType}`;
    }
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "update user successfully.",
            data: userData,
          });
        } else {
          reject({
            status: 400,
            message: "User not updated.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
let deletemessages = (id) =>
  new Promise((resolve, reject) => {
    let sql = `update user_chat set is_active=0,updateddate=now() where id=${id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Delete message successfully.",
            data: [],
          });
        } else {
          reject({
            status: 400,
            message: "Message not deleted.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate company not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let updateTicks = (userData) =>
  new Promise((resolve, reject) => {
    let sql = `update user_chat set is_seen=${userData.isSeen},updateddate=now() where id=${userData.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Status of seen updated successfully.",
            data: [],
          });
        } else {
          reject({
            status: 400,
            message: "Not yet seen.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate company not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let updateBlockUser = (userData) =>
  new Promise((resolve, reject) => {
    let sql = "";
    if (userData.isBlock) {
      sql += `INSERT INTO user_chat_details (user_id,is_block,createdby,createddate) VALUES(${userData.userId},'${userData.isBlock}',${userData.createdBy},NOW()) ON DUPLICATE KEY UPDATE is_block='${userData.isBlock}', updateddate=NOW();`;
    }
    if (userData.isMute) {
      sql += `INSERT INTO user_chat_details (user_id,is_mute,createdby,createddate) VALUES(${userData.userId},'${userData.isMute}',${userData.createdBy},NOW()) ON DUPLICATE KEY UPDATE is_mute='${userData.isMute}', updateddate=NOW();`;
    }

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "User data update successfully.",
            data: [userData],
          });
        } else {
          reject({
            status: 400,
            message: "User data not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let sendNotification = (userData) =>
  new Promise((resolve, reject) => {
    let notificationBody = "";
    let userDetailsSQL = `select (select profile_image from users where id=${userData.createdBy}) as profileImage,(select name from users where id=${userData.createdBy}) as username,(select device_token from users where id=${userData.userId}) as registrationToken`;
    return dbQuery
      .queryRunner(userDetailsSQL)
      .then((result) => {
        if (
          result &&
          result.length != 0 &&
          result[0].registrationToken != null
        ) {
          let notificationTitle =
            userData.enum == 9
              ? "Crossed you"
              : enums.notification[userData.enum];
          notificationBody = {
            messageId: userData.messageId ? userData.messageId : null,
            userId: userData.userId,
            profileImage: result[0].profileImage,
            notificationId: userData.enum,
            createdBy: userData.createdBy,
            title: enums.notification[userData.enum],
            message: `${result[0].username} ${notificationTitle}`,
            registrationToken: result[0].registrationToken,
          };
          return utils.sendFcmNotification(notificationBody);
        } else {
          return reject({
            status: 400,
            message: "User request save but notification not send",
            data: result,
          });
        }
      })
      .then((result) => {
        if (result && result.status === 200) {
          let notificationSql = `INSERT INTO notifications(user_id,title,notification_type,device_token,message,fcm_response,is_active,createdby,created_date) VALUES (${
            userData.userId
          },'${notificationBody.title}',${notificationBody.notificationId},'${
            notificationBody.registrationToken
          }','${notificationBody.message}','${JSON.stringify(result.data)}',1,${
            userData.createdBy
          },NOW());`;
          return dbQuery.queryRunner(notificationSql);
        } else {
          reject(result);
        }
      })
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "User view request sent",
            data: [result],
          });
        } else {
          reject(result);
        }
      })
      .catch((err) => {
        let notificationSql = `INSERT INTO notifications(user_id,title,notification_type,device_token,message,fcm_response,is_active,createdby,created_date) 
            VALUES (${userData.userId},'${notificationBody.title}',${
          notificationBody.notificationId
        },'${notificationBody.registrationToken}','${
          notificationBody.message
        }','${JSON.stringify(err)}',0,${userData.createdBy},NOW());`;
        dbQuery.queryRunner(notificationSql);
        reject(err);
      });
  });

let sendCallingNotification = (userData) =>
  new Promise((resolve, reject) => {
    let notificationBody = "";
    let userDetailsSQL = `select (select profile_image from users where id=${userData.sender}) as profileImage,(select name from users where id=${userData.sender}) as username,(select device_token from users where id=${userData.reciever}) as registrationToken`;
    userData.enum = 1;
    return dbQuery
      .queryRunner(userDetailsSQL)
      .then((result) => {
        if (
          result &&
          result.length != 0 &&
          result[0].registrationToken != null
        ) {
          let notificationTitle = "Calling you";
          notificationBody = {
            messageId: userData.messageId ? userData.messageId : null,
            userId: userData.userId,
            profileImage: result[0].profileImage,
            notificationId: userData.enum,
            createdBy: userData.createdBy,
            title: enums.notification[userData.enum],
            message: `${result[0].username} ${notificationTitle}`,
            registrationToken: result[0].registrationToken,
          };
          return utils.sendFcmNotification(notificationBody);
        } else {
          return reject({
            status: 400,
            message: "Audio Calling Failed",
            data: result,
          });
        }
      })
      .then((result) => {
        if (result && result.status === 200) {
          let notificationSql = `INSERT INTO notifications(user_id,title,notification_type,device_token,message,fcm_response,is_active,createdby,created_date) VALUES (${
            userData.reciever
          },'${notificationBody.title}',${notificationBody.notificationId},'${
            notificationBody.registrationToken
          }','${notificationBody.message}','${JSON.stringify(result.data)}',1,${
            userData.sender
          },NOW());`;
          return dbQuery.queryRunner(notificationSql);
        } else {
          reject(result);
        }
      })
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Audio Calling request sent",
            data: [result],
          });
        } else {
          reject(result);
        }
      })
      .catch((err) => {
        let notificationSql = `INSERT INTO notifications(user_id,title,notification_type,device_token,message,fcm_response,is_active,createdby,created_date) 
            VALUES (${userData.reciever},'${notificationBody.title}',${
          notificationBody.notificationId
        },'${notificationBody.registrationToken}','${
          notificationBody.message
        }','${JSON.stringify(err)}',0,${userData.sender},NOW());`;
        dbQuery.queryRunner(notificationSql);
        reject(err);
      });
  });

let smsNotification = (userData) =>
  new Promise((resolve, reject) => {
    let notificationBody = "";
    let userDetailsSQL = `select (select profile_image from users where id=${userData.userId}) as profileImage,
    (select name from users where id=${userData.userId}) as username,
    (select device_token from users where id=${userData.receiverId}) as registrationToken,
    (select is_block from user_chat_details where createdby=${userData.receiverId} and user_id=${userData.userId}) as is_block,
    (select is_mute from user_chat_details where createdby=${userData.receiverId} and user_id=${userData.userId}) as is_mute;`;
    return dbQuery
      .queryRunner(userDetailsSQL)
      .then((result) => {
        if (
          result &&
          result.length != 0 &&
          result[0].registrationToken != null
        ) {
          notificationBody = {
            messageId: userData.messageId,
            senderId: userData.userId,
            profileImage: result[0].profileImage,
            notificationId: userData.enum,
            createdBy: userData.userId,
            title: enums.notification[userData.enum],
            messageContent: userData.message,
            is_mute: result[0].is_mute,
            is_block: result[0].is_block,
            senderByName: result[0].username,
            message: `${result[0].username} ${
              enums.notification[userData.enum]
            }`,
            registrationToken: result[0].registrationToken,
          };
          return utils.sendFcmNotification(notificationBody);
        } else {
          return reject({
            status: 400,
            message: "Message not send",
            data: [result],
          });
        }
      })
      .then((result) => {
        if (result && result.status === 200) {
          let notificationSql = `INSERT INTO notifications(user_id,title,notification_type,device_token,message,fcm_response,is_active,createdby,created_date) 
                VALUES (${userData.userId},'${notificationBody.title}',${
            notificationBody.notificationId
          },'${notificationBody.registrationToken}','${
            notificationBody.message
          }','${JSON.stringify(result.data)}',1,${userData.userId},NOW());`;
          return dbQuery.queryRunner(notificationSql);
        } else {
          reject(result);
        }
      })
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Message send",
            data: [result],
          });
        } else {
          reject(result);
        }
      })
      .catch((err) => {
        let notificationSql = `INSERT INTO notifications(user_id,title,notification_type,device_token,message,fcm_response,is_active,createdby,created_date) 
            VALUES (${userData.userId},'${notificationBody.title}',${
          notificationBody.notificationId
        },'${notificationBody.registrationToken}','${
          notificationBody.message
        }','${JSON.stringify(err)}',0,${userData.userId},NOW());`;
        dbQuery.queryRunner(notificationSql);
        reject(err);
      });
  });
let getMessage = (id) =>
  new Promise((resolve, reject) => {
    let sql = `select * from user_chat where id=${id} and is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch data successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Message not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let userSave = (userData) =>
  new Promise(async (resolve, reject) => {
    try {
      const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      let sql = `INSERT INTO users(mobile_number,otp,message_id,user_type,isOtpVerified,access_type,is_first_time,created_date,otp_created_at)
              VALUES('${userData.mobileNumber}','${userData.otp}','${userData.messageId}',${userData.userType},0,${userData.userType},true,'${updatedTime}',NOW());`;
      
      const result = await dbQuery.queryRunner(sql);
      
      if (result && result.length != 0) {
        let userMobile = userData.mobileNumber;
        delete userData.mobileNumber;
        let userType = userData.userType;
        delete userData.userType;
        userData.mobile_number = userMobile;
        userData.user_type = userType;
        userData.id = result.insertId;
        userData.otp = "";
        userData.messageId = "";
        userData.is_first_time = true;
        resolve({
          status: 200,
          message: "OTP sent on registered mobile number.",
          data: [userData],
        });
      } else {
        reject({
          status: 400,
          message: "OTP not sent.",
          data: [],
        });
      }
    } catch (err) {
      reject(err);
    }
  });
  
let getEducations = () =>
  new Promise((resolve, reject) => {
    let sql = `select * from education where is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch education successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Education not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
let getNotifications = (userId, page, limit) => {
  return new Promise((resolve, reject) => {
    let offset = page * limit;
    let sql = `SELECT * from mobile_notifications where user_id=${userId} OR user_id IS NULL order by time desc LIMIT ${limit} OFFSET ${offset}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "notification found",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "notification not found",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let saveNotifications = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO mobile_notifications(title,subtitle,user_id,image,send_to,data,time)VALUES("${
      data.title
    }","${data.subtitle}",${data.userId || null},"${data.image || ""}","${
      data.send_to || ""
    }",'${JSON.stringify(data.data || {})}',NOW())`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "notification added",
            data: [data],
          });
        } else {
          resolve({
            status: 200,
            message: "Notification not added.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getProfessions = () =>
  new Promise((resolve, reject) => {
    let sql = `select * from professions where is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch professions successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Professions not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

module.exports = {
  //getNotifications
  //generateReferalId
  //checkReferalCodeValid
  ///saveReferWithdrawRequest
  ///getTransactionsOfReferalWithdrawRequests
  ///addStatus
  ///addStatusViewers
  ///getCurrentUserStatusList
  ////getStatusViewersList
  ///getOtherUserStatusList
  ////deleteStatus
  deleteStatus: (statusId) =>
    new Promise((resolve, reject) => {
      return deleteStatus(statusId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getOtherUserStatusList: (userId) =>
    new Promise((resolve, reject) => {
      return getOtherUserStatusList(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getStatusViewersList: (statusId) =>
    new Promise((resolve, reject) => {
      return getStatusViewersList(statusId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getCurrentUserStatusList: (userId) =>
    new Promise((resolve, reject) => {
      return getCurrentUserStatusList(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  addStatusViewers: (data) =>
    new Promise((resolve, reject) => {
      return addStatusViewers(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  addStatus: (data) =>
    new Promise((resolve, reject) => {
      return addStatus(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getTransactionsOfReferalWithdrawRequests: (data) =>
    new Promise((resolve, reject) => {
      return getTransactionsOfReferalWithdrawRequests(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  saveReferWithdrawRequest: (data) =>
    new Promise((resolve, reject) => {
      return saveReferWithdrawRequest(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  checkReferalCodeValid: (data) => {
    return new Promise((resolve, reject) => {
      return checkReferalCodeValid(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  generateReferalId: (userId) => {
    return new Promise((resolve, reject) => {
      return generateReferalId(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  getNotifications: (userId, page, limit) => {
    return new Promise((resolve, reject) => {
      return getNotifications(userId, page, limit)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  saveNotifications: (data) => {
    return new Promise((resolve, reject) => {
      return saveNotifications(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  saveLoginOtp: (userData) =>
    new Promise(async (resolve, reject) => {
      try {
        let status = 200;
        
        // Check if user exists
        const userResult = await getUserByMobileNumber(userData.mobileNumber);
        status = userResult.status ? userResult.status : 200;

        // Generate OTP
        const otp = Math.floor(Math.random() * 899999 + 100000);
        userData.otp = otp;
        
        // Send OTP via SMS
        const smsResult = await utils.sendOtpSms(userData.mobileNumber, otp);
        userData.messageId = smsResult.length != 0 ? smsResult.Details : "";
        
        // Handle existing vs new user
        if (status != 400) {
          // Existing user - update OTP and clear session
          userData.id = userResult.data[0].id;
          await dbQuery.queryRunner(`UPDATE users SET current_session_token = NULL, current_device_id = NULL, device_type = NULL WHERE id = ${userData.id}`);
          const result = await updateOtpUser(userData);
          resolve(result);
        } else {
          // New user - create user with OTP
          const result = await userSave(userData);
          resolve(result);
        }
      } catch (error) {
        console.log("saveLoginOtp error:", error);
        reject(error);
      }
    }),

  userLogout: (userData) =>
    new Promise((resolve, reject) => {
      let sql = `update users set isOtpVerified = FALSE, current_session_token = NULL, current_device_id = NULL, device_type = NULL where id=${userData.id}`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "You have been Logged Out",
              accessToken: "Logout",
            });
          } else {
            reject({
              status: 400,
              message: "User Logout Processe Failed",
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  updateUserDeviceToken: (userData) =>
    new Promise((resolve, reject) => {
      let sql = `update users set device_token = '${userData.deviceToken}' where id=${userData.id}`;
      return dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Device token updated successfully.",
            });
          } else {
            reject({
              status: 400,
              message: "Unable to update device token",
            });
          }
        })
        .catch((err) => {
          console.log("error updated device token", err);
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  otpVerify: (userData) =>
    new Promise(async (resolve, reject) => {
      try {
        let message = "";
        
        // Use the new OTPService for verification
        const verifyResult = await otpService.verifyOTP(userData.mobile_number, userData.otp);
        
        if (!verifyResult.success) {
          reject({
            status: 400,
            message: verifyResult.message || "OTP verification failed.",
          });
          return;
        }

        // Get user data for the verified user
        const userResult = await getUserById(verifyResult.userId);
        
        if (userResult && userResult.status == 200) {
          // Generate referral ID
          await generateReferalId(verifyResult.userId);
          
          // Update OTP status
          userData.id = verifyResult.userId;
          userData.isOtpVerified = true;
          userData.userType = userResult.data[0].user_type;
          await updateOtpStatus(userData);
          
          // Get user details with languages and interests
          const getUserInterestAndLanguagesD = await getUserInterestAndLanguages(verifyResult.userId);
          
          // Generate JWT token
          const token = jwt.sign(
            { user_id: verifyResult.userId, email: userResult.data[0].emailId },
            process.env.JWT_KEY,
            { expiresIn: appenvconfig.expiresIn }
          );
          
          // --- Single Device Login Logic ---
          const deviceId = userData.device_id || uuidv4();
          const deviceType = userData.device_type || 'android';
          await dbQuery.queryRunner(
            `UPDATE users SET current_session_token = '${token}', current_device_id = '${deviceId}', device_type = '${deviceType}' WHERE id = ${verifyResult.userId}`
          );
          
          // Prepare response data
          const updatedData = userResult.data.map((user) => ({
            ...user,
            languages:
              getUserInterestAndLanguagesD.status == 404
                ? []
                : getUserInterestAndLanguagesD.languages,
            interests:
              getUserInterestAndLanguagesD.status == 404
                ? []
                : getUserInterestAndLanguagesD.interests,
            is_premium: user.premium_plan_id > 0 ? true : false,
            is_available: user.is_available == 0 ? false : true,
            online_status: user.online_status == 0 ? false : true,
            dnd: user.dnd == 0 ? false : true,
          }));
          
          resolve({
            status: 200,
            message: "OTP verify successful.",
            accessToken: token,
            data: updatedData,
          });
        } else {
          reject({
            status: 400,
            message: "User not found.",
            data: userResult.data,
          });
        }
      } catch (error) {
        console.log("OTP verification error:", error);
        reject({
          status: error.status || 500,
          message: error.message || "OTP verification failed.",
        });
      }
    }),
  saveUserDetails: (userData) =>
    new Promise((resolve, reject) => {
      return checkUserExists(userData)
        .then((result) => {
          if (result && result.status == 200 && result.data.length != 0) {
            return updateUser(userData);
          } else {
            return reject(result);
          }
        })
        .then((result) => {
          return getUserById(userData.id);
        })
        .then((result) => {
          if (result && result.status == 200) {
            resolve({
              status: 200,
              message: "User data saved successful",
              data: result.data,
            });
          } else {
            reject({
              status: 400,
              message: message,
              data: result.data,
            });
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  // updateUser: (userData) =>
  //   new Promise((resolve, reject) => {
  //     let sql = `update users set `;
  //     if (userData.name)
  //       sql += ` name='${userData.name ? userData.name : ""}',`;
  //     if (userData.gender)
  //       sql += ` gender='${userData.gender ? userData.gender : ""}',`;
  //     if (userData.longitude)
  //       sql += ` longitude='${userData.longitude ? userData.longitude : ""}',`;
  //     if (userData.latitude)
  //       sql += ` latitude='${userData.latitude ? userData.latitude : ""}',`;
  //     if (userData.profession)
  //       sql += ` profession='${
  //         userData.profession ? userData.profession : ""
  //       }',`;
  //     if (userData.maternalStatus)
  //       sql += ` maternal_status='${
  //         userData.maternalStatus ? userData.maternalStatus : ""
  //       }',`;

  //     if (userData.address)
  //       sql += ` address='${userData.address ? userData.address : ""}',`;
  //     if (userData.dob) sql += ` dob='${userData.dob ? userData.dob : ""}',`;
  //     if (userData.profileImage)
  //       sql += ` profile_image='${
  //         userData.profileImage ? userData.profileImage : ""
  //       }',`;
  //     sql += ` updated_date=NOW() where id=${userData.id}`;
  //     dbQuery
  //       .queryRunner(sql)
  //       .then((result) => {
  //         if (result && result.length != 0) {
  //           resolve({
  //             status: 200,
  //             message: "update user successfully.",
  //             data: [userData],
  //           });
  //         } else {
  //           reject({
  //             status: 400,
  //             message: "User not updated.",
  //             data: result,
  //           });
  //         }
  //       })
  //       .catch((err) => {
  //         reject({
  //           status: 500,
  //           message: err,
  //           data: [],
  //         });
  //       });
  //   }),

  updateUser: (userData) =>
    new Promise(async (resolve, reject) => {
      try {
        let updatedTime;

        // Check if updated_date is provided; if not, set it to the current time
        if (!userData.updated_date) {
          updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
        } else {
          updatedTime = moment(userData.updated_date)
            .utcOffset(330)
            .format("YYYY-MM-DD HH:mm:ss");
        }

        // Prepare the fields dynamically
        const fieldsToUpdate = [];
        if (userData.name) fieldsToUpdate.push(`name='${userData.name}'`);
        if (userData.gender) fieldsToUpdate.push(`gender='${userData.gender}'`);
        if (userData.emailId)
          fieldsToUpdate.push(`emailId='${userData.emailId}'`);
        if (userData.username)
          fieldsToUpdate.push(`username='${userData.username}'`);
        if (userData.longitude)
          fieldsToUpdate.push(`longitude='${userData.longitude}'`);
        if (userData.latitude)
          fieldsToUpdate.push(`latitude='${userData.latitude}'`);
        if (userData.profession)
          fieldsToUpdate.push(`profession='${userData.profession}'`);
        if (userData.maternalStatus)
          fieldsToUpdate.push(`maternal_status='${userData.maternalStatus}'`);
        if (userData.address)
          fieldsToUpdate.push(`address='${userData.address}'`);
        if (userData.dob) fieldsToUpdate.push(`dob='${userData.dob}'`);
        if (userData.profileImage)
          fieldsToUpdate.push(`profile_image='${userData.profileImage}'`);
        if (userData.bio) fieldsToUpdate.push(`bio='${userData.bio}'`);
        if (
          userData.online_status !== undefined &&
          userData.online_status !== null
        ) {
          fieldsToUpdate.push(`online_status=${userData.online_status}`);
        }
        if (
          userData.is_available !== undefined &&
          userData.is_available !== null
        ) {
          fieldsToUpdate.push(`is_available=${userData.is_available}`);
        }

        if (userData.dnd !== undefined && userData.dnd !== null) {
          fieldsToUpdate.push(`dnd=${userData.dnd}`);
        }

        // Ensure updated_date is always included
        fieldsToUpdate.push(`updated_date='${updatedTime}'`);

        if (fieldsToUpdate.length > 0) {
          const sql = `UPDATE users SET ${fieldsToUpdate.join(", ")} WHERE id=${
            userData.id
          }`;
          console.log("Executing SQL:", sql);
          const result = await dbQuery.queryRunner(sql);

          if (!result || result.length === 0) {
            return reject({
              status: 400,
              message: "User not updated.",
              data: result,
            });
          }
        }

        // Handle languages
        if (userData.languages) {
          await handleUserDataWithDeleteInsert(
            userData,
            "user_languages",
            "language_id",
            userData.languages
          );
        }

        // Handle interests
        if (userData.interests) {
          await handleUserDataWithDeleteInsert(
            userData,
            "user_interests",
            "interest_id",
            userData.interests
          );
        }

        // Handle Social Links
        if (userData.social_links && userData.social_links.length > 0) {
          await updateUserSocialLinks(userData.id, userData.social_links);
        }

        resolve({
          status: 200,
          message: "User updated successfully.",
          data: [userData],
        });
      } catch (err) {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      }
    }),

  savemessages: (userData) =>
    new Promise((resolve, reject) => {
      let message = userData.message ? userData.message.replace("'", "''") : '';
      let userId = userData.userId !== undefined && userData.userId !== null ? userData.userId : 'NULL';
      let receiverId = userData.receiverId !== undefined && userData.receiverId !== null ? userData.receiverId : 'NULL';
      let parentId = userData.parentId !== undefined && userData.parentId !== null ? userData.parentId : 0;
      let chatType = (userData.chat_type !== undefined && userData.chat_type !== null && userData.chat_type !== '') ? `'${userData.chat_type}'` : `'text'`;
      let chatTypeIdValue = (userData.chat_type_id_value !== undefined && userData.chat_type_id_value !== null) ? userData.chat_type_id_value : 0;
      // Adding a placeholder for the missing 7th argument
      let placeholderArg = 0; // Replace with actual parameter if known
      // Reordered to have userId (sender) first as per error message
      let sql = `call save_message(${userId}, ${receiverId}, '${message}', ${parentId}, ${chatType}, ${chatTypeIdValue}, ${placeholderArg})`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result[0].length != 0 && result[0][0].insertId != 0) {
            userData.enum = 6;
            userData.messageId = result[0][0].insertId;
            // smsNotification(userData);
            resolve({
              status: 200,
              message: "Send message successfully.",
              data: [userData],
            });
          } else {
            reject({
              status: 200,
              message: "Message not send, User blocked you.",
              data: result,
            });
          }
        })
        .catch((err) => {
          let message = "";
          if (err.message.includes("ER_DUP_ENTRY"))
            message = "Product already available.";
          if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
            message = "Invalid userId.";
          reject({
            status: 500,
            message: message != "" ? message : err.message,
            data: [],
          });
        });
    }),
  savechat: (userData) =>
    new Promise((resolve, reject) => {
      savechat(userData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  deletemessages: (id) =>
    new Promise((resolve, reject) => {
      return getMessage(id)
        .then((result) => {
          if (result && result.status == 200) {
            return deletemessages(id);
          } else {
            reject(result);
          }
        })
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getMessages: (userId, chattinguserid) =>
    new Promise((resolve, reject) => {
      // let sql = `select u.name as senderName, u.profile_image as senderNameProfileImage, u1.name as receiver,
      // u1.profile_image as receiverProfileImage, uc.id message_id,uc.message, uc.parent_id,uc.is_seen,uc.is_like,uc.createddate from user_chat uc
      // INNER JOIN users u ON uc.sender=u.id INNER JOIN users u1 ON uc.receiver=u1.id where uc.sender=${userId}`;
      let messageData = "",
        sql = "";
      if (chattinguserid == null) {
        sql = `call latest_message_byuser(${userId})`;
      } else {
        // Use direct SQL query instead of broken stored procedure
        // Generate chat_id in the format: chat_userId1_userId2 (sorted)
        const user1 = Math.min(parseInt(userId), parseInt(chattinguserid));
        const user2 = Math.max(parseInt(userId), parseInt(chattinguserid));
        const chatId = `chat_${user1}_${user2}`;

        sql = `SELECT
          m.id as message_id,
          m.content as message,
          m.sender_id as sender,
          m.recipient_id as receiver,
          m.sender_name as senderName,
          m.sender_name as receiverName,
          m.created_at as createddate,
          CASE WHEN m.status = 'read' THEN 1 ELSE 0 END as is_seen,
          0 as is_like,
          m.reply_to_message_id as parent_id
        FROM messages m
        WHERE m.chat_id = '${chatId}'
          AND m.is_deleted = 0
          AND ((m.sender_id = ${userId} AND m.recipient_id = ${chattinguserid})
               OR (m.sender_id = ${chattinguserid} AND m.recipient_id = ${userId}))
        ORDER BY m.created_at ASC`;
      }

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (chattinguserid == null) {
            // Handle stored procedure result for latest messages
            if (result && result[0].length != 0) {
              messageData = result[0];
              let receiverUserData = _.pluck(result[0], "receiver");
              let senderUserData = _.pluck(result[0], "sender");
              let userData = receiverUserData.concat(senderUserData);
              userData = _.uniq(userData);
              let d = _.without(userData, parseInt(userId));
              let userSql = `select u.id,u.profile_image,u.firstName,u.name,IFNULL(ucd.is_block,0) as is_block,
                      IFNULL(ucd.is_mute,0) as is_mute,ud.is_rating_profile,ud.rating from users u
                      LEFT JOIN user_chat_details ucd ON ucd.user_id=u.id and ucd.createdby=${userId}
                      LEFT JOIN user_details ud ON ud.user_id=u.id and ud.created_by=${userId}
                      where u.id in
                      (${d.toString()});`;
              return dbQuery.queryRunner(userSql);
            } else {
              return Promise.resolve([]);
            }
          } else {
            // Handle direct SQL result for specific chat messages
            if (result && result.length != 0) {
              messageData = result;
              resolve({
                status: 200,
                message: "Fetch data successfully.",
                data: messageData,
              });
            } else {
              resolve({
                status: 200,
                message: "No messages found.",
                data: [],
              });
            }
            return Promise.resolve([]);
          }
        })
        .then((result) => {
          if (result && result[0].length != 0) {
            messageData.forEach((message) => {
              result.forEach((user) => {
                if (message && message.receiver == user.id) {
                  message.receiver_profile_image = user.profile_image;
                  message.receiver_id = user.id;
                  message.receiver_name = user.name;
                  message.is_block = user.is_block;
                  message.is_mute = user.is_mute;
                  message.is_rating_profile = user.is_rating_profile;
                  message.rating = user.rating;
                }
                if (message && message.sender == user.id) {
                  message.sender_profile_image = user.profile_image;
                  message.sender_id = user.id;
                  message.sender_name = user.name;
                  message.is_block = user.is_block;
                  message.is_mute = user.is_mute;
                  message.is_rating_profile = user.is_rating_profile;
                  message.rating = user.rating;
                }
              });
            });
            resolve({
              status: 200,
              message: "Fetch data successfully.",
              data: messageData,
            });
          } else {
            resolve(result);
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  //not use
  getMessage: (usersData) =>
    new Promise((resolve, reject) => {
      // let sql = `select u.name as senderName, u.profile_image as senderNameProfileImage, u1.name as receiver,
      // u1.profile_image as receiverProfileImage, uc.id message_id,uc.message, uc.parent_id,uc.is_seen,uc.is_like,uc.createddate from user_chat uc
      // INNER JOIN users u ON uc.sender=u.id INNER JOIN users u1 ON uc.receiver=u1.id where uc.sender=${userId}`;
      let sql = `call getmessagesbysenderandreceiver(${usersData.loginuserid},${usersData.chattinguserid})`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Fetch data successfully.",
              data: result[0],
            });
          } else {
            resolve({
              status: 200,
              message: "Message not found.",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  clearAllchat: (userId, chattinguserid) =>
    new Promise((resolve, reject) => {
      let sql = `call clear_all_chat(${userId},${chattinguserid})`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result) {
            resolve({
              status: 200,
              message: "chat deleted.",
              data: [],
            });
          } else {
            resolve({
              status: 200,
              message: "Chat not deleted",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  deletechatforme: (userData) =>
    new Promise((resolve, reject) => {
      let sql = `call delete_chat_for_me(${userData.userId},${
        userData.chattinguserid
      },'${userData.messagesId.toString()}')`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result) {
            resolve({
              status: 200,
              message: "chat deleted.",
              data: [],
            });
          } else {
            resolve({
              status: 200,
              message: "Chat not deleted",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  deletechatforEveryone: (userData) =>
    new Promise((resolve, reject) => {
      let sql = `call delete_chat_for_everyone(${userData.userId},${
        userData.chattinguserid
      },'${userData.messagesId.toString()}')`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result) {
            sendNotification({
              createdBy: userData.userId,
              userId: userData.chattinguserid,
              enum: 8,
              messageId: userData.messagesId.toString(),
            });
            resolve({
              status: 200,
              message: "chat deleted.",
              data: [],
            });
          } else {
            resolve({
              status: 200,
              message: "Chat not deleted",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  seenAllMessage: (userId, chattinguserid) =>
    new Promise((resolve, reject) => {
      // Use simple SQL instead of missing stored procedure
      let sql = `UPDATE user_chat SET is_seen = 1, updateddate = NOW() 
                 WHERE receiver = ${userId} AND sender = ${chattinguserid} AND is_seen = 0 AND is_active = 1`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result) {
            sendNotification({
              createdBy: userId,
              userId: chattinguserid,
              enum: 7,
              messageId: null,
            });
            resolve({
              status: 200,
              message: "All messages seen.",
              data: [],
            });
          } else {
            resolve({
              status: 200,
              message: "No message not seen",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Internal server error.",
            data: [],
          });
        });
    }),

  getUnSeenMessageCount: (userId) =>
    new Promise((resolve, reject) => {
      // Use simple SQL instead of stored procedure
      let sql = `SELECT COUNT(*) as unread_count FROM user_chat 
                 WHERE receiver = ${userId} AND is_seen = 0 AND is_active = 1`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Fetch data successfully.",
              data: [{
                unread_count: result[0].unread_count || 0
              }],
            });
          } else {
            resolve({
              status: 200,
              message: "No unread messages.",
              data: [{
                unread_count: 0
              }],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Internal server error.",
            data: [],
          });
        });
    }),
  getMuteAndBlockUsers: (userId) =>
    new Promise((resolve, reject) => {
      let sql = `call getmuteandblockusers(${userId})`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Fetch data successfully.",
              data: result[0],
            });
          } else {
            resolve({
              status: 200,
              message: "Message not found.",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  saveticks: (userData) =>
    new Promise((resolve, reject) => {
      return getMessage(userData.id)
        .then((result) => {
          if (result && result.status == 200) {
            return updateTicks(userData);
          } else {
            reject(result);
          }
        })
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  updateBlockUser: (userData) =>
    new Promise((resolve, reject) => {
      return updateBlockUser(userData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  saveUserDeviceToken: (userData) =>
    new Promise((resolve, reject) => {
      let sql = `update users set device_token='${userData.deviceToken}' where id=${userData.id}`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "user device token saved.",
              data: userData,
            });
          } else {
            resolve({
              status: 200,
              message: "user device token not save.",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  getSentNotification: (userId) =>
    new Promise((resolve, reject) => {
      let sql = `select u.id, u.name, u.profile_image, n.title,n.message,n.notification_type,n.created_date,DATE_FORMAT(n.created_date,'%d/%m/%Y') AS formated_date from users u
        LEFT JOIN notifications n ON n.user_id=u.id  where u.is_active=1 and n.createdby=${userId} and n.is_active=1 order by u.created_date desc`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            let finalData = [];
            //let updateResult = dbDataMapping(result);
            //result=_.groupBy(result, 'formated_date');

            let categaryData = _.pluck(result, "formated_date");
            categaryData = _.uniq(categaryData);
            if (categaryData && categaryData.length != 0) {
              categaryData.forEach((element) => {
                let usersData = _.filter(
                  result,
                  (user) => user.formated_date === element
                );
                let categaryObj = {
                  name: element,
                  notification: usersData,
                };
                finalData.push(categaryObj);
              });
            }
            resolve({
              status: 200,
              message: "Fetch users notifications successfully.",
              data: finalData,
            });
          } else {
            resolve({
              status: 200,
              message: "Users notifications not found.",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  getReceivedNotification: (userId) =>
    new Promise((resolve, reject) => {
      let sql = `select u.id, u.name, u.profile_image, n.title,n.message,n.notification_type,n.created_date,n.created_date,DATE_FORMAT(n.created_date,'%d/%m/%Y') AS formated_date from users u
        LEFT JOIN notifications n ON n.createdby=u.id where u.is_active=1 and n.user_id=${userId} and n.is_active=1 order by n.created_date desc`;

      //LEFT JOIN user_details ud ON ud.user_id=u.id and ud.created_by=${userId}
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            let finalData = [];
            //let updateResult = dbDataMapping(result);
            //result=_.groupBy(result, 'formated_date');
            let categaryData = _.pluck(result, "formated_date");
            categaryData = _.uniq(categaryData);
            if (categaryData && categaryData.length != 0) {
              categaryData.forEach((element) => {
                let usersData = _.filter(
                  result,
                  (user) => user.formated_date === element
                );
                let categaryObj = {
                  name: element,
                  notification: usersData,
                };
                finalData.push(categaryObj);
              });
            }
            resolve({
              status: 200,
              message: "Fetch users notifications successfully.",
              data: finalData,
            });
          } else {
            resolve({
              status: 200,
              message: "Users notifications not found.",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getReceivedNotificationList: (userId, notification_type) =>
    new Promise((resolve, reject) => {
      let sql = `select TIMESTAMPDIFF(minute, n.created_date, NOW()) as duration_ago ,n.id as notification_id,u.id, u.name, u.profile_image, n.title,n.message,n.notification_type,n.created_date,n.created_date,DATE_FORMAT(n.created_date,'%d/%m/%Y') AS formated_date,n.read_by_user,u.* from users u
        LEFT JOIN notifications n ON n.createdby=u.id where u.is_active=1 and n.user_id=${userId} and n.is_active=1 and n.notification_type=${notification_type} order by n.created_date desc`;
      console.log(sql);
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            result.forEach((element1) => {
              if (element1) {
                let durationInText = "";
                if (element1.duration_ago == 0) {
                  durationInText = "Just Now";
                } else if (element1.duration_ago < 60) {
                  durationInText = element1.duration_ago + " Minutes Ago";
                } else if (element1.duration_ago / 60 < 24) {
                  durationInText =
                    ((element1.duration_ago / 60) | 0) + " Hours Ago";
                } else {
                  durationInText =
                    ((element1.duration_ago / 60 / 24) | 0) + " Days Ago";
                }
                element1.duration_ago_in_text = durationInText;
              }
            });
            return dbDataMapping(result);
          } else {
            return result;
          }
        })
        .then((result) => {
          return getUserAvgRating(result);
        })
        .then((result) => {
          resolve(result);
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  audioCalling: (userData) =>
    new Promise((resolve, reject) => {
      return sendCallingNotification(userData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getProfessions: () =>
    new Promise((resolve, reject) => {
      return getProfessions()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getEducations: () =>
    new Promise((resolve, reject) => {
      return getEducations()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getLanguages: () =>
    new Promise((resolve, reject) => {
      let sql = `select id,name from languages where is_active=1`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Fetch language successfully.",
              data: result,
            });
          } else {
            reject({
              status: 400,
              message: "language not found.",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getInterest: () =>
    new Promise((resolve, reject) => {
      let sql = `select id,name from interest`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Fetch user interests successfully.",
              data: result,
            });
          } else {
            reject({
              status: 400,
              message: "interest not found.",
              data: result,
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  getAllUsers: async ({
    id = 0,
    page = 1,
    limit = 20,
    language = [],
    interest = [],
    user_id,
    search_by_name,
    loggined_user_id,
    sortBy = {},
  }) => {
    try {
      // Validate language and interest arrays
      if (language.length > 1) {
        throw new Error("Only one language ID is allowed.");
      }
      if (interest.length > 1) {
        throw new Error("Only one interest ID is allowed.");
      }

      page = Math.max(1, page);
      const offset = (page - 1) * limit;
      let filters = "";
      const params = [];

      if (search_by_name) {
        filters += ` AND u.name LIKE ?`;
        params.push(`%${search_by_name}%`);
      }
      if (id > 0) {
        filters += ` AND u.id = ?`;
        params.push(id);
      }
      // Only apply loggined_user_id filter if search_by_name is not provided
      if (id === 0 && loggined_user_id && !search_by_name) {
        filters += ` AND u.id != ?`;
        params.push(loggined_user_id);
      }

      // Language and interest filters in JOIN conditions
      let languageFilter = "";
      let interestFilter = "";
      if (language.length === 1) {
        languageFilter = ` AND l.id = ?`;
        params.push(language[0]);
      }
      if (interest.length === 1) {
        interestFilter = ` AND i.id = ?`;
        params.push(interest[0]);
      }

      // Only apply user_followers and blocked_users joins if search_by_name is not provided
      const userFollowersJoin = search_by_name
        ? ""
        : `LEFT JOIN user_followers uf ON uf.followingId = u.id AND uf.followerId = ?`;
      const blockedUsersJoin = search_by_name
        ? ""
        : `LEFT JOIN blocked_users bu ON bu.user_id = ? AND bu.blocked_id = u.id`;

      // Adjust is_following and is_blocked based on whether joins are included
      const isFollowingClause = search_by_name
        ? "FALSE AS is_following"
        : `CASE WHEN uf.followerId IS NOT NULL THEN TRUE ELSE FALSE END AS is_following`;
      const isBlockedClause = search_by_name
        ? "FALSE AS is_blocked"
        : `CASE WHEN bu.is_blocked = 1 THEN TRUE ELSE FALSE END AS is_blocked`;

      // Sort by last_active (or updated_date) in descending order
      const orderBy = `ORDER BY COALESCE(ua.last_active, u.updated_date) DESC, u.id DESC`;

      const baseSql = `
        SELECT 
          u.id, u.name, u.emailId, u.is_available, u.dnd, u.updated_date,
          ua.last_active,
          CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('{',
            '"id":', l.id, ',',
            '"name":"', l.name, '",',
            '"isPrimary":', IF(ul.is_primary = 1, 'true', 'false'),
          '}') ORDER BY l.id), ']') AS languages,
          CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('{',
            '"id":', i.id, ',',
            '"name":"', i.name, '",',
            '"isPrimary":', IF(ui.is_primary = 1, 'true', 'false'),
          '}') ORDER BY i.id), ']') AS interests,
          COUNT(DISTINCT p.id) AS product_count,
          COUNT(DISTINCT ps.id) AS post_count,
          ${isFollowingClause},
          (SELECT COUNT(*) FROM user_followers WHERE followerId = u.id) AS following_count,
          (SELECT COUNT(*) FROM user_followers WHERE followingId = u.id) AS followers_count,
          ${isBlockedClause}
        FROM users u
        LEFT JOIN user_activity ua ON u.id = ua.user_id
        LEFT JOIN user_languages ul ON u.id = ul.user_id
        LEFT JOIN languages l ON ul.language_id = l.id ${languageFilter}
        LEFT JOIN user_interests ui ON u.id = ui.user_id
        LEFT JOIN interest i ON ui.interest_id = i.id ${interestFilter}
        LEFT JOIN product p ON u.id = p.created_by
        LEFT JOIN posts ps ON u.id = ps.user_id
        ${userFollowersJoin}
        ${blockedUsersJoin}
        WHERE 1=1 ${filters}
        GROUP BY u.id
        ${orderBy}
        LIMIT ? OFFSET ?
      `;
      // Only push loggined_user_id for joins if search_by_name is not provided
      if (!search_by_name) {
        params.push(loggined_user_id, loggined_user_id);
      }
      params.push(limit, offset);

      // Execute the main query and count query in parallel
      const countParams = [...params.slice(0, -2)]; // Remove limit and offset from params for count query

      // Create count query with same filters but without GROUP BY, ORDER BY, LIMIT, OFFSET
      const countSql = `
        SELECT COUNT(DISTINCT u.id) as total
        FROM users u
        LEFT JOIN user_activity ua ON u.id = ua.user_id
        LEFT JOIN user_languages ul ON u.id = ul.user_id
        LEFT JOIN languages l ON ul.language_id = l.id ${languageFilter}
        LEFT JOIN user_interests ui ON u.id = ui.user_id
        LEFT JOIN interest i ON ui.interest_id = i.id ${interestFilter}
        ${userFollowersJoin}
        ${blockedUsersJoin}
        WHERE 1=1 ${filters}
      `;

      const [result, countResult] = await Promise.all([
        dbQuery.queryRunner(baseSql, params),
        dbQuery.queryRunner(countSql, countParams)
      ]);

      const totalRecords = countResult && countResult.length > 0 ? countResult[0].total : 0;

      if (result && result.length > 0) {
        const userIds = result.map((row) => row.id);
        const socialLinksSql = `
          SELECT user_id, platform, link
          FROM user_social_links
          WHERE user_id IN (${userIds.map(() => "?").join(",")})
        `;
        const socialLinksResult = await dbQuery.queryRunner(socialLinksSql, userIds);

        const socialLinksMap = {};
        socialLinksResult.forEach(({ user_id, platform, link }) => {
          if (!socialLinksMap[user_id]) socialLinksMap[user_id] = [];
          socialLinksMap[user_id].push({ platform, link });
        });

        const currentTime = moment().utcOffset(330);
        const parsedResult = result.map((row) => {
          // Check if user was active within the last 5 minutes
          // Use last_active if available, otherwise fall back to updated_date
          const lastActivityTime = row.last_active || row.updated_date;
          const isActive = lastActivityTime
            ? moment(lastActivityTime).utcOffset(330).isAfter(currentTime.clone().subtract(5, "minutes"))
            : false;
          const lastSeen = row.last_active || row.updated_date
            ? (() => {
                const lastTime = row.last_active ? moment(row.last_active) : moment(row.updated_date);
                const diffSeconds = currentTime.diff(lastTime, "seconds");
                const diffMinutes = currentTime.diff(lastTime, "minutes");
                const diffHours = currentTime.diff(lastTime, "hours");
                const diffDays = currentTime.diff(lastTime, "days");
                return diffSeconds < 60
                  ? `${diffSeconds}s ago`
                  : diffMinutes < 60
                  ? `${diffMinutes} minute${diffMinutes === 1 ? "" : "s"} ago`
                  : diffHours < 24
                  ? `${diffHours} hr${diffHours === 1 ? "" : "s"} ago`
                  : `${diffDays} day${diffDays === 1 ? "" : "s"} ago`;
              })()
            : "30 days ago"; // Default to "30 days ago" if both are null

          return {
            ...row,
            languages: row.languages ? JSON.parse(row.languages) : [],
            interests: row.interests ? JSON.parse(row.interests) : [],
            social_links: socialLinksMap[row.id] || [],
            is_blocked: row.is_blocked === 1,
            is_active: isActive,
            last_seen: lastSeen,
            is_available: row.is_available === 1,
            dnd: row.dnd === 1,
            online_status: isActive,
          };
        });

        return {
          status: true,
          message: "Fetched users successfully.",
          data: parsedResult,
          pagination: id > 0
            ? { page: 0, limit: 0, totalRecords: 1 }
            : { page, limit, totalRecords },
        };
      }

      return {
        status: false,
        message: "No users found.",
        data: [],
        pagination: { page, limit, totalRecords },
      };
    } catch (err) {
      console.error("Error in getAllUsers:", err.stack);
      return {
        status: false,
        message: err.message || "Internal server error.",
        data: [],
      };
    }
  },

  getAllUsersWithoutLanguage : async ({
    id = 0,
    page = 1,
    limit = 20,
    interest = [],
    user_id,
    search_by_name,
    loggined_user_id,
    sortBy = {},
  }) => {
    try {
      page = Math.max(1, page);
      const offset = (page - 1) * limit;
      let filters = "";
      const params = [];

      if (search_by_name) {
        filters += ` AND u.name LIKE ?`;
        params.push(`%${search_by_name}%`);
      }
      if (id > 0) {
        filters += ` AND u.id = ?`;
        params.push(id);
      }
      if (interest.length > 0) {
        filters += ` AND i.id IN (${interest.map(() => "?").join(",")})`;
        params.push(...interest);
      }
      if (id === 0 && loggined_user_id) {
        filters += ` AND u.id != ?`;
        params.push(loggined_user_id);
      }

      // Sort by last_active (or updated_date) in descending order
      const orderBy = `ORDER BY COALESCE(ua.last_active, u.updated_date) DESC, u.id DESC`;

      const baseSql = `
        SELECT 
          u.id, u.name, u.emailId, u.is_available, u.dnd, u.updated_date,
          ua.last_active,
          CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('{',
            '"id":', l.id, ',',
            '"name":"', l.name, '",',
            '"isPrimary":', IF(ul.is_primary = 1, 'true', 'false'),
          '}')), ']') AS languages,
          CONCAT('[', GROUP_CONCAT(DISTINCT CONCAT('{',
            '"id":', i.id, ',',
            '"name":"', i.name, '",',
            '"isPrimary":', IF(ui.is_primary = 1, 'true', 'false'),
          '}')), ']') AS interests,
          COUNT(DISTINCT p.id) AS product_count,
          COUNT(DISTINCT ps.id) AS post_count,
          CASE 
            WHEN uf.followerId IS NOT NULL THEN TRUE 
            ELSE FALSE 
          END AS is_following,
          (SELECT COUNT(*) FROM user_followers WHERE followerId = u.id) AS following_count,
          (SELECT COUNT(*) FROM user_followers WHERE followingId = u.id) AS followers_count,
          CASE 
            WHEN bu.is_blocked = 1 THEN TRUE 
            ELSE FALSE 
          END AS is_blocked
        FROM users u
        LEFT JOIN user_activity ua ON u.id = ua.user_id
        LEFT JOIN user_languages ul ON u.id = ul.user_id
        LEFT JOIN languages l ON ul.language_id = l.id
        LEFT JOIN user_interests ui ON u.id = ui.user_id
        LEFT JOIN interest i ON ui.interest_id = i.id
        LEFT JOIN product p ON u.id = p.created_by
        LEFT JOIN posts ps ON u.id = ps.user_id
        LEFT JOIN user_followers uf 
          ON uf.followingId = u.id AND uf.followerId = ?
        LEFT JOIN blocked_users bu ON (bu.user_id = ? AND bu.blocked_id = u.id) 
        WHERE 1=1 ${filters}
        GROUP BY u.id
        ${orderBy}
        LIMIT ? OFFSET ?
      `;
      params.push(loggined_user_id, loggined_user_id, limit, offset);

      // Execute the main query and count query in parallel
      const countParams = [...params.slice(0, -2)]; // Remove limit and offset from params for count query

      // Create count query with same filters but without GROUP BY, ORDER BY, LIMIT, OFFSET
      const countSql = `
        SELECT COUNT(DISTINCT u.id) as total
        FROM users u
        LEFT JOIN user_activity ua ON u.id = ua.user_id
        LEFT JOIN user_languages ul ON u.id = ul.user_id
        LEFT JOIN languages l ON ul.language_id = l.id
        LEFT JOIN user_interests ui ON u.id = ui.user_id
        LEFT JOIN interest i ON ui.interest_id = i.id
        LEFT JOIN user_followers uf
          ON uf.followingId = u.id AND uf.followerId = ?
        LEFT JOIN blocked_users bu ON (bu.user_id = ? AND bu.blocked_id = u.id)
        WHERE 1=1 ${filters}
      `;

      const [result, countResult] = await Promise.all([
        dbQuery.queryRunner(baseSql, params),
        dbQuery.queryRunner(countSql, countParams)
      ]);

      const totalRecords = countResult && countResult.length > 0 ? countResult[0].total : 0;

      if (result && result.length > 0) {
        const userIds = result.map((row) => row.id);
        const socialLinksSql = `
          SELECT user_id, platform, link 
          FROM user_social_links 
          WHERE user_id IN (${userIds.map(() => "?").join(",")})
        `;
        const socialLinksResult = await dbQuery.queryRunner(socialLinksSql, userIds);

        const socialLinksMap = {};
        socialLinksResult.forEach(({ user_id, platform, link }) => {
          if (!socialLinksMap[user_id]) socialLinksMap[user_id] = [];
          socialLinksMap[user_id].push({ platform, link });
        });

        const currentTime = moment().utcOffset(330); // IST (+5:30)
        const parsedResult = result.map((row) => {
          // Check if user was active within the last 5 minutes
          // Use last_active if available, otherwise fall back to updated_date
          const lastActivityTime = row.last_active || row.updated_date;
          const isActive = lastActivityTime
            ? moment(lastActivityTime).utcOffset(330).isAfter(currentTime.clone().subtract(5, "minutes"))
            : false;

          const lastSeen = (row.last_active || row.updated_date)
            ? (() => {
                // Normalize timestamps to IST
                const lastTime = row.last_active
                  ? moment(row.last_active).utcOffset(330)
                  : moment(row.updated_date).utcOffset(330);
                // Log future timestamps for debugging
                if (lastTime.isAfter(currentTime)) {
                  console.warn(`Future timestamp for user ${row.id}: last_active=${row.last_active}, updated_date=${row.updated_date}`);
                  return "just now";
                }
                const diffSeconds = Math.abs(currentTime.diff(lastTime, "seconds"));
                const diffMinutes = Math.abs(currentTime.diff(lastTime, "minutes"));
                const diffHours = Math.abs(currentTime.diff(lastTime, "hours"));
                const diffDays = Math.abs(currentTime.diff(lastTime, "days"));
                if (diffSeconds < 60) {
                  return `${diffSeconds}s ago`;
                } else if (diffMinutes < 60) {
                  return `${diffMinutes} minute${diffMinutes === 1 ? "" : "s"} ago`;
                } else if (diffHours < 24) {
                  return `${diffHours} hr${diffHours === 1 ? "" : "s"} ago`;
                } else {
                  return `${diffDays} day${diffDays === 1 ? "" : "s"} ago`;
                }
              })()
            : "30 days ago";

          return {
            ...row,
            languages: row.languages ? JSON.parse(row.languages) : [],
            interests: row.interests ? JSON.parse(row.interests) : [],
            social_links: socialLinksMap[row.id] || [],
            is_blocked: row.is_blocked === 1,
            is_active: isActive,
            last_seen: lastSeen,
            is_available: row.is_available === 1,
            dnd: row.dnd === 1,
            online_status: isActive,
          };
        });

        return {
          status: true,
          message: "Fetched all users successfully.",
          data: parsedResult,
          pagination: id > 0
            ? { page: 0, limit: 0, totalRecords: 1 }
            : { page, limit, totalRecords },
        };
      }

      return {
        status: false,
        message: "No users found.",
        data: [],
        pagination: { page, limit, totalRecords },
      };
    } catch (err) {
      console.error("Error in getAllUsersWithoutLanguage:", err.message);
      return {
        status: false,
        message: err.message || "Internal server error.",
        data: [],
      };
    }
  },
  /**
   *
   * @returns appID and appSignId,currently we are not handling from database
   *
   */
  getZegoCloudDetails: () => {
    return new Promise(async (resolve, reject) => {
      try {
        const getAppId = process.env.appid;
        const getAppSign = process.env.appsignin;
        resolve({
          status: 200,
          message: "successfull",
          getAppId: getAppId,
          getAppSign: getAppSign,
        });
      } catch (error) {
        reject({
          status: 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      }
    });
  },
  getRazorpayDetails: () => {
    return new Promise(async (resolve, reject) => {
      try {
        const { RAZOR_PAY_KEY_ID, RAZOR_PAY_KEY_SECRET } = process.env;
        resolve({
          status: 200,
          message: "successfull",
          // phonepe_url: PHONEPE_BASE_TEST_URL,
          api_key: RAZOR_PAY_KEY_ID,
          api_secret: RAZOR_PAY_KEY_SECRET,
        });
      } catch (error) {
        reject({
          status: 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      }
    });
  },
  
  getRazorpayDetailsTest: () => {
    return new Promise(async (resolve, reject) => {
      try {
        const { RAZOR_PAY_TEST_KEY_ID, RAZOR_PAY_TEST_KEY_SECRET } = process.env;
        resolve({
          status: 200,
          message: "successfull",
          // phonepe_url: PHONEPE_BASE_TEST_URL,
          api_key: RAZOR_PAY_TEST_KEY_ID,
          api_secret: RAZOR_PAY_TEST_KEY_SECRET,
        });
      } catch (error) {
        reject({
          status: 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      }
    });
  },

  blockUser: (user_id, block_user_id) => {
    return new Promise(async (resolve, reject) => {
      if (user_id === block_user_id) {
        return resolve({ status: false, message: "you cannot block yourself" });
      }
      try {
        const start_time = moment()
          .add(5, "hours")
          .add(30, "minutes")
          .format("YYYY-MM-DD HH:mm:ss");
        // Validate user existence
        const userCheckQuery = `SELECT id FROM users WHERE id=${user_id}`;
        const blockUserExistsQuery = `SELECT id FROM users WHERE id=${block_user_id}`;

        const userData = await dbQuery.queryRunner(userCheckQuery);
        const blockUserData = await dbQuery.queryRunner(blockUserExistsQuery);

        if (userData.length == 0 || blockUserData.length == 0) {
          return { error: "Invalid user, please check it." };
        }

        // Check if user is already blocked
        const checkBlockedUserQuery = `SELECT is_blocked FROM blocked_users WHERE user_id=${user_id} AND blocked_id=${block_user_id}`;
        const blockedUser = await dbQuery.queryRunner(checkBlockedUserQuery);

        console.log("blockedUser", blockedUser);

        if (blockedUser.length !== 0) {
          console.log("iff");
          // If user is already blocked, unblock them
          const newBlockStatus = blockedUser[0].is_blocked ? 0 : 1;
          await dbQuery.queryRunner(
            `UPDATE blocked_users 
         SET is_blocked = ${newBlockStatus}, updated_at = '${start_time}' 
         WHERE user_id = ${user_id} AND blocked_id = ${block_user_id}`
          );

          return resolve({
            status: true,
            message: newBlockStatus
              ? "User blocked successfully"
              : "User unblocked successfully",
            is_blocked: !!newBlockStatus,
          });
        } else {
          console.log("in else");
          // If user is not in the table, insert a new blocked record
          await dbQuery.queryRunner(
            `INSERT INTO blocked_users(user_id, blocked_id, is_blocked, created_at, updated_at) 
         VALUES (${user_id}, ${block_user_id}, 1, '${start_time}', '${start_time}')`
          );
          return resolve({
            status: true,
            message: "User blocked successfully",
            is_blocked: true,
          });
        }
      } catch (error) {
        console.error(error);
        return reject({ error: "Something went wrong" });
      }
    });
  },

  // goin to remove
  unBlockUser: (user_id, block_user_id) => {
    return new Promise(async (resolve, reject) => {
      try {
        // Get current time and add 5 hours 30 minute
        const updatedTime = moment()
          .utcOffset(330)
          .format("YYYY-MM-DD HH:mm:ss");
        // check user exists
        const userCheckQuery = `SELECT * FROM users WHERE id=${user_id}`;
        const blockUserExistsQuery = `SELECT * FROM users WHERE id=${block_user_id}`;

        const userData = await dbQuery.queryRunner(userCheckQuery);
        const blockUserData = await dbQuery.queryRunner(blockUserExistsQuery);
        if (userData.length == 0 || blockUserData.length == 0) {
          return resolve({ error: "invalid user, please check it" });
        }
        const checkBlockedUserExistsQuery = `SELECT is_blocked FROM blocked_users WHERE user_id=${user_id} AND blocked_id=${block_user_id}`;
        const checkBlockedUserExistsData = await dbQuery.queryRunner(
          checkBlockedUserExistsQuery
        );

        if (checkBlockedUserExistsData.length > 0) {
          if (checkBlockedUserExistsData[0].is_blocked == 0) {
            return resolve({ error: "user already unblocked" });
          }
        }

        await dbQuery.queryRunner(
          `UPDATE blocked_users SET is_blocked=false,updated_at='${updatedTime}' WHERE user_id = ${user_id} AND blocked_id = ${block_user_id}`
        );

        return resolve({
          status: true,
          is_blocked: false,
          message: "User unblocked successfully",
        });
      } catch (error) {
        return reject({ error: "something went wrong" });
      }
    });
  },
  blockedList: (user_id) => {
    return new Promise(async (resolve, reject) => {
      try {
        const result = await dbQuery.queryRunner(
          `
          SELECT 
              u.id, 
              u.name, 
              u.emailId, 
              u.mobile_number, 
              u.profile_image,
              u.username, 
              COALESCE(bu.is_blocked, FALSE) AS is_blocked
          FROM blocked_users bu
          JOIN users u ON u.id = bu.blocked_id
          WHERE bu.user_id = ${user_id};
          `
        );

        return resolve(result);
      } catch (error) {
        return reject({ status: false, message: "something went wrong" });
      }
    });
  },
  saveCallDetails: (caller_user_id, receiver_user_id) => {
    return new Promise(async (resolve, reject) => {
      try {
        const query = `
            INSERT INTO user_calls (caller_user_id, receiver_user_id) 
            VALUES (${caller_user_id}, ${receiver_user_id})`;
        await dbQuery.queryRunner(query);
        return resolve({ message: "Call details saved successfully" });
      } catch (error) {
        console.log(error);
        return reject("Error saving call details");
      }
    });
  },
  getRecentCalls: (user_id, limit) => {
    return new Promise(async (resolve, reject) => {
      try {
        // Query to fetch recent calls based on user_id and limit
        const query = `
          SELECT 
            uc.call_id,
            DATE_FORMAT(uc.created_at, '%Y-%m-%d %H:%i:%s') AS created_at,
            uc.caller_user_id, 
            uc.receiver_user_id,
            u2.profile_image AS receiver_profile_image,
            u1.name AS caller_name,
            u2.name AS receiver_name
          FROM 
            user_calls AS uc
          LEFT JOIN 
            users AS u1 ON uc.caller_user_id = u1.id
          LEFT JOIN 
            users AS u2 ON uc.receiver_user_id = u2.id
          WHERE 
            uc.caller_user_id = ${user_id} OR uc.receiver_user_id = ${user_id}
          ORDER BY 
            uc.created_at DESC
          LIMIT ${limit};
        `;

        // Execute the query and return the results
        const rows = await dbQuery.queryRunner(query);
        return resolve(rows); // Returns the list of recent calls
      } catch (error) {
        console.log(error);
        return reject("Error fetching recent calls");
      }
    });
  },

  /**
   * Process referral logic using raw SQL queries.
   * @param {String} referrerId - ID of the referrer.
   * @param {String} referredUserId - ID of the referred user.
   * @returns {Promise<Object>} Result of the referral process.
   */
  processReferral: async (referredUserId, referredCode) => {
    return new Promise(async (resolve, reject) => {
      try {
        const referredUserData = await dbQuery.queryRunner(
          `SELECT * FROM users WHERE id = ${referredUserId}`
        );

        if (referredUserData.length > 0) {
          return resolve({
            success: false,
            message: "Referral skipped, user already installed before.",
          });
        }

        if (referredCode) {
          const coupon = await dbQuery.queryRunner(
            `SELECT * FROM users WHERE referal_code = '${referredCode}' AND id=${referrerId}`
          );

          if (coupon.length == 0 || !coupon[0]) {
            return resolve({
              success: false,
              message:
                "Invalid or expired coupon code or generated by referral code",
            });
          }
        }

        // Update the referred user's installation status
        await dbQuery.queryRunner(
          `UPDATE users SET is_first_time = false WHERE id = '${referredUserId}'`
        );

        // Update the referred_count and referal_earnings for referrer
        await dbQuery.queryRunner(
          `UPDATE users 
        SET referred_count = referred_count + 1
        WHERE id = ${referrerId}`
        );

        // Update wallets for referrer and referred user
        await WalletService.insertTransaction("refer", referrerId, 5, null);

        return resolve({
          status: true,
          message: "Referral rewards credited successfully.",
        });
      } catch (error) {
        return reject({
          status: false,
          message: "Error processing referral: " + error.message,
        });
      }
    });
  },

  /**
   * Apply coupon for a user using raw SQL queries.
   * @param {String} userId - ID of the user.
   * @param {String} couponCode - Coupon code to apply.
   * @returns {Promise<Object>} Result of coupon application.
   */

  checkCouponEligibility: async (userId, couponCode, plan_id, couponAmount) => {
    try {
      console.log("Checking coupon eligibility for:", couponCode);

      if (!userId || !couponCode) {
        throw new Error("Missing required fields: user_id or coupon_code.");
      }

      // Step 1: Validate User
      const userCheckQuery = `SELECT id FROM users WHERE id = ${userId}`;
      const userResult = await dbQuery.queryRunner(userCheckQuery);
      if (userResult.length === 0) {
        return { is_applicable: false, message: "Invalid user." };
      }

      // Step 2: Validate Coupon
      const couponQuery = `SELECT * FROM coupons WHERE code = '${couponCode}' AND is_active = 1`;
      const couponResult = await dbQuery.queryRunner(couponQuery);
      console.log("couponResult...", couponResult, "couponQuery", couponQuery);
      if (couponResult.length === 0) {
        return { is_applicable: false, message: "Invalid or inactive coupon." };
      }

      const coupon = couponResult[0];

      // Step 3: Validate Plan ID (if provided)
      if (plan_id) {
        const planCheckQuery = `SELECT id FROM subscription_plans WHERE id = ${plan_id}`;
        const planResult = await dbQuery.queryRunner(planCheckQuery);
        if (planResult.length === 0) {
          return { is_applicable: false, message: "Invalid plan ID." };
        }
      }

      // // Step 4: Check Expiry Date
      // if (coupon.expiry_date && new Date(coupon.expiry_date) < new Date()) {
      //   return { is_applicable: false, message: "Coupon has expired." };
      // }

      // // Step 5: Check Max Uses
      // if (coupon.max_uses !== null && coupon.used_count >= coupon.max_uses) {
      //   return { is_applicable: false, message: "Coupon usage limit reached." };
      // }

      // Step 6: Determine Coupon Type (Normal or Referral)
      let isReferral = coupon.owner_user_id !== null;

      if (coupon.owner_user_id == userId) {
        return {
          is_applicable: false,
          message: "You can't use your own referral coupon",
        };
      }
      if (isReferral) {
        // Parse coupon description safely
        let getCouponDetails;
        try {
          getCouponDetails = JSON.parse(coupon.description);
        } catch (error) {
          console.error("Error parsing coupon description:", error);
          return {
            is_applicable: false,
            message: "Invalid coupon description",
          };
        }

        // Find matching entry for couponAmount and plan_id dynamically
        let matchedCoupon = null;

        if (Array.isArray(getCouponDetails)) {
          matchedCoupon = getCouponDetails.find(
            (item) =>
              item.actual_price === couponAmount && item.plan_id === plan_id
          );
        } else if (
          typeof getCouponDetails === "object" &&
          getCouponDetails.actual_price === couponAmount &&
          getCouponDetails.plan_id === plan_id
        ) {
          matchedCoupon = getCouponDetails;
        }

        if (!matchedCoupon) {
          return {
            is_applicable: false,
            message: "This coupon is not applicable for this plan",
          };
        }

        // Step 6.2: Referral Coupon Validation - Ensure referred user has not used it before
        const referrerCheckQuery = `
    SELECT * FROM user_coupon
    WHERE referred_user_id = ${userId} 
      AND code_id = ${coupon.coupon_id}
  `;
        const referrerCheckResult = await dbQuery.queryRunner(
          referrerCheckQuery
        );

        if (referrerCheckResult.length > 0) {
          return {
            is_applicable: false,
            message: "Coupon already used.",
          };
        }

        // Step 6.3: Fetch referral discount from the plan
        let discountValue = coupon.discount_value;
        let discountType = coupon.discount_type;
        let description = coupon.description;

        try {
          const planDiscountQuery = `SELECT discount_referral FROM subscription_plans WHERE id = ${plan_id}`;
          const planDiscountResult = await dbQuery.queryRunner(
            planDiscountQuery
          );

          if (planDiscountResult.length > 0) {
            discountValue = planDiscountResult[0].discount_referral; // Referral bonus from plan
            discountType = "amount"; // Fixed referral reward
            description = `Referral bonus for plan ${plan_id}`;
          }
        } catch (error) {
          console.error("Error fetching referral discount:", error);
        }

        return {
          is_applicable: true,
          message: "Coupon is applicable",
          couponCode: coupon.code,
          discount: discountValue,
          discount_type: discountType,
          description,
          discount_value: discountValue,
        };
      } else {
        if (couponAmount != coupon.actual_price) {
          return {
            is_applicable: false,
            message: "This coupon is not applicable for this plann",
          };
        }

        // Step 7: Normal Coupon Validation - Ensure user hasn't used it before
        const normalUsageCheckQuery = `
        SELECT * FROM user_coupon 
        WHERE applied_by_user = ${userId} 
          AND code_id = ${coupon.coupon_id}
      `;
        const normalUsageResult = await dbQuery.queryRunner(
          normalUsageCheckQuery
        );

        if (normalUsageResult.length > 0) {
          return {
            is_applicable: false,
            message: "Coupon already used.",
          };
        }

        return {
          is_applicable: true,
          message: "Coupon is applicable",
          couponCode,
          discount: coupon.discount_value,
          discount_type: coupon.discount_type,
          description: coupon.description,
          discount_value: coupon.discount_value,
        };
      }
    } catch (error) {
      return {
        is_applicable: false,
        message: "Error checking coupon: " + error.message,
      };
    }
  },

  // services/userService.js
  upgradePremium : async ({
    payment_status,
    user_id,
    plan_id,
    order_id,
    payment_id,
    coupon_code,
  }) => {
    try {
      if (payment_status === "success") {
        // Validate user exists
        const userQuery = `SELECT id, premium_plan_id FROM users WHERE id = '${user_id}' LIMIT 1`;
        const user = await dbQuery.queryRunner(userQuery);
        if (!user.length) {
          return { status: false, statusCode: 400, message: "Invalid user" };
        }

        // Validate plan exists
        const planQuery = `SELECT duration_in_months FROM subscription_plans WHERE id = '${plan_id}' LIMIT 1`;
        const plan = await dbQuery.queryRunner(planQuery);
        if (!plan.length) {
          return { status: false, statusCode: 400, message: "Invalid plan" };
        }

        // Validate transaction
        const transactionQuery = `SELECT status, payment_id, order_id FROM transactions WHERE payment_id = '${payment_id}' AND order_id = '${order_id}'`;
        const transaction = await dbQuery.queryRunner(transactionQuery);

        if (
          transaction.length === 0 ||
          transaction[0].status !== "success" ||
          transaction[0].payment_id !== payment_id ||
          transaction[0].order_id !== order_id
        ) {
          return {
            status: false,
            statusCode: 400,
            message: "Payment verification failed. Try again later.",
          };
        }

        // Check for active premium plans
        const activePlanQuery = `
          SELECT id, end_time
          FROM user_premium_plans
          WHERE user_id = '${user_id}' AND status = 'active' AND is_active = 1
          LIMIT 1
        `;
        const activePlan = await dbQuery.queryRunner(activePlanQuery);

        const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

        if (activePlan.length) {
          // Queue the new plan
          const start_time = moment(activePlan[0].end_time)
            .add(1, "second")
            .format("YYYY-MM-DD HH:mm:ss");
          const end_time = moment(start_time)
            .add(plan[0].duration_in_months, "months")
            .format("YYYY-MM-DD HH:mm:ss");

          const insertPlanQuery = `
            INSERT INTO user_premium_plans (user_id, plan_id, start_time, end_time, status, is_active, created_at, order_id, payment_id)
            VALUES ('${user_id}', '${plan_id}', '${start_time}', '${end_time}', 'queued', 0, '${currentTime}', '${order_id}', '${payment_id}')
          `;
          await dbQuery.queryRunner(insertPlanQuery);

          return {
            status: true,
            statusCode: 200,
            message: "Premium plan queued successfully",
            is_premium: true,
          };
        } else {
          // No active plan, activate immediately
          const start_time = currentTime;
          const end_time = moment()
            .add(plan[0].duration_in_months, "months")
            .utcOffset(330)
            .format("YYYY-MM-DD HH:mm:ss");

          const insertPlanQuery = `
            INSERT INTO user_premium_plans (user_id, plan_id, start_time, end_time, status, is_active, created_at, order_id, payment_id)
            VALUES ('${user_id}', '${plan_id}', '${start_time}', '${end_time}', 'active', 1, '${currentTime}', '${order_id}', '${payment_id}')
          `;
          await dbQuery.queryRunner(insertPlanQuery);

          // Update users table
          const updateUserQuery = `UPDATE users SET premium_plan_id = '${plan_id}' WHERE id = '${user_id}'`;
          await dbQuery.queryRunner(updateUserQuery);

          return {
            status: true,
            statusCode: 200,
            message: "User upgraded to premium successfully",
            is_premium: true,
          };
        }
      } else {
        return {
          status: false,
          statusCode: 400,
          message: "Payment was not successful",
          is_premium: false,
        };
      }
    } catch (error) {
      console.error("Error upgrading premium:", error);
      return {
        status: false,
        statusCode: 400,
        message: "Internal Server Error",
        is_premium: false,
      };
    }
  },


  checkPremiumStatus: async (userId) => {
    try {
      // Fetch latest premium plan for user
      const query = `
      SELECT 
        id,
        plan_id, 
        end_time 
      FROM user_premium_plans 
      WHERE 
        user_id = ${userId} 
      ORDER BY end_time DESC 
      LIMIT 1
    `;

      const premiumPlan = await dbQuery.queryRunner(query);

      if (premiumPlan.length === 0) {
        return {
          error: "No active premium plan",
        };
      }

      // If `end_time` is null, return expired
      if (premiumPlan[0].end_time == null) {
        return {
          status: false,
          user_id: userId,
          is_premium_expired: true,
          message: "Premium plan has expired",
        };
      }

      // Get current time and add 5 hours 30 minutes
      const currentTime = moment()
        .add(5, "hours")
        .add(30, "minutes")
        .format("YYYY-MM-DD HH:mm:ss");
      const isExpired = moment(premiumPlan[0].end_time).isBefore(currentTime);

      if (isExpired) {
        // if user premium expired then update the table accordingly
        const updateQuery = `
        UPDATE user_premium_plans 
        SET is_active = false, end_time = NULL 
        WHERE id = ${premiumPlan[0].id}
      `;
        await dbQuery.queryRunner(updateQuery);
      }

      return {
        status: 200,
        user_id: userId,
        plan_id: premiumPlan[0].plan_id,
        end_time: moment(premiumPlan[0].end_time)
          .add(5, "hours")
          .add(30, "minutes")
          .format("YYYY-MM-DD HH:mm:ss"),
        is_premium_expired: isExpired,
        message: isExpired
          ? "Premium plan has expired"
          : "Premium plan is active",
      };
    } catch (error) {
      console.error("Service Error:", error);
      return { error: "Failed to check premium status" };
    }
  },

  fetchCoupons: async (data) => {
    try {
      let query = `SELECT * FROM coupons WHERE is_active = true AND owner_user_id is NULL`;

      if (data.user_id > 0) {
        query += ` AND owner_user_id = ${data.user_id}`;
      }

      query += ` ORDER BY created_at DESC;`;

      console.log("logg", query);
      const results = await dbQuery.queryRunner(query);

      return results.map((coupon) => {
        return {
          coupon_id: coupon.coupon_id,
          code: coupon.code,
          expiry_date: coupon.expiry_date,
          is_active: coupon.is_active,
          description: coupon.description,
        };
      });
    } catch (error) {
      console.error("Error fetching coupons:", error);
      throw new Error("Error fetching coupons");
    }
  },

  // Handle the call
  handleCallS: async (data) => {
    return new Promise(async (resolve, reject) => {
      try {
        console.log("data", data);
        // Validate inputs
        if (data.callerId === data.receiverId) {
          return reject({
            status: 400,
            message: "You can't call yourself.",
          });
        }

        const formatDateTime = (dateTime) =>
          moment(dateTime).format("YYYY-MM-DD HH:mm:ss");

        // Ensure wallets exist for caller and receiver
        const ensureWalletQuery = (userId) => `
            INSERT INTO user_wallet (user_id, balance)
            SELECT ${userId}, 0
            WHERE NOT EXISTS (SELECT 1 FROM user_wallet WHERE user_id = ${userId});
        `;

        await Promise.all([
          dbQuery.queryRunner(ensureWalletQuery(data.callerId)),
          dbQuery.queryRunner(ensureWalletQuery(data.receiverId)),
        ]);

        // Fetch caller and receiver details
        const getUserDetailsQuery = (userId) => `
            SELECT COALESCE(premium_plan_id, 0) AS premium_plan_id, id AS user_id, uw.balance AS wallet_balance
            FROM users u
            LEFT JOIN user_wallet uw ON u.id = uw.user_id
            WHERE u.id = ${userId};
        `;

        const [[caller], [receiver]] = await Promise.all([
          dbQuery.queryRunner(getUserDetailsQuery(data.callerId)),
          dbQuery.queryRunner(getUserDetailsQuery(data.receiverId)),
        ]);

        if (!caller || !receiver) {
          return reject({
            status: 400,
            message: "Caller or receiver not found.",
          });
        }

        if (!data.callId) {
          // Call initiation logic
          console.log("CALL INITIATED...");

          const callerChargePerMinute = caller.premium_plan_id > 0 ? 2 : 4;

          if (caller.wallet_balance < callerChargePerMinute) {
            return reject({
              status: 400,
              message: `Insufficient balance. Minimum required is ₹${callerChargePerMinute}`,
            });
          }

          const startDateTime = formatDateTime(
            data.startTime || moment().format("YYYY-MM-DD HH:mm:ss")
          );
          const convertedMomentStartTime = moment(startDateTime)
            .add(5, "hours")
            .add(30, "minutes")
            .format("YYYY-MM-DD HH:mm:ss");
          console.log(
            "startTime",
            data.startTime,
            "startDateTime",
            startDateTime,
            "convertedMomentStartTime",
            convertedMomentStartTime
          );

          const provisionalDurationMinutes = Math.floor(
            caller.wallet_balance / callerChargePerMinute
          );

          const endDateTime = formatDateTime(
            moment(startDateTime)
              .add(provisionalDurationMinutes, "minutes")
              .add(5, "hours")
              .add(30, "minutes")
          );

          const maxCallLimitTime = moment(endDateTime).format(
            "YYYY-MM-DD HH:mm:ss"
          );

          const logCallQuery = `
                INSERT INTO user_calls (caller_user_id, receiver_user_id, start_time, max_call_limit_time)
                VALUES (${data.callerId}, ${data.receiverId}, '${convertedMomentStartTime}', '${maxCallLimitTime}');
            `;

          const lastCallData = await dbQuery.queryRunner(logCallQuery);

          return resolve({
            status: true,
            message: "Call initiated successfully.",
            data: {
              callId: lastCallData.insertId,
              startTime: convertedMomentStartTime,
              maxCallLimitTime: maxCallLimitTime,
            },
          });
        } else {
          // Call completion logic
          console.log("CALL COMPLETED...");

          const getCallDetailsQuery = `SELECT * FROM user_calls WHERE call_id = ${data.callId};`;
          const [callDetails] = await dbQuery.queryRunner(getCallDetailsQuery);

          if (!callDetails) {
            return reject({
              status: 400,
              message: "Invalid call ID.",
            });
          }

          const startMinutes = moment(
            callDetails.start_time,
            "YYYY-MM-DD HH:mm:ss"
          ).unix();

          const endMinutes = moment(data.endTime, "YYYY-MM-DD HH:mm:ss").unix();

          if (endMinutes <= startMinutes) {
            return reject({
              status: 400,
              message: "endTime must be later than startTime.",
            });
          }

          const duration = Math.ceil((endMinutes - startMinutes) / 60);
          console.log("duration", duration);

          const callerChargePerMinute = caller.premium_plan_id > 0 ? 2 : 4;
          const receiverCreditPerMinute = receiver.premium_plan_id > 0 ? 2 : 1;

          const totalCharge = duration * callerChargePerMinute;
          const totalCredit = duration * receiverCreditPerMinute;
          if (caller.wallet_balance < totalCharge) {
            return reject({
              status: 400,
              message: "Insufficient balance to complete the call.",
            });
          }

          // Update wallets for caller and receiver
          const updateCallerWalletQuery = `UPDATE user_wallet SET balance = balance - ${totalCharge} WHERE user_id = ${data.callerId};`;
          const updateReceiverWalletQuery = `UPDATE user_wallet SET balance = balance + ${totalCredit} WHERE user_id = ${data.receiverId};`;

          await Promise.all([
            dbQuery.queryRunner(updateCallerWalletQuery),
            dbQuery.queryRunner(updateReceiverWalletQuery),
          ]);

          // Update call details
          const updateCallQuery = `
                UPDATE user_calls 
                SET 
                  end_time = '${data.endTime}'
                WHERE call_id = ${data.callId};
            `;

          await dbQuery.queryRunner(updateCallQuery);

          return resolve({
            status: true,
            message: "Call processed successfully.",
            data: {
              callId: data.callId,
              duration,
              caller: {
                id: data.callerId,
                totalCharge,
                remainingBalance: caller.wallet_balance - totalCharge,
              },
              receiver: {
                id: data.receiverId,
                totalCredit,
                updatedBalance: receiver.wallet_balance + totalCredit,
              },
            },
          });
        }
      } catch (error) {
        console.error("Error handling call:", error.message);
        return reject({
          status: 400,
          message: "Error handling call:" + error.message,
        });
      }
    });
  },
  // Service for getting referral details
  getEarnings: async (user_id) => {
    try {
      const getReferalCodeQuery = `
      SELECT referal_code
      FROM users
      WHERE id = ${user_id}
    `;
      const getReferalCodeData = await dbQuery.queryRunner(getReferalCodeQuery);
      console.log("getReferalCodeData.....", getReferalCodeData);

      const generateCouponCode = await generateUserCoupon(user_id);

      // Step 1: Count total referrals (number of referred users)
      const totalReferralsQuery = `
      SELECT COUNT(*) AS total_referrals
      FROM user_referrals
      WHERE referrer_user_id = ${user_id}
    `;
      const totalReferralsResult = await dbQuery.queryRunner(
        totalReferralsQuery
      );
      const totalReferrals = totalReferralsResult[0]?.total_referrals || 0;

      // Step 2: Calculate total referral earnings (sum of deposits)
      const totalReferralEarningsQuery = `
      SELECT SUM(amount) AS total_referrals_earnings
      FROM user_referral_wallet_transactions
      WHERE user_id = ${user_id} AND transaction_type = 'deposit'
    `;
      console.log("totalReferralEarningsQuery", totalReferralEarningsQuery);
      const totalReferralEarningsResult = await dbQuery.queryRunner(
        totalReferralEarningsQuery
      );

      const totalReferralsEarnings =
        totalReferralEarningsResult[0]?.total_referrals_earnings || 0;

      // Step 3: Get the amount earned per referral (each_referral)
      const eachReferralQuery = `
      SELECT amount 
      FROM user_referral_wallet_transactions
      WHERE user_id = ${user_id} AND transaction_type = 'deposit'
      LIMIT 1
    `;
      const eachReferralResult = await dbQuery.queryRunner(eachReferralQuery);
      const eachReferral =
        eachReferralResult.length > 0 ? eachReferralResult[0].amount : 3; // Default Rs. 5 per referral

      // Step 4: Count total premiums (users who applied the referral coupon)
      const totalPremiumsQuery = `
      SELECT COUNT(*) AS total_premiums
      FROM user_coupon
      WHERE referrer_user_id = ${user_id}
    `;
      const totalPremiumsResult = await dbQuery.queryRunner(totalPremiumsQuery);
      const totalPremiums = totalPremiumsResult[0]?.total_premiums || 0;

      // Step 5: Calculate total premium earnings (sum of deposits)
      const totalPremiumEarningsQuery = `
      SELECT SUM(amount) AS total_premium_earnings
      FROM user_coupon_wallet_transactions
      WHERE user_id = ${user_id} AND transaction_type = 'deposit'
    `;
      const totalPremiumEarningsResult = await dbQuery.queryRunner(
        totalPremiumEarningsQuery
      );
      const totalPremiumEarnings =
        totalPremiumEarningsResult[0]?.total_premium_earnings || 0;

      // Step 6: Fetch referrer's coupon code
      const couponCodeQuery = `
      SELECT code 
      FROM coupons
      WHERE 
        owner_user_id = ${user_id}
        AND is_active=1
      ORDER BY coupon_id DESC
      LIMIT 1
    `;
      const couponCodeResult = await dbQuery.queryRunner(couponCodeQuery);
      const couponCode =
        couponCodeResult.length > 0 ? couponCodeResult[0].code : null;

      // Step 7: Get the discount amount per coupon (each_coupon)
      const eachCouponQuery = `
      SELECT discount_value AS each_coupon 
      FROM coupons 
      WHERE owner_user_id = ${user_id}
      LIMIT 1
    `;
      const eachCouponResult = await dbQuery.queryRunner(eachCouponQuery);
      const eachCoupon =
        eachCouponResult.length > 0 ? eachCouponResult[0].each_coupon : 0;

      // Step 8: Calculate total withdrawn from referrals
      const totalReferralWithdrawalsQuery = `
      SELECT SUM(amount) AS total_referral_withdrawals
      FROM user_referral_wallet_transactions
      WHERE user_id = ${user_id} AND transaction_type = 'withdrawal'
    `;
      const totalReferralWithdrawalsResult = await dbQuery.queryRunner(
        totalReferralWithdrawalsQuery
      );
      const totalReferralWithdrawals =
        totalReferralWithdrawalsResult[0]?.total_referral_withdrawals || 0;

      // Step 9: Calculate total withdrawn from coupon earnings
      const totalCouponWithdrawalsQuery = `
      SELECT SUM(amount) AS total_coupon_withdrawals
      FROM user_coupon_wallet_transactions
      WHERE user_id = ${user_id} AND transaction_type = 'withdrawal'
    `;
      const totalCouponWithdrawalsResult = await dbQuery.queryRunner(
        totalCouponWithdrawalsQuery
      );
      const totalCouponWithdrawals =
        totalCouponWithdrawalsResult[0]?.total_coupon_withdrawals || 0;

      // Step 10: Get available balances from respective wallet tables
      const referralBalanceQuery = `
      SELECT balance FROM user_referral_wallet WHERE user_id = ${user_id}
    `;
      const referralBalanceResult = await dbQuery.queryRunner(
        referralBalanceQuery
      );
      const availableReferralBalance = referralBalanceResult[0]?.balance || 0;

      const couponBalanceQuery = `
      SELECT balance FROM user_coupon_wallet WHERE user_id = ${user_id}
    `;
      const couponBalanceResult = await dbQuery.queryRunner(couponBalanceQuery);
      const availableCouponBalance = couponBalanceResult[0]?.balance || 0;

      // Return merged earnings data
      return {
        referral_code: getReferalCodeData[0].referal_code,
        total_referrals: totalReferrals, // Total referred users
        total_referrals_earnings: totalReferralsEarnings, // Total earnings from referrals
        total_referral_withdrawals_amount: totalReferralWithdrawals, // Total withdrawn from referrals
        available_referral_balance: availableReferralBalance, // Remaining referral earnings
        each_referral: 3, // Amount per referral (default Rs. 5)
        total_premiums: totalPremiums, // Total users who applied the referral coupon
        total_premium_earnings: totalPremiumEarnings, // Total earnings from coupon transactions
        total_coupon_withdrawals_amount: totalCouponWithdrawals, // Total withdrawn from coupons
        available_coupon_balance: availableCouponBalance, // Remaining coupon earnings
        coupon_code: couponCode, // Referrer's coupon code
        each_coupon: 30, // Discount amount per coupon used
      };
    } catch (error) {
      throw new Error("Error fetching earnings: " + error.message);
    }
  },
  isUserPremiumOrNot: async (userId) => {
    return new Promise(async (resolve, reject) => {
      try {
        const userPremiumSql = `
          SELECT premium_plan_id FROM users WHERE id=${userId}
        `;
        const getData = await dbQuery.queryRunner(userPremiumSql);
        console.log("getData.....", getData);
        return resolve({
          status: true,
          statusCode: 200,
          message: "user is premium",
          is_premium: getData[0].premium_plan_id > 0 ? true : false,
          premium_plan_id: getData[0].premium_plan_id,
        });
      } catch (error) {
        console.log("error in service", error);
        return reject({
          status: false,
          statusCode: 400,
          message: "error in service",
        });
      }
    });
  },

  processWithdrawal: async (
    user_id,
    amount,
    withdrawal_type,
    bank_details,
    upi_id
  ) => {
    try {
      if (!user_id || !amount || !withdrawal_type) {
        throw {
          status: false,
          statusCode: 400,
          message:
            "Missing required fields: user_id, amount, or withdrawal_type.",
        };
      }

      if (!bank_details && !upi_id) {
        throw {
          status: false,
          statusCode: 400,
          message: "Either bank details or UPI ID must be provided.",
        };
      }

      if (
        !["coupon", "referral", "creator_referral"].includes(withdrawal_type)
      ) {
        throw {
          status: false,
          statusCode: 400,
          message: "Invalid withdrawal type.",
        };
      }

      // ✅ Convert amount to a float & round to 2 decimal places
      amount = parseFloat(amount);
      // if (isNaN(amount) || amount < 1) {
      //   throw {
      //     status: false,
      //     statusCode: 400,
      //     message: "Invalid withdrawal amount. Minimum withdrawal is ₹1.",
      //   };
      // }
      amount = parseFloat(amount.toFixed(2)); // Ensure 2 decimal places

      const userQuery = `SELECT premium_plan_id FROM users WHERE id = ${user_id}`;
      const userResult = await dbQuery.queryRunner(userQuery);
      if (userResult.length === 0) {
        throw {
          status: false,
          statusCode: 400,
          message: "User does not exist.",
        };
      }
      const is_premium = userResult[0].premium_plan_id > 0;

      const walletTable =
        withdrawal_type === "coupon"
          ? "user_coupon_wallet"
          : withdrawal_type === "referral"
          ? "user_referral_wallet"
          : "channel_content_creator_wallet";
      const transactionTable =
        withdrawal_type === "coupon"
          ? "user_coupon_wallet_transactions"
          : withdrawal_type === "referral"
          ? "user_referral_wallet_transactions"
          : "channel_content_creator_transactions";
      const walletColumn =
        withdrawal_type === "coupon"
          ? "coupon_wallet_id"
          : withdrawal_type === "referral"
          ? "referrer_wallet_id"
          : `channel_id`;

      let balanceQuery = `SELECT * FROM ${walletTable} WHERE user_id = ${user_id}`;
      let balanceResult = await dbQuery.queryRunner(balanceQuery);

      if (balanceResult.length === 0) {
        if (!["coupon", "referral"].includes(withdrawal_type)) {
          const createChannelWalletQuery = `INSERT INTO channel_content_creator_wallet (user_id, channel_id, balance) VALUES (${user_id}, (SELECT id FROM channels WHERE createdby=${user_id} ORDER BY id DESC LIMIT 1), 0)`;
          await dbQuery.queryRunner(createChannelWalletQuery);
        }
        const createWalletQuery = `INSERT INTO ${walletTable} (user_id, balance) VALUES (${user_id}, 0)`;
        await dbQuery.queryRunner(createWalletQuery);
        balanceResult = await dbQuery.queryRunner(balanceQuery);
      }

      const currentBalance = parseFloat(balanceResult[0].balance);
      const minBalance = is_premium ? 1000 : 5000;
      if (currentBalance < minBalance) {
        throw {
          status: false,
          statusCode: 400,
          message: `Minimum balance of ₹${minBalance} required for withdrawal.`,
        };
      }

      if (amount > currentBalance) {
        throw {
          status: false,
          statusCode: 400,
          message: "Insufficient balance.",
        };
      }

      let withDrawData;
      if (["coupon", "referral"].includes(withdrawal_type)) {
        const transactionQuery = `
        INSERT INTO ${transactionTable} 
        (user_id, ${walletColumn}, transaction_type, balance, amount, created_at, bank_name, bank_ifsc, bank_account_number, upi_id, paid_status)
        VALUES (${user_id}, ${balanceResult[0].id}, 'withdrawal', 
        (SELECT balance FROM ${walletTable} WHERE user_id=${user_id}), ${amount}, NOW(),
        ${bank_details?.bank_name ? `'${bank_details.bank_name}'` : "NULL"},
        ${bank_details?.bank_ifsc ? `'${bank_details.bank_ifsc}'` : "NULL"},
        ${
          bank_details?.bank_account_number
            ? `'${bank_details.bank_account_number}'`
            : "NULL"
        },
        ${upi_id ? `'${upi_id}'` : "NULL"}, 'completed')
      `;
        console.log("transactionQuery-IF.......", transactionQuery);

        withDrawData = await dbQuery.queryRunner(transactionQuery);
      } else {
        const transactionQuery = `
        INSERT INTO channel_content_creator_transactions (
            user_id, channel_id, wallet_id, transaction_type, amount, paid_status, 
            payment_method, bank_name, bank_account_number, bank_account_holder_name, 
            bank_ifsc_code, upi_id, created_at, updated_at
        ) VALUES (
            ${user_id}, (SELECT id FROM channels WHERE createdby=${user_id}), 
            (SELECT id FROM channel_content_creator_wallet WHERE user_id=${user_id} ORDER BY id DESC LIMIT 1), 
            'withdrawal', ${amount}, 'completed', 
            ${bank_details?.bank_name ? "'bank'" : "'upi'"},
            ${bank_details?.bank_name ? `'${bank_details.bank_name}'` : "NULL"},
            ${
              bank_details?.bank_account_number
                ? `'${bank_details.bank_account_number}'`
                : "NULL"
            },
            (SELECT name FROM users WHERE id=${user_id}), 
            ${bank_details?.bank_ifsc ? `'${bank_details.bank_ifsc}'` : "NULL"},
            ${upi_id ? `'${upi_id}'` : "NULL"},
            NOW(), NOW()
        )
      `;
        console.log("transactionQuery-ELSE.......", transactionQuery);
        withDrawData = await dbQuery.queryRunner(transactionQuery);
      }

      const updateWalletQuery = `UPDATE ${walletTable} SET balance = balance - ${amount} WHERE user_id = ${user_id}`;
      await dbQuery.queryRunner(updateWalletQuery);

      return {
        status: true,
        statusCode: 200,
        withdrawal_id: withDrawData.insertId,
        withdrawal_type,
        message: "Withdrawal successful",
        withdrawn_amount: amount.toFixed(2).toString(), // Ensuring string with 2 decimals
        remaining_balance: (currentBalance - amount).toFixed(2).toString(), // Ensuring string with 2 decimals
      };
    } catch (error) {
      throw {
        status: false,
        statusCode: 400,
        message: "Error processing withdrawal: " + error.message,
      };
    }
  },

  /*
  missedCall: async (callerId, receiverId) => {
    try {
      // Validate if caller and receiver exist
      const callerCheckQuery = `SELECT * FROM users WHERE id=${callerId}`;
      const receiverCheckQuery = `SELECT * FROM users WHERE id=${receiverId}`;
      const callerExists = await dbQuery.queryRunner(callerCheckQuery);
      const receiverExists = await dbQuery.queryRunner(receiverCheckQuery);

      if (callerExists.length == 0) {
        return {
          status: false,
          statusCode: 400,
          message: "Caller does not exist.",
        };
      }
      if (receiverExists.length == 0) {
        return {
          status: false,
          statusCode: 400,
          message: "Receiver does not exist.",
        };
      }

      // Check if the caller or receiver has blocked each other
      const blockedCheckQuery = `
      SELECT 
        * 
      FROM blocked_users 
      WHERE (user_id = ${callerId} AND blocked_id = ${receiverId} AND is_blocked = 1)
      OR (user_id = ${receiverId} AND blocked_id = ${callerId} AND is_blocked = 1)
    `;
      const blockedResult = await dbQuery.queryRunner(blockedCheckQuery);
      if (blockedResult.length > 0) {
        return {
          status: false,
          statusCode: 400,
          message: "Call not allowed due to blocking.",
        };
      }

      const startTime = moment()
        .utcOffset("+05:30")
        .format("YYYY-MM-DD HH:mm:ss");

      // Insert missed call entry into user_calls table
      const insertMissedCallQuery = `
      INSERT INTO user_calls (caller_user_id, receiver_user_id, start_time, call_type, created_at) 
      VALUES (${callerId}, ${receiverId}, '${startTime}', 'missed-call', '${startTime}')
    `;
      const callResult = await dbQuery.queryRunner(insertMissedCallQuery);

      return {
        status: true,
        statusCode: 200,
        callId: callResult.insertId,
        message: "Missed call recorded successfully",
      };
    } catch (error) {
      console.log("error", error);
      return {
        status: false,
        statusCode: 400,
        message: error.message || "Server Error",
      };
    }
  },

  // Start Call
  startCall: async (callerId, receiverId) => {
    try {
      const startTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");

      const callerCheckQuery = `SELECT id, premium_plan_id FROM users WHERE id = ?`;
      console.log("Caller check query:", callerCheckQuery, "with params:", [callerId]);
      const [callerExists] = await dbQuery.queryRunner(callerCheckQuery, [callerId]);

      const receiverCheckQuery = `SELECT id, dnd FROM users WHERE id = ?`;
      console.log("Receiver check query:", receiverCheckQuery, "with params:", [receiverId]);
      const [receiverExists] = await dbQuery.queryRunner(receiverCheckQuery, [receiverId]);

      if (!callerExists) {
        return { status: false, statusCode: 400, message: "Caller does not exist." };
      }
      if (!receiverExists) {
        return { status: false, statusCode: 400, message: "Receiver does not exist." };
      }

      const blockedCheckQuery = `
        SELECT * FROM blocked_users 
        WHERE (user_id = ? AND blocked_id = ? AND is_blocked = 1) 
        OR (user_id = ? AND blocked_id = ? AND is_blocked = 1)
      `;
      console.log("Blocked check query:", blockedCheckQuery, "with params:", [callerId, receiverId, receiverId, callerId]);
      const blockedResult = await dbQuery.queryRunner(blockedCheckQuery, [callerId, receiverId, receiverId, callerId]);
      if (blockedResult.length > 0) {
        return { status: false, statusCode: 400, message: "Call not allowed due to blocking." };
      }

      if (receiverExists.dnd === 1) {
        return { status: false, statusCode: 400, message: "User has enabled DND" };
      }

      // Determine caller charge rate based on premium status
      const callerChargePerMinute = callerExists.premium_plan_id > 0 ? 4 : 7; // 4 INR/min for premium, 7 INR/min for non-premium

      const latestBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY createddate DESC LIMIT 1`;
      console.log("Balance query:", latestBalanceQuery, "with params:", [callerId]);
      const [balanceData] = await dbQuery.queryRunner(latestBalanceQuery, [callerId]);
      const callerBalance = balanceData?.totalBalance || 0;

      // Remove receiver balance check, keep caller balance check optional
      // if (callerBalance <= 0) return { status: false, statusCode: 400, message: "Insufficient balance to start a call." };

      const maxCallLimitMinutes = Math.floor(callerBalance / callerChargePerMinute);
      const maxCallLimitSeconds = maxCallLimitMinutes * 60;
      const maxCallLimitDateTime = moment(startTime)
        .add(maxCallLimitMinutes, "minutes")
        .utcOffset("+05:30")
        .format("YYYY-MM-DD HH:mm:ss");

      const insertCallQuery = `
        INSERT INTO user_calls (caller_user_id, receiver_user_id, start_time, max_call_limit_time, duration_seconds, created_at, call_type) 
        VALUES (?, ?, ?, ?, ?, ?, 'call')
      `;
      const params = [callerId, receiverId, startTime, maxCallLimitDateTime, maxCallLimitSeconds, startTime];
      console.log("Insert query:", insertCallQuery, "with params:", params);
      const callResult = await dbQuery.queryRunner(insertCallQuery, params);

      return {
        status: true,
        statusCode: 200,
        is_call_ended: false,
        startTime: startTime,
        maxCallLimitTime: maxCallLimitMinutes,
        maxCallLimitDateTime: maxCallLimitDateTime,
        callId: callResult.insertId,
        duration_seconds: maxCallLimitSeconds,
        message: "Call started successfully",
      };
    } catch (error) {
      console.error("Error in startCall:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  },

  // End Call & Handle Wallet and Transactions
  endCall: async (callerId, receiverId, callId) => {
    try {
      let endDateTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");
  
      const callDetailsQuery = `SELECT start_time, end_time FROM user_calls WHERE call_id = ? AND call_type = 'call'`;
      console.log("Executing query:", callDetailsQuery, "with params:", [callId]);
      const [callData] = await dbQuery.queryRunner(callDetailsQuery, [callId]);
      if (!callData) {
        return { status: false, statusCode: 400, message: "Call record not found." };
      }
  
      const { start_time: startTime, end_time: endTime } = callData;
      let callDurationSeconds = moment(endDateTime).diff(moment(startTime), "seconds");
      if (!endTime) {
        const updateEndTimeQuery = `UPDATE user_calls SET end_time = ?, duration = ? WHERE call_id = ? AND call_type = 'call'`;
        console.log("Update end time and duration query:", updateEndTimeQuery, "with params:", [endDateTime, callDurationSeconds, callId]);
        await dbQuery.queryRunner(updateEndTimeQuery, [endDateTime, callDurationSeconds, callId]);
      }
  
      if (callDurationSeconds <= 0) {
        return { status: false, statusCode: 400, message: "Invalid call duration" };
      }
  
      const callDurationMinutes = callDurationSeconds / 60;
  
      const callerQuery = `SELECT id AS caller_user_id, name AS caller_user_name, premium_plan_id FROM users WHERE id = ?`;
      console.log("Caller query:", callerQuery, "with params:", [callerId]);
      const [callerData] = await dbQuery.queryRunner(callerQuery, [callerId]);
  
      const receiverQuery = `SELECT id AS receiver_user_id, name AS receiver_user_name, premium_plan_id FROM users WHERE id = ?`;
      console.log("Receiver query:", receiverQuery, "with params:", [receiverId]);
      const [receiverData] = await dbQuery.queryRunner(receiverQuery, [receiverId]);
  
      // Caller charge based on premium status
      const callerChargePerMinute = callerData.premium_plan_id > 0 ? 4 : 7;
  
      // Receiver earnings based on caller and receiver premium status
      let receiverEarningsPerMinute;
      if (callerData.premium_plan_id > 0) {
        receiverEarningsPerMinute = receiverData.premium_plan_id > 0 ? 2 : 1;
      } else {
        receiverEarningsPerMinute = receiverData.premium_plan_id > 0 ? 2 : 1;
      }
      const totalCharge = (callDurationMinutes * callerChargePerMinute).toFixed(2);
      const totalEarnings = (callDurationMinutes * receiverEarningsPerMinute).toFixed(2);
  
      const getCallerBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY createddate DESC LIMIT 1`;
      console.log("Caller balance query:", getCallerBalanceQuery, "with params:", [callerId]);
      const [callerBalanceData] = await dbQuery.queryRunner(getCallerBalanceQuery, [callerId]);
      const latestCallerBalance = parseFloat(callerBalanceData?.totalBalance || 0);
      const newCallerBalance = (latestCallerBalance - parseFloat(totalCharge)).toFixed(2);
  
      const getReceiverBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY createddate DESC LIMIT 1`;
      console.log("Receiver balance query:", getReceiverBalanceQuery, "with params:", [receiverId]);
      const [receiverBalanceData] = await dbQuery.queryRunner(getReceiverBalanceQuery, [receiverId]);
      const latestReceiverBalance = parseFloat(receiverBalanceData?.totalBalance || 0);
      const newReceiverBalance = (latestReceiverBalance + parseFloat(totalEarnings)).toFixed(2);
  
      if (newCallerBalance < 0) {
        return { status: false, statusCode: 400, message: "Insufficient balance to complete the call." };
      }
  
      const insertTransactionQuery = `
        INSERT INTO call_transactions (caller_id, receiver_id, call_id, duration_in_minutes, charge_amount, status, created_at) 
        VALUES (?, ?, ?, ?, ?, 'completed', ?)
      `;
      console.log("Transaction insert query:", insertTransactionQuery, "with params:", [callerId, receiverId, callId, callDurationMinutes.toFixed(2), totalCharge, endDateTime]);
      await dbQuery.queryRunner(insertTransactionQuery, [callerId, receiverId, callId, callDurationMinutes.toFixed(2), totalCharge, endDateTime]);
  
      const insertCallerDebitQuery = `
        INSERT INTO wallet (amount, transaction_type, createdby, paid_status, call_transaction_id, createddate, totalBalance)
        VALUES (?, 'call_withdrawal', ?, 'completed', ?, ?, ?)
      `;
      console.log("Caller debit query:", insertCallerDebitQuery, "with params:", [totalCharge, callerId, callId, endDateTime, newCallerBalance]);
      await dbQuery.queryRunner(insertCallerDebitQuery, [totalCharge, callerId, callId, endDateTime, newCallerBalance]);
  
      const insertReceiverCreditQuery = `
        INSERT INTO wallet (amount, transaction_type, createdby, paid_status, call_transaction_id, createddate, totalBalance)
        VALUES (?, 'call_deposit', ?, 'completed', ?, ?, ?)
      `;
      console.log("Receiver credit query:", insertReceiverCreditQuery, "with params:", [totalEarnings, receiverId, callId, endDateTime, newReceiverBalance]);
      await dbQuery.queryRunner(insertReceiverCreditQuery, [totalEarnings, receiverId, callId, endDateTime, newReceiverBalance]);
  
      return {
        status: true,
        statusCode: 200,
        caller_user_id: callerData.caller_user_id,
        caller_user_name: callerData.caller_user_name,
        receiver_user_id: receiverData.receiver_user_id,
        receiver_user_name: receiverData.receiver_user_name,
        caller_debited_charge: totalCharge,
        receiver_credited_charge: totalEarnings,
        total_duration_seconds: callDurationSeconds,
        available_caller_balance: newCallerBalance,
        available_receiver_balance: newReceiverBalance,
        message: "Call ended, transactions recorded successfully",
        is_call_ended: true,
      };
    } catch (error) {
      console.error("Error in endCall:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  },

  */

  //agora voice and videocall

  missedCall: async (callerId, receiverId, callType) => {
      try {
        const callerCheckQuery = `SELECT id, name FROM users WHERE id = ?`;
        const receiverCheckQuery = `SELECT id, fcm_token FROM users WHERE id = ?`;
        const [callerExists] = await dbQuery.queryRunner(callerCheckQuery, [callerId]);
        const [receiverExists] = await dbQuery.queryRunner(receiverCheckQuery, [receiverId]);

        if (!callerExists) {
          return { status: false, statusCode: 400, message: "Caller does not exist." };
        }
        if (!receiverExists) {
          return { status: false, statusCode: 400, message: "Receiver does not exist." };
        }

        const blockedCheckQuery = `
          SELECT * 
          FROM blocked_users 
          WHERE (user_id = ? AND blocked_id = ? AND is_blocked = 1)
          OR (user_id = ? AND blocked_id = ? AND is_blocked = 1)
        `;
        const blockedResult = await dbQuery.queryRunner(blockedCheckQuery, [callerId, receiverId, receiverId, callerId]);
        if (blockedResult.length > 0) {
          return { status: false, statusCode: 400, message: `${callType} not allowed due to blocking.` };
        }

        const startTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");

        const insertMissedCallQuery = `
          INSERT INTO user_calls (caller_user_id, receiver_user_id, start_time, call_type, created_at) 
          VALUES (?, ?, ?, ?, ?)
        `;
        const callResult = await dbQuery.queryRunner(insertMissedCallQuery, [callerId, receiverId, startTime, `missed-${callType}`, startTime]);

        // Send FCM notification to the receiver for the missed call
        if (receiverExists.fcm_token) {
          const message = {
            token: receiverExists.fcm_token,
            notification: {
              title: `Missed ${callType === 'video-call' ? 'Video' : 'Voice'} Call`,
              body: `You missed a ${callType === 'video-call' ? 'video' : 'voice'} call from ${callerExists.name}.`,
            },
            data: {
              callId: callResult.insertId.toString(),
              callType: `missed-${callType}`,
              callerId: callerId.toString(),
              receiverId: receiverId.toString(),
              startTime: startTime,
            },
          };

          try {
            await admin.messaging().send(message);
            console.log('Missed call FCM notification sent to receiver:', receiverId);
          } catch (error) {
            console.error(`Error sending missed call FCM notification to receiver ${receiverId}:`, error.message);
          }
        } else {
          console.warn(`Receiver FCM token not found for user ${receiverId}. Missed call notification not sent.`);
        }

        return {
          status: true,
          statusCode: 200,
          callId: callResult.insertId,
          message: `Missed ${callType} recorded successfully`,
        };
      } catch (error) {
        console.error("Error in missedCall:", error);
        return { status: false, statusCode: 400, message: error.message || "Server Error" };
      }
    },

  startCall: async (callerId, receiverId, callType) => {
    try {
      const startTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");

      const callerCheckQuery = `SELECT id, premium_plan_id, name FROM users WHERE id = ?`;
      const [callerExists] = await dbQuery.queryRunner(callerCheckQuery, [callerId]);

      const receiverCheckQuery = `SELECT id, dnd, premium_plan_id, name, fcm_token FROM users WHERE id = ?`;
      const [receiverExists] = await dbQuery.queryRunner(receiverCheckQuery, [receiverId]);

      if (!callerExists) {
        return { status: false, statusCode: 400, message: "Caller does not exist." };
      }
      if (!receiverExists) {
        return { status: false, statusCode: 400, message: "Receiver does not exist." };
      }

      const blockedCheckQuery = `
        SELECT * FROM blocked_users 
        WHERE (user_id = ? AND blocked_id = ? AND is_blocked = 1) 
        OR (user_id = ? AND blocked_id = ? AND is_blocked = 1)
      `;
      const blockedResult = await dbQuery.queryRunner(blockedCheckQuery, [callerId, receiverId, receiverId, callerId]);
      if (blockedResult.length > 0) {
        return { status: false, statusCode: 400, message: `${callType} not allowed due to blocking.` };
      }

      if (receiverExists.dnd === 1) {
        return { status: false, statusCode: 400, message: "User has enabled DND" };
      }

      const isVideoCall = callType === 'video-call';
      const callerChargePerMinute = isVideoCall
        ? (callerExists.premium_plan_id > 0 ? 7 : 12)
        : (callerExists.premium_plan_id > 0 ? 4 : 7);
      const receiverEarningsPerMinute = isVideoCall
        ? (receiverExists.premium_plan_id > 0 ? 4 : 2)
        : (receiverExists.premium_plan_id > 0 ? 2 : 1);

      // Fetch and validate wallet balance
      const latestBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      const [callerBalanceData] = await dbQuery.queryRunner(latestBalanceQuery, [callerId]);
      const callerBalance = parseFloat(callerBalanceData?.totalBalance || 0);

      if (callerBalance <= 0) {
        return {
          status: false,
          statusCode: 200,
          message: "Wallet balance is zero or negative.",
        };
      }

      const maxCallLimitMinutes = Math.floor(callerBalance / callerChargePerMinute);
      const maxCallLimitSeconds = maxCallLimitMinutes * 60;

      if (maxCallLimitMinutes <= 0) {
        return {
          status: false,
          statusCode: 200,
          message: "Insufficient balance to make even a 1-minute call.",
        };
      }

      const maxCallLimitDateTime = moment(startTime)
        .add(maxCallLimitMinutes, "minutes")
        .utcOffset("+05:30")
        .format("YYYY-MM-DD HH:mm:ss");

      const channelName = `call_${callerId}_${receiverId}_${Date.now()}`;
      const appId = process.env.AGORA_APP_ID;
      const appCertificate = process.env.AGORA_APP_CERTIFICATE;
      if (!appId || !appCertificate) {
        return { status: false, statusCode: 500, message: "Agora credentials missing." };
      }

      const expireTimeInSeconds = maxCallLimitSeconds || 3600;
      const currentTimestamp = Math.floor(Date.now() / 1000);
      const privilegeExpireTime = currentTimestamp + expireTimeInSeconds;

      const callerToken = RtcTokenBuilder.buildTokenWithUid(
        appId,
        appCertificate,
        channelName,
        parseInt(callerId),
        RtcRole.PUBLISHER,
        privilegeExpireTime
      );

      const receiverToken = RtcTokenBuilder.buildTokenWithUid(
        appId,
        appCertificate,
        channelName,
        parseInt(receiverId),
        RtcRole.SUBSCRIBER,
        privilegeExpireTime
      );

      const insertCallQuery = `
        INSERT INTO user_calls (caller_user_id, receiver_user_id, start_time, max_call_limit_time, duration_seconds, created_at, call_type, channel_name, call_status) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'active')
      `;
      const params = [callerId, receiverId, startTime, maxCallLimitDateTime, maxCallLimitSeconds, startTime, callType, channelName];
      const callResult = await dbQuery.queryRunner(insertCallQuery, params);

      const callId = callResult.insertId;

      // Send FCM notification to the receiver (handle errors gracefully)
      if (receiverExists.fcm_token) {
        const message = {
          token: receiverExists.fcm_token,
          notification: {
            title: `Incoming ${callType === 'video-call' ? 'Video' : 'Voice'} Call`,
            body: `${callerExists.name} is calling you!`,
          },
          data: {
            callId: callId.toString(),
            callType: callType,
            callerId: callerId.toString(),
            receiverId: receiverId.toString(),
            channelName: channelName,
            appId: appId,
            receiverToken: receiverToken,
            startTime: startTime,
          },
        };

        try {
          await admin.messaging().send(message);
          console.log('FCM notification sent to receiver:', receiverId);
        } catch (error) {
          console.error(`Failed to send FCM notification to receiver ${receiverId}:`, error.message);
          // Continue with the call even if notification fails
        }
      } else {
        console.warn(`Receiver FCM token not found for user ${receiverId}. Notification not sent.`);
      }

      setTimeout(() => {
        module.exports.startLiveTracking(callId, callerId, receiverId, startTime, callerChargePerMinute, receiverEarningsPerMinute);
      }, 0);

      return {
        status: true,
        statusCode: 200,
        is_call_ended: false,
        startTime: startTime,
        maxCallLimitTime: maxCallLimitMinutes,
        maxCallLimitDateTime: maxCallLimitDateTime,
        callId: callId,
        duration_seconds: maxCallLimitSeconds,
        channelName: channelName,
        appId: appId,
        callerToken: callerToken,
        receiverToken: receiverToken,
        message: `${callType} started successfully`,
      };
    } catch (error) {
      console.error(`Error in startCall (${callType}):`, error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  },

  endCall: async (callerId, receiverId, callId) => {
    try {
      const endDateTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");

      const callDetailsQuery = `SELECT start_time, end_time, channel_name, call_status, call_type FROM user_calls WHERE call_id = ?`;
      const [callData] = await dbQuery.queryRunner(callDetailsQuery, [callId]);
      if (!callData) {
        return { status: false, statusCode: 400, message: "Call record not found." };
      }

      if (callData.call_status === 'ended') {
        return { status: false, statusCode: 400, message: "Call already ended." };
      }

      const { start_time: startTime, end_time: endTime, channel_name: channelName, call_type: callType } = callData;
      let callDurationSeconds = moment(endDateTime).diff(moment(startTime), "seconds");

      // Check if this is a missed call (duration is 0 or less)
      if (callDurationSeconds <= 0) {
        // Update the call record to reflect it as ended
        const updateEndTimeQuery = `UPDATE user_calls SET end_time = ?, duration = ?, call_status = 'ended' WHERE call_id = ?`;
        await dbQuery.queryRunner(updateEndTimeQuery, [endDateTime, 0, callId]);

        // Record the missed call and notify the receiver
        await module.exports.missedCall(callerId, receiverId, callType);

        const wssAgora = getWssAgora();
        if (wssAgora && typeof wssAgora.closeConnection === 'function') {
          wssAgora.closeConnection(callId);
        }

        return {
          status: true,
          statusCode: 200,
          channelName: channelName,
          total_duration_seconds: 0,
          message: "Call ended (missed by receiver)",
          is_call_ended: true,
        };
      }

      // Proceed with normal call end logic if the call was answered
      if (!endTime) {
        const updateEndTimeQuery = `UPDATE user_calls SET end_time = ?, duration = ?, call_status = 'ended' WHERE call_id = ?`;
        await dbQuery.queryRunner(updateEndTimeQuery, [endDateTime, callDurationSeconds, callId]);
      }

      const callerQuery = `SELECT premium_plan_id FROM users WHERE id = ?`;
      const receiverQuery = `SELECT premium_plan_id FROM users WHERE id = ?`;
      const [callerData] = await dbQuery.queryRunner(callerQuery, [callerId]);
      const [receiverData] = await dbQuery.queryRunner(receiverQuery, [receiverId]);

      const isVideoCall = callType === 'video-call';
      const callerChargePerMinute = isVideoCall
        ? (callerData.premium_plan_id > 0 ? 7 : 12)
        : (callerData.premium_plan_id > 0 ? 4 : 7);
      const receiverEarningsPerMinute = isVideoCall
        ? (receiverData.premium_plan_id > 0 ? 4 : 2)
        : (receiverData.premium_plan_id > 0 ? 2 : 1);

      const callerChargePerSecond = callerChargePerMinute / 60;
      const receiverEarningsPerSecond = receiverEarningsPerMinute / 60;

      const totalCallerDebit = callerChargePerSecond * callDurationSeconds;
      const totalReceiverCredit = receiverEarningsPerSecond * callDurationSeconds;

      const latestBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      const [callerBalanceData] = await dbQuery.queryRunner(latestBalanceQuery, [callerId]);
      const [receiverBalanceData] = await dbQuery.queryRunner(latestBalanceQuery, [receiverId]);

      let callerBalance = parseFloat(callerBalanceData?.totalBalance || 0);
      let receiverBalance = parseFloat(receiverBalanceData?.totalBalance || 0);

      callerBalance -= totalCallerDebit;
      receiverBalance += totalReceiverCredit;

      const updateCallerBalanceQuery = `UPDATE wallet SET totalBalance = ? WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      await dbQuery.queryRunner(updateCallerBalanceQuery, [callerBalance.toFixed(2), callerId]);

      const updateReceiverBalanceQuery = `UPDATE wallet SET totalBalance = ? WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      await dbQuery.queryRunner(updateReceiverBalanceQuery, [receiverBalance.toFixed(2), receiverId]);

      console.log(`Wallet updated - Caller ${callerId}: ${callerBalance.toFixed(2)}, Receiver ${receiverId}: ${receiverBalance.toFixed(2)}`);

      const wssAgora = getWssAgora();
      if (wssAgora && typeof wssAgora.closeConnection === 'function') {
        wssAgora.closeConnection(callId);
      }

      return {
        status: true,
        statusCode: 200,
        channelName: channelName,
        total_duration_seconds: callDurationSeconds,
        message: "Call ended successfully",
        is_call_ended: true,
      };
    } catch (error) {
      console.error("Error in endCall:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  },

  startLiveTracking: async (callId, callerId, receiverId, startTime, callerChargePerMinute, receiverEarningsPerMinute) => {
    try {
      let callDurationSeconds = 0;
      const wssAgora = getWssAgora();

      const latestBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      const [callerBalanceData] = await dbQuery.queryRunner(latestBalanceQuery, [callerId]);
      const [receiverBalanceData] = await dbQuery.queryRunner(latestBalanceQuery, [receiverId]);

      let callerBalance = parseFloat(callerBalanceData?.totalBalance || 0);
      let receiverBalance = parseFloat(receiverBalanceData?.totalBalance || 0);

      const callerChargePerSecond = callerChargePerMinute / 60;
      const maxCallDurationSeconds = Math.floor(callerBalance / callerChargePerSecond);
      let isCallEnded = false;

      const client = wssAgora?.clients?.get(callId.toString());
      if (client) {
        client.on('message', (message) => {
          const data = JSON.parse(message.toString());
          if (data.event === 'call_ended') {
            isCallEnded = true;
          }
        });

        client.on('close', () => {
          isCallEnded = true;
        });
      }

      const interval = setInterval(async () => {
        // If WebSocket connection exists and call is ended
        if (isCallEnded) {
          clearInterval(interval);
          const endDateTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");
          const updateEndTimeQuery = `UPDATE user_calls SET end_time = ?, duration = ?, call_status = 'ended' WHERE call_id = ?`;
          await dbQuery.queryRunner(updateEndTimeQuery, [endDateTime, callDurationSeconds, callId]);

          const totalCallerDebit = callerChargePerSecond * callDurationSeconds;
          const totalReceiverCredit = (receiverEarningsPerMinute / 60) * callDurationSeconds;

          callerBalance -= totalCallerDebit;
          receiverBalance += totalReceiverCredit;

          const updateCallerBalanceQuery = `UPDATE wallet SET totalBalance = ? WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
          await dbQuery.queryRunner(updateCallerBalanceQuery, [callerBalance.toFixed(2), callerId]);

          const updateReceiverBalanceQuery = `UPDATE wallet SET totalBalance = ? WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
          await dbQuery.queryRunner(updateReceiverBalanceQuery, [receiverBalance.toFixed(2), receiverId]);

          console.log(`Call ended - Caller ${callerId}: ${callerBalance.toFixed(2)}, Receiver ${receiverId}: ${receiverBalance.toFixed(2)}`);

          if (wssAgora && typeof wssAgora.closeConnection === 'function') {
            wssAgora.closeConnection(callId);
          }
          return;
        }

        // If no WebSocket connection, check call_status in the database
        if (!client || !wssAgora?.clients?.has(callId.toString())) {
          const callStatusQuery = `SELECT call_status FROM user_calls WHERE call_id = ?`;
          const [callStatusData] = await dbQuery.queryRunner(callStatusQuery, [callId]);
          if (callStatusData?.call_status === 'ended') {
            clearInterval(interval);
            console.log(`Call ${callId} ended (detected via database). Stopping live tracking.`);
            if (wssAgora && typeof wssAgora.closeConnection === 'function') {
              wssAgora.closeConnection(callId);
            }
            return;
          }
        }

        callDurationSeconds++;

        const estimatedCallerDebit = callerChargePerSecond * callDurationSeconds;
        const estimatedCallerBalance = callerBalance - estimatedCallerDebit;

        if (estimatedCallerBalance < 0 || callDurationSeconds >= maxCallDurationSeconds) {
          clearInterval(interval);
          const endDateTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");
          const updateEndTimeQuery = `UPDATE user_calls SET end_time = ?, duration = ?, call_status = 'ended' WHERE call_id = ?`;
          await dbQuery.queryRunner(updateEndTimeQuery, [endDateTime, callDurationSeconds, callId]);

          const totalCallerDebit = callerChargePerSecond * callDurationSeconds;
          const totalReceiverCredit = (receiverEarningsPerMinute / 60) * callDurationSeconds;

          callerBalance -= totalCallerDebit;
          receiverBalance += totalReceiverCredit;

          const updateCallerBalanceQuery = `UPDATE wallet SET totalBalance = ? WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
          await dbQuery.queryRunner(updateCallerBalanceQuery, [callerBalance.toFixed(2), callerId]);

          const updateReceiverBalanceQuery = `UPDATE wallet SET totalBalance = ? WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
          await dbQuery.queryRunner(updateReceiverBalanceQuery, [receiverBalance.toFixed(2), receiverId]);

          console.log(`Call ended due to insufficient balance or max duration - Caller ${callerId}: ${callerBalance.toFixed(2)}, Receiver ${receiverId}: ${receiverBalance.toFixed(2)}`);

          if (wssAgora && typeof wssAgora.sendToCall === 'function') {
            wssAgora.sendToCall(callId, { event: 'call_ended', reason: estimatedCallerBalance < 0 ? 'insufficient_balance' : 'max_duration_reached' });
          }
          if (wssAgora && typeof wssAgora.closeConnection === 'function') {
            wssAgora.closeConnection(callId);
          }
          return;
        }

        const estimatedReceiverCredit = (receiverEarningsPerMinute / 60) * callDurationSeconds;
        const estimatedReceiverBalance = receiverBalance + estimatedReceiverCredit;

        if (wssAgora && typeof wssAgora.sendToCall === 'function') {
          wssAgora.sendToCall(callId, {
            event: 'call_update',
            duration_seconds: callDurationSeconds,
            caller_balance: estimatedCallerBalance.toFixed(2),
            receiver_balance: estimatedReceiverBalance.toFixed(2),
          });
        } else {
          console.log(`No active WebSocket connection for callId: ${callId}`);
        }
      }, 1000);
    } catch (error) {
      console.error("Error in startLiveTracking:", error);
    }
  },

   //videocall 
   missedVideoCall: async (callerId, receiverId) => {
    try {
      // Validate if caller and receiver exist
      const callerCheckQuery = `SELECT * FROM users WHERE id = ?`;
      const receiverCheckQuery = `SELECT * FROM users WHERE id = ?`;
      const [callerExists] = await dbQuery.queryRunner(callerCheckQuery, [callerId]);
      const [receiverExists] = await dbQuery.queryRunner(receiverCheckQuery, [receiverId]);
  
      if (!callerExists) {
        return {
          status: false,
          statusCode: 400,
          message: "Caller does not exist.",
        };
      }
      if (!receiverExists) {
        return {
          status: false,
          statusCode: 400,
          message: "Receiver does not exist.",
        };
      }
  
      // Check if the caller or receiver has blocked each other
      const blockedCheckQuery = `
        SELECT * 
        FROM blocked_users 
        WHERE (user_id = ? AND blocked_id = ? AND is_blocked = 1)
        OR (user_id = ? AND blocked_id = ? AND is_blocked = 1)
      `;
      const blockedResult = await dbQuery.queryRunner(blockedCheckQuery, [callerId, receiverId, receiverId, callerId]);
      if (blockedResult.length > 0) {
        return {
          status: false,
          statusCode: 400,
          message: "Video call not allowed due to blocking.",
        };
      }
  
      const startTime = moment()
        .utcOffset("+05:30")
        .format("YYYY-MM-DD HH:mm:ss");
  
      // Insert missed video call entry into user_video_calls table
      const insertMissedCallQuery = `
        INSERT INTO user_video_calls (caller_user_id, receiver_user_id, start_time, call_type, created_at) 
        VALUES (?, ?, ?, 'missed-video-call', ?)
      `;
      const callResult = await dbQuery.queryRunner(insertMissedCallQuery, [callerId, receiverId, startTime, startTime]);
  
      return {
        status: true,
        statusCode: 200,
        callId: callResult.insertId,
        message: "Missed video call recorded successfully",
      };
    } catch (error) {
      console.log("error", error);
      return {
        status: false,
        statusCode: 400,
        message: error.message || "Server Error",
      };
    }
  },
  
  // userService.startVideoCall
  startVideoCall: async (callerId, receiverId) => {
    try {
      const startTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");
  
      const callerCheckQuery = `SELECT id, premium_plan_id FROM users WHERE id = ?`;
      const [callerExists] = await dbQuery.queryRunner(callerCheckQuery, [callerId]);
  
      const receiverCheckQuery = `SELECT id, dnd FROM users WHERE id = ?`;
      const [receiverExists] = await dbQuery.queryRunner(receiverCheckQuery, [receiverId]);
  
      if (!callerExists) {
        return { status: false, statusCode: 400, message: "Caller does not exist." };
      }
      if (!receiverExists) {
        return { status: false, statusCode: 400, message: "Receiver does not exist." };
      }
  
      const blockedCheckQuery = `
        SELECT * FROM blocked_users 
        WHERE (user_id = ? AND blocked_id = ? AND is_blocked = 1) 
        OR (user_id = ? AND blocked_id = ? AND is_blocked = 1)
      `;
      const blockedResult = await dbQuery.queryRunner(blockedCheckQuery, [callerId, receiverId, receiverId, callerId]);
      if (blockedResult.length > 0) {
        return { status: false, statusCode: 400, message: "Video call not allowed due to blocking." };
      }
  
      if (receiverExists.dnd === 1) {
        return { status: false, statusCode: 400, message: "User has enabled DND" };
      }
  
      // Determine caller charge rate based on premium status
      const callerChargePerMinute = callerExists.premium_plan_id > 0 ? 7 : 12; // 7 INR/min for premium, 12 INR/min for non-premium
  
      const latestBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY createddate DESC LIMIT 1`;
      const [balanceData] = await dbQuery.queryRunner(latestBalanceQuery, [callerId]);
      const callerBalance = balanceData?.totalBalance || 0;
  
      const maxCallLimitMinutes = Math.floor(callerBalance / callerChargePerMinute);
      const maxCallLimitSeconds = maxCallLimitMinutes * 60;
      const maxCallLimitDateTime = moment(startTime)
        .add(maxCallLimitMinutes, "minutes")
        .utcOffset("+05:30")
        .format("YYYY-MM-DD HH:mm:ss");
  
      const insertCallQuery = `
        INSERT INTO user_video_calls (caller_user_id, receiver_user_id, start_time, max_call_limit_time, duration_seconds, created_at, call_type) 
        VALUES (?, ?, ?, ?, ?, ?, 'video-call')
      `;
      const params = [callerId, receiverId, startTime, maxCallLimitDateTime, maxCallLimitSeconds, startTime];
      const callResult = await dbQuery.queryRunner(insertCallQuery, params);
  
      return {
        status: true,
        statusCode: 200,
        is_call_ended: false,
        startTime: startTime,
        maxCallLimitTime: maxCallLimitMinutes,
        maxCallLimitDateTime: maxCallLimitDateTime,
        callId: callResult.insertId,
        duration_seconds: maxCallLimitSeconds,
        message: "Video call started successfully",
      };
    } catch (error) {
      console.error("Error in startVideoCall:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  },
  
  // userService.endVideoCall
  endVideoCall: async (callerId, receiverId, callId) => {
    try {
      let endDateTime = moment().utcOffset("+05:30").format("YYYY-MM-DD HH:mm:ss");
  
      const callDetailsQuery = `SELECT start_time, end_time FROM user_video_calls WHERE call_id = ? AND call_type = 'video-call'`;
      const [callData] = await dbQuery.queryRunner(callDetailsQuery, [callId]);
      if (!callData) {
        return { status: false, statusCode: 400, message: "Video call record not found." };
      }
  
      const { start_time: startTime, end_time: endTime } = callData;
      let callDurationSeconds = moment(endDateTime).diff(moment(startTime), "seconds");
      if (!endTime) {
        const updateEndTimeQuery = `UPDATE user_video_calls SET end_time = ?, duration = ? WHERE call_id = ? AND call_type = 'video-call'`;
        await dbQuery.queryRunner(updateEndTimeQuery, [endDateTime, callDurationSeconds, callId]);
      }
  
      if (callDurationSeconds <= 0) {
        return { status: false, statusCode: 400, message: "Invalid video call duration" };
      }
  
      const callDurationMinutes = callDurationSeconds / 60;
  
      const callerQuery = `SELECT id AS caller_user_id, name AS caller_user_name, premium_plan_id FROM users WHERE id = ?`;
      const [callerData] = await dbQuery.queryRunner(callerQuery, [callerId]);
  
      const receiverQuery = `SELECT id AS receiver_user_id, name AS receiver_user_name, premium_plan_id FROM users WHERE id = ?`;
      const [receiverData] = await dbQuery.queryRunner(receiverQuery, [receiverId]);
  
      // Caller charge based on premium status
      const callerChargePerMinute = callerData.premium_plan_id > 0 ? 7 : 12;
  
      // Receiver earnings based on their premium status
      const receiverEarningsPerMinute = receiverData.premium_plan_id > 0 ? 4 : 2;
  
      const totalCharge = (callDurationMinutes * callerChargePerMinute).toFixed(2);
      const totalEarnings = (callDurationMinutes * receiverEarningsPerMinute).toFixed(2);
  
      const getCallerBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY createddate DESC LIMIT 1`;
      const [callerBalanceData] = await dbQuery.queryRunner(getCallerBalanceQuery, [callerId]);
      const latestCallerBalance = parseFloat(callerBalanceData?.totalBalance || 0);
      const newCallerBalance = (latestCallerBalance - parseFloat(totalCharge)).toFixed(2);
  
      const getReceiverBalanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY createddate DESC LIMIT 1`;
      const [receiverBalanceData] = await dbQuery.queryRunner(getReceiverBalanceQuery, [receiverId]);
      const latestReceiverBalance = parseFloat(receiverBalanceData?.totalBalance || 0);
      const newReceiverBalance = (latestReceiverBalance + parseFloat(totalEarnings)).toFixed(2);
  
      if (newCallerBalance < 0) {
        return { status: false, statusCode: 400, message: "Insufficient balance to complete the video call." };
      }
  
      const insertTransactionQuery = `
        INSERT INTO call_transactions (caller_id, receiver_id, call_id, duration_in_minutes, charge_amount, status, created_at, call_type)
        VALUES (?, ?, ?, ?, ?, 'completed', ?, 'video-call')
      `;
      await dbQuery.queryRunner(insertTransactionQuery, [callerId, receiverId, callId, callDurationMinutes.toFixed(2), totalCharge, endDateTime]);
  
      const insertCallerDebitQuery = `
        INSERT INTO wallet (amount, transaction_type, createdby, paid_status, call_transaction_id, createddate, totalBalance)
        VALUES (?, 'video_call_withdrawal', ?, 'completed', ?, ?, ?)
      `;
      await dbQuery.queryRunner(insertCallerDebitQuery, [totalCharge, callerId, callId, endDateTime, newCallerBalance]);
  
      const insertReceiverCreditQuery = `
        INSERT INTO wallet (amount, transaction_type, createdby, paid_status, call_transaction_id, createddate, totalBalance)
        VALUES (?, 'video_call_deposit', ?, 'completed', ?, ?, ?)
      `;
      await dbQuery.queryRunner(insertReceiverCreditQuery, [totalEarnings, receiverId, callId, endDateTime, newReceiverBalance]);
  
      return {
        status: true,
        statusCode: 200,
        caller_user_id: callerData.caller_user_id,
        caller_user_name: callerData.caller_user_name,
        receiver_user_id: receiverData.receiver_user_id,
        receiver_user_name: receiverData.receiver_user_name,
        caller_debited_charge: totalCharge,
        receiver_credited_charge: totalEarnings,
        total_duration_seconds: callDurationSeconds,
        available_caller_balance: newCallerBalance,
        available_receiver_balance: newReceiverBalance,
        message: "Video call ended, transactions recorded successfully",
        is_call_ended: true,
      };
    } catch (error) {
      console.error("Error in endVideoCall:", error);
      return { status: false, statusCode: 400, message: error.message || "Server Error" };
    }
  },

  //followingId, followerId, action
  followUser: async (data) => {
    try {
      // Get current time and add 5 hours 30 minute
      const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      if (!["follow", "unfollow"].includes(data.action)) {
        return {
          error: "Invalid action. Use 'follow' or 'unfollow'.",
        };
      }

      // Check if user is already following
      const existing = await dbQuery.queryRunner(
        `SELECT id FROM user_followers WHERE followingId = ${data.followingId} AND followerId = ${data.followerId} LIMIT 1`
      );

      if (data.action === "follow") {
        if (existing.length > 0) {
          return {
            status: 200,
            message: "Already following",
            is_following: true,
            action: "follow",
          };
        }

        // Follow the user
        await dbQuery.queryRunner(
          `INSERT INTO user_followers (followingId, followerId,created_at) VALUES (${data.followingId}, ${data.followerId},'${updatedTime}')`
        );
        return {
          status: 200,
          message: "Followed successfully",
          is_following: true,
          action: "follow",
        };
      }

      if (data.action === "unfollow") {
        if (existing.length === 0) {
          return {
            status: 200,
            message: "Already unfollowed",
            is_following: false,
            action: "unfollow",
          };
        }

        // Unfollow the user
        await dbQuery.queryRunner(
          `DELETE FROM user_followers WHERE followingId = ${data.followingId} AND followerId = ${data.followerId}`
        );
        return {
          status: 200,
          message: "Unfollowed successfully",
          is_following: false,
          action: "unfollow",
        };
      }
    } catch (error) {
      console.error("Service Error:", error);
      return { error: "Failed to follow/unfollow user" };
    }
  },

  getFollowers: async (user_id) => {
    try {
      const userExists = `
          SELECT * FROM users WHERE id=${user_id}
        `;
      const getUserData = await dbQuery.queryRunner(userExists);

      if (getUserData.length == 0) {
        return {
          error: "Invalid user",
        };
      }

      const followersQuery = `
      SELECT 
          u.id, 
          MAX(u.name) AS name,  -- Using MAX() ensures a single value per group
          MAX(u.profile_image) AS profile_image,
          CASE 
              WHEN MAX(uf2.followerId) IS NOT NULL THEN true 
              ELSE false 
          END AS is_followed
      FROM user_followers uf 
      LEFT JOIN users u ON uf.followerId = u.id
      LEFT JOIN user_followers uf2 ON u.id = uf2.followingId AND uf2.followerId = ${user_id}
      WHERE uf.followingId = ${user_id}
      GROUP BY u.id;
      `;
      const rows = await dbQuery.queryRunner(followersQuery);
      console.log("rrr", rows);
      return { followers: rows };
    } catch (error) {
      return {
        error: "Database error while fetching followers." + error,
      };
    }
  },

  // Get followings list
  getFollowings: async (user_id) => {
    try {
      const userExists = `
          SELECT * FROM users WHERE id=${user_id}
        `;
      const getUserData = await dbQuery.queryRunner(userExists);

      if (getUserData.length == 0) {
        return {
          error: "Invalid user",
        };
      }

      const query = `
      SELECT 
        u.id, 
        u.name, 
        u.profile_image 
      FROM user_followers uf 
      LEFT JOIN users u ON uf.followingId = u.id
      WHERE uf.followerId = ${user_id}`;

      console.log("query followings", query);
      const rows = await dbQuery.queryRunner(query);
      return { followings: rows };
    } catch (error) {
      throw new Error("Database error while fetching followings");
    }
  },

  getBlockedUsers: async (user_id, page, limit) => {
    try {
      const offset = (page - 1) * limit;

      // Query to get paginated blocked users and check if the user is blocked
      const query = `
            SELECT u.id, u.name, u.emailId AS emailId, u.mobile_number, u.username, 
              CASE 
                  WHEN bu.user_id IS NOT NULL THEN true 
                  ELSE false 
              END AS is_blocked
            FROM users u
            LEFT JOIN blocked_users bu ON u.id = bu.blocked_id AND bu.user_id = ${user_id}
            WHERE u.id != ${user_id}
            LIMIT ${limit} OFFSET ${offset}
        `;
      const users = await dbQuery.queryRunner(query);
      111;
      // Query to get the total count of blocked users (for pagination)
      const countQuery = `
            SELECT COUNT(*) AS total FROM users WHERE id != ${user_id}
        `;
      const countResult = await dbQuery.queryRunner(countQuery);
      const total = countResult[0].total;

      return { list: users, total };
    } catch (error) {
      console.log("E", error);
      throw new Error("Database error while fetching blocked users");
    }
  },
  checkUserAvailability: async (user_id) => {
    try {
      const isUserExistQuery = `SELECT * FROM users WHERE id=${user_id}`;
      const isUserExist = await dbQuery.queryRunner(isUserExistQuery);

      if (isUserExist.length == 0) {
        return {
          status: false,
          message: "Invalid caller or receiver id",
        };
      }
      return {
        status: true,
        is_available: isUserExist[0].is_available == 1 ? true : false,
        dnd: isUserExist[0].dnd == 1 ? true : false,
        message:
          isUserExist[0].is_available == 1
            ? "User is available"
            : "User is not available",
      };
    } catch (error) {
      console.error("Database error:", error);
      return {
        status: false,
        is_available: null,
        message: "Something went wrong. Please try again later.",
      };
    }
  },

  getMissedCallList: async (userId = null, page = 1, limit = 20) => {
    try {
      const offset = (page - 1) * limit;

      // Base Query with Join to Users Table
      let baseQuery = `
      SELECT 
        uc.call_id, 
        uc.caller_user_id, 
        caller.name AS caller_name,
        uc.receiver_user_id, 
        receiver.name AS receiver_name,
        DATE_FORMAT(uc.start_time, '%Y-%m-%d %H:%i:%s') AS start_time
      FROM user_calls AS uc
      LEFT JOIN users AS caller ON uc.caller_user_id = caller.id
      LEFT JOIN users AS receiver ON uc.receiver_user_id = receiver.id
      WHERE uc.call_type = 'missed-call'
    `;

      let countQuery = `
      SELECT COUNT(*) AS total 
      FROM user_calls 
      WHERE call_type = 'missed-call'
    `;

      // If userId is provided, apply filtering
      if (userId) {
        baseQuery += ` AND (uc.caller_user_id = ${userId} OR uc.receiver_user_id = ${userId})`;
        countQuery += ` AND (caller_user_id = ${userId} OR receiver_user_id = ${userId})`;
      }

      // Add sorting and pagination
      baseQuery += ` ORDER BY uc.start_time DESC LIMIT ${limit} OFFSET ${offset}`;

      // Execute Queries
      const missedCalls = await dbQuery.queryRunner(baseQuery);
      const totalRecordsResult = await dbQuery.queryRunner(countQuery);
      const totalRecords = totalRecordsResult[0]?.total || 0;

      return {
        status: true,
        statusCode: 200,
        data: missedCalls,
        pagination: {
          page,
          limit,
          totalRecords,
        },
        message: "Missed calls fetched successfully",
      };
    } catch (error) {
      console.error("Error in getMissedCallList:", error);
      return {
        status: false,
        statusCode: 500,
        message: "Database error while fetching missed calls.",
      };
    }
  },

  
  /*
  upgradePremium :  async ({
    payment_status,
    user_id,
    plan_id,
    order_id,
    payment_id,
    coupon_code,
  }) => {
    try {
      if (payment_status !== "success") {
        return {
          status: false,
          statusCode: 400,
          message: "Payment was not successful",
          is_premium: false,
        };
      }
  
      // Validate user
      const userQuery = `SELECT id FROM users WHERE id = ${user_id} LIMIT 1`;
      const user = await dbQuery.queryRunner(userQuery);
      if (!user.length) {
        return { status: false, statusCode: 400, message: "Invalid user" };
      }
  
      // Validate plan
      const planQuery = `SELECT duration_in_months FROM subscription_plans WHERE id = ${plan_id} LIMIT 1`;
      const plan = await dbQuery.queryRunner(planQuery);
      if (!plan.length) {
        return { status: false, statusCode: 400, message: "Invalid plan" };
      }
  
      // Validate transaction
      const transactionQuery = `SELECT status, payment_id, order_id FROM transactions WHERE payment_id = '${payment_id}' AND order_id = '${order_id}'`;
      const transaction = await dbQuery.queryRunner(transactionQuery);
      if (
        !transaction.length ||
        transaction[0].status !== "success" ||
        transaction[0].payment_id !== payment_id ||
        transaction[0].order_id !== order_id
      ) {
        return {
          status: false,
          statusCode: 400,
          message: "Payment verification failed.",
        };
      }
  
      // Handle coupon if provided
      if (coupon_code && ![null, undefined, "", 0].includes(coupon_code)) {
        const couponResult = await WalletService.createCouponTransaction(
          coupon_code,
          user_id,
          plan_id
        );
        if (couponResult.statusCode === 400) {
          return {
            status: false,
            statusCode: 400,
            message: couponResult.message,
          };
        }
      }
  
      // Note: Plan creation is handled in verifyAndUpsertTransaction
      return {
        status: true,
        statusCode: 200,
        message: "Premium plan processed successfully",
        is_premium: true,
      };
    } catch (error) {
      console.error("Error upgrading premium:", error);
      return {
        status: false,
        statusCode: 400,
        message: "Internal Server Error",
        is_premium: false,
      };
    }
  },
  */

  

  getUserPremiumPlans : async (user_id) => {
    try {
      const userQuery = `SELECT id FROM users WHERE id = ${user_id} LIMIT 1`;
      const user = await dbQuery.queryRunner(userQuery);
      if (!user.length) {
        return { status: false, statusCode: 400, message: "Invalid user" };
      }
  
      // Check for expired plans and update status
      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      await dbQuery.queryRunner(`
        UPDATE user_premium_plans 
        SET status = 'expired', is_active = 0
        WHERE user_id = ${user_id} AND status = 'active' AND end_time < '${currentTime}'
      `);
  
      // Activate next queued plan if no active plan
      const activePlanQuery = `
        SELECT id FROM user_premium_plans 
        WHERE user_id = ${user_id} AND status = 'active' AND end_time > '${currentTime}'
      `;
      const activePlan = await dbQuery.queryRunner(activePlanQuery);
  
      if (!activePlan.length) {
        const queuedPlanQuery = `
          SELECT upp.id, upp.plan_id, sp.duration_in_months
          FROM user_premium_plans upp
          JOIN subscription_plans sp ON upp.plan_id = sp.id
          WHERE upp.user_id = ${user_id} AND upp.status = 'queued'
          ORDER BY upp.created_at ASC
          LIMIT 1
        `;
        const queuedPlan = await dbQuery.queryRunner(queuedPlanQuery);
  
        if (queuedPlan.length) {
          const endTime = moment()
            .add(queuedPlan[0].duration_in_months, "months")
            .utcOffset(330)
            .format("YYYY-MM-DD HH:mm:ss");
          await dbQuery.queryRunner(`
            UPDATE user_premium_plans 
            SET status = 'active', is_active = 1, start_time = '${currentTime}', end_time = '${endTime}'
            WHERE id = ${queuedPlan[0].id}
          `);
  
          await dbQuery.queryRunner(`
            UPDATE users SET premium = 1, premium_plan_id = ${queuedPlan[0].plan_id} WHERE id = ${user_id}
          `);
        } else {
          await dbQuery.queryRunner(`
            UPDATE users SET premium = 0, premium_plan_id = 0 WHERE id = ${user_id}
          `);
        }
      }
  
      // Fetch all plans
      const plansQuery = `
        SELECT upp.id, upp.plan_id, upp.amount, upp.start_time, upp.end_time, upp.status, upp.created_at, 
               sp.plan_name, sp.duration_in_months, sp.actual_price
        FROM user_premium_plans upp
        JOIN subscription_plans sp ON upp.plan_id = sp.id
        WHERE upp.user_id = ${user_id}
        ORDER BY upp.created_at ASC
      `;
      const plans = await dbQuery.queryRunner(plansQuery);
  
      return {
        status: true,
        statusCode: 200,
        message: "User premium plans fetched successfully",
        data: plans,
      };
    } catch (error) {
      console.error("Error fetching user premium plans:", error);
      return {
        status: false,
        statusCode: 400,
        message: "Failed to fetch user premium plans",
      };
    }
  },  



  getUserPremiumPlansCount : async (user_id) => {
    try {
      const userQuery = `SELECT id FROM users WHERE id = ${user_id} LIMIT 1`;
      const user = await dbQuery.queryRunner(userQuery);
      if (!user.length) {
        return { status: false, statusCode: 400, message: "Invalid user" };
      }
  
      const countQuery = `
        SELECT 
          COUNT(*) as total_plans,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_plans,
          SUM(CASE WHEN status = 'queued' THEN 1 ELSE 0 END) as queued_plans,
          SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_plans
        FROM user_premium_plans 
        WHERE user_id = ${user_id}
      `;
      const counts = await dbQuery.queryRunner(countQuery);
  
      return {
        status: true,
        statusCode: 200,
        message: "User premium plans count fetched successfully",
        data: counts[0],
      };
    } catch (error) {
      console.error("Error fetching user premium plans count:", error);
      return {
        status: false,
        statusCode: 400,
        message: "Failed to fetch user premium plans count",
      };
    }
  },
  
  activatePremiumPlan : async ({ user_id, plan_id }) => {
    try {
      const userQuery = `SELECT id FROM users WHERE id = ${user_id} LIMIT 1`;
      const user = await dbQuery.queryRunner(userQuery);
      if (!user.length) {
        return { status: false, statusCode: 400, message: "Invalid user" };
      }
  
      const planQuery = `
        SELECT upp.id, upp.plan_id, upp.status, sp.duration_in_months
        FROM user_premium_plans upp
        JOIN subscription_plans sp ON upp.plan_id = sp.id
        WHERE upp.id = ${plan_id} AND upp.user_id = ${user_id} AND upp.status = 'queued'
      `;
      const plan = await dbQuery.queryRunner(planQuery);
      if (!plan.length) {
        return {
          status: false,
          statusCode: 400,
          message: "Invalid or already active plan",
        };
      }
  
      // Deactivate existing active plan
      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      await dbQuery.queryRunner(`
        UPDATE user_premium_plans 
        SET status = 'expired', is_active = 0, end_time = '${currentTime}'
        WHERE user_id = ${user_id} AND status = 'active'
      `);
  
      // Activate the selected plan
      const endTime = moment()
        .add(plan[0].duration_in_months, "months")
        .utcOffset(330)
        .format("YYYY-MM-DD HH:mm:ss");
      await dbQuery.queryRunner(`
        UPDATE user_premium_plans 
        SET status = 'active', is_active = 1, start_time = '${currentTime}', end_time = '${endTime}'
        WHERE id = ${plan_id}
      `);
  
      // Update users table
      await dbQuery.queryRunner(`
        UPDATE users SET premium = 1, premium_plan_id = ${plan[0].plan_id} WHERE id = ${user_id}
      `);
  
      return {
        status: true,
        statusCode: 200,
        message: "Plan activated successfully",
      };
    } catch (error) {
      console.error("Error activating premium plan:", error);
      return {
        status: false,
        statusCode: 400,
        message: "Failed to activate premium plan",
      };
    }
  },

  // New method to get user by mobile number
  getUserByMobile: async (mobile_number) => {
    try {
      const query = `SELECT * FROM users WHERE mobile_number = ?`;
      const user = await dbQuery.queryRunner(query, [mobile_number]);
      if (!user.length) {
        return null;
      }
      return user[0];
    } catch (error) {
      console.error("Error in getUserByMobile:", error);
      throw error;
    }
  },

  // New method to get multiple users by mobile numbers
  getMultipleUsers: async (mobile_numbers) => {
    try {
      // Clean numbers: remove +91 if present
      const cleanMobileNumbers = mobile_numbers.map((number) => {
        number = number.toString().trim();
        return number.startsWith("+91") ? number.slice(3) : number; // remove +91 if present
      });
  
      // Create placeholders for the IN clause
      const placeholders = cleanMobileNumbers.map(() => "?").join(",");
      const query = `SELECT * FROM users WHERE mobile_number IN (${placeholders})`;
  
      const users = await dbQuery.queryRunner(query, cleanMobileNumbers);
      return users;
    } catch (error) {
      console.error("Error in getMultipleUsers:", error);
      throw error;
    }
  },
  

  getOrdersByUserId: async (user_id) => {
    try {
      const query = `SELECT * FROM orders WHERE user_id = ?`;
      const orders = await dbQuery.queryRunner(query, [user_id]);
      return orders;
    } catch (error) {
      console.error("Error in getOrdersByUserId:", error);
      throw error;
    }
  },
  
  getTransactionsByUserId: async (user_id) => {
    try {
      const query = `SELECT * FROM transactions WHERE user_id = ?`;
      const transactions = await dbQuery.queryRunner(query, [user_id]);
      return transactions;
    } catch (error) {
      console.error("Error in getTransactionsByUserId:", error);
      throw error;
    }
  },

  // Service
  getUserByPhoneNumber: async (mobile_number) => {
    try {
      const query = `SELECT id FROM users WHERE mobile_number = ? OR mobile_number = ?`;
      const users = await dbQuery.queryRunner(query, [mobile_number, `+91${mobile_number}`]);
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      console.error("Error in getUserByPhoneNumber:", error);
      throw error;
    }
  },

  // content premium
  upgradeContentPremium: async ({
    payment_status,
    user_id,
    plan_id,
    order_id,
    payment_id,
    coupon_code,
  }) => {
    try {
      if (payment_status === "success") {
        // Validate user exists
        const userQuery = `SELECT id, content_creator_plan_id FROM users WHERE id = '${user_id}' LIMIT 1`;
        const user = await dbQuery.queryRunner(userQuery);
        if (!user.length) {
          return { status: false, statusCode: 400, message: "Invalid user" };
        }

        // Validate plan exists
        const planQuery = `SELECT actual_price AS amount, duration_in_months FROM content_creator_plans WHERE subscription_id = '${plan_id}' LIMIT 1`;
        const plan = await dbQuery.queryRunner(planQuery);
        if (!plan.length) {
          return { status: false, statusCode: 400, message: "Invalid plan" };
        }

        // Validate transaction
        const transactionQuery = `SELECT status, payment_id, order_id FROM transactions WHERE payment_id = '${payment_id}' AND order_id = '${order_id}'`;
        const transaction = await dbQuery.queryRunner(transactionQuery);

        if (
          transaction.length === 0 ||
          transaction[0].status !== "success" ||
          transaction[0].payment_id !== payment_id ||
          transaction[0].order_id !== order_id
        ) {
          return {
            status: false,
            statusCode: 400,
            message: "Payment verification failed. Try again later.",
          };
        }

        // Calculate amount with discount if coupon_code is provided
        const amount = coupon_code 
          ? await calculateDiscountedAmount(plan[0].amount, coupon_code)
          : plan[0].amount;

        // Check for active content premium plans
        const activePlanQuery = `
          SELECT id, end_time
          FROM user_content_premium_plans
          WHERE user_id = '${user_id}' AND status = 'active' AND is_active = 1
          LIMIT 1
        `;
        const activePlan = await dbQuery.queryRunner(activePlanQuery);

        const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

        if (activePlan.length) {
          // Queue the new plan
          const start_time = moment(activePlan[0].end_time)
            .add(1, "second")
            .format("YYYY-MM-DD HH:mm:ss");
          const end_time = moment(start_time)
            .add(plan[0].duration_in_months, "months")
            .format("YYYY-MM-DD HH:mm:ss");

          const insertPlanQuery = `
            INSERT INTO user_content_premium_plans (
              user_id, plan_id, amount, start_time, end_time, status, is_active, created_at, order_id, payment_id
            ) VALUES ('${user_id}', '${plan_id}', '${amount}', '${start_time}', '${end_time}', 'queued', 0, '${currentTime}', '${order_id}', '${payment_id}')
          `;
          await dbQuery.queryRunner(insertPlanQuery);

          return {
            status: true,
            statusCode: 200,
            message: "Content premium plan queued successfully",
            is_premium: true,
          };
        } else {
          // No active plan, activate immediately
          const start_time = currentTime;
          const end_time = moment()
            .add(plan[0].duration_in_months, "months")
            .utcOffset(330)
            .format("YYYY-MM-DD HH:mm:ss");

          const insertPlanQuery = `
            INSERT INTO user_content_premium_plans (
              user_id, plan_id, amount, start_time, end_time, status, is_active, created_at, order_id, payment_id
            ) VALUES ('${user_id}', '${plan_id}', '${amount}', '${start_time}', '${end_time}', 'active', 1, '${currentTime}', '${order_id}', '${payment_id}')
          `;
          await dbQuery.queryRunner(insertPlanQuery);

          // Update users table
          const updateUserQuery = `UPDATE users SET content_creator_plan_id = '${plan_id}' WHERE id = '${user_id}'`;
          await dbQuery.queryRunner(updateUserQuery);

          return {
            status: true,
            statusCode: 200,
            message: "Content premium plan activated successfully",
            is_premium: true,
          };
        }
      } else {
        return {
          status: false,
          statusCode: 400,
          message: "Payment was not successful",
          is_premium: false,
        };
      }
    } catch (error) {
      console.error("Error in upgradeContentPremium service:", error);
      return {
        status: false,
        statusCode: 400,
        message: "Internal Server Error",
        is_premium: false,
      };
    }
  },

  getContentPremiumPlans: async (user_id) => {
    try {
      const query = `
        SELECT 
          ucpp.id,
          ucpp.user_id,
          ucpp.plan_id,
          ucpp.amount,
          ucpp.start_time,
          ucpp.end_time,
          ucpp.is_active,
          ucpp.status,
          ucpp.created_at,
          ucpp.order_id,
          ucpp.payment_id,
          ccp.plan_name,
          ccp.duration_in_months,
          ccp.actual_price
        FROM user_content_premium_plans ucpp
        LEFT JOIN content_creator_plans ccp ON ucpp.plan_id = ccp.subscription_id
        WHERE ucpp.user_id = ?
      `;
      const plans = await dbQuery.queryRunner(query, [user_id]);

      if (!plans.length) {
        return {
          statusCode: 200,
          message: "No content premium plans found for this user",
          data: [],
        };
      }

      return {
        statusCode: 200,
        message: "Content premium plans retrieved successfully",
        data: plans,
      };
    } catch (error) {
      console.error("Error in getContentPremiumPlans service:", error);
      throw error;
    }
  },  

  updateUserActivity :  async (userId) => {
    try {
      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
      const query = `
        INSERT INTO user_activity (user_id, last_active, updated_at)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE last_active = ?, updated_at = ?
      `;
      const params = [userId, currentTime, currentTime, currentTime, currentTime];
      await dbQuery.queryRunner(query, params);
      return { status: true, message: "User activity updated" };
    } catch (err) {
      throw new Error(`Failed to update user activity: ${err.message}`);
    }
  },

// Single user
getUserNameById  : async (id) => {
  try {
    const query = 'SELECT name FROM users WHERE id = ?';
    const rows = await dbQuery.queryRunner(query, [id]);
    return rows.length ? rows[0].name : null;
  } catch (err) {
    console.error('Error in getUserNameById:', err);
    throw err;
  }
},

// Get multiple users' names by IDs
getUserNamesByIds : async (ids) => {
  try {
    if (!Array.isArray(ids) || ids.length === 0) return {};

    const placeholders = ids.map(() => '?').join(',');
    const query = `SELECT id, name FROM users WHERE id IN (${placeholders})`;

    const rows = await dbQuery.queryRunner(query, ids);

    const result = {};
    rows.forEach(row => {
      result[row.id] = row.name;
    });

    return result;
  } catch (err) {
    console.error('Error in getUserNamesByIds:', err);
    throw err;
  }

},

// Get unread message count for a user
getUnreadMessageCount: async (userId) => {
  try {
    const query = `
      SELECT COUNT(*) as count 
      FROM user_chat 
      WHERE receiver = ? AND is_seen = 0
    `;
    
    const result = await queryRunner(query, [userId]);
    
    if (result && result.length > 0) {
      return {
        status: 200,
        message: 'Unread count fetched successfully',
        data: {
          unreadCount: parseInt(result[0].count) || 0
        }
      };
    } else {
      return {
        status: 200,
        message: 'No unread messages found',
        data: {
          unreadCount: 0
        }
      };
    }
  } catch (error) {
    console.error('Error getting unread message count:', error);
    return {
      status: 500,
      message: 'Failed to get unread message count',
      error: error.message
    };
  }
},

// Mark all messages as read for a specific conversation
markMessagesAsRead: async (userId, chattingUserId) => {
  try {
    const query = `
      UPDATE user_chat 
      SET is_seen = 1 
      WHERE receiver = ? AND sender = ? AND is_seen = 0
    `;
    
    const result = await queryRunner(query, [userId, chattingUserId]);
    
    return {
      status: 200,
      message: 'Messages marked as read successfully',
      data: {
        updatedRows: result.affectedRows || 0
      }
    };
  } catch (error) {
    console.error('Error marking messages as read:', error);
    return {
      status: 500,
      message: 'Failed to mark messages as read',
      error: error.message
    };
  }
},



}

exports.getUser = (id) => {
  return new Promise((resolve, reject) => {
    return getUserById(id)
      .then((result) => {
        if (result && result.status == 200) {
          resolve(result);
        } else {
          reject(result);
        }
      })
      .catch((err) => {
        reject(err);
      });
  });
};

