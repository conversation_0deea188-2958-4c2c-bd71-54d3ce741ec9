let dbQuery = require("../dbConfig/queryRunner");
let _ = require("underscore");
const dateFormatObj = require("date-and-time");
const utils = require("../utils/utils");
const fcm = require("../utils/fcm");
const userService = require("./UsersService");
const utilsConstants = require("../utils/constants");
const util = require("../utils/utils");
const moment = require('moment-timezone'); // Ensure moment-timezone is installed: npm install moment-timezone


let getAdModels = () =>
  new Promise((resolve, reject) => {
    let sql = `select id,name,view_price as viewPrice, base_price as basePrice,discount,model_image as modelImage,media_type from admodels_master where is_active=1 and is_ad_model_master=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch ad models successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Ad models not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
let getAdForShortVideo = (userId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT admodels.*, company.*
FROM admodels
LEFT JOIN company ON admodels.company_id = company.id
WHERE admodels.ad_upload_filename IS NOT NULL
  AND admodels.is_active = 1
  AND admodels.ad_model_id = 2
ORDER BY RAND()
LIMIT 1;
`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch ad successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Fetch ad successfully.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getAdvModels = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,am.name,am.is_brand from admodels a 
    LEFT JOIN admodels_master am ON am.id = a.ad_model_id 
    where  a.createdby=${userId} 
    order by a.updateddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch ad successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Ad models not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getTargetProfession = () =>
  new Promise((resolve, reject) => {
    let sql = `select id,name from admodels_master where is_active=1 and is_target_profession=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch target profession successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Target profession not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getTargetAreas = () =>
  new Promise((resolve, reject) => {
    let sql = `select id,name from admodels_master where is_active=1 and is_target_areas=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch target areas successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Target areas not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getButtons = () =>
  new Promise((resolve, reject) => {
    let sql = `select id,name from admodels_master where is_active=1 and is_button_master=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch button master successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Button master not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getCompanyButton = () =>
  new Promise((resolve, reject) => {
    let sql = `select id,name from admodels_master where is_active=1 and is_company_button_master=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch button master successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Button master not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getAnimations = () =>
  new Promise((resolve, reject) => {
    let sql = `select id,name from admodels_master where is_active=1 and is_animation_master=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch animation master successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Animation master not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let saveFirstAdModel = (adModel) =>
  new Promise((resolve, reject) => {
    let targetGender = null,
      maritalStatus = null,
      targetProfessions = null,
      targetArea = null,
      modelTypeName;
    targetGender =
      adModel.targetGender == null
        ? null
        : `'${adModel.targetGender.toString()}'`;
    maritalStatus =
      adModel.maritalStatus == null
        ? null
        : `'${adModel.maritalStatus.toString()}'`;
    targetProfessions =
      adModel.targetProfessions == null
        ? null
        : `'${adModel.targetProfessions.toString()}'`;
    targetArea =
      adModel.targetArea == null ? null : `'${adModel.targetArea.toString()}'`;

    modelTypeName =
      adModel.modelTypeName == null ? "" : adModel.modelTypeName.toString();

    let sql = `INSERT INTO admodels (company_name, campaign_name, target_gender,marital_status,target_lower_age,
        target_upper_age,target_professions,target_area,adwatch_per_day,
        ad_perday_pay,ad_spend_per_day,company_id,ad_model_id,is_fisrtpage_save,
        is_active,createdby,createddate,ad_start_date,ad_end_date,ad_time,ad_end_time,ad_customer_target_per_day,modelTypeName)
     VALUES('${adModel.companyName}', '${
      adModel.campaignName
    }', ${targetGender},${maritalStatus},${adModel.targetLowerAge},
     ${adModel.targetUpperAge},${targetProfessions},${targetArea},${
      adModel.adwatchPerDay
    },${adModel.adPerdayPay},${adModel.adSpendPerDay},
     ${adModel.companyId},${adModel.adModelId}, 1,0,${adModel.createdby},NOW(),
     '${adModel.adStartDate}','${
      adModel.adEndDate ? adModel.adEndDate : null
    }','${adModel.adTime}',
     '${adModel.adEndTime ? adModel.adEndTime : null}',${
      adModel.adCustomerTargetPerDay
    },'${modelTypeName}')`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          adModel.id = result.insertId;
          adModel.firstPageSave = 1;
          resolve({
            status: 200,
            message: "Ad added successfully.",
            data: [adModel],
          });
        } else {
          reject({
            status: 400,
            message: "Admodel not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let saveSecondPageAdmodel = (adModel) =>
  new Promise((resolve, reject) => {
    let sql = `update admodels set ad_per_view_percentage=${
      adModel.adPerViewPercentage
    },ad_per_preview_percentage=${
      adModel.adPerPreviewPercentage
    },ad_comments_on=${adModel.adCommentsOn},ad_upload_filename='${
      adModel.adFile
    }',mediaType=${
      adModel.mediaType ? adModel.mediaType : null
    },ad_upload_original_filename='${adModel.adFile}', ad_animation_id=${
      adModel.adAnimationId
    },
    ad_button_text_id=${
      adModel.adButtonTextId
    },is_second_page_save=1,updateddate=NOW() where id = ${adModel.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          adModel.secondPageSave = 1;
          resolve({
            status: 200,
            message: "Admodel update successfully.",
            data: [adModel],
          });
        } else {
          reject({
            status: 400,
            message: "Admodel not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let saveCelebrationAddModels = (adModel, adModelFile) =>
  new Promise((resolve, reject) => {
    let imagesFilename = null,
      imagesoriginalname = null;
    if (adModelFile && adModelFile.adFile && adModelFile.adFile.length > 0) {
      imagesFilename = adModelFile.adFile.map((o) => o["filename"]).toString();
      imagesoriginalname = adModelFile.adFile
        .map((o) => o["originalname"])
        .toString();
    }
    imagesFilename = imagesFilename == null ? null : `'${imagesFilename}'`;
    imagesoriginalname =
      imagesoriginalname == null ? null : `'${imagesoriginalname}'`;

    modelTypeName =
      adModel.modelTypeName == null ? "" : adModel.modelTypeName.toString();

    let sql = `INSERT INTO admodels (campaign_name,ad_total_people,ad_model_id, ad_upload_filename,ad_upload_original_filename, 
        target_area,target_lower_age,target_upper_age,marital_status,target_gender, target_professions,
        ad_total, ad_coupon,ad_tax,is_active,createdby,createddate,updateddate,ad_start_date,ad_end_date,modelTypeName)
     VALUES('${adModel.campaignName}',${adModel.targetPeople},${
      adModel.AdModelId
    },${imagesFilename},${imagesoriginalname},'${adModel.targetArea}',
     ${adModel.targetLowerAge},${
      adModel.targetUpperAge
    }, '${adModel.maritalStatus.toString()}','${adModel.targetGender.toString()}',
     '${adModel.targetProfessions.toString()}',${adModel.adTotal},'${
      adModel.Coupon ? adModel.Coupon : null
    }',${adModel.AdTax}, 1,${adModel.createdby},NOW(),NOW(),
     '${adModel.adStartDateTime}','${
      adModel.adEndDateTime
    }','${modelTypeName}')`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Celebration added successfully.",
            data: [adModel],
          });
        } else {
          reject({
            status: 400,
            message: "Celebration not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

// let saveThirdPageAdmodel = (adModel, adModelFile) => new Promise((resolve, reject) => {
//     let sql = `update admodels set ad_image_alignment='${adModel.adImageAlignment}',ad_headline='${adModel.adHeadline}',ad_font_size=${adModel.adFontSize},ad_font_bold=${adModel.adFontBold},is_third_page_save=1,updateddate=NOW()
//     where id = ${adModel.id}`;
//     dbQuery.queryRunner(sql)
//         .then(result => {
//             if (result && result.length != 0) {
//                 adModel.thirdPageSave = 1;
//                 resolve({
//                     status: 200,
//                     message: "Admodel update successfully.",
//                     data: [adModel]
//                 });
//             } else {
//                 reject({
//                     status: 400,
//                     message: "Admodel not saved.",
//                     data: result
//                 });
//             }
//         })
//         .catch(err => {
//             let message = '';
//             if (err.message.includes('ER_DUP_ENTRY')) message = 'Ad already available.';
//             if (err.message.includes('ER_NO_REFERENCED_ROW_2')) message = 'Invalid referance Id.';
//             if (err.message.includes('UNKNOWN_CODE_PLEASE_REPORT')) message = 'Invalid referance Id.';
//             reject({
//                 status: 500,
//                 message: message != '' ? message : err.message,
//                 data: []
//             });
//         });
// });

let saveThirdPageAdmodel = (adModel) =>
  new Promise((resolve, reject) => {
    let sql = `update admodels set ad_description='${
      adModel.adDescription
    }',ad_website_link='${adModel.adWebsiteLink}', ad_website='${
      adModel.adWebsite
    }', ad_company_location='${adModel.adCompanyLocation}', ad_tax_number='${
      adModel.adTaxNumber
    }',ad_place_app='${adModel.adPlaceApp ? adModel.adPlaceApp : null}',
    ad_refferal='${adModel.adRefferal}',ad_payment_mode='${
      adModel.adPaymendMode
    }',ad_order_value='${adModel.adOrderValue}',pending_ad_balance='${
      adModel.adOrderValue
    }',ad_charges_value='${adModel.adChargesValue}',ad_tax='${
      adModel.adTax
    }',ad_total='${adModel.adTotal}',ad_coupon='${
      adModel.adCoupan
    }',is_third_page_save=1,is_active=1, updateddate=NOW() where id = ${
      adModel.id
    }`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          adModel.thirdPageSave = 1;
          fcm.sendNotificationToAll({
            title: `${utilsConstants.newAdPostTitle}`,
            body: `${utilsConstants.newAdPostSubtitle}`,
          });
          userService.saveNotifications({
            title: `${utilsConstants.newAdPostTitle}`,
            subtitle: `${utilsConstants.newAdPostSubtitle}`,
            image: `${utilsConstants.adtiplogo}`,
          });

          if (adModel.adRefferal) {
            var referalAmount = (adModel.adOrderValue * 10) / 100;
            dbQuery.queryRunner(
              `UPDATE users set referal_earnings=referal_earnings+${referalAmount} where id=${adModel.adRefferal}`
            );
            dbQuery.queryRunner(`INSERT INTO referal_details (referal_code,applied_by,paid_amount,referal_creator,time)
              VALUES("${adModel.adCoupan}",${adModel.userId},${adModel.adOrderValue},${adModel.adRefferal},NOW())`);
            dbQuery.queryRunner(`INSERT INTO referral_earnings_details (referral_creator,referral_code,ad_value,ad_id,time,earning)VALUES(
                ${adModel.adRefferal},"${adModel.adCoupan}",${adModel.adOrderValue},${adModel.id},NOW(),${referalAmount})`);
          }

          resolve({
            status: 200,
            message: "Admodel update successfully.",
            data: [adModel],
          });
        } else {
          reject({
            status: 400,
            message: "Admodel not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });
/** request demo model */
let saveRequestDemo = (rqModel) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO reqeuest_ad_demo (name_description, phone_no, emailId,start_date,start_time,createdby)
        VALUES('${rqModel.name_description}', 
            '${rqModel.phone_no}',
            '${rqModel.emailId}',
            '${rqModel.start_date}','${rqModel.start_time}',${rqModel.createdby})`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          rqModel.id = result.insertId;
          resolve({
            status: 200,
            message: "Demo request saved.",
            data: [rqModel],
          });
        } else {
          reject({
            status: 400,
            message: "Demo request saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Demo already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let validateCoupon = (adCoupon) =>
  new Promise((resolve, reject) => {
    let sql = `select id, coupon_code as couponCode,coupan_startdate as startDate,coupan_enddate as coupanEnddate, coupon_desc as couponDesc, coupon_discount as couponDiscount from coupon_master where isActive=1 and LOWER(coupon_code)='${
      adCoupon.couponCode != ""
        ? adCoupon.couponCode.toLowerCase()
        : adCoupon.couponCode
    }' and coupan_startdate <= '${
      adCoupon.coupanDate
    }' and coupan_enddate >=  '${adCoupon.coupanDate}'`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Valid Coupon.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Invalid coupon.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getCoupon = (req) =>
  new Promise((resolve, reject) => {
    let sql = `select * from coupon_master where isActive=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch Coupon master successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Coupon master not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getrequestdemoads = (req) =>
  new Promise((resolve, reject) => {
    let sql = `select * from reqeuest_ad_demo where createdBy = ${req.params.userid}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch reqeuest_ad_demo successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "reqeuest_ad_demo not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getFilterAds = (filterdata) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like from admodels a
    LEFT JOIN ad_details ad ON ad.ad_id = a.id and ad.user_id=${filterdata.userId}
    where 
    NOW() between a.ad_start_date and a.ad_end_date 
    and ${filterdata.age} between a.target_lower_age and 
    a.target_upper_age and a.is_active=1 
    and FIND_IN_SET(${filterdata.gender}, a.target_gender)`;
    if (filterdata.modelType)
      sql += ` and a.ad_model_id=${filterdata.modelType} `;
    if (filterdata.targetLocation)
      sql += ` and FIND_IN_SET(${filterdata.targetLocation}, a.target_area) `;
    if (filterdata.targetProfession)
      sql += ` and FIND_IN_SET(${filterdata.targetProfession}, a.target_professions) `;
    if (filterdata.maritalStatus)
      sql += ` and FIND_IN_SET(${filterdata.maritalStatus}, a.marital_status) `;

    sql += ` and a.is_third_page_save=1 order by a.updateddate desc limit ${
      filterdata.limit ? filterdata.limit : 10
    };`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getMasterFilterAds = (filterdata) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,a.ad_upload_filename as adUrl,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like from admodels a
    LEFT JOIN ad_details ad ON ad.ad_id = a.id
    where ad.user_id=${filterdata.userId} AND
    NOW() between a.ad_start_date and a.ad_end_date 
    and ${filterdata.age} between a.target_lower_age and 
    a.target_upper_age and a.is_active=1 
    and FIND_IN_SET(${filterdata.gender}, a.target_gender)`;
    if (filterdata.modelType)
      sql += ` and a.ad_model_id=${filterdata.modelType} `;
    if (filterdata.targetLocation)
      sql += ` and FIND_IN_SET(${filterdata.targetLocation}, a.target_area) `;
    if (filterdata.targetProfession)
      sql += ` and FIND_IN_SET(${filterdata.targetProfession}, a.target_professions) `;
    if (filterdata.maritalStatus)
      sql += ` and FIND_IN_SET(${filterdata.maritalStatus}, a.marital_status) `;

    sql += ` and a.is_third_page_save=1 order by a.updateddate desc limit ${
      filterdata.limit ? filterdata.limit : 5000
    };`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getMasterAdsPagination = (page) =>
  new Promise((resolve, reject) => {
    let offset = (page.page - 1) * 10;
    let sql = `select a.*,a.ad_upload_filename as adUrl,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like, adm.is_brand  from admodels a
    LEFT JOIN ad_details ad ON ad.ad_id = a.id
    LEFT JOIN admodels_master adm ON a.ad_model_id = adm.id
    where NOW() between a.ad_start_date and a.ad_end_date`;
    if (page.modelType) sql += ` and a.ad_model_id=${page.modelType} `;
    sql += ` and a.is_third_page_save=1 and a.pending_ad_balance>0 and a.is_active=1 order by a.updateddate desc limit 10 OFFSET ${offset} ;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getCelebrationAds = (filterdata) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like from admodels a
    LEFT JOIN ad_details ad ON ad.ad_id = a.id and ad.user_id=${filterdata.userId}
    where 
    NOW() between a.ad_start_date and a.ad_end_date 
    and ${filterdata.age} between a.target_lower_age and 
    a.target_upper_age and a.is_active=1 and a.ad_model_id= 32
    and FIND_IN_SET(${filterdata.gender}, a.target_gender)`;
    // if(filterdata.modelType) sql += ` and a.ad_model_id=${filterdata.modelType} `;
    if (filterdata.targetLocation)
      sql += ` and FIND_IN_SET(${filterdata.targetLocation}, a.target_area) `;
    if (filterdata.targetProfession)
      sql += ` and FIND_IN_SET(${filterdata.targetProfession}, a.target_professions) `;
    if (filterdata.maritalStatus)
      sql += ` and FIND_IN_SET(${filterdata.maritalStatus}, a.marital_status) `;

    sql += ` order by ad.is_view asc, a.createddate desc limit ${
      filterdata.limit ? filterdata.limit : 5000
    };`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getFallowCompany = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select c.* from company c JOIN company_details cd ON c.id=cd.company_id where cd.is_follow=1 and cd.user_id=${userId} order by cd.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch followed company successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Comapny not followed.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getBuisnessAds = (filterdata) =>
  new Promise((resolve, reject) => {
    //and CONVERT_TZ(TIME(),'+00:00','+05:30') between a.ad_time and a.ad_end_time
    let sql = `select a.*,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like from admodels a
    LEFT JOIN ad_details ad ON ad.ad_id = a.id and ad.user_id=${filterdata.userId}
    where 
    NOW() between a.ad_start_date and a.ad_end_date     
    and ${filterdata.age} between a.target_lower_age and a.target_upper_age and a.is_active=1 and a.ad_model_id= 33
    and FIND_IN_SET(${filterdata.gender}, a.target_gender)`;
    //if(filterdata.modelType) sql += ` and a.ad_model_id=${filterdata.modelType} `;
    if (filterdata.targetLocation)
      sql += ` and FIND_IN_SET(${filterdata.targetLocation}, a.target_area) `;
    if (filterdata.targetProfession)
      sql += ` and FIND_IN_SET(${filterdata.targetProfession}, a.target_professions) `;
    if (filterdata.maritalStatus)
      sql += ` and FIND_IN_SET(${filterdata.maritalStatus}, a.marital_status) `;

    sql += ` order by ad.is_view asc, a.createddate desc limit ${
      filterdata.limit ? filterdata.limit : 5000
    };`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getAdHubsAds = (filterdata) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like, IFNULL(cd.is_follow,0) as is_follow, c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename from admodels a JOIN company c ON c.id=a.company_id
    LEFT JOIN ad_details ad ON ad.ad_id = a.id and ad.user_id=${filterdata.userId}
    LEFT JOIN company_details cd ON c.id=cd.company_id and cd.user_id=${filterdata.userId}
    where NOW() between a.ad_start_date and a.ad_end_date 
    and ${filterdata.age} between a.target_lower_age and 
    a.target_upper_age and a.is_active=1 
    and  a.ad_model_id NOT IN (32,33)
    and FIND_IN_SET(${filterdata.gender}, a.target_gender)`;
    //if(filterdata.modelType) sql += ` and a.ad_model_id=${filterdata.modelType} `;
    if (filterdata.targetLocation)
      sql += ` and FIND_IN_SET(${filterdata.targetLocation}, a.target_area) `;
    if (filterdata.targetProfession)
      sql += ` and FIND_IN_SET(${filterdata.targetProfession}, a.target_professions) `;
    if (filterdata.maritalStatus)
      sql += ` and FIND_IN_SET(${filterdata.maritalStatus}, a.marital_status) `;

    sql += ` order by ad.is_view asc, a.createddate desc limit ${
      filterdata.limit ? filterdata.limit : 5000
    };`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getAdViewsAndLike = (filterdata) =>
  new Promise((resolve, reject) => {
    let sql = `select ad_view as adView, ad_like as adLike from admodels where is_active=1 and createdby=${filterdata.userid} and id = ${filterdata.adid};`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let saveAdViewsAndLikes = (adModel) =>
  new Promise((resolve, reject) => {
    let sql = `update admodels set ad_view=${
      adModel.adView ? adModel.adView : 0
    },ad_like=${
      adModel.adLike ? adModel.adLike : 0
    },updateddate=NOW() where id = ${adModel.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Update Ad data successfully.",
            data: [adModel],
          });
        } else {
          reject({
            status: 400,
            message: "Data not update",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

const addAmountToWallet = (adModel) =>
  new Promise((resolve, reject) => {
    // Validate inputs
    if (!adModel.id || !adModel.id.startsWith("ca")) {
      reject({
        status: 400,
        message: "Invalid ad ID. Must start with 'ca'.",
        data: [],
      });
      return;
    }

    if (!adModel.amount || adModel.amount <= 0) {
      reject({
        status: 400,
        message: "Invalid amount. Must be greater than 0.",
        data: [],
      });
      return;
    }

    if (!adModel.userId || isNaN(adModel.userId)) {
      reject({
        status: 400,
        message: "Invalid user ID.",
        data: [],
      });
      return;
    }

    // Get IST time
    const istTime = moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss');

    // SQL queries
    const sqlUpsertEarnings = `
      INSERT INTO ads_earnings (userId, ad_earnings, updateddate) 
      VALUES (?, ?, ?) 
      ON DUPLICATE KEY UPDATE 
          ad_earnings = ad_earnings + ?, 
          updateddate = ?;
    `;
    const sqlUpdateWallet = `
      UPDATE wallet 
      SET totalBalance = totalBalance + ?, 
          updateddate = ? 
      WHERE id = (
          SELECT id FROM (
              SELECT id FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1
          ) AS latest
      );
    `;

    // Transaction function
    const runTransaction = () =>
      new Promise((resolve, reject) => {
        dbQuery
          .queryRunner("START TRANSACTION")
          .then(() =>
            Promise.all([
              dbQuery.queryRunner(sqlUpsertEarnings, [
                adModel.userId,
                adModel.amount,
                istTime,
                adModel.amount,
                istTime,
              ]).catch((err) => {
                if (err.code === 'ER_NO_SUCH_TABLE') {
                  console.error(`Table ads_earnings does not exist: ${err.message}`);
                  throw new Error('ads_earnings table missing');
                }
                throw err;
              }),
              dbQuery.queryRunner(sqlUpdateWallet, [adModel.amount, istTime, adModel.userId]),
            ])
          )
          .then(() => dbQuery.queryRunner("COMMIT"))
          .then(() => {
            console.log(
              `Transaction completed: User: ${adModel.userId}, Amount: ${adModel.amount}, Time: ${istTime}`
            );
            resolve({
              status: 200,
              message: "Ad earnings and wallet balance updated successfully.",
              data: {
                userId: adModel.userId,
                addedAmount: parseFloat(adModel.amount).toFixed(2),
                transactionDate: istTime,
              },
            });
          })
          .catch((err) => {
            console.error(`Transaction error: ${err.message}`);
            return dbQuery.queryRunner("ROLLBACK").then(() =>
              reject({
                status: 500,
                message: "Failed to update ad earnings or wallet balance: " + (err.message || "Unknown error"),
                data: [],
              })
            );
          });
      });

    // Execute transaction
    runTransaction()
      .then((result) => {
        console.log(`Final result: ${JSON.stringify(result)}`);
        resolve(result);
      })
      .catch((err) => {
        console.error(`Transaction failed: ${err.message}`);
        dbQuery.queryRunner("ROLLBACK").catch(() => {});
        reject({
          status: 500,
          message: "Failed to update ad earnings or wallet balance: " + (err.message || "Unknown error"),
          data: [],
        });
      });
  });

let saveAdViewAmount = (adModel) =>
  new Promise((resolve, reject) => {
    let sql = `SELECT * FROM admodels WHERE id = ${adModel.id}`;
    let columnCount = adModel.likeFlag == true ? "is_like" : "is_view";

    // let sql1 = `SELECT totalBalance FROM wallet where createdby= ${adModel.userId} and paid_status!='Unpaid' order by transaction_date desc limit 1;`;
    let sql1 = `SELECT totalBalance FROM wallet where createdby= ${adModel.userId}  order by id desc limit 1;`;
    let userLikeandViewAlready = `SELECT count(*) as viewCount FROM ad_details where ad_id= ${adModel.id} and user_id=${adModel.userId} AND ${columnCount}=1 AND cast(createddate as Date) = cast(now() as Date);`;
    // let sql2=`SELECT total_ads_display, total_ads_like, total_ads_view,total_earnings from channels where id=${adModel.channelId}`;

    const promises = [
      dbQuery.queryRunner(sql),
      dbQuery.queryRunner(sql1),
      dbQuery.queryRunner(userLikeandViewAlready),
    ];
    if (adModel.channelId) {
      let sql2 = `SELECT total_ads_display, total_ads_like, total_ads_view, total_earnings from channels where id=${adModel.channelId}`;
      promises.push(dbQuery.queryRunner(sql2));
    }
    Promise.all(promises)
      .then((result) => {
        if (result && result.length != 0) {
          let totalBalance = 0;
          let viewCount = 0;
          let totalViews = 0;
          let isAdFromChannelVideo = false;
          let totalChannelAdDisplay = 0;
          let totalChannelAdLike = 0;
          let totalChannelAdView = 0;
          let totalChannelChannelEarning = 0;
          let totalChannelToPay = 0;
          let total = 0;

          if (result[3] && result[3].length != 0) {
            isAdFromChannelVideo = true;
            totalChannelAdDisplay = result[3][0].total_ads_display;
            totalChannelAdLike = result[3][0].total_ads_like;
            totalChannelAdView = result[3][0].total_ads_view;
            totalChannelChannelEarning = result[3][0].total_earnings;
          }
          //// total amount to pay to user

          if (result[3]) {
            total =
              (result[0][0].ad_perday_pay / 100) *
              result[0][0].ad_per_preview_percentage *
              0.7;
          } else {
            total =
              (result[0][0].ad_perday_pay / 100) *
              result[0][0].ad_per_preview_percentage;
          }

          /// total amount to pay to content creator
          totalChannelToPay =
            (result[0][0].ad_perday_pay / 100) *
              result[0][0].ad_per_preview_percentage *
              0.3 +
            totalChannelChannelEarning;

          let pending_balance = result[0][0].pending_ad_balance - total;
          totalViews = result[0][0].ad_view ?? 0;
          console.log("pending balance ", pending_balance);

          if (result[1].length != 0 && result[1][0].length != 0)
            totalBalance = result[1][0].totalBalance;
          let finalAmount = Math.round(total + totalBalance).toFixed(2);
          if (result[2].length != 0 && result[2][0].length != 0) {
            viewCount = result[2][0].viewCount;
          }
          adModel.totalAmount = total;
          if (pending_balance <= 0) {
            console.log("pending balance 2", pending_balance);
            dbQuery
              .queryRunner(
                `UPDATE admodels set is_active=0 WHERE id=${adModel.id}`
              )
              .then((result) => {
                console.log("updated active");
                reject({
                  status: 400,
                  message: "Amount not added",
                  data: result,
                });
              })
              .catch((err) => {
                reject({
                  status: 400,
                  message: "Amount not added",
                  data: [],
                });
              });
            return;
          }
          if (viewCount >= 1) {
            reject({
              status: 400,
              message: "User already watched ad earlier.",
              data: result,
            });
          } else {
            dbQuery.queryRunner(
              `INSERT INTO ad_details (ad_id,user_id,is_view,ad_view_payment,createddate,is_view_createddate) values (${adModel.id},${adModel.userId},1,${total},NOW(),NOW()) ON DUPLICATE KEY UPDATE is_view = 1, ad_view_payment = ${total},is_view_createddate=NOW();`
            );
            dbQuery.queryRunner(
              `INSERT INTO wallet (totalBalance, transaction_status, amount,transaction_date,createdby, createddate) VALUES (${finalAmount},  4, ${total},NOW(),${adModel.userId},NOW());`
            );
            dbQuery.queryRunner(
              `Update admodels set ad_view=${
                totalViews + 1
              },pending_ad_balance=${pending_balance} WHERE id = ${adModel.id}`
            );
            if (isAdFromChannelVideo) {
              dbQuery.queryRunner(
                `Update channels set total_ads_display=${
                  totalChannelAdDisplay + 1
                }, total_ads_view=${
                  totalChannelAdView + 1
                }, total_earnings=${totalChannelToPay} where id=${
                  adModel.channelId
                }`
              );
            }

            resolve({
              status: 200,
              message: "Amount added successfully.",
              data: [adModel],
            });
          }
        } else {
          reject({
            status: 400,
            message: "Amount not added",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let saveAdLikeAmount = (adModel) =>
  new Promise((resolve, reject) => {
    let sql = `SELECT * FROM admodels WHERE id = ${adModel.id}`;
    //  let sql1 = `SELECT totalBalance FROM wallet where createdby= ${adModel.userId} AND paid_status!='Unpaid' order by transaction_date desc limit 1;`;
    let sql1 = `SELECT totalBalance FROM wallet where createdby= ${adModel.userId}  order by id desc limit 1;`;

    let columnCount = adModel.likeFlag == true ? "is_like" : "is_view";
    let userLikeandViewAlready = `SELECT count(*) as likeCount FROM ad_details where ad_id= ${adModel.id} and user_id=${adModel.userId} AND ${columnCount}=1 AND cast(createddate as Date) = cast(now() as Date);`;
    let AllUserLikeToday = `SELECT count(*) as likeCount FROM ad_details where ad_id= ${adModel.id} AND ${columnCount}=1 and cast(createddate as Date) = cast(now() as Date);`;
    let recordExist = `SELECT count(*) as likeCount FROM ad_details where ad_id= ${adModel.id} and user_id=${adModel.userId} AND cast(createddate as Date) = cast(now() as Date);`;
    const promises = [
      dbQuery.queryRunner(sql),
      dbQuery.queryRunner(sql1),
      dbQuery.queryRunner(AllUserLikeToday),
      dbQuery.queryRunner(userLikeandViewAlready),
      dbQuery.queryRunner(recordExist),
    ];

    if (adModel.channelId) {
      let sql2 = `SELECT total_ads_display, total_ads_like, total_ads_view, total_earnings from channels where id=${adModel.channelId}`;
      promises.push(dbQuery.queryRunner(sql2));
    }

    Promise.all(promises)
      .then((result) => {
        if (result && result.length != 0) {
          let totalBalance = 0,
            total = 0,
            totalLikes = 0,
            totalViews = 0,
            customerTargetPerDay = 0,
            customerTargetPerDayDone = 0,
            likePerUser = 0,
            userLikeDone = 0;
          pending_balance = 0;
          let recordExistCount = 0;

          //for channel
          let isAdFromChannelVideo = false;
          let totalChannelAdDisplay = 0;
          let totalChannelAdLike = 0;
          let totalChannelAdView = 0;
          let totalChannelEarning = 0;
          let totalChannelToPay = 0;

          if (result[5] && result[5].length != 0) {
            isAdFromChannelVideo = true;
            totalChannelAdDisplay = result[5][0].total_ads_display;
            totalChannelAdLike = result[5][0].total_ads_like;
            totalChannelAdView = result[5][0].total_ads_view;
            totalChannelEarning = result[5][0].total_earnings;
            console.log("channel details", result[5][0]);
          }

          if (result[0].length != 0 && result[0][0].length != 0) {
            if (result[5]) {
              total =
                (result[0][0].ad_perday_pay / 100) *
                result[0][0].ad_per_view_percentage *
                0.7;
            } else {
              total =
                (result[0][0].ad_perday_pay / 100) *
                result[0][0].ad_per_view_percentage;
            }
            totalChannelToPay =
              (result[0][0].ad_perday_pay / 100) *
                result[0][0].ad_per_view_percentage *
                0.3 +
              totalChannelEarning;

            pending_balance = result[0][0].pending_ad_balance - total;
            totalLikes = result[0][0].ad_like ?? 0;
            totalViews = result[0][0].ad_view ?? 0;
            console.log("total view", totalLikes);
            if (pending_balance <= 0) {
              dbQuery
                .queryRunner(
                  `Update admodels set is_active=0 WHERE id = ${adModel.id}`
                )
                .then((result) => {
                  reject({
                    status: 400,
                    message: "Amount not added",
                    data: result,
                  });
                })
                .catch((err) => {
                  reject({
                    status: 400,
                    message: "Amount not added",
                    data: [],
                  });
                });
              return;
            }

            customerTargetPerDay = result[0][0].ad_customer_target_per_day;
            likePerUser = 1;
          }
          if (result[1].length != 0 && result[1][0].length != 0)
            totalBalance = result[1][0].totalBalance;
          if (result[2].length != 0 && result[2][0].length != 0)
            customerTargetPerDayDone = result[2][0].likeCount;
          if (result[3].length != 0 && result[3][0].length != 0)
            userLikeDone = result[3][0].likeCount;
          if (result[3].length != 0 && result[3][0].length != 0)
            recordExistCount = result[4][0].likeCount;

          if (
            userLikeDone >= likePerUser ||
            customerTargetPerDayDone >= customerTargetPerDay
          ) {
            adModel.customerTargetPerDay = customerTargetPerDayDone;
            adModel.totalLikes = userLikeDone;
            return resolve({
              status: 200,
              message: "Customer per day target already completed.",
              data: [adModel],
            });
          } else {
            let totalAmount = total.toFixed(2);
            let finalAmount = (total + totalBalance).toFixed(2);
            let admodelUpdateColumn;
            if (adModel.likeFlag == true) {
              if (recordExistCount == 1) {
                dbQuery.queryRunner(
                  `UPDATE ad_details set is_like=1,is_like_createddate=NOW() WHERE ad_id= ${adModel.id} and user_id=${adModel.userId} AND cast(createddate as Date) = cast(now() as Date)`
                );
              } else {
                dbQuery.queryRunner(
                  `INSERT INTO ad_details (ad_id,user_id,is_like,ad_like_payment,createddate,is_like_createddate) values (${adModel.id},${adModel.userId},1,${totalAmount},NOW(),NOW()) ON DUPLICATE KEY UPDATE is_like = 1, ad_like_payment = ${totalAmount},is_like_createddate=NOW();`
                );
              }
              admodelUpdateColumn = "ad_like";
            } else {
              if (recordExistCount == 1) {
                dbQuery.queryRunner(
                  `UPDATE ad_details set is_view=1,is_view_createddate=NOW() WHERE ad_id= ${adModel.id} and user_id=${adModel.userId} AND cast(createddate as Date) = cast(now() as Date)`
                );
              } else {
                dbQuery.queryRunner(
                  `INSERT INTO ad_details (ad_id,user_id,is_view,ad_like_payment,createddate,is_view_createddate) values (${adModel.id},${adModel.userId},1,${totalAmount},NOW(),NOW()) ON DUPLICATE KEY UPDATE is_view = 1, ad_like_payment = ${totalAmount},is_view_createddate=NOW();`
                );
              }
              admodelUpdateColumn = "ad_view";
            }
            dbQuery.queryRunner(
              `INSERT INTO wallet (totalBalance, transaction_status, amount,transaction_date,createdby, createddate) VALUES (${finalAmount},5, ${totalAmount},NOW(),${adModel.userId},NOW());`
            );
            dbQuery.queryRunner(
              `Update admodels set ad_like=${
                totalLikes + 1
              },pending_ad_balance=${pending_balance} WHERE id = ${adModel.id}`
            );
            if (isAdFromChannelVideo) {
              dbQuery.queryRunner(
                `Update channels set total_ads_display=${
                  totalChannelAdDisplay + 1
                }, total_ads_like=${
                  totalChannelAdLike + 1
                }, total_earnings=${totalChannelToPay} where id=${
                  adModel.channelId
                }`
              );
            }

            adModel.totalAmount = totalAmount;
            resolve({
              status: 200,
              message: "Amount added successfully.",
              data: [adModel],
            });
          }
        } else {
          reject({
            status: 400,
            message: "Amount not added",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let saveFallowCompany = (companyModel) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO company_details(company_id,user_id,is_follow,createddate,company_follow_date)
    values (${companyModel.companyId},${companyModel.userId},${companyModel.isFollow},NOW(),NOW()) ON DUPLICATE KEY UPDATE is_follow = ${companyModel.isFollow},company_follow_date=NOW();`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Company followed details added successfully.",
            data: [companyModel],
          });
        } else {
          reject({
            status: 400,
            message: "Company followed details not added",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getMyLikesAds = (userId, page) =>
  new Promise((resolve, reject) => {
    var offset = page * 20;
    let sql = `SELECT 
DISTINCT
    ad.ad_id,
  
    a.company_id,
    a.company_name,
    a.createdby,
    a.ad_description
FROM 
    ad_details ad
LEFT JOIN 
    admodels a ON ad.ad_id = a.id
WHERE 
    ad.user_id = ${userId}
ORDER BY 
    a.createddate DESC 
LIMIT 20 OFFSET ${offset}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let saveBussinessAdModel = (adModel, adModelFile) =>
  new Promise((resolve, reject) => {
    let imagesFilename = null,
      imagesoriginalname = null;
    if (adModelFile && adModelFile.adFile && adModelFile.adFile.length > 0) {
      imagesFilename = adModelFile.adFile.map((o) => o["filename"]).toString();
      imagesoriginalname = adModelFile.adFile
        .map((o) => o["originalname"])
        .toString();
    }
    imagesFilename = imagesFilename == null ? null : `'${imagesFilename}'`;
    imagesoriginalname =
      imagesoriginalname == null ? null : `'${imagesoriginalname}'`;

    modelTypeName =
      adModel.modelTypeName == null ? "" : adModel.modelTypeName.toString();

    let sql = `INSERT INTO admodels (company_name,company_id,ad_model_id, campaign_name,ad_upload_filename,ad_upload_original_filename, 
        target_area,target_lower_age,target_upper_age,marital_status,target_gender, target_professions,ad_per_view_percentage,ad_per_preview_percentage,
        ad_total_people, ad_total, ad_coupon,ad_tax,is_active,createdby,createddate,updateddate,ad_start_date,ad_end_date,modelTypeName)
     VALUES('${adModel.companyName ? adModel.companyName : ""}','${
      adModel.companyId
    }',${adModel.AdModelId},'${
      adModel.campaignName
    }',${imagesFilename},${imagesoriginalname},
     '${adModel.targetArea}', ${adModel.targetLowerAge},${
      adModel.targetUpperAge
    }, '${adModel.maritalStatus.toString()}',
     '${adModel.targetGender.toString()}',
     '${adModel.targetProfessions.toString()}','${
      adModel.adPerViewPercentage
    }','${adModel.adPerPreviewPercentage}',
     ${adModel.adTotalPeople},${adModel.adTotal},'${
      adModel.Coupon ? adModel.Coupon : null
    }',${adModel.AdTax}, 1,${adModel.createdby},NOW(),NOW(),
     '${adModel.adStartDateTime}','${
      adModel.adEndDateTime
    }','${modelTypeName}')`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Bussiness ad model added successfully.",
            data: [adModel],
          });
        } else {
          reject({
            status: 400,
            message: "Bussiness ad model not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let saveCelebrationAdView = (adModel) =>
  new Promise((resolve, reject) => {
    let sql = `SELECT id FROM admodels WHERE id = ${adModel.adId} and ad_model_id=32`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          dbQuery.queryRunner(
            `INSERT INTO ad_details (ad_id,user_id,is_view,createddate,is_view_createddate)values (${adModel.adId},${adModel.userId},1,NOW(),NOW()) ON DUPLICATE KEY UPDATE is_view = 1, is_view_createddate=NOW();`
          );
          resolve({
            status: 200,
            message: "Celebration ad view successfully.",
            data: [adModel],
          });
        } else {
          resolve({
            status: 200,
            message: "Celebration ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getLossViewAmount = (adId) =>
  new Promise((resolve, reject) => {
    let adModel = {};
    let sql = `SELECT a.ad_per_view_percentage, am.base_price, a.ad_customer_target_per_day FROM admodels a JOIN admodels_master am ON a.ad_model_id=am.id WHERE a.id = ${adId}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let total = result[0].base_price / result[0].ad_per_view_percentage;
          adModel.adId = adId;
          adModel.totalAmount = total;
          resolve({
            status: 200,
            message: "Fetch total amount loss.",
            data: [adModel],
          });
        } else {
          reject({
            status: 400,
            message: "Ad not found",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getLastAdDetails = (userId, companyId) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like, IFNULL(cd.is_follow,0) as is_follow, c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename from admodels a JOIN company c ON c.id=a.company_id
    LEFT JOIN ad_details ad ON ad.ad_id = a.id and ad.user_id=${userId}
    LEFT JOIN company_details cd ON c.id=cd.company_id and cd.user_id=${userId}
    where a.is_active=1 and a.company_id=${companyId} order by a.createddate desc limit 1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getAdDetailsforVideo = (adId) =>
  new Promise((resolve, reject) => {
    let sql = `select  a.company_name, a.campaign_name ,a.ad_upload_filename,a.mediaType,a.ad_upload_original_filename , c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename 
    from admodels a 
    LEFT JOIN company c ON c.id=a.company_id
    where a.is_active=1 and a.id=${adId} order by a.createddate desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });
let getAdDetailsForQr = (adId) => {
  let sql = `SELECT 
  admodels.*, 
  admodels.id as ad_id,
  company.id as company_id, 
  company.*,
  adm.is_brand
FROM 
  admodels 
LEFT JOIN 
  company 
ON 
  admodels.company_id = company.id 
  LEFT JOIN admodels_master adm ON admodels.ad_model_id=adm.id
WHERE 
  admodels.id = ${adId};`;
  return new Promise((resolve, reject) => {
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });
};

let getAdDetails = (userId, adId) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like, IFNULL(cd.is_follow,0) as is_follow, c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename from admodels a JOIN company c ON c.id=a.company_id
    LEFT JOIN ad_details ad ON ad.ad_id = a.id and ad.user_id=${userId}
    LEFT JOIN company_details cd ON c.id=cd.company_id and cd.user_id=${userId}
    where a.is_active=1 and a.id=${adId} order by a.createddate desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getOrderTracking = (adid) =>
  new Promise((resolve, reject) => {
    let sql = `select a.id, a.company_name, a.campaign_name, 
    a.ad_total_people, a.ad_customer_target_per_day, 
    a.adwatch_per_day, a.ad_start_date, a.ad_end_date, 
    a.ad_upload_filename, a.mediaType, a.ad_upload_original_filename,
     a.ad_description, a.ad_order_value, a.ad_total, 
     a.ad_spend_per_day, a.ad_model_id, a.createdby, 
     a.is_active, a.adPauseCountinue, a.createddate,
     a.pending_ad_balance,
     a.ad_view,
     a.ad_like,
     a.ad_perday_pay,a.ad_per_preview_percentage,a.ad_per_view_percentage, 
     a. updateddate, a.ad_pause_status_date, a.modelTypeName
    from admodels a where a.id=${adid}`;

    let isViewQuery = `SELECT count(is_view) as viewcount FROM ad_details WHERE ad_id=${adid}`;
    let isLikeQuery = `SELECT count(is_like) as likecount FROM ad_details WHERE ad_id=${adid}`;
    let current_date = new Date();
    Promise.all([
      dbQuery.queryRunner(sql),
      dbQuery.queryRunner(isViewQuery),
      dbQuery.queryRunner(isLikeQuery),
    ])
      .then((result) => {
        if (result && result.length != 0) {
          console.log("ad start date", result);
          let resultData = result[0];
          let totalReachPending = 0;
          let orderSuccessfull = false;

          let viewCount = 0;
          let likeCount = 0;
          result[1].forEach((record) => {
            viewCount = record.viewcount;
          });
          result[2].forEach((record) => {
            likeCount = record.likecount;
          });
          var totalDays = utils.calculateDaysBetweenDates(
            result[0][0].ad_start_date,
            result[0][0].ad_end_date
          );
          totalReachPending =
            totalDays * result[0][0].ad_customer_target_per_day;

          var totalDaysRemaining = utils.calculateNoOfDaysRemaining(
            result[0][0].ad_start_date,
            result[0][0].ad_end_date
          );
          if (
            result[0][0].pending_ad_balance <= 0 ||
            result[0][0].is_active === 0 ||
            totalReachPending - viewCount <= 0
          ) {
            orderSuccessfull = true;
          }

          resultData.forEach((record) => {
            record.adOrdered = record.createddate;
            record.adminApproval = true;
            record.campaingStartNow =
              record.ad_start_date < current_date ? true : false;
            record.reachSuccessfull = viewCount;
            record.reachPending =
              totalReachPending - viewCount <= 0
                ? 0
                : totalReachPending - viewCount;
            record.orderSuccessfull = orderSuccessfull;
            // record.totalViews = viewCount;
            // record.totalLikes = likeCount;
            record.totalViews = record.ad_view ?? 0;
            record.totalLikes = record.ad_like ?? 0;
            record.adPerViewCost =
              (record.ad_perday_pay * record.ad_per_preview_percentage) / 100;
            record.adPerLikeCost =
              (record.ad_perday_pay * record.ad_per_view_percentage) / 100;
            record.totalDays = totalDays;
            record.noOfDaysRemaining =
              totalDaysRemaining <= 0 ? 0 : totalDaysRemaining;
            record.noOfDaysCompleted = totalDays - totalDaysRemaining;
            record.totalAvailableBalance = record.pending_ad_balance;
            // record.totalAvailableBalance = (
            //   record.ad_order_value -
            //   (likeCount *
            //     record.ad_perday_pay *
            //     record.ad_per_view_percentage) /
            //     100 -
            //   (viewCount *
            //     record.ad_perday_pay *
            //     record.ad_per_preview_percentage) /
            //     100
            // ).toFixed(2);
          });

          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: resultData,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        console.log("", err);
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getUserLikeAds = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like, IFNULL(cd.is_follow,0) as is_follow,IFNULL(cd.is_block,0) as is_block, c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename from admodels a JOIN company c ON c.id=a.company_id
    LEFT JOIN ad_details ad ON ad.ad_id = a.id and ad.user_id=${userId}
    LEFT JOIN company_details cd ON c.id=cd.company_id and cd.user_id=${userId}
    where a.is_active=1 and ad.is_like=1 order by a.createddate desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getLikeAdsbyCompanyUserId = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,u.id as likeUserId,u.name as likeUserName,u.firstName as 
    likefirstName,u.lastName as likeLastName,
    u.profession as likeUserProfession,
    u.profile_image as userLikeProfileImage, c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename from ad_details  ad
    INNER JOIN admodels a on a.id=ad.ad_id
    INNER JOIN users u on u.id=ad.user_id
   INNER JOIN company c ON c.id=a.company_id
  where ad.ad_id in (select id from admodels where createdby=${userId}) and ad.is_like=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getGraphData = (filterdata) =>
  new Promise((resolve, reject) => {
    let date = new Date();
    let sqlMethod = "DATE";
    let sqlDataKey = "xAxis";
    switch (filterdata.dateformat) {
      case "Today":
        filterdata.startDate = dateFormatObj.format(
          new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            0,
            0,
            0
          ),
          "YYYY-MM-DD HH:mm:ss"
        );
        filterdata.endDate = dateFormatObj.format(
          new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            23,
            59,
            0
          ),
          "YYYY-MM-DD HH:mm:ss"
        );
        sqlMethod = "HOUR";
        break;
      case "Yesterday":
        filterdata.startDate = new Date(
          date.getFullYear,
          date.getMonth,
          date.getDay - 1,
          0,
          0
        );
        filterdata.endDate = new Date(
          date.getFullYear,
          date.getMonth,
          date.getDay - 1,
          23,
          59
        );
        sqlMethod = "HOUR";
        break;
      case "Last Week":
        filterdata.startDate = dateFormatObj.format(
          new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate() - 7,
            0,
            0,
            0
          ),
          "YYYY-MM-DD HH:mm:ss"
        );
        filterdata.endDate = dateFormatObj.format(
          new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            23,
            59,
            0
          ),
          "YYYY-MM-DD HH:mm:ss"
        );
        sqlMethod = "DAY";
        break;
      case "Last 14 days":
        filterdata.startDate = dateFormatObj.format(
          new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate() - 14,
            0,
            0,
            0
          ),
          "YYYY-MM-DD HH:mm:ss"
        );
        filterdata.endDate = dateFormatObj.format(
          new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            23,
            59,
            0
          ),
          "YYYY-MM-DD HH:mm:ss"
        );
        sqlMethod = "DAY";
        break;
      case "Current Month":
        filterdata.startDate = dateFormatObj.format(
          new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0),
          "YYYY-MM-DD HH:mm:ss"
        );
        filterdata.endDate = dateFormatObj.format(
          new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            23,
            59,
            0
          ),
          "YYYY-MM-DD HH:mm:ss"
        );
        sqlMethod = "DAY";
        break;
      case "Current Year":
        filterdata.startDate = dateFormatObj.format(
          new Date(date.getFullYear(), 0, 1, 0, 0, 0),
          "YYYY-MM-DD HH:mm:ss"
        );
        filterdata.endDate = dateFormatObj.format(
          new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            23,
            59,
            0
          ),
          "YYYY-MM-DD HH:mm:ss"
        );
        sqlMethod = "MONTH";
        break;
    }

    let isLikeQuery = `select sum(is_like) as count,${sqlMethod}(is_like_createddate) as ${sqlDataKey} from ad_details where ad_id `;
    if (filterdata.isOverview == "1" && filterdata.campaignId != 0)
      isLikeQuery += `=${filterdata.campaignId}`;
    isLikeQuery += ` and is_like=1 and is_like_createddate BETWEEN '${filterdata.startDate}' AND '${filterdata.endDate}' GROUP BY ${sqlDataKey};`;

    let isViewQuery = `select sum(is_view) as count,${sqlMethod}(is_view_createddate) as ${sqlDataKey} from ad_details where ad_id `;
    if (filterdata.isOverview == "1" && filterdata.campaignId != 0)
      isViewQuery += `=${filterdata.campaignId}`;
    isViewQuery += ` and is_view=1 and is_view_createddate BETWEEN '${filterdata.startDate}' AND '${filterdata.endDate}' GROUP BY ${sqlDataKey};`;

    let isFollowQuery = `select SUM(is_follow) as count,${sqlMethod}(company_follow_date) as ${sqlDataKey} from company_details 
    where is_follow=1 and company_id in (select company_id from admodels where id=${filterdata.campaignId}) 
    and company_follow_date BETWEEN '${filterdata.startDate}' AND '${filterdata.endDate}' GROUP BY ${sqlDataKey};`;

    let isViewUserQuery = `Select * From users where id in ( select user_id from ad_details where ad_id =${filterdata.campaignId} and is_view=1 and is_view_createddate BETWEEN '${filterdata.startDate}' AND '${filterdata.endDate}')`;
    let isLikeUserQuery = `Select * From users where id in ( select user_id from ad_details where ad_id =${filterdata.campaignId} and is_like=1 and is_like_createddate BETWEEN '${filterdata.startDate}' AND '${filterdata.endDate}')`;

    let isFollowUserQuery = `Select * From users where id in(select user_id from company_details where is_follow=1 
    and company_id in (select company_id from admodels where id=${filterdata.campaignId})
    and company_follow_date BETWEEN '${filterdata.startDate}' AND '${filterdata.endDate}') `;

    Promise.all([
      dbQuery.queryRunner(isLikeQuery),
      dbQuery.queryRunner(isViewQuery),
      dbQuery.queryRunner(isFollowQuery),
      dbQuery.queryRunner(isLikeUserQuery),
      dbQuery.queryRunner(isViewUserQuery),
      dbQuery.queryRunner(isFollowUserQuery),
    ])
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Data fetch successfully.",
            data: {
              isLike: result[0],
              isView: result[1],
              isFollow: result[2],
              isLikeUserList: result[3],
              isViewUserList: result[4],
              isFollowUserList: result[5],
              dateformat: filterdata.dateformat,
            },
          });
        } else {
          resolve({
            status: 400,
            message: "Data not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getAdPassBook = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `
      select a.*,u.id as likeUserId,u.name as likeUserName,u.firstName as likefirstName,u.lastName as likeLastName,
      u.profession as likeUserProfession,
      u.profile_image as userLikeProfileImage, c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
      c.profileFilename as companyProfileFilename from ad_details  ad
      INNER JOIN admodels a on a.id=ad.ad_id
      INNER JOIN users u on u.id=ad.user_id
      INNER JOIN company c ON c.id=a.company_id
      where ad.ad_id in (select id from admodels where createdby=${userId}) and ad.is_like=1
    `;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let saveBlockad = (adBlockData) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO company_details(company_id,user_id,is_block,ad_block_date)
    values (${adBlockData.companyId},${adBlockData.userId},${adBlockData.isAdBlock},NOW()) ON DUPLICATE KEY UPDATE is_block = ${adBlockData.isAdBlock},ad_block_date=NOW();`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Company ad block added successfully.",
            data: [adBlockData],
          });
        } else {
          reject({
            status: 400,
            message: "company ad block not added",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getBlockAdbyCompany = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select a.*,u.id as likeUserId,u.name as likeUserName,u.firstName as 
    likefirstName,u.lastName as likeLastName,
    u.profession as likeUserProfession,
    u.profile_image as userLikeProfileImage, c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename from ad_details  ad
    INNER JOIN admodels a on a.id=ad.ad_id
    INNER JOIN users u on u.id=ad.user_id
   INNER JOIN company c ON c.id=a.company_id
  where ad.ad_id in (select id from admodels where createdby=${userId}) and ad.is_like=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Ad fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Ad not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });
let getAdview = (adModel) =>
  new Promise((resolve, reject) => {
    let sql = `SELECT a.ad_per_preview_percentage, am.base_price,a.adwatch_per_day, a.ad_customer_target_per_day,target_area FROM admodels a JOIN admodels_master am ON a.ad_model_id=am.id WHERE a.id = ${adModel.id}`;
    let sql1 = `SELECT * FROM ad_details WHERE user_id = ${adModel.userId} AND ad_id=${adModel.id} AND is_view = 1;`;
    let sql2 = `SELECT longitude, latitude from users where id=${adModel.userId}`;

    Promise.all([
      dbQuery.queryRunner(sql),
      dbQuery.queryRunner(sql1),
      dbQuery.queryRunner(sql2),
    ])
      .then((result) => {
        if (result && result.length != 0) {
          let targetArea = [];
          let targetAreaName = [];
          (customerTargetPerDay = 0), (totalViews = 0);
          if (result[0].length != 0 && result[0][0].length != 0) {
            try {
              targetArea = JSON.parse(result[0][0].target_area);
            } catch (e) {}

            total =
              (result[0][0].base_price / 100) *
              result[0][0].ad_per_preview_percentage;
            customerTargetPerDay = result[0][0].adwatch_per_day;
          }
          if (result[2].length != 0) {
            var latitude = parseFloat(result[2][0].latitude);
            var longitude = parseFloat(result[2][0].longitude);

            const currentLocation = {
              latitude: latitude,
              longitude: longitude,
            };

            if (targetArea.length === 0) {
              //not able to parse that means location set is not as expected
              console.log("parse error");
              if (result[1].length != 0) {
                totalViews = result[1].length;
                var totalLikes = result[1][0].is_like;
                var totalViewss = result[1][0].is_view;

                if (totalLikes >= 1) {
                  return resolve({
                    status: 200,
                    message: 0,
                    data: {
                      customerTargetPerDay: customerTargetPerDay,
                      totalViews: totalViews,
                    },
                  });
                }

                if (totalViews >= customerTargetPerDay) {
                  adModel.customerTargetPerDay = customerTargetPerDay;
                  adModel.totalViews = totalViews;

                  return resolve({
                    status: 200,
                    message: 0,
                    data: {
                      customerTargetPerDay: customerTargetPerDay,
                      totalViews: totalViews,
                    },
                  });
                } else {
                  return resolve({
                    status: 200,
                    message: 1,
                    data: {
                      customerTargetPerDay: customerTargetPerDay,
                      totalViews: totalViews,
                    },
                  });
                }
              } else {
                return resolve({
                  status: 200,
                  message: 1,
                  data: {
                    customerTargetPerDay: customerTargetPerDay,
                    totalViews: 0,
                  },
                });
              }
            } else {
              ///location is correct
              console.log("location correct set in db");
              targetAreaName = targetArea.map((loc) => ({
                address: loc.name,
              }));
              console.log("target area name ", targetAreaName);
              const targetLocations = targetArea.map((loc) => ({
                latitude: loc.location.latitude,
                longitude: loc.location.longitude,
              }));
              const hasIndiaAddress = targetAreaName.some((area) =>
                area.address.startsWith("India")
              );

              if (hasIndiaAddress) {
                if (result[1].length != 0) {
                  totalViews = result[1].length;
                  var totalLikes = result[1][0].is_like;
                  var totalViewss = result[1][0].is_view;

                  if (totalLikes >= 1) {
                    return resolve({
                      status: 200,
                      message: 0,
                      data: {
                        customerTargetPerDay: customerTargetPerDay,
                        totalViews: totalViews,
                      },
                    });
                  }

                  if (totalViews >= customerTargetPerDay) {
                    adModel.customerTargetPerDay = customerTargetPerDay;
                    adModel.totalViews = totalViews;

                    return resolve({
                      status: 200,
                      message: 0,
                      data: {
                        customerTargetPerDay: customerTargetPerDay,
                        totalViews: totalViews,
                      },
                    });
                  } else {
                    return resolve({
                      status: 200,
                      message: 1,
                      data: {
                        customerTargetPerDay: customerTargetPerDay,
                        totalViews: totalViews,
                      },
                    });
                  }
                } else {
                  return resolve({
                    status: 200,
                    message: 1,
                    data: {
                      customerTargetPerDay: customerTargetPerDay,
                      totalViews: 0,
                    },
                  });
                }
              }
              console.log("location", targetLocations);
              if (util.isWithinRadius(currentLocation, targetLocations)) {
                console.log("within radius");
                if (result[1].length != 0) {
                  totalViews = result[1].length;
                  var totalLikes = result[1][0].is_like;
                  var totalViewss = result[1][0].is_view;

                  if (totalLikes >= 1) {
                    return resolve({
                      status: 200,
                      message: 0,
                      data: {
                        customerTargetPerDay: customerTargetPerDay,
                        totalViews: totalViews,
                      },
                    });
                  }

                  if (totalViews >= customerTargetPerDay) {
                    adModel.customerTargetPerDay = customerTargetPerDay;
                    adModel.totalViews = totalViews;

                    return resolve({
                      status: 200,
                      message: 0,
                      data: {
                        customerTargetPerDay: customerTargetPerDay,
                        totalViews: totalViews,
                      },
                    });
                  } else {
                    return resolve({
                      status: 200,
                      message: 1,
                      data: {
                        customerTargetPerDay: customerTargetPerDay,
                        totalViews: totalViews,
                      },
                    });
                  }
                } else {
                  return resolve({
                    status: 200,
                    message: 1,
                    data: {
                      customerTargetPerDay: customerTargetPerDay,
                      totalViews: 0,
                    },
                  });
                }
              } else {
                console.log("not within within radius");
                return resolve({
                  status: 200,
                  message: 0,
                  data: {
                    customerTargetPerDay: 1,
                    totalViews: 0,
                  },
                });
              }
            }
          } else {
            reject({
              status: 400,
              message: "Amount not added",
              data: result,
            });
          }
        } else {
          reject({
            status: 400,
            message: "Amount not added",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getBlockAdCompanyByUser = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select *,1 as isBlock from company where id in (select company_id from company_details where user_id=${userId} and is_block=1)`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Blocked Ad company fetch successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Blocked Ad company not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let saveAdPauseCountinueStatus = (adModel) =>
  new Promise((resolve, reject) => {
    let sql = `update admodels set adPauseCountinue='${adModel.adPauseContinue}',ad_pause_status_date=NOW() where id = ${adModel.adId}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Admodel update successfully.",
            data: [adModel],
          });
        } else {
          reject({
            status: 400,
            message: "Admodel not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });
// let saveFivePageAdmodel = adModel => new Promise((resolve, reject) => {
//     let sql = `update admodels set ad_place_app='${adModel.adPlaceApp}',ad_other_platform='${adModel.adOtherPlatform}', ad_customised_que1='${adModel.adQa1}', ad_customised_que2='${adModel.adQa2}', ad_customised_que3='${adModel.adQa3}',ad_customised_que4='${adModel.adQa4}',ad_refferal='${adModel.adRefferal}',ad_bank_account='${adModel.adBankAccountNumber}',ad_order_value='${adModel.adOrderValue}',ad_charges_value='${adModel.adChargesValue}',ad_tax='${adModel.adTax}',ad_total='${adModel.adTotal}',ad_coupon='${adModel.adCoupan}',is_five_page_save=1, updateddate=NOW() where id = ${adModel.id}`;
//     dbQuery.queryRunner(sql)
//         .then(result => {
//             if (result && result.length != 0) {
//                 adModel.fourthPageSave = 1;
//                 resolve({
//                     status: 200,
//                     message: "Admodel update successfully.",
//                     data: [adModel]
//                 });
//             } else {
//                 reject({
//                     status: 400,
//                     message: "Admodel not saved.",
//                     data: result
//                 });
//             }
//         })
//         .catch(err => {
//             let message = '';
//             if (err.message.includes('ER_DUP_ENTRY')) message = 'Ad already available.';
//             if (err.message.includes('ER_NO_REFERENCED_ROW_2')) message = 'Invalid referance Id.';
//             if (err.message.includes('UNKNOWN_CODE_PLEASE_REPORT')) message = 'Invalid referance Id.';
//             reject({
//                 status: 500,
//                 message: message != '' ? message : err.message,
//                 data: []
//             });
//         });
// });

// let getAllSaveAds = () => new Promise((resolve, reject) => {
//     let sql = `select id,name,view_price as viewPrice, base_price as basePrice,discount,model_image as modelImage from admodels_master where is_active=1 and is_ad_model_master=1`;
//     dbQuery.queryRunner(sql)
//         .then(result => {
//             if (result && result.length != 0) {
//                 resolve({
//                     status: 200,
//                     message: "Fetch ad models successfully.",
//                     data: result
//                 });
//             } else {
//                 reject({
//                     status: 400,
//                     message: "Ad models not found.",
//                     data: result
//                 });
//             }
//         })
//         .catch(err => {
//             reject({
//                 status: 500,
//                 message: err,
//                 data: []
//             });
//         });
// });
module.exports = {
  getAdDetailsForQr: (adId) => {
    return new Promise((resolve, reject) => {
      getAdDetailsForQr(adId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  getAdview: (adModel) =>
    new Promise((resolve, reject) => {
      return getAdview(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getAdModels: () =>
    new Promise((resolve, reject) => {
      return getAdModels()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getAdvModel: (userId) =>
    new Promise((resolve, reject) => {
      return getAdvModels(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getTargetProfession: () =>
    new Promise((resolve, reject) => {
      return getTargetProfession()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getTargetAreas: () =>
    new Promise((resolve, reject) => {
      return getTargetAreas()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getAdForShortVideo: (userId) => {
    return new Promise((resolve, reject) => {
      return getAdForShortVideo(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  getButtons: () =>
    new Promise((resolve, reject) => {
      return getButtons()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getCompanyButton: () =>
    new Promise((resolve, reject) => {
      return getCompanyButton()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getAnimations: () =>
    new Promise((resolve, reject) => {
      return getAnimations()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveFirstAdModel: (adModel) =>
    new Promise((resolve, reject) => {
      return saveFirstAdModel(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveSecondPageAdmodel: (adModel, adFile) =>
    new Promise((resolve, reject) => {
      return saveSecondPageAdmodel(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveCelebrationAddModels: (adModel, adFile) =>
    new Promise((resolve, reject) => {
      return saveCelebrationAddModels(adModel, adFile)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveThirdPageAdmodel: (adModel) =>
    new Promise((resolve, reject) => {
      return saveThirdPageAdmodel(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  requesdemoservice: (adModel) =>
    new Promise((resolve, reject) => {
      return saveRequestDemo(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  validateCoupon: (adCoupon) =>
    new Promise((resolve, reject) => {
      return validateCoupon(adCoupon)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getCoupon: (adCoupon) =>
    new Promise((resolve, reject) => {
      return getCoupon(adCoupon)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getrequestdemoads: (req) =>
    new Promise((resolve, reject) => {
      return getrequestdemoads(req)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getFilterAds: (filterdata) =>
    new Promise((resolve, reject) => {
      return getFilterAds(filterdata)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getMasterFilterAds: (filterdata) =>
    new Promise((resolve, reject) => {
      return getMasterFilterAds(filterdata)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getMasterAdsPagination: (page) =>
    new Promise((resolve, reject) => {
      return getMasterAdsPagination(page)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getCelebrationAds: (filterdata) =>
    new Promise((resolve, reject) => {
      return getCelebrationAds(filterdata)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getBuisnessAds: (filterdata) =>
    new Promise((resolve, reject) => {
      return getBuisnessAds(filterdata)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getFallowCompany: (userId) =>
    new Promise((resolve, reject) => {
      return getFallowCompany(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getAdHubsAds: (filterdata) =>
    new Promise((resolve, reject) => {
      return getAdHubsAds(filterdata)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getAdViewsAndLike: (filterdata) =>
    new Promise((resolve, reject) => {
      return getAdViewsAndLike(filterdata)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveAdViewsAndLikes: (adModel) =>
    new Promise((resolve, reject) => {
      return saveAdViewsAndLikes(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveAdLikeAmount: (adModel) =>
    new Promise((resolve, reject) => {
      adModel.likeFlag = true;
      return saveAdLikeAmount(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  addAmountToWallet: (adModel) =>
    new Promise((resolve, reject) => {
      adModel.likeFlag = false;
      return addAmountToWallet(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveAdViewAmount: (adModel) =>
    new Promise((resolve, reject) => {
      adModel.likeFlag = false;
      return saveAdViewAmount(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveFallowCompany: (companyModel) =>
    new Promise((resolve, reject) => {
      return saveFallowCompany(companyModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getMyLikesAds: (userId, page) =>
    new Promise((resolve, reject) => {
      return getMyLikesAds(userId, page)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveBussinessAdModel: (adModel, adFile) =>
    new Promise((resolve, reject) => {
      return saveBussinessAdModel(adModel, adFile)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveCelebrationAdView: (adModel) =>
    new Promise((resolve, reject) => {
      return saveCelebrationAdView(adModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getLossViewAmount: (adId) =>
    new Promise((resolve, reject) => {
      return getLossViewAmount(adId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getAdDetails: (userId, adId) =>
    new Promise((resolve, reject) => {
      return getAdDetails(userId, adId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getOrderTracking: (adId) =>
    new Promise((resolve, reject) => {
      return getOrderTracking(adId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getAdDetailsforVideo: (userId, adId) =>
    new Promise((resolve, reject) => {
      return getAdDetailsforVideo(userId, adId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getLastAdDetails: (userId, companyId) =>
    new Promise((resolve, reject) => {
      return getLastAdDetails(userId, companyId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getUserLikeAds: (userId) =>
    new Promise((resolve, reject) => {
      return getUserLikeAds(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getLikeAdsbyCompanyUserId: (companyuserid) =>
    new Promise((resolve, reject) => {
      return getLikeAdsbyCompanyUserId(companyuserid)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getGraphData: (filterdata) =>
    new Promise((resolve, reject) => {
      return getGraphData(filterdata)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getAdPassBook: (userId) =>
    new Promise((resolve, reject) => {
      return getAdPassBook(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveBlockad: (adBlockData) =>
    new Promise((resolve, reject) => {
      return saveBlockad(adBlockData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getBlockAdbyCompany: (userId) =>
    new Promise((resolve, reject) => {
      return getBlockAdbyCompany(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getBlockAdCompanyByUser: (userId) =>
    new Promise((resolve, reject) => {
      return getBlockAdCompanyByUser(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveAdPauseCountinueStatus: (adData) =>
    new Promise((resolve, reject) => {
      return saveAdPauseCountinueStatus(adData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  // saveFourPageAdmodel: adModel => new Promise((resolve, reject) => {
  //     return saveFourPageAdmodel(adModel).then(result => {
  //         if (result && result.status == 200) {
  //             resolve(result);
  //         } else {
  //             reject(result);
  //         }
  //     }).catch(err => {
  //         reject(err);
  //     })
  // }),
  // saveFivePageAdmodel: adModel => new Promise((resolve, reject) => {
  //     return saveFivePageAdmodel(adModel).then(result => {
  //         if (result && result.status == 200) {
  //             resolve(result);
  //         } else {
  //             reject(result);
  //         }
  //     }).catch(err => {
  //         reject(err);
  //     })
  // }),

  // getAllSaveAds: adModel => new Promise((resolve, reject) => {
  //     return getAllSaveAds(adModel).then(result => {
  //         if (result && result.status == 200) {
  //             resolve(result);
  //         } else {
  //             reject(result);
  //         }
  //     }).catch(err => {
  //         reject(err);
  //     })
  // }),
};
