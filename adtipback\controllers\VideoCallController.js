const VideoCallService = require('../services/VideoCallService');
const { queryRunner } = require('../dbConfig/queryRunner');

// @route   POST /api/video-call
// @desc    Initiate a video call with subscription-based pricing
exports.initiateVideoCall = async (req, res) => {
  try {
    const { callerId, receiverId, action, callId } = req.body;

    // Check if required parameters are provided
    if (!callerId || !receiverId || !action) {
      return res.status(400).json({ 
        status: false, 
        message: "Missing required parameters: callerId, receiverId, action" 
      });
    }

    // Handle different call actions
    switch (action) {
      case "start":
        const startResult = await VideoCallService.startVideoCall(callerId, receiverId);
        return res.status(startResult.statusCode || 200).json(startResult);

      case "end":
        if (!callId) {
          return res.status(400).json({ 
            status: false, 
            message: "callId is required for ending a call" 
          });
        }
        const endResult = await VideoCallService.endVideoCall(callerId, receiverId, callId);
        return res.status(endResult.statusCode || 200).json(endResult);

      case "missed":
        const missedResult = await VideoCallService.missedVideoCall(callerId, receiverId);
        return res.status(missedResult.statusCode || 200).json(missedResult);

      default:
        return res.status(400).json({ 
          status: false, 
          message: "Invalid action. Use 'start', 'end', or 'missed'" 
        });
    }
  } catch (error) {
    console.error("Error in initiateVideoCall:", error);
    return res.status(500).json({ 
      status: false, 
      message: "Internal server error", 
      error: error.message 
    });
  }
};

// @route   GET /api/video-call/balance/:userId
// @desc    Get user's video call balance and subscription status
exports.getVideoCallBalance = async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ 
        status: false, 
        message: "User ID is required" 
      });
    }

    const balanceResult = await VideoCallService.getVideoCallBalance(userId);
    return res.status(balanceResult.statusCode || 200).json(balanceResult);
  } catch (error) {
    console.error("Error in getVideoCallBalance:", error);
    return res.status(500).json({ 
      status: false, 
      message: "Internal server error", 
      error: error.message 
    });
  }
};

// @route   GET /api/video-call/history/:userId
// @desc    Get user's video call history
exports.getVideoCallHistory = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    if (!userId) {
      return res.status(400).json({ 
        status: false, 
        message: "User ID is required" 
      });
    }

    const historyResult = await VideoCallService.getVideoCallHistory(userId, parseInt(page), parseInt(limit));
    return res.status(historyResult.statusCode || 200).json(historyResult);
  } catch (error) {
    console.error("Error in getVideoCallHistory:", error);
    return res.status(500).json({ 
      status: false, 
      message: "Internal server error", 
      error: error.message 
    });
  }
}; 