{"cells": [{"cell_type": "markdown", "id": "a69d223a", "metadata": {}, "source": ["# error"]}, {"cell_type": "code", "execution_count": null, "id": "ae5bb43b", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["\n", "C:\\projects\\adtip-reactnative\\Adtip\\android>gradlew clean\n", "\n", "FAILURE: Build failed with an exception.\n", "\n", "* Where:\n", "* Where:\n", "Settings file 'C:\\projects\\adtip-reactnative\\Adtip\\android\\settings.gradle' line: 2\n", "\n", "* What went wrong:\n", "Error resolving plugin [id: 'com.facebook.react.settings']\n", "> Could not read workspace metadata from C:\\Users\\<USER>\\.gradle\\caches\\8.13\\kotlin-dsl\\accessors\\cbd2677dac5813b0a8ba9dfa391abe66\\metadata.bin        \n", "\n", "* Try:\n", "> Run with --stacktrace option to get the stack trace.\n", "> Run with --info or --debug option to get more log output.\n", "> Run with --scan to get full insights.\n", "> Get more help at https://help.gradle.org.\n", "\n", "BUILD FAILED in 29s\n"]}, {"cell_type": "markdown", "id": "4bb27786", "metadata": {}, "source": [" # solution"]}, {"cell_type": "code", "execution_count": null, "id": "b9245934", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}