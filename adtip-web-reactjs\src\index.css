
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 170 100% 43%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 170 100% 43%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .teal-gradient {
    @apply bg-gradient-to-br from-adtip-teal to-[#13b799];
  }

  .teal-button {
    @apply teal-gradient text-white font-semibold py-3 px-6 rounded-full shadow-md hover:shadow-lg transition-all duration-300;
  }

  .content-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg;
  }

  /* Safe area utilities for mobile devices */
  .safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-pt {
    padding-top: env(safe-area-inset-top);
  }

  /* Grid layout utilities */
  .app-grid {
    display: grid;
    min-height: 100vh;
  }

  .app-grid-mobile {
    grid-template-areas:
      "header"
      "main"
      "bottom-nav";
    grid-template-rows: auto 1fr auto;
    grid-template-columns: 1fr;
  }

  .app-grid-desktop {
    grid-template-areas:
      "header header"
      "sidebar main";
    grid-template-rows: auto 1fr;
    grid-template-columns: auto 1fr;
  }

  /* Scrollbar hiding utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Responsive utilities */
  .mobile-only {
    @apply block md:hidden;
  }

  .tablet-only {
    @apply hidden md:block lg:hidden;
  }

  .desktop-only {
    @apply hidden lg:block;
  }

  .mobile-tablet {
    @apply block lg:hidden;
  }

  .tablet-desktop {
    @apply hidden md:block;
  }

  /* Container utilities with consistent breakpoints */
  .container-responsive {
    @apply w-full mx-auto px-4 sm:px-6 lg:px-8;
    max-width: 1280px;
  }

  .container-narrow {
    @apply w-full mx-auto px-4 sm:px-6;
    max-width: 768px;
  }
}
