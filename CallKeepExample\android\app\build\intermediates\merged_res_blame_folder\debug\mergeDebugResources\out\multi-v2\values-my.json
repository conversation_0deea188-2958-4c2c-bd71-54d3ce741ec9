{"logs": [{"outputFile": "com.callkeepexample.app-mergeDebugResources-27:/values-my/values-my.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/625ed137c6a3f5343b71917adedb437c/transformed/appcompat-1.7.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,4624", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,4705"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,208,282,353,437,506,574,653,737,827,909,979,1071,1154,1236,1328,1412,1495,1567,1639,1724,1800,1877,1956", "endColumns": "73,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "124,203,277,348,432,501,569,648,732,822,904,974,1066,1149,1231,1323,1407,1490,1562,1634,1719,1795,1872,1951,2031"}, "to": {"startLines": "29,37,38,39,40,41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2866,3682,3761,3835,3906,3990,4059,4127,4206,4290,4380,4462,4532,4710,4793,4875,4967,5051,5134,5206,5278,5464,5540,5617,5696", "endColumns": "73,78,73,70,83,68,67,78,83,89,81,69,91,82,81,91,83,82,71,71,84,75,76,78,79", "endOffsets": "2935,3756,3830,3901,3985,4054,4122,4201,4285,4375,4457,4527,4619,4788,4870,4962,5046,5129,5201,5273,5358,5535,5612,5691,5771"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "30,31,32,33,34,35,36,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2940,3043,3147,3250,3352,3457,3563,5363", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3038,3142,3245,3347,3452,3558,3677,5459"}}]}]}