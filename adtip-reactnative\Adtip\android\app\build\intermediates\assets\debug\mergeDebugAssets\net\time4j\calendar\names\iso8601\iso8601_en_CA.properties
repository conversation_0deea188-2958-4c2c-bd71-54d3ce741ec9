# months
M(a)_1=Jan.
M(a)_2=Feb.
M(a)_3=Mar.
M(a)_4=Apr.
M(a)_6=Jun.
M(a)_7=Jul.
M(a)_8=Aug.
M(a)_9=Sep.
M(a)_10=Oct.
M(a)_11=Nov.
M(a)_12=Dec.

# weekdays
D(a)_1=Mon.
D(a)_2=Tue.
D(a)_3=Wed.
D(a)_4=Thu.
D(a)_5=Fri.
D(a)_6=Sat.
D(a)_7=Sun.

# day-period-translations
P(a)_am=a.m.
P(a)_pm=p.m.

P(n)_midnight=mid
P(n)_am=am
P(n)_pm=pm
P(n)_morning1=mor
P(n)_afternoon1=aft
P(n)_evening1=eve
P(n)_night1=night

P(w)_am=a.m.
P(w)_pm=p.m.

P(A)_am=a.m.
P(A)_pm=p.m.

P(N)_midnight=mid
P(N)_am=a.m.
P(N)_pm=pm
P(N)_morning1=mor
P(N)_afternoon1=aft
P(N)_evening1=eve

P(W)_am=a.m.
P(W)_pm=p.m.

# format patterns
F(f)_d=EEEE, MMMM d, y
F(l)_d=MMMM d, y
F(m)_d=MMM d, y
F(s)_d=d/M/yy

F_Md=d/M
F_MMMd=MMM d
F_MMMMd=MMMM d
F_yM=M/y

# labels of elements
L_dayperiod=a.m./p.m.
