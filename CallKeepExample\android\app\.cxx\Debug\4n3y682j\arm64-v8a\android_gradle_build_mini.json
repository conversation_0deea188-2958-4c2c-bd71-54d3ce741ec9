{"buildFiles": ["/Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "/Users/<USER>/Desktop/CallKeepExample/android/app/build/intermediates/cxx/Debug/4n3y682j/obj/arm64-v8a/libappmodules.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"]}}}