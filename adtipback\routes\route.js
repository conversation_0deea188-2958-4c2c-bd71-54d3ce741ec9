require("dotenv").config();
const express = require("express");
const upload = require("multer")({ dest: "public/" });
const checkId = require("../utils/utils");
const Auth = require("../config/auth");
const router = express.Router();
const app = express();
const { queryRunner } = require("../dbConfig/queryRunner"); // Add this if not already imported
app.use("/users", require("../routes/users.js"));

router.use((req, res, next) => {
  console.log(req.path);
  next();
});

const ProductController = require("../controllers/ProductController.js");


//pubscale integration
router.get(
  "/payments/offers/pubscale",
  ProductController.handlePubscaleCallback
);

module.exports = { router, app };
