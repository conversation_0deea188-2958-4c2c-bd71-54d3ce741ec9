-- Add voice-call enum value to user_video_calls table
-- This script adds the new call type to the existing enum

-- First, let's check the current enum values
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'user_video_calls' 
  AND COLUMN_NAME = 'call_type';

-- Add the new enum value to user_video_calls table
ALTER TABLE user_video_calls 
MODIFY COLUMN call_type ENUM('call', 'video-call', 'voice-call') DEFAULT 'call';

-- Add the new enum value to call_transactions table (if it exists)
-- First check if the table exists
SELECT COUNT(*) as table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'call_transactions';

-- If call_transactions table exists, update its enum
-- Note: This is a conditional update - only run if the table exists
-- You may need to run this manually if the table exists

-- Verify the changes
SELECT COLUMN_TYPE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'user_video_calls' 
  AND COLUMN_NAME = 'call_type';

-- Show success message
SELECT 'voice-call enum value added successfully!' as message; 