const dbQuery = require("../dbConfig/queryRunner");

exports.logError = async (error, functionName, requestBody = null) => {
  try {
    console.log("Logging error for function:", functionName);
    const sql = `
      INSERT INTO error_logs (error_message, error_stack, function_name, request_body)
      VALUES (?, ?, ?, ?)`;

    const params = [
      error.message || 'Unknown error',
      error.stack || '',
      functionName,
      requestBody ? JSON.stringify(requestBody) : null
    ];

    await dbQuery.queryRunner(sql, params);
  } catch (logError) {
    console.error("Error logging failed:", logError);
  }
};
