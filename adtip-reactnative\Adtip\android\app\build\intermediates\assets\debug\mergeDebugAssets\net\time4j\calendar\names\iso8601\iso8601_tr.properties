# months
M(a)_1=Oca
M(a)_2=Şub
M(a)_3=Mar
M(a)_4=Nis
M(a)_5=May
M(a)_6=Haz
M(a)_7=Tem
M(a)_8=Ağu
M(a)_9=Eyl
M(a)_10=Eki
M(a)_11=Kas
M(a)_12=Ara

M(n)_1=O
M(n)_2=Ş
M(n)_3=M
M(n)_4=N
M(n)_5=M
M(n)_6=H
M(n)_7=T
M(n)_8=A
M(n)_9=E
M(n)_10=E
M(n)_11=K
M(n)_12=A

M(w)_1=Ocak
M(w)_2=Şubat
M(w)_3=Mart
M(w)_4=Nisan
M(w)_5=Mayıs
M(w)_6=Haziran
M(w)_7=Temmuz
M(w)_8=Ağustos
M(w)_9=Eylül
M(w)_10=<PERSON><PERSON>
M(w)_11=Kasım
M(w)_12=Aralık

M(A)_1=Oca
M(A)_2=Şub
M(A)_3=Mar
M(A)_4=Nis
M(A)_5=May
M(A)_6=Haz
M(A)_7=Tem
M(A)_8=Ağu
M(A)_9=Eyl
M(A)_10=Eki
M(A)_11=Kas
M(A)_12=Ara

M(N)_1=O
M(N)_2=Ş
M(N)_3=M
M(N)_4=N
M(N)_5=M
M(N)_6=H
M(N)_7=T
M(N)_8=A
M(N)_9=E
M(N)_10=E
M(N)_11=K
M(N)_12=A

M(W)_1=Ocak
M(W)_2=Şubat
M(W)_3=Mart
M(W)_4=Nisan
M(W)_5=Mayıs
M(W)_6=Haziran
M(W)_7=Temmuz
M(W)_8=Ağustos
M(W)_9=Eylül
M(W)_10=Ekim
M(W)_11=Kasım
M(W)_12=Aralık

# weekdays
D(a)_1=Pzt
D(a)_2=Sal
D(a)_3=Çar
D(a)_4=Per
D(a)_5=Cum
D(a)_6=Cmt
D(a)_7=Paz

D(n)_1=P
D(n)_2=S
D(n)_3=Ç
D(n)_4=P
D(n)_5=C
D(n)_6=C
D(n)_7=P

D(s)_1=Pt
D(s)_2=Sa
D(s)_3=Ça
D(s)_4=Pe
D(s)_5=Cu
D(s)_6=Ct
D(s)_7=Pa

D(w)_1=Pazartesi
D(w)_2=Salı
D(w)_3=Çarşamba
D(w)_4=Perşembe
D(w)_5=Cuma
D(w)_6=Cumartesi
D(w)_7=Pazar

D(A)_1=Pzt
D(A)_2=Sal
D(A)_3=Çar
D(A)_4=Per
D(A)_5=Cum
D(A)_6=Cmt
D(A)_7=Paz

D(N)_1=P
D(N)_2=S
D(N)_3=Ç
D(N)_4=P
D(N)_5=C
D(N)_6=C
D(N)_7=P

D(S)_1=Pt
D(S)_2=Sa
D(S)_3=Ça
D(S)_4=Pe
D(S)_5=Cu
D(S)_6=Ct
D(S)_7=Pa

D(W)_1=Pazartesi
D(W)_2=Salı
D(W)_3=Çarşamba
D(W)_4=Perşembe
D(W)_5=Cuma
D(W)_6=Cumartesi
D(W)_7=Pazar

# quarters
Q(a)_1=Ç1
Q(a)_2=Ç2
Q(a)_3=Ç3
Q(a)_4=Ç4

Q(n)_1=1.
Q(n)_2=2.
Q(n)_3=3.
Q(n)_4=4.

Q(w)_1=1. çeyrek
Q(w)_2=2. çeyrek
Q(w)_3=3. çeyrek
Q(w)_4=4. çeyrek

Q(A)_1=Ç1
Q(A)_2=Ç2
Q(A)_3=Ç3
Q(A)_4=Ç4

Q(N)_1=1.
Q(N)_2=2.
Q(N)_3=3.
Q(N)_4=4.

Q(W)_1=1. çeyrek
Q(W)_2=2. çeyrek
Q(W)_3=3. çeyrek
Q(W)_4=4. çeyrek

# day-period-rules
T0600=morning1
T1100=morning2
T1200=afternoon1
T1800=afternoon2
T1900=evening1
T2100=night1

# day-period-translations
P(a)_midnight=gece yarısı
P(a)_am=ÖÖ
P(a)_noon=öğle
P(a)_pm=ÖS
P(a)_morning1=sabah
P(a)_morning2=öğleden önce
P(a)_afternoon1=öğleden sonra
P(a)_afternoon2=akşamüstü
P(a)_evening1=akşam
P(a)_night1=gece

P(n)_midnight=gece
P(n)_am=öö
P(n)_noon=ö
P(n)_pm=ös
P(n)_morning1=sabah
P(n)_morning2=öğleden önce
P(n)_afternoon1=öğleden sonra
P(n)_afternoon2=akşamüstü
P(n)_evening1=akşam
P(n)_night1=gece

P(w)_midnight=gece yarısı
P(w)_am=ÖÖ
P(w)_noon=öğle
P(w)_pm=ÖS
P(w)_morning1=sabah
P(w)_morning2=öğleden önce
P(w)_afternoon1=öğleden sonra
P(w)_afternoon2=akşamüstü
P(w)_evening1=akşam
P(w)_night1=gece

P(A)_midnight=gece yarısı
P(A)_am=ÖÖ
P(A)_noon=öğle
P(A)_pm=ÖS
P(A)_morning1=sabah
P(A)_morning2=öğleden önce
P(A)_afternoon1=öğleden sonra
P(A)_afternoon2=akşamüstü
P(A)_evening1=akşam
P(A)_night1=gece

P(N)_midnight=gece yarısı
P(N)_am=ÖÖ
P(N)_noon=öğle
P(N)_pm=ÖS
P(N)_morning1=sabah
P(N)_morning2=öğleden önce
P(N)_afternoon1=öğleden sonra
P(N)_afternoon2=akşamüstü
P(N)_evening1=akşam
P(N)_night1=gece

P(W)_midnight=gece yarısı
P(W)_am=ÖÖ
P(W)_noon=öğle
P(W)_pm=ÖS
P(W)_morning1=sabah
P(W)_morning2=öğleden önce
P(W)_afternoon1=öğleden sonra
P(W)_afternoon2=akşamüstü
P(W)_evening1=akşam
P(W)_night1=gece

# eras
E(w)_0=Milattan Önce
E(w|alt)_0=İsa’dan Önce
E(w)_1=Milattan Sonra
E(w|alt)_1=İsa’dan Sonra

E(a)_0=MÖ
E(a|alt)_0=İÖ
E(a)_1=MS
E(a|alt)_1=İS

# format patterns
F(f)_d=d MMMM y EEEE
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=d.MM.y

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=B h
F_Bhm=B h:mm
F_Bhms=B h:mm:ss
F_h=a h
F_H=HH
F_hm=a h:mm
F_Hm=HH:mm
F_hms=a h:mm:ss
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM/y
F_yMM=MM.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=y QQQ
F_yQQQQ=y QQQQ
F_yw=Y 'yılının' w. 'haftası'

I={0} – {1}

# labels of elements
L_era=çağ
L_year=yıl
L_quarter=çeyrek
L_month=ay
L_week=hafta
L_day=gün
L_weekday=haftanın günü
L_dayperiod=ÖÖ/ÖS
L_hour=saat
L_minute=dakika
L_second=saniye
L_zone=saat dilimi
