# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2016-03-09 18:40:14 -0600 using RuboCop version 0.38.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 3
Lint/IneffectiveAccessModifier:
  Exclude:
    - 'lib/claide/command.rb'

# Offense count: 1
# Cop supports --auto-correct.
Lint/RedundantCopDisableDirective:
  Exclude:
    - 'spec/command/banner_spec.rb'

# Offense count: 1
Performance/FixedSize:
  Exclude:
    - 'lib/claide/command/banner.rb'

# Offense count: 1
# Cop supports --auto-correct.
Performance/StringReplacement:
  Exclude:
    - 'lib/claide/command/banner.rb'

# Offense count: 8
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SupportedStyles.
# SupportedStyles: prefer_alias, prefer_alias_method
Style/Alias:
  Exclude:
    - 'lib/claide/argument.rb'
    - 'lib/claide/command.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: SingleLineConditionsOnly.
Style/ConditionalAssignment:
  Exclude:
    - 'lib/claide/command/banner.rb'

# Offense count: 1
Style/IfInsideElse:
  Exclude:
    - 'lib/claide/command.rb'

# Offense count: 9
# Cop supports --auto-correct.
Style/MutableConstant:
  Exclude:
    - 'lib/claide/ansi.rb'
    - 'lib/claide/argument.rb'
    - 'lib/claide/command.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/ParallelAssignment:
  Exclude:
    - 'lib/claide/command/argument_suggester.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/RedundantInterpolation:
  Exclude:
    - 'lib/claide/command/argument_suggester.rb'
