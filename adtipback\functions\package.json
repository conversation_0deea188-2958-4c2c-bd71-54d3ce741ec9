{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "firebase-admin": "^12.7.0", "firebase-functions": "^6.3.2", "morgan": "^1.10.0", "uuid": "^11.1.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}