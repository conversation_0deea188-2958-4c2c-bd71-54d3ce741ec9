Libffi change logs used to be maintained in separate ChangeLog files.
These days we generate them directly from the git commit messages.
The old ChangeLog files are saved here in order to maintain the historical
record.

=============================================================================
From the old ChangeLog.libffi-3.1 file...

2014-03-16  <PERSON>  <<EMAIL>>

	* ChangeLog: Archive to ChangeLog.libffi-3.1 and delete.  Future
	changelogs will come from git, with autogenerated snapshots shipped in
	distributed tarballs.

2014-03-16  Josh <PERSON>  <<EMAIL>>

	Add support for stdcall, thiscall, and fastcall on non-Windows
	x86-32.

	Linux supports the stdcall calling convention, either via
	functions explicitly declared with the stdcall attribute, or via
	code compiled with -mrtd which effectively makes stdcall the
	default.

	This introduces FFI_STDCALL, FFI_THISCALL, and FFI_FASTCALL on
	non-Windows x86-32 platforms, as non-default calling conventions.

	* Makefile.am: Compile in src/x86/win32.S on non-Windows x86-32.
	* src/x86/ffitarget.h: Add FFI_STDCALL, FFI_THISCALL, and
	FFI_FASTCALL on non-Windows x86-32.  Increase trampoline size to
	accomodate these calling conventions, and unify some ifdeffery.
	* src/x86/ffi.c: Add support for FFI_STDCALL, FFI_THISCALL, and
	FFI_FASTCALL on non-Windows x86-32 platforms; update ifdeffery.
	* src/x86/win32.S: Support compiling on non-Windows x86-32
	platforms.  On those platforms, avoid redefining the SYSV symbols
	already provided by src/x86/sysv.S.
	* testsuite/libffi.call/closure_stdcall.c: Run on non-Windows.
	#define __stdcall if needed.
	* testsuite/libffi.call/closure_thiscall.c: Run on non-Windows.
	#define __fastcall if needed.
	* testsuite/libffi.call/fastthis1_win32.c: Run on non-Windows.
	* testsuite/libffi.call/fastthis2_win32.c: Ditto.
	* testsuite/libffi.call/fastthis3_win32.c: Ditto.
	* testsuite/libffi.call/many2_win32.c: Ditto.
	* testsuite/libffi.call/many_win32.c: Ditto.
	* testsuite/libffi.call/strlen2_win32.c: Ditto.
	* testsuite/libffi.call/strlen_win32.c: Ditto.
	* testsuite/libffi.call/struct1_win32.c: Ditto.
	* testsuite/libffi.call/struct2_win32.c: Ditto.

2014-03-16  Josh Triplett  <<EMAIL>>

	* prep_cif.c: Remove unnecessary ifdef for X86_WIN32.
	ffi_prep_cif_core had a special case for X86_WIN32, checking for
	FFI_THISCALL in addition to the FFI_FIRST_ABI-to-FFI_LAST_ABI
	range before returning FFI_BAD_ABI.  However, on X86_WIN32,
	FFI_THISCALL already falls in that range, making the special case
	unnecessary.  Remove it.

2014-03-16  Josh Triplett  <<EMAIL>>

	* testsuite/libffi.call/closure_stdcall.c,
	testsuite/libffi.call/closure_thiscall.c: Remove fragile stack
	pointer checks.  These files included inline assembly to save the
	stack pointer before and after the call, and compare the values.
	However, compilers can and do leave the stack in different states
	for these two pieces of inline assembly, such as by saving a
	temporary value on the stack across the call; observed with gcc
	-Os, and verified as spurious through careful inspection of
	disassembly.

2014-03-16  Josh Triplett  <<EMAIL>>

	* testsuite/libffi.call/many.c: Avoid spurious failure due to
	excess floating-point precision.
	* testsuite/libffi.call/many_win32.c: Ditto.

2014-03-16  Josh Triplett <<EMAIL>>

	* libtool-ldflags: Re-add.

2014-03-16  Josh Triplett <<EMAIL>>

	* Makefile.in, aclocal.m4, compile, config.guess, config.sub,
	configure, depcomp, include/Makefile.in, install-sh,
	libtool-ldflags, ltmain.sh, m4/libtool.m4, m4/ltoptions.m4,
	m4/ltsugar.m4, m4/ltversion.m4, m4/lt~obsolete.m4,
	man/Makefile.in, mdate-sh, missing, testsuite/Makefile.in: Delete
	autogenerated files from version control.
	* .gitignore: Add autogenerated files.
	* autogen.sh: New script to generate the autogenerated files.
	* README: Document requirement to run autogen.sh when building
	directly from version control.
	* .travis.yml: Run autogen.sh

2014-03-14  Anthony Green <<EMAIL>>

	* configure, Makefile.in: Rebuilt.

2014-03-10  Mike Hommey <<EMAIL>>

	* configure.ac: Allow building for mipsel with Android NDK r8.
	* Makefile.am (AM_MAKEFLAGS): Replace double quotes with single
	quotes.

2014-03-10  Landry Breuil <<EMAIL>>

	* configure.ac: Ensure the linker supports @unwind sections in libffi.

2014-03-01  Anthony Green  <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Replace old scripts with
	generate-darwin-source-and-headers.py.
	* Makefile.in: Rebuilt.

2014-02-28  Anthony Green  <<EMAIL>>

	* Makefile.am (AM_CFLAGS): Reintroduce missing -DFFI_DEBUG for
	--enable-debug builds.
	* Makefile.in: Rebuilt.

2014-02-28  Makoto Kato  <<EMAIL>>

	* src/closures.c: Fix build failure when using clang for Android.

2014-02-28  Marcin Wojdyr  <<EMAIL>>

	* libffi.pc.in (toolexeclibdir): use -L${toolexeclibdir} instead
	of -L${libdir}.

2014-02-28  Paulo Pizarro  <<EMAIL>>

	* src/bfin/sysv.S: Calling functions in shared libraries requires
	considering the GOT.

2014-02-28  Josh Triplett  <<EMAIL>>

	* src/x86/ffi64.c (classify_argument): Handle case where
	FFI_TYPE_LONGDOUBLE == FFI_TYPE_DOUBLE.

2014-02-28  Anthony Green  <<EMAIL>>

	* ltmain.sh: Generate with libtool-2.4.2.418.
	* m4/libtool.m4, m4/ltoptions.m4, m4/ltversion.m4: Ditto.
	* configure: Rebuilt.

2014-02-28  Dominik Vogt  <<EMAIL>>

	* configure.ac (AC_ARG_ENABLE struct): Fix typo in help
	message.
	(AC_ARG_ENABLE raw_api): Ditto.
	* configure, fficonfig.h.in: Rebuilt.

2014-02-28  Will Newton  <<EMAIL>>

	* src/arm/sysv.S: Initialize IP register with FP.

2014-02-28  Yufeng Zhang  <<EMAIL>>

	* src/aarch64/sysv.S (ffi_closure_SYSV): Use x29 as the
	main CFA reg; update cfi_rel_offset.

2014-02-15  Marcus Comstedt  <<EMAIL>>

	* src/powerpc/ffi_linux64.c, src/powerpc/linux64_closure.S: Remove
	assumption on contents of r11 in closure.

2014-02-09  Heiher  <<EMAIL>>

	* src/mips/n32.S: Fix call floating point va function.

2014-01-21  Zachary Waldowski  <<EMAIL>>

	* src/aarch64/ffi.c: Fix missing semicolons on assertions under
	debug mode.

2013-12-30  Zachary Waldowski  <<EMAIL>>

	* .gitignore: Exclude darwin_* generated source and build_* trees.
	* src/aarch64/ffi.c, src/arm/ffi.c, src/x86/ffi.c: Inhibit Clang
	previous prototype warnings.
	* src/arm/ffi.c: Prevent NULL dereference, fix short type warning
	* src/dlmalloc.c: Fix warnings from set_segment_flags return type,
	and the native use of size_t for malloc on platforms
	* src/arm/sysv.S: Use unified syntax. Clang clean-ups for
	ARM_FUNC_START.
	* generate-osx-source-and-headers.py: Remove.
	* build-ios.sh: Remove.
	* libffi.xcodeproj/project.pbxproj: Rebuild targets. Include
	x86_64+aarch64 pieces in library.  Export headers properly.
	* src/x86/ffi64.c: More Clang warning clean-ups.
	* src/closures.c (open_temp_exec_file_dir): Use size_t.
	* src/prep_cif.c (ffi_prep_cif_core): Cast ALIGN result.
	* src/aarch64/sysv.S: Use CNAME for global symbols.  Only use
	.size for ELF targets.
	* src/aarch64/ffi.c: Clean up for double == long double.  Clean up
	from Clang warnings.  Use Clang cache invalidation builtin.  Use
	size_t in place of unsigned in many places.  Accommodate for
	differences in Apple AArch64 ABI.

2013-12-02  Daniel Rodríguez Troitiño  <<EMAIL>>

	* generate-darwin-source-and-headers.py: Clean up, modernize,
	merged version of previous scripts.

2013-11-21  Anthony Green  <<EMAIL>>

	* configure, Makefile.in, include/Makefile.in, include/ffi.h.in,
	man/Makefile.in, testsuite/Makefile.in, fficonfig.h.in: Rebuilt.

2013-11-21  Alan Modra  <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Add new src/powerpc files.
	(nodist_libffi_la_SOURCES <POWERPC, POWERPC_FREEBSD>): Likewise.
	* configure.ac (HAVE_LONG_DOUBLE_VARIANT): Define for powerpc.
	* include/ffi.h.in (ffi_prep_types): Declare.
	* src/prep_cif.c (ffi_prep_cif_core): Call ffi_prep_types.
	* src/types.c (FFI_NONCONST_TYPEDEF): Define and use for
	HAVE_LONG_DOUBLE_VARIANT.
	* src/powerpc/ffi_powerpc.h: New file.
	* src/powerpc/ffi.c: Split into..
	* src/powerpc/ffi_sysv.c: ..new file, and..
	* src/powerpc/ffi_linux64.c: ..new file, rewriting parts.
	* src/powerpc/ffitarget.h (enum ffi_abi): Rewrite powerpc ABI
	selection as bits controlling features.
	* src/powerpc/linux64.S: For consistency, use POWERPC64 rather
	than __powerpc64__.
	* src/powerpc/linux64_closure.S: Likewise.
	* src/powerpc/ppc_closure.S: Likewise.  Move .note.FNU-stack
	inside guard.
	* src/powerpc/sysv.S: Likewise.
	* configure: Regenerate.
	* fficonfig.h.in: Regenerate.
	* Makefile.in: Regenerate.

2013-11-20  Alan Modra  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_cif_machdep_core): Use
	NUM_FPR_ARG_REGISTERS64 and NUM_GPR_ARG_REGISTERS64 not their
	32-bit versions for 64-bit code.
	* src/powerpc/linux64_closure.S: Don't use the return value area
	as a parameter save area on ELFv2.

2013-11-18  Iain Sandoe  <<EMAIL>>

	* src/powerpc/darwin.S (EH): Correct use of pcrel FDE encoding.
	* src/powerpc/darwin_closure.S (EH): Likewise. Modernise picbase
	labels.

2013-11-18  Anthony Green  <<EMAIL>>

	* src/arm/ffi.c (ffi_call): Hoist declaration of temp to top of
	function.
	* src/arm/ffi.c (ffi_closure_inner): Moderize function declaration
	to appease compiler.
	Thanks for Gregory P. Smith <<EMAIL>>.

2013-11-18  Anthony Green  <<EMAIL>>

	* README (tested): Mention PowerPC ELFv2.

2013-11-16  Alan Modra  <<EMAIL>>

	* src/powerpc/ppc_closure.S: Move errant #endif to where it belongs.
	Don't bl .Luint128.

2013-11-16  Alan Modra  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_cif_machdep_core): Use #if _CALL_ELF
	test to select parameter save sizing for ELFv2 vs. ELFv1.
	* src/powerpc/ffitarget.h (FFI_V2_TYPE_FLOAT_HOMOG,
	FFI_V2_TYPE_DOUBLE_HOMOG, FFI_V2_TYPE_SMALL_STRUCT): Define.
	(FFI_TRAMPOLINE_SIZE): Define variant for ELFv2.
	* src/powerpc/ffi.c (FLAG_ARG_NEEDS_PSAVE): Define.
	(discover_homogeneous_aggregate): New function.
	(ffi_prep_args64): Adjust start of param save area for ELFv2.
	Handle homogenous floating point struct parms.
	(ffi_prep_cif_machdep_core): Adjust space calculation for ELFv2.
	Handle ELFv2 return values.  Set FLAG_ARG_NEEDS_PSAVE.  Handle
	homogenous floating point structs.
	(ffi_call): Increase size of smst_buffer for ELFv2.  Handle ELFv2.
	(flush_icache): Compile for ELFv2.
	(ffi_prep_closure_loc): Set up ELFv2 trampoline.
	(ffi_closure_helper_LINUX64): Don't return all structs directly
	to caller.  Handle homogenous floating point structs.  Handle
	ELFv2 struct return values.
	* src/powerpc/linux64.S (ffi_call_LINUX64): Set up r2 for
	ELFv2.  Adjust toc save location.  Call function pointer using
	r12.  Handle FLAG_RETURNS_SMST.  Don't predict branches.
	* src/powerpc/linux64_closure.S (ffi_closure_LINUX64): Set up r2
	for ELFv2.  Define ELFv2 versions of STACKFRAME, PARMSAVE, and
	RETVAL.  Handle possibly missing parameter save area.  Handle
	ELFv2 return values.
	(.note.GNU-stack): Move inside outer #ifdef.

2013-11-16  Alan Modra  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_cif_machdep): Revert 2013-02-08
	change.  Do not consume an int arg when returning a small struct
	for FFI_SYSV ABI.
	(ffi_call): Only use bounce buffer when FLAG_RETURNS_SMST.
	Properly copy bounce buffer to destination.
	* src/powerpc/sysv.S: Revert 2013-02-08 change.
	* src/powerpc/ppc_closure.S: Remove stray '+'.

2013-11-16  Alan Modra  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_args64): Align struct parameters
	according to __STRUCT_PARM_ALIGN__.
	(ffi_prep_cif_machdep_core): Likewise.
	(ffi_closure_helper_LINUX64): Likewise.

2013-11-16  Alan Modra  <<EMAIL>>

	* src/powerpc/linux64.S (ffi_call_LINUX64): Tweak restore of r28.
	(.note.GNU-stack): Move inside outer #ifdef.
	* src/powerpc/linux64_closure.S (STACKFRAME, PARMSAVE,
	RETVAL): Define and use throughout.
	(ffi_closure_LINUX64): Save fprs before buying stack.
	(.note.GNU-stack): Move inside outer #ifdef.

2013-11-16  Alan Modra  <<EMAIL>>

	* src/powerpc/ffitarget.h (FFI_TARGET_SPECIFIC_VARIADIC): Define.
	(FFI_EXTRA_CIF_FIELDS): Define.
	* src/powerpc/ffi.c (ffi_prep_args64): Save fprs as per the
	ABI, not to both fpr and param save area.
	(ffi_prep_cif_machdep_core): Renamed from ffi_prep_cif_machdep.
	Keep initial flags.  Formatting.  Remove dead FFI_LINUX_SOFT_FLOAT
	code.
	(ffi_prep_cif_machdep, ffi_prep_cif_machdep_var): New functions.
	(ffi_closure_helper_LINUX64): Pass floating point as per ABI,
	not to both fpr and parameter save areas.

	* libffi/testsuite/libffi.call/cls_double_va.c (main): Correct
	function cast and don't call ffi_prep_cif.
	* libffi/testsuite/libffi.call/cls_longdouble_va.c (main): Likewise.

2013-11-15  Andrew Haley  <<EMAIL>>

	* doc/libffi.texi (Closure Example): Fix the sample code.
	* doc/libffi.info, doc/stamp-vti, doc/version.texi: Rebuilt.

2013-11-15  Andrew Haley  <<EMAIL>>

	* testsuite/libffi.call/va_struct1.c (main): Fix broken test.
	* testsuite/libffi.call/cls_uint_va.c (cls_ret_T_fn): Likewise
	* testsuite/libffi.call/cls_struct_va1.c (test_fn): Likewise.
	* testsuite/libffi.call/va_1.c (main): Likewise.

2013-11-14  David Schneider  <<EMAIL>>

	* src/arm/ffi.c: Fix register allocation for mixed float and
	doubles.
	* testsuite/libffi.call/cls_many_mixed_float_double.c: Testcase
	for many mixed float and double arguments.

2013-11-13  Alan Modra  <<EMAIL>>

	* doc/libffi.texi (Simple Example): Correct example code.
	* doc/libffi.info, doc/stamp-vti, doc/version.texi: Rebuilt.

2013-11-13  Anthony Green  <<EMAIL>>

	* include/ffi_common.h: Respect HAVE_ALLOCA_H for GNU compiler
	based build. (Thanks to tmr111116 on github)

2013-11-09  Anthony Green  <<EMAIL>>

	* m4/libtool.m4: Refresh.
	* configure, Makefile.in: Rebuilt.
	* README: Add more notes about next release.

2013-11-09  Shigeharu TAKENO  <<EMAIL>>

	* m4/ax_gcc_archflag.m4 (ax_gcc_arch): Don't recognize
	UltraSPARC-IIi as ultrasparc3.

2013-11-06  Mark Kettenis  <<EMAIL>>

	* src/x86/freebsd.S (ffi_call_SYSV): Align the stack pointer to
        16-bytes.

2013-11-06  Konstantin Belousov <<EMAIL>>

	* src/x86/freebsd.S (ffi_closure_raw_SYSV): Mark the assembler
	source as not requiring executable stack.

2013-11-02  Anthony Green  <<EMAIL>>

	* doc/libffi.texi (The Basics): Clarify return value buffer size
	requirements.  Also, NULL result buffer pointers are no longer
	supported.
	* doc/libffi.info: Rebuilt.

2013-11-02  Mischa Jonker  <<EMAIL>>

	* Makefile.am (nodist_libffi_la_SOURCES): Fix build error.
	* Makefile.in: Rebuilt.

2013-11-02  David Schneider  <<EMAIL>>

	* src/arm/ffi.c: more robust argument handling for closures on arm hardfloat
	* testsuite/libffi.call/many_mixed.c: New file.
	* testsuite/libffi.call/cls_many_mixed_args.c: More tests.

2013-11-02  Vitaly Budovski

	* src/x86/ffi.c (ffi_prep_cif_machdep): Don't align stack for win32.

2013-10-23  Mark H Weaver  <<EMAIL>>

	* src/mips/ffi.c: Fix handling of uint32_t arguments on the
	MIPS N32 ABI.

2013-10-13  Sandra Loosemore  <<EMAIL>>

	* README: Add Nios II to table of supported platforms.
	* Makefile.am (EXTRA_DIST): Add nios2 files.
	(nodist_libffi_la_SOURCES): Likewise.
	* Makefile.in: Regenerated.
	* configure.ac (nios2*-linux*): New host.
	(NIOS2): Add AM_CONDITIONAL.
	* configure: Regenerated.
	* src/nios2/ffi.c: New.
	* src/nios2/ffitarget.h: New.
	* src/nios2/sysv.S: New.
	* src/prep_cif.c (initialize_aggregate): Handle extra structure
	alignment via FFI_AGGREGATE_ALIGNMENT.
	(ffi_prep_cif_core): Conditionalize structure return for NIOS2.

2013-10-10  Sandra Loosemore  <<EMAIL>>

	* testsuite/libffi.call/cls_many_mixed_args.c (cls_ret_double_fn):
	Fix uninitialized variable.

2013-10-11  Marcus Shawcroft  <<EMAIL>>

	* testsuite/libffi.call/many.c (many): Replace * with +.

2013-10-08  Ondřej Bílka  <<EMAIL>>

	* src/aarch64/ffi.c, src/aarch64/sysv.S, src/arm/ffi.c,
	src/arm/gentramp.sh, src/bfin/sysv.S, src/closures.c,
	src/dlmalloc.c, src/ia64/ffi.c, src/microblaze/ffi.c,
	src/microblaze/sysv.S, src/powerpc/darwin_closure.S,
	src/powerpc/ffi.c, src/powerpc/ffi_darwin.c, src/sh/ffi.c,
	src/tile/tile.S, testsuite/libffi.call/nested_struct11.c: Fix
	spelling errors.

2013-10-08  Anthony Green  <<EMAIL>>

	* aclocal.m4, compile, config.guess, config.sub, depcomp,
	install-sh, mdate-sh, missing, texinfo.tex: Update from upstream.
	* configure.ac: Update version to 3.0.14-rc0.
	* Makefile.in, configure, Makefile.in, include/Makefile.in,
	man/Makefile.in, testsuite/Makefile.in: Rebuilt.
	* README: Mention M88K and VAX.

2013-07-15  Miod Vallat  <<EMAIL>>

	* Makefile.am,
	configure.ac,
	src/m88k/ffi.c,
	src/m88k/ffitarget.h,
	src/m88k/obsd.S,
	src/vax/elfbsd.S,
	src/vax/ffi.c,
	src/vax/ffitarget.h: Add m88k and vax support.

2013-06-24 Alan Modra  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_args_SYSV): Move var declaration
	before statements.
	(ffi_prep_args64): Support little-endian.
	(ffi_closure_helper_SYSV, ffi_closure_helper_LINUX64): Likewise.
	* src/powerpc/linux64_closure.S (ffi_closure_LINUX64): Likewise.
	* src/powerpc/ppc_closure.S (ffi_closure_SYSV): Likewise.

2013-06-12  Mischa Jonker  <<EMAIL>>

	 * configure.ac: Add support for ARC.
	 * Makefile.am: Likewise.
	 * README: Add ARC details.
	 * src/arc/arcompact.S: New.
	 * src/arc/ffi.c: Likewise.
	 * src/arc/ffitarget.h: Likewise.

2013-03-28  David Schneider  <<EMAIL>>

	 * src/arm/ffi.c: Fix support for ARM hard-float calling convention.
	 * src/arm/sysv.S: call different methods for SYSV and VFP ABIs.
	 * testsuite/libffi.call/cls_many_mixed_args.c: testcase for a closure with
	 mixed arguments, many doubles.
	 * testsuite/libffi.call/many_double.c: testcase for calling a function using
	 more than 8 doubles.
	 * testcase/libffi.call/many.c: use absolute value to check result against an
	 epsilon

2013-03-17  Anthony Green  <<EMAIL>>

	* README: Update for 3.0.13.
	* configure.ac: Ditto.
	* configure: Rebuilt.
	* doc/*: Update version.

2013-03-17  Dave Korn  <<EMAIL>>

	* src/closures.c (is_emutramp_enabled
	[!FFI_MMAP_EXEC_EMUTRAMP_PAX]): Move default definition outside
	enclosing #if scope.

2013-03-17  Anthony Green  <<EMAIL>>

	* configure.ac: Only modify toolexecdir in certain cases.
	* configure: Rebuilt.

2013-03-16  Gilles Talis  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_args_SYSV): Don't use
	fparg_count,etc on __NO_FPRS__ targets.

2013-03-16  Alan Hourihane  <<EMAIL>>

	* src/m68k/sysv.S (epilogue): Don't use extb instruction on
	m680000 machines.

2013-03-16  Alex Gaynor <<EMAIL>>

	* src/x86/ffi.c (ffi_prep_cif_machdep): Always align stack.

2013-03-13  Markos Chandras <<EMAIL>>

	* configure.ac: Add support for Imagination Technologies Meta.
	* Makefile.am: Likewise.
	* README: Add Imagination Technologies Meta details.
	* src/metag/ffi.c: New.
	* src/metag/ffitarget.h: Likewise.
	* src/metag/sysv.S: Likewise.

2013-02-24  Andreas Schwab  <<EMAIL>>

	* doc/libffi.texi (Structures): Fix missing category argument of
	@deftp.

2013-02-11  Anthony Green <<EMAIL>>

	* configure.ac: Update release number to 3.0.12.
	* configure: Rebuilt.
	* README: Update release info.

2013-02-10  Anthony Green <<EMAIL>>

	* README: Add Moxie.
	* src/moxie/ffi.c: Created.
	* src/moxie/eabi.S: Created.
	* src/moxie/ffitarget.h: Created.
	* Makefile.am (nodist_libffi_la_SOURCES): Add Moxie.
	* Makefile.in: Rebuilt.
	* configure.ac: Add Moxie.
	* configure: Rebuilt.
	* testsuite/libffi.call/huge_struct.c: Disable format string
	warnings for moxie*-*-elf tests.

2013-02-10  Anthony Green <<EMAIL>>

	* Makefile.am (LTLDFLAGS): Fix reference.
	* Makefile.in: Rebuilt.

2013-02-10  Anthony Green <<EMAIL>>

	* README: Update supported platforms.  Update test results link.

2013-02-09  Anthony Green <<EMAIL>>

	* testsuite/libffi.call/negint.c: Remove forced -O2.
	* testsuite/libffi.call/many2.c (foo): Remove GCCism.
	* testsuite/libffi.call/ffitest.h: Add default PRIuPTR definition.

	* src/sparc/v8.S (ffi_closure_v8): Import ancient ulonglong
	closure return type fix developed by Martin v. Löwis for cpython
	fork.

2013-02-08  Andreas Tobler  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_cif_machdep): Fix small struct
	support.
	* src/powerpc/sysv.S: Ditto.

2013-02-08  Anthony Green <<EMAIL>>

	* testsuite/libffi.call/cls_longdouble.c: Remove xfail for
	arm*-*-*.

2013-02-08  Anthony Green <<EMAIL>>

	* src/sparc/ffi.c (ffi_prep_closure_loc): Fix cache flushing for GCC.

2013-02-08  Matthias Klose  <<EMAIL>>

	* man/ffi_prep_cif.3: Clean up for debian linter.

2013-02-08  Peter Bergner  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_args_SYSV): Account for FP args pushed
	on the stack.

2013-02-08  Anthony Green <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Add missing files.
	* testsuite/Makefile.am (EXTRA_DIST): Ditto.
	* Makefile.in: Rebuilt.

2013-02-08  Anthony Green <<EMAIL>>

	* configure.ac: Move sparc asm config checks to within functions
	for compatibility with sun tools.
	* configure: Rebuilt.
	* src/sparc/ffi.c (ffi_prep_closure_loc): Flush cache on v9
	systems.
	* src/sparc/v8.S (ffi_flush_icache): Implement a sparc v9 cache
	flusher.

2013-02-08  Nathan Rossi <<EMAIL>>

	* src/microblaze/ffi.c (ffi_closure_call_SYSV): Fix handling of
	small big-endian structures.
	(ffi_prep_args): Ditto.

2013-02-07  Anthony Green <<EMAIL>>

	* src/sparc/v8.S (ffi_call_v8): Fix typo from last patch
	(effectively hiding ffi_call_v8).

2013-02-07  Anthony Green <<EMAIL>>

	* configure.ac: Update bug reporting address.
	* configure.in: Rebuild.

	* src/sparc/v8.S (ffi_flush_icache): Out-of-line cache flusher for
	Sun compiler.
	* src/sparc/ffi.c (ffi_call): Remove warning.
	Call ffi_flush_icache for non-GCC builds.
	(ffi_prep_closure_loc): Use ffi_flush_icache.

	* Makefile.am (EXTRA_DIST): Add libtool-ldflags.
	* Makefile.in: Rebuilt.
	* libtool-ldflags: New file.

2013-02-07  Daniel Schepler <<EMAIL>>

	* configure.ac: Correctly identify x32 systems as 64-bit.
	* m4/libtool.m4: Remove libtool expr error.
	* aclocal.m4, configure: Rebuilt.

2013-02-07  Anthony Green <<EMAIL>>

	* configure.ac: Fix GCC usage test.
	* configure: Rebuilt.
	* README: Mention LLVM/GCC x86_64 issue.
	* testsuite/Makefile.in: Rebuilt.

2013-02-07  Anthony Green <<EMAIL>>

	* testsuite/libffi.call/cls_double_va.c (main): Replace // style
	comments with /* */ for xlc compiler.
	* testsuite/libffi.call/stret_large.c (main): Ditto.
	* testsuite/libffi.call/stret_large2.c (main): Ditto.
	* testsuite/libffi.call/nested_struct1.c (main): Ditto.
	* testsuite/libffi.call/huge_struct.c (main): Ditto.
	* testsuite/libffi.call/float_va.c (main): Ditto.
	* testsuite/libffi.call/cls_struct_va1.c (main): Ditto.
	* testsuite/libffi.call/cls_pointer_stack.c (main): Ditto.
	* testsuite/libffi.call/cls_pointer.c (main): Ditto.
	* testsuite/libffi.call/cls_longdouble_va.c (main): Ditto.

2013-02-06  Anthony Green <<EMAIL>>

	* man/ffi_prep_cif.3: Clean up for debian lintian checker.

2013-02-06  Anthony Green <<EMAIL>>

	* Makefile.am (pkgconfigdir): Add missing pkgconfig install bits.
	* Makefile.in: Rebuild.

2013-02-02  Mark H Weaver <<EMAIL>>

	* src/x86/ffi64.c (ffi_call): Sign-extend integer arguments passed
	via general purpose registers.

2013-01-21  Nathan Rossi <<EMAIL>>

	* README: Add MicroBlaze details.
	* Makefile.am: Add MicroBlaze support.
	* configure.ac: Likewise.
	* src/microblaze/ffi.c: New.
	* src/microblaze/ffitarget.h: Likewise.
	* src/microblaze/sysv.S: Likewise.

2013-01-21  Nathan Rossi <<EMAIL>>
	* testsuite/libffi.call/return_uc.c: Fixed issue.

2013-01-21  Chris Zankel   <<EMAIL>>

	* README: Add Xtensa support.
	* Makefile.am: Likewise.
	* configure.ac: Likewise.
	* Makefile.in Regenerate.
	* configure: Likewise.
	* src/prep_cif.c: Handle Xtensa.
	* src/xtensa: New directory.
	* src/xtensa/ffi.c: New file.
	* src/xtensa/ffitarget.h: Ditto.
	* src/xtensa/sysv.S: Ditto.

2013-01-11  Anthony Green  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_args): Replace // style
	comments with /* */ for xlc compiler.
	* src/powerpc/aix.S (ffi_call_AIX): Ditto.
	* testsuite/libffi.call/ffitest.h (allocate_mmap): Delete
	deprecated inline function.
	* testsuite/libffi.special/ffitestcxx.h: Ditto.
	* README: Add update for AIX support.

2013-01-11  Anthony Green  <<EMAIL>>

	* configure.ac: Robustify pc relative reloc check.
	* m4/ax_cc_maxopt.m4: Don't -malign-double.  This is an ABI
	changing option for 32-bit x86.
	* aclocal.m4, configure: Rebuilt.
	* README: Update supported target list.

2013-01-10  Anthony Green  <<EMAIL>>

	* README (tested): Add Compiler column to table.

2013-01-10  Anthony Green  <<EMAIL>>

	* src/x86/ffi64.c (struct register_args): Make sse array and array
	of unions for sunpro compiler compatibility.

2013-01-10  Anthony Green  <<EMAIL>>

	* configure.ac: Test target platform size_t size.  Handle both 32
	and 64-bit builds for x86_64-* and i?86-* targets (allowing for
	CFLAG option to change default settings).
	* configure, aclocal.m4: Rebuilt.

2013-01-10  Anthony Green  <<EMAIL>>

	* testsuite/libffi.special/special.exp: Only run exception
	handling tests when using GNU compiler.

	* m4/ax_compiler_vendor.m4: New file.
	* configure.ac: Test for compiler vendor and don't use
	AX_CFLAGS_WARN_ALL with the sun compiler.
	* aclocal.m4, configure: Rebuilt.

2013-01-10  Anthony Green  <<EMAIL>>

	* include/ffi_common.h: Don't use GCCisms to define types when
	building with the SUNPRO compiler.

2013-01-10  Anthony Green  <<EMAIL>>

	* configure.ac: Put local.exp in the right place.
	* configure: Rebuilt.

	* src/x86/ffi.c: Update comment about regparm function attributes.
	* src/x86/sysv.S (ffi_closure_SYSV): The SUNPRO compiler requires
	that all function arguments be passed on the stack (no regparm
	support).

2013-01-08  Anthony Green  <<EMAIL>>

	* configure.ac: Generate local.exp.  This sets CC_FOR_TARGET
	when we are using the vendor compiler.
	* testsuite/Makefile.am (EXTRA_DEJAGNU_SITE_CONFIG): Point to
	../local.exp.
	* configure, testsuite/Makefile.in: Rebuilt.

	* testsuite/libffi.call/call.exp: Run tests with different
	options, depending on whether or not we are using gcc or the
	vendor compiler.
	* testsuite/lib/libffi.exp (libffi-init): Set using_gcc based on
	whether or not we are building/testing with gcc.

2013-01-08  Anthony Green  <<EMAIL>>

	* configure.ac: Switch x86 solaris target to X86 by default.
	* configure: Rebuilt.

2013-01-08  Anthony Green  <<EMAIL>>

	* configure.ac: Fix test for read-only eh_frame.
	* configure: Rebuilt.

2013-01-08  Anthony Green  <<EMAIL>>

	* src/x86/sysv.S, src/x86/unix64.S: Only emit DWARF unwind info
	when building with the GNU toolchain.
	* testsuite/libffi.call/ffitest.h (CHECK): Fix for Solaris vendor
	compiler.

2013-01-07  Thorsten Glaser <<EMAIL>>

	* testsuite/libffi.call/cls_uchar_va.c,
	testsuite/libffi.call/cls_ushort_va.c,
	testsuite/libffi.call/va_1.c: Testsuite fixes.

2013-01-07  Thorsten Glaser <<EMAIL>>

	* src/m68k/ffi.c (CIF_FLAGS_SINT8, CIF_FLAGS_SINT16): Define.
	(ffi_prep_cif_machdep): Fix 8-bit and 16-bit signed calls.
	* src/m68k/sysv.S (ffi_call_SYSV, ffi_closure_SYSV): Ditto.

2013-01-04  Anthony Green  <<EMAIL>>

	* Makefile.am (AM_CFLAGS): Don't automatically add -fexceptions
	and -Wall.  This is set in the configure script after testing for
	GCC.
	* Makefile.in: Rebuilt.

2013-01-02  rofl0r <https://github.com/rofl0r>

	* src/powerpc/ffi.c (ffi_prep_cif_machdep): Fix build error on ppc
	when long double == double.

2013-01-02  Reini Urban  <<EMAIL>>

	* Makefile.am (libffi_la_LDFLAGS): Add -no-undefined to LDFLAGS
	(required for shared libs on cygwin/mingw).
	* Makefile.in: Rebuilt.

2012-10-31  Alan Modra  <<EMAIL>>

	* src/powerpc/linux64_closure.S: Add new ABI support.
	* src/powerpc/linux64.S: Likewise.

2012-10-30  Magnus Granberg  <<EMAIL>>
	    Pavel Labushev  <<EMAIL>>

	* configure.ac: New options pax_emutramp
	* configure, fficonfig.h.in: Regenerated
	* src/closures.c: New function emutramp_enabled_check() and
	checks.

2012-10-30  Frederick Cheung  <<EMAIL>>

	* configure.ac: Enable FFI_MAP_EXEC_WRIT for Darwin 12 (mountain
	lion) and future version.
	* configure: Rebuild.

2012-10-30  James Greenhalgh  <james.greenhalgh at arm.com>
            Marcus Shawcroft  <marcus.shawcroft at arm.com>

        * README: Add details of aarch64 port.
        * src/aarch64/ffi.c: New.
        * src/aarch64/ffitarget.h: Likewise.
        * src/aarch64/sysv.S: Likewise.
	* Makefile.am: Support aarch64.
	* configure.ac: Support aarch64.
	* Makefile.in, configure: Rebuilt.

2012-10-30  James Greenhalgh  <james.greenhalgh at arm.com>
            Marcus Shawcroft  <marcus.shawcroft at arm.com>

        * testsuite/lib/libffi.exp: Add support for aarch64.
        * testsuite/libffi.call/cls_struct_va1.c: New.
        * testsuite/libffi.call/cls_uchar_va.c: Likewise.
        * testsuite/libffi.call/cls_uint_va.c: Likewise.
        * testsuite/libffi.call/cls_ulong_va.c: Likewise.
        * testsuite/libffi.call/cls_ushort_va.c: Likewise.
        * testsuite/libffi.call/nested_struct11.c: Likewise.
        * testsuite/libffi.call/uninitialized.c: Likewise.
        * testsuite/libffi.call/va_1.c: Likewise.
        * testsuite/libffi.call/va_struct1.c: Likewise.
        * testsuite/libffi.call/va_struct2.c: Likewise.
        * testsuite/libffi.call/va_struct3.c: Likewise.

2012-10-12  Walter Lee  <<EMAIL>>

        * Makefile.am: Add TILE-Gx/TILEPro support.
        * configure.ac: Likewise.
        * Makefile.in: Regenerate.
        * configure: Likewise.
        * src/prep_cif.c (ffi_prep_cif_core): Handle TILE-Gx/TILEPro.
        * src/tile: New directory.
        * src/tile/ffi.c: New file.
        * src/tile/ffitarget.h: Ditto.
        * src/tile/tile.S: Ditto.

2012-10-12  Matthias Klose  <<EMAIL>>

	* generate-osx-source-and-headers.py: Normalize whitespace.

2012-09-14  David Edelsohn  <<EMAIL>>

	* configure: Regenerated.

2012-08-26  Andrew Pinski  <<EMAIL>>

	PR libffi/53014
	* src/mips/ffi.c (ffi_prep_closure_loc): Allow n32 with soft-float and n64 with
	soft-float.

2012-08-08  Uros Bizjak  <<EMAIL>>

	* src/s390/ffi.c (ffi_prep_closure_loc): Don't ASSERT ABI test,
	just return FFI_BAD_ABI when things are wrong.

2012-07-18  H.J. Lu  <<EMAIL>>

	PR libffi/53982
	PR libffi/53973
	* src/x86/ffitarget.h: Check __ILP32__ instead of __LP64__ for x32.
	(FFI_SIZEOF_JAVA_RAW): Defined to 4 for x32.

2012-05-16  H.J. Lu  <<EMAIL>>

	* configure: Regenerated.

2012-05-05  Nicolas Lelong

	* libffi.xcodeproj/project.pbxproj: Fixes.
	* README: Update for iOS builds.

2012-04-23  Alexandre Keunecke I. de Mendonca <<EMAIL>>

	* configure.ac: Add Blackfin/sysv support
	* Makefile.am: Add Blackfin/sysv support
	* src/bfin/ffi.c:  Add Blackfin/sysv support
	* src/bfin/ffitarget.h: Add Blackfin/sysv support

2012-04-11  Anthony Green  <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Add new script.
	* Makefile.in: Rebuilt.

2012-04-11  Zachary Waldowski  <<EMAIL>>

	* generate-ios-source-and-headers.py,
	libffi.xcodeproj/project.pbxproj: Support a Mac static library via
	Xcode. Set iOS compatibility to 4.0.  Move iOS trampoline
	generation into an Xcode "run script" phase.  Include both as
	Xcode build scripts. Don't always regenerate config files.

2012-04-10  Anthony Green  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_args): Add missing semicolon.

2012-04-06  Anthony Green  <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Add new iOS/xcode files.
	* Makefile.in: Rebuilt.

2012-04-06  Mike Lewis  <<EMAIL>>

	* generate-ios-source-and-headers.py: New file.
	* libffi.xcodeproj/project.pbxproj: New file.
	* README: Update instructions on building iOS binary.
	* build-ios.sh: Delete.

2012-04-06  Anthony Green  <<EMAIL>>

	* src/x86/ffi64.c (UINT128): Define differently for Intel and GNU
	compilers, then use it.

2012-04-06  H.J. Lu  <<EMAIL>>

	* m4/libtool.m4 (_LT_ENABLE_LOCK): Support x32.

2012-04-06  Anthony Green  <<EMAIL>>

	* testsuite/Makefile.am (EXTRA_DIST): Add missing test cases.
	* testsuite/Makefile.in: Rebuilt.

2012-04-05  Zachary Waldowski  <<EMAIL>>

	* include/ffi.h.in: Add missing trampoline table fields.
	* src/arm/sysv.S: Fix ENTRY definition, and wrap symbol references
	in CNAME.
	* src/x86/ffi.c: Wrap Windows specific code in ifdefs.

2012-04-02  Peter Bergner  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_args_SYSV): Declare double_tmp.
	Silence casting pointer to integer of different size warning.
	Delete goto to previously deleted label.
	(ffi_call): Silence possibly undefined warning.
	(ffi_closure_helper_SYSV): Declare variable type.

2012-04-02  Peter Rosin  <<EMAIL>>

	* src/x86/win32.S (ffi_call_win32): Sign/zero extend the return
	value in the Intel version as is already done for the AT&T version.
	(ffi_closure_SYSV): Likewise.
	(ffi_closure_raw_SYSV): Likewise.
	(ffi_closure_STDCALL): Likewise.

2012-03-29  Peter Rosin  <<EMAIL>>

	* src/x86/win32.S (ffi_closure_raw_THISCALL): Unify the frame
	generation, fix the ENDP label and remove the surplus third arg
	from the 'lea' insn.

2012-03-29  Peter Rosin  <<EMAIL>>

	* src/x86/win32.S (ffi_closure_raw_SYSV): Make the 'stubraw' label
	visible outside the PROC, so that ffi_closure_raw_THISCALL can see
	it.  Also instruct the assembler to add a frame to the function.

2012-03-23  Peter Rosin  <<EMAIL>>

	* Makefile.am (AM_CPPFLAGS): Add -DFFI_BUILDING.
	* Makefile.in: Rebuilt.
	* include/ffi.h.in [MSVC]: Add __declspec(dllimport) decorations
	to all data exports, when compiling libffi clients using MSVC.

2012-03-29  Peter Rosin  <<EMAIL>>

	* src/x86/ffitarget.h (ffi_abi): Add new ABI FFI_MS_CDECL and
	make it the default for MSVC.
	(FFI_TYPE_MS_STRUCT): New structure return convention.
	* src/x86/ffi.c (ffi_prep_cif_machdep): Tweak the structure
	return convention for FFI_MS_CDECL to be FFI_TYPE_MS_STRUCT
	instead of an ordinary FFI_TYPE_STRUCT.
	(ffi_prep_args): Treat FFI_TYPE_MS_STRUCT as FFI_TYPE_STRUCT.
	(ffi_call): Likewise.
	(ffi_prep_incoming_args_SYSV): Likewise.
	(ffi_raw_call): Likewise.
	(ffi_prep_closure_loc): Treat FFI_MS_CDECL as FFI_SYSV.
	* src/x86/win32.S (ffi_closure_SYSV): For FFI_TYPE_MS_STRUCT,
	return a pointer to the result structure in eax and don't pop
	that pointer from the stack, the caller takes care of it.
	(ffi_call_win32): Treat FFI_TYPE_MS_STRUCT as FFI_TYPE_STRUCT.
	(ffi_closure_raw_SYSV): Likewise.

2012-03-22  Peter Rosin  <<EMAIL>>

	* testsuite/libffi.call/closure_stdcall.c [MSVC]: Add inline
	assembly version with Intel syntax.
	* testsuite/libffi.call/closure_thiscall.c [MSVC]: Likewise.

2012-03-23  Peter Rosin  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h: Provide abstration of
	__attribute__((fastcall)) in the form of a __FASTCALL__
	define.  Define it to __fastcall for MSVC.
	* testsuite/libffi.call/fastthis1_win32.c: Use the above.
	* testsuite/libffi.call/fastthis2_win32.c: Likewise.
	* testsuite/libffi.call/fastthis3_win32.c: Likewise.
	* testsuite/libffi.call/strlen2_win32.c: Likewise.
	* testsuite/libffi.call/struct1_win32.c: Likewise.
	* testsuite/libffi.call/struct2_win32.c: Likewise.

2012-03-22  Peter Rosin  <<EMAIL>>

	* src/x86/win32.S [MSVC] (ffi_closure_THISCALL): Remove the manual
	frame on function entry, MASM adds one automatically.

2012-03-22  Peter Rosin  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h [MSVC]: Add kludge for missing
	bits in the MSVC headers.

2012-03-22  Peter Rosin  <<EMAIL>>

	* testsuite/libffi.call/cls_12byte.c: Adjust to the C89 style
	with no declarations after statements.
	* testsuite/libffi.call/cls_16byte.c: Likewise.
	* testsuite/libffi.call/cls_18byte.c: Likewise.
	* testsuite/libffi.call/cls_19byte.c: Likewise.
	* testsuite/libffi.call/cls_1_1byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte1.c: Likewise.
	* testsuite/libffi.call/cls_24byte.c: Likewise.
	* testsuite/libffi.call/cls_2byte.c: Likewise.
	* testsuite/libffi.call/cls_3_1byte.c: Likewise.
	* testsuite/libffi.call/cls_3byte1.c: Likewise.
	* testsuite/libffi.call/cls_3byte2.c: Likewise.
	* testsuite/libffi.call/cls_4_1byte.c: Likewise.
	* testsuite/libffi.call/cls_4byte.c: Likewise.
	* testsuite/libffi.call/cls_5_1_byte.c: Likewise.
	* testsuite/libffi.call/cls_5byte.c: Likewise.
	* testsuite/libffi.call/cls_64byte.c: Likewise.
	* testsuite/libffi.call/cls_6_1_byte.c: Likewise.
	* testsuite/libffi.call/cls_6byte.c: Likewise.
	* testsuite/libffi.call/cls_7_1_byte.c: Likewise.
	* testsuite/libffi.call/cls_7byte.c: Likewise.
	* testsuite/libffi.call/cls_8byte.c: Likewise.
	* testsuite/libffi.call/cls_9byte1.c: Likewise.
	* testsuite/libffi.call/cls_9byte2.c: Likewise.
	* testsuite/libffi.call/cls_align_double.c: Likewise.
	* testsuite/libffi.call/cls_align_float.c: Likewise.
	* testsuite/libffi.call/cls_align_longdouble.c: Likewise.
	* testsuite/libffi.call/cls_align_longdouble_split.c: Likewise.
	* testsuite/libffi.call/cls_align_longdouble_split2.c: Likewise.
	* testsuite/libffi.call/cls_align_pointer.c: Likewise.
	* testsuite/libffi.call/cls_align_sint16.c: Likewise.
	* testsuite/libffi.call/cls_align_sint32.c: Likewise.
	* testsuite/libffi.call/cls_align_sint64.c: Likewise.
	* testsuite/libffi.call/cls_align_uint16.c: Likewise.
	* testsuite/libffi.call/cls_align_uint32.c: Likewise.
	* testsuite/libffi.call/cls_align_uint64.c: Likewise.
	* testsuite/libffi.call/cls_dbls_struct.c: Likewise.
	* testsuite/libffi.call/cls_pointer_stack.c: Likewise.
	* testsuite/libffi.call/err_bad_typedef.c: Likewise.
	* testsuite/libffi.call/huge_struct.c: Likewise.
	* testsuite/libffi.call/nested_struct.c: Likewise.
	* testsuite/libffi.call/nested_struct1.c: Likewise.
	* testsuite/libffi.call/nested_struct10.c: Likewise.
	* testsuite/libffi.call/nested_struct2.c: Likewise.
	* testsuite/libffi.call/nested_struct3.c: Likewise.
	* testsuite/libffi.call/nested_struct4.c: Likewise.
	* testsuite/libffi.call/nested_struct5.c: Likewise.
	* testsuite/libffi.call/nested_struct6.c: Likewise.
	* testsuite/libffi.call/nested_struct7.c: Likewise.
	* testsuite/libffi.call/nested_struct8.c: Likewise.
	* testsuite/libffi.call/nested_struct9.c: Likewise.
	* testsuite/libffi.call/stret_large.c: Likewise.
	* testsuite/libffi.call/stret_large2.c: Likewise.
	* testsuite/libffi.call/stret_medium.c: Likewise.
	* testsuite/libffi.call/stret_medium2.c: Likewise.
	* testsuite/libffi.call/struct1.c: Likewise.
	* testsuite/libffi.call/struct1_win32.c: Likewise.
	* testsuite/libffi.call/struct2.c: Likewise.
	* testsuite/libffi.call/struct2_win32.c: Likewise.
	* testsuite/libffi.call/struct3.c: Likewise.
	* testsuite/libffi.call/struct4.c: Likewise.
	* testsuite/libffi.call/struct5.c: Likewise.
	* testsuite/libffi.call/struct6.c: Likewise.
	* testsuite/libffi.call/struct7.c: Likewise.
	* testsuite/libffi.call/struct8.c: Likewise.
	* testsuite/libffi.call/struct9.c: Likewise.
	* testsuite/libffi.call/testclosure.c: Likewise.

2012-03-21  Peter Rosin	 <<EMAIL>>

	* testsuite/libffi.call/float_va.c (float_va_fn): Use %f when
	printing doubles (%lf is for long doubles).
	(main): Likewise.

2012-03-21  Peter Rosin  <<EMAIL>>

	* testsuite/lib/target-libpath.exp [*-*-cygwin*, *-*-mingw*]
	(set_ld_library_path_env_vars): Add the library search dir to PATH
	(and save PATH for later).
	(restore_ld_library_path_env_vars): Restore PATH.

2012-03-21  Peter Rosin  <<EMAIL>>

	* testsuite/lib/target-libpath.exp [*-*-cygwin*, *-*-mingw*]
	(set_ld_library_path_env_vars): Add the library search dir to PATH
	(and save PATH for later).
	(restore_ld_library_path_env_vars): Restore PATH.

2012-03-20  Peter Rosin  <<EMAIL>>

	* testsuite/libffi.call/strlen2_win32.c (main): Remove bug.
	* src/x86/win32.S [MSVC] (ffi_closure_SYSV): Make the 'stub' label
	visible outside the PROC, so that ffi_closure_THISCALL can see it.

2012-03-20  Peter Rosin  <<EMAIL>>

	* testsuite/libffi.call/strlen2_win32.c (main): Remove bug.
	* src/x86/win32.S [MSVC] (ffi_closure_SYSV): Make the 'stub' label
	visible outside the PROC, so that ffi_closure_THISCALL can see it.

2012-03-19  Alan Hourihane  <<EMAIL>>

	* src/m68k/ffi.c: Add MINT support.
	* src/m68k/sysv.S: Ditto.

2012-03-06  Chung-Lin Tang  <<EMAIL>>

	* src/arm/ffi.c (ffi_call): Add __ARM_EABI__ guard around call to
	ffi_call_VFP().
	(ffi_prep_closure_loc): Add __ARM_EABI__ guard around use of
	ffi_closure_VFP.
	* src/arm/sysv.S: Add __ARM_EABI__ guard around VFP code.

2012-03-19  chennam  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_closure_loc): Fix AIX closure
	support.

2012-03-13  Kaz Kojima  <<EMAIL>>

	* src/sh/ffi.c (ffi_prep_closure_loc): Don't ASSERT ABI test,
	just return FFI_BAD_ABI when things are wrong.
	* src/sh64/ffi.c (ffi_prep_closure_loc): Ditto.

2012-03-09  David Edelsohn  <<EMAIL>>

	* src/powerpc/aix_closure.S (ffi_closure_ASM): Adjust for Darwin64
	change to return value of ffi_closure_helper_DARWIN and load type
	from return type.

2012-03-03  H.J. Lu  <<EMAIL>>

	* src/x86/ffi64.c (ffi_call): Cast the return value to unsigned
	long.
	(ffi_prep_closure_loc): Cast to 64bit address in trampoline.
	(ffi_closure_unix64_inner): Cast return pointer to unsigned long
	first.

	* src/x86/ffitarget.h (FFI_SIZEOF_ARG): Defined to 8 for x32.
	(ffi_arg): Set to unsigned long long for x32.
	(ffi_sarg): Set to long long for x32.

2012-03-03  H.J. Lu  <<EMAIL>>

	* src/prep_cif.c (ffi_prep_cif_core): Properly check bad ABI.

2012-03-03  Andoni Morales Alastruey  <<EMAIL>>

	* configure.ac: Add -no-undefined for both 32- and 64-bit x86
	windows-like hosts.
	* configure: Rebuilt.

2012-02-27  Mikael Pettersson  <<EMAIL>>

	PR libffi/52223
	* Makefile.am (FLAGS_TO_PASS): Define.
	* Makefile.in: Regenerate.

2012-02-23  Anthony Green  <<EMAIL>>

	* src/*/ffitarget.h: Ensure that users never include ffitarget.h
	directly.

2012-02-23  Kai Tietz  <<EMAIL>>

	PR libffi/52221
	* src/x86/ffi.c (ffi_closure_raw_THISCALL): New
	prototype.
	(ffi_prep_raw_closure_loc): Use ffi_closure_raw_THISCALL for
	thiscall-convention.
	(ffi_raw_call): Use ffi_prep_args_raw.
	* src/x86/win32.S (ffi_closure_raw_THISCALL): Add
	implementation for stub.

2012-02-10  Kai Tietz  <<EMAIL>>

	* configure.ac (AM_LTLDFLAGS): Add -no-undefine for x64
	windows target.
	* configure: Regenerated.

2012-02-08  Kai Tietz  <<EMAIL>>

	* src/prep_cif.c (ffi_prep_cif): Allow for X86_WIN32
	also FFI_THISCALL.
	* src/x86/ffi.c (ffi_closure_THISCALL): Add prototype.
	(FFI_INIT_TRAMPOLINE_THISCALL): New trampoline code.
	(ffi_prep_closure_loc): Add FFI_THISCALL support.
	* src/x86/ffitarget.h (FFI_TRAMPOLINE_SIZE): Adjust size.
	* src/x86/win32.S (ffi_closure_THISCALL): New closure code
	for thiscall-calling convention.
	* testsuite/libffi.call/closure_thiscall.c: New test.

2012-01-28  Kai Tietz  <<EMAIL>>

	* src/libffi/src/x86/ffi.c (ffi_call_win32): Add new
	argument to prototype for specify calling-convention.
	(ffi_call): Add support for stdcall/thiscall convention.
	(ffi_prep_args): Likewise.
	(ffi_raw_call): Likewise.
	* src/x86/ffitarget.h (ffi_abi): Add FFI_THISCALL and
	FFI_FASTCALL.
	* src/x86/win32.S (_ffi_call_win32): Add support for
	fastcall/thiscall calling-convention calls.
	* testsuite/libffi.call/fastthis1_win32.c: New test.
	* testsuite/libffi.call/fastthis2_win32.c: New test.
	* testsuite/libffi.call/fastthis3_win32.c: New test.
	* testsuite/libffi.call/strlen2_win32.c: New test.
	* testsuite/libffi.call/many2_win32.c: New test.
	* testsuite/libffi.call/struct1_win32.c: New test.
	* testsuite/libffi.call/struct2_win32.c: New test.

2012-01-23  Uros Bizjak  <<EMAIL>>

	* src/alpha/ffi.c (ffi_prep_closure_loc): Check for bad ABI.

2012-01-23  Anthony Green  <<EMAIL>>
	    Chris Young  <<EMAIL>>

	* configure.ac: Add Amiga support.
	* configure: Rebuilt.

2012-01-23  Dmitry Nadezhin  <<EMAIL>>

	* include/ffi_common.h (LIKELY, UNLIKELY): Fix definitions.

2012-01-23  Andreas Schwab  <<EMAIL>>

	* src/m68k/sysv.S (ffi_call_SYSV): Properly test for plain
	mc68000.  Test for __HAVE_68881__ in addition to __MC68881__.

2012-01-19  Jakub Jelinek  <<EMAIL>>

	PR rtl-optimization/48496
	* src/ia64/ffi.c (ffi_call): Fix up aliasing violations.

2012-01-09  Rainer Orth  <<EMAIL>>

	* configure.ac (i?86-*-*): Set TARGET to X86_64.
	* configure: Regenerate.

2011-12-07  Andrew Pinski  <<EMAIL>>

	PR libffi/50051
	* src/mips/n32.S: Add ".set mips4".

2011-11-21  Andreas Tobler  <<EMAIL>>

	* configure: Regenerate.

2011-11-12  David Gilbert <<EMAIL>>

	* doc/libffi.texi, include/ffi.h.in, include/ffi_common.h,
	man/Makefile.am, man/ffi.3, man/ffi_prep_cif.3,
	man/ffi_prep_cif_var.3, src/arm/ffi.c, src/arm/ffitarget.h,
	src/cris/ffi.c, src/prep_cif.c,
	testsuite/libffi.call/cls_double_va.c,
	testsuite/libffi.call/cls_longdouble_va.c,
	testsuite/libffi.call/float_va.c: Many changes to support variadic
	function calls.

2011-11-12  Kyle Moffett <<EMAIL>>

	* src/powerpc/ffi.c, src/powerpc/ffitarget.h,
	src/powerpc/ppc_closure.S, src/powerpc/sysv.S: Many changes for
	softfloat powerpc variants.

2011-11-12  Petr Salinger <<EMAIL>>

	* configure.ac (FFI_EXEC_TRAMPOLINE_TABLE): Fix kfreebsd support.
	* configure: Rebuilt.

2011-11-12  Timothy Wall  <<EMAIL>>

	* src/arm/ffi.c (ffi_prep_args, ffi_prep_incoming_args_SYSV): Max
	alignment of 4 for wince on ARM.

2011-11-12  Kyle Moffett <<EMAIL>>
	    Anthony Green <<EMAIL>>

	* src/ppc/sysv.S, src/ppc/ffi.c: Remove use of ppc string
	instructions (not available on some cores, like the PPC440).

2011-11-12  Kimura Wataru  <<EMAIL>>

	* m4/ax_enable_builddir: Change from string comparison to numeric
	comparison for wc output.
	* configure.ac: Enable FFI_MMAP_EXEC_WRIT for darwin11 aka Mac OS
	X 10.7.
	* configure: Rebuilt.

2011-11-12  Anthony Green  <<EMAIL>>

	* Makefile.am (AM_CCASFLAGS): Add -g option to build assembly
	files with debug info.
	* Makefile.in: Rebuilt.

2011-11-12  Jasper Lievisse Adriaanse <<EMAIL>>

	* README: Update list of supported OpenBSD systems.

2011-11-12  Anthony Green  <<EMAIL>>

	* libtool-version: Update.
	* Makefile.am (nodist_libffi_la_SOURCES): Add src/debug.c if
	FFI_DEBUG.
	(libffi_la_SOURCES): Remove src/debug.c
	(EXTRA_DIST): Add src/debug.c
	* Makefile.in: Rebuilt.
	* README: Update for 3.0.11.

2011-11-10  Richard Henderson  <<EMAIL>>

	* configure.ac (GCC_AS_CFI_PSEUDO_OP): Use it instead of inline check.
	* configure, aclocal.m4: Rebuild.

2011-09-04  Iain Sandoe  <<EMAIL>>

	PR libffi/49594
	* src/powerpc/darwin_closure.S (stubs): Make the stub binding
	helper reference track the architecture pointer size.

2011-08-25  Andrew Haley  <<EMAIL>>

	* src/arm/ffi.c (FFI_INIT_TRAMPOLINE): Remove hard-coded assembly
	instructions.
	* src/arm/sysv.S (ffi_arm_trampoline): Put them here instead.

2011-07-11  Andrew Haley  <<EMAIL>>

        * src/arm/ffi.c (FFI_INIT_TRAMPOLINE): Clear icache.

2011-06-29  Rainer Orth  <<EMAIL>>

	* testsuite/libffi.call/cls_double_va.c: Move PR number to comment.
	* testsuite/libffi.call/cls_longdouble_va.c: Likewise.

2011-06-29  Rainer Orth  <<EMAIL>>

	PR libffi/46660
	* testsuite/libffi.call/cls_double_va.c: xfail dg-output on
	mips-sgi-irix6*.
	* testsuite/libffi.call/cls_longdouble_va.c: Likewise.

2011-06-14  Rainer Orth  <<EMAIL>>

	* testsuite/libffi.call/huge_struct.c (test_large_fn): Use PRIu8,
	PRId8 instead of %hhu, %hhd.
	* testsuite/libffi.call/ffitest.h [__alpha__ && __osf__] (PRId8,
	PRIu8): Define.
	[__sgi__] (PRId8, PRIu8): Define.

2011-04-29  Rainer Orth  <<EMAIL>>

	* src/alpha/osf.S (UA_SI, FDE_ENCODING, FDE_ENCODE, FDE_ARANGE):
	Define.
	Use them to handle ELF vs. ECOFF differences.
	[__osf__] (_GLOBAL__F_ffi_call_osf): Define.

2011-03-30  Timothy Wall  <<EMAIL>>

	* src/powerpc/darwin.S: Fix unknown FDE encoding.
	* src/powerpc/darwin_closure.S: ditto.

2011-02-25  Anthony Green  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_closure_loc): Allow for more
	32-bit ABIs.

2011-02-15  Anthony Green  <<EMAIL>>

	* m4/ax_cc_maxopt.m4: Don't -malign-double or use -ffast-math.
	* configure: Rebuilt.

2011-02-13  Ralf Wildenhues  <<EMAIL>>

	* configure: Regenerate.

2011-02-13  Anthony Green  <<EMAIL>>

	* include/ffi_common.h (UNLIKELY, LIKELY): Define.
	* src/x86/ffi64.c (UNLIKELY, LIKELY): Remove definition.
	* src/prep_cif.c (UNLIKELY, LIKELY): Remove definition.

	* src/prep_cif.c (initialize_aggregate): Convert assertion into
	FFI_BAD_TYPEDEF return.  Initialize arg size and alignment to 0.

	* src/pa/ffi.c (ffi_prep_closure_loc): Don't ASSERT ABI test,
	just return FFI_BAD_ABI when things are wrong.
	* src/arm/ffi.c (ffi_prep_closure_loc): Ditto.
	* src/powerpc/ffi.c (ffi_prep_closure_loc): Ditto.
	* src/mips/ffi.c (ffi_prep_closure_loc): Ditto.
	* src/ia64/ffi.c (ffi_prep_closure_loc): Ditto.
	* src/avr32/ffi.c (ffi_prep_closure_loc): Ditto.

2011-02-11  Anthony Green  <<EMAIL>>

	* src/sparc/ffi.c (ffi_prep_closure_loc): Don't ASSERT ABI test,
	just return FFI_BAD_ABI when things are wrong.

2012-02-11  Eric Botcazou  <<EMAIL>>

	* src/sparc/v9.S (STACKFRAME): Bump to 176.

2011-02-09  Stuart Shelton  <<EMAIL>>

	http://bugs.gentoo.org/show_bug.cgi?id=286911
	* src/mips/ffitarget.h: Clean up error messages.
	* src/java_raw_api.c (ffi_java_translate_args): Cast raw arg to
	ffi_raw*.
	* include/ffi.h.in: Add pragma for SGI compiler.

2011-02-09  Anthony Green  <<EMAIL>>

	* configure.ac: Add powerpc64-*-darwin* support.

2011-02-09  Anthony Green <<EMAIL>>

	* README: Mention Interix.

2011-02-09  Jonathan Callen  <<EMAIL>>

	* configure.ac: Add Interix to win32/cygwin/mingw case.
	* configure: Ditto.
	* src/closures.c: Treat Interix like Cygwin, instead of as a
	generic win32.

2011-02-09  Anthony Green <<EMAIL>>

	* testsuite/libffi.call/err_bad_typedef.c: Remove xfail.
	* testsuite/libffi.call/err_bad_abi.c: Remove xfail.
	* src/x86/ffi64.c (UNLIKELY, LIKELY): Define.
	(ffi_prep_closure_loc): Check for bad ABI.
	* src/prep_cif.c (UNLIKELY, LIKELY): Define.
	(initialize_aggregate): Check for bad types.

2011-02-09  Landon Fuller <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Add build-ios.sh, src/arm/gentramp.sh,
	src/arm/trampoline.S.
	(nodist_libffi_la_SOURCES): Add src/arc/trampoline.S.
	* configure.ac (FFI_EXEC_TRAMPOLINE_TABLE): Define.
	* src/arm/ffi.c (ffi_trampoline_table)
	(ffi_closure_trampoline_table_page, ffi_trampoline_table_entry)
	(FFI_TRAMPOLINE_CODELOC_CONFIG, FFI_TRAMPOLINE_CONFIG_PAGE_OFFSET)
	(FFI_TRAMPOLINE_COUNT, ffi_trampoline_lock, ffi_trampoline_tables)
	(ffi_trampoline_table_alloc, ffi_closure_alloc, ffi_closure_free):
	Define for FFI_EXEC_TRAMPOLINE_TABLE case (iOS).
	(ffi_prep_closure_loc): Handl FFI_EXEC_TRAMPOLINE_TABLE case
	separately.
	* src/arm/sysv.S: Handle Apple iOS host.
	* src/closures.c: Handle FFI_EXEC_TRAMPOLINE_TABLE case.
	* build-ios.sh: New file.
	* fficonfig.h.in, configure, Makefile.in: Rebuilt.
	* README: Mention ARM iOS.

2011-02-08  Oren Held  <<EMAIL>>

	* src/dlmalloc.c (_STRUCT_MALLINFO): Define in order to avoid
	redefinition of mallinfo on HP-UX.

2011-02-08  Ginn Chen  <<EMAIL>>

	* src/sparc/ffi.c (ffi_call): Make compatible with Solaris Studio
	aggregate return ABI.  Flush cache.
	(ffi_prep_closure_loc): Flush cache.

2011-02-11  Anthony Green  <<EMAIL>>

	From Tom Honermann <<EMAIL>>:
	* src/powerpc/aix.S (ffi_call_AIX): Support for xlc toolchain on
	AIX.  Declare .ffi_prep_args.  Insert nops after branch
	instructions so that the AIX linker can insert TOC reload
	instructions.
	* src/powerpc/aix_closure.S: Declare .ffi_closure_helper_DARWIN.

2011-02-08  Ed  <<EMAIL>>

	* src/powerpc/asm.h: Fix grammar nit in comment.

2011-02-08  Uli Link  <<EMAIL>>

	* include/ffi.h.in (FFI_64_BIT_MAX): Define and use.

2011-02-09  Rainer Orth  <<EMAIL>>

	PR libffi/46661
	* testsuite/libffi.call/cls_pointer.c (main): Cast void * to
	uintptr_t first.
	* testsuite/libffi.call/cls_pointer_stack.c (main): Likewise.

2011-02-08  Rafael Avila de Espindola  <<EMAIL>>

	* configure.ac: Fix x86 test for pc related relocs.
	* configure: Rebuilt.

2011-02-07  Joel Sherrill <<EMAIL>>

	* libffi/src/m68k/ffi.c: Add RTEMS support for cache flushing.
	Handle case when CPU variant does not have long double support.
	* libffi/src/m68k/sysv.S: Add support for mc68000, Coldfire,
	and cores with soft floating point.

2011-02-07  Joel Sherrill <<EMAIL>>

	* configure.ac: Add mips*-*-rtems* support.
	* configure: Regenerate.
	* src/mips/ffitarget.h: Ensure needed constants are available
	for targets which do not have sgidefs.h.

2011-01-26  Dave Korn  <<EMAIL>>

	PR target/40125
	* configure.ac (AM_LTLDFLAGS): Add -bindir option for windows DLLs.
	* configure: Regenerate.

2010-12-18  Iain Sandoe  <<EMAIL>>

	PR libffi/29152
	PR libffi/42378
	* src/powerpc/darwin_closure.S: Provide Darwin64 implementation,
	update comments.
	* src/powerpc/ffitarget.h (POWERPC_DARWIN64): New,
	(FFI_TRAMPOLINE_SIZE): Update for Darwin64.
	* src/powerpc/darwin.S: Provide Darwin64 implementation,
	update comments.
	* src/powerpc/ffi_darwin.c: Likewise.

2010-12-06  Rainer Orth  <<EMAIL>>

	* configure.ac (libffi_cv_as_ascii_pseudo_op): Use double
	backslashes.
	(libffi_cv_as_string_pseudo_op): Likewise.
	* configure: Regenerate.

2010-12-03  Chung-Lin Tang  <<EMAIL>>

	* src/arm/sysv.S (ffi_closure_SYSV): Add UNWIND to .pad directive.
	(ffi_closure_VFP): Same.
	(ffi_call_VFP): Move down to before ffi_closure_VFP. Add '.fpu vfp'
	directive.

2010-12-01  Rainer Orth  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h [__sgi] (PRId64, PRIu64): Define.
	(PRIuPTR): Define.

2010-11-29  Richard Henderson  <<EMAIL>>
	    Rainer Orth  <<EMAIL>>

	* src/x86/sysv.S (FDE_ENCODING, FDE_ENCODE): Define.
	(.eh_frame): Use FDE_ENCODING.
	(.LASFDE1, .LASFDE2, LASFDE3): Simplify with FDE_ENCODE.

2010-11-22  Jacek Caban <<EMAIL>>

	* configure.ac: Check for symbol underscores on mingw-w64.
	* configure: Rebuilt.
	* src/x86/win64.S: Correctly access extern symbols in respect to
	underscores.

2010-11-15  Rainer Orth  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp: Rename ...
	* testsuite/lib/libffi.exp: ... to this.
	* libffi/testsuite/libffi.call/call.exp: Don't load libffi-dg.exp.
	* libffi/testsuite/libffi.special/special.exp: Likewise.

2010-10-28  Chung-Lin Tang  <<EMAIL>>

	* src/arm/ffi.c (ffi_prep_args): Add VFP register argument handling
	code, new parameter, and return value. Update comments.
	(ffi_prep_cif_machdep): Add case for VFP struct return values. Add
	call to layout_vfp_args().
	(ffi_call_SYSV): Update declaration.
	(ffi_call_VFP): New declaration.
	(ffi_call): Add VFP struct return conditions. Call ffi_call_VFP()
	when ABI is FFI_VFP.
	(ffi_closure_VFP): New declaration.
	(ffi_closure_SYSV_inner): Add new vfp_args parameter, update call to
	ffi_prep_incoming_args_SYSV().
	(ffi_prep_incoming_args_SYSV): Update parameters. Add VFP argument
	case handling.
	(ffi_prep_closure_loc): Pass ffi_closure_VFP to trampoline
	construction under VFP hard-float.
	(rec_vfp_type_p): New function.
	(vfp_type_p): Same.
	(place_vfp_arg): Same.
	(layout_vfp_args): Same.
	* src/arm/ffitarget.h (ffi_abi): Add FFI_VFP. Define FFI_DEFAULT_ABI
	based on __ARM_PCS_VFP.
	(FFI_EXTRA_CIF_FIELDS): Define for adding VFP hard-float specific
	fields.
	(FFI_TYPE_STRUCT_VFP_FLOAT): Define internally used type code.
	(FFI_TYPE_STRUCT_VFP_DOUBLE): Same.
	* src/arm/sysv.S (ffi_call_SYSV): Change call of ffi_prep_args() to
	direct call. Move function pointer load upwards.
	(ffi_call_VFP): New function.
	(ffi_closure_VFP): Same.

	* testsuite/lib/libffi-dg.exp (check-flags): New function.
	(dg-skip-if): New function.
	* testsuite/libffi.call/cls_double_va.c: Skip if target is arm*-*-*
	and compiler options include -mfloat-abi=hard.
	* testsuite/libffi.call/cls_longdouble_va.c: Same.

2010-10-01  Jakub Jelinek  <<EMAIL>>

	PR libffi/45677
	* src/x86/ffi64.c (ffi_prep_cif_machdep): Ensure cif->bytes is
	a multiple of 8.
	* testsuite/libffi.call/many2.c: New test.

2010-08-20  Mark Wielaard  <<EMAIL>>

	* src/closures.c (open_temp_exec_file_mnt): Check if getmntent_r
	returns NULL.

2010-08-09  Andreas Tobler  <<EMAIL>>

	* configure.ac: Add target powerpc64-*-freebsd*.
	* configure: Regenerate.
	* testsuite/libffi.call/cls_align_longdouble_split.c: Pass
	-mlong-double-128 only to linux targets.
	* testsuite/libffi.call/cls_align_longdouble_split2.c: Likewise.
	* testsuite/libffi.call/cls_longdouble.c: Likewise.
	* testsuite/libffi.call/huge_struct.c: Likewise.

2010-08-05  Dan Witte  <<EMAIL>>

        * Makefile.am: Pass FFI_DEBUG define to msvcc.sh for linking to the
        debug CRT when --enable-debug is given.
        * configure.ac: Define it.
        * msvcc.sh: Translate -g and -DFFI_DEBUG appropriately.

2010-08-04  Dan Witte  <<EMAIL>>

	* src/x86/ffitarget.h: Add X86_ANY define for all x86/x86_64
	platforms.
	* src/x86/ffi.c: Remove redundant ifdef checks.
	* src/prep_cif.c: Push stack space computation into src/x86/ffi.c
	for X86_ANY so return value space doesn't get added twice.

2010-08-03  Neil Rashbrooke <<EMAIL>>

	* msvcc.sh: Don't pass -safeseh to ml64 because behavior is buggy.

2010-07-22  Dan Witte  <<EMAIL>>

	* src/*/ffitarget.h: Make FFI_LAST_ABI one past the last valid ABI.
	* src/prep_cif.c: Fix ABI assertion.
        * src/cris/ffi.c: Ditto.

2010-07-10  Evan Phoenix  <<EMAIL>>

	* src/closures.c (selinux_enabled_check): Fix strncmp usage bug.

2010-07-07  Dan Horák <<EMAIL>>

	* include/ffi.h.in: Protect #define with #ifndef.
	* src/powerpc/ffitarget.h: Ditto.
	* src/s390/ffitarget.h: Ditto.
	* src/sparc/ffitarget.h: Ditto.

2010-07-07   Neil Roberts <<EMAIL>>

	* src/x86/sysv.S (ffi_call_SYSV): Align the stack pointer to
	16-bytes.

2010-07-02  Jakub Jelinek  <<EMAIL>>

	* Makefile.am (AM_MAKEFLAGS): Pass also mandir to submakes.
	* Makefile.in: Regenerated.

2010-05-19  Rainer Orth  <<EMAIL>>

	* configure.ac (libffi_cv_as_x86_pcrel): Check for illegal in as
	output, too.
	(libffi_cv_as_ascii_pseudo_op): Check for .ascii.
	(libffi_cv_as_string_pseudo_op): Check for .string.
	* configure: Regenerate.
	* fficonfig.h.in: Regenerate.
	* src/x86/sysv.S (.eh_frame): Use .ascii, .string or error.

2010-05-11  Dan Witte  <<EMAIL>>

	* doc/libffi.tex: Document previous change.

2010-05-11  Makoto Kato <<EMAIL>>

	* src/x86/ffi.c (ffi_call): Don't copy structs passed by value.

2010-05-05  Michael Kohler <<EMAIL>>

	* src/dlmalloc.c (dlfree): Fix spelling.
	* src/ia64/ffi.c (ffi_prep_cif_machdep): Ditto.
	* configure.ac: Ditto.
	* configure: Rebuilt.

2010-04-13  Dan Witte  <<EMAIL>>

	* msvcc.sh: Build with -W3 instead of -Wall.
	* src/powerpc/ffi_darwin.c: Remove build warnings.
	* src/x86/ffi.c: Ditto.
	* src/x86/ffitarget.h: Ditto.

2010-04-12  Dan Witte  <<EMAIL>>
	    Walter Meinl <<EMAIL>>

	* configure.ac: Add OS/2 support.
	* configure: Rebuilt.
	* src/closures.c: Ditto.
	* src/dlmalloc.c: Ditto.
	* src/x86/win32.S: Ditto.

2010-04-07  Jakub Jelinek  <<EMAIL>>

	* testsuite/libffi.call/err_bad_abi.c: Remove unused args variable.

2010-04-02  Ralf Wildenhues  <<EMAIL>>

	* Makefile.in: Regenerate.
	* aclocal.m4: Regenerate.
	* include/Makefile.in: Regenerate.
	* man/Makefile.in: Regenerate.
	* testsuite/Makefile.in: Regenerate.

2010-03-30  Dan Witte  <<EMAIL>>

	* msvcc.sh: Disable build warnings.
	* README (tested): Clarify windows build procedure.

2010-03-15  Rainer Orth  <<EMAIL>>

	* configure.ac (libffi_cv_as_x86_64_unwind_section_type): New test.
	* configure: Regenerate.
	* fficonfig.h.in: Regenerate.
	* libffi/src/x86/unix64.S (.eh_frame)
	[HAVE_AS_X86_64_UNWIND_SECTION_TYPE]: Use @unwind section type.

2010-03-14  Matthias Klose  <<EMAIL>>

	* src/x86/ffi64.c: Fix typo in comment.
	* src/x86/ffi.c: Use /* ... */ comment style.

2010-02-24  Rainer Orth  <<EMAIL>>

	* doc/libffi.texi (The Closure API): Fix typo.
	* doc/libffi.info: Remove.

2010-02-15  Matthias Klose  <<EMAIL>>

	* src/arm/sysv.S (__ARM_ARCH__): Define for processor
	__ARM_ARCH_7EM__.

2010-01-15  Anthony Green  <<EMAIL>>

	* README: Add notes on building with Microsoft Visual C++.

2010-01-15  Daniel Witte  <<EMAIL>>

	* msvcc.sh: New file.

	* src/x86/win32.S: Port assembly routines to MSVC and #ifdef.
	* src/x86/ffi.c: Tweak function declaration and remove excess
	parens.
	* include/ffi.h.in: Add __declspec(align(8)) to typedef struct
	ffi_closure.

	* src/x86/ffi.c: Merge ffi_call_SYSV and ffi_call_STDCALL into new
	function ffi_call_win32 on X86_WIN32.
	* src/x86/win32.S (ffi_call_SYSV): Rename to ffi_call_win32.
	(ffi_call_STDCALL): Remove.

	* src/prep_cif.c (ffi_prep_cif): Move stack space allocation code
	to ffi_prep_cif_machdep for x86.
	* src/x86/ffi.c (ffi_prep_cif_machdep): To here.

2010-01-15  Oliver Kiddle  <<EMAIL>>

	* src/x86/ffitarget.h (ffi_abi): Check for __i386 and __amd64 for
	Sun Studio compiler compatibility.

2010-01-12  Conrad Irwin <<EMAIL>>

	* doc/libffi.texi: Add closure example.

2010-01-07  Rainer Orth  <<EMAIL>>

	PR libffi/40701
	* testsuite/libffi.call/ffitest.h [__alpha__ && __osf__] (PRIdLL,
	PRIuLL, PRId64, PRIu64, PRIuPTR): Define.
	* testsuite/libffi.call/cls_align_sint64.c: Add -Wno-format on
	alpha*-dec-osf*.
	* testsuite/libffi.call/cls_align_uint64.c: Likewise.
	* testsuite/libffi.call/cls_ulonglong.c: Likewise.
	* testsuite/libffi.call/return_ll1.c: Likewise.
	* testsuite/libffi.call/stret_medium2.c: Likewise.
	* testsuite/libffi.special/ffitestcxx.h (allocate_mmap): Cast
	MAP_FAILED to char *.

2010-01-06  Rainer Orth  <<EMAIL>>

	* src/mips/n32.S: Use .abicalls and .eh_frame with __GNUC__.

2009-12-31  Anthony Green  <<EMAIL>>

	* README: Update for libffi 3.0.9.

2009-12-27  Matthias Klose  <<EMAIL>>

	* configure.ac (HAVE_LONG_DOUBLE): Define for mips when
	appropriate.
	* configure: Rebuilt.

2009-12-26  Anthony Green  <<EMAIL>>

	* testsuite/libffi.call/cls_longdouble_va.c: Mark as xfail for
	avr32*-*-*.
	* testsuite/libffi.call/cls_double_va.c: Ditto.

2009-12-26  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h: Conditionally include stdint.h
	and inttypes.h.
	* testsuite/libffi.special/unwindtest.cc: Ditto.

2009-12-26  Andreas Tobler  <<EMAIL>>

	* configure.ac: Add amd64-*-openbsd*.
	* configure: Rebuilt.
	* testsuite/lib/libffi-dg.exp (libffi_target_compile): Link
	openbsd programs with -lpthread.

2009-12-26  Anthony Green  <<EMAIL>>

	* testsuite/libffi.call/cls_double_va.c,
	testsuite/libffi.call/cls_longdouble.c,
	testsuite/libffi.call/cls_longdouble_va.c,
	testsuite/libffi.call/cls_pointer.c,
	testsuite/libffi.call/cls_pointer_stack.c: Remove xfail for
	mips*-*-* and arm*-*-*.
	* testsuite/libffi.call/cls_align_longdouble_split.c,
	testsuite/libffi.call/cls_align_longdouble_split2.c,
	testsuite/libffi.call/stret_medium2.c,
	testsuite/libffi.call/stret_medium.c,
	testsuite/libffi.call/stret_large.c,
	testsuite/libffi.call/stret_large2.c: Remove xfail for arm*-*-*.

2009-12-31  Kay Tietz  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h,
	testsuite/libffi.special/ffitestcxx.h (PRIdLL, PRuLL): Fix
	definitions.

2009-12-31  Carlo Bramini  <<EMAIL>>

	* configure.ac (AM_LTLDFLAGS): Define for windows hosts.
	* Makefile.am (libffi_la_LDFLAGS): Add AM_LTLDFLAGS.
	* configure: Rebuilt.
	* Makefile.in: Rebuilt.

2009-12-31  Anthony Green  <<EMAIL>>
	    Blake Chaffin.

	* testsuite/libffi.call/huge_struct.c: New test case from Blake
	Chaffin @ Apple.

2009-12-28  David Edelsohn  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_args): Copy abi and nargs to
	local variables.
	(aix_adjust_aggregate_sizes): New function.
	(ffi_prep_cif_machdep): Call it.

2009-12-26  Andreas Tobler  <<EMAIL>>

	* configure.ac: Define FFI_MMAP_EXEC_WRIT for the given targets.
	* configure: Regenerate.
	* fficonfig.h.in: Likewise.
	* src/closures.c: Remove the FFI_MMAP_EXEC_WRIT definition for
	Solaris/x86.

2009-12-26  Andreas Schwab  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_args_SYSV): Advance intarg_count
	when a float arguments is passed in memory.
	(ffi_closure_helper_SYSV): Mark general registers as used up when
	a 64bit or soft-float long double argument is passed in memory.

2009-12-25  Matthias Klose  <<EMAIL>>

	* man/ffi_call.3: Fix #include in examples.
	* doc/libffi.texi: Add dircategory.

2009-12-25  Frank Everdij <<EMAIL>>

	* include/ffi.h.in: Placed '__GNUC__' ifdef around
	'__attribute__((aligned(8)))' in ffi_closure, fixes compile for
	IRIX MIPSPro c99.
	* include/ffi_common.h: Added '__sgi' define to non
	'__attribute__((__mode__()))' integer typedefs.
	* src/mips/ffi.c (ffi_call, ffi_closure_mips_inner_O32,
	ffi_closure_mips_inner_N32): Added 'defined(_MIPSEB)' to BE check.
	(ffi_closure_mips_inner_O32, ffi_closure_mips_inner_N32): Added
	FFI_LONGDOUBLE support and alignment(N32 only).
	* src/mips/ffitarget.h: Corrected '#include <sgidefs.h>' for IRIX and
	fixed non '__attribute__((__mode__()))' integer typedefs.
	* src/mips/n32.S: Put '#ifdef linux' around '.abicalls' and '.eh_frame'
	since they are Linux/GNU Assembler specific.

2009-12-25  Bradley Smith  <<EMAIL>>

	* configure.ac, Makefile.am, src/avr32/ffi.c,
	src/avr32/ffitarget.h,
	src/avr32/sysv.S: Add AVR32 port.
	* configure, Makefile.in: Rebuilt.

2009-12-21  Andreas Tobler  <<EMAIL>>

	* configure.ac: Make i?86 build on FreeBSD and OpenBSD.
	* configure: Regenerate.

2009-12-15  John David Anglin  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h: Define PRIuPTR on PA HP-UX.

2009-12-13  John David Anglin  <<EMAIL>>

	* src/pa/ffi.c (ffi_closure_inner_pa32): Handle FFI_TYPE_LONGDOUBLE
	type on HP-UX.

2012-02-13  Kai Tietz  <<EMAIL>>

	PR libffi/52221
	* src/x86/ffi.c (ffi_prep_raw_closure_loc): Add thiscall
	support for X86_WIN32.
	(FFI_INIT_TRAMPOLINE_THISCALL): Fix displacement.

2009-12-11  Eric Botcazou  <<EMAIL>>

	* src/sparc/ffi.c (ffi_closure_sparc_inner_v9): Properly align 'long
	double' arguments.

2009-12-11  Eric Botcazou  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h: Define PRIuPTR on Solaris < 10.

2009-12-10  Rainer Orth  <<EMAIL>>

	PR libffi/40700
	* src/closures.c [X86_64 && __sun__ && __svr4__]
	(FFI_MMAP_EXEC_WRIT): Define.

2009-12-08  David Daney  <<EMAIL>>

	* testsuite/libffi.call/stret_medium.c: Remove xfail for mips*-*-*
	* testsuite/libffi.call/cls_align_longdouble_split2.c: Same.
	* testsuite/libffi.call/stret_large.c: Same.
	* testsuite/libffi.call/cls_align_longdouble_split.c: Same.
	* testsuite/libffi.call/stret_large2.c: Same.
	* testsuite/libffi.call/stret_medium2.c: Same.

2009-12-07  David Edelsohn  <<EMAIL>>

	* src/powerpc/aix_closure.S (libffi_closure_ASM): Fix tablejump
	typo.

2009-12-05  David Edelsohn  <<EMAIL>>

	* src/powerpc/aix.S: Update AIX32 code to be consistent with AIX64
	code.
	* src/powerpc/aix_closure.S: Same.

2009-12-05  Ralf Wildenhues  <<EMAIL>>

	* Makefile.in: Regenerate.
	* configure: Regenerate.
	* include/Makefile.in: Regenerate.
	* man/Makefile.in: Regenerate.
	* testsuite/Makefile.in: Regenerate.

2009-12-04  David Edelsohn  <<EMAIL>>

	* src/powerpc/aix_closure.S: Reorganize 64-bit code to match
	linux64_closure.S.

2009-12-04  Uros Bizjak  <<EMAIL>>

	PR libffi/41908
	* src/x86/ffi64.c (classify_argument): Update from
	gcc/config/i386/i386.c.
	(ffi_closure_unix64_inner): Do not use the address of two consecutive
	SSE registers directly.
	* testsuite/libffi.call/cls_dbls_struct.c (main): Remove xfail
	for x86_64 linux targets.

2009-12-04  David Edelsohn  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_closure_helper_DARWIN): Increment
	pfr for long double split between fpr13 and stack.

2009-12-03  David Edelsohn  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_args): Increment next_arg and
	fparg_count twice for long double.

2009-12-03  David Edelsohn  <<EMAIL>>

	PR libffi/42243
	* src/powerpc/ffi_darwin.c (ffi_prep_args): Remove extra parentheses.

2009-12-03  Uros Bizjak  <<EMAIL>>

	* testsuite/libffi.call/cls_longdouble_va.c (main): Fix format string.
	Remove xfails for x86 linux targets.

2009-12-02  David Edelsohn  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_args): Fix typo in INT64
	case.

2009-12-01  David Edelsohn  <<EMAIL>>

	* src/powerpc/aix.S (ffi_call_AIX): Convert to more standard
	register usage.  Call ffi_prep_args directly.  Add long double
	return value support.
	* src/powerpc/ffi_darwin.c (ffi_prep_args): Double arg increment
	applies to FFI_TYPE_DOUBLE.  Correct fpr_base increment typo.
	Separate FFI_TYPE_SINT32 and FFI_TYPE_UINT32 cases.
	(ffi_prep_cif_machdep): Only 16 byte stack alignment in 64 bit
	mode.
	(ffi_closure_helper_DARWIN): Remove nf and ng counters.  Move temp
	into case.
	* src/powerpc/aix_closure.S: Maintain 16 byte stack alignment.
	Allocate result area between params and FPRs.

2009-11-30  David Edelsohn  <<EMAIL>>

	PR target/35484
	* src/powerpc/ffitarget.h (POWERPC64): Define for PPC64 Linux and
	AIX64.
	* src/powerpc/aix.S: Implement AIX64 version.
	* src/powerpc/aix_closure.S: Implement AIX64 version.
	(ffi_closure_ASM): Use extsb, lha and displament addresses.
	* src/powerpc/ffi_darwin.c (ffi_prep_args): Implement AIX64
	support.
	(ffi_prep_cif_machdep): Same.
	(ffi_call): Same.
	(ffi_closure_helper_DARWIN): Same.

2009-11-02  Andreas Tobler  <<EMAIL>>

	PR libffi/41908
	* testsuite/libffi.call/testclosure.c: New test.

2009-09-28  Kai Tietz  <<EMAIL>>

	* src/x86/win64.S (_ffi_call_win64 stack): Remove for gnu
	assembly version use of ___chkstk.

2009-09-23  Matthias Klose  <<EMAIL>>

	PR libffi/40242, PR libffi/41443
	* src/arm/sysv.S (__ARM_ARCH__): Define for processors
	__ARM_ARCH_6T2__, __ARM_ARCH_6M__, __ARM_ARCH_7__,
	__ARM_ARCH_7A__, __ARM_ARCH_7R__, __ARM_ARCH_7M__.
	Change the conditionals to __SOFTFP__ || __ARM_EABI__
	for -mfloat-abi=softfp to work.

2009-09-17  Loren J. Rittle  <<EMAIL>>

	PR testsuite/32843 (strikes again)
	* src/x86/ffi.c (ffi_prep_cif_machdep): Add X86_FREEBSD to
	enable proper extension on char and short.

2009-09-15  David Daney  <<EMAIL>>

	* src/java_raw_api.c (ffi_java_raw_to_rvalue): Remove special
	handling for FFI_TYPE_POINTER.
	* src/mips/ffitarget.h (FFI_TYPE_STRUCT_D_SOFT,
	FFI_TYPE_STRUCT_F_SOFT, FFI_TYPE_STRUCT_DD_SOFT,
	FFI_TYPE_STRUCT_FF_SOFT, FFI_TYPE_STRUCT_FD_SOFT,
	FFI_TYPE_STRUCT_DF_SOFT, FFI_TYPE_STRUCT_SOFT): New defines.
	(FFI_N32_SOFT_FLOAT, FFI_N64_SOFT_FLOAT): New ffi_abi enumerations.
	(enum ffi_abi): Set FFI_DEFAULT_ABI for soft-float.
	* src/mips/n32.S (ffi_call_N32): Add handling for soft-float
	structure and pointer returns.
	(ffi_closure_N32): Add handling for pointer returns.
	* src/mips/ffi.c (ffi_prep_args, calc_n32_struct_flags,
	calc_n32_return_struct_flags): Handle soft-float.
	(ffi_prep_cif_machdep):  Handle soft-float, fix pointer handling.
	(ffi_call_N32): Declare proper argument types.
	(ffi_call, copy_struct_N32, ffi_closure_mips_inner_N32): Handle
	soft-float.

2009-08-24  Ralf Wildenhues  <<EMAIL>>

	* configure.ac (AC_PREREQ): Bump to 2.64.

2009-08-22  Ralf Wildenhues  <<EMAIL>>

	* Makefile.am (install-html, install-pdf): Remove.
	* Makefile.in: Regenerate.

	* Makefile.in: Regenerate.
	* aclocal.m4: Regenerate.
	* configure: Regenerate.
	* fficonfig.h.in: Regenerate.
	* include/Makefile.in: Regenerate.
	* man/Makefile.in: Regenerate.
	* testsuite/Makefile.in: Regenerate.

2011-08-22  Jasper Lievisse Adriaanse <<EMAIL>>

	* configure.ac: Add OpenBSD/hppa and OpenBSD/powerpc support.
	* configure: Rebuilt.

2009-07-30  Ralf Wildenhues  <<EMAIL>>

	* configure.ac (_AC_ARG_VAR_PRECIOUS): Use m4_rename_force.

2009-07-24  Dave Korn  <<EMAIL>>

	PR libffi/40807
	* src/x86/ffi.c (ffi_prep_cif_machdep): Also use sign/zero-extending
	return types for X86_WIN32.
	* src/x86/win32.S (_ffi_call_SYSV): Handle omitted return types.
	(_ffi_call_STDCALL, _ffi_closure_SYSV, _ffi_closure_raw_SYSV,
	_ffi_closure_STDCALL): Likewise.

	* src/closures.c (is_selinux_enabled): Define to const 0 for Cygwin.
	(dlmmap, dlmunmap): Also use these functions on Cygwin.

2009-07-11  Richard Sandiford  <<EMAIL>>

	PR testsuite/40699
	PR testsuite/40707
	PR testsuite/40709
	* testsuite/lib/libffi-dg.exp: Revert 2009-07-02, 2009-07-01 and
	2009-06-30 commits.

2009-07-01  Richard Sandiford  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp (libffi-init): Set ld_library_path
	to "" before adding paths.  (This reinstates an assignment that
	was removed by my 2009-06-30 commit, but changes the initial
	value from "." to "".)

2009-07-01  H.J. Lu  <<EMAIL>>

	PR testsuite/40601
	* testsuite/lib/libffi-dg.exp (libffi-init): Properly set
	gccdir.  Adjust ld_library_path for gcc only if gccdir isn't
	empty.

2009-06-30  Richard Sandiford  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp (libffi-init): Don't add "."
	to ld_library_path.  Use add_path.  Add just find_libgcc_s
	to ld_library_path, not every libgcc multilib directory.

2009-06-16  Wim Lewis  <<EMAIL>>

	* src/powerpc/ffi.c: Avoid clobbering cr3 and cr4, which are
	supposed to be callee-saved.
	* src/powerpc/sysv.S (small_struct_return_value): Fix overrun of
	return buffer for odd-size structs.

2009-06-16  Andreas Tobler  <<EMAIL>>

	PR libffi/40444
	* testsuite/lib/libffi-dg.exp (libffi_target_compile): Add
	allow_stack_execute for Darwin.

2009-06-16  Andrew Haley  <<EMAIL>>

	* configure.ac (TARGETDIR): Add missing blank lines.
	* configure: Regenerate.

2009-06-16  Andrew Haley  <<EMAIL>>

	* testsuite/libffi.call/cls_align_sint64.c,
	testsuite/libffi.call/cls_align_uint64.c,
	testsuite/libffi.call/cls_longdouble_va.c,
	testsuite/libffi.call/cls_ulonglong.c,
	testsuite/libffi.call/return_ll1.c,
	testsuite/libffi.call/stret_medium2.c: Fix printf format
	specifiers.
	* testsuite/libffi.call/ffitest.h,
	testsuite/libffi.special/ffitestcxx.h (PRIdLL, PRIuLL): Define.

2009-06-15  Andrew Haley  <<EMAIL>>

	* testsuite/libffi.call/err_bad_typedef.c: xfail everywhere.
	* testsuite/libffi.call/err_bad_abi.c: Likewise.

2009-06-12  Andrew Haley  <<EMAIL>>

	* Makefile.am: Remove info_TEXINFOS.

2009-06-12  Andrew Haley  <<EMAIL>>

	* ChangeLog.libffi: testsuite/libffi.call/cls_align_sint64.c,
	testsuite/libffi.call/cls_align_uint64.c,
	testsuite/libffi.call/cls_ulonglong.c,
	testsuite/libffi.call/return_ll1.c,
	testsuite/libffi.call/stret_medium2.c: Fix printf format
	specifiers.
	testsuite/libffi.special/unwindtest.cc: include stdint.h.

2009-06-11  Timothy Wall  <<EMAIL>>

	* Makefile.am,
	configure.ac,
	include/ffi.h.in,
	include/ffi_common.h,
	src/closures.c,
	src/dlmalloc.c,
	src/x86/ffi.c,
	src/x86/ffitarget.h,
	src/x86/win64.S (new),
	README: Added win64 support (mingw or MSVC)
	* Makefile.in,
	include/Makefile.in,
	man/Makefile.in,
	testsuite/Makefile.in,
	configure,
	aclocal.m4: Regenerated
	* ltcf-c.sh: properly escape cygwin/w32 path
	* man/ffi_call.3: Clarify size requirements for return value.
	* src/x86/ffi64.c: Fix filename in comment.
	* src/x86/win32.S: Remove unused extern.

	* testsuite/libffi.call/closure_fn0.c,
	testsuite/libffi.call/closure_fn1.c,
	testsuite/libffi.call/closure_fn2.c,
	testsuite/libffi.call/closure_fn3.c,
	testsuite/libffi.call/closure_fn4.c,
	testsuite/libffi.call/closure_fn5.c,
	testsuite/libffi.call/closure_fn6.c,
	testsuite/libffi.call/closure_stdcall.c,
	testsuite/libffi.call/cls_12byte.c,
	testsuite/libffi.call/cls_16byte.c,
	testsuite/libffi.call/cls_18byte.c,
	testsuite/libffi.call/cls_19byte.c,
	testsuite/libffi.call/cls_1_1byte.c,
	testsuite/libffi.call/cls_20byte.c,
	testsuite/libffi.call/cls_20byte1.c,
	testsuite/libffi.call/cls_24byte.c,
	testsuite/libffi.call/cls_2byte.c,
	testsuite/libffi.call/cls_3_1byte.c,
	testsuite/libffi.call/cls_3byte1.c,
 	testsuite/libffi.call/cls_3byte2.c,
 	testsuite/libffi.call/cls_4_1byte.c,
 	testsuite/libffi.call/cls_4byte.c,
 	testsuite/libffi.call/cls_5_1_byte.c,
 	testsuite/libffi.call/cls_5byte.c,
 	testsuite/libffi.call/cls_64byte.c,
 	testsuite/libffi.call/cls_6_1_byte.c,
 	testsuite/libffi.call/cls_6byte.c,
 	testsuite/libffi.call/cls_7_1_byte.c,
 	testsuite/libffi.call/cls_7byte.c,
 	testsuite/libffi.call/cls_8byte.c,
 	testsuite/libffi.call/cls_9byte1.c,
 	testsuite/libffi.call/cls_9byte2.c,
 	testsuite/libffi.call/cls_align_double.c,
 	testsuite/libffi.call/cls_align_float.c,
 	testsuite/libffi.call/cls_align_longdouble.c,
 	testsuite/libffi.call/cls_align_longdouble_split.c,
 	testsuite/libffi.call/cls_align_longdouble_split2.c,
 	testsuite/libffi.call/cls_align_pointer.c,
 	testsuite/libffi.call/cls_align_sint16.c,
 	testsuite/libffi.call/cls_align_sint32.c,
 	testsuite/libffi.call/cls_align_sint64.c,
 	testsuite/libffi.call/cls_align_uint16.c,
 	testsuite/libffi.call/cls_align_uint32.c,
 	testsuite/libffi.call/cls_align_uint64.c,
 	testsuite/libffi.call/cls_dbls_struct.c,
 	testsuite/libffi.call/cls_double.c,
 	testsuite/libffi.call/cls_double_va.c,
 	testsuite/libffi.call/cls_float.c,
 	testsuite/libffi.call/cls_longdouble.c,
 	testsuite/libffi.call/cls_longdouble_va.c,
 	testsuite/libffi.call/cls_multi_schar.c,
 	testsuite/libffi.call/cls_multi_sshort.c,
 	testsuite/libffi.call/cls_multi_sshortchar.c,
 	testsuite/libffi.call/cls_multi_uchar.c,
 	testsuite/libffi.call/cls_multi_ushort.c,
 	testsuite/libffi.call/cls_multi_ushortchar.c,
 	testsuite/libffi.call/cls_pointer.c,
 	testsuite/libffi.call/cls_pointer_stack.c,
 	testsuite/libffi.call/cls_schar.c,
 	testsuite/libffi.call/cls_sint.c,
 	testsuite/libffi.call/cls_sshort.c,
 	testsuite/libffi.call/cls_uchar.c,
 	testsuite/libffi.call/cls_uint.c,
 	testsuite/libffi.call/cls_ulonglong.c,
 	testsuite/libffi.call/cls_ushort.c,
 	testsuite/libffi.call/err_bad_abi.c,
 	testsuite/libffi.call/err_bad_typedef.c,
 	testsuite/libffi.call/float2.c,
 	testsuite/libffi.call/huge_struct.c,
 	testsuite/libffi.call/nested_struct.c,
 	testsuite/libffi.call/nested_struct1.c,
 	testsuite/libffi.call/nested_struct10.c,
 	testsuite/libffi.call/nested_struct2.c,
 	testsuite/libffi.call/nested_struct3.c,
 	testsuite/libffi.call/nested_struct4.c,
 	testsuite/libffi.call/nested_struct5.c,
 	testsuite/libffi.call/nested_struct6.c,
 	testsuite/libffi.call/nested_struct7.c,
 	testsuite/libffi.call/nested_struct8.c,
 	testsuite/libffi.call/nested_struct9.c,
 	testsuite/libffi.call/problem1.c,
 	testsuite/libffi.call/return_ldl.c,
 	testsuite/libffi.call/return_ll1.c,
 	testsuite/libffi.call/stret_large.c,
 	testsuite/libffi.call/stret_large2.c,
 	testsuite/libffi.call/stret_medium.c,
 	testsuite/libffi.call/stret_medium2.c,
	testsuite/libffi.special/unwindtest.cc: use ffi_closure_alloc instead
	of checking for MMAP.  Use intptr_t instead of long casts.

2009-06-11  Kaz Kojima  <<EMAIL>>

	* testsuite/libffi.call/cls_longdouble_va.c: Add xfail sh*-*-linux-*.
	* testsuite/libffi.call/err_bad_abi.c: Add xfail sh*-*-*.
	* testsuite/libffi.call/err_bad_typedef.c: Likewise.

2009-06-09  Andrew Haley  <<EMAIL>>

	* src/x86/freebsd.S: Add missing file.

2009-06-08  Andrew Haley  <<EMAIL>>

	Import from libffi 3.0.8:

	* doc/libffi.texi: New file.
	* doc/libffi.info: Likewise.
	* doc/stamp-vti: Likewise.
	* man/Makefile.am: New file.
	* man/ffi_call.3: New file.

	* Makefile.am (EXTRA_DIST): Add src/x86/darwin64.S,
	src/dlmalloc.c.
	(nodist_libffi_la_SOURCES): Add X86_FREEBSD.

	* configure.ac: Bump version to 3.0.8.
	parisc*-*-linux*: Add.
	i386-*-freebsd* | i386-*-openbsd*: Add.
	powerpc-*-beos*: Add.
	AM_CONDITIONAL X86_FREEBSD: Add.
	AC_CONFIG_FILES: Add man/Makefile.

	* include/ffi.h.in (FFI_FN): Change void (*)() to void (*)(void).

2009-06-08  Andrew Haley  <<EMAIL>>

	* README: Import from libffi 3.0.8.

2009-06-08  Andrew Haley  <<EMAIL>>

	* testsuite/libffi.call/err_bad_abi.c: Add xfails.
	* testsuite/libffi.call/cls_longdouble_va.c: Add xfails.
	* testsuite/libffi.call/cls_dbls_struct.c: Add xfail x86_64-*-linux-*.
	* testsuite/libffi.call/err_bad_typedef.c: Add xfails.

	* testsuite/libffi.call/stret_medium2.c: Add __UNUSED__ to args.
	* testsuite/libffi.call/stret_medium.c: Likewise.
	* testsuite/libffi.call/stret_large2.c: Likewise.
	* testsuite/libffi.call/stret_large.c:  Likewise.

2008-12-26  Timothy Wall  <<EMAIL>>

	* testsuite/libffi.call/cls_longdouble.c,
	testsuite/libffi.call/cls_longdouble_va.c,
	testsuite/libffi.call/cls_align_longdouble.c,
	testsuite/libffi.call/cls_align_longdouble_split.c,
	testsuite/libffi.call/cls_align_longdouble_split2.c: mark expected
	failures on x86_64 cygwin/mingw.

2008-12-22  Timothy Wall  <<EMAIL>>

	* testsuite/libffi.call/closure_fn0.c,
	testsuite/libffi.call/closure_fn1.c,
	testsuite/libffi.call/closure_fn2.c,
	testsuite/libffi.call/closure_fn3.c,
	testsuite/libffi.call/closure_fn4.c,
	testsuite/libffi.call/closure_fn5.c,
	testsuite/libffi.call/closure_fn6.c,
	testsuite/libffi.call/closure_loc_fn0.c,
	testsuite/libffi.call/closure_stdcall.c,
	testsuite/libffi.call/cls_align_pointer.c,
	testsuite/libffi.call/cls_pointer.c,
	testsuite/libffi.call/cls_pointer_stack.c: use portable cast from
	pointer to integer (intptr_t).
	* testsuite/libffi.call/cls_longdouble.c: disable for win64.

2008-07-24  Anthony Green  <<EMAIL>>

	* testsuite/libffi.call/cls_dbls_struct.c,
	testsuite/libffi.call/cls_double_va.c,
	testsuite/libffi.call/cls_longdouble.c,
	testsuite/libffi.call/cls_longdouble_va.c,
	testsuite/libffi.call/cls_pointer.c,
	testsuite/libffi.call/cls_pointer_stack.c,
	testsuite/libffi.call/err_bad_abi.c: Clean up failures from
	compiler warnings.

2008-03-04  Anthony Green  <<EMAIL>>
	    Blake Chaffin
	    <EMAIL>

	* testsuite/libffi.call/cls_align_longdouble_split2.c
	  testsuite/libffi.call/cls_align_longdouble_split.c
	  testsuite/libffi.call/cls_dbls_struct.c
	  testsuite/libffi.call/cls_double_va.c
	  testsuite/libffi.call/cls_longdouble.c
	  testsuite/libffi.call/cls_longdouble_va.c
	  testsuite/libffi.call/cls_pointer.c
	  testsuite/libffi.call/cls_pointer_stack.c
	  testsuite/libffi.call/err_bad_abi.c
	  testsuite/libffi.call/err_bad_typedef.c
	  testsuite/libffi.call/stret_large2.c
	  testsuite/libffi.call/stret_large.c
	  testsuite/libffi.call/stret_medium2.c
	  testsuite/libffi.call/stret_medium.c: New tests from Apple.

2009-06-05  Andrew Haley  <<EMAIL>>

	* src/x86/ffitarget.h, src/x86/ffi.c: Merge stdcall changes from
	libffi.

2009-06-04  Andrew Haley  <<EMAIL>>

	* src/x86/ffitarget.h, src/x86/win32.S, src/x86/ffi.c: Back out
	stdcall changes.

2008-02-26  Anthony Green  <<EMAIL>>
	    Thomas Heller  <<EMAIL>>

	* src/x86/ffi.c (ffi_closure_SYSV_inner): Change C++ comment to C
	comment.

2008-02-03  Timothy Wall  <<EMAIL>>

	* src/x86/ffi.c (FFI_INIT_TRAMPOLINE_STDCALL): Calculate jump return
	  offset based on code pointer, not data pointer.

2008-01-31  Timothy Wall <<EMAIL>>

	* testsuite/libffi.call/closure_stdcall.c: Add test for stdcall
	closures.
	* src/x86/ffitarget.h: Increase size of trampoline for stdcall
	closures.
	* src/x86/win32.S: Add assembly for stdcall closure.
	* src/x86/ffi.c: Initialize stdcall closure trampoline.

2009-06-04  Andrew Haley  <<EMAIL>>

	* include/ffi.h.in: Change void (*)() to void (*)(void).
	* src/x86/ffi.c: Likewise.

2009-06-04  Andrew Haley  <<EMAIL>>

	* src/powerpc/ppc_closure.S: Insert licence header.
	* src/powerpc/linux64_closure.S: Likewise.
	* src/m68k/sysv.S: Likewise.

	* src/sh64/ffi.c: Change void (*)() to void (*)(void).
	* src/powerpc/ffi.c: Likewise.
	* src/powerpc/ffi_darwin.c: Likewise.
	* src/m32r/ffi.c: Likewise.
	* src/sh64/ffi.c: Likewise.
	* src/x86/ffi64.c: Likewise.
	* src/alpha/ffi.c: Likewise.
	* src/alpha/osf.S: Likewise.
	* src/frv/ffi.c: Likewise.
	* src/s390/ffi.c: Likewise.
	* src/pa/ffi.c: Likewise.
	* src/pa/hpux32.S: Likewise.
	* src/ia64/unix.S: Likewise.
	* src/ia64/ffi.c: Likewise.
	* src/sparc/ffi.c: Likewise.
	* src/mips/ffi.c: Likewise.
	* src/sh/ffi.c: Likewise.

2008-02-15  David Daney  <<EMAIL>>

	* src/mips/ffi.c (USE__BUILTIN___CLEAR_CACHE):
	Define (conditionally), and use it to include cachectl.h.
	(ffi_prep_closure_loc): Fix cache flushing.
	* src/mips/ffitarget.h (_ABIN32, _ABI64, _ABIO32): Define.

2009-06-04  Andrew Haley  <<EMAIL>>

	include/ffi.h.in,
	src/arm/ffitarget.h,
	src/arm/ffi.c,
	src/arm/sysv.S,
	src/powerpc/ffitarget.h,
	src/closures.c,
	src/sh64/ffitarget.h,
	src/sh64/ffi.c,
	src/sh64/sysv.S,
	src/types.c,
	src/x86/ffi64.c,
	src/x86/ffitarget.h,
	src/x86/win32.S,
	src/x86/darwin.S,
	src/x86/ffi.c,
	src/x86/sysv.S,
	src/x86/unix64.S,
	src/alpha/ffitarget.h,
	src/alpha/ffi.c,
	src/alpha/osf.S,
	src/m68k/ffitarget.h,
	src/frv/ffitarget.h,
	src/frv/ffi.c,
	src/s390/ffitarget.h,
	src/s390/sysv.S,
	src/cris/ffitarget.h,
	src/pa/linux.S,
	src/pa/ffitarget.h,
	src/pa/ffi.c,
	src/raw_api.c,
	src/ia64/ffitarget.h,
	src/ia64/unix.S,
	src/ia64/ffi.c,
	src/ia64/ia64_flags.h,
	src/java_raw_api.c,
	src/debug.c,
	src/sparc/v9.S,
	src/sparc/ffitarget.h,
	src/sparc/ffi.c,
	src/sparc/v8.S,
	src/mips/ffitarget.h,
	src/mips/n32.S,
	src/mips/o32.S,
	src/mips/ffi.c,
	src/prep_cif.c,
	src/sh/ffitarget.h,
	src/sh/ffi.c,
	src/sh/sysv.S: Update license text.

2009-05-22  Dave Korn  <<EMAIL>>

	* src/x86/win32.S (_ffi_closure_STDCALL):  New function.
	(.eh_frame):  Add FDE for it.

2009-05-22  Dave Korn  <<EMAIL>>

	* configure.ac:  Also check if assembler supports pc-relative
	relocs on X86_WIN32 targets.
	* configure:  Regenerate.
	* src/x86/win32.S (ffi_prep_args):  Declare extern, not global.
	(_ffi_call_SYSV):  Add missing function type symbol .def and
	add EH markup labels.
	(_ffi_call_STDCALL):  Likewise.
	(_ffi_closure_SYSV):  Likewise.
	(_ffi_closure_raw_SYSV):  Likewise.
	(.eh_frame):  Add hand-crafted EH data.

2009-04-09  Jakub Jelinek  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp: Change copyright header to refer to
	version 3 of the GNU General Public License and to point readers
	at the COPYING3 file and the FSF's license web page.
	* testsuite/libffi.call/call.exp: Likewise.
	* testsuite/libffi.special/special.exp: Likewise.

2009-03-01  Ralf Wildenhues  <<EMAIL>>

	* configure: Regenerate.

2008-12-18  Rainer Orth  <<EMAIL>>

	PR libffi/26048
	* configure.ac (HAVE_AS_X86_PCREL): New test.
	* configure: Regenerate.
	* fficonfig.h.in: Regenerate.
	* src/x86/sysv.S [!FFI_NO_RAW_API]: Precalculate
	RAW_CLOSURE_CIF_OFFSET, RAW_CLOSURE_FUN_OFFSET,
	RAW_CLOSURE_USER_DATA_OFFSET for the Solaris 10/x86 assembler.
	(.eh_frame): Only use SYMBOL-. iff HAVE_AS_X86_PCREL.
	* src/x86/unix64.S (.Lstore_table): Move to .text section.
	(.Lload_table): Likewise.
	(.eh_frame): Only use SYMBOL-. iff HAVE_AS_X86_PCREL.

2008-12-18  Ralf Wildenhues  <<EMAIL>>

	* configure: Regenerate.

2008-11-21  Eric Botcazou  <<EMAIL>>

	* src/sparc/ffi.c (ffi_prep_cif_machdep): Add support for
	signed/unsigned int8/16 return values.
	* src/sparc/v8.S (ffi_call_v8): Likewise.
	(ffi_closure_v8): Likewise.

2008-09-26  Peter O'Gorman  <<EMAIL>>
	    Steve Ellcey  <<EMAIL>>

	* configure: Regenerate for new libtool.
	* Makefile.in: Ditto.
	* include/Makefile.in: Ditto.
	* aclocal.m4: Ditto.

2008-08-25  Andreas Tobler  <<EMAIL>>

	* src/powerpc/ffitarget.h (ffi_abi): Add FFI_LINUX and
	FFI_LINUX_SOFT_FLOAT to the POWERPC_FREEBSD enum.
	Add note about flag bits used for FFI_SYSV_TYPE_SMALL_STRUCT.
	Adjust copyright notice.
	* src/powerpc/ffi.c: Add two new flags to indicate if we have one
	register or two register to use for FFI_SYSV structs.
	(ffi_prep_cif_machdep): Pass the right register flag introduced above.
	(ffi_closure_helper_SYSV): Fix the return type for
	FFI_SYSV_TYPE_SMALL_STRUCT. Comment.
	Adjust copyright notice.

2008-07-16  Kaz Kojima  <<EMAIL>>

	* src/sh/ffi.c (ffi_prep_closure_loc): Turn INSN into an unsigned
	int.

2008-06-17  Ralf Wildenhues  <<EMAIL>>

	* configure: Regenerate.
	* include/Makefile.in: Regenerate.
	* testsuite/Makefile.in: Regenerate.

2008-06-07  Joseph Myers  <<EMAIL>>

	* configure.ac (parisc*-*-linux*, powerpc-*-sysv*,
	powerpc-*-beos*): Remove.
	* configure: Regenerate.

2008-05-09  Julian Brown  <<EMAIL>>

	* Makefile.am (LTLDFLAGS): New.
	(libffi_la_LDFLAGS): Use above.
	* Makefile.in: Regenerate.

2008-04-18  Paolo Bonzini  <<EMAIL>>

	PR bootstrap/35457
	* aclocal.m4: Regenerate.
	* configure: Regenerate.

2008-03-26  Kaz Kojima  <<EMAIL>>

	* src/sh/sysv.S: Add .note.GNU-stack on Linux.
	* src/sh64/sysv.S: Likewise.

2008-03-26  Daniel Jacobowitz  <<EMAIL>>

	* src/arm/sysv.S: Fix ARM comment marker.

2008-03-26  Jakub Jelinek  <<EMAIL>>

	* src/alpha/osf.S: Add .note.GNU-stack on Linux.
	* src/s390/sysv.S: Likewise.
	* src/powerpc/ppc_closure.S: Likewise.
	* src/powerpc/sysv.S: Likewise.
	* src/x86/unix64.S: Likewise.
	* src/x86/sysv.S: Likewise.
	* src/sparc/v8.S: Likewise.
	* src/sparc/v9.S: Likewise.
	* src/m68k/sysv.S: Likewise.
	* src/arm/sysv.S: Likewise.

2008-03-16  Ralf Wildenhues  <<EMAIL>>

	* aclocal.m4: Regenerate.
	* configure: Likewise.
	* Makefile.in: Likewise.
	* include/Makefile.in: Likewise.
	* testsuite/Makefile.in: Likewise.

2008-02-12  Bjoern Koenig  <<EMAIL>>
	    Andreas Tobler  <<EMAIL>>

	* configure.ac: Add amd64-*-freebsd* target.
	* configure: Regenerate.

2008-01-30  H.J. Lu  <<EMAIL>>

	PR libffi/34612
	* src/x86/sysv.S (ffi_closure_SYSV): Pop 4 byte from stack when
	returning struct.

	* testsuite/libffi.call/call.exp: Add "-O2 -fomit-frame-pointer"
	tests.

2008-01-24  David Edelsohn  <<EMAIL>>

	* configure: Regenerate.

2008-01-06  Andreas Tobler  <<EMAIL>>

	* src/x86/ffi.c (ffi_prep_cif_machdep): Fix thinko.

2008-01-05  Andreas Tobler  <<EMAIL>>

	PR testsuite/32843
	* src/x86/ffi.c (ffi_prep_cif_machdep): Add code for
	signed/unsigned int8/16 for X86_DARWIN.
	Updated copyright info.
	Handle one and two byte structs with special cif->flags.
	* src/x86/ffitarget.h: Add special types for one and two byte structs.
	Updated copyright info.
	* src/x86/darwin.S (ffi_call_SYSV): Rewrite to use a jump table like
	sysv.S
	Remove code to pop args from the stack after call.
	Special-case signed/unsigned for int8/16, one and two byte structs.
	(ffi_closure_raw_SYSV): Handle FFI_TYPE_UINT8,
	FFI_TYPE_SINT8, FFI_TYPE_UINT16, FFI_TYPE_SINT16, FFI_TYPE_UINT32,
	FFI_TYPE_SINT32.
	Updated copyright info.

2007-12-08  David Daney  <<EMAIL>>

	* src/mips/n32.S (ffi_call_N32):  Replace dadd with ADDU, dsub with
	SUBU, add with ADDU and use smaller code sequences.

2007-12-07  David Daney  <<EMAIL>>

	* src/mips/ffi.c (ffi_prep_cif_machdep): Handle long double return
	type.

2007-12-06  David Daney  <<EMAIL>>

	* include/ffi.h.in (FFI_SIZEOF_JAVA_RAW): Define if not	already
	defined.
	(ffi_java_raw): New typedef.
	(ffi_java_raw_call, ffi_java_ptrarray_to_raw,
	ffi_java_raw_to_ptrarray): Change parameter types from ffi_raw to
	ffi_java_raw.
	(ffi_java_raw_closure) : Same.
	(ffi_prep_java_raw_closure, ffi_prep_java_raw_closure_loc): Change
	parameter types.
	* src/java_raw_api.c (ffi_java_raw_size):  Replace FFI_SIZEOF_ARG with
	FFI_SIZEOF_JAVA_RAW.
	(ffi_java_raw_to_ptrarray): Change type of raw to ffi_java_raw.
	Replace FFI_SIZEOF_ARG with FFI_SIZEOF_JAVA_RAW. Use
	sizeof(ffi_java_raw) for alignment calculations.
	(ffi_java_ptrarray_to_raw): Same.
	(ffi_java_rvalue_to_raw): Add special handling for FFI_TYPE_POINTER
	if FFI_SIZEOF_JAVA_RAW == 4.
	(ffi_java_raw_to_rvalue): Same.
	(ffi_java_raw_call): Change type of raw to ffi_java_raw.
	(ffi_java_translate_args): Same.
	(ffi_prep_java_raw_closure_loc, ffi_prep_java_raw_closure): Change
	parameter types.
	* src/mips/ffitarget.h (FFI_SIZEOF_JAVA_RAW): Define for N32 ABI.

2007-12-06  David Daney  <<EMAIL>>

	* src/mips/n32.S (ffi_closure_N32): Use 64-bit add instruction on
	pointer values.

2007-12-01  Andreas Tobler  <<EMAIL>>

	PR libffi/31937
	* src/powerpc/ffitarget.h: Introduce new ABI FFI_LINUX_SOFT_FLOAT.
	Add local FFI_TYPE_UINT128 to handle soft-float long-double-128.
	* src/powerpc/ffi.c: Distinguish between __NO_FPRS__ and not and
	set the NUM_FPR_ARG_REGISTERS according to.
	Add support for potential soft-float support under hard-float
	architecture.
	(ffi_prep_args_SYSV): Set NUM_FPR_ARG_REGISTERS to 0 in case of
	FFI_LINUX_SOFT_FLOAT, handle float, doubles and long-doubles according
	to the FFI_LINUX_SOFT_FLOAT ABI.
	(ffi_prep_cif_machdep): Likewise.
	(ffi_closure_helper_SYSV): Likewise.
	* src/powerpc/ppc_closure.S: Make sure not to store float/double
	on archs where __NO_FPRS__ is true.
	Add FFI_TYPE_UINT128 support.
	* src/powerpc/sysv.S: Add support for soft-float long-double-128.
	Adjust copyright notice.

2007-11-25  Andreas Tobler  <<EMAIL>>

	* src/closures.c: Move defintion of MAYBE_UNUSED from here to ...
	* include/ffi_common.h: ... here.
	Update copyright.

2007-11-17  Andreas Tobler  <<EMAIL>>

	* src/powerpc/sysv.S: Load correct cr to compare if we have long double.
	* src/powerpc/linux64.S: Likewise.
	* src/powerpc/ffi.c: Add a comment to show which part goes into cr6.
	* testsuite/libffi.call/return_ldl.c: New test.

2007-09-04    <<EMAIL>>

	* src/arm/sysv.S (UNWIND): New.
	(Whole file): Conditionally compile unwinder directives.
	* src/arm/sysv.S: Add unwinder directives.

	* src/arm/ffi.c (ffi_prep_args): Align structs by at least 4 bytes.
	Only treat r0 as a struct address if we're actually returning a
	struct by address.
	Only copy the bytes that are actually within a struct.
	(ffi_prep_cif_machdep): A Composite Type not larger than 4 bytes
	is returned in r0, not passed by address.
	(ffi_call): Allocate a word-sized temporary for the case where
	a composite is returned in r0.
	(ffi_prep_incoming_args_SYSV): Align as necessary.

2007-08-05  Steven Newbury  <<EMAIL>>

	* src/arm/ffi.c (FFI_INIT_TRAMPOLINE): Use __clear_cache instead of
	directly using the sys_cacheflush syscall.

2007-07-27  Andrew Haley  <<EMAIL>>

	* src/arm/sysv.S (ffi_closure_SYSV): Add soft-float.

2007-09-03  Maciej W. Rozycki  <<EMAIL>>

	* Makefile.am: Unify MIPS_IRIX and MIPS_LINUX into MIPS.
	* configure.ac: Likewise.
	* Makefile.in: Regenerate.
	* include/Makefile.in: Likewise.
	* testsuite/Makefile.in: Likewise.
	* configure: Likewise.

2007-08-24  David Daney  <<EMAIL>>

	* testsuite/libffi.call/return_sl.c: New test.

2007-08-10  David Daney  <<EMAIL>>

	* testsuite/libffi.call/cls_multi_ushort.c,
	testsuite/libffi.call/cls_align_uint16.c,
	testsuite/libffi.call/nested_struct1.c,
	testsuite/libffi.call/nested_struct3.c,
	testsuite/libffi.call/cls_7_1_byte.c,
	testsuite/libffi.call/nested_struct5.c,
	testsuite/libffi.call/cls_double.c,
	testsuite/libffi.call/nested_struct7.c,
	testsuite/libffi.call/cls_sint.c,
	testsuite/libffi.call/nested_struct9.c,
	testsuite/libffi.call/cls_20byte1.c,
	testsuite/libffi.call/cls_multi_sshortchar.c,
	testsuite/libffi.call/cls_align_sint64.c,
	testsuite/libffi.call/cls_3byte2.c,
	testsuite/libffi.call/cls_multi_schar.c,
	testsuite/libffi.call/cls_multi_uchar.c,
	testsuite/libffi.call/cls_19byte.c,
	testsuite/libffi.call/cls_9byte1.c,
	testsuite/libffi.call/cls_align_float.c,
	testsuite/libffi.call/closure_fn1.c,
	testsuite/libffi.call/problem1.c,
	testsuite/libffi.call/closure_fn3.c,
	testsuite/libffi.call/cls_sshort.c,
	testsuite/libffi.call/closure_fn5.c,
	testsuite/libffi.call/cls_align_double.c,
	testsuite/libffi.call/nested_struct.c,
	testsuite/libffi.call/cls_2byte.c,
	testsuite/libffi.call/nested_struct10.c,
	testsuite/libffi.call/cls_4byte.c,
	testsuite/libffi.call/cls_6byte.c,
	testsuite/libffi.call/cls_8byte.c,
	testsuite/libffi.call/cls_multi_sshort.c,
	testsuite/libffi.call/cls_align_sint16.c,
	testsuite/libffi.call/cls_align_uint32.c,
	testsuite/libffi.call/cls_20byte.c,
	testsuite/libffi.call/cls_float.c,
	testsuite/libffi.call/nested_struct2.c,
	testsuite/libffi.call/cls_5_1_byte.c,
	testsuite/libffi.call/nested_struct4.c,
	testsuite/libffi.call/cls_24byte.c,
	testsuite/libffi.call/nested_struct6.c,
	testsuite/libffi.call/cls_64byte.c,
	testsuite/libffi.call/nested_struct8.c,
	testsuite/libffi.call/cls_uint.c,
	testsuite/libffi.call/cls_multi_ushortchar.c,
	testsuite/libffi.call/cls_schar.c,
	testsuite/libffi.call/cls_uchar.c,
	testsuite/libffi.call/cls_align_uint64.c,
	testsuite/libffi.call/cls_ulonglong.c,
	testsuite/libffi.call/cls_align_longdouble.c,
	testsuite/libffi.call/cls_1_1byte.c,
	testsuite/libffi.call/cls_12byte.c,
	testsuite/libffi.call/cls_3_1byte.c,
	testsuite/libffi.call/cls_3byte1.c,
	testsuite/libffi.call/cls_4_1byte.c,
	testsuite/libffi.call/cls_6_1_byte.c,
	testsuite/libffi.call/cls_16byte.c,
	testsuite/libffi.call/cls_18byte.c,
	testsuite/libffi.call/closure_fn0.c,
	testsuite/libffi.call/cls_9byte2.c,
	testsuite/libffi.call/closure_fn2.c,
	testsuite/libffi.call/closure_fn4.c,
	testsuite/libffi.call/cls_ushort.c,
	testsuite/libffi.call/closure_fn6.c,
	testsuite/libffi.call/cls_5byte.c,
	testsuite/libffi.call/cls_align_pointer.c,
	testsuite/libffi.call/cls_7byte.c,
	testsuite/libffi.call/cls_align_sint32.c,
	testsuite/libffi.special/unwindtest_ffi_call.cc,
	testsuite/libffi.special/unwindtest.cc: Remove xfail for mips64*-*-*.

2007-08-10  David Daney  <<EMAIL>>

	PR libffi/28313
	* configure.ac: Don't treat mips64 as a special case.
	* Makefile.am (nodist_libffi_la_SOURCES): Add n32.S.
	* configure: Regenerate
	* Makefile.in: Ditto.
	* fficonfig.h.in: Ditto.
	* src/mips/ffitarget.h (REG_L, REG_S, SUBU, ADDU, SRL, LI): Indent.
	(LA, EH_FRAME_ALIGN, FDE_ADDR_BYTES): New preprocessor macros.
	(FFI_DEFAULT_ABI): Set for n64 case.
	(FFI_CLOSURES, FFI_TRAMPOLINE_SIZE): Define for n32 and n64 cases.
	* src/mips/n32.S (ffi_call_N32): Add debug macros and labels for FDE.
	(ffi_closure_N32): New function.
	(.eh_frame): New section
	* src/mips/o32.S: Clean up comments.
	(ffi_closure_O32): Pass ffi_closure parameter in $12.
	* src/mips/ffi.c: Use FFI_MIPS_N32 instead of
	_MIPS_SIM == _ABIN32 throughout.
	(FFI_MIPS_STOP_HERE): New, use in place of
	ffi_stop_here.
	(ffi_prep_args): Use unsigned long to hold pointer values.  Rewrite
	to support n32/n64 ABIs.
	(calc_n32_struct_flags): Rewrite.
	(calc_n32_return_struct_flags): Remove unused variable.  Reverse
	position of flag bits.
	(ffi_prep_cif_machdep): Rewrite n32 portion.
	(ffi_call): Enable for n64.  Add special handling for small structure
	return values.
	(ffi_prep_closure_loc): Add n32 and n64 support.
	(ffi_closure_mips_inner_O32): Add cast to silence warning.
	(copy_struct_N32, ffi_closure_mips_inner_N32): New functions.

2007-08-08  David Daney  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h (ffi_type_mylong): Remove definition.
	* testsuite/libffi.call/cls_align_uint16.c (main): Use correct type
	specifiers.
	* testsuite/libffi.call/nested_struct1.c (main): Ditto.
	* testsuite/libffi.call/cls_sint.c (main): Ditto.
	* testsuite/libffi.call/nested_struct9.c (main): Ditto.
	* testsuite/libffi.call/cls_20byte1.c (main): Ditto.
	* testsuite/libffi.call/cls_9byte1.c (main): Ditto.
	* testsuite/libffi.call/closure_fn1.c (main): Ditto.
	* testsuite/libffi.call/closure_fn3.c (main): Ditto.
	* testsuite/libffi.call/return_dbl2.c (main): Ditto.
	* testsuite/libffi.call/cls_sshort.c (main): Ditto.
	* testsuite/libffi.call/return_fl3.c (main): Ditto.
	* testsuite/libffi.call/closure_fn5.c (main): Ditto.
	* testsuite/libffi.call/nested_struct.c (main): Ditto.
	* testsuite/libffi.call/nested_struct10.c (main): Ditto.
	* testsuite/libffi.call/return_ll1.c (main): Ditto.
	* testsuite/libffi.call/cls_8byte.c (main): Ditto.
	* testsuite/libffi.call/cls_align_uint32.c (main): Ditto.
	* testsuite/libffi.call/cls_align_sint16.c (main): Ditto.
	* testsuite/libffi.call/cls_20byte.c (main): Ditto.
	* testsuite/libffi.call/nested_struct2.c (main): Ditto.
	* testsuite/libffi.call/cls_24byte.c (main): Ditto.
	* testsuite/libffi.call/nested_struct6.c (main): Ditto.
	* testsuite/libffi.call/cls_uint.c (main): Ditto.
	* testsuite/libffi.call/cls_12byte.c (main): Ditto.
	* testsuite/libffi.call/cls_16byte.c (main): Ditto.
	* testsuite/libffi.call/closure_fn0.c (main): Ditto.
	* testsuite/libffi.call/cls_9byte2.c (main): Ditto.
	* testsuite/libffi.call/closure_fn2.c (main): Ditto.
	* testsuite/libffi.call/return_dbl1.c (main): Ditto.
	* testsuite/libffi.call/closure_fn4.c (main): Ditto.
	* testsuite/libffi.call/closure_fn6.c (main): Ditto.
	* testsuite/libffi.call/cls_align_sint32.c (main): Ditto.

2007-08-07  Andrew Haley  <<EMAIL>>

	* src/x86/sysv.S (ffi_closure_raw_SYSV): Fix typo in previous
	checkin.

2007-08-06  Andrew Haley  <<EMAIL>>

	PR testsuite/32843
	* src/x86/sysv.S (ffi_closure_raw_SYSV): Handle FFI_TYPE_UINT8,
	FFI_TYPE_SINT8, FFI_TYPE_UINT16, FFI_TYPE_SINT16, FFI_TYPE_UINT32,
	FFI_TYPE_SINT32.

2007-08-02  David Daney  <<EMAIL>>

	* testsuite/libffi.call/return_ul.c (main): Define return type as
	ffi_arg.  Use proper printf conversion specifier.

2007-07-30  Andrew Haley  <<EMAIL>>

	PR testsuite/32843
	* src/x86/ffi.c (ffi_prep_cif_machdep): in x86 case, add code for
	signed/unsigned int8/16.
	* src/x86/sysv.S (ffi_call_SYSV): Rewrite to:
	Use a jump table.
	Remove code to pop args from the stack after call.
	Special-case signed/unsigned int8/16.
	* testsuite/libffi.call/return_sc.c (main): Revert.

2007-07-26  Richard Guenther  <<EMAIL>>

	PR testsuite/32843
	* testsuite/libffi.call/return_sc.c (main): Verify call
	result as signed char, not ffi_arg.

2007-07-16  Rainer Orth  <<EMAIL>>

	* configure.ac (i?86-*-solaris2.1[0-9]): Set TARGET to X86_64.
	* configure: Regenerate.

2007-07-11  David Daney  <<EMAIL>>

	* src/mips/ffi.c: Don't include sys/cachectl.h.
	(ffi_prep_closure_loc): Use __builtin___clear_cache() instead of
	cacheflush().

2007-05-18  Aurelien Jarno  <<EMAIL>>

	* src/arm/ffi.c (ffi_prep_closure_loc): Renamed and ajusted
	from (ffi_prep_closure): ... this.
	(FFI_INIT_TRAMPOLINE): Adjust.

2005-12-31  Phil Blundell  <<EMAIL>>

	* src/arm/ffi.c (ffi_prep_incoming_args_SYSV,
	ffi_closure_SYSV_inner, ffi_prep_closure): New, add closure support.
	* src/arm/sysv.S(ffi_closure_SYSV): Likewise.
	* src/arm/ffitarget.h (FFI_TRAMPOLINE_SIZE): Likewise.
	(FFI_CLOSURES): Enable closure support.

2007-07-03  Andrew Haley  <<EMAIL>>

	* testsuite/libffi.call/cls_multi_ushort.c,
	testsuite/libffi.call/cls_align_uint16.c,
	testsuite/libffi.call/nested_struct1.c,
	testsuite/libffi.call/nested_struct3.c,
	testsuite/libffi.call/cls_7_1_byte.c,
	testsuite/libffi.call/cls_double.c,
	testsuite/libffi.call/nested_struct5.c,
	testsuite/libffi.call/nested_struct7.c,
	testsuite/libffi.call/cls_sint.c,
	testsuite/libffi.call/nested_struct9.c,
	testsuite/libffi.call/cls_20byte1.c,
	testsuite/libffi.call/cls_multi_sshortchar.c,
	testsuite/libffi.call/cls_align_sint64.c,
	testsuite/libffi.call/cls_3byte2.c,
	testsuite/libffi.call/cls_multi_schar.c,
	testsuite/libffi.call/cls_multi_uchar.c,
	testsuite/libffi.call/cls_19byte.c,
	testsuite/libffi.call/cls_9byte1.c,
	testsuite/libffi.call/cls_align_float.c,
	testsuite/libffi.call/closure_fn1.c,
	testsuite/libffi.call/problem1.c,
	testsuite/libffi.call/closure_fn3.c,
	testsuite/libffi.call/cls_sshort.c,
	testsuite/libffi.call/closure_fn5.c,
	testsuite/libffi.call/cls_align_double.c,
	testsuite/libffi.call/cls_2byte.c,
	testsuite/libffi.call/nested_struct.c,
	testsuite/libffi.call/nested_struct10.c,
	testsuite/libffi.call/cls_4byte.c,
	testsuite/libffi.call/cls_6byte.c,
	testsuite/libffi.call/cls_8byte.c,
	testsuite/libffi.call/cls_multi_sshort.c,
	testsuite/libffi.call/cls_align_uint32.c,
	testsuite/libffi.call/cls_align_sint16.c,
	testsuite/libffi.call/cls_float.c,
	testsuite/libffi.call/cls_20byte.c,
	testsuite/libffi.call/cls_5_1_byte.c,
	testsuite/libffi.call/nested_struct2.c,
	testsuite/libffi.call/cls_24byte.c,
	testsuite/libffi.call/nested_struct4.c,
	testsuite/libffi.call/nested_struct6.c,
	testsuite/libffi.call/cls_64byte.c,
	testsuite/libffi.call/nested_struct8.c,
	testsuite/libffi.call/cls_uint.c,
	testsuite/libffi.call/cls_multi_ushortchar.c,
	testsuite/libffi.call/cls_schar.c,
	testsuite/libffi.call/cls_uchar.c,
	testsuite/libffi.call/cls_align_uint64.c,
	testsuite/libffi.call/cls_ulonglong.c,
	testsuite/libffi.call/cls_align_longdouble.c,
	testsuite/libffi.call/cls_1_1byte.c,
	testsuite/libffi.call/cls_12byte.c,
	testsuite/libffi.call/cls_3_1byte.c,
	testsuite/libffi.call/cls_3byte1.c,
	testsuite/libffi.call/cls_4_1byte.c,
	testsuite/libffi.call/cls_6_1_byte.c,
	testsuite/libffi.call/cls_16byte.c,
	testsuite/libffi.call/cls_18byte.c,
	testsuite/libffi.call/closure_fn0.c,
	testsuite/libffi.call/cls_9byte2.c,
	testsuite/libffi.call/closure_fn2.c,
	testsuite/libffi.call/closure_fn4.c,
	testsuite/libffi.call/cls_ushort.c,
	testsuite/libffi.call/closure_fn6.c,
	testsuite/libffi.call/cls_5byte.c,
	testsuite/libffi.call/cls_align_pointer.c,
	testsuite/libffi.call/cls_7byte.c,
	testsuite/libffi.call/cls_align_sint32.c,
	testsuite/libffi.special/unwindtest_ffi_call.cc,
	testsuite/libffi.special/unwindtest.cc: Enable for ARM.

2007-07-05  H.J. Lu  <<EMAIL>>

	* aclocal.m4: Regenerated.

2007-06-02  Paolo Bonzini  <<EMAIL>>

	* configure: Regenerate.

2007-05-23  Steve Ellcey  <<EMAIL>>

	* Makefile.in: Regenerate.
	* configure: Regenerate.
	* aclocal.m4: Regenerate.
	* include/Makefile.in: Regenerate.
	* testsuite/Makefile.in: Regenerate.

2007-05-10  Roman Zippel <<EMAIL>>

	* src/m68k/ffi.c (ffi_prep_incoming_args_SYSV,
	ffi_closure_SYSV_inner,ffi_prep_closure): New, add closure support.
	* src/m68k/sysv.S(ffi_closure_SYSV,ffi_closure_struct_SYSV): Likewise.
	* src/m68k/ffitarget.h (FFI_TRAMPOLINE_SIZE): Likewise.
	(FFI_CLOSURES): Enable closure support.

2007-05-10  Roman Zippel <<EMAIL>>

	* configure.ac (HAVE_AS_CFI_PSEUDO_OP): New test.
	* configure: Regenerate.
	* fficonfig.h.in: Regenerate.
	* src/m68k/sysv.S (CFI_STARTPROC,CFI_ENDPROC,
	CFI_OFFSET,CFI_DEF_CFA): New macros.
	(ffi_call_SYSV): Add callframe annotation.

2007-05-10  Roman Zippel <<EMAIL>>

	* src/m68k/ffi.c (ffi_prep_args,ffi_prep_cif_machdep): Fix
	numerous test suite failures.
	* src/m68k/sysv.S (ffi_call_SYSV): Likewise.

2007-04-11  Paolo Bonzini  <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Bring up to date.
	* Makefile.in: Regenerate.
	* src/frv/eabi.S: Remove RCS keyword.

2007-04-06  Richard Henderson  <<EMAIL>>

	* configure.ac: Tidy target case.
	(HAVE_LONG_DOUBLE): Allow the target to override.
	* configure: Regenerate.
	* include/ffi.h.in: Don't define ffi_type_foo if
	LIBFFI_HIDE_BASIC_TYPES is defined.
	(ffi_type_longdouble): If not HAVE_LONG_DOUBLE, define
	to ffi_type_double.
	* types.c (LIBFFI_HIDE_BASIC_TYPES): Define.
	(FFI_TYPEDEF, ffi_type_void): Mark the data const.
	(ffi_type_longdouble): Special case for Alpha.  Don't define
	if long double == double.

	* src/alpha/ffi.c (FFI_TYPE_LONGDOUBLE): Assert unique value.
	(ffi_prep_cif_machdep): Handle it as the 128-bit type.
	(ffi_call, ffi_closure_osf_inner): Likewise.
	(ffi_closure_osf_inner): Likewise.  Mark hidden.
	(ffi_call_osf, ffi_closure_osf): Mark hidden.
	* src/alpha/ffitarget.h (FFI_LAST_ABI): Tidy definition.
	* src/alpha/osf.S (ffi_call_osf, ffi_closure_osf): Mark hidden.
	(load_table): Handle 128-bit long double.

	* testsuite/libffi.call/float4.c: Add -mieee for alpha.

2007-04-06  Tom Tromey  <<EMAIL>>

	PR libffi/31491:
	* README: Fixed bug in example.

2007-04-03  Jakub Jelinek  <<EMAIL>>

	* src/closures.c: Include sys/statfs.h.
	(_GNU_SOURCE): Define on Linux.
	(FFI_MMAP_EXEC_SELINUX): Define.
	(selinux_enabled): New variable.
	(selinux_enabled_check): New function.
	(is_selinux_enabled): Define.
	(dlmmap): Use it.

2007-03-24  Uros Bizjak  <<EMAIL>>

	* testsuite/libffi.call/return_fl2.c (return_fl): Mark as static.
	Use 'volatile float sum' to create sum of floats to avoid false
	negative due to excess precision on ix86 targets.
	(main): Ditto.

2007-03-08  Alexandre Oliva  <<EMAIL>>

	* src/powerpc/ffi.c (flush_icache): Fix left-over from previous
	patch.
	(ffi_prep_closure_loc): Remove unneeded casts.  Add needed ones.

2007-03-07  Alexandre Oliva  <<EMAIL>>

	* include/ffi.h.in (ffi_closure_alloc, ffi_closure_free): New.
	(ffi_prep_closure_loc): New.
	(ffi_prep_raw_closure_loc): New.
	(ffi_prep_java_raw_closure_loc): New.
	* src/closures.c: New file.
	* src/dlmalloc.c [FFI_MMAP_EXEC_WRIT] (struct malloc_segment):
	Replace sflags with exec_offset.
	[FFI_MMAP_EXEC_WRIT] (mmap_exec_offset, add_segment_exec_offset,
	sub_segment_exec_offset): New macros.
	(get_segment_flags, set_segment_flags, check_segment_merge): New
	macros.
	(is_mmapped_segment, is_extern_segment): Use get_segment_flags.
	(add_segment, sys_alloc, create_mspace, create_mspace_with_base,
	destroy_mspace): Use new macros.
	(sys_alloc): Silence warning.
	* Makefile.am (libffi_la_SOURCES): Add src/closures.c.
	* Makefile.in: Rebuilt.
	* src/prep_cif [FFI_CLOSURES] (ffi_prep_closure): Implement in
	terms of ffi_prep_closure_loc.
	* src/raw_api.c (ffi_prep_raw_closure_loc): Renamed and adjusted
	from...
	(ffi_prep_raw_closure): ... this.  Re-implement in terms of the
	renamed version.
	* src/java_raw_api (ffi_prep_java_raw_closure_loc): Renamed and
	adjusted from...
	(ffi_prep_java_raw_closure): ... this.  Re-implement in terms of
	the renamed version.
	* src/alpha/ffi.c (ffi_prep_closure_loc): Renamed from
	(ffi_prep_closure): ... this.
	* src/pa/ffi.c: Likewise.
	* src/cris/ffi.c: Likewise.  Adjust.
	* src/frv/ffi.c: Likewise.
	* src/ia64/ffi.c: Likewise.
	* src/mips/ffi.c: Likewise.
	* src/powerpc/ffi_darwin.c: Likewise.
	* src/s390/ffi.c: Likewise.
	* src/sh/ffi.c: Likewise.
	* src/sh64/ffi.c: Likewise.
	* src/sparc/ffi.c: Likewise.
	* src/x86/ffi64.c: Likewise.
	* src/x86/ffi.c: Likewise.
	(FFI_INIT_TRAMPOLINE): Adjust.
	(ffi_prep_raw_closure_loc): Renamed and adjusted from...
	(ffi_prep_raw_closure): ... this.
	* src/powerpc/ffi.c (ffi_prep_closure_loc): Renamed from
	(ffi_prep_closure): ... this.
	(flush_icache): Adjust.

2007-03-07  Alexandre Oliva  <<EMAIL>>

	* src/dlmalloc.c: New file, imported version 2.8.3 of Doug
	Lea's malloc.

2007-03-01  Brooks Moses  <<EMAIL>>

	* Makefile.am: Add dummy install-pdf target.
	* Makefile.in: Regenerate

2007-02-13  Andreas Krebbel  <<EMAIL>>

	* src/s390/ffi.c (ffi_prep_args, ffi_prep_cif_machdep,
	ffi_closure_helper_SYSV): Add long double handling.

2007-02-02  Jakub Jelinek  <<EMAIL>>

	* src/powerpc/linux64.S (ffi_call_LINUX64): Move restore of r2
	immediately after bctrl instruction.

2007-01-18  Alexandre Oliva  <<EMAIL>>

	* Makefile.am (all-recursive, install-recursive,
	mostlyclean-recursive, clean-recursive, distclean-recursive,
	maintainer-clean-recursive): Add missing targets.
	* Makefile.in: Rebuilt.

2006-12-14  Andreas Tobler  <<EMAIL>>

	* configure.ac: Add TARGET for x86_64-*-darwin*.
	* Makefile.am (nodist_libffi_la_SOURCES): Add rules for 64-bit sources
	for X86_DARWIN.
	* src/x86/ffitarget.h: Set trampoline size for x86_64-*-darwin*.
	* src/x86/darwin64.S: New file for x86_64-*-darwin* support.
	* configure: Regenerate.
	* Makefile.in: Regenerate.
	* include/Makefile.in: Regenerate.
	* testsuite/Makefile.in: Regenerate.
	* testsuite/libffi.special/unwindtest_ffi_call.cc: New test case for
	ffi_call only.

2006-12-13  Andreas Tobler <<EMAIL>>

	* aclocal.m4: Regenerate with aclocal -I .. as written in the
	Makefile.am.

2006-10-31  Geoffrey Keating  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (darwin_adjust_aggregate_sizes): New.
	(ffi_prep_cif_machdep): Call darwin_adjust_aggregate_sizes for
	Darwin.
	* testsuite/libffi.call/nested_struct4.c: Remove Darwin XFAIL.
	* testsuite/libffi.call/nested_struct6.c: Remove Darwin XFAIL.

2006-10-10  Paolo Bonzini  <<EMAIL>>
	    Sandro Tolaini  <<EMAIL>>

	* configure.ac [i*86-*-darwin*]: Set X86_DARWIN symbol and
	conditional.
	* configure: Regenerated.
	* Makefile.am (nodist_libffi_la_SOURCES) [X86_DARWIN]: New case.
	(EXTRA_DIST): Add src/x86/darwin.S.
	* Makefile.in: Regenerated.
	* include/Makefile.in: Regenerated.
	* testsuite/Makefile.in: Regenerated.

	* src/x86/ffi.c (ffi_prep_cif_machdep) [X86_DARWIN]: Treat like
	X86_WIN32, and additionally align stack to 16 bytes.
	* src/x86/darwin.S: New, based on sysv.S.
	* src/prep_cif.c (ffi_prep_cif) [X86_DARWIN]: Align > 8-byte structs.

2006-09-12  David Daney  <<EMAIL>>

	PR libffi/23935
	* include/Makefile.am: Install both ffi.h and ffitarget.h in
	$(libdir)/gcc/$(target_alias)/$(gcc_version)/include.
	* aclocal.m4: Regenerated for automake 1.9.6.
	* Makefile.in: Regenerated.
	* include/Makefile.in: Regenerated.
	* testsuite/Makefile.in: Regenerated.

2006-08-17  Andreas Tobler  <<EMAIL>>

	* include/ffi_common.h (struct): Revert accidental commit.

2006-08-15  Andreas Tobler  <<EMAIL>>

	* include/ffi_common.h: Remove lint directives.
	* include/ffi.h.in: Likewise.

2006-07-25  Torsten Schoenfeld  <<EMAIL>>

	* include/ffi.h.in (ffi_type_ulong, ffi_type_slong): Define correctly
	for 32-bit architectures.
	* testsuite/libffi.call/return_ul.c: New test case.

2006-07-19  David Daney  <<EMAIL>>

	* testsuite/libffi.call/closure_fn6.c: Remove xfail for mips,
	xfail remains for mips64.

2006-05-23  Carlos O'Donell  <<EMAIL>>

	* Makefile.am: Add install-html target. Add install-html to .PHONY
	* Makefile.in: Regenerate.
	* aclocal.m4: Regenerate.
	* include/Makefile.in: Regenerate.
	* testsuite/Makefile.in: Regenerate.

2006-05-18  John David Anglin  <<EMAIL>>

	* pa/ffi.c (ffi_prep_args_pa32): Load floating point arguments from
	stack slot.

2006-04-22  Andreas Tobler  <<EMAIL>>

	* README: Remove notice about 'Crazy Comments'.
	* src/debug.c: Remove lint directives. Cleanup white spaces.
	* src/java_raw_api.c: Likewise.
	* src/prep_cif.c: Likewise.
	* src/raw_api.c: Likewise.
	* src/ffitest.c: Delete. No longer needed, all test cases migrated
	to the testsuite.
	* src/arm/ffi.c: Remove lint directives.
	* src/m32r/ffi.c: Likewise.
	* src/pa/ffi.c: Likewise.
	* src/powerpc/ffi.c: Likewise.
	* src/powerpc/ffi_darwin.c: Likewise.
	* src/sh/ffi.c: Likewise.
	* src/sh64/ffi.c: Likewise.
	* src/x86/ffi.c: Likewise.
	* testsuite/libffi.call/float2.c: Likewise.
	* testsuite/libffi.call/promotion.c: Likewise.
	* testsuite/libffi.call/struct1.c: Likewise.

2006-04-13  Andreas Tobler  <<EMAIL>>

	* src/pa/hpux32.S: Correct unwind offset calculation for
	ffi_closure_pa32.
	* src/pa/linux.S: Likewise.

2006-04-12  James E Wilson  <<EMAIL>>

	PR libgcj/26483
	* src/ia64/ffi.c (stf_spill, ldf_fill): Rewrite as macros.
	(hfa_type_load): Call stf_spill.
	(hfa_type_store): Call ldf_fill.
	(ffi_call): Adjust calls to above routines.  Add local temps for
	macro result.

2006-04-10  Matthias Klose  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp (libffi-init): Recognize multilib
	directory names containing underscores.

2006-04-07  James E Wilson  <<EMAIL>>

	* testsuite/libffi.call/float4.c: New testcase.

2006-04-05  John David Anglin  <<EMAIL>>
	    Andreas Tobler  <<EMAIL>>

	* Makefile.am: Add PA_HPUX port.
	* Makefile.in: Regenerate.
	* include/Makefile.in: Likewise.
	* testsuite/Makefile.in: Likewise.
	* configure.ac: Add PA_HPUX rules.
	* configure: Regenerate.
	* src/pa/ffitarget.h: Rename linux target to PA_LINUX.
	Add PA_HPUX and PA64_HPUX.
	Rename FFI_LINUX ABI to FFI_PA32 ABI.
	(FFI_TRAMPOLINE_SIZE): Define for 32-bit HP-UX targets.
	(FFI_TYPE_SMALL_STRUCT2): Define.
	(FFI_TYPE_SMALL_STRUCT4): Likewise.
	(FFI_TYPE_SMALL_STRUCT8): Likewise.
	(FFI_TYPE_SMALL_STRUCT3): Redefine.
	(FFI_TYPE_SMALL_STRUCT5): Likewise.
	(FFI_TYPE_SMALL_STRUCT6): Likewise.
	(FFI_TYPE_SMALL_STRUCT7): Likewise.
	* src/pa/ffi.c (ROUND_DOWN): Delete.
	(fldw, fstw, fldd, fstd): Use '__asm__'.
	(ffi_struct_type): Add support for FFI_TYPE_SMALL_STRUCT2,
	FFI_TYPE_SMALL_STRUCT4 and FFI_TYPE_SMALL_STRUCT8.
	(ffi_prep_args_LINUX): Rename to ffi_prep_args_pa32. Update comment.
	Simplify incrementing of stack slot variable. Change type of local
	'n' to unsigned int.
	(ffi_size_stack_LINUX): Rename to ffi_size_stack_pa32. Handle long
	double on PA_HPUX.
	(ffi_prep_cif_machdep): Likewise.
	(ffi_call): Likewise.
	(ffi_closure_inner_LINUX): Rename to ffi_closure_inner_pa32. Change
	return type to ffi_status. Simplify incrementing of stack slot
	variable. Only copy floating point argument registers when PA_LINUX
	is true. Reformat debug statement.
	Add support for FFI_TYPE_SMALL_STRUCT2, FFI_TYPE_SMALL_STRUCT4 and
	FFI_TYPE_SMALL_STRUCT8.
	(ffi_closure_LINUX): Rename to ffi_closure_pa32. Add 'extern' to
	declaration.
	(ffi_prep_closure): Make linux trampoline conditional on PA_LINUX.
	Add nops to cache flush.  Add trampoline for PA_HPUX.
	* src/pa/hpux32.S: New file.
	* src/pa/linux.S (ffi_call_LINUX): Rename to ffi_call_pa32. Rename
	ffi_prep_args_LINUX to ffi_prep_args_pa32.
	Localize labels. Add support for 2, 4 and 8-byte small structs. Handle
	unaligned destinations in 3, 5, 6 and 7-byte small structs. Order
	argument type checks so that common argument types appear first.
	(ffi_closure_LINUX): Rename to ffi_closure_pa32. Rename
	ffi_closure_inner_LINUX to ffi_closure_inner_pa32.

2006-03-24  Alan Modra  <<EMAIL>>

	* src/powerpc/ffitarget.h (enum ffi_abi): Add FFI_LINUX.  Default
	for 32-bit using IBM extended double format.  Fix FFI_LAST_ABI.
	* src/powerpc/ffi.c (ffi_prep_args_SYSV): Handle linux variant of
	FFI_TYPE_LONGDOUBLE.
	(ffi_prep_args64): Assert using IBM extended double.
	(ffi_prep_cif_machdep): Don't munge FFI_TYPE_LONGDOUBLE type.
	Handle FFI_LINUX FFI_TYPE_LONGDOUBLE return and args.
	(ffi_call): Handle FFI_LINUX.
	(ffi_closure_helper_SYSV): Non FFI_LINUX long double return needs
	gpr3 return pointer as for struct return.  Handle FFI_LINUX
	FFI_TYPE_LONGDOUBLE return and args.  Don't increment "nf"
	unnecessarily.
	* src/powerpc/ppc_closure.S (ffi_closure_SYSV): Load both f1 and f2
	for FFI_TYPE_LONGDOUBLE.  Move epilogue insns into case table.
	Don't use r6 as pointer to results, instead use sp offset.  Don't
	make a special call to load lr with case table address, instead
	use offset from previous call.
	* src/powerpc/sysv.S (ffi_call_SYSV): Save long double return.
	* src/powerpc/linux64.S (ffi_call_LINUX64): Simplify long double
	return.

2006-03-15  Kaz Kojima  <<EMAIL>>

	* src/sh64/ffi.c (ffi_prep_cif_machdep): Handle float arguments
	passed with FP registers correctly.
	(ffi_closure_helper_SYSV): Likewise.
	* src/sh64/sysv.S: Likewise.

2006-03-01  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.special/unwindtest.cc (closure_test_fn): Mark cif,
	args and userdata unused.
	(closure_test_fn1): Mark cif and userdata unused.
	(main): Remove unused res.

2006-02-28  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/call.exp: Adjust FSF address. Add test runs for
	-O2, -O3, -Os and the warning flags -W -Wall.
	* testsuite/libffi.special/special.exp: Likewise.
	* testsuite/libffi.call/ffitest.h: Add an __UNUSED__ macro to mark
	unused parameter unused for gcc or else do nothing.
	* testsuite/libffi.special/ffitestcxx.h: Likewise.
	* testsuite/libffi.call/cls_12byte.c (cls_struct_12byte_gn): Mark cif
	and userdata unused.
	* testsuite/libffi.call/cls_16byte.c (cls_struct_16byte_gn): Likewise.
	* testsuite/libffi.call/cls_18byte.c (cls_struct_18byte_gn): Likewise.
	* testsuite/libffi.call/cls_19byte.c (cls_struct_19byte_gn): Likewise.
	* testsuite/libffi.call/cls_1_1byte.c (cls_struct_1_1byte_gn): Likewise.
	* testsuite/libffi.call/cls_20byte.c (cls_struct_20byte_gn): Likewise.
	* testsuite/libffi.call/cls_20byte1.c (cls_struct_20byte_gn): Likewise.
	* testsuite/libffi.call/cls_24byte.c (cls_struct_24byte_gn): Likewise.
	* testsuite/libffi.call/cls_2byte.c (cls_struct_2byte_gn): Likewise.
	* testsuite/libffi.call/cls_3_1byte.c (cls_struct_3_1byte_gn): Likewise.
	* testsuite/libffi.call/cls_3byte1.c (cls_struct_3byte_gn): Likewise.
	* testsuite/libffi.call/cls_3byte2.c (cls_struct_3byte_gn1): Likewise.
	* testsuite/libffi.call/cls_4_1byte.c (cls_struct_4_1byte_gn): Likewise.
	* testsuite/libffi.call/cls_4byte.c (cls_struct_4byte_gn): Likewise.
	* testsuite/libffi.call/cls_5_1_byte.c (cls_struct_5byte_gn): Likewise.
	* testsuite/libffi.call/cls_5byte.c (cls_struct_5byte_gn): Likewise.
	* testsuite/libffi.call/cls_64byte.c (cls_struct_64byte_gn): Likewise.
	* testsuite/libffi.call/cls_6_1_byte.c (cls_struct_6byte_gn): Likewise.
	* testsuite/libffi.call/cls_6byte.c (cls_struct_6byte_gn): Likewise.
	* testsuite/libffi.call/cls_7_1_byte.c (cls_struct_7byte_gn): Likewise.
	* testsuite/libffi.call/cls_7byte.c (cls_struct_7byte_gn): Likewise.
	* testsuite/libffi.call/cls_8byte.c (cls_struct_8byte_gn): Likewise.
	* testsuite/libffi.call/cls_9byte1.c (cls_struct_9byte_gn): Likewise.
	* testsuite/libffi.call/cls_9byte2.c (cls_struct_9byte_gn): Likewise.
	* testsuite/libffi.call/cls_align_double.c (cls_struct_align_gn):
	Likewise.
	* testsuite/libffi.call/cls_align_float.c (cls_struct_align_gn):
	Likewise.
	* testsuite/libffi.call/cls_align_longdouble.c (cls_struct_align_gn):
	Likewise.
	* testsuite/libffi.call/cls_align_pointer.c (cls_struct_align_fn): Cast
	void* to avoid compiler warning.
	(main): Likewise.
	(cls_struct_align_gn): Mark cif and userdata unused.
	* testsuite/libffi.call/cls_align_sint16.c (cls_struct_align_gn):
	Likewise.
	* testsuite/libffi.call/cls_align_sint32.c (cls_struct_align_gn):
	Likewise.
	* testsuite/libffi.call/cls_align_sint64.c (cls_struct_align_gn):
	Likewise.
	* testsuite/libffi.call/cls_align_uint16.c (cls_struct_align_gn):
	Likewise.
	* testsuite/libffi.call/cls_align_uint32.c (cls_struct_align_gn):
	Likewise.
	* testsuite/libffi.call/cls_double.c (cls_ret_double_fn): Likewise.
	* testsuite/libffi.call/cls_float.c (cls_ret_float_fn): Likewise.
	* testsuite/libffi.call/cls_multi_schar.c (test_func_gn): Mark cif and
	data unused.
	(main): Cast res_call to silence gcc.
	* testsuite/libffi.call/cls_multi_sshort.c (test_func_gn): Mark cif and
	data unused.
	(main): Cast res_call to silence gcc.
	* testsuite/libffi.call/cls_multi_sshortchar.c (test_func_gn): Mark cif
	and data unused.
	(main): Cast res_call to silence gcc.
	* testsuite/libffi.call/cls_multi_uchar.c (test_func_gn): Mark cif and
	data unused.
	(main): Cast res_call to silence gcc.
	* testsuite/libffi.call/cls_multi_ushort.c (test_func_gn): Mark cif and
	data unused.
	(main): Cast res_call to silence gcc.
	* testsuite/libffi.call/cls_multi_ushortchar.c (test_func_gn): Mark cif
	and data unused.
	(main): Cast res_call to silence gcc.
	* testsuite/libffi.call/cls_schar.c (cls_ret_schar_fn): Mark cif and
	userdata unused.
	(cls_ret_schar_fn): Cast printf parameter to silence gcc.
	* testsuite/libffi.call/cls_sint.c (cls_ret_sint_fn): Mark cif and
	userdata unused.
	(cls_ret_sint_fn): Cast printf parameter to silence gcc.
	* testsuite/libffi.call/cls_sshort.c (cls_ret_sshort_fn): Mark cif and
	userdata unused.
	(cls_ret_sshort_fn): Cast printf parameter to silence gcc.
	* testsuite/libffi.call/cls_uchar.c (cls_ret_uchar_fn):  Mark cif and
	userdata unused.
	(cls_ret_uchar_fn): Cast printf parameter to silence gcc.
	* testsuite/libffi.call/cls_uint.c (cls_ret_uint_fn): Mark cif and
	userdata unused.
	(cls_ret_uint_fn): Cast printf parameter to silence gcc.
	* testsuite/libffi.call/cls_ulonglong.c (cls_ret_ulonglong_fn): Mark cif
	and userdata unused.
	* testsuite/libffi.call/cls_ushort.c (cls_ret_ushort_fn): Mark cif and
	userdata unused.
	(cls_ret_ushort_fn): Cast printf parameter to silence gcc.
	* testsuite/libffi.call/float.c (floating): Remove unused parameter e.
	* testsuite/libffi.call/float1.c (main): Remove unused variable i.
	Cleanup white spaces.
	* testsuite/libffi.call/negint.c (checking): Remove unused variable i.
	* testsuite/libffi.call/nested_struct.c (cls_struct_combined_gn): Mark
	cif and userdata unused.
	* testsuite/libffi.call/nested_struct1.c (cls_struct_combined_gn):
	Likewise.
	* testsuite/libffi.call/nested_struct10.c (B_gn): Likewise.
	* testsuite/libffi.call/nested_struct2.c (B_fn): Adjust printf
	formatters to silence gcc.
	(B_gn): Mark cif and userdata unused.
	* testsuite/libffi.call/nested_struct3.c (B_gn): Mark cif and userdata
	unused.
	* testsuite/libffi.call/nested_struct4.c: Mention related PR.
	(B_gn): Mark cif and userdata unused.
	* testsuite/libffi.call/nested_struct5.c (B_gn): Mark cif and userdata
	unused.
	* testsuite/libffi.call/nested_struct6.c: Mention related PR.
	(B_gn): Mark cif and userdata unused.
	* testsuite/libffi.call/nested_struct7.c (B_gn): Mark cif and userdata
	unused.
	* testsuite/libffi.call/nested_struct8.c (B_gn): Likewise.
	* testsuite/libffi.call/nested_struct9.c (B_gn): Likewise.
	* testsuite/libffi.call/problem1.c (stub): Likewise.
	* testsuite/libffi.call/pyobjc-tc.c (main): Cast the result to silence
	gcc.
	* testsuite/libffi.call/return_fl2.c (return_fl): Add the note mentioned
	in the last commit for this test case in the test case itself.
	* testsuite/libffi.call/closure_fn0.c (closure_test_fn0): Mark cif as
	unused.
	* testsuite/libffi.call/closure_fn1.c (closure_test_fn1): Likewise.
	* testsuite/libffi.call/closure_fn2.c (closure_test_fn2): Likewise.
	* testsuite/libffi.call/closure_fn3.c (closure_test_fn3): Likewise.
	* testsuite/libffi.call/closure_fn4.c (closure_test_fn0): Likewise.
	* testsuite/libffi.call/closure_fn5.c (closure_test_fn5): Likewise.
	* testsuite/libffi.call/closure_fn6.c (closure_test_fn0): Likewise.

2006-02-22  Kaz Kojima  <<EMAIL>>

	* src/sh/sysv.S: Fix register numbers in the FDE for
	ffi_closure_SYSV.

2006-02-20  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/return_fl2.c (return_fl): Remove static
	declaration to avoid a false negative on ix86. See PR323.

2006-02-18  Kaz Kojima  <<EMAIL>>

	* src/sh/ffi.c (ffi_closure_helper_SYSV): Remove unused variable
	and cast integer to void * if needed.  Update the pointer to
	the FP register saved area correctly.

2006-02-17  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/nested_struct6.c: XFAIL this test until PR25630
	is fixed.
	* testsuite/libffi.call/nested_struct4.c: Likewise.

2006-02-16  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/return_dbl.c: New test case.
	* testsuite/libffi.call/return_dbl1.c: Likewise.
	* testsuite/libffi.call/return_dbl2.c: Likewise.
	* testsuite/libffi.call/return_fl.c: Likewise.
	* testsuite/libffi.call/return_fl1.c: Likewise.
	* testsuite/libffi.call/return_fl2.c: Likewise.
	* testsuite/libffi.call/return_fl3.c: Likewise.
	* testsuite/libffi.call/closure_fn6.c: Likewise.

	* testsuite/libffi.call/nested_struct2.c: Remove ffi_type_mylong
	definition.
	* testsuite/libffi.call/ffitest.h: Add ffi_type_mylong definition
	here to be used by other test cases too.

	* testsuite/libffi.call/nested_struct10.c: New test case.
	* testsuite/libffi.call/nested_struct9.c: Likewise.
	* testsuite/libffi.call/nested_struct8.c: Likewise.
	* testsuite/libffi.call/nested_struct7.c: Likewise.
	* testsuite/libffi.call/nested_struct6.c: Likewise.
	* testsuite/libffi.call/nested_struct5.c: Likewise.
	* testsuite/libffi.call/nested_struct4.c: Likewise.

2006-01-21  Andreas Tobler  <<EMAIL>>

	* configure.ac: Enable libffi for sparc64-*-freebsd*.
	* configure: Rebuilt.

2006-01-18  Jakub Jelinek  <<EMAIL>>

	* src/powerpc/sysv.S (smst_two_register): Don't call __ashldi3,
	instead do the shifting inline.
	* src/powerpc/ppc_closure.S (ffi_closure_SYSV): Don't compute %r5
	shift count unconditionally.  Simplify load sequences for 1, 2, 3, 4
	and 8 byte structs, for the remaining struct sizes don't call
	__lshrdi3, instead do the shifting inline.

2005-12-07  Thiemo Seufer  <<EMAIL>>

	* src/mips/ffitarget.h: Remove obsolete sgidefs.h include. Add
	missing parentheses.
	* src/mips/o32.S (ffi_call_O32): Code formatting. Define
	and use A3_OFF, FP_OFF, RA_OFF. Micro-optimizations.
	(ffi_closure_O32): Likewise, but with newly defined A3_OFF2,
	A2_OFF2, A1_OFF2, A0_OFF2, RA_OFF2, FP_OFF2, S0_OFF2, GP_OFF2,
	V1_OFF2, V0_OFF2, FA_1_1_OFF2, FA_1_0_OFF2, FA_0_1_OFF2,
	FA_0_0_OFF2.
	* src/mips/ffi.c (ffi_prep_args): Code formatting. Fix
	endianness bugs.
	(ffi_prep_closure): Improve trampoline instruction scheduling.
	(ffi_closure_mips_inner_O32): Fix endianness bugs.

2005-12-03  Alan Modra  <<EMAIL>>

	* src/powerpc/ffi.c: Formatting.
	(ffi_prep_args_SYSV): Avoid possible aliasing problems by using unions.
	(ffi_prep_args64): Likewise.

2005-09-30  Geoffrey Keating  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp (libffi_target_compile): For
	darwin, use -shared-libgcc not -lgcc_s, and explain why.

2005-09-26  Tom Tromey  <<EMAIL>>

	* testsuite/libffi.call/float1.c (value_type): New typedef.
	(CANARY): New define.
	(main): Check for result buffer overflow.
	* src/powerpc/linux64.S: Handle linux64 long double returns.
	* src/powerpc/ffi.c (FLAG_RETURNS_128BITS): New constant.
	(ffi_prep_cif_machdep): Handle linux64 long double returns.

2005-08-25  Alan Modra  <<EMAIL>>

	PR target/23404
	* src/powerpc/ffi.c (ffi_prep_args_SYSV): Correct placement of stack
	homed fp args.
	(ffi_status ffi_prep_cif_machdep): Correct stack sizing for same.

2005-08-11  Jakub Jelinek  <<EMAIL>>

	* configure.ac (HAVE_HIDDEN_VISIBILITY_ATTRIBUTE): New test.
	(AH_BOTTOM): Add FFI_HIDDEN definition.
	* configure: Rebuilt.
	* fficonfig.h.in: Rebuilt.
	* src/powerpc/ffi.c (hidden): Remove.
	(ffi_closure_LINUX64, ffi_prep_args64, ffi_call_LINUX64,
	ffi_closure_helper_LINUX64): Use FFI_HIDDEN instead of hidden.
	* src/powerpc/linux64_closure.S (ffi_closure_LINUX64,
	.ffi_closure_LINUX64): Use FFI_HIDDEN instead of .hidden.
	* src/x86/ffi.c (ffi_closure_SYSV, ffi_closure_raw_SYSV): Remove,
	add FFI_HIDDEN to its prototype.
	(ffi_closure_SYSV_inner): New.
	* src/x86/sysv.S (ffi_closure_SYSV, ffi_closure_raw_SYSV): New.
	* src/x86/win32.S (ffi_closure_SYSV, ffi_closure_raw_SYSV): New.

2005-08-10  Alfred M. Szmidt  <<EMAIL>>

	PR libffi/21819:
	* configure: Rebuilt.
	* configure.ac: Handle i*86-*-gnu*.

2005-08-09  Jakub Jelinek  <<EMAIL>>

	* src/powerpc/ppc_closure.S (ffi_closure_SYSV): Use
	DW_CFA_offset_extended_sf rather than
	DW_CFA_GNU_negative_offset_extended.
	* src/powerpc/sysv.S (ffi_call_SYSV): Likewise.

2005-07-22  SUGIOKA Toshinobu  <<EMAIL>>

	* src/sh/sysv.S (ffi_call_SYSV): Stop argument popping correctly
	on sh3.
	(ffi_closure_SYSV): Change the stack layout for sh3 struct argument.
	* src/sh/ffi.c (ffi_prep_args): Fix sh3 argument copy, when it is
	partially on register.
	(ffi_closure_helper_SYSV): Likewise.
	(ffi_prep_cif_machdep): Don't set too many cif->flags.

2005-07-20  Kaz Kojima  <<EMAIL>>

	* src/sh/ffi.c (ffi_call): Handle small structures correctly.
	Remove empty line.
	* src/sh64/ffi.c (simple_type): Remove.
	(return_type): Handle small structures correctly.
	(ffi_prep_args): Likewise.
	(ffi_call): Likewise.
	(ffi_closure_helper_SYSV): Likewise.
	* src/sh64/sysv.S (ffi_call_SYSV): Handle 1, 2 and 4-byte return.
	Emit position independent code if PIC and remove wrong datalabel
	prefixes from EH data.

2005-07-19  Andreas Tobler  <<EMAIL>>

	* Makefile.am (nodist_libffi_la_SOURCES): Add POWERPC_FREEBSD.
	* Makefile.in: Regenerate.
	* include/Makefile.in: Likewise.
	* testsuite/Makefile.in: Likewise.
	* configure.ac: Add POWERPC_FREEBSD rules.
	* configure: Regenerate.
	* src/powerpc/ffitarget.h: Add POWERPC_FREEBSD rules.
	(FFI_SYSV_TYPE_SMALL_STRUCT): Define.
	* src/powerpc/ffi.c: Add flags to handle small structure returns
	in ffi_call_SYSV.
	(ffi_prep_cif_machdep): Handle small structures for SYSV 4 ABI.
	Aka FFI_SYSV.
	(ffi_closure_helper_SYSV): Likewise.
	* src/powerpc/ppc_closure.S: Add return types for small structures.
	* src/powerpc/sysv.S: Add bits to handle small structures for
	final SYSV 4 ABI.

2005-07-10  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/cls_5_1_byte.c: New test file.
	* testsuite/libffi.call/cls_6_1_byte.c: Likewise.
	* testsuite/libffi.call/cls_7_1_byte.c: Likewise.

2005-07-05  Randolph Chung  <<EMAIL>>

	* src/pa/ffi.c (ffi_struct_type): Rename FFI_TYPE_SMALL_STRUCT1
	as FFI_TYPE_SMALL_STRUCT3.  Break out handling for 5-7 byte
	structures.  Kill compilation warnings.
	(ffi_closure_inner_LINUX): Print return values as hex in debug
	message.  Rename FFI_TYPE_SMALL_STRUCT1 as FFI_TYPE_SMALL_STRUCT3.
	Properly handle 5-7 byte structure returns.
	* src/pa/ffitarget.h (FFI_TYPE_SMALL_STRUCT1)
	(FFI_TYPE_SMALL_STRUCT2): Remove.
	(FFI_TYPE_SMALL_STRUCT3, FFI_TYPE_SMALL_STRUCT5)
	(FFI_TYPE_SMALL_STRUCT6, FFI_TYPE_SMALL_STRUCT7): Define.
	* src/pa/linux.S: Mark source file as using PA1.1 assembly.
	(checksmst1, checksmst2): Remove.
	(checksmst3): Optimize handling of 3-byte struct returns.
	(checksmst567): Properly handle 5-7 byte struct returns.

2005-06-15  Rainer Orth  <<EMAIL>>

	PR libgcj/21943
	* src/mips/n32.S: Enforce PIC code.
	* src/mips/o32.S: Likewise.

2005-06-15  Rainer Orth  <<EMAIL>>

	* configure.ac: Treat i*86-*-solaris2.10 and up as X86_64.
	* configure: Regenerate.

2005-06-01  Alan Modra  <<EMAIL>>

	* src/powerpc/ppc_closure.S (ffi_closure_SYSV): Don't use JUMPTARGET
	to call ffi_closure_helper_SYSV.  Append @local instead.
	* src/powerpc/sysv.S (ffi_call_SYSV): Likewise for ffi_prep_args_SYSV.

2005-05-17  Kelley Cook  <<EMAIL>>

	* configure.ac: Use AC_C_BIGENDIAN instead of AC_C_BIGENDIAN_CROSS.
	Use AC_CHECK_SIZEOF instead of AC_COMPILE_CHECK_SIZEOF.
	* Makefile.am (ACLOCAL_AMFLAGS): Remove -I ../config.
	* aclocal.m4, configure, fficonfig.h.in, Makefile.in,
	include/Makefile.in, testsuite/Makefile.in: Regenerate.

2005-05-09  Mike Stump  <<EMAIL>>

	* configure: Regenerate.

2005-05-08  Richard Henderson  <<EMAIL>>

	PR libffi/21285
	* src/alpha/osf.S: Update unwind into to match code.

2005-05-04  Andreas Degert <<EMAIL>>
	    Richard Henderson  <<EMAIL>>

	* src/x86/ffi64.c (ffi_prep_cif_machdep): Save sse-used flag in
	bit 11 of flags.
	(ffi_call): Mask return type field.  Pass ssecount to ffi_call_unix64.
	(ffi_prep_closure): Set carry bit if sse-used flag set.
	* src/x86/unix64.S (ffi_call_unix64): Add ssecount argument.
	Only load sse registers if ssecount non-zero.
	(ffi_closure_unix64): Only save sse registers if carry set on entry.

2005-04-29  Ralf Corsepius  <<EMAIL>>

	* configure.ac: Add i*86-*-rtems*, sparc*-*-rtems*,
	powerpc-*rtems*, arm*-*-rtems*, sh-*-rtems*.
	* configure: Regenerate.

2005-04-20  Hans-Peter Nilsson  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp (libffi-dg-test-1): In regsub use,
	have Tcl8.3-compatible intermediate variable.

2005-04-18  Simon Posnjak <<EMAIL>>
	    Hans-Peter Nilsson  <<EMAIL>>

	* Makefile.am: Add CRIS support.
	* configure.ac: Likewise.
	* Makefile.in, configure, testsuite/Makefile.in,
	include/Makefile.in: Regenerate.
	* src/cris: New directory.
	* src/cris/ffi.c, src/cris/sysv.S, src/cris/ffitarget.h: New files.
	* src/prep_cif.c (ffi_prep_cif): Wrap in #ifndef __CRIS__.

	* testsuite/lib/libffi-dg.exp (libffi-dg-test-1): Replace \n with
	\r?\n in output tests.

2005-04-12  Mike Stump  <<EMAIL>>

	* configure: Regenerate.

2005-03-30  Hans Boehm  <<EMAIL>>

	* src/ia64/ffitarget.h (ffi_arg): Use long long instead of DI.

2005-03-30  Steve Ellcey  <<EMAIL>>

	* src/ia64/ffitarget.h (ffi_arg) ADD DI attribute.
	(ffi_sarg) Ditto.
	* src/ia64/unix.S (ffi_closure_unix): Extend gp
	to 64 bits in ILP32 mode.
	Load 64 bits even for short data.

2005-03-23  Mike Stump  <<EMAIL>>

	* src/powerpc/darwin.S: Update for -m64 multilib.
	* src/powerpc/darwin_closure.S: Likewise.

2005-03-21  Zack Weinberg  <<EMAIL>>

	* configure.ac: Do not invoke TL_AC_GCC_VERSION.
	Do not set tool_include_dir.
	* aclocal.m4, configure, Makefile.in, testsuite/Makefile.in:
	Regenerate.
	* include/Makefile.am: Set gcc_version and toollibffidir.
	* include/Makefile.in: Regenerate.

2005-02-22  Andrew Haley  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_cif_machdep): Bump alignment to
	odd-numbered register pairs for 64-bit integer types.

2005-02-23  Andreas Tobler  <<EMAIL>>

	PR libffi/20104
	* testsuite/libffi.call/return_ll1.c: New test case.

2005-02-11  Janis Johnson  <<EMAIL>>

	* testsuite/libffi.call/cls_align_longdouble.c: Remove dg-options.
	* testsuite/libffi.call/float.c: Ditto.
	* testsuite/libffi.call/float2.c: Ditto.
	* testsuite/libffi.call/float3.c: Ditto.

2005-02-08  Andreas Tobler  <<EMAIL>>

	* src/frv/ffitarget.h: Remove PPC stuff which does not belong to frv.

2005-01-12  Eric Botcazou  <<EMAIL>>

	* testsuite/libffi.special/special.exp (cxx_options): Add
	-shared-libgcc.

2004-12-31  Richard Henderson  <<EMAIL>>

	* src/types.c (FFI_AGGREGATE_TYPEDEF): Remove.
	(FFI_TYPEDEF): Rename from FFI_INTEGRAL_TYPEDEF.  Replace size and
	offset parameters with a type parameter; deduce size and structure
	alignment.  Update all users.

2004-12-31  Richard Henderson  <<EMAIL>>

	* src/types.c (FFI_TYPE_POINTER): Define with sizeof.
	(FFI_TYPE_LONGDOUBLE): Fix for ia64.
	* src/ia64/ffitarget.h (struct ffi_ia64_trampoline_struct): Move
	into ffi_prep_closure.
	* src/ia64/ia64_flags.h, src/ia64/ffi.c, src/ia64/unix.S: Rewrite
	from scratch.

2004-12-27  Richard Henderson  <<EMAIL>>

	* src/x86/unix64.S: Fix typo in unwind info.

2004-12-25  Richard Henderson  <<EMAIL>>

	* src/x86/ffi64.c (struct register_args): Rename from stackLayout.
	(enum x86_64_reg_class): Add X86_64_COMPLEX_X87_CLASS.
	(merge_classes): Check for it.
	(SSE_CLASS_P): New.
	(classify_argument): Pass byte_offset by value; perform all updates
	inside struct case.
	(examine_argument): Add classes argument; handle
	X86_64_COMPLEX_X87_CLASS.
	(ffi_prep_args): Merge into ...
	(ffi_call): ... here.  Share stack frame with ffi_call_unix64.
	(ffi_prep_cif_machdep): Setup cif->flags for proper structure return.
	(ffi_fill_return_value): Remove.
	(ffi_prep_closure): Remove dead assert.
	(ffi_closure_unix64_inner): Rename from ffi_closure_UNIX64_inner.
	Rewrite to use struct register_args instead of va_list.  Create
	flags for handling structure returns.
	* src/x86/unix64.S: Remove dead strings.
	(ffi_call_unix64): Rename from ffi_call_UNIX64.  Rewrite to share
	stack frame with ffi_call.  Handle structure returns properly.
	(float2sse, floatfloat2sse, double2sse): Remove.
	(sse2float, sse2double, sse2floatfloat): Remove.
	(ffi_closure_unix64): Rename from ffi_closure_UNIX64.  Rewrite
	to handle structure returns properly.

2004-12-08  David Edelsohn  <<EMAIL>>

	* Makefile.am (AM_MAKEFLAGS): Remove duplicate LIBCFLAGS and
	PICFLAG.
	* Makefile.in: Regenerated.

2004-12-02  Richard Sandiford  <<EMAIL>>

	* configure.ac: Use TL_AC_GCC_VERSION to set gcc_version.
	* configure, aclocal.m4, Makefile.in: Regenerate.
	* include/Makefile.in, testsuite/Makefile.in: Regenerate.

2004-11-29  Kelley Cook  <<EMAIL>>

	* configure: Regenerate for libtool change.

2004-11-25  Kelley Cook  <<EMAIL>>

	* configure: Regenerate for libtool reversion.

2004-11-24  Kelley Cook  <<EMAIL>>

	* configure: Regenerate for libtool change.

2004-11-23  John David Anglin  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp: Use new procs in target-libpath.exp.

2004-11-23  Richard Sandiford  <<EMAIL>>

	* src/mips/o32.S (ffi_call_O32, ffi_closure_O32): Use jalr instead
	of jal.  Use an absolute encoding for the frame information.

2004-11-23  Kelley Cook  <<EMAIL>>

	* Makefile.am: Remove no-dependencies.  Add ACLOCAL_AMFLAGS.
	* acinclude.m4: Delete logic for sincludes.
	* aclocal.m4, Makefile.in, configure: Regenerate.
	* include/Makefile: Likewise.
	* testsuite/Makefile: Likewise.

2004-11-22  Eric Botcazou  <<EMAIL>>

	* src/sparc/ffi.c (ffi_prep_closure): Align doubles and 64-bit integers
	on a 8-byte boundary.
	* src/sparc/v8.S (ffi_closure_v8): Reserve frame space for arguments.

2004-10-27  Richard Earnshaw  <<EMAIL>>

	* src/arm/ffi.c (ffi_prep_cif_machdep): Handle functions that return
	long long values.  Round stack allocation to a multiple of 8 bytes
	for ATPCS compatibility.
	* src/arm/sysv.S (ffi_call_SYSV): Rework to avoid use of APCS register
	names.  Handle returning long long types.  Add Thumb and interworking
	support.  Improve soft-float code.

2004-10-27  Richard Earnshaw  <<EMAIL>>

	* testsuite/lib/libffi-db.exp (load_gcc_lib): New function.
	(libffi_exit): New function.
	(libffi_init): Build the testglue wrapper if needed.

2004-10-25  Eric Botcazou  <<EMAIL>>

	PR other/18138
	* testsuite/lib/libffi-dg.exp: Accept more than one multilib libgcc.

2004-10-25  Kazuhiro Inaoka <<EMAIL>>

	* src/m32r/libffitarget.h (FFI_CLOSURES): Set to 0.

2004-10-20  Kaz Kojima  <<EMAIL>>

	* src/sh/sysv.S (ffi_call_SYSV): Don't align for double data.
	* testsuite/libffi.call/float3.c: New test case.

2004-10-18  Kaz Kojima  <<EMAIL>>

	* src/sh/ffi.c (ffi_prep_closure): Set T bit in trampoline for
	the function returning a structure pointed with R2.
	* src/sh/sysv.S (ffi_closure_SYSV): Use R2 as the pointer to
	the structure return value if T bit set.  Emit position
	independent code and EH data if PIC.

2004-10-13  Kazuhiro Inaoka  <<EMAIL>>

	* Makefile.am: Add m32r support.
	* configure.ac: Likewise.
	* Makefile.in: Regenerate.
	* confiugre: Regenerate.
	* src/types.c: Add m32r port to FFI_INTERNAL_TYPEDEF
	(uint64, sint64, double, longdouble)
	* src/m32r: New directory.
	* src/m32r/ffi.c: New file.
	* src/m32r/sysv.S: Likewise.
	* src/m32r/ffitarget.h: Likewise.

2004-10-02  Kaz Kojima  <<EMAIL>>

	* testsuite/libffi.call/negint.c: New test case.

2004-09-14  H.J. Lu  <<EMAIL>>

	PR libgcj/17465
	* testsuite/lib/libffi-dg.exp: Don't use global ld_library_path.
	Set up LD_LIBRARY_PATH, SHLIB_PATH, LD_LIBRARYN32_PATH,
	LD_LIBRARY64_PATH, LD_LIBRARY_PATH_32, LD_LIBRARY_PATH_64 and
	DYLD_LIBRARY_PATH.

2004-09-05  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/many_win32.c: Remove whitespaces.
	* testsuite/libffi.call/promotion.c: Likewise.
	* testsuite/libffi.call/return_ll.c: Remove unused var. Cleanup
	whitespaces.
	* testsuite/libffi.call/return_sc.c: Likewise.
	* testsuite/libffi.call/return_uc.c: Likewise.

2004-09-05  Andreas Tobler  <<EMAIL>>

	* src/powerpc/darwin.S: Fix comments and identation.
	* src/powerpc/darwin_closure.S: Likewise.

2004-09-02  Andreas Tobler  <<EMAIL>>

	* src/powerpc/ffi_darwin.c: Add flag for longdouble return values.
	(ffi_prep_args): Handle longdouble arguments.
	(ffi_prep_cif_machdep): Set flags for longdouble. Calculate space for
	longdouble.
	(ffi_closure_helper_DARWIN): Add closure handling for longdouble.
	* src/powerpc/darwin.S (_ffi_call_DARWIN): Add handling of longdouble
	values.
	* src/powerpc/darwin_closure.S (_ffi_closure_ASM): Likewise.
	* src/types.c: Defined longdouble size and alignment for darwin.

2004-09-02  Andreas Tobler  <<EMAIL>>

	* src/powerpc/aix.S: Remove whitespaces.
	* src/powerpc/aix_closure.S: Likewise.
	* src/powerpc/asm.h: Likewise.
	* src/powerpc/ffi.c: Likewise.
	* src/powerpc/ffitarget.h: Likewise.
	* src/powerpc/linux64.S: Likewise.
	* src/powerpc/linux64_closure.S: Likewise.
	* src/powerpc/ppc_closure.S: Likewise.
	* src/powerpc/sysv.S: Likewise.

2004-08-30  Anthony Green  <<EMAIL>>

	* Makefile.am: Add frv support.
	* Makefile.in, testsuite/Makefile.in: Rebuilt.
	* configure.ac: Read configure.host.
	* configure.in: Read configure.host.
	* configure.host: New file.  frv-elf needs libgloss.
	* include/ffi.h.in: Force ffi_closure to have a nice big (8)
	alignment.  This is needed to frv and shouldn't harm the others.
	* include/ffi_common.h (ALIGN_DOWN): New macro.
	* src/frv/ffi.c, src/frv/ffitarget.h, src/frv/eabi.S: New files.

2004-08-24  David Daney  <<EMAIL>>

	* testsuite/libffi.call/closure_fn0.c: Xfail mips64* instead of mips*.
	* testsuite/libffi.call/closure_fn1.c: Likewise.
	* testsuite/libffi.call/closure_fn2.c  Likewise.
	* testsuite/libffi.call/closure_fn3.c: Likewise.
	* testsuite/libffi.call/closure_fn4.c: Likewise.
	* testsuite/libffi.call/closure_fn5.c: Likewise.
	* testsuite/libffi.call/cls_18byte.c: Likewise.
	* testsuite/libffi.call/cls_19byte.c: Likewise.
	* testsuite/libffi.call/cls_1_1byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte1.c: Likewise.
	* testsuite/libffi.call/cls_24byte.c: Likewise.
	* testsuite/libffi.call/cls_2byte.c: Likewise.
	* testsuite/libffi.call/cls_3_1byte.c: Likewise.
	* testsuite/libffi.call/cls_3byte1.c: Likewise.
	* testsuite/libffi.call/cls_3byte2.c: Likewise.
	* testsuite/libffi.call/cls_4_1byte.c: Likewise.
	* testsuite/libffi.call/cls_4byte.c: Likewise.
	* testsuite/libffi.call/cls_64byte.c: Likewise.
	* testsuite/libffi.call/cls_6byte.c: Likewise.
	* testsuite/libffi.call/cls_7byte.c: Likewise.
	* testsuite/libffi.call/cls_8byte.c: Likewise.
	* testsuite/libffi.call/cls_9byte1.c: Likewise.
	* testsuite/libffi.call/cls_9byte2.c: Likewise.
	* testsuite/libffi.call/cls_align_double.c: Likewise.
	* testsuite/libffi.call/cls_align_float.c: Likewise.
	* testsuite/libffi.call/cls_align_longdouble.c: Likewise.
	* testsuite/libffi.call/cls_align_pointer.c: Likewise.
	* testsuite/libffi.call/cls_align_sint16.c: Likewise.
	* testsuite/libffi.call/cls_align_sint32.c: Likewise.
	* testsuite/libffi.call/cls_align_sint64.c: Likewise.
	* testsuite/libffi.call/cls_align_uint16.c: Likewise.
	* testsuite/libffi.call/cls_align_uint32.c: Likewise.
	* testsuite/libffi.call/cls_align_uint64.c: Likewise.
	* testsuite/libffi.call/cls_double.c: Likewise.
	* testsuite/libffi.call/cls_float.c: Likewise.
	* testsuite/libffi.call/cls_multi_schar.c: Likewise.
	* testsuite/libffi.call/cls_multi_sshort.c: Likewise.
	* testsuite/libffi.call/cls_multi_sshortchar.c: Likewise.
	* testsuite/libffi.call/cls_multi_uchar.c: Likewise.
	* testsuite/libffi.call/cls_multi_ushort.c: Likewise.
	* testsuite/libffi.call/cls_multi_ushortchar.c: Likewise.
	* testsuite/libffi.call/cls_schar.c: Likewise.
	* testsuite/libffi.call/cls_sint.c: Likewise.
	* testsuite/libffi.call/cls_sshort.c: Likewise.
	* testsuite/libffi.call/cls_uchar.c: Likewise.
	* testsuite/libffi.call/cls_uint.c: Likewise.
	* testsuite/libffi.call/cls_ulonglong.c: Likewise.
	* testsuite/libffi.call/cls_ushort.c: Likewise.
	* testsuite/libffi.call/nested_struct.c: Likewise.
	* testsuite/libffi.call/nested_struct1.c: Likewise.
	* testsuite/libffi.call/nested_struct2.c: Likewise.
	* testsuite/libffi.call/nested_struct3.c: Likewise.
	* testsuite/libffi.call/problem1.c: Likewise.
	* testsuite/libffi.special/unwindtest.cc: Likewise.
	* testsuite/libffi.call/cls_12byte.c: Likewise and set return value
	to zero.
	* testsuite/libffi.call/cls_16byte.c: Likewise.
	* testsuite/libffi.call/cls_5byte.c: Likewise.

2004-08-23  David Daney <<EMAIL>>

	PR libgcj/13141
	* src/mips/ffitarget.h (FFI_O32_SOFT_FLOAT): New ABI.
	* src/mips/ffi.c (ffi_prep_args): Fix alignment calculation.
	(ffi_prep_cif_machdep): Handle FFI_O32_SOFT_FLOAT floating point
	parameters and return types.
	(ffi_call): Handle FFI_O32_SOFT_FLOAT ABI.
	(ffi_prep_closure): Ditto.
	(ffi_closure_mips_inner_O32): Handle FFI_O32_SOFT_FLOAT ABI, fix
	alignment calculations.
	* src/mips/o32.S (ffi_closure_O32): Don't use floating point
	instructions if FFI_O32_SOFT_FLOAT, make stack frame ABI compliant.

2004-08-14  Casey Marshall <<EMAIL>>

	* src/mips/ffi.c (ffi_pref_cif_machdep): set `cif->flags' to
	contain `FFI_TYPE_UINT64' as return type for any 64-bit
	integer (O32 ABI only).
	(ffi_prep_closure): new function.
	(ffi_closure_mips_inner_O32): new function.
	* src/mips/ffitarget.h: Define `FFI_CLOSURES' and
	`FFI_TRAMPOLINE_SIZE' appropriately if the ABI is o32.
	* src/mips/o32.S (ffi_call_O32): add labels for .eh_frame. Return
	64 bit integers correctly.
	(ffi_closure_O32): new function.
	Added DWARF-2 unwind info for both functions.

2004-08-10  Andrew Haley  <<EMAIL>>

	* src/x86/ffi64.c (ffi_prep_args ): 8-align all stack arguments.

2004-08-01  Robert Millan  <<EMAIL>>

	* configure.ac: Detect knetbsd-gnu and kfreebsd-gnu.
	* configure: Regenerate.

2004-07-30  Maciej W. Rozycki  <<EMAIL>>

	* acinclude.m4 (AC_FUNC_MMAP_BLACKLIST): Check for <sys/mman.h>
	and mmap() explicitly instead of relying on preset autoconf cache
	variables.
	* aclocal.m4: Regenerate.
	* configure: Regenerate.

2004-07-11  Ulrich Weigand  <<EMAIL>>

	* src/s390/ffi.c (ffi_prep_args): Fix C aliasing violation.
	(ffi_check_float_struct): Remove unused prototype.

2004-06-30  Geoffrey Keating  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (flush_icache): ';' is a comment
	character on Darwin, use '\n\t' instead.

2004-06-26  Matthias Klose  <<EMAIL>>

	* libtool-version: Fix typo in revision/age.

2004-06-17  Matthias Klose  <<EMAIL>>

	* libtool-version: New.
	* Makefile.am (libffi_la_LDFLAGS): Use -version-info for soname.
	* Makefile.in: Regenerate.

2004-06-15  Paolo Bonzini  <<EMAIL>>

	* Makefile.am: Remove useless multilib rules.
	* Makefile.in: Regenerate.
	* aclocal.m4: Regenerate with automake 1.8.5.
	* configure.ac: Remove useless multilib configury.
	* configure: Regenerate.

2004-06-15  Paolo Bonzini  <<EMAIL>>

	* .cvsignore: New file.

2004-06-10  Jakub Jelinek  <<EMAIL>>

	* src/ia64/unix.S (ffi_call_unix): Insert group barrier break
	fp_done.
	(ffi_closure_UNIX): Fix f14/f15 adjustment if FLOAT_SZ is ever
	changed from 8.

2004-06-06  Sean McNeil  <<EMAIL>>

	* configure.ac: Add x86_64-*-freebsd* support.
	* configure: Regenerate.

2004-04-26  Joe Buck <<EMAIL>>

	Bug 15093
	* configure.ac: Test for existence of mmap and sys/mman.h before
	checking blacklist.  Fix suggested by Jim Wilson.
	* configure: Regenerate.

2004-04-26  Matt Austern  <<EMAIL>>

	* src/powerpc/darwin.S: Go through a non-lazy pointer for initial
	FDE location.
	* src/powerpc/darwin_closure.S: Likewise.

2004-04-24  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/cls_multi_schar.c (main): Fix initialization
	error. Reported by Thomas Heller <<EMAIL>>.
	* testsuite/libffi.call/cls_multi_sshort.c (main): Likewise.
	* testsuite/libffi.call/cls_multi_ushort.c (main): Likewise.

2004-03-20  Matthias Klose  <<EMAIL>>

	* src/pa/linux.S: Fix typo.

2004-03-19  Matthias Klose  <<EMAIL>>

	* Makefile.am: Update.
	* Makefile.in: Regenerate.
	* src/pa/ffi.h.in: Remove.
	* src/pa/ffitarget.h: New file.

2004-02-10  Randolph Chung  <<EMAIL>>

	* Makefile.am: Add PA support.
	* Makefile.in: Regenerate.
	* include/Makefile.in: Regenerate.
	* configure.ac: Add PA target.
	* configure: Regenerate.
	* src/pa/ffi.c: New file.
	* src/pa/ffi.h.in: Add PA support.
	* src/pa/linux.S: New file.
	* prep_cif.c: Add PA support.

2004-03-16  Hosaka Yuji  <<EMAIL>>

	* src/types.c: Fix alignment size of X86_WIN32 case int64 and
	double.
	* src/x86/ffi.c (ffi_prep_args): Replace ecif->cif->rtype->type
	with ecif->cif->flags.
	(ffi_call, ffi_prep_incoming_args_SYSV): Replace cif->rtype->type
	with cif->flags.
	(ffi_prep_cif_machdep): Add X86_WIN32 struct case.
	(ffi_closure_SYSV): Add 1 or 2-bytes struct case for X86_WIN32.
	* src/x86/win32.S (retstruct1b, retstruct2b, sc_retstruct1b,
	sc_retstruct2b): Add for 1 or 2-bytes struct case.

2004-03-15 Kelley Cook <<EMAIL>>

	* configure.in: Rename file to ...
	* configure.ac: ... this.
	* fficonfig.h.in: Regenerate.
	* Makefile.in: Regenerate.
	* include/Makefile.in: Regenerate.
	* testsuite/Makefile.in: Regenerate.

2004-03-12  Matt Austern  <<EMAIL>>

	* src/powerpc/darwin.S: Fix EH information so it corresponds to
	changes in EH format resulting from addition of linkonce support.
	* src/powerpc/darwin_closure.S: Likewise.

2004-03-11  Andreas Tobler  <<EMAIL>>
	    Paolo Bonzini  <<EMAIL>>

	* Makefile.am (AUTOMAKE_OPTIONS): Set them.
	Remove VPATH. Remove rules for object files. Remove multilib support.
	(AM_CCASFLAGS): Add.
	* configure.in (AC_CONFIG_HEADERS): Relace AM_CONFIG_HEADER.
	(AC_PREREQ): Bump version to 2.59.
	(AC_INIT): Fill with version info and bug address.
	(ORIGINAL_LD_FOR_MULTILIBS): Remove.
	(AM_ENABLE_MULTILIB): Use this instead of AC_ARG_ENABLE.
	De-precious CC so that the right flags are passed down to multilibs.
	(AC_MSG_ERROR): Replace obsolete macro AC_ERROR.
	(AC_CONFIG_FILES): Replace obsolete macro AC_LINK_FILES.
	(AC_OUTPUT): Reorganize the output with AC_CONFIG_COMMANDS.
	* configure: Rebuilt.
	* aclocal.m4: Likewise.
	* Makefile.in, include/Makefile.in, testsuite/Makefile.in: Likewise.
	* fficonfig.h.in: Likewise.

2004-03-11  Andreas Schwab  <<EMAIL>>

	* src/ia64/ffi.c (ffi_prep_incoming_args_UNIX): Get floating point
	arguments from fp registers only for the first 8 parameter slots.
	Don't convert a float parameter when passed in memory.

2004-03-09  Hans-Peter Nilsson  <<EMAIL>>

	* configure: Regenerate for config/accross.m4 correction.

2004-02-25  Matt Kraai  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_args_SYSV): Change
	ecif->cif->bytes to bytes.
	(ffi_prep_cif_machdep): Add braces around nested if statement.

2004-02-09  Alan Modra  <<EMAIL>>

	* src/types.c (pointer): POWERPC64 has 8 byte pointers.

	* src/powerpc/ffi.c (ffi_prep_args64): Correct long double handling.
	(ffi_closure_helper_LINUX64): Fix typo.
	* testsuite/libffi.call/cls_align_longdouble.c: Pass -mlong-double-128
	for powerpc64-*-*.
	* testsuite/libffi.call/float.c: Likewise.
	* testsuite/libffi.call/float2.c: Likewise.

2004-02-08  Alan Modra  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_cif_machdep <FFI_LINUX64>): Correct
	long double function return and long double arg handling.
	(ffi_closure_helper_LINUX64): Formatting.  Delete unused "ng" var.
	Use "end_pfr" instead of "nf".  Correct long double handling.
	Localise "temp".
	* src/powerpc/linux64.S (ffi_call_LINUX64): Save f2 long double
	return value.
	* src/powerpc/linux64_closure.S (ffi_closure_LINUX64): Allocate
	space for long double return value.  Adjust stack frame and offsets.
	Load f2 long double return.

2004-02-07  Alan Modra  <<EMAIL>>

	* src/types.c: Use 16 byte long double for POWERPC64.

2004-01-25  Eric Botcazou  <<EMAIL>>

	* src/sparc/ffi.c (ffi_prep_args_v9): Shift the parameter array
	when the structure return address is passed in %o0.
	(ffi_V9_return_struct): Rename into ffi_v9_layout_struct.
	(ffi_v9_layout_struct): Align the field following a nested structure
	on a word boundary.  Use memmove instead of memcpy.
	(ffi_call): Update call to ffi_V9_return_struct.
	(ffi_prep_closure): Define 'ctx' only for V8.
	(ffi_closure_sparc_inner): Clone into ffi_closure_sparc_inner_v8
	and ffi_closure_sparc_inner_v9.
	(ffi_closure_sparc_inner_v8): Return long doubles by reference.
	Always skip the structure return address.  For structures and long
	doubles, copy the argument directly.
	(ffi_closure_sparc_inner_v9): Skip the structure return address only
	if required.  Shift the maximum floating-point slot accordingly.  For
	big structures, copy the argument directly; otherwise, left-justify the
	argument and call ffi_v9_layout_struct to lay out the structure on
	the stack.
	* src/sparc/v8.S: Undef STACKFRAME before defining it.
	(ffi_closure_v8): Pass the structure return address.  Update call to
	ffi_closure_sparc_inner_v8.  Short-circuit FFI_TYPE_INT handling.
	Skip the 'unimp' insn when returning long doubles and structures.
	* src/sparc/v9.S: Undef STACKFRAME before defining it.
	(ffi_closure_v9): Increase the frame size by 2 words.  Short-circuit
	FFI_TYPE_INT handling.  Load structures both in integers and
	floating-point registers on return.
	* README: Update status of the SPARC port.

2004-01-24  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/pyobjc-tc.c (main): Treat result value
	as of type ffi_arg.
	* testsuite/libffi.call/struct3.c (main): Fix CHECK.

2004-01-22  Ulrich Weigand  <<EMAIL>>

	* testsuite/libffi.call/cls_uint.c (cls_ret_uint_fn): Treat result
	value as of type ffi_arg, not unsigned int.

2004-01-21  Michael Ritzert  <<EMAIL>>

	* ffi64.c (ffi_prep_args): Cast the RHS of an assignment instead
	of the LHS.

2004-01-12  Andreas Tobler  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp: Set LD_LIBRARY_PATH_32 for
	Solaris.

2004-01-08  Rainer Orth  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h (allocate_mmap): Cast MAP_FAILED
	to void *.

2003-12-10  Richard Henderson  <<EMAIL>>

	* testsuite/libffi.call/cls_align_pointer.c: Cast pointers to
	size_t instead of int.

2003-12-04  Hosaka Yuji  <<EMAIL>>

	* testsuite/libffi.call/many_win32.c: Include <float.h>.
	* testsuite/libffi.call/many_win32.c (main): Replace variable
	int i with unsigned long ul.

	* testsuite/libffi.call/cls_align_uint64.c: New test case.
	* testsuite/libffi.call/cls_align_sint64.c: Likewise.
	* testsuite/libffi.call/cls_align_uint32.c: Likewise.
	* testsuite/libffi.call/cls_align_sint32.c: Likewise.
	* testsuite/libffi.call/cls_align_uint16.c: Likewise.
	* testsuite/libffi.call/cls_align_sint16.c: Likewise.
	* testsuite/libffi.call/cls_align_float.c: Likewise.
	* testsuite/libffi.call/cls_align_double.c: Likewise.
	* testsuite/libffi.call/cls_align_longdouble.c: Likewise.
	* testsuite/libffi.call/cls_align_pointer.c: Likewise.

2003-12-02  Hosaka Yuji  <<EMAIL>>

	PR other/13221
	* src/x86/ffi.c (ffi_prep_args, ffi_prep_incoming_args_SYSV):
	Align arguments to 32 bits.

2003-12-01  Andreas Tobler  <<EMAIL>>

	PR other/13221
	* testsuite/libffi.call/cls_multi_sshort.c: New test case.
	* testsuite/libffi.call/cls_multi_sshortchar.c: Likewise.
	* testsuite/libffi.call/cls_multi_uchar.c: Likewise.
	* testsuite/libffi.call/cls_multi_schar.c: Likewise.
	* testsuite/libffi.call/cls_multi_ushortchar.c: Likewise.
	* testsuite/libffi.call/cls_multi_ushort.c: Likewise.

	* testsuite/libffi.special/unwindtest.cc: Cosmetics.

2003-11-26  Kaveh R. Ghazi  <<EMAIL>>

	* testsuite/libffi.call/ffitest.h: Include <fcntl.h>.
	* testsuite/libffi.special/ffitestcxx.h: Likewise.

2003-11-22  Andreas Tobler  <<EMAIL>>

	* Makefile.in: Rebuilt.
	* configure: Likewise.
	* testsuite/libffi.special/unwindtest.cc: Convert the mmap to
	the right type.

2003-11-21  Andreas Jaeger  <<EMAIL>>
	    Andreas Tobler  <<EMAIL>>

	* acinclude.m4: Add AC_FUNC_MMAP_BLACKLIST.
	* configure.in: Call AC_FUNC_MMAP_BLACKLIST.
	* Makefile.in: Rebuilt.
	* aclocal.m4: Likewise.
	* configure: Likewise.
	* fficonfig.h.in: Likewise.
	* testsuite/lib/libffi-dg.exp: Add include dir.
	* testsuite/libffi.call/ffitest.h: Add MMAP definitions.
	* testsuite/libffi.special/ffitestcxx.h: Likewise.
	* testsuite/libffi.call/closure_fn0.c: Use MMAP functionality
	for ffi_closure if available.
	* testsuite/libffi.call/closure_fn1.c: Likewise.
	* testsuite/libffi.call/closure_fn2.c: Likewise.
	* testsuite/libffi.call/closure_fn3.c: Likewise.
	* testsuite/libffi.call/closure_fn4.c: Likewise.
	* testsuite/libffi.call/closure_fn5.c: Likewise.
	* testsuite/libffi.call/cls_12byte.c: Likewise.
	* testsuite/libffi.call/cls_16byte.c: Likewise.
	* testsuite/libffi.call/cls_18byte.c: Likewise.
	* testsuite/libffi.call/cls_19byte.c: Likewise.
	* testsuite/libffi.call/cls_1_1byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte1.c: Likewise.
	* testsuite/libffi.call/cls_24byte.c: Likewise.
	* testsuite/libffi.call/cls_2byte.c: Likewise.
	* testsuite/libffi.call/cls_3_1byte.c: Likewise.
	* testsuite/libffi.call/cls_3byte1.c: Likewise.
	* testsuite/libffi.call/cls_3byte2.c: Likewise.
	* testsuite/libffi.call/cls_4_1byte.c: Likewise.
	* testsuite/libffi.call/cls_4byte.c: Likewise.
	* testsuite/libffi.call/cls_5byte.c: Likewise.
	* testsuite/libffi.call/cls_64byte.c: Likewise.
	* testsuite/libffi.call/cls_6byte.c: Likewise.
	* testsuite/libffi.call/cls_7byte.c: Likewise.
	* testsuite/libffi.call/cls_8byte.c: Likewise.
	* testsuite/libffi.call/cls_9byte1.c: Likewise.
	* testsuite/libffi.call/cls_9byte2.c: Likewise.
	* testsuite/libffi.call/cls_double.c: Likewise.
	* testsuite/libffi.call/cls_float.c: Likewise.
	* testsuite/libffi.call/cls_schar.c: Likewise.
	* testsuite/libffi.call/cls_sint.c: Likewise.
	* testsuite/libffi.call/cls_sshort.c: Likewise.
	* testsuite/libffi.call/cls_uchar.c: Likewise.
	* testsuite/libffi.call/cls_uint.c: Likewise.
	* testsuite/libffi.call/cls_ulonglong.c: Likewise.
	* testsuite/libffi.call/cls_ushort.c: Likewise.
	* testsuite/libffi.call/nested_struct.c: Likewise.
	* testsuite/libffi.call/nested_struct1.c: Likewise.
	* testsuite/libffi.call/nested_struct2.c: Likewise.
	* testsuite/libffi.call/nested_struct3.c: Likewise.
	* testsuite/libffi.call/problem1.c: Likewise.
	* testsuite/libffi.special/unwindtest.cc: Likewise.

2003-11-20  Andreas Tobler  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp: Make the -lgcc_s conditional.

2003-11-19  Andreas Tobler  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp: Add DYLD_LIBRARY_PATH for darwin.
	Add -lgcc_s to additional flags.

2003-11-12  Andreas Tobler  <<EMAIL>>

	* configure.in, include/Makefile.am: PR libgcj/11147, install
	the ffitarget.h header file in a gcc versioned and target
	dependent place.
	* configure: Regenerated.
	* Makefile.in, include/Makefile.in: Likewise.
	* testsuite/Makefile.in: Likewise.

2003-11-09  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/closure_fn0.c: Print result and check
	with dg-output to make debugging easier.
	* testsuite/libffi.call/closure_fn1.c: Likewise.
	* testsuite/libffi.call/closure_fn2.c: Likewise.
	* testsuite/libffi.call/closure_fn3.c: Likewise.
	* testsuite/libffi.call/closure_fn4.c: Likewise.
	* testsuite/libffi.call/closure_fn5.c: Likewise.
	* testsuite/libffi.call/cls_12byte.c: Likewise.
	* testsuite/libffi.call/cls_16byte.c: Likewise.
	* testsuite/libffi.call/cls_18byte.c: Likewise.
	* testsuite/libffi.call/cls_19byte.c: Likewise.
	* testsuite/libffi.call/cls_1_1byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte1.c: Likewise.
	* testsuite/libffi.call/cls_24byte.c: Likewise.
	* testsuite/libffi.call/cls_2byte.c: Likewise.
	* testsuite/libffi.call/cls_3_1byte.c: Likewise.
	* testsuite/libffi.call/cls_3byte1.c: Likewise.
	* testsuite/libffi.call/cls_3byte2.c: Likewise.
	* testsuite/libffi.call/cls_4_1byte.c: Likewise.
	* testsuite/libffi.call/cls_4byte.c: Likewise.
	* testsuite/libffi.call/cls_5byte.c: Likewise.
	* testsuite/libffi.call/cls_64byte.c: Likewise.
	* testsuite/libffi.call/cls_6byte.c: Likewise.
	* testsuite/libffi.call/cls_7byte.c: Likewise.
	* testsuite/libffi.call/cls_8byte.c: Likewise.
	* testsuite/libffi.call/cls_9byte1.c: Likewise.
	* testsuite/libffi.call/cls_9byte2.c: Likewise.
	* testsuite/libffi.call/cls_double.c: Likewise.
	* testsuite/libffi.call/cls_float.c: Likewise.
	* testsuite/libffi.call/cls_schar.c: Likewise.
	* testsuite/libffi.call/cls_sint.c: Likewise.
	* testsuite/libffi.call/cls_sshort.c: Likewise.
	* testsuite/libffi.call/cls_uchar.c: Likewise.
	* testsuite/libffi.call/cls_uint.c: Likewise.
	* testsuite/libffi.call/cls_ulonglong.c: Likewise.
	* testsuite/libffi.call/cls_ushort.c: Likewise.
	* testsuite/libffi.call/problem1.c: Likewise.

	* testsuite/libffi.special/unwindtest.cc: Make ffi_closure
	static.

2003-11-08  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/cls_9byte2.c: New test case.
	* testsuite/libffi.call/cls_9byte1.c: Likewise.
	* testsuite/libffi.call/cls_64byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte1.c: Likewise.
	* testsuite/libffi.call/cls_19byte.c: Likewise.
	* testsuite/libffi.call/cls_18byte.c: Likewise.
	* testsuite/libffi.call/closure_fn4.c: Likewise.
	* testsuite/libffi.call/closure_fn5.c: Likewise.
	* testsuite/libffi.call/cls_schar.c: Likewise.
	* testsuite/libffi.call/cls_sint.c: Likewise.
	* testsuite/libffi.call/cls_sshort.c: Likewise.
	* testsuite/libffi.call/nested_struct2.c: Likewise.
	* testsuite/libffi.call/nested_struct3.c: Likewise.

2003-11-08  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/cls_double.c: Do a check on the result.
	* testsuite/libffi.call/cls_uchar.c: Likewise.
	* testsuite/libffi.call/cls_uint.c: Likewise.
	* testsuite/libffi.call/cls_ulonglong.c: Likewise.
	* testsuite/libffi.call/cls_ushort.c: Likewise.
	* testsuite/libffi.call/return_sc.c: Cleanup whitespaces.

2003-11-06  Andreas Tobler  <<EMAIL>>

	* src/prep_cif.c (ffi_prep_cif): Move the validity check after
	the initialization.

2003-10-23  Andreas Tobler  <<EMAIL>>

	* src/java_raw_api.c (ffi_java_ptrarray_to_raw): Replace
	FFI_ASSERT(FALSE) with FFI_ASSERT(0).

2003-10-22  David Daney  <<EMAIL>>

	* src/mips/ffitarget.h: Replace undefined UINT32 and friends with
	__attribute__((__mode__(__SI__))) and friends.

2003-10-22  Andreas Schwab  <<EMAIL>>

	* src/ia64/ffi.c: Replace FALSE/TRUE with false/true.

2003-10-21  Andreas Tobler  <<EMAIL>>

	* configure.in: AC_LINK_FILES(ffitarget.h).
	* configure: Regenerate.
	* Makefile.in: Likewise.
	* include/Makefile.in: Likewise.
	* testsuite/Makefile.in: Likewise.
	* fficonfig.h.in: Likewise.

2003-10-21  Paolo Bonzini  <<EMAIL>>
	    Richard Henderson  <<EMAIL>>

	Avoid that ffi.h includes fficonfig.h.

	* Makefile.am (EXTRA_DIST): Include ffitarget.h files
	(TARGET_SRC_MIPS_GCC): Renamed to TARGET_SRC_MIPS_IRIX.
	(TARGET_SRC_MIPS_SGI): Removed.
	(MIPS_GCC): Renamed to TARGET_SRC_MIPS_IRIX.
	(MIPS_SGI): Removed.
	(CLEANFILES): Removed.
	(mostlyclean-am, clean-am, mostlyclean-sub, clean-sub): New
	targets.
	* acconfig.h: Removed.
	* configure.in: Compute sizeofs only for double and long double.
	Use them to define and subst HAVE_LONG_DOUBLE.  Include comments
	into AC_DEFINE instead of using acconfig.h.  Create
	include/ffitarget.h instead of include/fficonfig.h.  Rename
	MIPS_GCC to MIPS_IRIX, drop MIPS_SGI since we are in gcc's tree.
	AC_DEFINE EH_FRAME_FLAGS.
	* include/Makefile.am (DISTCLEANFILES): New automake macro.
	(hack_DATA): Add ffitarget.h.
	* include/ffi.h.in: Remove all system specific definitions.
	Declare raw API even if it is not installed, why bother?
	Use limits.h instead of SIZEOF_* to define ffi_type_*.  Do
	not define EH_FRAME_FLAGS, it is in fficonfig.h now.  Include
	ffitarget.h instead of fficonfig.h.  Remove ALIGN macro.
	(UINT_ARG, INT_ARG): Removed, use ffi_arg and ffi_sarg instead.
	* include/ffi_common.h (bool): Do not define.
	(ffi_assert): Accept failed assertion.
	(ffi_type_test): Return void and accept file/line.
	(FFI_ASSERT): Pass stringized failed assertion.
	(FFI_ASSERT_AT): New macro.
	(FFI_ASSERT_VALID_TYPE): New macro.
	(UINT8, SINT8, UINT16, SINT16, UINT32, SINT32,
	UINT64, SINT64): Define here with gcc's __attribute__ macro
	instead of in ffi.h
	(FLOAT32, ALIGN): Define here instead of in ffi.h
	* include/ffi-mips.h: Removed.  Its content moved to
	src/mips/ffitarget.h after separating assembly and C sections.
	* src/alpha/ffi.c, src/alpha/ffi.c, src/java_raw_api.c
	src/prep_cif.c, src/raw_api.c, src/ia64/ffi.c,
	src/mips/ffi.c, src/mips/n32.S, src/mips/o32.S,
	src/mips/ffitarget.h, src/sparc/ffi.c, src/x86/ffi64.c:
	SIZEOF_ARG -> FFI_SIZEOF_ARG.
	* src/ia64/ffi.c: Include stdbool.h (provided by GCC 2.95+).
	* src/debug.c (ffi_assert): Accept stringized failed assertion.
	(ffi_type_test): Rewritten.
	* src/prep-cif.c (initialize_aggregate, ffi_prep_cif): Call
	FFI_ASSERT_VALID_TYPE.
	* src/alpha/ffitarget.h, src/arm/ffitarget.h,
	src/ia64/ffitarget.h, src/m68k/ffitarget.h,
	src/mips/ffitarget.h, src/powerpc/ffitarget.h,
	src/s390/ffitarget.h, src/sh/ffitarget.h,
	src/sh64/ffitarget.h, src/sparc/ffitarget.h,
	src/x86/ffitarget.h: New files.
	* src/alpha/osf.S, src/arm/sysv.S, src/ia64/unix.S,
	src/m68k/sysv.S, src/mips/n32.S, src/mips/o32.S,
	src/powerpc/aix.S, src/powerpc/darwin.S,
	src/powerpc/ffi_darwin.c, src/powerpc/linux64.S,
	src/powerpc/linux64_closure.S, src/powerpc/ppc_closure.S,
	src/powerpc/sysv.S, src/s390/sysv.S, src/sh/sysv.S,
	src/sh64/sysv.S, src/sparc/v8.S, src/sparc/v9.S,
	src/x86/sysv.S, src/x86/unix64.S, src/x86/win32.S:
	include fficonfig.h

2003-10-20  Rainer Orth  <<EMAIL>>

	* src/mips/ffi.c: Use _ABIN32, _ABIO32 instead of external
	_MIPS_SIM_NABI32, _MIPS_SIM_ABI32.

2003-10-19  Andreas Tobler  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_args): Declare bytes again.
	Used when FFI_DEBUG = 1.

2003-10-14  Alan Modra  <<EMAIL>>

	* src/types.c (double, longdouble): Default POWERPC64 to 8 byte size
	and align.

2003-10-06  Rainer Orth  <<EMAIL>>

	* include/ffi_mips.h: Define FFI_MIPS_N32 for N32/N64 ABIs,
	FFI_MIPS_O32 for O32 ABI.

2003-10-01  Andreas Tobler  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp: Set LD_LIBRARY_PATH_64 for
	SPARC64. Cleanup whitespaces.

2003-09-19  Andreas Tobler  <<EMAIL>>

	* testsuite/libffi.call/closure_fn0.c: Xfail mips, arm,
	strongarm, xscale. Cleanup whitespaces.
	* testsuite/libffi.call/closure_fn1.c: Likewise.
	* testsuite/libffi.call/closure_fn2.c: Likewise.
	* testsuite/libffi.call/closure_fn3.c: Likewise.
	* testsuite/libffi.call/cls_12byte.c: Likewise.
	* testsuite/libffi.call/cls_16byte.c: Likewise.
	* testsuite/libffi.call/cls_1_1byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte.c: Likewise.
	* testsuite/libffi.call/cls_24byte.c: Likewise.
	* testsuite/libffi.call/cls_2byte.c: Likewise.
	* testsuite/libffi.call/cls_3_1byte.c: Likewise.
	* testsuite/libffi.call/cls_3byte1.c: Likewise.
	* testsuite/libffi.call/cls_3byte2.c: Likewise.
	* testsuite/libffi.call/cls_4_1byte.c: Likewise.
	* testsuite/libffi.call/cls_4byte.c: Likewise.
	* testsuite/libffi.call/cls_5byte.c: Likewise.
	* testsuite/libffi.call/cls_6byte.c: Likewise.
	* testsuite/libffi.call/cls_7byte.c: Likewise.
	* testsuite/libffi.call/cls_8byte.c: Likewise.
	* testsuite/libffi.call/cls_double.c: Likewise.
	* testsuite/libffi.call/cls_float.c: Likewise.
	* testsuite/libffi.call/cls_uchar.c: Likewise.
	* testsuite/libffi.call/cls_uint.c: Likewise.
	* testsuite/libffi.call/cls_ulonglong.c: Likewise.
	* testsuite/libffi.call/cls_ushort.c: Likewise.
	* testsuite/libffi.call/nested_struct.c: Likewise.
	* testsuite/libffi.call/nested_struct1.c: Likewise.
	* testsuite/libffi.call/problem1.c: Likewise.
	* testsuite/libffi.special/unwindtest.cc: Likewise.
	* testsuite/libffi.call/pyobjc-tc.c: Cleanup whitespaces.

2003-09-18  David Edelsohn  <<EMAIL>>

	* src/powerpc/aix.S: Cleanup whitespaces.
	* src/powerpc/aix_closure.S: Likewise.

2003-09-18  Andreas Tobler  <<EMAIL>>

	* src/powerpc/darwin.S: Cleanup whitespaces, comment formatting.
	* src/powerpc/darwin_closure.S: Likewise.
	* src/powerpc/ffi_darwin.c: Likewise.

2003-09-18  Andreas Tobler  <<EMAIL>>
	    David Edelsohn  <<EMAIL>>

	* src/types.c (double): Add AIX and Darwin to the right TYPEDEF.
	* src/powerpc/aix_closure.S: Remove the pointer to the outgoing
	parameter stack.
	* src/powerpc/darwin_closure.S: Likewise.
	* src/powerpc/ffi_darwin.c (ffi_prep_args): Handle structures
	according to the Darwin/AIX ABI.
	(ffi_prep_cif_machdep): Likewise.
	(ffi_closure_helper_DARWIN): Likewise.
	Remove the outgoing parameter stack logic. Simplify the evaluation
	of the different CASE types.
	(ffi_prep_clousure): Avoid the casts on lvalues. Change the branch
	statement in the trampoline code.

2003-09-18  Kaz Kojima  <<EMAIL>>

	* src/sh/ffi.c (ffi_prep_args): Take account into the alignement
	for the register size.
	(ffi_closure_helper_SYSV): Handle the structure return value
	address correctly.
	(ffi_closure_helper_SYSV): Return the appropriate type when
	the registers are used for the structure return value.
	* src/sh/sysv.S (ffi_closure_SYSV): Fix the stack layout for
	the 64-bit return value.  Update copyright years.

2003-09-17  Rainer Orth  <<EMAIL>>

	* testsuite/lib/libffi-dg.exp (libffi_target_compile): Search in
	srcdir for ffi_mips.h.

2003-09-12  Alan Modra  <<EMAIL>>

	* src/prep_cif.c (initialize_aggregate): Include tail padding in
	structure size.
	* src/powerpc/linux64_closure.S (ffi_closure_LINUX64): Correct
	placement of float result.
	* testsuite/libffi.special/unwindtest.cc (closure_test_fn1): Correct
	cast of "resp" for big-endian 64 bit machines.

2003-09-11  Alan Modra  <<EMAIL>>

	* src/types.c (double, longdouble): Merge identical SH and ARM
	typedefs, and add POWERPC64.
	* src/powerpc/ffi.c (ffi_prep_args64): Correct next_arg calc for
	struct split over gpr and rest.
	(ffi_prep_cif_machdep): Correct intarg_count for structures.
	* src/powerpc/linux64.S (ffi_call_LINUX64): Fix gpr offsets.

2003-09-09  Andreas Tobler  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_closure_helper_SYSV) Handle struct
	passing correctly.

2003-09-09  Alan Modra  <<EMAIL>>

	* configure: Regenerate.

2003-09-04  Andreas Tobler  <<EMAIL>>

	* Makefile.am: Remove build rules for ffitest.
	* Makefile.in: Rebuilt.

2003-09-04  Andreas Tobler  <<EMAIL>>

	* src/java_raw_api.c: Include <stdlib.h> to fix compiler warning
	about implicit declaration of abort().

2003-09-04  Andreas Tobler  <<EMAIL>>

	* Makefile.am: Add dejagnu test framework. Fixes PR other/11411.
	* Makefile.in: Rebuilt.
	* configure.in: Add dejagnu test framework.
	* configure: Rebuilt.

	* testsuite/Makefile.am: New file.
	* testsuite/Makefile.in: Built
	* testsuite/lib/libffi-dg.exp: New file.
	* testsuite/config/default.exp: Likewise.
	* testsuite/libffi.call/call.exp: Likewise.
	* testsuite/libffi.call/ffitest.h: Likewise.
	* testsuite/libffi.call/closure_fn0.c: Likewise.
	* testsuite/libffi.call/closure_fn1.c: Likewise.
	* testsuite/libffi.call/closure_fn2.c: Likewise.
	* testsuite/libffi.call/closure_fn3.c: Likewise.
	* testsuite/libffi.call/cls_1_1byte.c: Likewise.
	* testsuite/libffi.call/cls_3_1byte.c: Likewise.
	* testsuite/libffi.call/cls_4_1byte.c: Likewise.
	* testsuite/libffi.call/cls_2byte.c: Likewise.
	* testsuite/libffi.call/cls_3byte1.c: Likewise.
	* testsuite/libffi.call/cls_3byte2.c: Likewise.
	* testsuite/libffi.call/cls_4byte.c: Likewise.
	* testsuite/libffi.call/cls_5byte.c: Likewise.
	* testsuite/libffi.call/cls_6byte.c: Likewise.
	* testsuite/libffi.call/cls_7byte.c: Likewise.
	* testsuite/libffi.call/cls_8byte.c: Likewise.
	* testsuite/libffi.call/cls_12byte.c: Likewise.
	* testsuite/libffi.call/cls_16byte.c: Likewise.
	* testsuite/libffi.call/cls_20byte.c: Likewise.
	* testsuite/libffi.call/cls_24byte.c: Likewise.
	* testsuite/libffi.call/cls_double.c: Likewise.
	* testsuite/libffi.call/cls_float.c: Likewise.
	* testsuite/libffi.call/cls_uchar.c: Likewise.
	* testsuite/libffi.call/cls_uint.c: Likewise.
	* testsuite/libffi.call/cls_ulonglong.c: Likewise.
	* testsuite/libffi.call/cls_ushort.c: Likewise.
	* testsuite/libffi.call/float.c: Likewise.
	* testsuite/libffi.call/float1.c: Likewise.
	* testsuite/libffi.call/float2.c: Likewise.
	* testsuite/libffi.call/many.c: Likewise.
	* testsuite/libffi.call/many_win32.c: Likewise.
	* testsuite/libffi.call/nested_struct.c: Likewise.
	* testsuite/libffi.call/nested_struct1.c: Likewise.
	* testsuite/libffi.call/pyobjc-tc.c: Likewise.
	* testsuite/libffi.call/problem1.c: Likewise.
	* testsuite/libffi.call/promotion.c: Likewise.
	* testsuite/libffi.call/return_ll.c: Likewise.
	* testsuite/libffi.call/return_sc.c: Likewise.
	* testsuite/libffi.call/return_uc.c: Likewise.
	* testsuite/libffi.call/strlen.c: Likewise.
	* testsuite/libffi.call/strlen_win32.c: Likewise.
	* testsuite/libffi.call/struct1.c: Likewise.
	* testsuite/libffi.call/struct2.c: Likewise.
	* testsuite/libffi.call/struct3.c: Likewise.
	* testsuite/libffi.call/struct4.c: Likewise.
	* testsuite/libffi.call/struct5.c: Likewise.
	* testsuite/libffi.call/struct6.c: Likewise.
	* testsuite/libffi.call/struct7.c: Likewise.
	* testsuite/libffi.call/struct8.c: Likewise.
	* testsuite/libffi.call/struct9.c: Likewise.
	* testsuite/libffi.special/special.exp: New file.
	* testsuite/libffi.special/ffitestcxx.h: Likewise.
	* testsuite/libffi.special/unwindtest.cc: Likewise.


2003-08-13  Kaz Kojima  <<EMAIL>>

	* src/sh/ffi.c (OFS_INT16): Set 0 for little endian case.  Update
	copyright years.

2003-08-02  Alan Modra  <<EMAIL>>

	* src/powerpc/ffi.c (ffi_prep_args64): Modify for changed gcc
	structure passing.
	(ffi_closure_helper_LINUX64): Likewise.
	* src/powerpc/linux64.S: Remove code writing to parm save area.
	* src/powerpc/linux64_closure.S (ffi_closure_LINUX64): Use return
	address in lr from ffi_closure_helper_LINUX64 call to calculate
	table address.  Optimize function tail.

2003-07-28  Andreas Tobler  <<EMAIL>>

	* src/sparc/ffi.c: Handle all floating point registers.
	* src/sparc/v9.S: Likewise. Fixes second part of PR target/11410.

2003-07-11  Gerald Pfeifer  <<EMAIL>>

	* README: Note that libffi is not part of GCC.  Update the project
	URL and status.

2003-06-19  Franz Sirl  <<EMAIL>>

	* src/powerpc/ppc_closure.S: Include ffi.h.

2003-06-13  Rainer Orth  <<EMAIL>>

	* src/x86/sysv.S: Avoid gas-only .uleb128/.sleb128 directives.
	Use C style comments.

2003-06-13  Kaz Kojima  <<EMAIL>>

	* Makefile.am: Add SHmedia support.  Fix a typo of SH support.
	* Makefile.in: Regenerate.
	* configure.in (sh64-*-linux*, sh5*-*-linux*): Add target.
	* configure: Regenerate.
	* include/ffi.h.in: Add SHmedia support.
	* src/sh64/ffi.c: New file.
	* src/sh64/sysv.S: New file.

2003-05-16  Jakub Jelinek  <<EMAIL>>

	* configure.in (HAVE_RO_EH_FRAME): Check whether .eh_frame section
	should be read-only.
	* configure: Rebuilt.
	* fficonfig.h.in: Rebuilt.
	* include/ffi.h.in (EH_FRAME_FLAGS): Define.
	* src/alpha/osf.S: Use EH_FRAME_FLAGS.
	* src/powerpc/linux64.S: Likewise.
	* src/powerpc/linux64_closure.S: Likewise.  Include ffi.h.
	* src/powerpc/sysv.S: Use EH_FRAME_FLAGS.  Use pcrel encoding
	if -fpic/-fPIC/-mrelocatable.
	* src/powerpc/powerpc_closure.S: Likewise.
	* src/sparc/v8.S: If HAVE_RO_EH_FRAME is defined, don't include
	#write in .eh_frame flags.
	* src/sparc/v9.S: Likewise.
	* src/x86/unix64.S: Use EH_FRAME_FLAGS.
	* src/x86/sysv.S: Likewise.  Use pcrel encoding if -fpic/-fPIC.
	* src/s390/sysv.S: Use EH_FRAME_FLAGS.  Include ffi.h.

2003-05-07  Jeff Sturm  <<EMAIL>>

	Fixes PR bootstrap/10656
	* configure.in (HAVE_AS_REGISTER_PSEUDO_OP): Test assembler
	support for .register pseudo-op.
	* src/sparc/v8.S: Use it.
	* fficonfig.h.in: Rebuilt.
	* configure: Rebuilt.

2003-04-18  Jakub Jelinek  <<EMAIL>>

	* include/ffi.h.in (POWERPC64): Define if 64-bit.
	(enum ffi_abi): Add FFI_LINUX64 on POWERPC.
	Make it the default on POWERPC64.
	(FFI_TRAMPOLINE_SIZE): Define to 24 on POWERPC64.
	* configure.in: Change powerpc-*-linux* into powerpc*-*-linux*.
	* configure: Rebuilt.
	* src/powerpc/ffi.c (hidden): Define.
	(ffi_prep_args_SYSV): Renamed from
	ffi_prep_args.  Cast pointers to unsigned long to shut up warnings.
	(NUM_GPR_ARG_REGISTERS64, NUM_FPR_ARG_REGISTERS64,
	ASM_NEEDS_REGISTERS64): New.
	(ffi_prep_args64): New function.
	(ffi_prep_cif_machdep): Handle FFI_LINUX64 ABI.
	(ffi_call): Likewise.
	(ffi_prep_closure): Likewise.
	(flush_icache): Surround by #ifndef POWERPC64.
	(ffi_dblfl): New union type.
	(ffi_closure_helper_SYSV): Use it to avoid aliasing problems.
	(ffi_closure_helper_LINUX64): New function.
	* src/powerpc/ppc_closure.S: Surround whole file by #ifndef
	__powerpc64__.
	* src/powerpc/sysv.S: Likewise.
	(ffi_call_SYSV): Rename ffi_prep_args to ffi_prep_args_SYSV.
	* src/powerpc/linux64.S: New file.
	* src/powerpc/linux64_closure.S: New file.
	* Makefile.am (EXTRA_DIST): Add src/powerpc/linux64.S and
	src/powerpc/linux64_closure.S.
	(TARGET_SRC_POWERPC): Likewise.

	* src/ffitest.c (closure_test_fn, closure_test_fn1, closure_test_fn2,
	closure_test_fn3): Fix result printing on big-endian 64-bit
	machines.
	(main): Print tst2_arg instead of uninitialized tst2_result.

	* src/ffitest.c (main): Hide what closure pointer really points to
	from the compiler.

2003-04-16  Richard Earnshaw  <<EMAIL>>

	* configure.in (arm-*-netbsdelf*): Add configuration.
	(configure): Regenerated.

2003-04-04  Loren J. Rittle  <<EMAIL>>

	* include/Makefile.in: Regenerate.

2003-03-21  Zdenek Dvorak  <<EMAIL>>

	* libffi/include/ffi.h.in: Define X86 instead of X86_64 in 32
	bit mode.
	* libffi/src/x86/ffi.c (ffi_closure_SYSV, ffi_closure_raw_SYSV):
	Receive closure pointer through parameter, read args using
	__builtin_dwarf_cfa.
	(FFI_INIT_TRAMPOLINE): Send closure reference through eax.

2003-03-12  Andreas Schwab  <<EMAIL>>

	* configure.in: Avoid trailing /. in toolexeclibdir.
	* configure: Rebuilt.

2003-03-03  Andreas Tobler <<EMAIL>>

	* src/powerpc/darwin_closure.S: Recode to fit dynamic libraries.

2003-02-06  Andreas Tobler <<EMAIL>>

	* libffi/src/powerpc/darwin_closure.S:
	Fix alignement bug, allocate 8 bytes for the result.
	* libffi/src/powerpc/aix_closure.S:
	Likewise.
	* libffi/src/powerpc/ffi_darwin.c:
	Update stackframe description for aix/darwin_closure.S.

2003-02-06  Jakub Jelinek  <<EMAIL>>

	* src/s390/ffi.c (ffi_closure_helper_SYSV): Add hidden visibility
	attribute.

2003-01-31  Christian Cornelssen  <<EMAIL>>,
	    Andreas Schwab  <<EMAIL>>

	* configure.in: Adjust command to source config-ml.in to account
	for changes to the libffi_basedir definition.
	(libffi_basedir): Remove ${srcdir} from value and include trailing
	slash if nonempty.

	* configure: Regenerate.

2003-01-29  Franz Sirl  <<EMAIL>>

	* src/powerpc/ppc_closure.S: Recode to fit shared libs.

2003-01-28  Andrew Haley  <<EMAIL>>

	* include/ffi.h.in: Enable FFI_CLOSURES for x86_64.
	* src/x86/ffi64.c (ffi_prep_closure): New.
	(ffi_closure_UNIX64_inner): New.
	* src/x86/unix64.S (ffi_closure_UNIX64): New.

2003-01-27  Alexandre Oliva  <<EMAIL>>

	* configure.in (toolexecdir, toolexeclibdir): Set and AC_SUBST.
	Remove USE_LIBDIR conditional.
	* Makefile.am (toolexecdir, toolexeclibdir): Don't override.
	* Makefile.in, configure: Rebuilt.

2003-01027  David Edelsohn  <<EMAIL>>

	* Makefile.am (TARGET_SRC_POWERPC_AIX): Fix typo.
	* Makefile.in: Regenerate.

2003-01-22  Andrew Haley  <<EMAIL>>

	* src/powerpc/darwin.S (_ffi_call_AIX): Add Augmentation size to
	unwind info.

2003-01-21  Andreas Tobler  <<EMAIL>>

	* src/powerpc/darwin.S: Add unwind info.
	* src/powerpc/darwin_closure.S: Likewise.

2003-01-14  Andrew Haley  <<EMAIL>>

	* src/x86/ffi64.c (ffi_prep_args): Check for void retval.
	(ffi_prep_cif_machdep): Likewise.
	* src/x86/unix64.S: Add unwind info.

2003-01-14  Andreas Jaeger  <<EMAIL>>

	* src/ffitest.c (main): Only use ffi_closures if those are
	supported.

2003-01-13 Andreas Tobler <<EMAIL>>

	* libffi/src/ffitest.c
	 add closure testcases

2003-01-13 Kevin B. Hendricks <<EMAIL>>

	* libffi/src/powerpc/ffi.c
	 fix alignment bug for float (4 byte aligned iso 8 byte)

2003-01-09  Geoffrey Keating  <<EMAIL>>

	* src/powerpc/ffi_darwin.c: Remove RCS version string.
	* src/powerpc/darwin.S: Remove RCS version string.

2003-01-03  Jeff Sturm  <<EMAIL>>

	* include/ffi.h.in: Add closure defines for SPARC, SPARC64.
	* src/ffitest.c (main): Use static storage for closure.
	* src/sparc/ffi.c (ffi_prep_closure, ffi_closure_sparc_inner): New.
	* src/sparc/v8.S (ffi_closure_v8): New.
	* src/sparc/v9.S (ffi_closure_v9): New.

2002-11-10  Ranjit Mathew <<EMAIL>>

	* include/ffi.h.in: Added FFI_STDCALL ffi_type
	  enumeration for X86_WIN32.
	* src/x86/win32.S: Added ffi_call_STDCALL function
	  definition.
	* src/x86/ffi.c (ffi_call/ffi_raw_call): Added
	  switch cases for recognising FFI_STDCALL and
	  calling ffi_call_STDCALL if target is X86_WIN32.
	* src/ffitest.c (my_stdcall_strlen/stdcall_many):
	  stdcall versions of the "my_strlen" and "many"
	  test functions (for X86_WIN32).
	  Added test cases to test stdcall invocation using
	  these functions.

2002-12-02  Kaz Kojima  <<EMAIL>>

	* src/sh/sysv.S: Add DWARF2 unwind info.

2002-11-27  Ulrich Weigand  <<EMAIL>>

	* src/s390/sysv.S (.eh_frame section): Make section read-only.

2002-11-26  Jim Wilson  <<EMAIL>>

	* src/types.c (FFI_TYPE_POINTER): Has size 8 on IA64.

2002-11-23  H.J. Lu <<EMAIL>>

	* acinclude.m4: Add dummy AM_PROG_LIBTOOL.
	Include ../config/accross.m4.
	* aclocal.m4; Rebuild.
	* configure: Likewise.

2002-11-15  Ulrich Weigand  <<EMAIL>>

	* src/s390/sysv.S (.eh_frame section): Adapt to pcrel FDE encoding.

2002-11-11  DJ Delorie  <<EMAIL>>

	* configure.in: Look for common files in the right place.

2002-10-08  Ulrich Weigand  <<EMAIL>>

	* src/java_raw_api.c (ffi_java_raw_to_ptrarray): Interpret
	raw data as _Jv_word values, not ffi_raw.
	(ffi_java_ptrarray_to_raw): Likewise.
	(ffi_java_rvalue_to_raw): New function.
	(ffi_java_raw_call): Call it.
	(ffi_java_raw_to_rvalue): New function.
	(ffi_java_translate_args): Call it.
	* src/ffitest.c (closure_test_fn): Interpret return value
	as ffi_arg, not int.
	* src/s390/ffi.c (ffi_prep_cif_machdep): Add missing
	FFI_TYPE_POINTER case.
	(ffi_closure_helper_SYSV): Likewise.  Also, assume return
	values extended to word size.

2002-10-02  Andreas Jaeger  <<EMAIL>>

	* src/x86/ffi64.c (ffi_prep_cif_machdep): Remove debug output.

2002-10-01  Bo Thorsen  <<EMAIL>>

	* include/ffi.h.in: Fix i386 win32 compilation.

2002-09-30  Ulrich Weigand  <<EMAIL>>

	* configure.in: Add s390x-*-linux-* target.
	* configure: Regenerate.
	* include/ffi.h.in: Define S390X for s390x targets.
	(FFI_CLOSURES): Define for s390/s390x.
	(FFI_TRAMPOLINE_SIZE): Likewise.
	(FFI_NATIVE_RAW_API): Likewise.
	* src/prep_cif.c (ffi_prep_cif): Do not compute stack space for s390.
	* src/types.c (FFI_TYPE_POINTER): Use 8-byte pointers on s390x.
	* src/s390/ffi.c: Major rework of existing code.  Add support for
	s390x targets.  Add closure support.
	* src/s390/sysv.S: Likewise.

2002-09-29  Richard Earnshaw  <<EMAIL>>

	* src/arm/sysv.S: Fix typo.

2002-09-28  Richard Earnshaw  <<EMAIL>>

	* src/arm/sysv.S: If we don't have machine/asm.h and the pre-processor
	has defined __USER_LABEL_PREFIX__, then use it in CNAME.
	(ffi_call_SYSV): Handle soft-float.

2002-09-27  Bo Thorsen  <<EMAIL>>

	* include/ffi.h.in: Fix multilib x86-64 support.

2002-09-22  Kaveh R. Ghazi  <<EMAIL>>

	* Makefile.am (all-multi): Fix multilib parallel build.

2002-07-19  Kaz Kojima  <<EMAIL>>

	* configure.in (sh[34]*-*-linux*): Add brackets.
	* configure: Regenerate.

2002-07-18  Kaz Kojima  <<EMAIL>>

	* Makefile.am: Add SH support.
	* Makefile.in: Regenerate.
	* configure.in (sh-*-linux*, sh[34]*-*-linux*): Add target.
	* configure: Regenerate.
	* include/ffi.h.in: Add SH support.
	* src/sh/ffi.c: New file.
	* src/sh/sysv.S: New file.
	* src/types.c: Add SH support.

2002-07-16  Bo Thorsen  <<EMAIL>>

	* src/x86/ffi64.c: New file that adds x86-64 support.
	* src/x86/unix64.S: New file that handles argument setup for
	x86-64.
	* src/x86/sysv.S: Don't use this on x86-64.
	* src/x86/ffi.c: Don't use this on x86-64.
	Remove unused vars.
	* src/prep_cif.c (ffi_prep_cif): Don't do stack size calculation
	for x86-64.
	* src/ffitest.c (struct6): New test that tests a special case in
	the x86-64 ABI.
	(struct7): Likewise.
	(struct8): Likewise.
	(struct9): Likewise.
	(closure_test_fn): Silence warning about this when it's not used.
	(main): Add the new tests.
	(main): Fix a couple of wrong casts and silence some compiler warnings.
	* include/ffi.h.in: Add x86-64 ABI definition.
	* fficonfig.h.in: Regenerate.
	* Makefile.am: Add x86-64 support.
	* configure.in: Likewise.
	* Makefile.in: Regenerate.
	* configure: Likewise.

2002-06-24  Bo Thorsen  <<EMAIL>>

	* src/types.c: Merge settings for similar architectures.
	Add x86-64 sizes and alignments.

2002-06-23  Bo Thorsen  <<EMAIL>>

	* src/arm/ffi.c (ffi_prep_args): Remove unused vars.
	* src/sparc/ffi.c (ffi_prep_args_v8): Likewise.
	* src/mips/ffi.c (ffi_prep_args): Likewise.
	* src/m68k/ffi.c (ffi_prep_args): Likewise.

2002-07-18  H.J. Lu  (<EMAIL>)

	* Makefile.am (TARGET_SRC_MIPS_LINUX): New.
	(libffi_la_SOURCES): Support MIPS_LINUX.
	(libffi_convenience_la_SOURCES): Likewise.
	* Makefile.in: Regenerated.

	* configure.in (mips64*-*): Skip.
	(mips*-*-linux*): New.
	* configure: Regenerated.

	* src/mips/ffi.c: Include <sgidefs.h>.

2002-06-06  Ulrich Weigand  <<EMAIL>>

	* src/s390/sysv.S: Save/restore %r6.  Add DWARF-2 unwind info.

2002-05-27  Roger Sayle  <<EMAIL>>

	* src/x86/ffi.c (ffi_prep_args): Remove reference to avn.

2002-05-27  Bo Thorsen  <<EMAIL>>

	* src/x86/ffi.c (ffi_prep_args): Remove unused variable and
	fix formatting.

2002-05-13  Andreas Tobler  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_closure): Declare fd at
	beginning of function (for older apple cc).

2002-05-08  Alexandre Oliva  <<EMAIL>>

	* configure.in (ORIGINAL_LD_FOR_MULTILIBS): Preserve LD at
	script entry, and set LD to it when configuring multilibs.
	* configure: Rebuilt.

2002-05-05  Jason Thorpe  <<EMAIL>>

	* configure.in (sparc64-*-netbsd*): Add target.
	(sparc-*-netbsdelf*): Likewise.
	* configure: Regenerate.

2002-04-28  David S. Miller  <<EMAIL>>

	* configure.in, configure: Fix SPARC test in previous change.

2002-04-29  Gerhard Tonn  <<EMAIL>>

	* Makefile.am: Add Linux for S/390 support.
	* Makefile.in: Regenerate.
	* configure.in: Add Linux for S/390 support.
	* configure: Regenerate.
	* include/ffi.h.in: Add Linux for S/390 support.
	* src/s390/ffi.c: New file from libffi CVS tree.
	* src/s390/sysv.S: New file from libffi CVS tree.

2002-04-28  Jakub Jelinek  <<EMAIL>>

	* configure.in (HAVE_AS_SPARC_UA_PCREL): Check for working
	%r_disp32().
	* src/sparc/v8.S: Use it.
	* src/sparc/v9.S: Likewise.
	* fficonfig.h.in: Rebuilt.
	* configure: Rebuilt.

2002-04-08  Hans Boehm  <<EMAIL>>

	* src/java_raw_api.c (ffi_java_raw_size): Handle FFI_TYPE_DOUBLE
	correctly.
	* src/ia64/unix.S: Add unwind information. Fix comments.
	Save sp in a way that's compatible with unwind info.
	(ffi_call_unix): Correctly restore sp in all cases.
	* src/ia64/ffi.c: Add, fix comments.

2002-04-08  Jakub Jelinek  <<EMAIL>>

	* src/sparc/v8.S: Make .eh_frame dependent on target word size.

2002-04-06  Jason Thorpe  <<EMAIL>>

	* configure.in (alpha*-*-netbsd*): Add target.
	* configure: Regenerate.

2002-04-04  Jeff Sturm  <<EMAIL>>

	* src/sparc/v8.S: Add unwind info.
	* src/sparc/v9.S: Likewise.

2002-03-30  Krister Walfridsson  <<EMAIL>>

	* configure.in: Enable i*86-*-netbsdelf*.
	* configure: Rebuilt.

2002-03-29  David Billinghurst <<EMAIL>>

	PR other/2620
	* src/mips/n32.s: Delete
	* src/mips/o32.s: Delete

2002-03-21  Loren J. Rittle  <<EMAIL>>

	* configure.in: Enable alpha*-*-freebsd*.
	* configure: Rebuilt.

2002-03-17  Bryce McKinlay  <<EMAIL>>

	* Makefile.am: libfficonvenience -> libffi_convenience.
	* Makefile.in: Rebuilt.

	* Makefile.am: Define ffitest_OBJECTS.
	* Makefile.in: Rebuilt.

2002-03-07  Andreas Tobler  <<EMAIL>>
	    David Edelsohn  <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Add Darwin and AIX closure files.
	(TARGET_SRC_POWERPC_AIX): Add aix_closure.S.
	(TARGET_SRC_POWERPC_DARWIN): Add darwin_closure.S.
	* Makefile.in: Regenerate.
	* include/ffi.h.in: Add AIX and Darwin closure definitions.
	* src/powerpc/ffi_darwin.c (ffi_prep_closure): New function.
	(flush_icache, flush_range): New functions.
	(ffi_closure_helper_DARWIN): New function.
	* src/powerpc/aix_closure.S: New file.
	* src/powerpc/darwin_closure.S: New file.

2002-02-24  Jeff Sturm  <<EMAIL>>

	* include/ffi.h.in: Add typedef for ffi_arg.
	* src/ffitest.c (main): Declare rint with ffi_arg.

2002-02-21  Andreas Tobler  <<EMAIL>>

	* src/powerpc/ffi_darwin.c (ffi_prep_args): Skip appropriate
	number of GPRs for floating-point arguments.

2002-01-31  Anthony Green  <<EMAIL>>

	* configure: Rebuilt.
	* configure.in: Replace CHECK_SIZEOF and endian tests with
	cross-compiler friendly macros.
	* aclocal.m4 (AC_COMPILE_CHECK_SIZEOF, AC_C_BIGENDIAN_CROSS): New
	macros.

2002-01-18  David Edelsohn  <<EMAIL>>

	* src/powerpc/darwin.S (_ffi_call_AIX): New.
	* src/powerpc/aix.S (ffi_call_DARWIN): New.

2002-01-17  David Edelsohn  <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Add Darwin and AIX files.
	(TARGET_SRC_POWERPC_AIX): New.
	(POWERPC_AIX): New stanza.
	* Makefile.in: Regenerate.
	* configure.in: Add AIX case.
	* configure: Regenerate.
	* include/ffi.h.in (ffi_abi): Add FFI_AIX.
	* src/powerpc/ffi_darwin.c (ffi_status): Use "long" to scale frame
	size.  Fix "long double" support.
	(ffi_call): Add FFI_AIX case.
	* src/powerpc/aix.S: New.

2001-10-09  John Hornkvist  <<EMAIL>>

	Implement Darwin PowerPC ABI.
	* configure.in: Handle powerpc-*-darwin*.
	* Makefile.am: Set source files for POWERPC_DARWIN.
	* configure: Rebuilt.
	* Makefile.in: Rebuilt.
	* include/ffi.h.in: Define FFI_DARWIN and FFI_DEFAULT_ABI for
	POWERPC_DARWIN.
	* src/powerpc/darwin.S: New file.
	* src/powerpc/ffi_darwin.c: New file.

2001-10-07  Joseph S. Myers  <<EMAIL>>

	* src/x86/ffi.c: Fix spelling error of "separate" as "seperate".

2001-07-16  Rainer Orth  <<EMAIL>>

	* src/x86/sysv.S: Avoid gas-only .balign directive.
	Use C style comments.

2001-07-16  Rainer Orth  <<EMAIL>>

	* src/alpha/ffi.c (ffi_prep_closure): Avoid gas-only mnemonic.
	Fixes PR bootstrap/3563.

2001-06-26  Rainer Orth  <<EMAIL>>

	* src/alpha/osf.S (ffi_closure_osf): Use .rdata for ECOFF.

2001-06-25  Rainer Orth  <<EMAIL>>

	* configure.in: Recognize sparc*-sun-* host.
	* configure: Regenerate.

2001-06-06  Andrew Haley  <<EMAIL>>

	* src/alpha/osf.S (__FRAME_BEGIN__): Conditionalize for ELF.

2001-06-03  Andrew Haley  <<EMAIL>>

	* src/alpha/osf.S: Add unwind info.
	* src/powerpc/sysv.S: Add unwind info.
	* src/powerpc/ppc_closure.S: Likewise.

2000-05-31  Jeff Sturm  <<EMAIL>>

	* configure.in: Fix AC_ARG_ENABLE usage.
	* configure: Rebuilt.

2001-05-06  Bryce McKinlay  <<EMAIL>>

	* configure.in: Remove warning about beta code.
	* configure: Rebuilt.

2001-04-25  Hans Boehm <<EMAIL>>

	* src/ia64/unix.S: Restore stack pointer when returning from
	ffi_closure_UNIX.
	* src/ia64/ffi.c: Fix typo in comment.

2001-04-18  Jim Wilson  <<EMAIL>>

	* src/ia64/unix.S: Delete unnecessary increment and decrement of loc2
	to eliminate RAW DV.

2001-04-12  Bryce McKinlay  <<EMAIL>>

	* Makefile.am: Make a libtool convenience library.
	* Makefile.in: Rebuilt.

2001-03-29  Bryce McKinlay  <<EMAIL>>

	* configure.in: Use different syntax for subdirectory creation.
	* configure: Rebuilt.

2001-03-27  Jon Beniston  <<EMAIL>>

	* configure.in: Added X86_WIN32 target (Win32, CygWin, MingW).
	* configure: Rebuilt.
	* Makefile.am: Added X86_WIN32 target support.
	* Makefile.in: Rebuilt.

	* include/ffi.h.in: Added X86_WIN32 target support.

	* src/ffitest.c: Doesn't run structure tests for X86_WIN32 targets.
	* src/types.c: Added X86_WIN32 target support.

	* src/x86/win32.S: New file. Based on sysv.S, but with EH
	stuff removed and made to work with CygWin's gas.

2001-03-26  Bryce McKinlay  <<EMAIL>>

	* configure.in: Make target subdirectory in build dir.
	* Makefile.am: Override suffix based rules to specify correct output
	subdirectory.
	* Makefile.in: Rebuilt.
	* configure: Rebuilt.

2001-03-23  Kevin B Hendricks  <<EMAIL>>

	* src/powerpc/ppc_closure.S: New file.
	* src/powerpc/ffi.c (ffi_prep_args): Fixed ABI compatibility bug
	involving long long and register pairs.
	(ffi_prep_closure): New function.
	(flush_icache): Likewise.
	(ffi_closure_helper_SYSV): Likewise.
	* include/ffi.h.in (FFI_CLOSURES): Define on PPC.
	(FFI_TRAMPOLINE_SIZE): Likewise.
	(FFI_NATIVE_RAW_API): Likewise.
	* Makefile.in: Rebuilt.
	* Makefile.am (EXTRA_DIST): Added src/powerpc/ppc_closure.S.
	(TARGET_SRC_POWERPC): Likewise.

2001-03-19  Tom Tromey  <<EMAIL>>

	* Makefile.in: Rebuilt.
	* Makefile.am (ffitest_LDFLAGS): New macro.

2001-03-02  Nick Clifton  <<EMAIL>>

	* include/ffi.h.in: Remove RCS ident string.
	* include/ffi_mips.h: Remove RCS ident string.
	* src/debug.c: Remove RCS ident string.
	* src/ffitest.c: Remove RCS ident string.
	* src/prep_cif.c: Remove RCS ident string.
	* src/types.c: Remove RCS ident string.
	* src/alpha/ffi.c: Remove RCS ident string.
	* src/alpha/osf.S: Remove RCS ident string.
	* src/arm/ffi.c: Remove RCS ident string.
	* src/arm/sysv.S: Remove RCS ident string.
	* src/mips/ffi.c: Remove RCS ident string.
	* src/mips/n32.S: Remove RCS ident string.
	* src/mips/o32.S: Remove RCS ident string.
	* src/sparc/ffi.c: Remove RCS ident string.
	* src/sparc/v8.S: Remove RCS ident string.
	* src/sparc/v9.S: Remove RCS ident string.
	* src/x86/ffi.c: Remove RCS ident string.
	* src/x86/sysv.S: Remove RCS ident string.

2001-02-08  Joseph S. Myers  <<EMAIL>>

	* include/ffi.h.in: Change sourceware.cygnus.com references to
	gcc.gnu.org.

2000-12-09  Richard Henderson  <<EMAIL>>

	* src/alpha/ffi.c (ffi_call): Simplify struct return test.
	(ffi_closure_osf_inner): Index rather than increment avalue
	and arg_types.  Give ffi_closure_osf the raw return value type.
	* src/alpha/osf.S (ffi_closure_osf): Handle return value type
	promotion.

2000-12-07  Richard Henderson  <<EMAIL>>

	* src/raw_api.c (ffi_translate_args): Fix typo.
	(ffi_prep_closure): Likewise.

	* include/ffi.h.in [ALPHA]: Define FFI_CLOSURES and
	FFI_TRAMPOLINE_SIZE.
	* src/alpha/ffi.c (ffi_prep_cif_machdep): Adjust minimal
	cif->bytes for new ffi_call_osf implementation.
	(ffi_prep_args): Absorb into ...
	(ffi_call): ... here.  Do all stack allocation here and
	avoid a callback function.
	(ffi_prep_closure, ffi_closure_osf_inner): New.
	* src/alpha/osf.S (ffi_call_osf): Reimplement with no callback.
	(ffi_closure_osf): New.

2000-09-10  Alexandre Oliva  <<EMAIL>>

	* config.guess, config.sub, install-sh: Removed.
	* ltconfig, ltmain.sh, missing, mkinstalldirs: Likewise.
	* Makefile.in: Rebuilt.

	* acinclude.m4: Include libtool macros from the top level.
	* aclocal.m4, configure: Rebuilt.

2000-08-22  Alexandre Oliva  <<EMAIL>>

	* configure.in [i*86-*-freebsd*] (TARGET, TARGETDIR): Set.
	* configure: Rebuilt.

2000-05-11  Scott Bambrough  <<EMAIL>>

	* libffi/src/arm/sysv.S (ffi_call_SYSV): Doubles are not saved to
	memory correctly.  Use conditional instructions, not branches where
	possible.

2000-05-04  Tom Tromey  <<EMAIL>>

	* configure: Rebuilt.
	* configure.in: Match `arm*-*-linux-*'.
	From Chris Dornan <<EMAIL>>.

2000-04-28  Jakub Jelinek  <<EMAIL>>

	* Makefile.am (SUBDIRS): Define.
	(AM_MAKEFLAGS): Likewise.
	(Multilib support.): Add section.
	* Makefile.in: Rebuilt.
	* ltconfig (extra_compiler_flags, extra_compiler_flags_value):
	New variables. Set for gcc using -print-multi-lib. Export them
	to libtool.
	(sparc64-*-linux-gnu*): Use libsuff 64 for search paths.
	* ltmain.sh (B|b|V): Don't throw away gcc's -B, -b and -V options
	for -shared links.
	(extra_compiler_flags_value, extra_compiler_flags): Check these
	for extra compiler options which need to be passed down in
	compiler_flags.

2000-04-16  Anthony Green  <<EMAIL>>

	* configure: Rebuilt.
	* configure.in: Change i*86-pc-linux* to i*86-*-linux*.

2000-04-14  Jakub Jelinek  <<EMAIL>>

	* include/ffi.h.in (SPARC64): Define for 64bit SPARC builds.
	Set SPARC FFI_DEFAULT_ABI based on SPARC64 define.
	* src/sparc/ffi.c (ffi_prep_args_v8): Renamed from ffi_prep_args.
	Replace all void * sizeofs with sizeof(int).
	Only compare type with FFI_TYPE_LONGDOUBLE if LONGDOUBLE is
	different than DOUBLE.
	Remove FFI_TYPE_SINT32 and FFI_TYPE_UINT32 cases (handled elsewhere).
	(ffi_prep_args_v9): New function.
	(ffi_prep_cif_machdep): Handle V9 ABI and long long on V8.
	(ffi_V9_return_struct): New function.
	(ffi_call): Handle FFI_V9 ABI from 64bit code and FFI_V8 ABI from
	32bit code (not yet cross-arch calls).
	* src/sparc/v8.S: Add struct return delay nop.
	Handle long long.
	* src/sparc/v9.S: New file.
	* src/prep_cif.c (ffi_prep_cif): Return structure pointer
	is used on sparc64 only for structures larger than 32 bytes.
	Pass by reference for structures is done for structure arguments
	larger than 16 bytes.
	* src/ffitest.c (main): Use 64bit rint on sparc64.
	Run long long tests on sparc.
	* src/types.c (FFI_TYPE_POINTER): Pointer is 64bit on alpha and
	sparc64.
	(FFI_TYPE_LONGDOUBLE): long double is 128 bit aligned to 128 bits
	on sparc64.
	* configure.in (sparc-*-linux*): New supported target.
	(sparc64-*-linux*): Likewise.
	* configure: Rebuilt.
	* Makefile.am: Add v9.S to SPARC files.
	* Makefile.in: Likewise.
	(LINK): Surround $(CCLD) into double quotes, so that multilib
	compiles work correctly.

2000-04-04  Alexandre Petit-Bianco  <<EMAIL>>

	* configure: Rebuilt.
	* configure.in: (i*86-*-solaris*): New libffi target. Patch
	proposed by Bryce McKinlay.

2000-03-20  Tom Tromey  <<EMAIL>>

	* Makefile.in: Hand edit for java_raw_api.lo.

2000-03-08  Bryce McKinlay  <<EMAIL>>

	* config.guess, config.sub: Update from the gcc tree.
	Fix for PR libgcj/168.

2000-03-03  Tom Tromey  <<EMAIL>>

	* Makefile.in: Fixed ia64 by hand.

	* configure: Rebuilt.
	* configure.in (--enable-multilib): New option.
	(libffi_basedir): New subst.
	(AC_OUTPUT): Added multilib code.

2000-03-02  Tom Tromey  <<EMAIL>>

	* Makefile.in: Rebuilt.
	* Makefile.am (TARGET_SRC_IA64): Use `ia64', not `alpha', as
	directory name.

2000-02-25  Hans Boehm <<EMAIL>>

	* src/ia64/ffi.c, src/ia64/ia64_flags.h, src/ia64/unix.S: New
	files.
	* src/raw_api.c (ffi_translate_args): Fixed typo in argument
	list.
	(ffi_prep_raw_closure): Use ffi_translate_args, not
	ffi_closure_translate.
	* src/java_raw_api.c: New file.
	* src/ffitest.c (closure_test_fn): New function.
	(main): Define `rint' as long long on IA64.  Added new test when
	FFI_CLOSURES is defined.
	* include/ffi.h.in (ALIGN): Use size_t, not unsigned.
	(ffi_abi): Recognize IA64.
	(ffi_raw): Added `flt' field.
	Added "Java raw API" code.
	* configure.in: Recognize ia64.
	* Makefile.am (TARGET_SRC_IA64): New macro.
	(libffi_la_common_SOURCES): Added java_raw_api.c.
	(libffi_la_SOURCES): Define in IA64 case.

2000-01-04  Tom Tromey  <<EMAIL>>

	* Makefile.in: Rebuilt with newer automake.

1999-12-31  Tom Tromey  <<EMAIL>>

	* Makefile.am (INCLUDES): Added -I$(top_srcdir)/src.

1999-09-01  Tom Tromey  <<EMAIL>>

	* include/ffi.h.in: Removed PACKAGE and VERSION defines and
	undefs.
	* fficonfig.h.in: Rebuilt.
	* configure: Rebuilt.
	* configure.in: Pass 3rd argument to AM_INIT_AUTOMAKE.
	Use AM_PROG_LIBTOOL (automake 1.4 compatibility).
	* acconfig.h: Don't #undef PACKAGE or VERSION.

1999-08-09  Anthony Green  <<EMAIL>>

	* include/ffi.h.in: Try to work around messy header problem
	with PACKAGE and VERSION.

	* configure: Rebuilt.
	* configure.in: Change version to 2.00-beta.

	* fficonfig.h.in: Rebuilt.
	* acconfig.h (FFI_NO_STRUCTS, FFI_NO_RAW_API): Define.

	* src/x86/ffi.c (ffi_raw_call): Rename.

1999-08-02  Kresten Krab Thorup  <<EMAIL>>

	* src/x86/ffi.c (ffi_closure_SYSV): New function.
	(ffi_prep_incoming_args_SYSV): Ditto.
	(ffi_prep_closure): Ditto.
	(ffi_closure_raw_SYSV): Ditto.
	(ffi_prep_raw_closure): More ditto.
	(ffi_call_raw): Final ditto.

	* include/ffi.h.in: Add definitions for closure and raw API.

	* src/x86/ffi.c (ffi_prep_cif_machdep): Added case for
	FFI_TYPE_UINT64.

	* Makefile.am (libffi_la_common_SOURCES): Added raw_api.c

	* src/raw_api.c: New file.

	* include/ffi.h.in (ffi_raw): New type.
	(UINT_ARG, SINT_ARG): New defines.
	(ffi_closure, ffi_raw_closure): New types.
	(ffi_prep_closure, ffi_prep_raw_closure): New declarations.

	* configure.in: Add check for endianness and sizeof void*.

	* src/x86/sysv.S (ffi_call_SYSV): Call fixup routine via argument,
	instead of directly.

	* configure: Rebuilt.

Thu Jul  8 14:28:42 1999  Anthony Green  <<EMAIL>>

	* configure.in: Add x86 and powerpc BeOS configurations.
	From Makoto Kato <<EMAIL>>.

1999-05-09  Anthony Green  <<EMAIL>>

	* configure.in: Add warning about this being beta code.
	Remove src/Makefile.am from the picture.
	* configure: Rebuilt.

	* Makefile.am: Move logic from src/Makefile.am.  Add changes
	to support libffi as a target library.
	* Makefile.in: Rebuilt.

	* aclocal.m4, config.guess, config.sub, ltconfig, ltmain.sh:
	Upgraded to new autoconf, automake, libtool.

	* README: Tweaks.

	* LICENSE: Update copyright date.

	* src/Makefile.am, src/Makefile.in: Removed.

1998-11-29  Anthony Green  <<EMAIL>>

	* include/ChangeLog: Removed.
	* src/ChangeLog: Removed.
	* src/mips/ChangeLog: Removed.
	* src/sparc/ChangeLog: Remboved.
	* src/x86/ChangeLog: Removed.

	* ChangeLog.v1: Created.

=============================================================================
From the old ChangeLog.libffi file....

2011-02-08  Andreas Tobler  <<EMAIL>>

	* testsuite/lib/libffi.exp: Tweak for stand-alone mode.

2009-12-25  Samuli Suominen  <<EMAIL>>

	* configure.ac: Undefine _AC_ARG_VAR_PRECIOUS for autoconf 2.64.
	* configure: Rebuilt.
	* fficonfig.h.in: Rebuilt.

2009-06-16  Andrew Haley  <<EMAIL>>

	* testsuite/libffi.call/cls_align_sint64.c,
	testsuite/libffi.call/cls_align_uint64.c,
	testsuite/libffi.call/cls_longdouble_va.c,
	testsuite/libffi.call/cls_ulonglong.c,
	testsuite/libffi.call/return_ll1.c,
	testsuite/libffi.call/stret_medium2.c: Fix printf format
	specifiers.
	* testsuite/libffi.call/huge_struct.c: Ad x86 XFAILs.
	* testsuite/libffi.call/float2.c: Fix dg-excess-errors.
	* testsuite/libffi.call/ffitest.h,
	testsuite/libffi.special/ffitestcxx.h (PRIdLL, PRIuLL): Define.

2009-06-12  Andrew Haley  <<EMAIL>>

	* testsuite/libffi.call/cls_align_sint64.c,
	testsuite/libffi.call/cls_align_uint64.c,
	testsuite/libffi.call/cls_ulonglong.c,
	testsuite/libffi.call/return_ll1.c,
	testsuite/libffi.call/stret_medium2.c: Fix printf format
	specifiers.
	testsuite/libffi.special/unwindtest.cc: include stdint.h.

2009-06-11  Timothy Wall  <<EMAIL>>

	* Makefile.am,
        configure.ac,
        include/ffi.h.in,
        include/ffi_common.h,
        src/closures.c,
        src/dlmalloc.c,
        src/x86/ffi.c,
        src/x86/ffitarget.h,
        src/x86/win64.S (new),
	README: Added win64 support (mingw or MSVC)
        * Makefile.in,
        include/Makefile.in,
        man/Makefile.in,
        testsuite/Makefile.in,
        configure,
        aclocal.m4: Regenerated
        * ltcf-c.sh: properly escape cygwin/w32 path
        * man/ffi_call.3: Clarify size requirements for return value.
        * src/x86/ffi64.c: Fix filename in comment.
        * src/x86/win32.S: Remove unused extern.

        * testsuite/libffi.call/closure_fn0.c,
        testsuite/libffi.call/closure_fn1.c,
        testsuite/libffi.call/closure_fn2.c,
        testsuite/libffi.call/closure_fn3.c,
        testsuite/libffi.call/closure_fn4.c,
        testsuite/libffi.call/closure_fn5.c,
        testsuite/libffi.call/closure_fn6.c,
	testsuite/libffi.call/closure_stdcall.c,
	testsuite/libffi.call/cls_12byte.c,
	testsuite/libffi.call/cls_16byte.c,
	testsuite/libffi.call/cls_18byte.c,
	testsuite/libffi.call/cls_19byte.c,
	testsuite/libffi.call/cls_1_1byte.c,
	testsuite/libffi.call/cls_20byte.c,
	testsuite/libffi.call/cls_20byte1.c,
	testsuite/libffi.call/cls_24byte.c,
	testsuite/libffi.call/cls_2byte.c,
	testsuite/libffi.call/cls_3_1byte.c,
	testsuite/libffi.call/cls_3byte1.c,
 	testsuite/libffi.call/cls_3byte2.c,
 	testsuite/libffi.call/cls_4_1byte.c,
 	testsuite/libffi.call/cls_4byte.c,
 	testsuite/libffi.call/cls_5_1_byte.c,
 	testsuite/libffi.call/cls_5byte.c,
 	testsuite/libffi.call/cls_64byte.c,
 	testsuite/libffi.call/cls_6_1_byte.c,
 	testsuite/libffi.call/cls_6byte.c,
 	testsuite/libffi.call/cls_7_1_byte.c,
 	testsuite/libffi.call/cls_7byte.c,
 	testsuite/libffi.call/cls_8byte.c,
 	testsuite/libffi.call/cls_9byte1.c,
 	testsuite/libffi.call/cls_9byte2.c,
 	testsuite/libffi.call/cls_align_double.c,
 	testsuite/libffi.call/cls_align_float.c,
 	testsuite/libffi.call/cls_align_longdouble.c,
 	testsuite/libffi.call/cls_align_longdouble_split.c,
 	testsuite/libffi.call/cls_align_longdouble_split2.c,
 	testsuite/libffi.call/cls_align_pointer.c,
 	testsuite/libffi.call/cls_align_sint16.c,
 	testsuite/libffi.call/cls_align_sint32.c,
 	testsuite/libffi.call/cls_align_sint64.c,
 	testsuite/libffi.call/cls_align_uint16.c,
 	testsuite/libffi.call/cls_align_uint32.c,
 	testsuite/libffi.call/cls_align_uint64.c,
 	testsuite/libffi.call/cls_dbls_struct.c,
 	testsuite/libffi.call/cls_double.c,
 	testsuite/libffi.call/cls_double_va.c,
 	testsuite/libffi.call/cls_float.c,
 	testsuite/libffi.call/cls_longdouble.c,
 	testsuite/libffi.call/cls_longdouble_va.c,
 	testsuite/libffi.call/cls_multi_schar.c,
 	testsuite/libffi.call/cls_multi_sshort.c,
 	testsuite/libffi.call/cls_multi_sshortchar.c,
 	testsuite/libffi.call/cls_multi_uchar.c,
 	testsuite/libffi.call/cls_multi_ushort.c,
 	testsuite/libffi.call/cls_multi_ushortchar.c,
 	testsuite/libffi.call/cls_pointer.c,
 	testsuite/libffi.call/cls_pointer_stack.c,
 	testsuite/libffi.call/cls_schar.c,
 	testsuite/libffi.call/cls_sint.c,
 	testsuite/libffi.call/cls_sshort.c,
 	testsuite/libffi.call/cls_uchar.c,
 	testsuite/libffi.call/cls_uint.c,
 	testsuite/libffi.call/cls_ulonglong.c,
 	testsuite/libffi.call/cls_ushort.c,
 	testsuite/libffi.call/err_bad_abi.c,
 	testsuite/libffi.call/err_bad_typedef.c,
 	testsuite/libffi.call/float2.c,
 	testsuite/libffi.call/huge_struct.c,
 	testsuite/libffi.call/nested_struct.c,
 	testsuite/libffi.call/nested_struct1.c,
 	testsuite/libffi.call/nested_struct10.c,
 	testsuite/libffi.call/nested_struct2.c,
 	testsuite/libffi.call/nested_struct3.c,
 	testsuite/libffi.call/nested_struct4.c,
 	testsuite/libffi.call/nested_struct5.c,
 	testsuite/libffi.call/nested_struct6.c,
 	testsuite/libffi.call/nested_struct7.c,
 	testsuite/libffi.call/nested_struct8.c,
 	testsuite/libffi.call/nested_struct9.c,
 	testsuite/libffi.call/problem1.c,
 	testsuite/libffi.call/return_ldl.c,
 	testsuite/libffi.call/return_ll1.c,
 	testsuite/libffi.call/stret_large.c,
 	testsuite/libffi.call/stret_large2.c,
 	testsuite/libffi.call/stret_medium.c,
 	testsuite/libffi.call/stret_medium2.c,
        testsuite/libffi.special/unwindtest.cc: use ffi_closure_alloc instead
        of checking for MMAP.  Use intptr_t instead of long casts.

2009-06-04  Andrew Haley  <<EMAIL>>

	* src/powerpc/ffitarget.h: Fix misapplied merge from gcc.

2009-06-04  Andrew Haley  <<EMAIL>>

	* src/mips/o32.S,
	src/mips/n32.S: Fix licence formatting.

2009-06-04  Andrew Haley  <<EMAIL>>

	* src/x86/darwin.S: Fix licence formatting.
	src/x86/win32.S: Likewise.
	src/sh64/sysv.S: Likewise.
	src/sh/sysv.S: Likewise.

2009-06-04  Andrew Haley  <<EMAIL>>

	* src/sh64/ffi.c: Remove lint directives.  Was missing from merge
	of Andreas Tobler's patch from 2006-04-22.
	
2009-06-04  Andrew Haley  <<EMAIL>>

	* src/sh/ffi.c: Apply missing hunk from Alexandre Oliva's patch of
	2007-03-07.

2008-12-26  Timothy Wall  <<EMAIL>>

	* testsuite/libffi.call/cls_longdouble.c,
        testsuite/libffi.call/cls_longdouble_va.c,
        testsuite/libffi.call/cls_align_longdouble.c,
        testsuite/libffi.call/cls_align_longdouble_split.c,
        testsuite/libffi.call/cls_align_longdouble_split2.c: mark expected
        failures on x86_64 cygwin/mingw.

2008-12-22  Timothy Wall  <<EMAIL>>

	* testsuite/libffi.call/closure_fn0.c,
        testsuite/libffi.call/closure_fn1.c,    
        testsuite/libffi.call/closure_fn2.c,    
        testsuite/libffi.call/closure_fn3.c,    
        testsuite/libffi.call/closure_fn4.c,    
        testsuite/libffi.call/closure_fn5.c,    
        testsuite/libffi.call/closure_fn6.c,    
        testsuite/libffi.call/closure_loc_fn0.c,    
        testsuite/libffi.call/closure_stdcall.c,    
        testsuite/libffi.call/cls_align_pointer.c,    
        testsuite/libffi.call/cls_pointer.c,    
        testsuite/libffi.call/cls_pointer_stack.c: use portable cast from
        pointer to integer (intptr_t).
        * testsuite/libffi.call/cls_longdouble.c: disable for win64.
	
2008-12-19  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 3.0.8.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* libtool-version: Increment revision.
	* README: Update for new release.

2008-11-11  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 3.0.7.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* libtool-version: Increment revision.
	* README: Update for new release.

2008-08-25  Andreas Tobler  <<EMAIL>>

	* src/powerpc/ffitarget.h (ffi_abi): Add FFI_LINUX and
	FFI_LINUX_SOFT_FLOAT to the POWERPC_FREEBSD enum.
	Add note about flag bits used for FFI_SYSV_TYPE_SMALL_STRUCT.
	Adjust copyright notice.
	* src/powerpc/ffi.c: Add two new flags to indicate if we have one
	register or two register to use for FFI_SYSV structs.
	(ffi_prep_cif_machdep): Pass the right register flag introduced above.
	(ffi_closure_helper_SYSV): Fix the return type for
	FFI_SYSV_TYPE_SMALL_STRUCT. Comment.
	Adjust copyright notice.

2008-07-24  Anthony Green  <<EMAIL>>

	* testsuite/libffi.call/cls_dbls_struct.c,
	testsuite/libffi.call/cls_double_va.c,
	testsuite/libffi.call/cls_longdouble.c,
	testsuite/libffi.call/cls_longdouble_va.c,
	testsuite/libffi.call/cls_pointer.c,
	testsuite/libffi.call/cls_pointer_stack.c,
	testsuite/libffi.call/err_bad_abi.c: Clean up failures from
	compiler warnings.

2008-07-17  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 3.0.6.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* libtool-version: Increment revision.  Add documentation.
	* README: Update for new release.

2008-07-16  Kaz Kojima  <<EMAIL>>

	* src/sh/ffi.c (ffi_prep_closure_loc): Turn INSN into an unsigned
	int.

2008-07-16  Kaz Kojima  <<EMAIL>>

	* src/sh/sysv.S: Add .note.GNU-stack on Linux.
	* src/sh64/sysv.S: Likewise.

2008-04-03  Anthony Green  <<EMAIL>>

	* libffi.pc.in (Libs): Add -L${libdir}.
	* configure.ac: Bump version to 3.0.5.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* libtool-version: Increment revision.
	* README: Update for new release.

2008-04-03  Anthony Green  <<EMAIL>>
	    Xerces Ranby  <<EMAIL>>

	* include/ffi.h.in: Wrap definition of target architecture to
	protect from double definitions.

2008-03-22  Moriyoshi Koizumi  <<EMAIL>>

	* src/x86/ffi.c (ffi_prep_closure_loc): Fix for bug revealed in
	closure_loc_fn0.c.
	* testsuite/libffi.call/closure_loc_fn0.c (closure_loc_test_fn0):
	New test.

2008-03-04  Anthony Green  <<EMAIL>>
	    Blake Chaffin
	    <EMAIL>

	* testsuite/libffi.call/cls_align_longdouble_split2.c
          testsuite/libffi.call/cls_align_longdouble_split.c
          testsuite/libffi.call/cls_dbls_struct.c
          testsuite/libffi.call/cls_double_va.c
          testsuite/libffi.call/cls_longdouble.c
          testsuite/libffi.call/cls_longdouble_va.c
          testsuite/libffi.call/cls_pointer.c
          testsuite/libffi.call/cls_pointer_stack.c
          testsuite/libffi.call/err_bad_abi.c
          testsuite/libffi.call/err_bad_typedef.c
          testsuite/libffi.call/huge_struct.c
          testsuite/libffi.call/stret_large2.c
          testsuite/libffi.call/stret_large.c
          testsuite/libffi.call/stret_medium2.c
          testsuite/libffi.call/stret_medium.c: New tests from Apple.

2008-02-26  Jakub Jelinek  <<EMAIL>>
            Anthony Green  <<EMAIL>>

	* src/alpha/osf.S: Add .note.GNU-stack on Linux.
	* src/s390/sysv.S: Likewise.
	* src/powerpc/linux64.S: Likewise.
	* src/powerpc/linux64_closure.S: Likewise.
	* src/powerpc/ppc_closure.S: Likewise.
	* src/powerpc/sysv.S: Likewise.
	* src/x86/unix64.S: Likewise.
	* src/x86/sysv.S: Likewise.
	* src/sparc/v8.S: Likewise.
	* src/sparc/v9.S: Likewise.
	* src/m68k/sysv.S: Likewise.
	* src/ia64/unix.S: Likewise.
	* src/arm/sysv.S: Likewise.

2008-02-26  Anthony Green  <<EMAIL>>
            Thomas Heller  <<EMAIL>>

	* src/x86/ffi.c (ffi_closure_SYSV_inner): Change C++ comment to C
	comment.

2008-02-26  Anthony Green  <<EMAIL>>
            Thomas Heller  <<EMAIL>>

	* include/ffi.h.in: Change void (*)() to void (*)(void).

2008-02-26  Anthony Green  <<EMAIL>>
            Thomas Heller  <<EMAIL>>

	* src/alpha/ffi.c: Change void (*)() to void (*)(void).
	src/alpha/osf.S, src/arm/ffi.c, src/frv/ffi.c, src/ia64/ffi.c,
	src/ia64/unix.S, src/java_raw_api.c, src/m32r/ffi.c,
	src/mips/ffi.c, src/pa/ffi.c, src/pa/hpux32.S, src/pa/linux.S,
	src/powerpc/ffi.c, src/powerpc/ffi_darwin.c, src/raw_api.c,
	src/s390/ffi.c, src/sh/ffi.c, src/sh64/ffi.c, src/sparc/ffi.c,
	src/x86/ffi.c, src/x86/unix64.S, src/x86/darwin64.S,
	src/x86/ffi64.c: Ditto.

2008-02-24  Anthony Green  <<EMAIL>>

	* configure.ac: Accept openbsd*, not just openbsd.
	Bump version to 3.0.4.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* libtool-version: Increment revision.
	* README: Update for new release.

2008-02-22  Anthony Green  <<EMAIL>>

	* README: Clean up list of tested platforms.

2008-02-22  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 3.0.3.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* libtool-version: Increment revision.
	* README: Update for new release.  Clean up test docs.

2008-02-22  Bjoern Koenig  <<EMAIL>>
	    Andreas Tobler  <<EMAIL>>

	* configure.ac: Add amd64-*-freebsd* target.
	* configure: Regenerate.

2008-02-22  Thomas Heller <<EMAIL>>

	* configure.ac: Add x86 OpenBSD support.
	* configure: Rebuilt.

2008-02-21  Thomas Heller <<EMAIL>>

	* README: Change "make test" to "make check".

2008-02-21  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 3.0.2.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* libtool-version: Increment revision.
	* README: Update for new release.

2008-02-21  Björn König <<EMAIL>>

	* src/x86/freebsd.S: New file.
	* configure.ac: Add x86 FreeBSD support.
	* Makefile.am: Ditto.

2008-02-15  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 3.0.1.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* libtool-version: Increment revision.
	* README: Update for new release.

2008-02-15  David Daney	 <<EMAIL>>

	* src/mips/ffi.c: Remove extra '>' from include directive.
	(ffi_prep_closure_loc): Use clear_location instead of tramp.

2008-02-15  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 3.0.0.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.

2008-02-15  David Daney	 <<EMAIL>>

	* src/mips/ffi.c (USE__BUILTIN___CLEAR_CACHE):
	Define (conditionally), and use it to include cachectl.h.
	(ffi_prep_closure_loc): Fix cache flushing.
	* src/mips/ffitarget.h (_ABIN32, _ABI64, _ABIO32): Define.

2008-02-15  Anthony Green  <<EMAIL>>

        * man/ffi_call.3, man/ffi_prep_cif.3, man/ffi.3:
	Update dates and remove all references to ffi_prep_closure.
	* configure.ac: Bump version to 2.99.9.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.

2008-02-15  Anthony Green  <<EMAIL>>

	* man/ffi_prep_closure.3: Delete.
	* man/Makefile.am (EXTRA_DIST): Remove ffi_prep_closure.3.
	(man_MANS): Ditto.
	* man/Makefile.in: Rebuilt.
	* configure.ac: Bump version to 2.99.8.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.

2008-02-14  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 2.99.7.
	* configure, doc/stamp-vti, doc/version.texi: Rebuilt.
	* include/ffi.h.in LICENSE src/debug.c src/closures.c
          src/ffitest.c src/s390/sysv.S src/s390/ffitarget.h
          src/types.c src/m68k/ffitarget.h src/raw_api.c src/frv/ffi.c
          src/frv/ffitarget.h src/sh/ffi.c src/sh/sysv.S
          src/sh/ffitarget.h src/powerpc/ffitarget.h src/pa/ffi.c
          src/pa/ffitarget.h src/pa/linux.S src/java_raw_api.c
          src/cris/ffitarget.h src/x86/ffi.c src/x86/sysv.S
          src/x86/unix64.S src/x86/win32.S src/x86/ffitarget.h
          src/x86/ffi64.c src/x86/darwin.S src/ia64/ffi.c
          src/ia64/ffitarget.h src/ia64/ia64_flags.h src/ia64/unix.S
          src/sparc/ffi.c src/sparc/v9.S src/sparc/ffitarget.h
          src/sparc/v8.S src/alpha/ffi.c src/alpha/ffitarget.h
          src/alpha/osf.S src/sh64/ffi.c src/sh64/sysv.S
          src/sh64/ffitarget.h src/mips/ffi.c src/mips/ffitarget.h
          src/mips/n32.S src/mips/o32.S src/arm/ffi.c src/arm/sysv.S
          src/arm/ffitarget.h src/prep_cif.c: Update license text.

2008-02-14  Anthony Green  <<EMAIL>>

	* README: Update tested platforms.
	* configure.ac: Bump version to 2.99.6.
	* configure: Rebuilt.

2008-02-14  Anthony Green  <<EMAIL>>

	* configure.ac: Bump version to 2.99.5.
	* configure: Rebuilt.
	* Makefile.am (EXTRA_DIST): Add darwin64.S
	* Makefile.in: Rebuilt.
	* testsuite/lib/libffi-dg.exp: Remove libstdc++ bits from GCC tree.
	* LICENSE: Update WARRANTY.

2008-02-14  Anthony Green  <<EMAIL>>

	* libffi.pc.in (libdir): Fix libdir definition.
	* configure.ac: Bump version to 2.99.4.
	* configure: Rebuilt.

2008-02-14  Anthony Green  <<EMAIL>>

	* README: Update.
	* libffi.info: New file.
	* doc/stamp-vti: New file.
	* configure.ac: Bump version to 2.99.3.
	* configure: Rebuilt.

2008-02-14  Anthony Green  <<EMAIL>>

	* Makefile.am (SUBDIRS): Add man dir.
	* Makefile.in: Rebuilt.
	* configure.ac: Create Makefile.
	* configure: Rebuilt.
        * man/ffi_call.3 man/ffi_prep_cif.3 man/ffi_prep_closure.3
          man/Makefile.am man/Makefile.in: New files.

2008-02-14  Tom Tromey  <<EMAIL>>

	* aclocal.m4, Makefile.in, configure, fficonfig.h.in: Rebuilt.
	* mdate-sh, texinfo.tex: New files.
	* Makefile.am (info_TEXINFOS): New variable.
	* doc/libffi.texi: New file.
	* doc/version.texi: Likewise.

2008-02-14  Anthony Green  <<EMAIL>>

	* Makefile.am (AM_CFLAGS): Don't compile with -D$(TARGET).
	(lib_LTLIBRARIES): Define.
	(toolexeclib_LIBRARIES): Undefine.
	* Makefile.in: Rebuilt.
	* configure.ac: Reset version to 2.99.1.
	* configure.in: Rebuilt.

2008-02-14  Anthony Green  <<EMAIL>>

	* libffi.pc.in: Use @PACKAGE_NAME@ and @PACKAGE_VERSION@.
	* configure.ac: Reset version to 2.99.1.
	* configure.in: Rebuilt.
	* Makefile.am (EXTRA_DIST): Add ChangeLog.libffi.
	* Makefile.in: Rebuilt.
	* LICENSE: Update copyright notice.

2008-02-14  Anthony Green  <<EMAIL>>

	* include/Makefile.am (nodist_includes_HEADERS): Define.  Don't
	distribute ffitarget.h or ffi.h from the build include dir.
	* Makefile.in: Rebuilt.

2008-02-14  Anthony Green  <<EMAIL>>

	* include/Makefile.am (includesdir): Install headers under libdir.
	(pkgconfigdir): Define. Install libffi.pc.
	* include/Makefile.in: Rebuilt.
	* libffi.pc.in: Create.
	* libtool-version: Increment CURRENT
	* configure.ac: Add libffi.pc.in
	* configure: Rebuilt.

2008-02-03  Anthony Green  <<EMAIL>>

	* include/Makefile.am (includesdir): Fix header install with
	DESTDIR.
	* include/Makefile.in: Rebuilt.

2008-02-03  Timothy Wall  <<EMAIL>>

	* src/x86/ffi.c (FFI_INIT_TRAMPOLINE_STDCALL): Calculate jump return
          offset based on code pointer, not data pointer.

2008-02-01  Anthony Green  <<EMAIL>>

	* include/Makefile.am: Fix header installs.
	* Makefile.am: Ditto.
	* include/Makefile.in: Rebuilt.
	* Makefile.in: Ditto.

2008-02-01  Anthony Green  <<EMAIL>>

	* src/x86/ffi.c (FFI_INIT_TRAMPOLINE_STDCALL,
	FFI_INIT_TRAMPOLINE): Revert my broken changes to twall's last
	patch.

2008-01-31  Anthony Green  <<EMAIL>>

	* Makefile.am (EXTRA_DIST): Add missing files.
	* testsuite/Makefile.am: Ditto.
	* Makefile.in, testsuite/Makefile.in: Rebuilt.

2008-01-31  Timothy Wall <<EMAIL>>

	* testsuite/libffi.call/closure_stdcall.c: Add test for stdcall
	closures.
	* src/x86/ffitarget.h: Increase size of trampoline for stdcall
	closures.
	* src/x86/win32.S: Add assembly for stdcall closure.
	* src/x86/ffi.c: Initialize stdcall closure trampoline.

2008-01-30  H.J. Lu <<EMAIL>>

	PR libffi/34612
	* src/x86/sysv.S (ffi_closure_SYSV): Pop 4 byte from stack when
	returning struct.

	* testsuite/libffi.call/call.exp: Add "-O2 -fomit-frame-pointer"
	tests.

2008-01-30  Anthony Green  <<EMAIL>>

	* Makefile.am, include/Makefile.am: Move headers to
	libffi_la_SOURCES for new automake.
	* Makefile.in, include/Makefile.in: Rebuilt.

	* testsuite/lib/wrapper.exp: Copied from gcc tree to allow for
	execution outside of gcc tree.
	* testsuite/lib/target-libpath.exp: Ditto.

	* testsuite/lib/libffi-dg.exp: Many changes to allow for execution
	outside of gcc tree.


=============================================================================
From the old ChangeLog.libgcj file....

2004-01-14  Kelley Cook  <<EMAIL>>

	* configure.in: Add in AC_PREREQ(2.13)

2003-02-20  Alexandre Oliva  <<EMAIL>>

	* configure.in: Propagate ORIGINAL_LD_FOR_MULTILIBS to
	config.status.
	* configure: Rebuilt.

2002-01-27  Alexandre Oliva  <<EMAIL>>

	* configure.in (toolexecdir, toolexeclibdir): Set and AC_SUBST.
	Remove USE_LIBDIR conditional.
	* Makefile.am (toolexecdir, toolexeclibdir): Don't override.
	* Makefile.in, configure: Rebuilt.

Mon Aug  9 18:33:38 1999  Rainer Orth  <<EMAIL>>

	* include/Makefile.in: Rebuilt.
	* Makefile.in: Rebuilt
	* Makefile.am (toolexeclibdir): Add $(MULTISUBDIR) even for native
	builds.
	Use USE_LIBDIR.

	* configure: Rebuilt.
	* configure.in (USE_LIBDIR): Define for native builds.
	Use lowercase in configure --help explanations.

1999-08-08  Anthony Green  <<EMAIL>>

	* include/ffi.h.in (FFI_FN): Remove `...'.

1999-08-08  Anthony Green  <<EMAIL>>

	* Makefile.in: Rebuilt.
	* Makefile.am (AM_CFLAGS): Compile with -fexceptions.

	* src/x86/sysv.S: Add exception handling metadata.


=============================================================================
	
The libffi version 1 ChangeLog archive.

Version 1 of libffi had per-directory ChangeLogs.  Current and future
versions have a single ChangeLog file in the root directory.  The
version 1 ChangeLogs have all been concatenated into this file for
future reference only.

--- libffi ----------------------------------------------------------------

Mon Oct  5 02:17:50 1998  Anthony Green  <<EMAIL>>

	* configure.in: Boosted rev.
	* configure, Makefile.in, aclocal.m4: Rebuilt.
	* README: Boosted rev and updated release notes.

Mon Oct  5 01:03:03 1998  Anthony Green  <<EMAIL>>

	* configure.in: Boosted rev.
	* configure, Makefile.in, aclocal.m4: Rebuilt.
	* README: Boosted rev and updated release notes.

1998-07-25  Andreas Schwab  <<EMAIL>>

	* m68k/ffi.c (ffi_prep_cif_machdep): Use bitmask for cif->flags.
	Correctly handle small structures.
	(ffi_prep_args): Also handle small structures.
	(ffi_call): Pass size of return type to ffi_call_SYSV.
	* m68k/sysv.S: Adjust for above changes.  Correctly align small
	structures in the return value.

	* types.c (uint64, sint64) [M68K]: Change alignment to 4.

Fri Apr 17 17:26:58 1998  Anthony Green  <<EMAIL>>

	* configure.in: Boosted rev.
	* configure,Makefile.in,aclocal.m4: Rebuilt.
	* README: Boosted rev and added release notes.

Sun Feb 22 00:50:41 1998  Geoff Keating  <<EMAIL>>

	* configure.in: Add PowerPC config bits.

1998-02-14  Andreas Schwab  <<EMAIL>>

	* configure.in: Add m68k config bits.  Change AC_CANONICAL_SYSTEM
	to AC_CANONICAL_HOST, this is not a compiler.  Use $host instead
	of $target.  Remove AC_CHECK_SIZEOF(char), we already know the
	result.  Fix argument of AC_ARG_ENABLE.
	* configure, fficonfig.h.in: Rebuilt.

Tue Feb 10 20:53:40 1998  Richard Henderson  <<EMAIL>>

	* configure.in: Add Alpha config bits.

Tue May 13 13:39:20 1997  Anthony Green  <<EMAIL>>

	* README: Updated dates and reworded Irix comments.

	* configure.in: Removed AC_PROG_RANLIB.

	* Makefile.in, aclocal.m4, config.guess, config.sub, configure,
	ltmain.sh, */Makefile.in: libtoolized again and	rebuilt with 
	automake and autoconf.
	
Sat May 10 18:44:50 1997  Tom Tromey  <<EMAIL>>

	* configure, aclocal.m4: Rebuilt.
	* configure.in: Don't compute EXTRADIST; now handled in
	src/Makefile.in.  Removed macros implied by AM_INIT_AUTOMAKE.
	Don't run AM_MAINTAINER_MODE.

Thu May  8 14:34:05 1997  Anthony Green  <<EMAIL>>

	* missing, ltmain.sh, ltconfig.sh: Created. These are new files
	required by automake and libtool.

	* README: Boosted rev to 1.14. Added notes.

	* acconfig.h: Moved PACKAGE and VERSION for new automake.
	
	* configure.in: Changes for libtool.
	
	* Makefile.am (check): make test now make check. Uses libtool now.

	* Makefile.in, configure.in, aclocal.h, fficonfig.h.in: Rebuilt.

Thu May  1 16:27:07 1997  Anthony Green  <<EMAIL>>

	* missing: Added file required by new automake.

Tue Nov 26 14:10:42 1996  Anthony Green  <<EMAIL>>

	* acconfig.h: Added USING_PURIFY flag. This is defined when
	--enable-purify-safety was used at configure time.

	* configure.in (allsources): Added --enable-purify-safety switch.
	(VERSION): Boosted rev to 1.13.
	* configure: Rebuilt.

Fri Nov 22 06:46:12 1996  Anthony Green  <<EMAIL>>

	* configure.in (VERSION): Boosted rev to 1.12.
	Removed special CFLAGS hack for gcc.
	* configure: Rebuilt.

	* README: Boosted rev to 1.12. Added notes.

	* Many files: Cygnus Support changed to Cygnus Solutions.

Wed Oct 30 11:15:25 1996  Anthony Green  <<EMAIL>>

	* configure.in (VERSION): Boosted rev to 1.11.
	* configure: Rebuilt.

	* README: Boosted rev to 1.11. Added notes about GNU make.

Tue Oct 29 12:25:12 1996  Anthony Green  <<EMAIL>>

	* configure.in: Fixed -Wall trick.
	(VERSION): Boosted rev.
	* configure: Rebuilt

	* acconfig.h: Needed for --enable-debug configure switch.

	* README: Boosted rev to 1.09. Added more notes on building
	libffi, and LCLint.

	* configure.in: Added --enable-debug switch. Boosted rev to
	1.09.
	* configure: Rebuilt

Tue Oct 15 13:11:28 1996  Anthony Green  <<EMAIL>>

	* configure.in (VERSION): Boosted rev to 1.08
	* configure: Rebuilt.

	* README: Added n32 bug fix notes.

	* Makefile.am: Added "make lint" production. 
	* Makefile.in: Rebuilt.

Mon Oct 14 10:54:46 1996  Anthony Green  <<EMAIL>>

	* README: Added web page reference.

	* configure.in, README: Boosted rev to 1.05
	* configure: Rebuilt.

	* README: Fixed n32 sample code.

Fri Oct 11 17:09:28 1996  Anthony Green  <<EMAIL>>

	* README: Added sparc notes.

	* configure.in, README: Boosted rev to 1.04.
	* configure: Rebuilt.

Thu Oct 10 10:31:03 1996  Anthony Green  <<EMAIL>>

	* configure.in, README: Boosted rev to 1.03.
	* configure: Rebuilt.

	* README: Added struct notes. 

	* Makefile.am (EXTRA_DIST): Added LICENSE to distribution.
	* Makefile.in: Rebuilt.

	* README: Removed Linux section. No special notes now
	because aggregates arg/return types work.

Wed Oct  9 16:16:42 1996  Anthony Green  <<EMAIL>>

	* README, configure.in (VERSION): Boosted rev to 1.02
	* configure: Rebuilt.

Tue Oct  8 11:56:33 1996  Anthony Green  <<EMAIL>>

	* README (NOTE): Added n32 notes.

	* Makefile.am: Added test production.
	* Makefile: Rebuilt

	* README: spell checked!

	* configure.in (VERSION): Boosted rev to 1.01
	* configure: Rebuilt.

Mon Oct  7 15:50:22 1996  Anthony Green  <<EMAIL>>

	* configure.in: Added nasty bit to support SGI tools.
	* configure: Rebuilt.
	
	* README: Added SGI notes. Added note about automake bug.

Mon Oct  7 11:00:28 1996  Anthony Green  <<EMAIL>>

	* README: Rewrote intro, and fixed examples.

Fri Oct  4 10:19:55 1996  Anthony Green  <<EMAIL>>

	* configure.in: -D$TARGET is no longer used as a compiler switch.
	It is now inserted into ffi.h at configure time.
	* configure: Rebuilt.

	* FFI_ABI and FFI_STATUS are now ffi_abi and ffi_status.

Thu Oct  3 13:47:34 1996  Anthony Green  <<EMAIL>>

	* README, LICENSE: Created. Wrote some docs.

	* configure.in: Don't barf on i586-unknown-linuxaout.
	Added EXTRADIST code for "make dist".
	* configure: Rebuilt.

	* */Makefile.in: Rebuilt with patched automake. 

Tue Oct  1 17:12:25 1996  Anthony Green  <<EMAIL>>

	* Makefile.am, aclocal.m4, config.guess, config.sub,
	configure.in, fficonfig.h.in, install-sh, mkinstalldirs, 
	stamp-h.in: Created
	* Makefile.in, configure: Generated

--- libffi/include --------------------------------------------------------

Tue Feb 24 13:09:36 1998  Anthony Green  <<EMAIL>>

	* ffi_mips.h: Updated FFI_TYPE_STRUCT_* values based on
	ffi.h.in changes.  This is a work-around for SGI's "simple"
	assembler.

Sun Feb 22 00:51:55 1998  Geoff Keating  <<EMAIL>>

	* ffi.h.in: PowerPC support.

1998-02-14  Andreas Schwab  <<EMAIL>>

	* ffi.h.in: Add m68k support.
	(FFI_TYPE_LONGDOUBLE): Make it a separate value.

Tue Feb 10 20:55:16 1998  Richard Henderson  <<EMAIL>>

	* ffi.h.in (SIZEOF_ARG): Use a pointer type by default.

	* ffi.h.in: Alpha support.

Fri Nov 22 06:48:45 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in, ffi_common.h: Cygnus Support -> Cygnus Solutions.

Wed Nov 20 22:31:01 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in: Added ffi_type_void definition.

Tue Oct 29 12:22:40 1996  Anthony Green  <<EMAIL>>

	* Makefile.am (hack_DATA): Always install ffi_mips.h.

	* ffi.h.in: Removed FFI_DEBUG. It's now in the correct
	place (acconfig.h).
	Added #include <stddef.h> for size_t definition.

Tue Oct 15 17:23:35 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in, ffi_common.h, ffi_mips.h: More clean up.
	Commented out #define of FFI_DEBUG.

Tue Oct 15 13:01:06 1996  Anthony Green  <<EMAIL>>

	* ffi_common.h: Added bool definition.

	* ffi.h.in, ffi_common.h: Clean up based on LCLint output.
	Added funny /*@...@*/ comments to annotate source.

Mon Oct 14 12:29:23 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in: Interface changes based on feedback from Jim
	Blandy.

Fri Oct 11 16:49:35 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in: Small change for sparc support.

Thu Oct 10 14:53:37 1996  Anthony Green  <<EMAIL>>

	* ffi_mips.h: Added FFI_TYPE_STRUCT_* definitions for 
	special structure return types.

Wed Oct  9 13:55:57 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in: Added SIZEOF_ARG definition for X86

Tue Oct  8 11:40:36 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in (FFI_FN): Added macro for eliminating compiler warnings.
	Use it to case your function pointers to the proper type.

	* ffi_mips.h (SIZEOF_ARG): Added magic to fix type promotion bug.

	* Makefile.am (EXTRA_DIST): Added ffi_mips.h to EXTRA_DIST.
	* Makefile: Rebuilt.

	* ffi_mips.h: Created. Moved all common mips definitions here.

Mon Oct  7 10:58:12 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in: The SGI assember is very picky about parens. Redefined
 	some macros to avoid problems.

	* ffi.h.in: Added FFI_DEFAULT_ABI definitions. Also added
	externs for pointer, and 64bit integral ffi_types.

Fri Oct  4 09:51:37 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in: Added FFI_ABI member to ffi_cif and changed
	function prototypes accordingly.
	Added #define @TARGET@. Now programs including ffi.h don't 
	have to specify this themselves.

Thu Oct  3 15:36:44 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in: Changed ffi_prep_cif's values from void* to void**

	* Makefile.am (EXTRA_DIST): Added EXTRA_DIST for "make dist"
	to work.
	* Makefile.in: Regenerated.

Wed Oct  2 10:16:59 1996  Anthony Green  <<EMAIL>>

	* Makefile.am: Created
	* Makefile.in: Generated

	* ffi_common.h: Added rcsid comment

Tue Oct  1 17:13:51 1996  Anthony Green  <<EMAIL>>

	* ffi.h.in, ffi_common.h: Created

--- libffi/src ------------------------------------------------------------

Mon Oct  5 02:17:50 1998  Anthony Green  <<EMAIL>>

	* arm/ffi.c, arm/sysv.S: Created.

	* Makefile.am: Added arm files.
	* Makefile.in: Rebuilt.

Mon Oct  5 01:41:38 1998  Anthony Green  <<EMAIL>>

	* Makefile.am (libffi_la_LDFLAGS): Incremented revision.

Sun Oct  4 16:27:17 1998  Anthony Green  <<EMAIL>>

	* alpha/osf.S (ffi_call_osf): Patch for DU assembler.

	* ffitest.c (main): long long and long double return values work
	for x86.

Fri Apr 17 11:50:58 1998  Anthony Green  <<EMAIL>>

	* Makefile.in: Rebuilt.

	* ffitest.c (main): Floating point tests not executed for systems
 	with broken lond double (SunOS 4 w/ GCC).

	* types.c: Fixed x86 alignment info for long long types.

Thu Apr 16 07:15:28 1998  Anthony Green  <<EMAIL>>

	* ffitest.c: Added more notes about GCC bugs under Irix 6.

Wed Apr 15 08:42:22 1998  Anthony Green  <<EMAIL>>

	* ffitest.c (struct5): New test function.
	(main): New test with struct5.

Thu Mar  5 10:48:11 1998  Anthony Green  <<EMAIL>>

	* prep_cif.c (initialize_aggregate): Fix assertion for
	nested structures.

Tue Feb 24 16:33:41 1998  Anthony Green  <<EMAIL>>

	* prep_cif.c (ffi_prep_cif): Added long double support for sparc.

Sun Feb 22 00:52:18 1998  Geoff Keating  <<EMAIL>>

	* powerpc/asm.h: New file.
	* powerpc/ffi.c: New file.
	* powerpc/sysv.S: New file.
	* Makefile.am: PowerPC port.
	* ffitest.c (main): Allow all tests to run even in presence of gcc
 	bug on PowerPC.

1998-02-17  Anthony Green  <<EMAIL>>

	* mips/ffi.c: Fixed comment typo.

	* x86/ffi.c (ffi_prep_cif_machdep), x86/sysv.S (retfloat): 
	Fixed x86 long double return handling.

	* types.c: Fixed x86 long double alignment info.

1998-02-14  Andreas Schwab  <<EMAIL>>

	* types.c: Add m68k support.

	* ffitest.c (floating): Add long double parameter.
	(return_ll, ldblit): New functions to test long long and long
	double return value.
	(main): Fix type error in assignment of ts[1-4]_type.elements.
	Add tests for long long and long double arguments and return
	values.

	* prep_cif.c (ffi_prep_cif) [M68K]: Don't allocate argument for
	struct value pointer.

	* m68k/ffi.c, m68k/sysv.S: New files.
	* Makefile.am: Add bits for m68k port.  Add kludge to work around
	automake deficiency.
	(test): Don't require "." in $PATH.
	* Makefile.in: Rebuilt.

Wed Feb 11 07:36:50 1998  Anthony Green  <<EMAIL>>

	* Makefile.in: Rebuilt.

Tue Feb 10 20:56:00 1998  Richard Henderson  <<EMAIL>>

	* alpha/ffi.c, alpha/osf.S: New files.
	* Makefile.am: Alpha port.

Tue Nov 18 14:12:07 1997  Anthony Green  <<EMAIL>>

	* mips/ffi.c (ffi_prep_cif_machdep): Initialize rstruct_flag
	for n32.

Tue Jun  3 17:18:20 1997  Anthony Green  <<EMAIL>>

	* ffitest.c (main): Added hack to get structure tests working
	correctly.

Sat May 10 19:06:42 1997  Tom Tromey  <<EMAIL>>

	* Makefile.in: Rebuilt.
	* Makefile.am (EXTRA_DIST): Explicitly list all distributable
	files in subdirs.
	(VERSION, CC): Removed.

Thu May  8 17:19:01 1997  Anthony Green  <<EMAIL>>

	* Makefile.am: Many changes for new automake and libtool.
	* Makefile.in: Rebuilt.

Fri Nov 22 06:57:56 1996  Anthony Green  <<EMAIL>>

	* ffitest.c (main): Fixed test case for non mips machines.

Wed Nov 20 22:31:59 1996  Anthony Green  <<EMAIL>>

	* types.c: Added ffi_type_void declaration.

Tue Oct 29 13:07:19 1996  Anthony Green  <<EMAIL>>

	* ffitest.c (main): Fixed character constants.
	(main): Emit warning for structure test 3 failure on Sun.

	* Makefile.am (VPATH): Fixed VPATH def'n so automake won't
	strip it out. 
	Moved distdir hack from libffi to automake. 
	(ffitest): Added missing -c for $(COMPILE) (change in automake).
	* Makefile.in: Rebuilt.
	
Tue Oct 15 13:08:20 1996  Anthony Green  <<EMAIL>>

	* Makefile.am: Added "make lint" production. 
	* Makefile.in: Rebuilt.

	* prep_cif.c (STACK_ARG_SIZE): Improved STACK_ARG_SIZE macro.
  	Clean up based on LCLint output. Added funny /*@...@*/ comments to
 	annotate source.

	* ffitest.c, debug.c: Cleaned up code.

Mon Oct 14 12:26:56 1996  Anthony Green  <<EMAIL>>

	* ffitest.c: Changes based on interface changes.

	* prep_cif.c (ffi_prep_cif): Cleaned up interface based on
	feedback from Jim Blandy.

Fri Oct 11 15:53:18 1996  Anthony Green  <<EMAIL>>

	* ffitest.c: Reordered tests while porting to sparc.
	Made changes to handle lame structure passing for sparc.
	Removed calls to fflush().

	* prep_cif.c (ffi_prep_cif): Added special case for sparc
	aggregate type arguments.

Thu Oct 10 09:56:51 1996  Anthony Green  <<EMAIL>>

	* ffitest.c (main): Added structure passing/returning tests.

	* prep_cif.c (ffi_prep_cif): Perform proper initialization
	of structure return types if needed.
	(initialize_aggregate): Bug fix

Wed Oct  9 16:04:20 1996  Anthony Green  <<EMAIL>>

	* types.c: Added special definitions for x86 (double doesn't
	need double word alignment).

	* ffitest.c: Added many tests

Tue Oct  8 09:19:22 1996  Anthony Green  <<EMAIL>>

	* prep_cif.c (ffi_prep_cif): Fixed assertion.

	* debug.c (ffi_assert): Must return a non void now.

	* Makefile.am: Added test production.
	* Makefile: Rebuilt.

	* ffitest.c (main): Created. 

	* types.c: Created. Stripped common code out of */ffi.c.

	* prep_cif.c: Added missing stdlib.h include.

	* debug.c (ffi_type_test): Used "a" to eliminate compiler
	warnings in non-debug builds. Included ffi_common.h.

Mon Oct  7 15:36:42 1996  Anthony Green  <<EMAIL>>

	* Makefile.am: Added a rule for .s -> .o
	This is required by the SGI compiler.
	* Makefile: Rebuilt.

Fri Oct  4 09:51:08 1996  Anthony Green  <<EMAIL>>

	* prep_cif.c (initialize_aggregate): Moved abi specification
	to ffi_prep_cif().

Thu Oct  3 15:37:37 1996  Anthony Green  <<EMAIL>>

	* prep_cif.c (ffi_prep_cif): Changed values from void* to void**.
	(initialize_aggregate): Fixed aggregate type initialization.

	* Makefile.am (EXTRA_DIST): Added support code for "make dist".
	* Makefile.in: Regenerated.

Wed Oct  2 11:41:57 1996  Anthony Green  <<EMAIL>>

	* debug.c, prep_cif: Created.

	* Makefile.am: Added debug.o and prep_cif.o to OBJ.
	* Makefile.in: Regenerated.

	* Makefile.am (INCLUDES): Added missing -I../include
	* Makefile.in: Regenerated.

Tue Oct  1 17:11:51 1996  Anthony Green  <<EMAIL>>

	* error.c, Makefile.am: Created.
	* Makefile.in: Generated.

--- libffi/src/x86 --------------------------------------------------------

Sun Oct  4 16:27:17 1998  Anthony Green  <<EMAIL>>

	* sysv.S (retlongdouble): Fixed long long return value support.
	* ffi.c (ffi_prep_cif_machdep): Ditto.

Wed May 13 04:30:33 1998  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_prep_cif_machdep): Fixed long double return value
	support.

Wed Apr 15 08:43:20 1998  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_prep_args): small struct support was missing.

Thu May  8 16:53:58 1997  Anthony Green  <<EMAIL>>

	* objects.mak: Removed.

Mon Dec  2 15:12:58 1996  Tom Tromey  <<EMAIL>>

	* sysv.S: Use .balign, for a.out Linux boxes.

Tue Oct 15 13:06:50 1996  Anthony Green  <<EMAIL>>

	* ffi.c: Clean up based on LCLint output.
	Added funny /*@...@*/ comments to annotate source.

Fri Oct 11 16:43:38 1996  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_call): Added assertion for bad ABIs.

Wed Oct  9 13:57:27 1996  Anthony Green  <<EMAIL>>

	* sysv.S (retdouble): Fixed double return problems.

	* ffi.c	(ffi_call): Corrected fn arg definition.
	(ffi_prep_cif_machdep): Fixed double return problems

Tue Oct  8 12:12:49 1996  Anthony Green  <<EMAIL>>

	* ffi.c: Moved ffi_type definitions to types.c.
	(ffi_prep_args): Fixed type promotion bug.

Mon Oct  7 15:53:06 1996  Anthony Green  <<EMAIL>>

	* ffi.c (FFI_*_TYPEDEF): Removed redundant ';'

Fri Oct  4 09:54:53 1996  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_call): Removed FFI_ABI arg, and swapped
	remaining args.

Wed Oct  2 10:07:05 1996  Anthony Green  <<EMAIL>>

	* ffi.c, sysv.S, objects.mak: Created.
	(ffi_prep_cif): cif->rvalue no longer initialized to NULL.
	(ffi_prep_cif_machdep): Moved machine independent cif processing
	to src/prep_cif.c. Introduced ffi_prep_cif_machdep().

--- libffi/src/mips -------------------------------------------------------

Tue Feb 17 17:18:07 1998  Anthony Green  <<EMAIL>>

	* o32.S: Fixed typo in comment.

	* ffi.c (ffi_prep_cif_machdep): Fixed argument processing.

Thu May  8 16:53:58 1997  Anthony Green  <<EMAIL>>

	* o32.s, n32.s: Wrappers for SGI tool support.

	* objects.mak: Removed.

Tue Oct 29 14:37:45 1996  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_prep_args): Changed int z to size_t z.

Tue Oct 15 13:17:25 1996  Anthony Green  <<EMAIL>>

	* n32.S: Fixed bad stack munging. 

	* ffi.c: Moved prototypes for ffi_call_?32() to here from
	ffi_mips.h because extended_cif is not defined in ffi_mips.h.

Mon Oct 14 12:42:02 1996  Anthony Green  <<EMAIL>>

	* ffi.c: Interface changes based on feedback from Jim Blandy.

Thu Oct 10 11:22:16 1996  Anthony Green  <<EMAIL>>

	* n32.S, ffi.c: Lots of changes to support passing and 
	returning structures with the n32 calling convention.

	* n32.S: Fixed fn pointer bug.

	* ffi.c (ffi_prep_cif_machdep): Fix for o32 structure
	return values.
	(ffi_prep_args): Fixed n32 structure passing when structures
	partially fit in registers.

Wed Oct  9 13:49:25 1996  Anthony Green  <<EMAIL>>

	* objects.mak: Added n32.o.

	* n32.S: Created.

	* ffi.c (ffi_prep_args): Added magic to support proper
	n32 processing.

Tue Oct  8 10:37:35 1996  Anthony Green  <<EMAIL>>

	* ffi.c: Moved ffi_type definitions to types.c.
	(ffi_prep_args): Fixed type promotion bug.

	* o32.S: This code is only built for o32 compiles.
	A lot of the #define cruft has moved to ffi_mips.h.

	* ffi.c (ffi_prep_cif_machdep): Fixed arg flags. Second arg
	is only processed if the first is either a float or double.

Mon Oct  7 15:33:59 1996  Anthony Green  <<EMAIL>>

	* o32.S: Modified to compile under each of o32, n32 and n64.

	* ffi.c (FFI_*_TYPEDEF): Removed redundant ';'

Fri Oct  4 09:53:25 1996  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_call): Removed FFI_ABI arg, and swapped
	remaining args.

Wed Oct  2 17:41:22 1996  Anthony Green  <<EMAIL>>

	* o32.S: Removed crufty definitions.

Wed Oct  2 12:53:42 1996  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_prep_cif): cif->rvalue no longer initialized to NULL.
	(ffi_prep_cif_machdep): Moved all machine independent cif processing
	to src/prep_cif.c. Introduced ffi_prep_cif_machdep. Return types
	of FFI_TYPE_STRUCT are no different than FFI_TYPE_INT.

Tue Oct  1 17:11:02 1996  Anthony Green  <<EMAIL>>

	* ffi.c, o32.S, object.mak: Created
	
--- libffi/src/sparc ------------------------------------------------------

Tue Feb 24 16:33:18 1998  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_prep_args): Added long double support.

Thu May  8 16:53:58 1997  Anthony Green  <<EMAIL>>

	* objects.mak: Removed.

Thu May  1 16:07:56 1997  Anthony Green  <<EMAIL>>

	* v8.S: Fixed minor portability problem reported by 
	Russ McManus <<EMAIL>>.

Tue Nov 26 14:12:43 1996  Anthony Green  <<EMAIL>>

	* v8.S: Used STACKFRAME define elsewhere. 

	* ffi.c (ffi_prep_args): Zero out space when USING_PURIFY
	is set.
	(ffi_prep_cif_machdep): Allocate the correct stack frame 
	space for functions with < 6 args.

Tue Oct 29 15:08:55 1996  Anthony Green  <<EMAIL>>

	* ffi.c (ffi_prep_args): int z is now size_t z.

Mon Oct 14 13:31:24 1996  Anthony Green  <<EMAIL>>

	* v8.S (ffi_call_V8): Gordon rewrites this again. It looks
	great now.

	* ffi.c (ffi_call): The comment about hijacked registers
	is no longer valid after gordoni hacked v8.S.

        * v8.S (ffi_call_V8): Rewrote with gordoni. Much simpler.
	
	* v8.S, ffi.c: ffi_call() had changed to accept more than
	two args, so v8.S had to change (because it hijacks incoming
	arg registers).

	* ffi.c: Interface changes based on feedback from Jim Blandy.

Thu Oct 10 17:48:16 1996  Anthony Green  <<EMAIL>>

	* ffi.c, v8.S, objects.mak: Created.
	

