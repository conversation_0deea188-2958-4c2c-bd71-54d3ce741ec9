const mysqlConnection = require("./dbconnection");

// Performance monitoring
const queryStats = {
  totalQueries: 0,
  slowQueries: 0,
  failedQueries: 0,
  averageQueryTime: 0,
  startTime: Date.now()
};

// Log slow queries (queries taking more than 1 second)
const SLOW_QUERY_THRESHOLD = 1000; // 1 second

function queryRunner(qry, params = []) {
  return new Promise((resolve, reject) => {
    if (qry === null || qry === undefined) {
      console.error("Error in query string: Query is null or undefined");
      return reject({
        status: 400,
        error: "No Query Found",
      });
    }

    const queryStartTime = Date.now();
    const queryId = Math.random().toString(36).substring(7);
    
    // Log query execution (truncated for readability)
    const truncatedQuery = qry.length > 200 ? qry.substring(0, 200) + '...' : qry;
    console.log(`[Query ${queryId}] Executing: ${truncatedQuery}`);
    console.log(`[Query ${queryId}] Params:`, params.length > 0 ? params : 'None');

    mysqlConnection
      .executeQuery(qry, params)
      .then((results) => {
        const queryTime = Date.now() - queryStartTime;
        
        // Update statistics
        queryStats.totalQueries++;
        queryStats.averageQueryTime = (queryStats.averageQueryTime * (queryStats.totalQueries - 1) + queryTime) / queryStats.totalQueries;
        
        // Log slow queries
        if (queryTime > SLOW_QUERY_THRESHOLD) {
          queryStats.slowQueries++;
          console.warn(`[Query ${queryId}] SLOW QUERY: ${queryTime}ms - ${truncatedQuery}`);
        }
        
        // Log successful query completion
        console.log(`[Query ${queryId}] Completed in ${queryTime}ms`);
        
        resolve(results);
      })
      .catch((error) => {
        const queryTime = Date.now() - queryStartTime;
        queryStats.failedQueries++;
        
        // Enhanced error logging
        console.error(`[Query ${queryId}] FAILED after ${queryTime}ms:`, {
          error: error.message,
          code: error.code,
          errno: error.errno,
          sqlMessage: error.sqlMessage,
          sqlState: error.sqlState,
          query: truncatedQuery,
          params: params
        });

        // Provide more specific error messages
        let errorResponse = {
          status: 500,
          error: "Database Error",
          message: error.message
        };

        if (error.code === 'ETIMEDOUT') {
          errorResponse = {
            status: 504,
            error: "Database Timeout",
            message: "Database connection timed out. Please try again."
          };
        } else if (error.code === 'ECONNREFUSED') {
          errorResponse = {
            status: 503,
            error: "Database Unavailable",
            message: "Database service is currently unavailable."
          };
        } else if (error.code === 'ER_CON_COUNT_ERROR') {
          errorResponse = {
            status: 503,
            error: "Database Overloaded",
            message: "Database is currently overloaded. Please try again later."
          };
        } else if (error.code === 'PROTOCOL_CONNECTION_LOST') {
          errorResponse = {
            status: 503,
            error: "Database Connection Lost",
            message: "Database connection was lost. Please try again."
          };
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
          errorResponse = {
            status: 500,
            error: "Database Access Denied",
            message: "Database access denied. Please contact administrator."
          };
        } else if (error.code === 'ER_BAD_DB_ERROR') {
          errorResponse = {
            status: 500,
            error: "Database Not Found",
            message: "Database not found. Please contact administrator."
          };
        }

        reject(errorResponse);
      });
  });
}

// Enhanced query runner with transaction support
function queryRunnerWithTransaction(queries) {
  return new Promise((resolve, reject) => {
    if (!Array.isArray(queries) || queries.length === 0) {
      console.error("Error: No queries provided for transaction");
      return reject({
        status: 400,
        error: "No Queries Found",
      });
    }

    const transactionStartTime = Date.now();
    const transactionId = Math.random().toString(36).substring(7);
    
    console.log(`[Transaction ${transactionId}] Starting transaction with ${queries.length} queries`);

    mysqlConnection
      .executeTransaction(queries)
      .then((result) => {
        const transactionTime = Date.now() - transactionStartTime;
        console.log(`[Transaction ${transactionId}] Completed successfully in ${transactionTime}ms`);
        resolve(result);
      })
      .catch((error) => {
        const transactionTime = Date.now() - transactionStartTime;
        console.error(`[Transaction ${transactionId}] FAILED after ${transactionTime}ms:`, error);
        reject(error);
      });
  });
}

// Health check function
async function checkDatabaseHealth() {
  try {
    await mysqlConnection.healthCheck();
    return {
      status: 'healthy',
      message: 'Database connection is healthy',
      stats: getQueryStats()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Database connection is unhealthy',
      error: error.message,
      stats: getQueryStats()
    };
  }
}

// Get query statistics
function getQueryStats() {
  const uptime = Date.now() - queryStats.startTime;
  return {
    ...queryStats,
    uptime: Math.floor(uptime / 1000), // in seconds
    queriesPerSecond: queryStats.totalQueries / (uptime / 1000),
    successRate: queryStats.totalQueries > 0 ? 
      ((queryStats.totalQueries - queryStats.failedQueries) / queryStats.totalQueries * 100).toFixed(2) + '%' : '0%'
  };
}

// Reset statistics
function resetQueryStats() {
  Object.assign(queryStats, {
    totalQueries: 0,
    slowQueries: 0,
    failedQueries: 0,
    averageQueryTime: 0,
    startTime: Date.now()
  });
  console.log('Query statistics reset');
}

// Export enhanced functions
module.exports = { 
  queryRunner,
  queryRunnerWithTransaction,
  checkDatabaseHealth,
  getQueryStats,
  resetQueryStats
};