{"name": "React-Mapbuffer", "version": "0.80.1", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.1"}, "source_files": "react/renderer/mapbuffer/*.{cpp,h}", "exclude_files": "react/renderer/mapbuffer/tests", "public_header_files": "react/renderer/mapbuffer/*.h", "header_dir": "react/renderer/mapbuffer", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"$(PODS_TARGET_SRCROOT)\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20"}, "dependencies": {"React-debug": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}