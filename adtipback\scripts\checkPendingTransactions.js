const dbQuery = require("../dbConfig/queryRunner");
const moment = require("moment");
const axios = require("axios");

const checkPendingTransactions = async () => {
  try {
    const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
    console.log(`Checking for pending transactions at ${currentTime}...`);

    // Step 1: Check if there are any transactions to process
    const countQuery = `
      SELECT COUNT(*) as count
      FROM transactions
      WHERE is_funds_added = 0 
        AND status = 'success' 
        AND is_active = 1 
        AND transaction_for IN ('addfunds', 'upgrade_premium', 'content_creator_premium')
    `;
    const countResult = await dbQuery.queryRunner(countQuery);

    const transactionCount = countResult[0]?.count || 0;
    if (transactionCount === 0) {
      console.log("No pending transactions found with is_funds_added = 0 and transaction_for in ('addfunds', 'upgrade_premium', 'content_creator_premium').");
      return;
    }

    console.log(`Found ${transactionCount} pending transaction(s) to process.`);

    // Step 2: Fetch and process transactions
    const pendingTransactionsQuery = `
      SELECT id, user_id, transaction_for, total, currency, order_id, payment_id, additional_info
      FROM transactions
      WHERE is_funds_added = 0 
        AND status = 'success' 
        AND is_active = 1 
        AND transaction_for IN ('addfunds', 'upgrade_premium', 'content_creator_premium')
    `;
    const pendingTransactions = await dbQuery.queryRunner(pendingTransactionsQuery);

    for (const transaction of pendingTransactions) {
      const { id, user_id, transaction_for, total, order_id, payment_id, additional_info } = transaction;

      try {
        if (transaction_for === "addfunds") {
          // Call /addfunds API
          const response = await axios.post(
            `http://localhost:${process.env.SERVER_PORT}/api/addfunds`,
            {
              createdby: user_id,
              amount: total,
              transaction_type: "Deposite",
              transactionStatus: "1",
              order_id,
              payment_id,
              isCron: true,
            },
            {
              headers: { Authorization: `Bearer ${process.env.CRON_AUTH_TOKEN}` },
            }
          );

          if (response.data.status === 200) {
            await dbQuery.queryRunner(`
              UPDATE transactions
              SET is_funds_added = 1, updated_at = '${currentTime}'
              WHERE id = ${id}
            `);
            console.log(`Successfully processed addfunds for transaction ID: ${id}`);
          } else {
            console.error(`Failed to process addfunds for transaction ID: ${id}`, response.data);
          }
        } else if (transaction_for === "upgrade_premium") {
          const { plan_id } = JSON.parse(additional_info || "{}");

          if (!plan_id) {
            console.error(`No plan_id found for upgrade_premium transaction ID: ${id}`);
            continue;
          }

          const response = await axios.post(
            `http://localhost:${process.env.SERVER_PORT}/api/upgrade-premium`,
            {
              payment_status: "success",
              user_id,
              plan_id,
              order_id,
              payment_id,
              isCron: true,
            },
            {
              headers: { Authorization: `Bearer ${process.env.CRON_AUTH_TOKEN}` },
            }
          );

          if (response.data.status === true) {
            await dbQuery.queryRunner(`
              UPDATE transactions
              SET is_funds_added = 1, updated_at = '${currentTime}'
              WHERE id = ${id}
            `);
            console.log(`Successfully processed upgrade_premium for transaction ID: ${id}`);
          } else {
            console.error(`Failed to process upgrade_premium for transaction ID: ${id}`, response.data);
          }
        } else if (transaction_for === "content_creator_premium") {
          const { plan_id } = JSON.parse(additional_info || "{}");

          if (!plan_id) {
            console.error(`No plan_id found for content_creator_premium transaction ID: ${id}`);
            continue;
          }

          const response = await axios.post(
            `http://localhost:${process.env.SERVER_PORT}/api/upgrade-content-premium`,
            {
              payment_status: "success",
              user_id,
              plan_id,
              order_id,
              payment_id,
              isCron: true,
            },
            {
              headers: { Authorization: `Bearer ${process.env.CRON_AUTH_TOKEN}` },
            }
          );

          if (response.data.status === true) {
            await dbQuery.queryRunner(`
              UPDATE transactions
              SET is_funds_added = 1, updated_at = '${currentTime}'
              WHERE id = ${id}
            `);
            console.log(`Successfully processed content_creator_premium for transaction ID: ${id}`);
          } else {
            console.error(`Failed to process content_creator_premium for transaction ID: ${id}`, response.data);
          }
        }
      } catch (error) {
        console.error(`Error processing transaction ID: ${id}`, error.message);
      }
    }

    console.log("Pending transactions check completed successfully");
  } catch (error) {
    console.error("Error in checkPendingTransactions:", error);
  }
};

module.exports = { checkPendingTransactions };