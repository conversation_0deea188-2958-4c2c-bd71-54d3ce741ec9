rbx.platform.typedef.__haiku_addr_t = ulong
rbx.platform.typedef.__haiku_generic_addr_t = ulong
rbx.platform.typedef.__haiku_int16 = short
rbx.platform.typedef.__haiku_int32 = int
rbx.platform.typedef.__haiku_int64 = long
rbx.platform.typedef.__haiku_int8 = char
rbx.platform.typedef.__haiku_phys_addr_t = ulong
rbx.platform.typedef.__haiku_phys_saddr_t = long
rbx.platform.typedef.__haiku_saddr_t = long
rbx.platform.typedef.__haiku_std_int16 = short
rbx.platform.typedef.__haiku_std_int32 = int
rbx.platform.typedef.__haiku_std_int64 = long
rbx.platform.typedef.__haiku_std_int8 = char
rbx.platform.typedef.__haiku_std_uint16 = ushort
rbx.platform.typedef.__haiku_std_uint32 = uint
rbx.platform.typedef.__haiku_std_uint64 = ulong
rbx.platform.typedef.__haiku_std_uint8 = uchar
rbx.platform.typedef.__haiku_uint16 = ushort
rbx.platform.typedef.__haiku_uint32 = uint
rbx.platform.typedef.__haiku_uint64 = ulong
rbx.platform.typedef.__haiku_uint8 = uchar
rbx.platform.typedef.addr_t = ulong
rbx.platform.typedef.bigtime_t = long
rbx.platform.typedef.blkcnt_t = long
rbx.platform.typedef.blksize_t = int
rbx.platform.typedef.caddr_t = pointer
rbx.platform.typedef.clock_t = int
rbx.platform.typedef.clockid_t = int
rbx.platform.typedef.cnt_t = int
rbx.platform.typedef.daddr_t = long
rbx.platform.typedef.dev_t = int
rbx.platform.typedef.fd_mask = uint
rbx.platform.typedef.fsblkcnt_t = long
rbx.platform.typedef.fsfilcnt_t = long
rbx.platform.typedef.generic_addr_t = ulong
rbx.platform.typedef.generic_size_t = ulong
rbx.platform.typedef.gid_t = uint
rbx.platform.typedef.id_t = int
rbx.platform.typedef.in_addr_t = uint
rbx.platform.typedef.in_port_t = ushort
rbx.platform.typedef.ino_t = long
rbx.platform.typedef.int16 = short
rbx.platform.typedef.int16_t = short
rbx.platform.typedef.int32 = int
rbx.platform.typedef.int32_t = int
rbx.platform.typedef.int64 = long
rbx.platform.typedef.int64_t = long
rbx.platform.typedef.int8 = char
rbx.platform.typedef.int8_t = char
rbx.platform.typedef.int_fast16_t = int
rbx.platform.typedef.int_fast32_t = int
rbx.platform.typedef.int_fast64_t = long
rbx.platform.typedef.int_fast8_t = int
rbx.platform.typedef.int_least16_t = short
rbx.platform.typedef.int_least32_t = int
rbx.platform.typedef.int_least64_t = long
rbx.platform.typedef.int_least8_t = char
rbx.platform.typedef.intmax_t = long
rbx.platform.typedef.intptr_t = long
rbx.platform.typedef.key_t = int
rbx.platform.typedef.mode_t = uint
rbx.platform.typedef.nanotime_t = long
rbx.platform.typedef.nlink_t = int
rbx.platform.typedef.off_t = long
rbx.platform.typedef.perform_code = uint
rbx.platform.typedef.phys_addr_t = ulong
rbx.platform.typedef.phys_size_t = ulong
rbx.platform.typedef.pid_t = int
rbx.platform.typedef.pthread_key_t = int
rbx.platform.typedef.ptrdiff_t = long
rbx.platform.typedef.rlim_t = ulong
rbx.platform.typedef.sa_family_t = uchar
rbx.platform.typedef.sig_atomic_t = int
rbx.platform.typedef.sigset_t = ulong
rbx.platform.typedef.size_t = ulong
rbx.platform.typedef.socklen_t = uint
rbx.platform.typedef.status_t = int
rbx.platform.typedef.suseconds_t = int
rbx.platform.typedef.time_t = long
rbx.platform.typedef.type_code = uint
rbx.platform.typedef.u_char = uchar
rbx.platform.typedef.u_int = uint
rbx.platform.typedef.u_int16_t = ushort
rbx.platform.typedef.u_int32_t = uint
rbx.platform.typedef.u_int64_t = ulong
rbx.platform.typedef.u_int8_t = uchar
rbx.platform.typedef.u_long = ulong
rbx.platform.typedef.u_short = ushort
rbx.platform.typedef.uchar = uchar
rbx.platform.typedef.uid_t = uint
rbx.platform.typedef.uint = uint
rbx.platform.typedef.uint16 = ushort
rbx.platform.typedef.uint16_t = ushort
rbx.platform.typedef.uint32 = uint
rbx.platform.typedef.uint32_t = uint
rbx.platform.typedef.uint64 = ulong
rbx.platform.typedef.uint64_t = ulong
rbx.platform.typedef.uint8 = uchar
rbx.platform.typedef.uint8_t = uchar
rbx.platform.typedef.uint_fast16_t = uint
rbx.platform.typedef.uint_fast32_t = uint
rbx.platform.typedef.uint_fast64_t = ulong
rbx.platform.typedef.uint_fast8_t = uint
rbx.platform.typedef.uint_least16_t = ushort
rbx.platform.typedef.uint_least32_t = uint
rbx.platform.typedef.uint_least64_t = ulong
rbx.platform.typedef.uint_least8_t = uchar
rbx.platform.typedef.uintmax_t = ulong
rbx.platform.typedef.uintptr_t = ulong
rbx.platform.typedef.ulong = ulong
rbx.platform.typedef.umode_t = uint
rbx.platform.typedef.unchar = uchar
rbx.platform.typedef.unichar = ushort
rbx.platform.typedef.useconds_t = uint
rbx.platform.typedef.ushort = ushort
rbx.platform.typedef.void*) = pointer
rbx.platform.typedef.wchar_t = int
