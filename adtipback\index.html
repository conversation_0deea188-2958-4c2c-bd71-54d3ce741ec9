<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Razorpay Payment</title>
</head>
<body>
    <button id="pay-btn">Pay ₹200</button>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        document.getElementById('pay-btn').onclick = async function (e) {
            // Call your backend to create an order
            const response = await fetch('http://localhost:7082/api/razorpay-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.CP2hoyHw7dOjB8A6uIifbdfNsztf0Pt1BSw8pEdM92Q'
                },
                body: JSON.stringify({
                    amount: 200,
                    currency: 'INR',
                    user_id: 50816
                })
            });
            const orderData = await response.json();

            if (!orderData.status) {
                alert('Order creation failed: ' + orderData.message);
                return;
            }

            // Initialize Razorpay checkout
            const options = {
                key: 'rzp_test_ojNkCSTYuUL3w9', // Replace with your Razorpay Key ID
                amount: orderData.data.amount, // Amount in paise (e.g., 20000 for ₹200)
                currency: 'INR',
                name: 'Your Business Name',
                description: 'Test Payment',
                order_id: orderData.data.id, // Razorpay order ID from backend
                handler: function (response) {
                    // This is where you get razorpay_signature
                    console.log('Payment Successful:', response);
                    alert('Payment ID: ' + response.razorpay_payment_id +
                          '\nOrder ID: ' + response.razorpay_order_id +
                          '\nSignature: ' + response.razorpay_signature);

                    // Send payment details to your verification endpoint
                    fetch('http://localhost:7082/api/razorpay-verification', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.CP2hoyHw7dOjB8A6uIifbdfNsztf0Pt1BSw8pEdM92Q' // Replace with your auth token
                        },
                        body: JSON.stringify({
                            order_id: response.razorpay_order_id,
                            razorpay_payment_id: response.razorpay_payment_id,
                            razorpay_signature: response.razorpay_signature,
                            amount: orderData.data.amount,
                            currency: 'INR',
                            user_id: 50816,
                            payment_status: 'success'
                        })
                    })
                    .then(res => res.json())
                    .then(data => {
                        console.log('Verification Response:', data);
                        alert('Verification: ' + data.message);
                    })
                    .catch(err => console.error('Verification Error:', err));
                },
                prefill: {
                    name: 'Test User',
                    email: '<EMAIL>',
                    contact: '9999999999'
                },
                theme: {
                    color: '#F37254'
                }
            };

            const rzp = new Razorpay(options);
            rzp.open();
        };
    </script>
</body>
</html>