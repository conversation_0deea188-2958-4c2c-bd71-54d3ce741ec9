// Optimized API Service with advanced caching and request deduplication
// Based on React Native patterns with web-specific optimizations

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { logDebug, logError, logWarn, logApi } from '../utils/ProductionLogger';

interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  expiresAt: number;
  etag?: string;
}

interface RequestConfig extends AxiosRequestConfig {
  cache?: {
    ttl?: number; // Time to live in milliseconds
    key?: string; // Custom cache key
    strategy?: 'cache-first' | 'network-first' | 'cache-only' | 'network-only';
  };
  retry?: {
    attempts?: number;
    delay?: number;
    exponentialBackoff?: boolean;
  };
  dedupe?: boolean; // Enable request deduplication
}

class OptimizedApiService {
  private axiosInstance: AxiosInstance;
  private cache = new Map<string, CacheEntry>();
  private pendingRequests = new Map<string, Promise<any>>();
  private readonly defaultCacheTTL = 5 * 60 * 1000; // 5 minutes
  private readonly maxCacheSize = 100;

  constructor(baseURL: string) {
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.setupCacheCleanup();
  }

  private setupInterceptors() {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = localStorage.getItem('UserLoggedIn');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request ID for tracking
        const requestId = Math.random().toString(36).substring(2, 15);
        config.metadata = { requestId, startTime: Date.now() };

        logApi('Request', config.method?.toUpperCase() || 'GET', config.url || '', {
          requestId,
          headers: config.headers,
          data: config.data,
        });

        return config;
      },
      (error) => {
        logError('OptimizedApiService', 'Request interceptor error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        const duration = Date.now() - (response.config.metadata?.startTime || 0);
        
        logApi('Response', response.status.toString(), response.config.url || '', {
          requestId: response.config.metadata?.requestId,
          duration,
          size: JSON.stringify(response.data).length,
        });

        return response;
      },
      (error) => {
        const duration = Date.now() - (error.config?.metadata?.startTime || 0);
        
        logError('OptimizedApiService', 'Response error', error, {
          requestId: error.config?.metadata?.requestId,
          duration,
          status: error.response?.status,
          url: error.config?.url,
        });

        return Promise.reject(error);
      }
    );
  }

  private setupCacheCleanup() {
    // Clean expired cache entries every 5 minutes
    setInterval(() => {
      const now = Date.now();
      const expiredKeys: string[] = [];

      this.cache.forEach((entry, key) => {
        if (entry.expiresAt < now) {
          expiredKeys.push(key);
        }
      });

      expiredKeys.forEach(key => this.cache.delete(key));

      if (expiredKeys.length > 0) {
        logDebug('OptimizedApiService', 'Cleaned expired cache entries', {
          count: expiredKeys.length,
          remainingEntries: this.cache.size,
        });
      }
    }, 5 * 60 * 1000);
  }

  private generateCacheKey(config: RequestConfig): string {
    if (config.cache?.key) {
      return config.cache.key;
    }

    const { method = 'GET', url = '', params, data } = config;
    const paramsStr = params ? JSON.stringify(params) : '';
    const dataStr = data ? JSON.stringify(data) : '';
    
    return `${method}:${url}:${paramsStr}:${dataStr}`;
  }

  private getCachedData<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (entry.expiresAt < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    logDebug('OptimizedApiService', 'Cache hit', { key });
    return entry.data;
  }

  private setCachedData<T>(key: string, data: T, ttl: number, etag?: string): void {
    // Implement LRU eviction if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
      etag,
    };

    this.cache.set(key, entry);
    logDebug('OptimizedApiService', 'Data cached', { key, ttl, size: this.cache.size });
  }

  private async executeRequest<T>(config: RequestConfig): Promise<AxiosResponse<T>> {
    const cacheKey = this.generateCacheKey(config);
    const cacheStrategy = config.cache?.strategy || 'cache-first';
    const cacheTTL = config.cache?.ttl || this.defaultCacheTTL;

    // Handle different cache strategies
    switch (cacheStrategy) {
      case 'cache-only': {
        const cachedData = this.getCachedData<T>(cacheKey);
        if (cachedData) {
          return { data: cachedData } as AxiosResponse<T>;
        }
        throw new Error('No cached data available');
      }

      case 'cache-first': {
        const cached = this.getCachedData<T>(cacheKey);
        if (cached) {
          return { data: cached } as AxiosResponse<T>;
        }
        // Fall through to network request
        break;
      }

      case 'network-first':
        try {
          const response = await this.axiosInstance.request<T>(config);
          this.setCachedData(cacheKey, response.data, cacheTTL, response.headers.etag);
          return response;
        } catch (error) {
          // Fall back to cache on network error
          const fallbackData = this.getCachedData<T>(cacheKey);
          if (fallbackData) {
            logWarn('OptimizedApiService', 'Network failed, using cached data', { cacheKey });
            return { data: fallbackData } as AxiosResponse<T>;
          }
          throw error;
        }

      case 'network-only':
        return this.axiosInstance.request<T>(config);
    }

    // Default: network request with caching
    const response = await this.axiosInstance.request<T>(config);
    this.setCachedData(cacheKey, response.data, cacheTTL, response.headers.etag);
    return response;
  }

  private async retryRequest<T>(
    config: RequestConfig,
    attempt: number = 1
  ): Promise<AxiosResponse<T>> {
    const maxAttempts = config.retry?.attempts || 3;
    const baseDelay = config.retry?.delay || 1000;
    const useExponentialBackoff = config.retry?.exponentialBackoff !== false;

    try {
      return await this.executeRequest<T>(config);
    } catch (error) {
      if (attempt >= maxAttempts) {
        throw error;
      }

      const delay = useExponentialBackoff 
        ? baseDelay * Math.pow(2, attempt - 1)
        : baseDelay;

      logWarn('OptimizedApiService', 'Request failed, retrying', {
        attempt,
        maxAttempts,
        delay,
        error: (error as Error).message,
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      return this.retryRequest<T>(config, attempt + 1);
    }
  }

  async request<T = any>(config: RequestConfig): Promise<AxiosResponse<T>> {
    const cacheKey = this.generateCacheKey(config);

    // Handle request deduplication
    if (config.dedupe !== false && this.pendingRequests.has(cacheKey)) {
      logDebug('OptimizedApiService', 'Request deduplicated', { cacheKey });
      return this.pendingRequests.get(cacheKey);
    }

    const requestPromise = this.retryRequest<T>(config);

    // Store pending request for deduplication
    if (config.dedupe !== false) {
      this.pendingRequests.set(cacheKey, requestPromise);
      
      // Clean up pending request when done
      requestPromise.finally(() => {
        this.pendingRequests.delete(cacheKey);
      });
    }

    return requestPromise;
  }

  // Convenience methods
  async get<T = any>(url: string, config?: RequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  async delete<T = any>(url: string, config?: RequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  // Cache management methods
  clearCache(): void {
    this.cache.clear();
    logDebug('OptimizedApiService', 'Cache cleared');
  }

  invalidateCache(pattern?: string): void {
    if (!pattern) {
      this.clearCache();
      return;
    }

    const keysToDelete: string[] = [];
    this.cache.forEach((_, key) => {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));
    logDebug('OptimizedApiService', 'Cache invalidated', { pattern, count: keysToDelete.length });
  }

  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      entries: Array.from(this.cache.entries()).map(([key, entry]) => ({
        key,
        timestamp: entry.timestamp,
        expiresAt: entry.expiresAt,
        size: JSON.stringify(entry.data).length,
      })),
    };
  }
}

// Create singleton instance
export const optimizedApiService = new OptimizedApiService(
  import.meta.env.VITE_API_URL || 'http://localhost:3000'
);

export default optimizedApiService;
