# months
M(a)_1=जनवरी
M(a)_2=फेब्रुअरी
M(a)_3=मार्च
M(a)_4=अप्रिल
M(a)_5=मे
M(a)_6=जुन
M(a)_7=जुलाई
M(a)_8=अगस्ट
M(a)_9=सेप्टेम्बर
M(a)_10=अक्टोबर
M(a)_11=नोभेम्बर
M(a)_12=डिसेम्बर

M(n)_1=जन
M(n)_2=फेब
M(n)_3=मार्च
M(n)_4=अप्र
M(n)_5=मे
M(n)_6=जुन
M(n)_7=जुल
M(n)_8=अग
M(n)_9=सेप
M(n)_10=अक्टो
M(n)_11=नोभे
M(n)_12=डिसे

M(w)_1=जनवरी
M(w)_2=फेब्रुअरी
M(w)_3=मार्च
M(w)_4=अप्रिल
M(w)_5=मे
M(w)_6=जुन
M(w)_7=जुलाई
M(w)_8=अगस्ट
M(w)_9=सेप्टेम्बर
M(w)_10=अक्टोबर
M(w)_11=नोभेम्बर
M(w)_12=डिसेम्बर

M(A)_1=जनवरी
M(A)_2=फेब्रुअरी
M(A)_3=मार्च
M(A)_4=अप्रिल
M(A)_5=मे
M(A)_6=जुन
M(A)_7=जुलाई
M(A)_8=अगस्ट
M(A)_9=सेप्टेम्बर
M(A)_10=अक्टोबर
M(A)_11=नोभेम्बर
M(A)_12=डिसेम्बर

M(N)_1=जन
M(N)_2=फेेब
M(N)_3=मार्च
M(N)_4=अप्र
M(N)_5=मे
M(N)_6=जुन
M(N)_7=जुल
M(N)_8=अग
M(N)_9=सेप
M(N)_10=अक्टो
M(N)_11=नोभे
M(N)_12=डिसे

M(W)_1=जनवरी
M(W)_2=फेब्रुअरी
M(W)_3=मार्च
M(W)_4=अप्रिल
M(W)_5=मे
M(W)_6=जुन
M(W)_7=जुलाई
M(W)_8=अगस्ट
M(W)_9=सेप्टेम्बर
M(W)_10=अक्टोबर
M(W)_11=नोभेम्बर
M(W)_12=डिसेम्बर

# weekdays
D(a)_1=सोम
D(a)_2=मङ्गल
D(a)_3=बुध
D(a)_4=बिहि
D(a)_5=शुक्र
D(a)_6=शनि
D(a)_7=आइत

D(n)_1=सो
D(n)_2=म
D(n)_3=बु
D(n)_4=बि
D(n)_5=शु
D(n)_6=श
D(n)_7=आ

D(s)_1=सोम
D(s)_2=मङ्गल
D(s)_3=बुध
D(s)_4=बिहि
D(s)_5=शुक्र
D(s)_6=शनि
D(s)_7=आइत

D(w)_1=सोमबार
D(w)_2=मङ्गलबार
D(w)_3=बुधबार
D(w)_4=बिहिबार
D(w)_5=शुक्रबार
D(w)_6=शनिबार
D(w)_7=आइतबार

D(A)_1=सोम
D(A)_2=मङ्गल
D(A)_3=बुध
D(A)_4=बिहि
D(A)_5=शुक्र
D(A)_6=शनि
D(A)_7=आइत

D(N)_1=सो
D(N)_2=म
D(N)_3=बु
D(N)_4=बि
D(N)_5=शु
D(N)_6=श
D(N)_7=आ

D(S)_1=सोम
D(S)_2=मङ्गल
D(S)_3=बुध
D(S)_4=बिहि
D(S)_5=शुक्र
D(S)_6=शनि
D(S)_7=आइत

D(W)_1=सोमबार
D(W)_2=मङ्गलबार
D(W)_3=बुधबार
D(W)_4=बिहिबार
D(W)_5=शुक्रबार
D(W)_6=शनिबार
D(W)_7=आइतबार

# quarters
Q(a)_1=पहिलो सत्र
Q(a)_2=दोस्रो सत्र
Q(a)_3=तेस्रो सत्र
Q(a)_4=चौथो सत्र

Q(n)_1=१
Q(n)_2=२
Q(n)_3=३
Q(n)_4=४

Q(w)_1=पहिलो सत्र
Q(w)_2=दोस्रो सत्र
Q(w)_3=तेस्रो सत्र
Q(w)_4=चौथो सत्र

Q(A)_1=पहिलो सत्र
Q(A)_2=दोस्रो सत्र
Q(A)_3=तेस्रो सत्र
Q(A)_4=चौथो सत्र

Q(N)_1=१
Q(N)_2=२
Q(N)_3=३
Q(N)_4=४

Q(W)_1=पहिलो सत्र
Q(W)_2=दोस्रो सत्र
Q(W)_3=तेस्रो सत्र
Q(W)_4=चौथो सत्र

# day-period-rules
T0400=morning1
T1200=afternoon1
T1600=afternoon2
T1900=evening1
T2200=night1

# day-period-translations
P(a)_midnight=मध्यरात
P(a)_am=पूर्वाह्न
P(a)_noon=मध्यान्ह
P(a)_pm=अपराह्न
P(a)_morning1=बिहान
P(a)_afternoon1=अपरान्ह
P(a)_afternoon2=साँझ
P(a)_evening1=बेलुकी
P(a)_night1=रात

P(n)_midnight=मध्यरात
P(n)_am=पूर्वाह्न
P(n)_noon=मध्यान्ह
P(n)_pm=अपराह्न
P(n)_morning1=बिहान
P(n)_afternoon1=अपरान्ह
P(n)_afternoon2=साँझ
P(n)_evening1=बेलुकी
P(n)_night1=रात

P(w)_midnight=मध्यरात
P(w)_am=पूर्वाह्न
P(w)_noon=मध्यान्ह
P(w)_pm=अपराह्न
P(w)_morning1=बिहान
P(w)_afternoon1=अपरान्ह
P(w)_afternoon2=साँझ
P(w)_evening1=बेलुकी
P(w)_night1=रात

P(A)_midnight=मध्यरात
P(A)_am=पूर्वाह्न
P(A)_noon=मध्यान्ह
P(A)_pm=अपराह्न
P(A)_morning1=बिहान
P(A)_afternoon1=अपरान्ह
P(A)_afternoon2=साँझ
P(A)_evening1=बेलुकी
P(A)_night1=रात

P(N)_midnight=मध्यरात
P(N)_am=पूर्वाह्न
P(N)_noon=मध्यान्ह
P(N)_pm=अपराह्न
P(N)_morning1=बिहान
P(N)_afternoon1=अपरान्ह
P(N)_afternoon2=साँझ
P(N)_evening1=बेलुकी
P(N)_night1=रात

P(W)_midnight=मध्यरात
P(W)_am=पूर्वाह्न
P(W)_noon=मध्यान्ह
P(W)_pm=अपराह्न
P(W)_morning1=बिहान
P(W)_afternoon1=अपरान्ह
P(W)_afternoon2=साँझ
P(W)_evening1=बेलुकी
P(W)_night1=रात

# eras
E(w)_0=ईसा पूर्व
E(w|alt)_0=इस्वीपूर्व
E(w)_1=सन्
E(w|alt)_1=ईसा काल

E(a)_0=ईसा पूर्व
E(a|alt)_0=इस्वीपूर्व
E(a)_1=सन्
E(a|alt)_1=ईसा काल

# format patterns
F(f)_d=y MMMM d, EEEE
F(l)_d=y MMMM d
F(m)_d=y MMM d
F(s)_d=yy/M/d

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=MM-dd
F_MMMd=MMM d
F_MMMMd=MMMM d
F_y=y
F_yM=y-MM
F_yMMM=y MMM
F_yMMMM=y MMMM
F_yQQQ=y QQQ
F_yQQQQ=y QQQQ
F_yw=Y को w हप्ता

I={0} – {1}

# labels of elements
L_era=काल
L_year=वर्ष
L_quarter=सत्र
L_month=महिना
L_week=हप्ता
L_day=बार
L_weekday=हप्ताको बार
L_dayperiod=पूर्वाह्न / अपराह्न
L_hour=घण्टा
L_minute=मिनेट
L_second=सेकेन्ड
L_zone=क्षेत्र
