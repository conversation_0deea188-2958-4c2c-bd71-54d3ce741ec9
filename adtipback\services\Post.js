const dbQuery = require("../dbConfig/queryRunner");
const moment = require("moment");
const { logError } = require("../dbConfig/errorLog");

const insertDeletePostTargets = async (
  postId,
  user_id,
  post_target_locations,
  post_target_genders
) => {
  try {
    console.log(`Updating post targets for Post ID: ${postId}`);

    // Step 1: Delete Existing Entries
    // await dbQuery.queryRunner(
    //   `DELETE FROM post_target_locations WHERE post_id = ${postId};`
    // );
    await dbQuery.queryRunner(
      `DELETE FROM post_target_gender WHERE post_id = ${postId};`
    );
    console.log("✅ Deleted existing post targets");

    // Step 2: Insert New Post Target Locations (if provided)
    // if (
    //   Array.isArray(post_target_locations) &&
    //   post_target_locations.length > 0
    // ) {
    //   const locationValues = post_target_locations
    //     .map((loc) => `(${postId}, ${loc.id},${user_id})`)
    //     .join(", ");
    //   const locationQuery = `INSERT INTO post_target_locations (post_id, location_id,user_id) VALUES ${locationValues};`;
    //   await dbQuery.queryRunner(locationQuery);
    //   console.log("✅ Inserted post target locations:", locationValues);
    // }

    // Step 3: Insert New Post Target Gender (if provided)
    if (Array.isArray(post_target_genders) && post_target_genders.length > 0) {
      for (const gender of post_target_genders) {
        // Handle both object format {id: 1} and direct ID format
        const genderId = typeof gender === 'object' ? gender.id : gender;

        if (!genderId || isNaN(parseInt(genderId))) {
          console.error("❌ Invalid gender_id:", gender);
          throw new Error(`Invalid gender_id: ${gender}`);
        }

        // Validate that gender_id exists in gender table
        const genderExistsQuery = `SELECT gender_id FROM gender WHERE gender_id = ? AND is_active = 1`;
        const genderExists = await dbQuery.queryRunner(genderExistsQuery, [parseInt(genderId)]);

        if (!genderExists || genderExists.length === 0) {
          console.error("❌ Gender ID not found:", genderId);
          throw new Error(`Gender ID ${genderId} does not exist or is inactive`);
        }

        const genderQuery = `INSERT INTO post_target_gender (post_id, gender_id, user_id) VALUES (?, ?, ?)`;
        const genderParams = [postId, parseInt(genderId), user_id];
        await dbQuery.queryRunner(genderQuery, genderParams);
      }
      console.log("✅ Inserted post target genders for post:", postId);
    }

    console.log("🎉 Post target locations & gender updated successfully");
  } catch (error) {
    console.error("❌ Error in insertDeletePostTargets:", error);
    throw new Error(`Failed to update post targets: ${error.message}`);
  }
};

exports.createPost = async (data) => {
  try {
    // Get current time and add 5 hours 30 minute
    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    const query = `
      INSERT INTO posts (user_id, title, content, media_url, media_type, is_promoted, created_at, video_category_id, updated_at, start_date, end_date, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)`;

    const params = [
      data.user_id,
      data.title,
      data.content || null,
      data.mediaUrl || null,
      data.mediaType,
      data.isPromoted,
      updatedTime,
      data.video_category_id || null,
      updatedTime,
      data.start_date || null,
      data.end_date || null
    ];

    const result = await dbQuery.queryRunner(query, params);
    return {
      status: true,
      statusCode: 201,
      message: "Post created",
      data: {
        post_id: result.insertId,
        user_id: data.user_id,
        title: data.title,
        is_promoted: data.isPromoted,
      },
    };
  } catch (error) {
    console.log("error", error);
    await logError(error, "normal_post", data);
    return {
      status: false,
      statusCode: 500,
      message: "Post creation error",
    };
  }
};

// Create Post Promotion
exports.createPostPromotion = async (data) => {
  try {
    // Get current time and add 5 hours 30 minute
    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    // Step 1: Insert Post
    const postQuery = `
      INSERT INTO posts (user_id, title, content, media_url, media_type, is_promoted, video_category_id, created_at, updated_at, start_date, end_date, target_locations, is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)`;

    const postParams = [
      data.user_id,
      data.title,
      data.content,
      data.mediaUrl,
      data.mediaType,
      data.isPromoted,
      data.video_category_id,
      updatedTime,
      updatedTime,
      data.start_date,
      data.end_date,
      JSON.stringify(data.post_target_locations)
    ];

    const postResult = await dbQuery.queryRunner(postQuery, postParams);
    console.log("Post Created:", postResult);

    const postId = postResult.insertId;
    if (!postId) throw new Error("Post creation failed.");

    // Step 2: Insert Post Promotion
    const postPromotionQuery = `
      INSERT INTO post_promotions (post_id, target_min_age, target_max_age, pay_per_view, reach_goal, duration_days, total_pay, platform_fee, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

    const promoParams = [
      postId,
      data.target_min_age,
      data.target_max_age,
      data.pay_per_view,
      data.reach_goal,
      data.duration_days,
      data.total_pay,
      data.platform_fee,
      updatedTime,
      updatedTime
    ];

    const promoResult = await dbQuery.queryRunner(postPromotionQuery, promoParams);
    console.log("Post Promotion Created:", promoResult);

    const addLocationsAndGender = await insertDeletePostTargets(
      postId,
      data.user_id,
      data.post_target_locations,
      data.post_target_genders
    );

    return {
      status: true,
      statusCode: 201,
      message: "Post created with promotion",
      data: {
        post_id: postId,
        user_id: data.user_id,
        title: data.title,
        is_promoted: data.isPromoted,
      },
    };
  } catch (error) {
    console.error("❌ Error in createPostPromotion:", error);
    await logError(error, "post_promotion", data);
    return {
      status: false,
      statusCode: 500,
      message: "Post creation error with promotion",
      error: error.message,
    };
  }
};

exports.listPosts = async (filters, page = 1, limit) => {
  try {
    const offset = (page - 1) * limit;

    // Validate user_id
    if (filters.userId) {
      const userExists = await dbQuery.queryRunner(
        `SELECT 1 FROM users WHERE id = ${filters.userId} LIMIT 1`
      );
      if (!userExists || userExists.length === 0) {
        return { status: 404, error: "User not found" };
      }
    }

    // Fetch specific post details
    if (filters.post_id > 0) {
      const postQuery = `
      WITH unique_genders AS (
        SELECT DISTINCT tg.gender_id, g.gender_name
        FROM post_target_gender tg
        JOIN gender g ON tg.gender_id = g.gender_id
        WHERE tg.post_id = ${filters.post_id}
      )
      SELECT 
          p.id, p.user_id, p.title, p.content, p.media_url, p.media_type, 
          p.is_promoted, p.video_category_id, u.name AS user_name, 
          u.profile_image AS user_profile_image, u.address, 
          COALESCE(u.premium_plan_id, 0) AS premium_plan_id,
          pp.id AS post_promotion_id, pp.target_min_age, pp.target_max_age, 
          pp.reach_goal, pp.duration_days, 
          FORMAT(pp.pay_per_view, 2) AS pay_per_view, 
          FORMAT(pp.total_pay, 2) AS total_pay, 
          FORMAT(pp.platform_fee, 2) AS platform_fee, 
          DATE_FORMAT(p.created_at, '%Y-%m-%d %H:%i:%s') AS created_at,
          DATE_FORMAT(p.updated_at, '%Y-%m-%d %H:%i:%s') AS updated_at,
          DATE_FORMAT(p.start_date, '%Y-%m-%d %H:%i:%s') AS promotion_start_date,
          DATE_FORMAT(p.end_date, '%Y-%m-%d %H:%i:%s') AS promotion_end_date,
          COALESCE(
              (SELECT JSON_ARRAYAGG(JSON_OBJECT('id', ug.gender_id, 'name', ug.        gender_name)) 
               FROM unique_genders ug), '[]') AS post_target_genders,
          COALESCE(p.target_locations, '[]') AS post_target_locations
      FROM posts p
      LEFT JOIN post_promotions pp ON p.id = pp.post_id
      LEFT JOIN users u ON p.user_id = u.id
      WHERE p.id = ${filters.post_id} AND p.is_active = 0
      GROUP BY p.id, pp.id, u.id;
      `;

      const postDetails = await dbQuery.queryRunner(postQuery);
      if (postDetails.length > 0) {
        postDetails.forEach((post) => {
          try {
            post.post_target_genders =
              JSON.parse(post.post_target_genders) || [];
          } catch {
            post.post_target_genders = [];
          }
          try {
            post.post_target_locations =
              JSON.parse(post.post_target_locations) || [];
          } catch {
            post.post_target_locations = [];
          }
        });
      }

      return {
        status: 200,
        message: postDetails.length > 0 ? "Post found" : "No post found",
        data: postDetails.length > 0 ? [postDetails[0]] : [],
      };
    }

    // Build filter query
    let filterQuery = "WHERE p.is_active = 0";
    if (filters.category)
      filterQuery += ` AND p.video_category_id = ${filters.category}`;
    if (filters.userId) filterQuery += ` AND p.user_id = ${filters.userId}`;
    if (filters.media_type)
      filterQuery += ` AND p.media_type = '${filters.media_type}'`;

    // Get total counts
    const totalCounts = await dbQuery.queryRunner(`
      SELECT 
        SUM(CASE WHEN p.is_promoted = 1 THEN 1 ELSE 0 END) AS totalPromoted,
        SUM(CASE WHEN p.is_promoted = 0 THEN 1 ELSE 0 END) AS totalNormal
      FROM posts p
      ${filterQuery}
    `);

    const totalPromoted = totalCounts[0]?.totalPromoted || 0;
    const totalNormal = totalCounts[0]?.totalNormal || 0;
    const totalPosts = totalPromoted + totalNormal;
    const totalPages = Math.ceil(totalPosts / limit);

    // Fetch posts efficiently
    const fetchPosts = async (isPromoted, offset, limit) => {
      return await dbQuery.queryRunner(`
              WITH LikeCounts AS (
            SELECT postId, COUNT(id) AS likeCount
            FROM user_post_likes
            GROUP BY postId
        ),
        CommentCounts AS (
            SELECT postId, COUNT(id) AS commentCount
            FROM user_post_comments
            GROUP BY postId
        ),
        UserLiked AS (
            SELECT postId, 1 AS is_liked
            FROM user_post_likes
            WHERE user_id = ${filters.loggined_user_id}
        )
        SELECT 
            p.id, p.user_id, p.title, p.content, p.media_url, p.media_type, 
            p.is_promoted, p.video_category_id, u.name AS user_name, 
            u.profile_image AS user_profile_image, u.address, vc.category_name, 
            COALESCE(u.premium_plan_id, 0) AS premium_plan_id,
            pp.id AS post_promotion_id, pp.target_min_age, pp.target_max_age, 
            pp.reach_goal, pp.duration_days, 
            FORMAT(pp.pay_per_view, 2) AS pay_per_view, 
            FORMAT(pp.total_pay, 2) AS total_pay, 
            FORMAT(pp.platform_fee, 2) AS platform_fee,
            DATE_FORMAT(p.created_at, '%Y-%m-%d %H:%i:%s') AS created_at,
            COALESCE(lc.likeCount, 0) AS likeCount,
            COALESCE(cc.commentCount, 0) AS commentCount,
            COALESCE(ul.is_liked, 0) AS is_liked
        FROM posts p
        LEFT JOIN post_promotions pp ON p.id = pp.post_id
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN video_categories vc ON p.video_category_id = vc.id
        LEFT JOIN LikeCounts lc ON p.id = lc.postId
        LEFT JOIN CommentCounts cc ON p.id = cc.postId
        LEFT JOIN UserLiked ul ON p.id = ul.postId
        ${filterQuery} AND p.is_promoted = ${isPromoted}
        ORDER BY ${isPromoted ? "pp.created_at DESC" : `
          CASE
            WHEN p.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1
            WHEN p.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 2
            ELSE 3
          END,
          RAND(UNIX_TIMESTAMP() + p.id),
          p.created_at DESC
        `}
        LIMIT ${limit} OFFSET ${offset};
      `);
    };

    // Fetch promoted and normal posts
    const [promotedPosts, normalPosts] = await Promise.all([
      fetchPosts(1, offset, limit),
      fetchPosts(0, offset, limit),
    ]);

    // Ensure `is_liked` is boolean
    promotedPosts.forEach((post) => (post.is_liked = !!post.is_liked));
    normalPosts.forEach((post) => (post.is_liked = !!post.is_liked));

    // Merge posts in alternating 5 Promoted → 5 Normal pattern
    let resultPosts = [];
    let pIndex = 0,
      nIndex = 0;
    const batchSize = 5;

    while (pIndex < promotedPosts.length || nIndex < normalPosts.length) {
      for (let i = 0; i < batchSize && pIndex < promotedPosts.length; i++) {
        resultPosts.push(promotedPosts[pIndex++]);
      }
      for (let i = 0; i < batchSize && nIndex < normalPosts.length; i++) {
        resultPosts.push(normalPosts[nIndex++]);
      }
    }

    return {
      status: 200,
      message: resultPosts.length === 0 ? "No posts found" : "Post lists",
      data: resultPosts,
      pagination: {
        current_page: page,
        total_page: totalPages,
        total_count: totalPosts,
      },
    };
  } catch (error) {
    console.error("Service Error:", error);
    return { error: "Failed to fetch posts" };
  }
};

exports.listPremiumPosts = async (filters, page = 1, limit) => {
  try {
    const offset = (page - 1) * limit;

    // Fetch specific post details
    if (filters.post_id > 0) {
      const postQuery = `
      WITH unique_genders AS (
        SELECT DISTINCT tg.gender_id, g.gender_name
        FROM post_target_gender tg
        JOIN gender g ON tg.gender_id = g.gender_id
        WHERE tg.post_id = ${filters.post_id}
      )
      SELECT 
          p.id, p.user_id, p.title, p.content, p.media_url, p.media_type, 
          p.is_promoted, p.video_category_id, u.name AS user_name, 
          u.profile_image AS user_profile_image, u.address, 
          COALESCE(u.premium_plan_id, 0) AS premium_plan_id,
          pp.id AS post_promotion_id, pp.target_min_age, pp.target_max_age, 
          pp.reach_goal, pp.duration_days, 
          FORMAT(pp.pay_per_view, 2) AS pay_per_view, 
          FORMAT(pp.total_pay, 2) AS total_pay, 
          FORMAT(pp.platform_fee, 2) AS platform_fee, 
          DATE_FORMAT(p.created_at, '%Y-%m-%d %H:%i:%s') AS created_at,
          DATE_FORMAT(p.updated_at, '%Y-%m-%d %H:%i:%s') AS updated_at,
          DATE_FORMAT(p.start_date, '%Y-%m-%d %H:%i:%s') AS promotion_start_date,
          DATE_FORMAT(p.end_date, '%Y-%m-%d %H:%i:%s') AS promotion_end_date,
          COALESCE(
              (SELECT JSON_ARRAYAGG(JSON_OBJECT('id', ug.gender_id, 'name', ug.gender_name)) 
               FROM unique_genders ug), '[]') AS post_target_genders,
          COALESCE(p.target_locations, '[]') AS post_target_locations
      FROM posts p
      LEFT JOIN post_promotions pp ON p.id = pp.post_id
      LEFT JOIN users u ON p.user_id = u.id
      WHERE p.id = ${filters.post_id} AND p.is_active = 0
      GROUP BY p.id, pp.id, u.id;
      `;

      const postDetails = await dbQuery.queryRunner(postQuery);
      if (postDetails.length > 0) {
        postDetails.forEach((post) => {
          try {
            post.post_target_genders =
              JSON.parse(post.post_target_genders) || [];
          } catch {
            post.post_target_genders = [];
          }
          try {
            post.post_target_locations =
              JSON.parse(post.post_target_locations) || [];
          } catch {
            post.post_target_locations = [];
          }
        });
      }

      return {
        status: 200,
        message: postDetails.length > 0 ? "Post found" : "No post found",
        data: postDetails.length > 0 ? [postDetails[0]] : [],
      };
    }

    // Build filter query
    let filterQuery = "WHERE p.is_active = 0";
    if (filters.category)
      filterQuery += ` AND p.video_category_id = ${filters.category}`;
    if (filters.media_type)
      filterQuery += ` AND p.media_type = '${filters.media_type}'`;

    // Get total counts
    const totalCounts = await dbQuery.queryRunner(`
      SELECT 
        SUM(CASE WHEN p.is_promoted = 1 THEN 1 ELSE 0 END) AS totalPromoted,
        SUM(CASE WHEN p.is_promoted = 0 THEN 1 ELSE 0 END) AS totalNormal
      FROM posts p
      ${filterQuery}
    `);

    const totalPromoted = totalCounts[0]?.totalPromoted || 0;
    const totalNormal = totalCounts[0]?.totalNormal || 0;
    const totalPosts = totalPromoted + totalNormal;
    const totalPages = Math.ceil(totalPosts / limit);

    // Fetch posts efficiently, prioritizing premium users and randomizing
    const fetchPosts = async (isPromoted, offset, limit) => {
      return await dbQuery.queryRunner(`
        WITH LikeCounts AS (
            SELECT postId, COUNT(id) AS likeCount
            FROM user_post_likes
            GROUP BY postId
        ),
        CommentCounts AS (
            SELECT postId, COUNT(id) AS commentCount
            FROM user_post_comments
            GROUP BY postId
        )
        SELECT 
            p.id, p.user_id, p.title, p.content, p.media_url, p.media_type, 
            p.is_promoted, p.video_category_id, u.name AS user_name, 
            u.profile_image AS user_profile_image, u.address, vc.category_name, 
            COALESCE(u.premium_plan_id, 0) AS premium_plan_id,
            pp.id AS post_promotion_id, pp.target_min_age, pp.target_max_age, 
            pp.reach_goal, pp.duration_days, 
            FORMAT(pp.pay_per_view, 2) AS pay_per_view, 
            FORMAT(pp.total_pay, 2) AS total_pay, 
            FORMAT(pp.platform_fee, 2) AS platform_fee,
            DATE_FORMAT(p.created_at, '%Y-%m-%d %H:%i:%s') AS created_at,
            COALESCE(lc.likeCount, 0) AS likeCount,
            COALESCE(cc.commentCount, 0) AS commentCount
        FROM posts p
        LEFT JOIN post_promotions pp ON p.id = pp.post_id
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN video_categories vc ON p.video_category_id = vc.id
        LEFT JOIN LikeCounts lc ON p.id = lc.postId
        LEFT JOIN CommentCounts cc ON p.id = cc.postId
        ${filterQuery} AND p.is_promoted = ${isPromoted}
        ORDER BY
          u.premium_plan_id DESC,
          CASE
            WHEN p.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1
            WHEN p.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 2
            ELSE 3
          END,
          RAND(UNIX_TIMESTAMP() + p.id + u.id),
          lc.likeCount DESC,
          p.created_at DESC
        LIMIT ${limit} OFFSET ${offset};
      `);
    };

    // Fetch promoted and normal posts
    const [promotedPosts, normalPosts] = await Promise.all([
      fetchPosts(1, offset, limit),
      fetchPosts(0, offset, limit),
    ]);

    // Merge posts in alternating 5 Promoted → 5 Normal pattern
    let resultPosts = [];
    let pIndex = 0,
      nIndex = 0;
    const batchSize = 5;

    while (pIndex < promotedPosts.length || nIndex < normalPosts.length) {
      for (let i = 0; i < batchSize && pIndex < promotedPosts.length; i++) {
        resultPosts.push(promotedPosts[pIndex++]);
      }
      for (let i = 0; i < batchSize && nIndex < normalPosts.length; i++) {
        resultPosts.push(normalPosts[nIndex++]);
      }
    }

    return {
      status: 200,
      message: resultPosts.length === 0 ? "No posts found" : "Premium post lists",
      data: resultPosts,
      pagination: {
        current_page: page,
        total_page: totalPages,
        total_count: totalPosts,
      },
    };
  } catch (error) {
    console.error("Service Error:", error);
    return { error: "Failed to fetch premium posts" };
  }
};

exports.saveUserPostLike = async (data) => {
  try {
    // Get current time and add 5 hours 30 minute
    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
    // Validate if the user exists
    const userCheckQuery = `SELECT id FROM users WHERE id = ${data.userId}`;
    const userExists = await dbQuery.queryRunner(userCheckQuery);

    if (!userExists || userExists.length === 0) {
      return { error: "User not found" };
    }

    // Validate if the post exists
    const postCheckQuery = `SELECT id FROM posts WHERE id = ${data.postId} LIMIT 1`;
    const postExists = await dbQuery.queryRunner(postCheckQuery);

    if (!postExists || postExists.length === 0) {
      return { error: "Post not found" };
    }

    const existing = await dbQuery.queryRunner(
      `SELECT id, is_liked FROM user_post_likes WHERE postId = ${data.postId} AND user_id = ${data.userId}`
    );

    if (existing && existing.length > 0) {
      // If the status hasn't changed, skip the update

      await dbQuery.queryRunner(
        `UPDATE user_post_likes SET is_liked = ${data.is_liked}, updated_at='${updatedTime}' WHERE id = ${existing[0].id}`
      );

      return {
        status: 200,
        message: data.is_liked ? "Liked" : "Disliked",
        is_liked: data.is_liked,
      };
    }

    // Insert new like entry
    await dbQuery.queryRunner(
      `INSERT INTO user_post_likes (postId, user_id, is_liked, created_at, updated_at) 
      VALUES (${data.postId}, ${data.userId}, ${data.is_liked}, '${updatedTime}', '${updatedTime}')`
    );

    return {
      status: 200,
      message: data.is_liked ? "Liked" : "Disliked",
      is_liked: data.is_liked,
    };
  } catch (error) {
    console.error("Service Error:", error);
    return { error: "Failed to save user post like" };
  }
};

exports.saveUserPostComment = async (data) => {
  try {
    // Get current time and add 5 hours 30 minute
    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    // Validate if the user exists
    const userCheckQuery = `SELECT id FROM users WHERE id = ?`;
    const userExists = await dbQuery.queryRunner(userCheckQuery, [data.userId]);

    if (!userExists || userExists.length === 0) {
      return { error: "User not found" };
    }

    const postCheckQuery = `SELECT id FROM posts WHERE id = ? LIMIT 1`;
    const postExists = await dbQuery.queryRunner(postCheckQuery, [data.postId]);

    if (!postExists || postExists.length === 0) {
      return { error: "Post not found" };
    }

    const insertCommentQuery = `
      INSERT INTO user_post_comments (postId, user_id, comment, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?)`;

    const commentParams = [
      data.postId,
      data.userId,
      data.comment,
      updatedTime,
      updatedTime
    ];

    await dbQuery.queryRunner(insertCommentQuery, commentParams);
    return {
      status: 200,
      message: "Comment added",
      data: {
        postId: data.postId,
        userId: data.userId,
        comment: data.comment,
      },
    };
  } catch (error) {
    console.error("Service Error:", error);
    return { error: "Failed to save user post like" };
  }
};

exports.getUserPosts = async ({
  userId,
  search_by_name,
  media_type,
  page = 1,
  limit = 20,
  loggined_user_id,
}) => {
  try {
    const offset = (page - 1) * limit;

    // Check if the user exists
    const userExistsQuery = `SELECT id FROM users WHERE id = ${userId} LIMIT 1`;
    const userExists = await dbQuery.queryRunner(userExistsQuery);

    if (!userExists || userExists.length === 0) {
      return { error: "User not found." };
    }

    // Build dynamic filters for posts query
    let filters = ` AND p.is_active = 0 AND p.user_id = ${userId}`;
    if (media_type) {
      filters += ` AND p.media_type = '${media_type}'`;
    }
    if (search_by_name) {
      filters += ` AND u.name LIKE '%${search_by_name}%'`; // Assuming you want to filter by name
    }

    // Get total count of posts based on the filters
    const countQuery = `
      SELECT COUNT(*) AS total 
      FROM posts p
      LEFT JOIN users u ON p.user_id = u.id
      WHERE 1=1 ${filters}
    `;

    const countResult = await dbQuery.queryRunner(countQuery);
    const totalPosts = countResult[0]?.total || 0;

    // Fetch posts with applied filters and pagination
    const postsQuery = `
      SELECT 
        p.id, 
        p.user_id,
        u.name,
        u.address,
        u.profile_image AS user_profile_image,
        p.title, 
        p.media_url,
        p.media_type,
        p.is_promoted,
        p.video_category_id,
        p.content, 
        p.target_locations,
        p.created_at, 
        p.updated_at,
        CASE 
          WHEN pp.id IS NOT NULL THEN JSON_OBJECT(
            'post_promotion_id', pp.id,
            'target_min_age', pp.target_min_age,
            'target_max_age', pp.target_max_age,
            'pay_per_view', FORMAT(pp.pay_per_view,2),
            'reach_goal', pp.reach_goal,
            'duration_days', pp.duration_days,
            'total_pay', FORMAT(pp.total_pay,2),
            'platform_fee', FORMAT(pp.platform_fee, 2)
          )
          ELSE NULL
        END AS promotion_details,
        (SELECT COUNT(*) FROM user_post_likes pl WHERE pl.postId = p.id) AS likeCount,
        (SELECT COUNT(*) FROM user_post_comments pc WHERE pc.postId = p.id) AS commentCount,
        CASE 
          WHEN EXISTS (SELECT 1 FROM user_post_likes pl WHERE pl.postId = p.id AND pl.user_id = ${loggined_user_id}) THEN TRUE
          ELSE FALSE
        END AS is_liked
      FROM posts p
      LEFT JOIN post_promotions pp ON p.id = pp.post_id
      LEFT JOIN users u ON p.user_id = u.id
      WHERE 1=1 ${filters}
      ORDER BY p.id DESC
      LIMIT ${limit} OFFSET ${offset};
    `;
    console.log("postsQuery", postsQuery);
    const posts = await dbQuery.queryRunner(postsQuery);

    // Convert JSON strings to objects (since raw SQL returns JSON as strings)
    const formattedPosts = posts.map((post) => ({
      ...post,
      promotion_details: post.promotion_details
        ? JSON.parse(post.promotion_details)
        : undefined,
      post_target_locations: post.target_locations
        ? JSON.parse(post.target_locations)
        : null,
      is_liked: post.is_liked === 1 ? true : false,
    }));

    return {
      status: 200,
      message:
        formattedPosts.length === 0
          ? "No posts found for this user."
          : "User posts retrieved successfully.",
      data: formattedPosts,
      pagination: {
        current_page: page,
        limit: limit,
        total_posts: totalPosts,
      },
    };
  } catch (error) {
    console.error("Service Error:", error);
    return { error: "Failed to fetch user posts with promotions." };
  }
};

exports.getPostComments = async (data) => {
  try {
    const offset = (data.page - 1) * data.limit;

    // Validate if the post exists
    const postCheckQuery = `SELECT id FROM posts WHERE id = ${data.postId}`;
    const postExists = await dbQuery.queryRunner(postCheckQuery);

    if (!postExists || postExists.length === 0) {
      return { error: "Post not found" };
    }

    // Get total count of comments for pagination
    const countQuery = `SELECT COUNT(*) AS total FROM user_post_comments WHERE postId = ${data.postId}`;
    const countResult = await dbQuery.queryRunner(countQuery);
    const totalComments = countResult[0]?.total || 0;

    // Fetch comments with pagination
    const commentsQuery = `
      SELECT 
        upc.*,
        u.name AS user_name,
        u.profile_image AS user_profile
      FROM user_post_comments upc
      LEFT JOIN users AS u
      ON u.id=upc.user_id
      WHERE upc.postId = ${data.postId}
      ORDER BY upc.created_at ASC
      LIMIT ${data.limit} OFFSET ${offset};
    `;
    const result = await dbQuery.queryRunner(commentsQuery);

    return {
      status: 200,
      message: result.length === 0 ? "No comments found" : "Post comment lists",
      data: result.length === 0 ? [] : result,
      pagination: {
        current_page: data.page,
        limit: data.limit,
        total_comments: totalComments,
      },
    };
  } catch (error) {
    console.error("Service Error:", error);
    return { error: "Failed to fetch post comments" };
  }
};

exports.updatePost = async (data) => {
  try {
    const { post_id, fieldsToUpdate, promotion_details, post_target_genders } =
      data;
    console.log("da", data);

    // Get current time and add 5 hours 30 minutes (IST)
    const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    // Check if the post exists
    const postCheckQuery = `SELECT * FROM posts WHERE id = ? LIMIT 1`;
    const post = await dbQuery.queryRunner(postCheckQuery, [post_id]);

    if (!post || post.length === 0) {
      return {
        status: 404,
        error: "Post not found",
      };
    }

    const currentIsPromoted = post[0].is_promoted; // Current DB value (0 or 1)
    let isPromotedBoolean = fieldsToUpdate.hasOwnProperty("is_promoted")
      ? typeof fieldsToUpdate.is_promoted === "boolean"
        ? fieldsToUpdate.is_promoted
          ? 1
          : 0
        : fieldsToUpdate.is_promoted === "true"
        ? 1
        : 0
      : currentIsPromoted;

    delete fieldsToUpdate.is_promoted; // Remove from dynamic update, handle separately

    // If is_promoted is already true in DB, do not update the flag, only update details
    if (isPromotedBoolean === 1 && currentIsPromoted === 1) {
      let updateQuery = `UPDATE posts SET `;
      let updateFields = Object.keys(fieldsToUpdate).map(
        (field) => `${field} = ?`
      );
      updateQuery +=
        updateFields.join(", ") +
        ` ,updated_at = ? WHERE id = ?`;

      const updateValues = [...Object.values(fieldsToUpdate), updatedTime, post_id];
      await dbQuery.queryRunner(updateQuery, updateValues);

      if (promotion_details) {
        let updatePromotionQuery = `UPDATE post_promotions SET `;
        let promotionFields = Object.keys(promotion_details).map(
          (key) => `${key} = '${promotion_details[key]}'`
        );

        if (promotionFields.length > 0) {
          updatePromotionQuery +=
            promotionFields.join(", ") +
            ` ,updated_at = '${updatedTime}' WHERE post_id = ${post_id}`;
          await dbQuery.queryRunner(updatePromotionQuery);
        }
      }

      if (post_target_genders.length > 0) {
        await dbQuery.queryRunner(
          `DELETE FROM post_target_gender WHERE post_id = ${post_id}`
        );
        for (const gender of post_target_genders) {
          console.log("gender", gender);
          // Handle both object format {id: 1} and direct ID format
          const genderId = typeof gender === 'object' ? gender.id : gender;

          if (!genderId || isNaN(parseInt(genderId))) {
            console.error("❌ Invalid gender_id:", gender);
            throw new Error(`Invalid gender_id: ${gender}`);
          }

          let insertGenderQuery = `
            INSERT INTO post_target_gender (post_id, user_id, gender_id, updated_at)
            VALUES (${post_id}, (SELECT user_id FROM posts WHERE id = ${post_id}), ${parseInt(genderId)}, '${updatedTime}')
          `;
          await dbQuery.queryRunner(insertGenderQuery);
        }
      }

      return {
        status: 200,
        message: "Post details updated successfully",
      };
    }

    // If post is not promoted in DB but user wants to promote it
    if (isPromotedBoolean === 1 && currentIsPromoted === 0) {
      let updatePromoteQuery = `UPDATE posts SET is_promoted = 1, updated_at = '${updatedTime}' WHERE id = ${post_id}`;
      await dbQuery.queryRunner(updatePromoteQuery);

      if (promotion_details) {
        let insertPromotionQuery = `
          INSERT INTO post_promotions (post_id, user_id, promotion_details,created_at, updated_at) 
          VALUES (${post_id}, (SELECT user_id FROM posts WHERE id = ${post_id}), '${
          promotion_details ? JSON.stringify(promotion_details) : null
        }','${updatedTime}', '${updatedTime}')
        `;
        await dbQuery.queryRunner(insertPromotionQuery);
      }

      if (post_target_genders.length > 0) {
        await dbQuery.queryRunner(
          `DELETE FROM post_target_gender WHERE post_id = ${post_id}`
        );
        for (const gender of post_target_genders) {
          console.log("gender", gender);
          // Handle both object format {id: 1} and direct ID format
          const genderId = typeof gender === 'object' ? gender.id : gender;

          if (!genderId || isNaN(parseInt(genderId))) {
            console.error("❌ Invalid gender_id:", gender);
            throw new Error(`Invalid gender_id: ${gender}`);
          }

          let insertGenderQuery = `
            INSERT INTO post_target_gender (post_id, user_id, gender_id, updated_at)
            VALUES (${post_id}, (SELECT user_id FROM posts WHERE id = ${post_id}), ${parseInt(genderId)}, '${updatedTime}')
          `;
          await dbQuery.queryRunner(insertGenderQuery);
        }
      }
    }

    // Only update other fields if there are fields to update and we haven't already updated them
    if (Object.keys(fieldsToUpdate).length > 0 && !(isPromotedBoolean === 1 && currentIsPromoted === 1)) {
      let updateQuery = `UPDATE posts SET `;
      let updateFields = Object.keys(fieldsToUpdate).map(
        (field) => `${field} = ?`
      );
      updateQuery +=
        updateFields.join(", ") +
        ` ,updated_at = ? WHERE id = ?`;

      const updateValues = [...Object.values(fieldsToUpdate), updatedTime, post_id];
      await dbQuery.queryRunner(updateQuery, updateValues);
    }

    return {
      status: 200,
      message: "Post updated successfully",
    };
  } catch (error) {
    console.error("Service Error:", error);
    return {
      status: 500,
      error: "Failed to update post",
    };
  }
};

exports.deletePost = async (post_id, user_id = null) => {
  try {
    // Check if the post exists and optionally verify ownership
    let checkSql = `SELECT id, user_id FROM posts WHERE id = ${post_id} AND is_active = 0`;
    const checkResult = await dbQuery.queryRunner(checkSql);

    if (checkResult.length === 0) {
      return {
        status: false,
        error: "Post not found or already deleted",
      };
    }

    // If user_id is provided, verify ownership
    if (user_id && checkResult[0].user_id !== user_id) {
      return {
        status: false,
        error: "You don't have permission to delete this post",
      };
    }

    // Update the post to mark it as deleted
    const deleteSql = `UPDATE posts SET is_active = 1 WHERE id = ${post_id}`;
    const updateResult = await dbQuery.queryRunner(deleteSql);

    return {
      status: true,
      message: "Post deleted successfully",
      post_id: post_id,
    };
  } catch (error) {
    return {
      status: false,
      error: `Error deleting post: ${error.message}`,
    };
  }
};
