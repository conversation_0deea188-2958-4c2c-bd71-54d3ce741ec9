# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "2.0.21"
  }
  digests {
    sha256: "q/H\a`\355\356\344\212\2046\236k\350\237j\265#u@\213\262\312;\341N\366c\367+\3561"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "react-android"
    version: "0.79.2"
  }
  digests {
    sha256: "\336\330\236\234\201+\252\346\264\375\336c\234\272\326G@\023K\215`\220\252\377\322\321\317\235\275\020\226\262"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.16.0"
  }
  digests {
    sha256: "\027f\333\330/d\241-\3155\236\313o\025\363\3175\333Mf\322)a\247\305\033/\374dh1L"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.16.0"
  }
  digests {
    sha256: "k\360=9\333\343tJ\314\342\'\323\266\2277L6%\252\341\002_\276\310\255\237\327\275X\274\3441"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.8.7"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\270\353\231}\3244\327\016\271|\275m\023\225\2663|gqq|z\315Y\361\034\bS\363\206\002\204"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.8.7"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.8.7"
  }
  digests {
    sha256: "a\310s\2472|\224n\3003\303\020\273\230\363\371.\352\274\355\340\341\245 \n\270\241\211d\203\307\277"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.9.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.9.0"
  }
  digests {
    sha256: "\255\211\302\211\"5\346p\362\"\330\031\313=\201\030\201C\313\031\240[Y\337\230\211\256Bi\365\307\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.9.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\275x:\315/\2278\204]X8\017F\364[\\\336\225\320\313\003\002}VrS8\262k\374Mr"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.9.0"
  }
  digests {
    sha256: "L\266\2662\035\361fK\231\376\347V\336c\366\257e\314\264\366\2116\362l\367l!\035\352\033#\256"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.5.0"
  }
  digests {
    sha256: "\243\337n\373)\276\262\377x\373|\336q*\307s\275l\334\337\311\203<\220\030\225\200O`\234\231^"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.1"
  }
  digests {
    sha256: "^\353Yd\250\355\262Yf\325\314y3\376\211><X\342E\254C=5\204x\262\vm\t\324\343"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.8.7"
  }
  digests {
    sha256: "\327\206\345\346\367\233\217\253\002o\t\242\324\256=\341\327(u\367dZk{\344\217\035\232\263\204sK"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.8.7"
  }
  digests {
    sha256: "p64RZg\261\306\276\252\253O\230\r\372\v\254\337~\024=\v]Q\365\254\r\224\004@\002\220"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\266\347\205\350S+\025Y!1\005c\251\017\322b\265\332\317id\256\352l\273\344\022\254\035\245\224`"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.8.7"
  }
  digests {
    sha256: "b\340v\242\336-e\n#\277yIT\031\370\2445z\330\351\262\215\200\023\030\320\374\335n5\372\207"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing-ktx"
    version: "1.2.0"
  }
  digests {
    sha256: "\303?\234\275\223\036a\220\3128\252\t\277\212z\212\0319\035K\017\267\247`ZkY\362\324%\200\321"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.8.7"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\263\323\234]\272^ ~\364\307\235I\032vP\001\224\305\352ht\025\177\005\241_\001y,\f\304\347"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.8.7"
  }
  digests {
    sha256: "\024P\347.\352.\310\b\260\211\271j\275\307s\312\003\244\024>&\177z\255\331\256\271\247\303\232\376\361"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.8.7"
  }
  digests {
    sha256: "C\322\212lm\251\301\313r\277\265\314\033Q\032y7\262\333\213y\325/7\320\267\361Ly\260\220\335"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.8.7"
  }
  digests {
    sha256: "\347\347\004?f\260wcuc\377VJ\177\306\316\344a\322\204\240\362H\\\005\n!C\253\275\211 "
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.8.7"
  }
  digests {
    sha256: "\032\316\273P\337/\f\a\363y\325\342:(\367yOp\357\262\266\3104\254\353\350\303\224xN\346\324"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.8.7"
  }
  digests {
    sha256: "do*B\322\210K\0020\217\261\2307\b\236&i\203_\342\034\246\245\266\006f\325\353\224\320\201\335"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.1.0"
  }
  digests {
    sha256: "\250;Y|\322@\235\301w\367\3712\367\304\b.\367\265P\213i\266\304\272q`~u\333\331\231q"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.1"
  }
  digests {
    sha256: ">E\225\315\251\276\343\222qYY6mpy\a\004\377$\fwenp/<\202 \263\331$Z"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.1.0"
  }
  digests {
    sha256: "g\316\270&|\'EW\303WX\353\276D\211\n :Uy\275+\bM\363Mpv<r\370\017"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.2.0-alpha01"
  }
  digests {
    sha256: "\306\251\347_\335\360?\t\314bB\370\202\352\263\262\336\336\'s\227{Xw+\005z\277\3204\276\227"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.facebook.fbjni"
    artifactId: "fbjni"
    version: "0.7.0"
  }
  digests {
    sha256: "~1\232\341\020\254^^\361\211\004\027\n\352\\>u>\221]\031f\231\327\3759\323l\216\035\3766"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "nativeloader"
    version: "0.12.1"
  }
  digests {
    sha256: "\227\035\355\206\000\234\n\305o\262\'^\022\217\205.}\313\304\032@\315y\226\210\370\241\256\275\345\031]"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fresco"
    version: "3.6.0"
  }
  digests {
    sha256: "\333\202\022\214\v\r \233/H\257\330B\222\305\365K;\376@{(\3722\272\277\336\225\221K\003\371"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "soloader"
    version: "3.6.0"
  }
  digests {
    sha256: "\373\276\n7\331\033\206\3242\r\a(\004\f2dY\232\224\263\032\035\344\037\222\242^\024\177\1775\357"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "fbcore"
    version: "3.6.0"
  }
  digests {
    sha256: "o\331\214+W7\356V\251\311\017\0344\330}$\345\3232O\327\374,\312mnt\261\300\264<\351"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "soloader"
    version: "0.12.1"
  }
  digests {
    sha256: "[\306\341]q/\220\240\243\211\r\"\333\016\357_\350\236\363\374\263\256\212\241\370\366\246\331\225G\310\222"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.soloader"
    artifactId: "annotation"
    version: "0.12.1"
  }
  digests {
    sha256: "\366\335\325Rh\277\030$\"]\367\243^\301\270\263<\371\210U\246\311\301S\321\211\211\236^\364\244\030"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-common"
    version: "3.6.0"
  }
  digests {
    sha256: "\000\0361\002d\310\272\316y$=\026\254\003\373`\272\347\255\374,J\300\306k\000\006\302I \217\360"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "ui-core"
    version: "3.6.0"
  }
  digests {
    sha256: "\265\025\306\233\241 \306\036\241W\342Ym\337:\222&3\234\016j\317\311kP\277\226aI\261\301A"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "middleware"
    version: "3.6.0"
  }
  digests {
    sha256: "\226a\364|\261\025j\nrF\201\230\002\311\260\027\272Qp\366\256z\276\3371\375,\366b\343\334\020"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "drawee"
    version: "3.6.0"
  }
  digests {
    sha256: "`CU\031\313v\316\347\2079\331(\304j\255\251\030\367l\271\223\267Z\371*\261~\235tk\002w"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline"
    version: "3.6.0"
  }
  digests {
    sha256: "W\360A\225\345\327\'6q\316F}\016#\304\207\263\302\310tj\306%\177*\'S\314\aq\366\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.parse.bolts"
    artifactId: "bolts-tasks"
    version: "1.4.0"
  }
  digests {
    sha256: "\233\305\036>\312\205:\235\362\373\254b\3021}\262\311\352a\233\225\265S\0340D\002C\332\254\316\342"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "urimod"
    version: "3.6.0"
  }
  digests {
    sha256: "T\274\b\206\345y\353\b3)t\222\267\330\234\264\306\023\312\225\303y\272\314A\255\376)S\232\221\367"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "vito-source"
    version: "3.6.0"
  }
  digests {
    sha256: "\265\303\305\300\207\332\261\231\254\212\376\203\227\372\211\314\363\206\337{)\250\333w\263a%Wj\362\344\221"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-base"
    version: "3.6.0"
  }
  digests {
    sha256: "i\312\255\a\3767\000\264\217;Vf%\004\371\301#]\242\351w\270\352\315\235\362Q2\036\226I\306"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.infer.annotation"
    artifactId: "infer-annotation"
    version: "0.18.0"
  }
  digests {
    sha256: "\312\357\272\223VC\334\226\'X\247`\361bz@\"\025\257\003M\226\300\364\204\326;\177\335\312\231/"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-annotations-jvm"
    version: "1.3.72"
  }
  digests {
    sha256: ">\343\245m\324Q\343?\203R\340\322\200:U\312\033\221V\327\370\250\340u\233\370\341\207\377\215\262J"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-native"
    version: "3.6.0"
  }
  digests {
    sha256: "\\\276\257\260\367\331\034\a\216%k\261\257)\201I?7\262\243\206\337CY1\t\326\253\357w\222\331"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-ashmem"
    version: "3.6.0"
  }
  digests {
    sha256: "\v+\032\215}nX\0061,K{F\241U7\343\255\022\247\252\237\303\254&$e00\202\271F"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-native"
    version: "3.6.0"
  }
  digests {
    sha256: "\315\300\314\341\b\274\253i\332+v\036\3558\236G\222\371\224\260;RK\330&1l)2\353\f\370"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "memory-type-java"
    version: "3.6.0"
  }
  digests {
    sha256: "E\004\375\305c\346\231t\2130\017>5\'\233\340\221\214\004\230\036~\366\037\221\226c\333\326\236h?"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagefilters"
    version: "3.6.0"
  }
  digests {
    sha256: "PDt^\205\3735\027~\216+\200\337\202\300*\226\212m\270\212\377\347\021\344\373\0008\222M2\275"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "nativeimagetranscoder"
    version: "3.6.0"
  }
  digests {
    sha256: "\r\217\205Ya|\210v=\307^`\005\374`\343{\375k\262\017\345\271z\030|\253\357\302\261\032|"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.fresco"
    artifactId: "imagepipeline-okhttp3"
    version: "3.6.0"
  }
  digests {
    sha256: "\372\240\036\3209J^\310l\376\234\006\341\006\243!y\344\373\203x\347\256\357\336X2\025\224\272e\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.facebook.yoga"
    artifactId: "proguard-annotations"
    version: "1.19.0"
  }
  digests {
    sha256: "\373e\367\006\356\240[V@\373k\223\322\035lA\371\342\264\347\246\346\331\362\214\351\322\245tGI\225"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-urlconnection"
    version: "4.9.2"
  }
  digests {
    sha256: "\016\0349\252\211f\217\226%\311 z\315J\207\204\334\206\212\320\325\242\001\035\236\025\340gO;}\021"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.15.1"
  }
  digests {
    sha256: "\"\234\'\363\337\232\376U(\306\2575\253@~\'\272\f\234\274\332\216\"\'R\272\320.pt\325%"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.15.1"
  }
  digests {
    sha256: "\357\370\204\270$\255y\306 \\\"\355\362F\3501\375\201\035\334\216\240V2oSA1C\360\006\244"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.15.1"
  }
  digests {
    sha256: "\265\006M\033\345\340e\035\350T)K\271\245\303\234\265\357\270.;\271w\220\265j\263Y\2273vi"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.15.1"
  }
  digests {
    sha256: "\3031\347O(\325s1\235hc\277\276\363i\034\323wU\200\377J3\271^\323\362\'\3143\233\r"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.4.1"
  }
  digests {
    sha256: "\027\341zk\206\273T\aM:~\317\246\225\253\222\016\257\364\317fz\203q\355\202\343wX\f\374\366"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "okhttp3-integration"
    version: "4.14.2"
  }
  digests {
    sha256: "\276\230\017\3233\nQ\203\261\txc\370[\274A\202g\230u\2014\337\314\372\"@\221\0239\200\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "avif-integration"
    version: "4.14.2"
  }
  digests {
    sha256: "\000^\314\032l\374\344\336\230t\343\321\323\301\376-\000K\002\313\030A\241\301]\346\016\f]\b,K"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.aomedia.avif.android"
    artifactId: "avif"
    version: "0.9.3.a319893"
  }
  digests {
    sha256: "\266\a~\371\216Z\306\006\036b\346z\335\227\302\326\006\211?\251\231(\347\273\373\377\023\266\302\233\207\310"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "33.3.1-android"
  }
  digests {
    sha256: ",>A\321\263\200\362\004M%yG\243\252\202\332\277:\344\271xb\'E%J\241\213l\370\232\260"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.2"
  }
  digests {
    sha256: "\212\217\201\317\2335\236?m\372i\032\036wi\205\300a\357/\"<\233,\200u>\033E\216\200d"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.43.0"
  }
  digests {
    sha256: "?\274.\230\360XT\303\337\026\337\232\272\251U\271\033\025\263\354\2543b2\b\355d$d\016\360\366"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.28.0"
  }
  digests {
    sha256: "\363\374\212:\n@ pj7;\000\347\365|%\022\335&\321\370=(\307\323\207h\370h+#\036"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "3.0.0"
  }
  digests {
    sha256: "\210$\025sF}\334\244O\375Mt\252\004\302\273\375\021\277|\027\340\303B\311L\235\347\247\n|d"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.zjupure"
    artifactId: "webpdecoder"
    version: "2.6.4.14.2"
  }
  digests {
    sha256: "(\a[\354J\021\330D\337v\233\006G\253F\f\241\'\340\376\260\274&\016\255\303\320$\332JK\221"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "app.notifee"
    artifactId: "core"
    version: "202108261754"
  }
  digests {
    sha256: "\216\017\250\202\346\ta\24065\357T\310\355\217]i\003\037\274\337e\235.\35144\245\255\022\231J"
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.8.1"
  }
  digests {
    sha256: "&\331\325b*\ri\302\266\001\313f\036\241u\366\340\265M\262u7\021\261\214\2416\276K\232\351\302"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.5.2"
  }
  digests {
    sha256: "5 \326\363\216\272\303\364a\345\207\327Y\240R\331\212Q_\0019\313\353\vh\306y\037\367\302\336\\"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.5.2"
  }
  digests {
    sha256: "\002b\342\240\342\242\351\307&{\237z@XG\316cj\006\304\301lR\227l\266\215\367xf\270}"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.3.1"
  }
  digests {
    sha256: "\344\260\305\200\266v\275=\373\355!\277\301]G\2002lT\\\301O\332F\245\251$~Y\354\213\212"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.3.1"
  }
  digests {
    sha256: "\202xu\245epI%\371\"\336\204~\002\367R\264_d\341U\220\233\035\262[#R~\345\302\\"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.greenrobot"
    artifactId: "eventbus"
    version: "3.3.1"
  }
  digests {
    sha256: "\3118_JW\017\213.\366\370\367Ri\000w\355S\2145\217\324uP2\253\200P\027U\203\217\021"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.greenrobot"
    artifactId: "eventbus-java"
    version: "3.3.1"
  }
  digests {
    sha256: "\205\366B7\212%\325\344\032i\370\272l\373/\276gN@kT7@>\237\225_y=l\331\221"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.Dimezis"
    artifactId: "BlurView"
    version: "version-2.0.4"
  }
  digests {
    sha256: "\303\342\227\210\324\247\321j,\316\345)s\022)\374\315z\331\237\270\274\035\364\025\341oX\247u\231\310"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.2.0"
  }
  digests {
    sha256: "w\224\b\261\2523\f\324\247\255\361\262\350_\322\211\303\016~\335.\216B\204{}\006X\232\367\025\372"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.1.0"
  }
  digests {
    sha256: "\242+\224\247w\211\363\263K\354\322@\202#\023\000R@\021\314\310?\002aB\304\232\326\00117\233"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.1"
  }
  digests {
    sha256: "L\376\324+\334\301\226\321\036\233\020\332h\301\371l\324\275\244\315\205!\347(_bD,\f\021\336\b"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0"
  }
  digests {
    sha256: "\0314\265\357\360\\F!X\226\267[\254\006Z{c\261>?\f\215\352\314\n\235T\262\a\331\365="
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.15.0"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "22.4.0"
  }
  digests {
    sha256: "\256\361,\0355\271$\"q>E\366\360P\036\303\024\372\221t\301\224\250}\227\361\343\336\200\222\370\236"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "22.4.0"
  }
  digests {
    sha256: "\207\312\312\260e\236\221\362\316C[I\232\006\f\375\n\200t\350\302Z\212z~\237\242\276>G\aN"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.1"
  }
  digests {
    sha256: "\251\200\207\313o(\256\351:\t\3527\377?\343\231\b\234\312\240Z\367\204\341\270\375\313\016\037\254\245\256"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "22.4.0"
  }
  digests {
    sha256: "\245@4%\257\333\231\271\267\ry\020\336~\211l\235QH\001b\207m\246\227S\227\376y\331\203\323"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "22.4.0"
  }
  digests {
    sha256: "\327\000*\357A\362E\351}\'f\303\246m\312\031\352\vL\375\217$l;\212~\t\300\r\322|K"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "T\nn-\307\2167\006\025\367\177\207,\017;p\n\032\312\035x\244\251\257}(\b\005\bH\306c"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "\262\210]\265\335\255\233E\177\224\322\020A\332S&}\250\232\337\3448$+\240}\321\034\217\246>t"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "22.4.0"
  }
  digests {
    sha256: "\276\004\002 $\336?\273/\231\227\033\364\2144\341\324\r\226S\311T\3567\301\361@s\320\205\247\316"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "22.4.0"
  }
  digests {
    sha256: "}\337\a\f\351FP\0166;\304\rV6\355\206P\256\365\232\003\251\3768|<\356\375\265\271\373\215"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.2.0"
  }
  digests {
    sha256: "\335\033b\b\243h\224\347\327\357\376\"\003\262Du\256\265\fM0E\243\030\020\256:\325\276C\273Y"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "20.0.1"
  }
  digests {
    sha256: "\267k\343\215\245O\227+\303v\3776YIN\353\307\0303\236\3443MBk\264\365.Y\365\352\344"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "22.4.0"
  }
  digests {
    sha256: "\313\202\273_v\tQ\177\352\002 \3263\220\006\375]p\317\300\260\224\256\225\003\340\234\214{\002\331\202"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.1.1"
  }
  digests {
    sha256: "K\301\327\263\205\307\r\374\006\330c\302\321\020|<\206cS\247\030\020\364\036D\227\250\355\335\231\027\216"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "19.0.0"
  }
  digests {
    sha256: "\005\0035\n\343\021\363\202T:\2108C\325\005\v\243\2355\363?\222\263\311\327\346\026R\210\260\273\""
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.2.0"
  }
  digests {
    sha256: "\342U-\035\307\\r\200\b\234\t\376\274\257/\356r\207\305\2368\305G\315JC\036\335_M4\030"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.3.0"
  }
  digests {
    sha256: "*j\235\211\362J\267\337\t3\230:\214Hl!o\263\'G\276\247+1\23162:q\226A\""
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.3.0"
  }
  digests {
    sha256: "\025\371\301O\300z\214\222@+.\006V\350\351B\222\262E\350\3269],\tm\020\222\r\223\313\240"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.1"
  }
  digests {
    sha256: "Jt+\237>\366\201\315\3256~\n\a\266\346v\240\203I\306\346\342\024\301\205\024\230\207\223\020\262\366"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth"
    version: "23.2.1"
  }
  digests {
    sha256: "\300\177q\022\246b\f\225r@\241N\032\a\330[\340\001^@5d\245\3023\022\324\334N`\267\334"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.browser"
    artifactId: "browser"
    version: "1.8.0"
  }
  digests {
    sha256: "-ag\263;\366\240d\303u\001P\025Cp#I\350P\254b\3072\321\357\244\347\020cgTV"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "I\217e\033O\221f\277w\017l\251z\353\250\351!\356\301\001\263\337\370\331\360\305\032\310\366\344\002\244"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.credentials"
    artifactId: "credentials-play-services-auth"
    version: "1.2.0-rc01"
  }
  digests {
    sha256: "X0\246A8N\227\226A[0\363M\302Px-x\b\020\034\366\22264`\33199\240 \311"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.3.0"
  }
  digests {
    sha256: "]2P\016\\,\"@\244\223-\272M_\375\006\302\244\244\250\206o\324\364\276\373)})_c,"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.1.0"
  }
  digests {
    sha256: "\222r:8\344\r:\312\037\2365\021YR\231\220!\251/\254$\2371a\3552\223\2214r\251\270"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.libraries.identity.googleid"
    artifactId: "googleid"
    version: "1.1.0"
  }
  digests {
    sha256: "\031J\301\374\031\206\335\037b\004o\2567\335\367~cw\017\334\037=4\272\2529|\373\364\321\221\242"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "integrity"
    version: "1.3.0"
  }
  digests {
    sha256: "6\253\374C\227P\270%\355\333?W\a\347\357\002\034K8\354\341\334r\202f\205\256\273\327\000L3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.play"
    artifactId: "core-common"
    version: "2.0.3"
  }
  digests {
    sha256: "C\003%_\025,Y\271T\221\233\264q\321N\345\376\322\337x5e\336\250R\213\b\b;\2474+"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.recaptcha"
    artifactId: "recaptcha"
    version: "18.6.1"
  }
  digests {
    sha256: "]\r\\\350i\332c\004\2754\342\027\273\367e`\034Q\306\036\305\305\177/!\233\232\210\024\321\370\036"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\036\241\020\250\3266\3042`\241\360}zE\372\005H\210Y\372\2637;\023\004/\201\022\005\266\376<"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-crashlytics"
    version: "19.4.4"
  }
  digests {
    sha256: "\353\"\366>\b\234.\315\311vq\\Fi\224\306\334\371u$\311\272_\240W!\347\311\312\006-\027"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-sessions"
    version: "2.1.2"
  }
  digests {
    sha256: "\253z\031\217`\032\317\240\355\264\367{HG\270\f\000\342\313\247\334\322\216\224)\255a\323\272\t7\224"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences"
    version: "1.1.3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\220\333\\\004\261Ho\255*\017\342\220 \3769D\300\372\f]6\361A\036\336\241\231\000t*E\023"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore"
    version: "1.1.3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-android"
    version: "1.1.3"
  }
  digests {
    sha256: "z\235o`!\215w_\3549\250X@C5\235\373jcZa\326\206\027\252n\230&\330\232\001;"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core"
    version: "1.1.3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-android"
    version: "1.1.3"
  }
  digests {
    sha256: "\260\257x\371\b\203@z\346?\321HW\304\346.w\037>\236?\202\340\227q[M\2518\215\340\362"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "2.0.21"
  }
  digests {
    sha256: "\210\226\256o\325\2560\217\360P\025`\317\262\342\234\266\363s\2038\237\234\226e\251\344\334\215\177\373\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "2.0.21"
  }
  digests {
    sha256: "r\030\210h\243\234\202T\022RR\243\300.*\365\274x\035\a\216I\203v\340~\235$\362\217\212\372"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio"
    version: "1.1.3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-core-okio-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: ")\025\006\264\374\343\230$\227\223\343\' v5\275M\362Q\201\331S\230\b\327\030\032J\275\bu\365"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core"
    version: "1.1.3"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-core-jvm"
    version: "1.1.3"
  }
  digests {
    sha256: "\3139\341\";C$\3270\305s\242\274\352:50\236\275\223X\373\3460\211\227\005\243\373\312\312\366"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-proto"
    version: "1.1.3"
  }
  digests {
    sha256: "chja/9\270\312`;\353\3437mU\373l\236\342\212JQs\357\244\234\211\313\314\250\241\'"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.datastore"
    artifactId: "datastore-preferences-external-protobuf"
    version: "1.1.3"
  }
  digests {
    sha256: "\374\263\363s\317t4&\310^\273\r<\306D\256\372\362o9\267\372U\273\220#\215A\266\211\354\247"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-config-interop"
    version: "16.0.1"
  }
  digests {
    sha256: "m\273\f\361\304nE\375\360;\376g+\247H3`\273\247\000\360\017\016dq\177p\017y\212\177\037"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-crashlytics-ndk"
    version: "19.4.4"
  }
  digests {
    sha256: "\227\324;\\\344\016Ga9\354*\fr\371?2\331}d\31297.\375\331\214\202M\260\342.$"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database"
    version: "21.0.0"
  }
  digests {
    sha256: "d\016\ag<\316F[\350;\272\270\337\327bZ9s2\212\377\246V\\\244\331#?\025\260h\247"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-firestore"
    version: "25.1.4"
  }
  digests {
    sha256: "F\021\333&\242\202\232=\300`w\2565\025\277\271\242\3544\000*\334~\201\025\211\222n\360F\375K"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "protolite-well-known-types"
    version: "18.0.1"
  }
  digests {
    sha256: "\355\327\206\202D\343\366x\226\357\260\304\306\274\233N\212\262W\v0\217E\312\376G\352\210x\225\346q"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.protobuf"
    artifactId: "protobuf-javalite"
    version: "3.25.5"
  }
  digests {
    sha256: "y\243\377Q\254*\213\033\347\377\372\"\204\203\326It\361\177>\377\377\331f\260T\330qy\344V4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-android"
    version: "1.62.2"
  }
  digests {
    sha256: "%D\222\016\245\364g \334\367^\202\000\243t\320\323v\375\310\362\224({\265yu\272\263\202\2469"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-api"
    version: "1.62.2"
  }
  digests {
    sha256: ".\211iD\317Q>\016\\\3752\274\327,\211`\032\'\306\312V\221o\204\262\017:\023\272\317\033\037"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-core"
    version: "1.62.2"
  }
  digests {
    sha256: "\030C\231\002\304s\242\301Q\036Q}\023\270\256ycx\205\n\216\332Cx|k\247x\372\220\374\305"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android"
    artifactId: "annotations"
    version: "4.1.1.4"
  }
  digests {
    sha256: "\272sN\036\204\300\235aZ\366\240\2353\003KO\004B\370w-\354\022\016\3737m\206\245e\256\025"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.codehaus.mojo"
    artifactId: "animal-sniffer-annotations"
    version: "1.23"
  }
  digests {
    sha256: "\237\376Rk\364:cH\351\330\263;\234\326\365\200\247\365\356\320\317\005Y\023\000~\332&=\351t\320"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.perfmark"
    artifactId: "perfmark-api"
    version: "0.26.0"
  }
  digests {
    sha256: "\267\322>\223\243E7\3163\'\b&\232\r\024\004x\212[^\031I\350/U5\374\345\033>\251["
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-context"
    version: "1.62.2"
  }
  digests {
    sha256: "\231Yt}\366\247S\021\236\034\032=\377\001\252vm$U\365\344\206\n\312\243\0055\236\035S:\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-okhttp"
    version: "1.62.2"
  }
  digests {
    sha256: "\236\220?\375+0\322\373{T\312Mr\221[\032\263[\r\"\274\220\230V[\016\217B(\325\232\376"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-util"
    version: "1.62.2"
  }
  digests {
    sha256: "<q\003\346\363s\205q\343\256\332B\017\342\246\254h\343TSM\213f\364\030\227\266u[H\2675"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-protobuf-lite"
    version: "1.62.2"
  }
  digests {
    sha256: "y\231y\211\250\302\265\277M\321\201\202\242\337./f\207\003\326\213\247\303\027\347\240x\t\323?\221\364"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-stub"
    version: "1.62.2"
  }
  digests {
    sha256: "\373L\246y\244!AC@le\254Ag\262\265\342\356,\253\037\301\001Vk\261\304i]\020^6"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-storage"
    version: "21.0.2"
  }
  digests {
    sha256: ".K\200X\311&\246\rIZ\374\254\037\213\000Q\320\205\351\225\334\240S\244\344\340\236}\">~\322"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck"
    version: "18.0.0"
  }
  digests {
    sha256: "\322z\363\363\034\214\316\241\351\003\f\035\303\204\021\304\v\230g?\027~\3763\344\361\265\021\360\366Y\220"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "io.github.webrtc-sdk"
    artifactId: "android"
    version: "125.6422.06.1"
  }
  digests {
    sha256: "d\367\255\001%#;\213A\006?\275\267\247\322\306\207\021\357\261\n\"\310\024\342^\001Bn\307,!"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.5.2"
  }
  digests {
    sha256: "\2723\354\3001\367\346{\325\253\026\033\320\034n\311.\006\av\032\340v\332&\242\305\277%w\273\f"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.mp4parser"
    artifactId: "isoparser"
    version: "1.9.56"
  }
  digests {
    sha256: "p3h\341|\273\346[\275\213*\242Q\362\347\321\bzJ\213F\315\231\024\234\314\241M1\357\342\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.aspectj"
    artifactId: "aspectjrt"
    version: "1.9.7"
  }
  digests {
    sha256: "f\257-\232\002\215\034?\235\237\005\274\367^6\272\035\355m=M^\351\016\177\316\206\211\023\220\344\272"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.slf4j"
    artifactId: "slf4j-api"
    version: "1.8.0-beta4"
  }
  digests {
    sha256: "`+q#)\310KJ\203\304\004d\364\375\375\017\344#\214S\3579q9\250g\006G9\333\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.banketree"
    artifactId: "AndroidLame-kotlin"
    version: "v0.0.1"
  }
  digests {
    sha256: "\323\b3\250HT\360|N<\370\344\260t\250\"\302\330P,\333\345(\221Z$OHdq\033\366"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "javazoom"
    artifactId: "jlayer"
    version: "1.0.1"
  }
  digests {
    sha256: "\205\005\b\3107EJ\033\006\001|2\243hv\372\345\026\336\036\211\250)\367%\376\341\346\334\305 \000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-io"
    artifactId: "commons-io"
    version: "2.8.0"
  }
  digests {
    sha256: "\002\362\221\345\321$=\301CIn<\273\264\n\034\355G\252X\362\3263\323\343\207\200\315\006\215Pt"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.commons"
    artifactId: "commons-lang3"
    version: "3.8"
  }
  digests {
    sha256: "\223u\252\321\000\f\335[\323\006\216\203-\351\200 \224\372\301\361EeRQ\341A\325\320\a/\253\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "net.time4j"
    artifactId: "time4j-android"
    version: "4.8-2021a"
  }
  digests {
    sha256: "\345I6)\376\230\375Ng\2113w\263\272\311\224\243\022\273\234d1>\371&e\233\217P\302\251w"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-location"
    version: "18.0.0"
  }
  digests {
    sha256: "\213F\362\376?\240\020c\236\222\016\333\337\fB\221\200\222\350NE=\231f\261\255\221o\v\026rY"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-places-placereport"
    version: "17.0.0"
  }
  digests {
    sha256: ",\177\326:\320/(\025\n\344\377\344a]\254}iMy\016,Fg\367w\256\334\216\340T\351)"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.facebook.react"
    artifactId: "hermes-android"
    version: "0.79.2"
  }
  digests {
    sha256: "\326\\\241\223u\227:\260\320\315\005\031\254\2250\372\220\352g\365\327t\215\307&\346\206\2413P\005a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.ump"
    artifactId: "user-messaging-platform"
    version: "3.2.0"
  }
  digests {
    sha256: "\027\262\303\364Th\313\2057\246d\353x8\\\006\221\027\212\2534\020,\246\350U\220(\037!&&"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads"
    version: "24.3.0"
  }
  digests {
    sha256: "\032s\370:i\324\264\247\360\325D\276!\235\\\3205\244X\333\240\315\270\364[gV\374\300\362\317\350"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.11.0-alpha02"
  }
  digests {
    sha256: ";\037^#n\221\231\2518\375\vP{\306\316q\034F\315\002\206\275@\246\330r\336@\232\267\331N"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-api"
    version: "24.3.0"
  }
  digests {
    sha256: "\340\2463\363P\003\020;Y\'\375q\206\272\260H_W}>\342\210\343Q\vV\307\236V\276V\206"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-appset"
    version: "16.0.2"
  }
  digests {
    sha256: "t\330\030I\254\311\353N\202\323\314\224-\245Z\330S|\373s7R6\355i\260v\331\327\255\206\031"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.github.yalantis"
    artifactId: "ucrop"
    version: "2.2.10"
  }
  digests {
    sha256: "\304M\\B\022\370h\370}\034\022[\251N\311z\313e\270\337\243\335\374h\367|\373\356\316\256\255\251"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "com.razorpay"
    artifactId: "checkout"
    version: "1.6.41"
  }
  digests {
    sha256: "\3669s\227\0356\315n\303\246\022\270\304\215\213Mw\332\352U\353<2sm\251\365r\221\317\261\335"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.razorpay"
    artifactId: "standard-core"
    version: "1.6.53"
  }
  digests {
    sha256: "*a\211XX6\276\337\241A*\005\034%\250\031\236\025cGM\"D\035\326\243\036&\3765\316S"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-wallet"
    version: "18.1.3"
  }
  digests {
    sha256: "\341\235\037FP\365\034\342 ,\t,\276\027@X\206\veX\317&\310\2767\2472\357\363\256\030d"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-identity"
    version: "17.0.0"
  }
  digests {
    sha256: "\211\207\306\303\003\352\252\234\020\304\003\202,\365\256\030\216\341\316a\303\005n\263\276,\244\252\354\310\v_"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-maps"
    version: "17.0.0"
  }
  digests {
    sha256: "\371\344y\274W\377B9Y\306\335\235\b\324c\306w\364@\342\235\220\336yT\030\352\'\332lg\373"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.jakewharton"
    artifactId: "process-phoenix"
    version: "2.1.2"
  }
  digests {
    sha256: "F\b\322\253+![1\224\300I\263\033 =\356\366h\320@\342I\270\203\225\3756W\000\036m\314"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.4.1"
  }
  digests {
    sha256: "f\315\017\231 \221\221f\b#\323,\217\303)\343\203\250\247\3773\340\326vc#\2076\032\274\272p"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.4.1"
  }
  digests {
    sha256: "\227>^{\016\317\216l\214\330%\312\263QE\270LV\002\n\367\303\354R\201^Y^\367\033\252\026"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.4.1"
  }
  digests {
    sha256: "e\266\322,\226\337\265\316vuK\b\365\260\004\356\r\275\276`y<^\253\237\225\301\365O\225\364\260"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.4.1"
  }
  digests {
    sha256: "!p\256dH\315I\237\305p\332\372\201\345hz\330\023\231[|+\177\366\200\032\312\252\232\210\273J"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.4.1"
  }
  digests {
    sha256: "\034\v\264\036\350\212k\372\362G \273\376-)\2403\253\255!\303\016\t!\357\275\316l\037\242\210B"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource-okhttp"
    version: "1.4.1"
  }
  digests {
    sha256: "\241\376\376:\310\265P\206\021\236\257\345c\037\375\252\324\222\374\345Y\240O\b\305\221^\374\256R3\372"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.4.1"
  }
  digests {
    sha256: "\367\331|Z9\334\243\303\302\033\310\247\001E\3311\330c>\320\332\350\221\230\303p\347\267\343\303\361\203"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-dash"
    version: "1.4.1"
  }
  digests {
    sha256: "9@\306i5\327\030\320z\272\256\005\261\032\270\227Q\263\000\267\316\344\270\255\241\272\225\020\3476\'Y"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-hls"
    version: "1.4.1"
  }
  digests {
    sha256: "CTkk.\360\302\201o]:\361\201\302\027\232\\\033\335\271\337\205\267\270K4(\3157\310\207\336"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-smoothstreaming"
    version: "1.4.1"
  }
  digests {
    sha256: "\016q@\353\221\a\232bI\372\301p#\aS\312e\224\347j!\375\236\202\345\364\254\361\230\000\347N"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.4.1"
  }
  digests {
    sha256: "\037Q\344c>\036a7\360\244\270\005R\257\303\231\342i \213p\003R\371eC\324Q;\346\277("
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-session"
    version: "1.4.1"
  }
  digests {
    sha256: "\266\031\262\000@^#q6\347\332\252\354y\353iCH\360V#\220\310\270\265E\244)N6A\324"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.7.0"
  }
  digests {
    sha256: "\201\241\231\356\207\306\323\325\237\263_}\276\307\033?\035Pq(a\020\231\266\254:K\034Z\v\361\371"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-ui"
    version: "1.4.1"
  }
  digests {
    sha256: "\352`O\032\210\376i\033\035M\253\212\343\243\336\021\a)4\026)\335i1\036\3506\021\343,\227\262"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-core"
    version: "1.5.0-alpha03"
  }
  digests {
    sha256: "\300~\365\356\031j\343ZM=w\272\272\346\031t`\222\2338\227\f\234\247\323\254\306\036\205\230a\230"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "\031h\277R\003\2368cj\246\361\024\315\027\327%i\031\321\350\231t\027qo\357\235\035\241\362M\205"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.auto.value"
    artifactId: "auto-value-annotations"
    version: "1.6.3"
  }
  digests {
    sha256: "\016\225\037\356\2141\366\002p\274FU:\205\206\000\033{\223\333\261*\354\0067:\251\232\025\003\222\300"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-camera2"
    version: "1.5.0-alpha03"
  }
  digests {
    sha256: "ZB\271\306\215\353\232\372\224f\322\035sh\270\336\222\021\226\271\220\243W\213\r\240\036\235\323\337m\326"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-extensions"
    version: "1.5.0-alpha03"
  }
  digests {
    sha256: "\021\031GmJ\317\230\304\330\327\236\025\262\224jC\313\2026T\re\261\0178\373\304\277\353l\217\202"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-lifecycle"
    version: "1.5.0-alpha03"
  }
  digests {
    sha256: "\210\034\324\212\205\324nj\352\316\026,wL\244V\367\305m\277\277C\032\022\336\3608\227\017\035\331g"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-video"
    version: "1.5.0-alpha03"
  }
  digests {
    sha256: "\314lE\"_\024t\bZ\346\334vup?s-\333#\257`\361uq\202jB[a\021\214\236"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.camera"
    artifactId: "camera-view"
    version: "1.5.0-alpha03"
  }
  digests {
    sha256: "\335\37638-\006Y\244a\206\223\232\237\3312\034\0028\257\r\364\335\224\260S_\274\024\230&\035\036"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-mlkit-barcode-scanning"
    version: "18.3.1"
  }
  digests {
    sha256: "\365\200\226\271\210gAzi\215\265]\337\327\310\355\003\203\262\021\177\250\363i/\247\001\326\023/\024b"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.odml"
    artifactId: "image"
    version: "1.0.0-beta1"
  }
  digests {
    sha256: ".q\2521\370:\224\025\'\177\021\235\346q\225ro\a\321v\016\225B\301\021w\2142\016:\241\362"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "barcode-scanning-common"
    version: "17.0.0"
  }
  digests {
    sha256: "\315\016\236q\2704\a\215J\212\177\t\347-}|:\"\342X\351`-\247\254\206\024fw\004?\024"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-common"
    version: "17.3.0"
  }
  digests {
    sha256: "\300\b\\\245\373\240\017\021R\234\n\203\261(Q\352lY\233\274\310\257\375\222~\221K)\247CE\237"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "common"
    version: "18.11.0"
  }
  digests {
    sha256: "f@\212\b\333\262FV\207\377]\320\310\366A\vVT\234\343\354\364I\331\to7\022(!1\320"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.mlkit"
    artifactId: "vision-interfaces"
    version: "16.3.0"
  }
  digests {
    sha256: "\217\001\222\242m\001\267(\240\032\025\246 \330)\377\367\tX\"\202&\224C\212\223\022|\004\313\026\373"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.pubscale.sdkone"
    artifactId: "offerwall"
    version: "1.0.11"
  }
  digests {
    sha256: "\200\276.\357\274\230\234P\205\347\243\242\227\242\256Q\332\342a\234\256G6\311\235\221\355\243d\306\256\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.8.1"
  }
  digests {
    sha256: "uV\rO^z\034T\21612\253\362s\317\016\332\217+\336f=PM\r\324*B\\25Q"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.10.0"
  }
  digests {
    sha256: "\';\242\030cl4\367\240\221\300Y\321Y`\005C\340>\250\276\357,_\305e%\264s\226\026\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.ads"
    artifactId: "ads-identifier"
    version: "1.0.0-alpha05"
  }
  digests {
    sha256: "$\201F\200\274\235\2349\232^\253$\373\245\373\3245\35289=S\031l\315\207\262\200\004H\200b"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "androidx.ads"
    artifactId: "ads-identifier-common"
    version: "1.0.0-alpha05"
  }
  digests {
    sha256: "\265\275\370\352\370g\316\236\2478\321\342\303\267\242\322u\226\217\033\020\243\266\242\344\213\232\361\241d$:"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.pubscale.caterpillar"
    artifactId: "analytics"
    version: "0.23"
  }
  digests {
    sha256: "\266|x\376O\271w|6\271\270\214N\306V\225Z\332?\213\275|r+w,\252d\256-\334}"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.mrmike"
    artifactId: "ok2curl"
    version: "0.8.0"
  }
  digests {
    sha256: "\207\210$\251\026@\374\330\262\274\2206&\252g@\235\177\200\252\263\232g\025z\375)\214\375*p\317"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.5.2"
  }
  digests {
    sha256: "p\341\223\373\035\305\317D\373\350_\315_q\2013;,kID\334\352\320\323\021\252\034\260<P;"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer"
    version: "2.19.1"
  }
  digests {
    sha256: "\222U\005\375S\347\270^\353\030y\277\354\214\334F &)]\307w\353\326,\350>\353J\221\005w"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-common"
    version: "2.19.1"
  }
  digests {
    sha256: "\\\300\225i\316R\201\a\"\311y\253s\261\202\001g\333\210\341\273\034\272\252\270\270e\fp\363_A"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-database"
    version: "2.19.1"
  }
  digests {
    sha256: "\237?\353\036\177\027\310r\177\317U\243\314\2115J\002\352\342\326.\233\2447\335\0344\326\016\22537"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-datasource"
    version: "2.19.1"
  }
  digests {
    sha256: "\322j<\277*o\312B\216\371]\224L\n\003\343\370\304G\tl\'\325\317S\362\035;\265\356\360\005"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-decoder"
    version: "2.19.1"
  }
  digests {
    sha256: "\2766\027\205\334\373_\226\355\332l\337\346MA]=D\327o%^\217|&\225\a\211JwD\317"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-extractor"
    version: "2.19.1"
  }
  digests {
    sha256: "\242\323D\252\321\223\313N\315\257/\224\3427\227\322\314\247\224\352\325Y)\a)\211\033\005\022\260\246Y"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-container"
    version: "2.19.1"
  }
  digests {
    sha256: "\n\022d\337\324\224\335\303\333\f@\265\030{\314\347\267\302\371Y\336\227\026*\231\246Q#\246\346y\352"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-core"
    version: "2.19.1"
  }
  digests {
    sha256: "\350\233v\360\324\226\321\f\357\033^\340\327``R\207_\322:V\256d\250\b;:aO*S\r"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-dash"
    version: "2.19.1"
  }
  digests {
    sha256: "w|\301k\037u\036V\260\304*!_\275\220W\303\b\373\372\242\333\266\363\236\r\311m1\203\250?"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-hls"
    version: "2.19.1"
  }
  digests {
    sha256: "\351\"\301\277\'b\253\257\037\3478Q\217\262$;\273#\315&5N\322\305\343\374\326\274\221g\223\252"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-rtsp"
    version: "2.19.1"
  }
  digests {
    sha256: "E\335\231\264&\016\302\0201]\211Q\310\177\205\203s\201x:\350\360d\2175\202Hf\026 \216\240"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-smoothstreaming"
    version: "2.19.1"
  }
  digests {
    sha256: "\333\022u\177\210\326\315T1\324A\332\372\214\366\204\273\205\355\3715~\212\221\342T\3107\341\024\036v"
  }
  repo_index {
    value: 2
  }
}
library {
  maven_library {
    groupId: "com.google.android.exoplayer"
    artifactId: "exoplayer-ui"
    version: "2.19.1"
  }
  digests {
    sha256: "\303X[\0046JO\371dY\n\241%6|\263\026\356\024\212\252\216\t`\254\251\224\321\352\t\005\305"
  }
  repo_index {
    value: 2
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 58
  library_dep_index: 66
  library_dep_index: 67
  library_dep_index: 39
  library_dep_index: 68
  library_dep_index: 70
  library_dep_index: 93
  library_dep_index: 77
  library_dep_index: 75
  library_dep_index: 84
  library_dep_index: 73
  library_dep_index: 97
  library_dep_index: 85
  library_dep_index: 98
  library_dep_index: 94
  library_dep_index: 95
  library_dep_index: 99
  library_dep_index: 0
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 10
  library_dep_index: 61
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 33
  library_dep_index: 20
  library_dep_index: 44
  library_dep_index: 51
  library_dep_index: 65
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 58
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 18
  library_dep_index: 24
  library_dep_index: 20
  library_dep_index: 44
  library_dep_index: 47
  library_dep_index: 51
  library_dep_index: 48
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 55
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 0
}
library_dependencies {
  library_index: 11
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 39
  library_dep_index: 56
  library_dep_index: 0
  library_dep_index: 57
  library_dep_index: 10
  library_dep_index: 0
}
library_dependencies {
  library_index: 12
  library_dep_index: 0
}
library_dependencies {
  library_index: 13
  library_dep_index: 14
}
library_dependencies {
  library_index: 14
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 15
  library_dep_index: 15
}
library_dependencies {
  library_index: 15
  library_dep_index: 13
  library_dep_index: 13
}
library_dependencies {
  library_index: 16
  library_dep_index: 8
  library_dep_index: 17
}
library_dependencies {
  library_index: 18
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 19
  library_dep_index: 8
}
library_dependencies {
  library_index: 20
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 8
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 22
  library_dep_index: 8
}
library_dependencies {
  library_index: 23
  library_dep_index: 8
  library_dep_index: 22
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
}
library_dependencies {
  library_index: 25
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 43
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 28
  library_dep_index: 0
}
library_dependencies {
  library_index: 28
  library_dep_index: 29
  library_dep_index: 26
  library_dep_index: 30
  library_dep_index: 27
}
library_dependencies {
  library_index: 29
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 0
}
library_dependencies {
  library_index: 30
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 0
}
library_dependencies {
  library_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 10
  library_dep_index: 34
  library_dep_index: 20
  library_dep_index: 44
  library_dep_index: 47
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 48
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 54
}
library_dependencies {
  library_index: 34
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 35
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 36
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 37
  library_dep_index: 8
  library_dep_index: 20
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 38
  library_dep_index: 8
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 8
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 39
  library_dep_index: 0
  library_dep_index: 39
}
library_dependencies {
  library_index: 41
  library_dep_index: 42
}
library_dependencies {
  library_index: 42
  library_dep_index: 8
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 43
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 44
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 26
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 46
  library_dep_index: 47
}
library_dependencies {
  library_index: 46
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 34
  library_dep_index: 44
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 37
  library_dep_index: 20
  library_dep_index: 41
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 46
}
library_dependencies {
  library_index: 48
  library_dep_index: 8
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 48
  library_dep_index: 0
  library_dep_index: 48
}
library_dependencies {
  library_index: 50
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 34
  library_dep_index: 44
  library_dep_index: 13
}
library_dependencies {
  library_index: 51
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 38
  library_dep_index: 17
}
library_dependencies {
  library_index: 52
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 13
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
  library_dep_index: 15
  library_dep_index: 10
  library_dep_index: 33
  library_dep_index: 36
  library_dep_index: 46
  library_dep_index: 49
  library_dep_index: 0
  library_dep_index: 33
}
library_dependencies {
  library_index: 55
  library_dep_index: 7
  library_dep_index: 41
  library_dep_index: 46
  library_dep_index: 49
  library_dep_index: 7
}
library_dependencies {
  library_index: 56
  library_dep_index: 8
  library_dep_index: 13
}
library_dependencies {
  library_index: 58
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 6
}
library_dependencies {
  library_index: 59
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 13
}
library_dependencies {
  library_index: 60
  library_dep_index: 59
  library_dep_index: 19
  library_dep_index: 13
}
library_dependencies {
  library_index: 61
  library_dep_index: 8
}
library_dependencies {
  library_index: 62
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 53
}
library_dependencies {
  library_index: 63
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 63
  library_dep_index: 63
}
library_dependencies {
  library_index: 65
  library_dep_index: 8
}
library_dependencies {
  library_index: 66
  library_dep_index: 11
}
library_dependencies {
  library_index: 67
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 19
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
  library_dep_index: 69
  library_dep_index: 75
  library_dep_index: 76
  library_dep_index: 77
  library_dep_index: 72
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 0
}
library_dependencies {
  library_index: 71
  library_dep_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 72
  library_dep_index: 11
  library_dep_index: 0
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
  library_dep_index: 69
}
library_dependencies {
  library_index: 75
  library_dep_index: 72
  library_dep_index: 0
}
library_dependencies {
  library_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 77
  library_dep_index: 72
  library_dep_index: 75
  library_dep_index: 0
}
library_dependencies {
  library_index: 78
  library_dep_index: 72
  library_dep_index: 79
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 75
  library_dep_index: 77
  library_dep_index: 76
  library_dep_index: 0
}
library_dependencies {
  library_index: 79
  library_dep_index: 69
  library_dep_index: 74
  library_dep_index: 80
  library_dep_index: 72
  library_dep_index: 77
  library_dep_index: 81
  library_dep_index: 0
  library_dep_index: 83
}
library_dependencies {
  library_index: 81
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 82
  library_dep_index: 0
}
library_dependencies {
  library_index: 82
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
  library_dep_index: 74
  library_dep_index: 80
  library_dep_index: 72
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 87
  library_dep_index: 79
  library_dep_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 88
  library_dep_index: 72
  library_dep_index: 79
}
library_dependencies {
  library_index: 89
  library_dep_index: 72
  library_dep_index: 79
  library_dep_index: 87
  library_dep_index: 69
}
library_dependencies {
  library_index: 90
  library_dep_index: 72
  library_dep_index: 79
  library_dep_index: 87
}
library_dependencies {
  library_index: 91
  library_dep_index: 79
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 69
  library_dep_index: 80
  library_dep_index: 72
}
library_dependencies {
  library_index: 92
  library_dep_index: 83
  library_dep_index: 69
  library_dep_index: 80
  library_dep_index: 72
}
library_dependencies {
  library_index: 93
  library_dep_index: 72
  library_dep_index: 79
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 77
  library_dep_index: 94
  library_dep_index: 0
}
library_dependencies {
  library_index: 94
  library_dep_index: 95
  library_dep_index: 3
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
}
library_dependencies {
  library_index: 96
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 98
  library_dep_index: 94
  library_dep_index: 3
}
library_dependencies {
  library_index: 100
  library_dep_index: 101
  library_dep_index: 102
  library_dep_index: 103
  library_dep_index: 33
  library_dep_index: 60
  library_dep_index: 104
  library_dep_index: 39
}
library_dependencies {
  library_index: 101
  library_dep_index: 8
}
library_dependencies {
  library_index: 104
  library_dep_index: 8
  library_dep_index: 57
}
library_dependencies {
  library_index: 105
  library_dep_index: 100
  library_dep_index: 94
  library_dep_index: 8
}
library_dependencies {
  library_index: 106
  library_dep_index: 100
  library_dep_index: 107
  library_dep_index: 108
}
library_dependencies {
  library_index: 108
  library_dep_index: 109
  library_dep_index: 17
  library_dep_index: 85
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 112
}
library_dependencies {
  library_index: 113
  library_dep_index: 100
}
library_dependencies {
  library_index: 115
  library_dep_index: 12
  library_dep_index: 11
  library_dep_index: 11
  library_dep_index: 35
  library_dep_index: 43
  library_dep_index: 116
  library_dep_index: 119
  library_dep_index: 38
  library_dep_index: 17
  library_dep_index: 0
}
library_dependencies {
  library_index: 116
  library_dep_index: 12
  library_dep_index: 23
  library_dep_index: 117
  library_dep_index: 118
  library_dep_index: 119
}
library_dependencies {
  library_index: 117
  library_dep_index: 8
  library_dep_index: 3
}
library_dependencies {
  library_index: 118
  library_dep_index: 8
  library_dep_index: 0
}
library_dependencies {
  library_index: 119
  library_dep_index: 8
  library_dep_index: 118
  library_dep_index: 0
}
library_dependencies {
  library_index: 120
  library_dep_index: 121
}
library_dependencies {
  library_index: 122
  library_dep_index: 8
}
library_dependencies {
  library_index: 123
  library_dep_index: 124
  library_dep_index: 111
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 125
  library_dep_index: 126
  library_dep_index: 127
  library_dep_index: 11
  library_dep_index: 62
  library_dep_index: 129
  library_dep_index: 12
  library_dep_index: 33
  library_dep_index: 20
  library_dep_index: 134
  library_dep_index: 65
  library_dep_index: 137
  library_dep_index: 59
  library_dep_index: 136
}
library_dependencies {
  library_index: 124
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 125
  library_dep_index: 8
}
library_dependencies {
  library_index: 126
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 53
}
library_dependencies {
  library_index: 127
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 128
}
library_dependencies {
  library_index: 129
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 130
}
library_dependencies {
  library_index: 130
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 131
  library_dep_index: 50
  library_dep_index: 132
  library_dep_index: 133
}
library_dependencies {
  library_index: 131
  library_dep_index: 8
}
library_dependencies {
  library_index: 132
  library_dep_index: 8
}
library_dependencies {
  library_index: 133
  library_dep_index: 8
}
library_dependencies {
  library_index: 134
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 53
  library_dep_index: 135
  library_dep_index: 136
}
library_dependencies {
  library_index: 135
  library_dep_index: 10
  library_dep_index: 0
}
library_dependencies {
  library_index: 136
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 33
  library_dep_index: 134
}
library_dependencies {
  library_index: 137
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 129
}
library_dependencies {
  library_index: 138
  library_dep_index: 139
  library_dep_index: 158
  library_dep_index: 150
  library_dep_index: 168
  library_dep_index: 182
  library_dep_index: 199
  library_dep_index: 200
  library_dep_index: 202
  library_dep_index: 217
  library_dep_index: 153
  library_dep_index: 163
  library_dep_index: 154
  library_dep_index: 218
}
library_dependencies {
  library_index: 139
  library_dep_index: 140
  library_dep_index: 149
  library_dep_index: 157
}
library_dependencies {
  library_index: 140
  library_dep_index: 13
  library_dep_index: 130
  library_dep_index: 141
  library_dep_index: 32
  library_dep_index: 142
  library_dep_index: 143
  library_dep_index: 148
  library_dep_index: 147
}
library_dependencies {
  library_index: 141
  library_dep_index: 32
}
library_dependencies {
  library_index: 142
  library_dep_index: 32
}
library_dependencies {
  library_index: 143
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 144
  library_dep_index: 145
  library_dep_index: 141
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 142
  library_dep_index: 147
  library_dep_index: 31
  library_dep_index: 108
}
library_dependencies {
  library_index: 144
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 145
}
library_dependencies {
  library_index: 145
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 10
  library_dep_index: 144
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 144
}
library_dependencies {
  library_index: 146
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 33
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 147
  library_dep_index: 130
  library_dep_index: 32
}
library_dependencies {
  library_index: 148
  library_dep_index: 32
  library_dep_index: 142
}
library_dependencies {
  library_index: 149
  library_dep_index: 141
  library_dep_index: 32
  library_dep_index: 142
  library_dep_index: 148
  library_dep_index: 31
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 154
  library_dep_index: 155
  library_dep_index: 156
  library_dep_index: 108
  library_dep_index: 0
}
library_dependencies {
  library_index: 150
  library_dep_index: 30
  library_dep_index: 151
  library_dep_index: 152
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 151
  library_dep_index: 152
  library_dep_index: 8
  library_dep_index: 111
}
library_dependencies {
  library_index: 152
  library_dep_index: 99
}
library_dependencies {
  library_index: 153
  library_dep_index: 150
  library_dep_index: 3
  library_dep_index: 151
  library_dep_index: 152
}
library_dependencies {
  library_index: 154
  library_dep_index: 31
  library_dep_index: 152
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 155
  library_dep_index: 0
}
library_dependencies {
  library_index: 155
  library_dep_index: 31
  library_dep_index: 152
}
library_dependencies {
  library_index: 156
  library_dep_index: 32
  library_dep_index: 152
}
library_dependencies {
  library_index: 157
  library_dep_index: 13
  library_dep_index: 32
  library_dep_index: 142
  library_dep_index: 143
}
library_dependencies {
  library_index: 158
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 159
  library_dep_index: 163
  library_dep_index: 165
  library_dep_index: 164
  library_dep_index: 166
  library_dep_index: 154
  library_dep_index: 155
  library_dep_index: 156
  library_dep_index: 8
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 167
  library_dep_index: 147
  library_dep_index: 111
  library_dep_index: 0
}
library_dependencies {
  library_index: 159
  library_dep_index: 8
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 3
}
library_dependencies {
  library_index: 160
  library_dep_index: 8
}
library_dependencies {
  library_index: 161
  library_dep_index: 160
  library_dep_index: 162
  library_dep_index: 163
  library_dep_index: 165
  library_dep_index: 8
}
library_dependencies {
  library_index: 162
  library_dep_index: 160
  library_dep_index: 8
  library_dep_index: 99
  library_dep_index: 163
  library_dep_index: 164
}
library_dependencies {
  library_index: 163
  library_dep_index: 8
}
library_dependencies {
  library_index: 164
  library_dep_index: 8
  library_dep_index: 163
}
library_dependencies {
  library_index: 165
  library_dep_index: 3
  library_dep_index: 8
  library_dep_index: 163
}
library_dependencies {
  library_index: 166
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 167
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 168
  library_dep_index: 169
  library_dep_index: 13
  library_dep_index: 170
  library_dep_index: 171
  library_dep_index: 33
  library_dep_index: 132
  library_dep_index: 173
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 177
  library_dep_index: 179
  library_dep_index: 152
  library_dep_index: 180
  library_dep_index: 181
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 0
}
library_dependencies {
  library_index: 169
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 19
  library_dep_index: 17
}
library_dependencies {
  library_index: 170
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 171
}
library_dependencies {
  library_index: 171
  library_dep_index: 170
  library_dep_index: 172
  library_dep_index: 175
  library_dep_index: 176
  library_dep_index: 0
  library_dep_index: 170
}
library_dependencies {
  library_index: 172
  library_dep_index: 33
  library_dep_index: 50
  library_dep_index: 173
  library_dep_index: 174
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 175
  library_dep_index: 31
}
library_dependencies {
  library_index: 173
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 174
  library_dep_index: 13
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 175
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 176
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 177
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 178
}
library_dependencies {
  library_index: 179
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 177
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 30
}
library_dependencies {
  library_index: 180
  library_dep_index: 146
  library_dep_index: 31
}
library_dependencies {
  library_index: 181
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 152
  library_dep_index: 150
}
library_dependencies {
  library_index: 182
  library_dep_index: 183
  library_dep_index: 31
  library_dep_index: 152
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 198
  library_dep_index: 163
  library_dep_index: 165
  library_dep_index: 154
  library_dep_index: 155
  library_dep_index: 156
  library_dep_index: 3
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 8
}
library_dependencies {
  library_index: 183
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 155
  library_dep_index: 152
  library_dep_index: 163
  library_dep_index: 165
  library_dep_index: 3
  library_dep_index: 154
  library_dep_index: 159
  library_dep_index: 160
  library_dep_index: 99
  library_dep_index: 8
  library_dep_index: 184
}
library_dependencies {
  library_index: 184
  library_dep_index: 185
}
library_dependencies {
  library_index: 185
  library_dep_index: 186
  library_dep_index: 194
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 186
  library_dep_index: 194
  library_dep_index: 188
  library_dep_index: 192
  library_dep_index: 196
  library_dep_index: 197
}
library_dependencies {
  library_index: 186
  library_dep_index: 187
}
library_dependencies {
  library_index: 187
  library_dep_index: 8
  library_dep_index: 188
  library_dep_index: 192
  library_dep_index: 95
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 188
  library_dep_index: 192
  library_dep_index: 184
  library_dep_index: 194
  library_dep_index: 196
  library_dep_index: 197
}
library_dependencies {
  library_index: 188
  library_dep_index: 189
}
library_dependencies {
  library_index: 189
  library_dep_index: 8
  library_dep_index: 190
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 186
  library_dep_index: 192
  library_dep_index: 184
  library_dep_index: 194
  library_dep_index: 197
  library_dep_index: 196
}
library_dependencies {
  library_index: 190
  library_dep_index: 0
  library_dep_index: 191
}
library_dependencies {
  library_index: 191
  library_dep_index: 0
}
library_dependencies {
  library_index: 192
  library_dep_index: 193
}
library_dependencies {
  library_index: 193
  library_dep_index: 188
  library_dep_index: 95
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 186
  library_dep_index: 188
  library_dep_index: 184
  library_dep_index: 194
  library_dep_index: 197
  library_dep_index: 196
}
library_dependencies {
  library_index: 194
  library_dep_index: 195
}
library_dependencies {
  library_index: 195
  library_dep_index: 188
  library_dep_index: 192
  library_dep_index: 196
  library_dep_index: 95
  library_dep_index: 0
  library_dep_index: 186
  library_dep_index: 188
  library_dep_index: 192
  library_dep_index: 184
  library_dep_index: 196
  library_dep_index: 197
}
library_dependencies {
  library_index: 196
  library_dep_index: 197
  library_dep_index: 186
  library_dep_index: 188
  library_dep_index: 192
  library_dep_index: 184
  library_dep_index: 194
  library_dep_index: 197
}
library_dependencies {
  library_index: 197
  library_dep_index: 186
  library_dep_index: 188
  library_dep_index: 192
  library_dep_index: 184
  library_dep_index: 194
  library_dep_index: 196
}
library_dependencies {
  library_index: 198
  library_dep_index: 165
  library_dep_index: 163
}
library_dependencies {
  library_index: 199
  library_dep_index: 182
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 32
}
library_dependencies {
  library_index: 200
  library_dep_index: 180
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 181
  library_dep_index: 201
  library_dep_index: 31
  library_dep_index: 8
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 201
  library_dep_index: 146
}
library_dependencies {
  library_index: 202
  library_dep_index: 203
  library_dep_index: 31
  library_dep_index: 152
  library_dep_index: 180
  library_dep_index: 181
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 201
  library_dep_index: 8
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 205
  library_dep_index: 213
  library_dep_index: 215
  library_dep_index: 216
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 203
  library_dep_index: 204
}
library_dependencies {
  library_index: 205
  library_dep_index: 206
  library_dep_index: 207
  library_dep_index: 108
}
library_dependencies {
  library_index: 206
  library_dep_index: 85
  library_dep_index: 111
  library_dep_index: 108
}
library_dependencies {
  library_index: 207
  library_dep_index: 206
  library_dep_index: 208
  library_dep_index: 209
  library_dep_index: 210
  library_dep_index: 111
  library_dep_index: 108
  library_dep_index: 211
  library_dep_index: 212
}
library_dependencies {
  library_index: 212
  library_dep_index: 206
}
library_dependencies {
  library_index: 213
  library_dep_index: 206
  library_dep_index: 214
  library_dep_index: 207
  library_dep_index: 95
  library_dep_index: 108
  library_dep_index: 211
}
library_dependencies {
  library_index: 214
  library_dep_index: 206
  library_dep_index: 207
  library_dep_index: 210
  library_dep_index: 108
}
library_dependencies {
  library_index: 215
  library_dep_index: 206
  library_dep_index: 204
  library_dep_index: 85
  library_dep_index: 108
}
library_dependencies {
  library_index: 216
  library_dep_index: 206
  library_dep_index: 108
  library_dep_index: 111
}
library_dependencies {
  library_index: 217
  library_dep_index: 152
  library_dep_index: 218
  library_dep_index: 180
  library_dep_index: 181
  library_dep_index: 150
  library_dep_index: 153
  library_dep_index: 151
  library_dep_index: 8
  library_dep_index: 146
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 218
  library_dep_index: 31
  library_dep_index: 180
  library_dep_index: 153
  library_dep_index: 8
  library_dep_index: 146
  library_dep_index: 0
}
library_dependencies {
  library_index: 220
  library_dep_index: 95
  library_dep_index: 6
}
library_dependencies {
  library_index: 221
  library_dep_index: 222
  library_dep_index: 223
}
library_dependencies {
  library_index: 224
  library_dep_index: 191
  library_dep_index: 2
  library_dep_index: 10
}
library_dependencies {
  library_index: 229
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 230
  library_dep_index: 31
}
library_dependencies {
  library_index: 230
  library_dep_index: 32
}
library_dependencies {
  library_index: 231
  library_dep_index: 68
  library_dep_index: 97
  library_dep_index: 8
}
library_dependencies {
  library_index: 232
  library_dep_index: 8
  library_dep_index: 32
}
library_dependencies {
  library_index: 233
  library_dep_index: 169
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 144
  library_dep_index: 145
  library_dep_index: 234
  library_dep_index: 235
  library_dep_index: 141
  library_dep_index: 236
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 17
}
library_dependencies {
  library_index: 234
  library_dep_index: 8
  library_dep_index: 11
}
library_dependencies {
  library_index: 235
  library_dep_index: 169
  library_dep_index: 115
  library_dep_index: 32
  library_dep_index: 148
  library_dep_index: 232
}
library_dependencies {
  library_index: 236
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 237
  library_dep_index: 6
  library_dep_index: 104
  library_dep_index: 137
  library_dep_index: 94
}
library_dependencies {
  library_index: 238
  library_dep_index: 239
  library_dep_index: 172
  library_dep_index: 173
  library_dep_index: 31
  library_dep_index: 240
  library_dep_index: 2
}
library_dependencies {
  library_index: 239
  library_dep_index: 172
  library_dep_index: 173
  library_dep_index: 31
  library_dep_index: 240
  library_dep_index: 38
  library_dep_index: 2
}
library_dependencies {
  library_index: 240
  library_dep_index: 33
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 241
  library_dep_index: 242
  library_dep_index: 31
}
library_dependencies {
  library_index: 241
  library_dep_index: 146
  library_dep_index: 32
}
library_dependencies {
  library_index: 242
  library_dep_index: 33
  library_dep_index: 146
  library_dep_index: 32
}
library_dependencies {
  library_index: 244
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 104
  library_dep_index: 245
  library_dep_index: 246
  library_dep_index: 248
  library_dep_index: 250
  library_dep_index: 254
  library_dep_index: 247
}
library_dependencies {
  library_index: 245
  library_dep_index: 8
  library_dep_index: 108
  library_dep_index: 12
  library_dep_index: 246
  library_dep_index: 247
  library_dep_index: 248
  library_dep_index: 249
  library_dep_index: 250
  library_dep_index: 244
  library_dep_index: 251
  library_dep_index: 252
  library_dep_index: 253
  library_dep_index: 254
  library_dep_index: 255
  library_dep_index: 257
}
library_dependencies {
  library_index: 246
  library_dep_index: 8
  library_dep_index: 245
}
library_dependencies {
  library_index: 247
  library_dep_index: 245
  library_dep_index: 8
}
library_dependencies {
  library_index: 248
  library_dep_index: 245
  library_dep_index: 247
  library_dep_index: 8
  library_dep_index: 104
}
library_dependencies {
  library_index: 249
  library_dep_index: 245
  library_dep_index: 248
  library_dep_index: 8
  library_dep_index: 94
}
library_dependencies {
  library_index: 250
  library_dep_index: 245
  library_dep_index: 8
}
library_dependencies {
  library_index: 251
  library_dep_index: 244
  library_dep_index: 8
}
library_dependencies {
  library_index: 252
  library_dep_index: 8
  library_dep_index: 244
}
library_dependencies {
  library_index: 253
  library_dep_index: 244
  library_dep_index: 8
}
library_dependencies {
  library_index: 254
  library_dep_index: 8
  library_dep_index: 245
  library_dep_index: 246
  library_dep_index: 250
}
library_dependencies {
  library_index: 255
  library_dep_index: 248
  library_dep_index: 13
  library_dep_index: 256
  library_dep_index: 11
  library_dep_index: 245
}
library_dependencies {
  library_index: 256
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 11
  library_dep_index: 11
}
library_dependencies {
  library_index: 257
  library_dep_index: 245
  library_dep_index: 256
  library_dep_index: 8
  library_dep_index: 134
}
library_dependencies {
  library_index: 258
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 16
  library_dep_index: 259
  library_dep_index: 11
  library_dep_index: 104
  library_dep_index: 24
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 260
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 261
  library_dep_index: 262
  library_dep_index: 263
  library_dep_index: 264
  library_dep_index: 265
}
library_dependencies {
  library_index: 259
  library_dep_index: 16
  library_dep_index: 0
  library_dep_index: 26
}
library_dependencies {
  library_index: 261
  library_dep_index: 8
  library_dep_index: 258
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 39
  library_dep_index: 260
  library_dep_index: 17
  library_dep_index: 258
  library_dep_index: 262
  library_dep_index: 263
  library_dep_index: 264
  library_dep_index: 265
}
library_dependencies {
  library_index: 262
  library_dep_index: 258
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 260
  library_dep_index: 17
  library_dep_index: 261
  library_dep_index: 258
  library_dep_index: 263
  library_dep_index: 264
  library_dep_index: 265
}
library_dependencies {
  library_index: 263
  library_dep_index: 258
  library_dep_index: 16
  library_dep_index: 259
  library_dep_index: 11
  library_dep_index: 24
  library_dep_index: 40
  library_dep_index: 260
  library_dep_index: 17
  library_dep_index: 29
  library_dep_index: 261
  library_dep_index: 258
  library_dep_index: 262
  library_dep_index: 264
  library_dep_index: 265
}
library_dependencies {
  library_index: 264
  library_dep_index: 8
  library_dep_index: 258
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 260
  library_dep_index: 261
  library_dep_index: 258
  library_dep_index: 262
  library_dep_index: 263
  library_dep_index: 265
}
library_dependencies {
  library_index: 265
  library_dep_index: 8
  library_dep_index: 12
  library_dep_index: 6
  library_dep_index: 258
  library_dep_index: 263
  library_dep_index: 264
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 24
  library_dep_index: 260
  library_dep_index: 17
  library_dep_index: 261
  library_dep_index: 258
  library_dep_index: 262
  library_dep_index: 263
  library_dep_index: 264
}
library_dependencies {
  library_index: 266
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 267
  library_dep_index: 151
  library_dep_index: 163
  library_dep_index: 165
  library_dep_index: 268
  library_dep_index: 270
  library_dep_index: 269
  library_dep_index: 271
}
library_dependencies {
  library_index: 268
  library_dep_index: 32
  library_dep_index: 269
}
library_dependencies {
  library_index: 269
  library_dep_index: 104
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 267
  library_dep_index: 151
  library_dep_index: 163
  library_dep_index: 165
  library_dep_index: 270
}
library_dependencies {
  library_index: 270
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 160
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 146
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 151
  library_dep_index: 163
  library_dep_index: 165
}
library_dependencies {
  library_index: 271
  library_dep_index: 32
  library_dep_index: 31
}
library_dependencies {
  library_index: 272
  library_dep_index: 190
  library_dep_index: 10
  library_dep_index: 6
  library_dep_index: 273
  library_dep_index: 127
  library_dep_index: 46
  library_dep_index: 26
  library_dep_index: 108
  library_dep_index: 274
  library_dep_index: 275
  library_dep_index: 276
  library_dep_index: 277
  library_dep_index: 141
  library_dep_index: 236
  library_dep_index: 100
  library_dep_index: 67
  library_dep_index: 279
  library_dep_index: 37
  library_dep_index: 0
}
library_dependencies {
  library_index: 273
  library_dep_index: 115
  library_dep_index: 0
  library_dep_index: 29
}
library_dependencies {
  library_index: 274
  library_dep_index: 94
}
library_dependencies {
  library_index: 275
  library_dep_index: 274
  library_dep_index: 208
}
library_dependencies {
  library_index: 276
  library_dep_index: 94
  library_dep_index: 3
}
library_dependencies {
  library_index: 277
  library_dep_index: 278
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 11
  library_dep_index: 260
  library_dep_index: 17
}
library_dependencies {
  library_index: 278
  library_dep_index: 8
}
library_dependencies {
  library_index: 279
  library_dep_index: 274
  library_dep_index: 208
  library_dep_index: 275
  library_dep_index: 276
  library_dep_index: 94
  library_dep_index: 280
  library_dep_index: 11
  library_dep_index: 116
  library_dep_index: 281
  library_dep_index: 273
  library_dep_index: 277
  library_dep_index: 108
  library_dep_index: 3
}
library_dependencies {
  library_index: 280
  library_dep_index: 94
  library_dep_index: 3
}
library_dependencies {
  library_index: 281
  library_dep_index: 117
  library_dep_index: 116
  library_dep_index: 0
  library_dep_index: 29
}
library_dependencies {
  library_index: 282
  library_dep_index: 283
  library_dep_index: 284
  library_dep_index: 285
  library_dep_index: 286
  library_dep_index: 287
  library_dep_index: 289
  library_dep_index: 290
  library_dep_index: 291
  library_dep_index: 292
  library_dep_index: 293
  library_dep_index: 294
}
library_dependencies {
  library_index: 283
  library_dep_index: 8
  library_dep_index: 108
}
library_dependencies {
  library_index: 284
  library_dep_index: 283
  library_dep_index: 8
}
library_dependencies {
  library_index: 285
  library_dep_index: 283
  library_dep_index: 284
  library_dep_index: 8
}
library_dependencies {
  library_index: 286
  library_dep_index: 283
  library_dep_index: 8
}
library_dependencies {
  library_index: 287
  library_dep_index: 8
  library_dep_index: 283
  library_dep_index: 288
  library_dep_index: 286
}
library_dependencies {
  library_index: 288
  library_dep_index: 283
  library_dep_index: 8
}
library_dependencies {
  library_index: 289
  library_dep_index: 8
  library_dep_index: 11
  library_dep_index: 283
  library_dep_index: 288
  library_dep_index: 285
  library_dep_index: 286
  library_dep_index: 287
  library_dep_index: 284
}
library_dependencies {
  library_index: 290
  library_dep_index: 289
  library_dep_index: 8
}
library_dependencies {
  library_index: 291
  library_dep_index: 8
  library_dep_index: 289
}
library_dependencies {
  library_index: 292
  library_dep_index: 8
  library_dep_index: 289
}
library_dependencies {
  library_index: 293
  library_dep_index: 289
  library_dep_index: 8
}
library_dependencies {
  library_index: 294
  library_dep_index: 283
  library_dep_index: 8
  library_dep_index: 134
  library_dep_index: 256
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 272
  dependency_index: 138
  dependency_index: 139
  dependency_index: 158
  dependency_index: 282
  dependency_index: 289
  dependency_index: 294
  dependency_index: 291
  dependency_index: 293
  dependency_index: 290
  dependency_index: 231
  dependency_index: 100
  dependency_index: 105
  dependency_index: 106
  dependency_index: 113
  dependency_index: 114
  dependency_index: 108
  dependency_index: 16
  dependency_index: 115
  dependency_index: 120
  dependency_index: 37
  dependency_index: 2
  dependency_index: 122
  dependency_index: 123
  dependency_index: 10
  dependency_index: 150
  dependency_index: 172
  dependency_index: 168
  dependency_index: 182
  dependency_index: 199
  dependency_index: 200
  dependency_index: 202
  dependency_index: 217
  dependency_index: 219
  dependency_index: 220
  dependency_index: 132
  dependency_index: 26
  dependency_index: 29
  dependency_index: 221
  dependency_index: 224
  dependency_index: 225
  dependency_index: 226
  dependency_index: 227
  dependency_index: 228
  dependency_index: 6
  dependency_index: 229
  dependency_index: 97
  dependency_index: 137
  dependency_index: 11
  dependency_index: 70
  dependency_index: 93
  dependency_index: 77
  dependency_index: 232
  dependency_index: 233
  dependency_index: 67
  dependency_index: 237
  dependency_index: 7
  dependency_index: 104
  dependency_index: 136
  dependency_index: 238
  dependency_index: 243
  dependency_index: 54
  dependency_index: 126
  dependency_index: 46
  dependency_index: 55
  dependency_index: 244
  dependency_index: 253
  dependency_index: 251
  dependency_index: 252
  dependency_index: 249
  dependency_index: 257
  dependency_index: 255
  dependency_index: 248
  dependency_index: 245
  dependency_index: 258
  dependency_index: 261
  dependency_index: 263
  dependency_index: 264
  dependency_index: 265
  dependency_index: 262
  dependency_index: 24
  dependency_index: 266
  dependency_index: 234
  dependency_index: 256
}
repositories {
  maven_repo {
    url: "https://oss.sonatype.org/content/repositories/snapshots/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://www.jitpack.io"
  }
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo1.maven.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
