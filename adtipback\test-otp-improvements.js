const otpService = require('./services/OTPService');
const { queryRunner } = require('./dbConfig/queryRunner');

async function testOTPImprovements() {
  console.log('🧪 Testing OTP Improvements...\n');

  const testMobileNumber = '9885002290';
  const testMessageId = 'test-message-id-' + Date.now();

  try {
    // Test 1: Generate OTP
    console.log('1️⃣ Testing OTP Generation...');
    const generateResult = await otpService.generateOTP(testMobileNumber, testMessageId);
    console.log('✅ OTP Generation Result:', generateResult);
    
    // Test 2: Verify OTP
    console.log('\n2️⃣ Testing OTP Verification...');
    const verifyResult = await otpService.verifyOTP(testMobileNumber, generateResult.otp);
    console.log('✅ OTP Verification Result:', verifyResult);

    // Test 3: Concurrent OTP Generation (simulate multiple requests)
    console.log('\n3️⃣ Testing Concurrent OTP Generation...');
    const concurrentPromises = [];
    for (let i = 0; i < 5; i++) {
      const mobileNumber = `98850022${i}`;
      const messageId = `concurrent-test-${i}-${Date.now()}`;
      concurrentPromises.push(
        otpService.generateOTP(mobileNumber, messageId)
          .then(result => ({ success: true, mobileNumber, result }))
          .catch(error => ({ success: false, mobileNumber, error: error.message }))
      );
    }
    
    const concurrentResults = await Promise.all(concurrentPromises);
    console.log('✅ Concurrent OTP Generation Results:');
    concurrentResults.forEach((result, index) => {
      console.log(`   ${index + 1}. ${result.mobileNumber}: ${result.success ? '✅ Success' : '❌ Failed - ' + result.error}`);
    });

    // Test 4: Rate Limiting
    console.log('\n4️⃣ Testing Rate Limiting...');
    try {
      const rapidOTP = await otpService.generateOTP(testMobileNumber, 'rapid-test-1');
      console.log('✅ First rapid OTP generated');
      
      // Try to generate another OTP immediately (should be rate limited)
      const rapidOTP2 = await otpService.resendOTP(testMobileNumber, 'rapid-test-2');
      console.log('⚠️  Rate limiting result:', rapidOTP2);
    } catch (error) {
      console.log('✅ Rate limiting working as expected:', error.message);
    }

    // Test 5: Database Health Check
    console.log('\n5️⃣ Testing Database Health...');
    const healthQuery = 'SELECT COUNT(*) as user_count FROM users WHERE mobile_number LIKE "98850022%"';
    const healthResult = await queryRunner(healthQuery);
    console.log('✅ Database Health Check:', healthResult);

    console.log('\n🎉 All OTP improvement tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log('   ✅ OTP Generation with transaction handling');
    console.log('   ✅ OTP Verification with proper error handling');
    console.log('   ✅ Concurrent request handling');
    console.log('   ✅ Rate limiting protection');
    console.log('   ✅ Database health monitoring');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testOTPImprovements()
    .then(() => {
      console.log('\n✅ OTP improvements test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ OTP improvements test failed:', error);
      process.exit(1);
    });
}

module.exports = { testOTPImprovements }; 