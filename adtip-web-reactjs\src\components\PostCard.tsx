import { memo } from 'react';
import { Post } from '../types/api';
import RandomAvatar, { getRandomAvatar } from './RandomAvatar';
import OptimizedImage from './OptimizedImage';

interface PostCardProps {
  post: Post;
  onPostClick: (id: number) => void;
}

const PostCard = memo(({ post, onPostClick }: PostCardProps) => (
  <article
    className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden cursor-pointer w-full transition-all duration-200 hover:shadow-md hover:border-gray-300"
    onClick={() => onPostClick(post.id)}
  >
    {/* Header */}
    <header className="flex items-center px-4 py-4">
      <div className="flex items-center flex-1 min-w-0">
        {post.user_profile_image ? (
          <img
            src={post.user_profile_image}
            alt={post.user_name || "User"}
            className="w-10 h-10 rounded-full object-cover border border-gray-200 flex-shrink-0"
            onError={(e) => {
              (e.target as HTMLImageElement).src = getRandomAvatar(post.user_id || post.user_name || post.id);
            }}
          />
        ) : (
          <RandomAvatar
            seed={post.user_id || post.user_name || post.id}
            alt={post.user_name || "User"}
            className="w-10 h-10 rounded-full object-cover border border-gray-200 flex-shrink-0"
          />
        )}
        <div className="ml-3 flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-sm text-gray-900 truncate">{post.user_name}</span>
            {post.is_promoted && (
              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-adtip-teal/10 text-adtip-teal">
                Sponsored
              </span>
            )}
          </div>
          <p className="text-xs text-gray-500 truncate">{post.address}</p>
        </div>
      </div>
      <button className="ml-3 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors">
        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
        </svg>
      </button>
    </header>

    {/* Media */}
    {post.media_url && post.media_url !== 'null' && post.media_url.trim() !== '' && (
      <div className="relative bg-gray-100" style={{ aspectRatio: '4/3' }}>
        {post.media_type === "video" ? (
          <video
            className="w-full h-full object-cover"
            controls
            preload="metadata"
            poster={post.thumbnail_url}
            onError={(e) => {
              console.error('Video failed to load:', post.media_url);
              // Hide the video element on error
              (e.target as HTMLVideoElement).style.display = 'none';
            }}
          >
            <source src={post.media_url} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : (
          <OptimizedImage
            src={post.media_url}
            alt={post.title || 'Post image'}
            className="w-full h-full object-cover"
            width={600}
            height={450}
            loading="lazy"
            quality={85}
            fallback="https://via.placeholder.com/600x450/e5e7eb/9ca3af?text=Image+Not+Available"
            onError={(error) => {
              console.error('Image failed to load:', post.media_url, error);
            }}
          />
        )}
      </div>
    )}

    {/* Content */}
    <div className="px-4 py-4">
      {/* Action bar */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-6">
          <button className="flex items-center gap-2 text-gray-600 hover:text-red-500 transition-colors">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>
            <span className="text-sm font-medium">{post.likeCount}</span>
          </button>
          <button className="flex items-center gap-2 text-gray-600 hover:text-blue-500 transition-colors">
            <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10Z"/>
            </svg>
            <span className="text-sm font-medium">{post.commentCount}</span>
          </button>
        </div>
        <div className="text-sm text-gray-500">{post.views || 0} views</div>
      </div>

      <div className="mt-3">
        <h3 className="font-semibold text-base mb-1 text-gray-900 line-clamp-2 leading-tight">{post.title}</h3>
        <p className="text-sm text-gray-700 line-clamp-3 leading-relaxed">{post.content}</p>
      </div>
    </div>
  </article>
));

PostCard.displayName = 'PostCard';

export default PostCard;
