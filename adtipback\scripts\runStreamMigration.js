// adtipback/scripts/runStreamMigration.js
// Execute the Stream inventory migration

const StreamInventoryMigrator = require('./migrateStreamInventory');
const VideoInventoryAnalyzer = require('./analyzeVideoInventory');

async function runMigration() {
  console.log('🚀 CLOUDFLARE STREAM INVENTORY MIGRATION');
  console.log('========================================\n');

  try {
    // Step 1: Run analysis first
    console.log('📊 Step 1: Analyzing current state...');
    const analyzer = new VideoInventoryAnalyzer();
    const report = await analyzer.generateMigrationReport();

    console.log('\n📋 ANALYSIS SUMMARY:');
    console.log(`Database videos: ${report.database.analysis.total}`);
    console.log(`Videos needing Stream IDs: ${report.database.analysis.noStreamId}`);
    console.log(`Stream videos ready: ${report.stream.streamAnalysis.ready}`);
    console.log(`Current Stream-ready videos in DB: ${report.database.analysis.streamReady}`);

    // Step 2: Ask for confirmation
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const question = (query) => new Promise(resolve => rl.question(query, resolve));

    console.log('\n⚠️  MIGRATION OPTIONS:');
    console.log('1. DRY RUN - Analyze matches without making changes');
    console.log('2. LIVE RUN - Execute actual database updates');
    console.log('3. CANCEL - Exit without changes');

    const choice = await question('\nEnter your choice (1/2/3): ');

    if (choice === '3') {
      console.log('❌ Migration cancelled by user');
      rl.close();
      return;
    }

    const dryRun = choice === '1';
    const batchSize = parseInt(await question('Enter batch size (default 50): ') || '50');

    rl.close();

    // Step 3: Run migration
    console.log(`\n🔄 Step 2: Running migration (${dryRun ? 'DRY RUN' : 'LIVE RUN'})...`);
    
    const migrator = new StreamInventoryMigrator();
    const result = await migrator.migrate({ dryRun, batchSize });

    console.log('\n✅ MIGRATION COMPLETED SUCCESSFULLY!');
    console.log(`Processed: ${result.processed} matches`);
    console.log(`Mode: ${result.dryRun ? 'DRY RUN' : 'LIVE RUN'}`);

    if (dryRun) {
      console.log('\n💡 To execute actual updates, run this script again and choose option 2');
    } else {
      console.log('\n🎉 Database has been updated with Stream URLs!');
      
      // Run analysis again to show new state
      console.log('\n📊 POST-MIGRATION ANALYSIS:');
      const postReport = await analyzer.generateMigrationReport();
      console.log(`Videos with Stream IDs: ${postReport.database.analysis.withStreamId}`);
      console.log(`Stream-ready videos: ${postReport.database.analysis.streamReady}`);
    }

  } catch (error) {
    console.error('\n❌ MIGRATION FAILED:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('\n👋 Migration process completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Fatal error:', error);
      process.exit(1);
    });
}

module.exports = { runMigration };
