-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Desktop/Call<PERSON>eepExample/android/app/src/main/AndroidManifest.xml:1:1-54:12
MERGED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:1:1-54:12
INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:2:1-9:12
INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:2:1-9:12
INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:2:1-9:12
MERGED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-14:12
MERGED from [:react-native-get-random-values] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-get-random-values/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:2:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5bca0cfaae241dea23eba63d623ea919/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/625ed137c6a3f5343b71917adedb437c/transformed/appcompat-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5d3738e50d488dad5737cb7b74d6eee7/transformed/fresco-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/00b03a8634c2252121ce18467cd792bc/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/956159063502be591b57ff4f5aaf7fef/transformed/drawee-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ea21ba60f5ca5e3b6210a51d223864a5/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0683b07cae0070ba68ff9dde96117a8d/transformed/memory-type-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6f217aceb2b4a0e81a7da8af6300133a/transformed/memory-type-java-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a8fa3656c0c691a623e836ced0654fbc/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5441e925cbbaab8558033fba83f56098/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7b381a187a8ac91ce3a8025ac0d6bf59/transformed/imagepipeline-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/efdea0ab8419de6825226b7e4412a067/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/4b0f30d4033881bf2d5cc4fa54e2172f/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d0765f5d48b93ec5a97559fdf7a2b17/transformed/urimod-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/27a46c0c30537e82f0dcff3c23499704/transformed/vito-source-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3447548837983fe444c234e94e57b7f4/transformed/middleware-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e35f83bd7e41599aceaf0d4ce5d0c0c8/transformed/ui-common-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.14.1/transforms/dec5fcd1d70b95b517bd22f6c9324073/transformed/fragment-1.5.4/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5104fe6d5a278875704c5617fdc0d9bb/transformed/activity-1.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a3cb7bf04a84099f6bc24510b14a8d32/transformed/autofill-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bfef782026e93cfc945e58eb2e9dfb5e/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/438756c305d966844c839f9a5b7f6149/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6f803d8da21c4dc075759336e7569e71/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f754c710f4479097018481dcee972409/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/75f30127dc54fec758f4aa7347dd0654/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/da71b07372af9691115e11798ef05622/transformed/soloader-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c5b879471b4556be91eedbd9dab05d36/transformed/fbcore-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2af4298c48b6932450e120370af731ca/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/62efb99be2fe7bdd45c0503d9f950b84/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/db52d1003d1ae1c20ba2e3486e74ea7c/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/457018029e361bb933ad4db9f52194f2/transformed/core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/5f7cdf5cb9559c5edcdf8f836411df92/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/dd91f2fa8a18639f14a2c2b25180debc/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/1e8ac3c34f4ad66978c651e59c9ade22/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/17365bb8d44a96e8cb68a585945c3ff1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/824d221d5c56526c3c6ddbae0b799560/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/875a7da5ac63e51a49a89062b608b501/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cdbc2fc941170b6004e5d876c052f435/transformed/ui-core-3.6.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/816951bc71efb4c99db481b15113489d/transformed/hermes-android-0.80.1-debug/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/aa47678c27f278fb59b12debc3f458b4/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/f8caf368debc3686ade571a4ec56cfe6/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/8e4ed2d5ba987a30decdd4c587d8ba80/transformed/tracing-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bf3dbfc63c3eda7a1c918d0d25cfb4e6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5db4160556851438fa19525aa3f72de9/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/73633f5366d7e42eb33ae1b58ff38e87/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cfda843d221c7111e1c82fff2513522e/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c3096af9d9b6d92e9258ff81f7684ec0/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:2:1-17:12
	package
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:2:5-34
		INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:4:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:4:22-64
uses-permission#android.permission.BIND_TELECOM_CONNECTION_SERVICE
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:5:5-89
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:5:22-87
uses-permission#android.permission.READ_PHONE_STATE
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:6:5-75
MERGED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-10:38
MERGED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-10:38
	android:maxSdkVersion
		ADDED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-35
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:6:22-72
uses-permission#android.permission.CALL_PHONE
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:7:5-69
MERGED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-69
MERGED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-69
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:7:22-66
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:8:5-77
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:8:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:9:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:9:22-78
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:10:5-77
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:16:5-78
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:10:22-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:11:5-71
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:11:22-68
uses-permission#android.permission.FOREGROUND_SERVICE_MICROPHONE
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:12:5-88
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:12:22-85
uses-permission#android.permission.FOREGROUND_SERVICE_CAMERA
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:13:5-84
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:13:22-81
application
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:16:5-53:19
MERGED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:16:5-53:19
MERGED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:16:5-53:19
INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:18:5-22:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/f8caf368debc3686ade571a4ec56cfe6/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/f8caf368debc3686ade571a4ec56cfe6/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/73633f5366d7e42eb33ae1b58ff38e87/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/73633f5366d7e42eb33ae1b58ff38e87/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:23:9-35
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:23:9-35
	android:label
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:18:9-41
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:18:9-41
	tools:ignore
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:20:9-54
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:20:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:7:9-29
	android:icon
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:19:9-43
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:21:9-36
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:21:9-36
	android:theme
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:22:9-40
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:22:9-40
	android:usesCleartextTraffic
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:6:9-44
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:17:9-40
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:17:9-40
activity#com.callkeepexample.MainActivity
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:24:9-35:20
	android:label
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:26:13-45
	android:launchMode
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:28:13-44
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:29:13-55
	android:exported
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:30:13-36
	android:configChanges
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:27:13-122
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:25:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:31:13-34:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:32:17-69
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:32:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:33:17-77
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:33:27-74
service#io.wazo.callkeep.VoiceConnectionService
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:36:9-45:19
	android:label
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:38:13-33
	android:exported
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:41:13-36
	android:permission
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:39:13-84
	android:foregroundServiceType
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:40:13-62
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:37:13-67
intent-filter#action:name:android.telecom.ConnectionService
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:42:13-44:29
action#android.telecom.ConnectionService
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:43:17-76
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:43:25-73
receiver#io.wazo.callkeep.VoiceBroadcastReceiver
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:46:9-52:20
	android:exported
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:47:13-36
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:46:19-73
intent-filter#action:name:io.wazo.callkeep.ACTION_ANSWER_CALL+action:name:io.wazo.callkeep.ACTION_END_CALL
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:48:13-51:29
action#io.wazo.callkeep.ACTION_ANSWER_CALL
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:49:17-77
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:49:25-75
action#io.wazo.callkeep.ACTION_END_CALL
ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:50:17-74
	android:name
		ADDED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:50:25-72
uses-sdk
INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml
MERGED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-get-random-values/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:react-native-get-random-values] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-get-random-values/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:10:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5bca0cfaae241dea23eba63d623ea919/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5bca0cfaae241dea23eba63d623ea919/transformed/appcompat-resources-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/625ed137c6a3f5343b71917adedb437c/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/625ed137c6a3f5343b71917adedb437c/transformed/appcompat-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5d3738e50d488dad5737cb7b74d6eee7/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5d3738e50d488dad5737cb7b74d6eee7/transformed/fresco-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/00b03a8634c2252121ce18467cd792bc/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/00b03a8634c2252121ce18467cd792bc/transformed/imagepipeline-okhttp3-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/956159063502be591b57ff4f5aaf7fef/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/956159063502be591b57ff4f5aaf7fef/transformed/drawee-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ea21ba60f5ca5e3b6210a51d223864a5/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/ea21ba60f5ca5e3b6210a51d223864a5/transformed/nativeimagefilters-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0683b07cae0070ba68ff9dde96117a8d/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/0683b07cae0070ba68ff9dde96117a8d/transformed/memory-type-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6f217aceb2b4a0e81a7da8af6300133a/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6f217aceb2b4a0e81a7da8af6300133a/transformed/memory-type-java-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a8fa3656c0c691a623e836ced0654fbc/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a8fa3656c0c691a623e836ced0654fbc/transformed/imagepipeline-native-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5441e925cbbaab8558033fba83f56098/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5441e925cbbaab8558033fba83f56098/transformed/memory-type-ashmem-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7b381a187a8ac91ce3a8025ac0d6bf59/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/7b381a187a8ac91ce3a8025ac0d6bf59/transformed/imagepipeline-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/efdea0ab8419de6825226b7e4412a067/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/efdea0ab8419de6825226b7e4412a067/transformed/nativeimagetranscoder-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/4b0f30d4033881bf2d5cc4fa54e2172f/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/4b0f30d4033881bf2d5cc4fa54e2172f/transformed/imagepipeline-base-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d0765f5d48b93ec5a97559fdf7a2b17/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d0765f5d48b93ec5a97559fdf7a2b17/transformed/urimod-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/27a46c0c30537e82f0dcff3c23499704/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/27a46c0c30537e82f0dcff3c23499704/transformed/vito-source-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3447548837983fe444c234e94e57b7f4/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/3447548837983fe444c234e94e57b7f4/transformed/middleware-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e35f83bd7e41599aceaf0d4ce5d0c0c8/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/e35f83bd7e41599aceaf0d4ce5d0c0c8/transformed/ui-common-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.14.1/transforms/dec5fcd1d70b95b517bd22f6c9324073/transformed/fragment-1.5.4/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] /Users/<USER>/.gradle/caches/8.14.1/transforms/dec5fcd1d70b95b517bd22f6c9324073/transformed/fragment-1.5.4/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5104fe6d5a278875704c5617fdc0d9bb/transformed/activity-1.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5104fe6d5a278875704c5617fdc0d9bb/transformed/activity-1.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a3cb7bf04a84099f6bc24510b14a8d32/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/a3cb7bf04a84099f6bc24510b14a8d32/transformed/autofill-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bfef782026e93cfc945e58eb2e9dfb5e/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bfef782026e93cfc945e58eb2e9dfb5e/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/438756c305d966844c839f9a5b7f6149/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/438756c305d966844c839f9a5b7f6149/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6f803d8da21c4dc075759336e7569e71/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/6f803d8da21c4dc075759336e7569e71/transformed/emoji2-views-helper-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f754c710f4479097018481dcee972409/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/f754c710f4479097018481dcee972409/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/75f30127dc54fec758f4aa7347dd0654/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/75f30127dc54fec758f4aa7347dd0654/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/da71b07372af9691115e11798ef05622/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/da71b07372af9691115e11798ef05622/transformed/soloader-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c5b879471b4556be91eedbd9dab05d36/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c5b879471b4556be91eedbd9dab05d36/transformed/fbcore-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2af4298c48b6932450e120370af731ca/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/2af4298c48b6932450e120370af731ca/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/62efb99be2fe7bdd45c0503d9f950b84/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/62efb99be2fe7bdd45c0503d9f950b84/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/db52d1003d1ae1c20ba2e3486e74ea7c/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/db52d1003d1ae1c20ba2e3486e74ea7c/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/457018029e361bb933ad4db9f52194f2/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/457018029e361bb933ad4db9f52194f2/transformed/core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/5f7cdf5cb9559c5edcdf8f836411df92/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/5f7cdf5cb9559c5edcdf8f836411df92/transformed/lifecycle-viewmodel-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/dd91f2fa8a18639f14a2c2b25180debc/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/dd91f2fa8a18639f14a2c2b25180debc/transformed/lifecycle-livedata-core-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/1e8ac3c34f4ad66978c651e59c9ade22/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/1e8ac3c34f4ad66978c651e59c9ade22/transformed/lifecycle-livedata-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/17365bb8d44a96e8cb68a585945c3ff1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/17365bb8d44a96e8cb68a585945c3ff1/transformed/lifecycle-runtime-2.6.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/824d221d5c56526c3c6ddbae0b799560/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/824d221d5c56526c3c6ddbae0b799560/transformed/lifecycle-viewmodel-savedstate-2.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/875a7da5ac63e51a49a89062b608b501/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/875a7da5ac63e51a49a89062b608b501/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cdbc2fc941170b6004e5d876c052f435/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cdbc2fc941170b6004e5d876c052f435/transformed/ui-core-3.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/816951bc71efb4c99db481b15113489d/transformed/hermes-android-0.80.1-debug/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/816951bc71efb4c99db481b15113489d/transformed/hermes-android-0.80.1-debug/AndroidManifest.xml:5:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/aa47678c27f278fb59b12debc3f458b4/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/aa47678c27f278fb59b12debc3f458b4/transformed/localbroadcastmanager-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/f8caf368debc3686ade571a4ec56cfe6/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/f8caf368debc3686ade571a4ec56cfe6/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/8e4ed2d5ba987a30decdd4c587d8ba80/transformed/tracing-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/8e4ed2d5ba987a30decdd4c587d8ba80/transformed/tracing-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bf3dbfc63c3eda7a1c918d0d25cfb4e6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/bf3dbfc63c3eda7a1c918d0d25cfb4e6/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5db4160556851438fa19525aa3f72de9/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/5db4160556851438fa19525aa3f72de9/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/73633f5366d7e42eb33ae1b58ff38e87/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/73633f5366d7e42eb33ae1b58ff38e87/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cfda843d221c7111e1c82fff2513522e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/cfda843d221c7111e1c82fff2513522e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c3096af9d9b6d92e9258ff81f7684ec0/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/c3096af9d9b6d92e9258ff81f7684ec0/transformed/annotation-experimental-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml
uses-permission#android.permission.READ_PHONE_NUMBERS
ADDED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-77
	android:name
		ADDED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:22-74
uses-permission#android.permission.MANAGE_OWN_CALLS
ADDED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-75
	android:name
		ADDED from [:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:22-72
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:20:13-77
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/f8caf368debc3686ade571a4ec56cfe6/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/f8caf368debc3686ade571a4ec56cfe6/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.callkeepexample.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.callkeepexample.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:13:13-57
