-- Add external_id column to messages table for linking FCM messages to database messages
-- This allows us to track which database message corresponds to which FCM message

ALTER TABLE messages 
ADD COLUMN external_id VARCHAR(255) NULL COMMENT 'External ID from FCM or other systems' 
AFTER id;

-- Add index for external_id for faster lookups
ALTER TABLE messages 
ADD INDEX idx_external_id (external_id);

-- Update existing messages to have NULL external_id (they're already NULL by default)
-- This is just for documentation purposes
-- UPDATE messages SET external_id = NULL WHERE external_id IS NULL;
