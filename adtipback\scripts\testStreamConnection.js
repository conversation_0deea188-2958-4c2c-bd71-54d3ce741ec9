// adtipback/scripts/testStreamConnection.js
// Test Cloudflare Stream API connection

require('dotenv').config();
const fetch = require('node-fetch');

async function testStreamConnection() {
  const accountId = process.env.CLOUDFLARE_ACCOUNT_ID || '94e2ffe1e7d5daf0d3de8d11c55dd2d6';
  const apiToken = process.env.CLOUDFLARE_STREAM_API_TOKEN || process.env.CLOUD_FARE_TOKEN_VALUE || '6h1Svn_NmQpWHuLZD8o7OEq23PXy5Y-UneEH9rUu';
  
  console.log('🔍 Testing Cloudflare Stream API Connection');
  console.log('==========================================');
  console.log(`Account ID: ${accountId}`);
  console.log(`API Token: ${apiToken.substring(0, 10)}...`);
  
  const baseUrl = `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream`;
  console.log(`Base URL: ${baseUrl}`);
  
  try {
    console.log('\n📡 Testing basic connection...');
    const response = await fetch(baseUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Response Status: ${response.status}`);
    console.log(`Response Headers:`, Object.fromEntries(response.headers.entries()));
    
    const result = await response.json();
    console.log('Response Body:', JSON.stringify(result, null, 2));

    if (response.status === 200 && result.success) {
      console.log('\n✅ Connection successful!');
      console.log(`Found ${result.result.length} videos`);
      
      // Show first few videos
      if (result.result.length > 0) {
        console.log('\n🎬 Sample videos:');
        result.result.slice(0, 3).forEach((video, index) => {
          console.log(`${index + 1}. UID: ${video.uid}`);
          console.log(`   Status: ${video.status?.state || 'unknown'}`);
          console.log(`   Filename: ${video.filename || 'N/A'}`);
          console.log(`   Duration: ${video.duration || 'N/A'}s`);
          console.log('');
        });
      }
      
      return { success: true, videos: result.result };
    } else {
      console.log('\n❌ Connection failed');
      console.log('Error details:', result);
      return { success: false, error: result };
    }

  } catch (error) {
    console.error('\n💥 Connection error:', error.message);
    return { success: false, error: error.message };
  }
}

// Run test
if (require.main === module) {
  testStreamConnection()
    .then(result => {
      if (result.success) {
        console.log('\n🎉 Stream API is working correctly!');
        process.exit(0);
      } else {
        console.log('\n❌ Stream API test failed');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testStreamConnection };
