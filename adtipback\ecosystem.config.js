module.exports = {
    apps: [
      {
        name: "adtip-server", // Updated to match your previous name
        script: "./index.js",
        instances: 4, // Set to 4 instances, matching -i 4
        exec_mode: "cluster", // Cluster mode for load balancing
        watch: false,
        max_memory_restart: "4G",
        env_dev: {
          NODE_ENV: "dev",
          NODE_OPTIONS: "--max_old_space_size=12000",
          APP_SERVICE_TYPE: "API",
        },
        env_staging: {
          NODE_ENV: "staging",
          NODE_OPTIONS: "--max_old_space_size=12000",
          APP_SERVICE_TYPE: "API",
        },
        env_production: {
          NODE_ENV: "production",
          NODE_OPTIONS: "--max_old_space_size=12000",
          APP_SERVICE_TYPE: "API",
        },
      },
    ],
  };