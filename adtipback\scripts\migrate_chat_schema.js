#!/usr/bin/env node

/**
 * Chat Schema Migration Script
 * 
 * This script safely migrates the chat system from the old user_chat table
 * to the new optimized schema with proper error handling and rollback capabilities.
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

// Use existing database configuration
const dbConfigModule = require('../config/appenvconfig.js');

// Database configuration
const dbConfig = {
  host: dbConfigModule.database.host,
  user: dbConfigModule.database.user,
  password: dbConfigModule.database.password,
  database: dbConfigModule.database.database,
  port: parseInt(dbConfigModule.database.port) || 3306,
  charset: 'utf8mb4',
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000
};

class ChatMigration {
  constructor() {
    this.connection = null;
    this.migrationStartTime = new Date();
    this.backupTableName = `user_chat_backup_${Date.now()}`;
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(dbConfig);
      console.log('✅ Connected to database successfully');
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log('✅ Database connection closed');
    }
  }

  async executeSQL(sqlContent, description) {
    try {
      console.log(`🔄 ${description}...`);

      // Split SQL content by semicolons and execute each statement separately
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      const results = [];
      for (const statement of statements) {
        if (statement.trim()) {
          try {
            const [result] = await this.connection.query(statement);
            results.push(result);
          } catch (error) {
            // Skip certain expected errors for CREATE statements
            if (error.code === 'ER_TABLE_EXISTS_ERROR' ||
                error.code === 'ER_DUP_FIELDNAME' ||
                error.message.includes('already exists')) {
              console.log(`   Skipping: ${error.message}`);
              continue;
            }
            throw error;
          }
        }
      }

      console.log(`✅ ${description} completed successfully`);
      return results;
    } catch (error) {
      console.error(`❌ ${description} failed:`, error.message);
      throw error;
    }
  }

  async executeSQLFile(filePath, description) {
    try {
      const sqlContent = fs.readFileSync(filePath, 'utf8');
      return await this.executeSQL(sqlContent, description);
    } catch (error) {
      if (error.code === 'ENOENT') {
        console.error(`❌ SQL file not found: ${filePath}`);
      }
      throw error;
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    try {
      // Check if user_chat table exists
      const [tables] = await this.connection.execute(
        "SHOW TABLES LIKE 'user_chat'"
      );
      
      if (tables.length === 0) {
        throw new Error('user_chat table not found. Nothing to migrate.');
      }

      // Check if new tables already exist
      const [existingTables] = await this.connection.execute(`
        SHOW TABLES WHERE Tables_in_${dbConfig.database} IN (
          'conversations', 'conversation_participants', 'messages', 
          'message_status', 'user_presence', 'chat_settings'
        )
      `);

      if (existingTables.length > 0) {
        console.log('⚠️  Some new chat tables already exist:');
        existingTables.forEach(table => {
          console.log(`   - ${Object.values(table)[0]}`);
        });
        
        const readline = require('readline').createInterface({
          input: process.stdin,
          output: process.stdout
        });
        
        const answer = await new Promise(resolve => {
          readline.question('Do you want to continue? This will drop existing tables. (y/N): ', resolve);
        });
        
        readline.close();
        
        if (answer.toLowerCase() !== 'y') {
          throw new Error('Migration cancelled by user');
        }
      }

      // Check data count
      const [countResult] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM user_chat WHERE is_active = 1'
      );
      
      const messageCount = countResult[0].count;
      console.log(`📊 Found ${messageCount} active messages to migrate`);
      
      if (messageCount === 0) {
        console.log('⚠️  No active messages found to migrate');
      }

      console.log('✅ Prerequisites check passed');
      return true;
    } catch (error) {
      console.error('❌ Prerequisites check failed:', error.message);
      throw error;
    }
  }

  async createBackup() {
    console.log('💾 Creating backup of existing data...');
    
    try {
      // Create backup table
      await this.connection.execute(`
        CREATE TABLE ${this.backupTableName} AS 
        SELECT * FROM user_chat
      `);
      
      // Verify backup
      const [backupCount] = await this.connection.execute(
        `SELECT COUNT(*) as count FROM ${this.backupTableName}`
      );
      
      const [originalCount] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM user_chat'
      );
      
      if (backupCount[0].count !== originalCount[0].count) {
        throw new Error('Backup verification failed - row counts do not match');
      }
      
      console.log(`✅ Backup created successfully: ${this.backupTableName} (${backupCount[0].count} rows)`);
      return true;
    } catch (error) {
      console.error('❌ Backup creation failed:', error.message);
      throw error;
    }
  }

  async createNewSchema() {
    const schemaPath = path.join(__dirname, '../database/chat_schema_simple.sql');
    return await this.executeSQLFile(schemaPath, 'Creating new chat schema');
  }

  async migrateData() {
    const migrationPath = path.join(__dirname, '../database/migrate_chat_data_simple.sql');
    return await this.executeSQLFile(migrationPath, 'Migrating chat data');
  }

  async verifyMigration() {
    console.log('🔍 Verifying migration results...');
    
    try {
      // Get migration statistics
      const queries = [
        'SELECT COUNT(*) as count FROM conversations',
        'SELECT COUNT(*) as count FROM conversation_participants',
        'SELECT COUNT(*) as count FROM messages',
        'SELECT COUNT(*) as count FROM message_status',
        'SELECT COUNT(*) as count FROM user_presence'
      ];
      
      const results = {};
      const tableNames = ['conversations', 'conversation_participants', 'messages', 'message_status', 'user_presence'];
      
      for (let i = 0; i < queries.length; i++) {
        const [result] = await this.connection.execute(queries[i]);
        results[tableNames[i]] = result[0].count;
      }
      
      // Display results
      console.log('📊 Migration Results:');
      Object.entries(results).forEach(([table, count]) => {
        console.log(`   ${table}: ${count} records`);
      });
      
      // Check for orphaned data
      const [orphanedMessages] = await this.connection.execute(`
        SELECT COUNT(*) as count 
        FROM messages m 
        LEFT JOIN conversations c ON m.conversation_id = c.id 
        WHERE c.id IS NULL
      `);
      
      if (orphanedMessages[0].count > 0) {
        console.log(`⚠️  Found ${orphanedMessages[0].count} orphaned messages`);
      }
      
      // Verify data integrity
      const [originalMessages] = await this.connection.execute(
        'SELECT COUNT(*) as count FROM user_chat WHERE is_active = 1'
      );
      
      const migrationSuccess = results.messages > 0 && orphanedMessages[0].count === 0;
      
      if (migrationSuccess) {
        console.log('✅ Migration verification passed');
        console.log(`📈 Migrated ${results.messages} messages from ${originalMessages[0].count} original records`);
      } else {
        throw new Error('Migration verification failed');
      }
      
      return migrationSuccess;
    } catch (error) {
      console.error('❌ Migration verification failed:', error.message);
      throw error;
    }
  }

  async rollback() {
    console.log('🔄 Rolling back migration...');
    
    try {
      // Drop new tables
      const newTables = [
        'message_reactions', 'chat_settings', 'user_presence', 
        'message_status', 'messages', 'conversation_participants', 'conversations'
      ];
      
      for (const table of newTables) {
        try {
          await this.connection.execute(`DROP TABLE IF EXISTS ${table}`);
          console.log(`   Dropped table: ${table}`);
        } catch (error) {
          console.log(`   Could not drop table ${table}: ${error.message}`);
        }
      }
      
      console.log('✅ Rollback completed');
    } catch (error) {
      console.error('❌ Rollback failed:', error.message);
      throw error;
    }
  }

  async run() {
    const startTime = Date.now();
    
    try {
      console.log('🚀 Starting Chat Schema Migration');
      console.log('=====================================');
      
      // Connect to database
      if (!(await this.connect())) {
        process.exit(1);
      }
      
      // Check prerequisites
      await this.checkPrerequisites();
      
      // Create backup
      await this.createBackup();
      
      // Start transaction
      await this.connection.execute('START TRANSACTION');
      
      try {
        // Create new schema
        await this.createNewSchema();
        
        // Migrate data
        await this.migrateData();
        
        // Verify migration
        await this.verifyMigration();
        
        // Commit transaction
        await this.connection.execute('COMMIT');
        
        const duration = ((Date.now() - startTime) / 1000).toFixed(2);
        console.log('=====================================');
        console.log(`🎉 Migration completed successfully in ${duration}s`);
        console.log(`💾 Backup table: ${this.backupTableName}`);
        console.log('✅ New chat schema is ready for use');
        
      } catch (error) {
        // Rollback transaction
        await this.connection.execute('ROLLBACK');
        throw error;
      }
      
    } catch (error) {
      console.log('=====================================');
      console.error('💥 Migration failed:', error.message);
      
      try {
        await this.rollback();
      } catch (rollbackError) {
        console.error('💥 Rollback also failed:', rollbackError.message);
      }
      
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  const migration = new ChatMigration();
  migration.run();
}

module.exports = ChatMigration;
