#!/bin/bash

# Message Cleanup Setup Script
# 
# This script sets up automated message cleanup for the Adtip backend.
# It configures cron jobs, creates log directories, and sets up monitoring.
#
# Usage: ./setup-message-cleanup.sh [options]
# 
# Options:
#   --environment <env>    Environment: dev, staging, prod (default: prod)
#   --retention-days <n>   Days to retain messages (default: 7)
#   --install-cron         Install cron jobs automatically
#   --dry-run             Show what would be done without making changes
#   --help                Show this help message

set -e  # Exit on any error

# Default configuration
ENVIRONMENT="prod"
RETENTION_DAYS=7
INSTALL_CRON=false
DRY_RUN=false
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="/var/log/adtip"
CRON_USER="www-data"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --retention-days)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        --install-cron)
            INSTALL_CRON=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            echo "Message Cleanup Setup Script"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --environment <env>    Environment: dev, staging, prod (default: prod)"
            echo "  --retention-days <n>   Days to retain messages (default: 7)"
            echo "  --install-cron         Install cron jobs automatically"
            echo "  --dry-run             Show what would be done without making changes"
            echo "  --help                Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --environment prod --retention-days 7 --install-cron"
            echo "  $0 --environment dev --retention-days 1 --dry-run"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod."
    exit 1
fi

# Validate retention days
if ! [[ "$RETENTION_DAYS" =~ ^[0-9]+$ ]] || [ "$RETENTION_DAYS" -lt 1 ]; then
    error "Invalid retention days: $RETENTION_DAYS. Must be a positive integer."
    exit 1
fi

log "Setting up message cleanup for environment: $ENVIRONMENT"
log "Retention days: $RETENTION_DAYS"
log "Project root: $PROJECT_ROOT"
log "Log directory: $LOG_DIR"

if [ "$DRY_RUN" = true ]; then
    warn "DRY RUN MODE: No changes will be made"
fi

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ] && [ "$INSTALL_CRON" = true ]; then
    error "This script needs to be run with sudo to install cron jobs and create log directories."
    error "Run with sudo or remove --install-cron flag to skip automatic installation."
    exit 1
fi

# Function to execute commands with dry-run support
execute() {
    local cmd="$1"
    local description="$2"
    
    if [ "$DRY_RUN" = true ]; then
        info "[DRY RUN] Would execute: $cmd"
        if [ -n "$description" ]; then
            info "[DRY RUN] Purpose: $description"
        fi
    else
        log "Executing: $description"
        eval "$cmd"
    fi
}

# Check dependencies
check_dependencies() {
    log "Checking dependencies..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed or not in PATH"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    log "Node.js version: $NODE_VERSION"
    
    # Check if the cleanup script exists
    if [ ! -f "$SCRIPT_DIR/cleanup-old-messages.js" ]; then
        error "Cleanup script not found: $SCRIPT_DIR/cleanup-old-messages.js"
        exit 1
    fi
    
    # Check if the script is executable
    if [ ! -x "$SCRIPT_DIR/cleanup-old-messages.js" ]; then
        warn "Cleanup script is not executable, making it executable..."
        execute "chmod +x '$SCRIPT_DIR/cleanup-old-messages.js'" "Make cleanup script executable"
    fi
    
    # Test the cleanup script
    log "Testing cleanup script..."
    if ! node "$SCRIPT_DIR/cleanup-old-messages.js" --help &> /dev/null; then
        error "Cleanup script test failed"
        exit 1
    fi
    
    log "Dependencies check passed"
}

# Create log directory and set permissions
setup_logging() {
    log "Setting up logging..."
    
    execute "mkdir -p '$LOG_DIR'" "Create log directory"
    execute "chown $CRON_USER:$CRON_USER '$LOG_DIR'" "Set log directory ownership"
    execute "chmod 755 '$LOG_DIR'" "Set log directory permissions"
    
    # Create initial log files
    local log_files=(
        "message-cleanup.log"
        "message-cleanup-weekly.log"
        "message-stats.log"
        "cleanup-heartbeat.log"
    )
    
    for log_file in "${log_files[@]}"; do
        execute "touch '$LOG_DIR/$log_file'" "Create log file: $log_file"
        execute "chown $CRON_USER:$CRON_USER '$LOG_DIR/$log_file'" "Set ownership for $log_file"
        execute "chmod 644 '$LOG_DIR/$log_file'" "Set permissions for $log_file"
    done
    
    log "Logging setup completed"
}

# Generate cron configuration based on environment
generate_cron_config() {
    local cron_file="/tmp/adtip-message-cleanup-$ENVIRONMENT.cron"
    
    log "Generating cron configuration for $ENVIRONMENT environment..."
    
    cat > "$cron_file" << EOF
# Adtip Message Cleanup Cron Jobs - $ENVIRONMENT Environment
# Generated on $(date)
# Retention: $RETENTION_DAYS days

EOF

    case $ENVIRONMENT in
        "dev")
            cat >> "$cron_file" << EOF
# Development: Cleanup every 30 minutes with 1-day retention
*/30 * * * * cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --retention-days 1 >> '$LOG_DIR/message-cleanup.log' 2>&1

# Development: Stats every hour
0 * * * * cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --stats-only >> '$LOG_DIR/message-stats.log' 2>&1
EOF
            ;;
        "staging")
            cat >> "$cron_file" << EOF
# Staging: Daily cleanup at 1 AM
0 1 * * * cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --retention-days $RETENTION_DAYS >> '$LOG_DIR/message-cleanup.log' 2>&1

# Staging: Weekly deep cleanup on Sunday at 2 AM
0 2 * * 0 cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --retention-days $RETENTION_DAYS --hard-delete-days 14 --verbose >> '$LOG_DIR/message-cleanup-weekly.log' 2>&1

# Staging: Stats every 2 hours
0 */2 * * * cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --stats-only >> '$LOG_DIR/message-stats.log' 2>&1
EOF
            ;;
        "prod")
            cat >> "$cron_file" << EOF
# Production: Daily cleanup at 2 AM
0 2 * * * cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --retention-days $RETENTION_DAYS >> '$LOG_DIR/message-cleanup.log' 2>&1

# Production: Weekly deep cleanup on Sunday at 3 AM
0 3 * * 0 cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --retention-days $RETENTION_DAYS --hard-delete-days 30 --verbose >> '$LOG_DIR/message-cleanup-weekly.log' 2>&1

# Production: Hourly stats for monitoring
0 * * * * cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --stats-only >> '$LOG_DIR/message-stats.log' 2>&1

# Production: Heartbeat every 5 minutes after daily cleanup
5 2 * * * cd '$PROJECT_ROOT' && /usr/bin/node scripts/cleanup-old-messages.js --stats-only > /tmp/adtip-cleanup-heartbeat && echo "\$(date): Cleanup script healthy" >> '$LOG_DIR/cleanup-heartbeat.log'
EOF
            ;;
    esac
    
    echo "$cron_file"
}

# Install cron jobs
install_cron_jobs() {
    if [ "$INSTALL_CRON" = false ]; then
        log "Skipping cron installation (use --install-cron to install automatically)"
        return
    fi
    
    log "Installing cron jobs..."
    
    local cron_file
    cron_file=$(generate_cron_config)
    
    # Backup existing crontab
    local backup_file="/tmp/crontab-backup-$(date +%Y%m%d-%H%M%S)"
    execute "crontab -u $CRON_USER -l > '$backup_file' 2>/dev/null || true" "Backup existing crontab"
    
    # Install new cron jobs
    execute "crontab -u $CRON_USER '$cron_file'" "Install cron jobs"
    
    log "Cron jobs installed successfully"
    log "Backup of previous crontab saved to: $backup_file"
    
    # Clean up temporary file
    execute "rm -f '$cron_file'" "Clean up temporary cron file"
}

# Setup log rotation
setup_log_rotation() {
    log "Setting up log rotation..."
    
    local logrotate_file="/etc/logrotate.d/adtip-message-cleanup"
    
    execute "cat > '$logrotate_file' << 'EOF'
$LOG_DIR/message-cleanup*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $CRON_USER $CRON_USER
}
EOF" "Create logrotate configuration"
    
    # Test logrotate configuration
    execute "logrotate -d '$logrotate_file'" "Test logrotate configuration"
    
    log "Log rotation setup completed"
}

# Main setup function
main() {
    log "Starting message cleanup setup..."
    
    check_dependencies
    setup_logging
    
    if [ "$INSTALL_CRON" = true ]; then
        install_cron_jobs
        setup_log_rotation
    fi
    
    # Generate manual cron configuration for reference
    local manual_cron_file="$PROJECT_ROOT/cron/message-cleanup-$ENVIRONMENT.cron"
    execute "mkdir -p '$PROJECT_ROOT/cron'" "Create cron directory"
    
    local generated_cron
    generated_cron=$(generate_cron_config)
    execute "cp '$generated_cron' '$manual_cron_file'" "Save cron configuration for manual installation"
    execute "rm -f '$generated_cron'" "Clean up temporary cron file"
    
    log "Setup completed successfully!"
    log ""
    log "Next steps:"
    if [ "$INSTALL_CRON" = false ]; then
        log "1. Review the generated cron configuration: $manual_cron_file"
        log "2. Install cron jobs manually: sudo crontab -u $CRON_USER $manual_cron_file"
    else
        log "1. Cron jobs have been installed automatically"
        log "2. Monitor logs in: $LOG_DIR/"
    fi
    log "3. Test the cleanup script: node $SCRIPT_DIR/cleanup-old-messages.js --dry-run --verbose"
    log "4. Monitor cleanup effectiveness through the stats logs"
    log ""
    log "Log files:"
    log "  - Daily cleanup: $LOG_DIR/message-cleanup.log"
    log "  - Weekly cleanup: $LOG_DIR/message-cleanup-weekly.log"
    log "  - Statistics: $LOG_DIR/message-stats.log"
    log "  - Heartbeat: $LOG_DIR/cleanup-heartbeat.log"
}

# Run main function
main
