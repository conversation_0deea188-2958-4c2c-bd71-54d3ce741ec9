-- Withdrawal Tables for Adtip App
-- This file contains all tables needed for withdrawal functionality

-- 1. Main wallet withdrawal requests table
CREATE TABLE IF NOT EXISTS wallet_withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    transaction_type VARCHAR(50) DEFAULT 'Withdrawal',
    withdraw_req_amount DECIMAL(10,2) NOT NULL,
    transaction_method ENUM('BANK', 'UPI') NOT NULL,
    transaction_status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    status ENUM('Unpaid', 'Paid', 'Failed') DEFAULT 'Unpaid',
    bank_name VARCHAR(255) NULL,
    account_number VARCHAR(50) NULL,
    ifsc_code VARCHAR(20) NULL,
    mobile_number VARCHAR(15) NULL,
    upi_id VARCHAR(100) NULL,
    withdrawal_charges DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL,
    admin_remarks TEXT NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_transaction_status (transaction_status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 2. Referral withdrawal requests table
CREATE TABLE IF NOT EXISTS referral_withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    withdrawal_type ENUM('referral', 'coupon') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    bank_name VARCHAR(255) NULL,
    bank_ifsc VARCHAR(20) NULL,
    bank_account_number VARCHAR(50) NULL,
    upi_id VARCHAR(100) NULL,
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    admin_remarks TEXT NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_withdrawal_type (withdrawal_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 3. Content creator/channel withdrawal requests table
CREATE TABLE IF NOT EXISTS channel_withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    channel_id INT NULL,
    withdrawal_type ENUM('creator_referral', 'content_earnings') DEFAULT 'content_earnings',
    amount DECIMAL(10,2) NOT NULL,
    bank_name VARCHAR(255) NULL,
    bank_ifsc VARCHAR(20) NULL,
    bank_account_number VARCHAR(50) NULL,
    upi_id VARCHAR(100) NULL,
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'pending',
    admin_remarks TEXT NULL,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_channel_id (channel_id),
    INDEX idx_withdrawal_type (withdrawal_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 4. Withdrawal settings table (for minimum amounts, charges, etc.)
CREATE TABLE IF NOT EXISTS withdrawal_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default withdrawal settings
INSERT INTO withdrawal_settings (setting_key, setting_value, description) VALUES
('min_withdrawal_regular', '5000', 'Minimum withdrawal amount for regular users (in INR)'),
('min_withdrawal_premium', '1000', 'Minimum withdrawal amount for premium users (in INR)'),
('withdrawal_charges_percent', '5', 'Withdrawal charges as percentage'),
('withdrawal_processing_days', '3-5', 'Expected processing time in business days'),
('max_daily_withdrawal', '10000', 'Maximum daily withdrawal limit per user (in INR)'),
('max_monthly_withdrawal', '100000', 'Maximum monthly withdrawal limit per user (in INR)')
ON DUPLICATE KEY UPDATE
    setting_value = VALUES(setting_value),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- 5. Add withdrawal-related columns to existing users table if not exists
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS total_withdrawals DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS last_withdrawal_date TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS withdrawal_count INT DEFAULT 0;

ALTER TABLE users 
ADD COLUMN total_withdrawals DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN last_withdrawal_date TIMESTAMP NULL,
ADD COLUMN withdrawal_count INT DEFAULT 0;


-- 6. Add withdrawal-related columns to existing wallet table if not exists
ALTER TABLE wallet 
ADD COLUMN IF NOT EXISTS total_withdrawn DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS pending_withdrawals DECIMAL(10,2) DEFAULT 0.00;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_wallet_withdrawals_user_status ON wallet_withdrawals(user_id, status);
CREATE INDEX IF NOT EXISTS idx_referral_withdrawals_user_type ON referral_withdrawals(user_id, withdrawal_type);
CREATE INDEX IF NOT EXISTS idx_channel_withdrawals_user_channel ON channel_withdrawals(user_id, channel_id);
CREATE INDEX IF NOT EXISTS idx_users_total_withdrawals ON users(total_withdrawals);
CREATE INDEX IF NOT EXISTS idx_wallet_total_withdrawn ON wallet(total_withdrawn); 