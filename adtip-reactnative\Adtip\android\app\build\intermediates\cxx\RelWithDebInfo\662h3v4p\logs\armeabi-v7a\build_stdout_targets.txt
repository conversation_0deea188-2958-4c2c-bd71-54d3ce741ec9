ninja: Entering directory `F:\A1\adtip-reactnative\Adtip\android\app\.cxx\RelWithDebInfo\662h3v4p\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/armeabi-v7a
[0/2] Re-checking globbed directories...
[1/5] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[2/5] Building CXX object RNCImageCropPickerSpec_autolinked_build/CMakeFiles/react_codegen_RNCImageCropPickerSpec.dir/react/renderer/components/RNCImageCropPickerSpec/RNCImageCropPickerSpecJSI-generated.cpp.o
[3/5] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[4/5] Building CXX object CMakeFiles/appmodules.dir/F_/A1/adtip-reactnative/Adtip/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[5/5] Linking CXX shared library F:\A1\adtip-reactnative\Adtip\android\app\build\intermediates\cxx\RelWithDebInfo\662h3v4p\obj\armeabi-v7a\libappmodules.so
