1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.callkeepexample"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:4:5-67
11-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.BIND_TELECOM_CONNECTION_SERVICE" />
12-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:5:5-89
12-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:5:22-87
13    <uses-permission
13-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:6:5-75
14        android:name="android.permission.READ_PHONE_STATE"
14-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:6:22-72
15        android:maxSdkVersion="29" />
15-->[:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-35
16    <uses-permission android:name="android.permission.CALL_PHONE" />
16-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:7:5-69
16-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:7:22-66
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:8:5-77
17-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:8:22-74
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:9:5-81
18-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:9:22-78
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:10:5-77
19-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:10:22-75
20    <uses-permission android:name="android.permission.RECORD_AUDIO" />
20-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:11:5-71
20-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:11:22-68
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
21-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:12:5-88
21-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:12:22-85
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
22-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:13:5-84
22-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:13:22-81
23    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
23-->[:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-77
23-->[:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:22-74
24    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
24-->[:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-75
24-->[:react-native-callkeep] /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:22-72
25
26    <permission
26-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
27        android:name="com.callkeepexample.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.callkeepexample.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
31
32    <application
32-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:16:5-53:19
33        android:name="com.callkeepexample.MainApplication"
33-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:17:9-40
34        android:allowBackup="false"
34-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:21:9-36
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:icon="@mipmap/ic_launcher"
38-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:19:9-43
39        android:label="@string/app_name"
39-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:18:9-41
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:20:9-54
41        android:supportsRtl="true"
41-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:23:9-35
42        android:theme="@style/AppTheme"
42-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:22:9-40
43        android:usesCleartextTraffic="true" >
43-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/AndroidManifest.xml:6:9-44
44        <activity
44-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:24:9-35:20
45            android:name="com.callkeepexample.MainActivity"
45-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:25:13-41
46            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
46-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:27:13-122
47            android:exported="true"
47-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:30:13-36
48            android:label="@string/app_name"
48-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:26:13-45
49            android:launchMode="singleTask"
49-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:28:13-44
50            android:windowSoftInputMode="adjustResize" >
50-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:29:13-55
51            <intent-filter>
51-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:31:13-34:29
52                <action android:name="android.intent.action.MAIN" />
52-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:32:17-69
52-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:32:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:33:17-77
54-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:33:27-74
55            </intent-filter>
56        </activity>
57
58        <service
58-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:36:9-45:19
59            android:name="io.wazo.callkeep.VoiceConnectionService"
59-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:37:13-67
60            android:exported="true"
60-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:41:13-36
61            android:foregroundServiceType="camera|microphone"
61-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:40:13-62
62            android:label="Wazo"
62-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:38:13-33
63            android:permission="android.permission.BIND_TELECOM_CONNECTION_SERVICE" >
63-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:39:13-84
64            <intent-filter>
64-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:42:13-44:29
65                <action android:name="android.telecom.ConnectionService" />
65-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:43:17-76
65-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:43:25-73
66            </intent-filter>
67        </service>
68
69        <receiver
69-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:46:9-52:20
70            android:name="io.wazo.callkeep.VoiceBroadcastReceiver"
70-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:46:19-73
71            android:exported="true" >
71-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:47:13-36
72            <intent-filter>
72-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:48:13-51:29
73                <action android:name="io.wazo.callkeep.ACTION_ANSWER_CALL" />
73-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:49:17-77
73-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:49:25-75
74                <action android:name="io.wazo.callkeep.ACTION_END_CALL" />
74-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:50:17-74
74-->/Users/<USER>/Desktop/CallKeepExample/android/app/src/main/AndroidManifest.xml:50:25-72
75            </intent-filter>
76        </receiver>
77
78        <activity
78-->[com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:19:9-21:40
79            android:name="com.facebook.react.devsupport.DevSettingsActivity"
79-->[com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:20:13-77
80            android:exported="false" />
80-->[com.facebook.react:react-android:0.80.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/AndroidManifest.xml:21:13-37
81
82        <provider
82-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
83            android:name="androidx.startup.InitializationProvider"
83-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
84            android:authorities="com.callkeepexample.androidx-startup"
84-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
85            android:exported="false" >
85-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
86            <meta-data
86-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
87                android:name="androidx.emoji2.text.EmojiCompatInitializer"
87-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
88                android:value="androidx.startup" />
88-->[androidx.emoji2:emoji2:1.3.0] /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
89            <meta-data
89-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
90                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
90-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
91                android:value="androidx.startup" />
91-->[androidx.lifecycle:lifecycle-process:2.6.2] /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
92            <meta-data
92-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
93                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
93-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
94                android:value="androidx.startup" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
95        </provider>
96
97        <receiver
97-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
98            android:name="androidx.profileinstaller.ProfileInstallReceiver"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
99            android:directBootAware="false"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
100            android:enabled="true"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
101            android:exported="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
102            android:permission="android.permission.DUMP" >
102-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
104                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
104-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
104-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
107                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
107-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
108            </intent-filter>
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
110                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
110-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
113                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
113-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
114            </intent-filter>
115        </receiver>
116
117        <meta-data
117-->[com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:12:9-14:37
118            android:name="com.facebook.soloader.enabled"
118-->[com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:13:13-57
119            android:value="false" />
119-->[com.facebook.soloader:soloader:0.12.1] /Users/<USER>/.gradle/caches/8.14.1/transforms/363f04887ef37f5443484b40dba2058e/transformed/soloader-0.12.1/AndroidManifest.xml:14:13-34
120    </application>
121
122</manifest>
