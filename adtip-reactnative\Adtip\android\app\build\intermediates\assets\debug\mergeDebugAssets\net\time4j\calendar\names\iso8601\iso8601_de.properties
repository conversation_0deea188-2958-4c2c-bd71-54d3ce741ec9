﻿# months
M(a)_1=Jan.
M(a)_2=Feb.
M(a)_3=<PERSON>ärz
M(a)_4=Apr.
M(a)_5=Mai
M(a)_6=Juni
M(a)_7=Juli
M(a)_8=Aug.
M(a)_9=Sept.
M(a)_10=Okt.
M(a)_11=Nov.
M(a)_12=Dez.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=Januar
M(w)_2=Februar
M(w)_3=März
M(w)_4=April
M(w)_5=Mai
M(w)_6=Juni
M(w)_7=Juli
M(w)_8=August
M(w)_9=September
M(w)_10=Oktober
M(w)_11=November
M(w)_12=Dezember

M(A)_1=Jan
M(A)_2=Feb
M(A)_3=Mär
M(A)_4=Apr
M(A)_5=Mai
M(A)_6=Jun
M(A)_7=Jul
M(A)_8=Aug
M(A)_9=Sep
M(A)_10=Okt
M(A)_11=Nov
M(A)_12=Dez

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=Januar
M(W)_2=Februar
M(W)_3=März
M(W)_4=April
M(W)_5=Mai
M(W)_6=Juni
M(W)_7=Juli
M(W)_8=August
M(W)_9=September
M(W)_10=Oktober
M(W)_11=November
M(W)_12=Dezember

# weekdays
D(a)_1=Mo.
D(a)_2=Di.
D(a)_3=Mi.
D(a)_4=Do.
D(a)_5=Fr.
D(a)_6=Sa.
D(a)_7=So.

D(n)_1=M
D(n)_2=D
D(n)_3=M
D(n)_4=D
D(n)_5=F
D(n)_6=S
D(n)_7=S

D(s)_1=Mo.
D(s)_2=Di.
D(s)_3=Mi.
D(s)_4=Do.
D(s)_5=Fr.
D(s)_6=Sa.
D(s)_7=So.

D(w)_1=Montag
D(w)_2=Dienstag
D(w)_3=Mittwoch
D(w)_4=Donnerstag
D(w)_5=Freitag
D(w)_6=Samstag
D(w)_7=Sonntag

D(A)_1=Mo
D(A)_2=Di
D(A)_3=Mi
D(A)_4=Do
D(A)_5=Fr
D(A)_6=Sa
D(A)_7=So

D(N)_1=M
D(N)_2=D
D(N)_3=M
D(N)_4=D
D(N)_5=F
D(N)_6=S
D(N)_7=S

D(S)_1=Mo.
D(S)_2=Di.
D(S)_3=Mi.
D(S)_4=Do.
D(S)_5=Fr.
D(S)_6=Sa.
D(S)_7=So.

D(W)_1=Montag
D(W)_2=Dienstag
D(W)_3=Mittwoch
D(W)_4=Donnerstag
D(W)_5=Freitag
D(W)_6=Samstag
D(W)_7=Sonntag

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. Quartal
Q(w)_2=2. Quartal
Q(w)_3=3. Quartal
Q(w)_4=4. Quartal

Q(A)_1=Q1
Q(A)_2=Q2
Q(A)_3=Q3
Q(A)_4=Q4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1. Quartal
Q(W)_2=2. Quartal
Q(W)_3=3. Quartal
Q(W)_4=4. Quartal

# day-period-rules
T0000=night1
T0500=morning1
T1000=morning2
T1200=afternoon1
T1300=afternoon2
T1800=evening1

# day-period-translations
P(a)_midnight=Mitternacht
P(a)_morning1=morgens
P(a)_morning2=vorm.
P(a)_afternoon1=mittags
P(a)_afternoon2=nachm.
P(a)_evening1=abends
P(a)_night1=nachts

P(n)_midnight=Mitternacht
P(n)_am=a
P(n)_pm=p
P(n)_morning1=morgens
P(n)_morning2=vorm.
P(n)_afternoon1=mittags
P(n)_afternoon2=nachm.
P(n)_evening1=abends
P(n)_night1=nachts

P(w)_midnight=Mitternacht
P(w)_morning1=morgens
P(w)_morning2=vormittags
P(w)_afternoon1=mittags
P(w)_afternoon2=nachmittags
P(w)_evening1=abends
P(w)_night1=nachts

P(A)_midnight=Mitternacht
P(A)_am=AM
P(A)_pm=PM
P(A)_morning1=Morgen
P(A)_morning2=Vorm.
P(A)_afternoon1=Mittag
P(A)_afternoon2=Nachm.
P(A)_evening1=Abend
P(A)_night1=Nacht

P(N)_midnight=Mitternacht
P(N)_am=a
P(N)_pm=p
P(N)_morning1=Morgen
P(N)_morning2=Vorm.
P(N)_afternoon1=Mittag
P(N)_afternoon2=Nachm.
P(N)_evening1=Abend
P(N)_night1=Nacht

P(W)_midnight=Mitternacht
P(W)_am=AM
P(W)_pm=PM
P(W)_morning1=Morgen
P(W)_morning2=Vormittag
P(W)_afternoon1=Mittag
P(W)_afternoon2=Nachmittag
P(W)_evening1=Abend
P(W)_night1=Nacht

# eras
E(w)_0=v. Chr.
E(w|alt)_0=vor unserer Zeitrechnung
E(w)_1=n. Chr.
E(w|alt)_1=unserer Zeitrechnung

E(a)_0=v. Chr.
E(a|alt)_0=v. u. Z.
E(a)_1=n. Chr.
E(a|alt)_1=u. Z.

E(n)_0=v. Chr.
E(n|alt)_0=v. u. Z.
E(n)_1=n. Chr.
E(n|alt)_1=u. Z.

# format patterns
F(f)_d=EEEE, d. MMMM y
F(l)_d=d. MMMM y
F(m)_d=dd.MM.y
F(s)_d=dd.MM.yy

F(alt)=HH:mm' Uhr'

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'um' {0}
F(l)_dt={1} 'um' {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h 'Uhr' a
F_H=HH 'Uhr'
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.M.
F_MMd=d.MM.
F_MMMd=d. MMM
F_MMMMd=d. MMMM
F_y=y
F_yM=M.y
F_yMM=MM.y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='Woche' w 'des' 'Jahres' Y

I={0} – {1}

# labels of elements
L_era=Epoche
L_year=Jahr
L_quarter=Quartal
L_month=Monat
L_week=Woche
L_day=Tag
L_weekday=Wochentag
L_dayperiod=Tageshälfte
L_hour=Stunde
L_minute=Minute
L_second=Sekunde
L_zone=Zeitzone

# spanish era
E(n)_2=Era
E(a)_2=Spanische Ära
E(w)_2=Spanische Ära
