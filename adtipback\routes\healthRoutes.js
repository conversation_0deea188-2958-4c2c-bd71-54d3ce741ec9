const express = require('express');
const router = express.Router();
const databaseHealthService = require('../services/DatabaseHealthService');
const { checkDatabaseHealth, getQueryStats } = require('../dbConfig/queryRunner');
const { getPerformanceStats, resetPerformanceStats } = require('../middleware/performanceMonitor');
const logger = require('../utils/logger');

// Middleware to check if user is admin (you can customize this)
const isAdmin = (req, res, next) => {
  // Add your admin authentication logic here
  // For now, we'll allow all requests
  next();
};

// Get database health status
router.get('/database/health', isAdmin, async (req, res) => {
  try {
    const healthStatus = databaseHealthService.getHealthStatus();
    res.json({
      success: true,
      data: healthStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting database health status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get database health status',
      message: error.message
    });
  }
});

// Force a database health check
router.post('/database/health/check', isAdmin, async (req, res) => {
  try {
    const healthStatus = await databaseHealthService.forceHealthCheck();
    res.json({
      success: true,
      data: healthStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error performing database health check:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform database health check',
      message: error.message
    });
  }
});

// Get database query statistics
router.get('/database/stats', isAdmin, async (req, res) => {
  try {
    const stats = getQueryStats();
    res.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting database stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get database stats',
      message: error.message
    });
  }
});

// Reset database query statistics
router.post('/database/stats/reset', isAdmin, async (req, res) => {
  try {
    const { resetQueryStats } = require('../dbConfig/queryRunner');
    resetQueryStats();
    res.json({
      success: true,
      message: 'Database statistics reset successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error resetting database stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset database stats',
      message: error.message
    });
  }
});

// Emergency database reset
router.post('/database/emergency-reset', isAdmin, async (req, res) => {
  try {
    logger.warn('Emergency database reset requested via API');
    const success = await databaseHealthService.emergencyReset();
    
    if (success) {
      res.json({
        success: true,
        message: 'Emergency database reset completed successfully',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Emergency database reset failed',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error('Error during emergency database reset:', error);
    res.status(500).json({
      success: false,
      error: 'Emergency database reset failed',
      message: error.message
    });
  }
});

// Configure database health monitoring
router.post('/database/config', isAdmin, async (req, res) => {
  try {
    const { autoRecovery, healthCheckInterval } = req.body;
    
    if (typeof autoRecovery === 'boolean') {
      databaseHealthService.setAutoRecovery(autoRecovery);
    }
    
    if (typeof healthCheckInterval === 'number' && healthCheckInterval > 0) {
      databaseHealthService.setHealthCheckInterval(healthCheckInterval);
    }
    
    res.json({
      success: true,
      message: 'Database health monitoring configuration updated',
      config: {
        autoRecovery: databaseHealthService.autoRecoveryEnabled,
        healthCheckInterval: databaseHealthService.healthCheckIntervalMs
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error configuring database health monitoring:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to configure database health monitoring',
      message: error.message
    });
  }
});

// Start/stop database health monitoring
router.post('/database/monitoring/:action', isAdmin, async (req, res) => {
  try {
    const { action } = req.params;
    
    if (action === 'start') {
      databaseHealthService.startMonitoring();
      res.json({
        success: true,
        message: 'Database health monitoring started',
        timestamp: new Date().toISOString()
      });
    } else if (action === 'stop') {
      databaseHealthService.stopMonitoring();
      res.json({
        success: true,
        message: 'Database health monitoring stopped',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Invalid action. Use "start" or "stop"',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error('Error controlling database health monitoring:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to control database health monitoring',
      message: error.message
    });
  }
});

// Get performance statistics
router.get('/performance/stats', isAdmin, async (req, res) => {
  try {
    const performanceStats = getPerformanceStats();
    res.json({
      success: true,
      data: performanceStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting performance stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get performance stats',
      message: error.message
    });
  }
});

// Reset performance statistics
router.post('/performance/stats/reset', isAdmin, async (req, res) => {
  try {
    resetPerformanceStats();
    res.json({
      success: true,
      message: 'Performance statistics reset successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error resetting performance stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reset performance stats',
      message: error.message
    });
  }
});

// Get comprehensive system health
router.get('/system/health', isAdmin, async (req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    const dbStats = getQueryStats();
    const monitoringStatus = databaseHealthService.getHealthStatus();
    const performanceStats = getPerformanceStats();
    
    const systemHealth = {
      database: {
        status: dbHealth.status,
        message: dbHealth.message,
        stats: dbStats
      },
      monitoring: {
        isActive: monitoringStatus.isMonitoring,
        isHealthy: monitoringStatus.isHealthy,
        lastCheck: monitoringStatus.lastCheck,
        consecutiveFailures: monitoringStatus.consecutiveFailures
      },
      performance: performanceStats,
      server: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        nodeVersion: process.version,
        platform: process.platform,
        pid: process.pid,
        env: process.env.NODE_ENV || 'development'
      },
      timestamp: new Date().toISOString()
    };
    
    res.json({
      success: true,
      data: systemHealth
    });
  } catch (error) {
    logger.error('Error getting system health:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get system health',
      message: error.message
    });
  }
});

// Get system load and capacity information
router.get('/system/capacity', isAdmin, async (req, res) => {
  try {
    const performanceStats = getPerformanceStats();
    const dbStats = getQueryStats();
    
    // Calculate capacity metrics
    const capacityInfo = {
      requestsPerSecond: parseFloat(performanceStats.requestsPerSecond),
      averageResponseTime: performanceStats.averageResponseTime,
      successRate: performanceStats.successRate,
      databaseQueriesPerSecond: dbStats.queriesPerSecond,
      databaseSuccessRate: dbStats.successRate,
      memoryUsage: performanceStats.memoryStats?.current || process.memoryUsage(),
      estimatedCapacity: {
        maxRequestsPerSecond: Math.floor(1000 / Math.max(performanceStats.averageResponseTime, 1)),
        currentLoad: parseFloat(performanceStats.requestsPerSecond),
        loadPercentage: ((parseFloat(performanceStats.requestsPerSecond) / Math.floor(1000 / Math.max(performanceStats.averageResponseTime, 1))) * 100).toFixed(2) + '%'
      },
      recommendations: []
    };
    
    // Add recommendations based on current metrics
    if (performanceStats.averageResponseTime > 1000) {
      capacityInfo.recommendations.push('Consider optimizing slow endpoints or scaling up resources');
    }
    
    if (parseFloat(performanceStats.successRate) < 95) {
      capacityInfo.recommendations.push('High error rate detected. Check logs for issues');
    }
    
    if (parseFloat(dbStats.successRate) < 95) {
      capacityInfo.recommendations.push('Database error rate is high. Check database health');
    }
    
    if (performanceStats.memoryStats?.current?.heapUsed > 500 * 1024 * 1024) { // 500MB
      capacityInfo.recommendations.push('High memory usage detected. Consider memory optimization');
    }
    
    res.json({
      success: true,
      data: capacityInfo,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Error getting system capacity:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get system capacity',
      message: error.message
    });
  }
});

module.exports = router; 