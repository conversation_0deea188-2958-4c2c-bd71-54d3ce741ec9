// Cloudflare R2 Configuration for Web Application
// Based on React Native implementation with web-specific adaptations

export interface CloudflareR2Config {
  accountId: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  region: string;
  publicUrl: string;
}

export interface CloudflareStreamConfig {
  accountId: string;
  apiToken: string;
  customerCode: string;
}

// IMPORTANT: In production, these should be loaded from environment variables
// Never commit real credentials to version control!
export const CLOUDFLARE_R2_CONFIG: CloudflareR2Config = {
  // Replace with your actual Cloudflare R2 credentials
  accountId: import.meta.env.VITE_CLOUDFLARE_ACCOUNT_ID || '94e2ffe1e7d5daf0d3de8d11c55dd2d6',
  accessKeyId: import.meta.env.VITE_CLOUDFLARE_ACCESS_KEY_ID || 'cee3aea0fa77a871fbc3d34a28897216',
  secretAccessKey: import.meta.env.VITE_CLOUDFLARE_SECRET_ACCESS_KEY || '686b7a165aa944fbd641de53bbbb277a07e9a284ace18c84a83237b330b63c1d',
  bucketName: import.meta.env.VITE_CLOUDFLARE_BUCKET_NAME || 'adtip',
  region: 'auto', // Cloudflare R2 uses 'auto' region
  publicUrl: import.meta.env.VITE_CLOUDFLARE_PUBLIC_URL || 'https://94e2ffe1e7d5daf0d3de8d11c55dd2d6.r2.cloudflarestorage.com',
};

// Folder structure for organized uploads
export const UPLOAD_FOLDERS = {
  VIDEOS: 'videos',
  THUMBNAILS: 'thumbnails',
  SHORTS: 'shorts',     // For TipShorts videos
  TIPTUBE: 'tiptube',   // For TipTube videos  
  IMAGES: 'images',     // For general images
  POSTS: 'posts',       // For post media
  TEMP: 'temp',
} as const;

// File size limits (in bytes) - following web standards
export const FILE_SIZE_LIMITS = {
  VIDEO_MAX: 100 * 1024 * 1024,    // 100MB for regular videos
  SHORT_MAX: 50 * 1024 * 1024,     // 50MB for short videos
  IMAGE_MAX: 10 * 1024 * 1024,     // 10MB for images
  THUMBNAIL_MAX: 5 * 1024 * 1024,  // 5MB for thumbnails
} as const;

// Supported file types - optimized for web playback
export const SUPPORTED_FORMATS = {
  VIDEO: ['mp4', 'webm', 'mov', 'avi'],
  IMAGE: ['jpg', 'jpeg', 'png', 'webp', 'gif'],
} as const;

// Upload configuration
export const UPLOAD_CONFIG = {
  // Use presigned URLs for files larger than this size (in bytes)
  PRESIGNED_THRESHOLD: 20 * 1024 * 1024, // 20MB
  // Maximum retries for failed uploads
  MAX_UPLOAD_RETRIES: 3,
  // Timeout for upload operations (in milliseconds)
  UPLOAD_TIMEOUT: 300000, // 5 minutes
  // Chunk size for multipart uploads
  CHUNK_SIZE: 5 * 1024 * 1024, // 5MB chunks
} as const;

// Presigned URL expiration time (in seconds)
export const PRESIGNED_URL_EXPIRY = 3600; // 1 hour

// Custom domain for publicly accessible URLs
export const CLOUDFLARE_PUBLIC_DOMAIN = import.meta.env.VITE_CLOUDFLARE_PUBLIC_DOMAIN || "https://theadtip.in";

// Cloudflare Stream Configuration
export const CLOUDFLARE_STREAM_CONFIG: CloudflareStreamConfig = {
  accountId: import.meta.env.VITE_CLOUDFLARE_STREAM_ACCOUNT_ID || '94e2ffe1e7d5daf0d3de8d11c55dd2d6',
  apiToken: import.meta.env.VITE_CLOUDFLARE_STREAM_API_TOKEN || '****************************************',
  customerCode: import.meta.env.VITE_CLOUDFLARE_STREAM_CUSTOMER_CODE || '94e2ffe1e7d5daf0d3de8d11c55dd2d6',
};

// Web-specific configurations
export const WEB_UPLOAD_CONFIG = {
  // Maximum concurrent uploads
  MAX_CONCURRENT_UPLOADS: 3,
  // Progress update interval (in milliseconds)
  PROGRESS_UPDATE_INTERVAL: 500,
  // Enable compression for images
  ENABLE_IMAGE_COMPRESSION: true,
  // Image compression quality (0-1)
  IMAGE_COMPRESSION_QUALITY: 0.8,
  // Maximum image dimensions
  MAX_IMAGE_WIDTH: 1920,
  MAX_IMAGE_HEIGHT: 1080,
} as const;

// MIME type mappings
export const MIME_TYPES = {
  // Video types
  'mp4': 'video/mp4',
  'webm': 'video/webm',
  'mov': 'video/quicktime',
  'avi': 'video/x-msvideo',
  
  // Image types
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg',
  'png': 'image/png',
  'webp': 'image/webp',
  'gif': 'image/gif',
} as const;

// Upload progress interface
export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number; // bytes per second
  timeRemaining?: number; // seconds
}

// Upload result interface
export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
  metadata?: {
    size: number;
    type: string;
    duration?: number; // for videos
    dimensions?: { width: number; height: number }; // for images
  };
}

// Presigned URL info interface
export interface PresignedUrlInfo {
  uploadUrl: string;
  downloadUrl: string;
  key: string;
  expiresIn: number;
}

export default {
  CLOUDFLARE_R2_CONFIG,
  CLOUDFLARE_STREAM_CONFIG,
  UPLOAD_FOLDERS,
  FILE_SIZE_LIMITS,
  SUPPORTED_FORMATS,
  UPLOAD_CONFIG,
  WEB_UPLOAD_CONFIG,
  CLOUDFLARE_PUBLIC_DOMAIN,
  MIME_TYPES,
};
