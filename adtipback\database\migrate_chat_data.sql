-- =====================================================
-- CHAT DATA MIGRATION SCRIPT
-- =====================================================
-- This script migrates data from the old user_chat table to the new chat schema
-- Run this script AFTER creating the new schema tables

-- =====================================================
-- STEP 1: BACKUP OLD DATA (SAFETY FIRST)
-- =====================================================
-- Create backup table
CREATE TABLE IF NOT EXISTS user_chat_backup AS SELECT * FROM user_chat;

-- =====================================================
-- STEP 2: MIGRATE CONVERSATIONS
-- =====================================================
-- Create conversations from unique sender-receiver pairs
INSERT INTO conversations (type, created_by, created_at, last_activity_at)
SELECT DISTINCT
    'direct' as type,
    LEAST(sender, receiver) as created_by,
    MIN(createddate) as created_at,
    MAX(createddate) as last_activity_at
FROM user_chat 
WHERE sender IS NOT NULL 
  AND receiver IS NOT NULL 
  AND receiver != 0
  AND is_active = 1
GROUP BY LEAST(sender, receiver), GREATEST(sender, receiver);

-- =====================================================
-- STEP 3: MIGRATE CONVERSATION PARTICIPANTS
-- =====================================================
-- Insert participants for each conversation
INSERT INTO conversation_participants (conversation_id, user_id, joined_at)
SELECT DISTINCT
    c.id as conversation_id,
    uc.sender as user_id,
    MIN(uc.createddate) as joined_at
FROM conversations c
JOIN user_chat uc ON (
    (c.created_by = LEAST(uc.sender, uc.receiver))
    AND EXISTS (
        SELECT 1 FROM user_chat uc2 
        WHERE (
            (uc2.sender = LEAST(uc.sender, uc.receiver) AND uc2.receiver = GREATEST(uc.sender, uc.receiver))
            OR (uc2.sender = GREATEST(uc.sender, uc.receiver) AND uc2.receiver = LEAST(uc.sender, uc.receiver))
        )
    )
)
WHERE uc.sender IS NOT NULL 
  AND uc.receiver IS NOT NULL 
  AND uc.receiver != 0
  AND uc.is_active = 1
GROUP BY c.id, uc.sender

UNION

SELECT DISTINCT
    c.id as conversation_id,
    uc.receiver as user_id,
    MIN(uc.createddate) as joined_at
FROM conversations c
JOIN user_chat uc ON (
    (c.created_by = LEAST(uc.sender, uc.receiver))
    AND EXISTS (
        SELECT 1 FROM user_chat uc2 
        WHERE (
            (uc2.sender = LEAST(uc.sender, uc.receiver) AND uc2.receiver = GREATEST(uc.sender, uc.receiver))
            OR (uc2.sender = GREATEST(uc.sender, uc.receiver) AND uc2.receiver = LEAST(uc.sender, uc.receiver))
        )
    )
)
WHERE uc.sender IS NOT NULL 
  AND uc.receiver IS NOT NULL 
  AND uc.receiver != 0
  AND uc.is_active = 1
  AND uc.receiver NOT IN (
    SELECT user_id FROM conversation_participants cp2 WHERE cp2.conversation_id = c.id
  )
GROUP BY c.id, uc.receiver;

-- =====================================================
-- STEP 4: MIGRATE MESSAGES
-- =====================================================
-- Insert messages into new messages table
INSERT INTO messages (
    conversation_id,
    sender_id,
    message_type,
    content,
    file_url,
    file_name,
    reply_to_message_id,
    created_at,
    updated_at,
    is_deleted
)
SELECT 
    c.id as conversation_id,
    uc.sender as sender_id,
    CASE 
        WHEN uc.message_file IS NOT NULL AND uc.message_file != '' THEN
            CASE 
                WHEN LOWER(uc.message_file) LIKE '%.jpg' OR LOWER(uc.message_file) LIKE '%.jpeg' OR LOWER(uc.message_file) LIKE '%.png' OR LOWER(uc.message_file) LIKE '%.gif' THEN 'image'
                WHEN LOWER(uc.message_file) LIKE '%.mp4' OR LOWER(uc.message_file) LIKE '%.avi' OR LOWER(uc.message_file) LIKE '%.mov' THEN 'video'
                WHEN LOWER(uc.message_file) LIKE '%.mp3' OR LOWER(uc.message_file) LIKE '%.wav' OR LOWER(uc.message_file) LIKE '%.m4a' THEN 'audio'
                ELSE 'file'
            END
        ELSE 'text'
    END as message_type,
    uc.message as content,
    uc.message_file as file_url,
    CASE 
        WHEN uc.message_file IS NOT NULL AND uc.message_file != '' THEN
            SUBSTRING_INDEX(uc.message_file, '/', -1)
        ELSE NULL
    END as file_name,
    uc.parent_id as reply_to_message_id,
    uc.createddate as created_at,
    uc.updateddate as updated_at,
    CASE 
        WHEN uc.is_deleted_for_everyone = 1 THEN TRUE
        ELSE FALSE
    END as is_deleted
FROM user_chat uc
JOIN conversations c ON (
    c.created_by = LEAST(uc.sender, uc.receiver)
    AND EXISTS (
        SELECT 1 FROM conversation_participants cp1, conversation_participants cp2
        WHERE cp1.conversation_id = c.id AND cp1.user_id = uc.sender
        AND cp2.conversation_id = c.id AND cp2.user_id = uc.receiver
    )
)
WHERE uc.sender IS NOT NULL 
  AND uc.receiver IS NOT NULL 
  AND uc.receiver != 0
  AND uc.is_active = 1
ORDER BY uc.createddate;

-- =====================================================
-- STEP 5: MIGRATE MESSAGE STATUS
-- =====================================================
-- Insert message status for delivered messages
INSERT INTO message_status (message_id, user_id, status, timestamp)
SELECT 
    m.id as message_id,
    cp.user_id,
    'delivered' as status,
    m.created_at as timestamp
FROM messages m
JOIN conversation_participants cp ON m.conversation_id = cp.conversation_id
WHERE cp.user_id != m.sender_id;

-- Insert read status for messages that were seen
INSERT INTO message_status (message_id, user_id, status, timestamp)
SELECT 
    m.id as message_id,
    uc.receiver as user_id,
    'read' as status,
    COALESCE(uc.updateddate, m.created_at) as timestamp
FROM messages m
JOIN user_chat uc ON (
    m.content = uc.message 
    AND m.sender_id = uc.sender 
    AND m.created_at = uc.createddate
)
JOIN conversations c ON m.conversation_id = c.id
JOIN conversation_participants cp ON (
    c.id = cp.conversation_id 
    AND cp.user_id = uc.receiver
)
WHERE uc.is_seen = 1 OR uc.is_receiver_seen = 1;

-- =====================================================
-- STEP 6: UPDATE CONVERSATION LAST MESSAGE
-- =====================================================
-- Update last_message_id for each conversation
UPDATE conversations c
SET last_message_id = (
    SELECT m.id 
    FROM messages m 
    WHERE m.conversation_id = c.id 
    ORDER BY m.created_at DESC 
    LIMIT 1
);

-- =====================================================
-- STEP 7: INITIALIZE USER PRESENCE
-- =====================================================
-- Insert initial presence data for all users who have sent messages
INSERT INTO user_presence (user_id, status, last_seen_at)
SELECT DISTINCT 
    u.id as user_id,
    CASE 
        WHEN u.online_status = 1 THEN 'online'
        WHEN u.online_status = 2 THEN 'offline'
        ELSE 'offline'
    END as status,
    COALESCE(
        (SELECT MAX(createddate) FROM user_chat WHERE sender = u.id),
        u.updated_date
    ) as last_seen_at
FROM users u
WHERE u.id IN (
    SELECT DISTINCT sender FROM user_chat 
    UNION 
    SELECT DISTINCT receiver FROM user_chat WHERE receiver != 0
)
ON DUPLICATE KEY UPDATE
    status = VALUES(status),
    last_seen_at = VALUES(last_seen_at);

-- =====================================================
-- STEP 8: INITIALIZE CHAT SETTINGS
-- =====================================================
-- Create default chat settings for all users
INSERT INTO chat_settings (user_id)
SELECT DISTINCT u.id
FROM users u
WHERE u.id IN (
    SELECT DISTINCT sender FROM user_chat 
    UNION 
    SELECT DISTINCT receiver FROM user_chat WHERE receiver != 0
)
ON DUPLICATE KEY UPDATE user_id = VALUES(user_id);

-- =====================================================
-- STEP 9: UPDATE UNREAD COUNTS
-- =====================================================
-- Calculate and update unread counts for each participant
UPDATE conversation_participants cp
SET unread_count = (
    SELECT COUNT(*)
    FROM messages m
    LEFT JOIN message_status ms ON (
        m.id = ms.message_id 
        AND ms.user_id = cp.user_id 
        AND ms.status = 'read'
    )
    WHERE m.conversation_id = cp.conversation_id
    AND m.sender_id != cp.user_id
    AND ms.id IS NULL
    AND m.is_deleted = FALSE
);

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these queries to verify the migration

-- Check conversation count
SELECT 'Conversations migrated:' as info, COUNT(*) as count FROM conversations;

-- Check participants count
SELECT 'Participants migrated:' as info, COUNT(*) as count FROM conversation_participants;

-- Check messages count
SELECT 'Messages migrated:' as info, COUNT(*) as count FROM messages;

-- Check message status count
SELECT 'Message statuses migrated:' as info, COUNT(*) as count FROM message_status;

-- Check user presence count
SELECT 'User presence records:' as info, COUNT(*) as count FROM user_presence;

-- Check for any orphaned data
SELECT 'Orphaned messages (no conversation):' as info, COUNT(*) as count 
FROM messages m 
LEFT JOIN conversations c ON m.conversation_id = c.id 
WHERE c.id IS NULL;

-- =====================================================
-- CLEANUP (OPTIONAL - RUN ONLY AFTER VERIFICATION)
-- =====================================================
-- Uncomment these lines only after verifying the migration is successful

-- -- Rename old table (keep as backup)
-- RENAME TABLE user_chat TO user_chat_old;
-- 
-- -- Drop old indexes that are no longer needed
-- -- DROP INDEX idx_createddate ON user_chat_old;
-- -- DROP INDEX idx_sender_receiver_date ON user_chat_old;

-- =====================================================
-- POST-MIGRATION OPTIMIZATIONS
-- =====================================================
-- Analyze tables for better query performance
ANALYZE TABLE conversations;
ANALYZE TABLE conversation_participants;
ANALYZE TABLE messages;
ANALYZE TABLE message_status;
ANALYZE TABLE user_presence;
ANALYZE TABLE chat_settings;

-- Update table statistics
OPTIMIZE TABLE conversations;
OPTIMIZE TABLE conversation_participants;
OPTIMIZE TABLE messages;
OPTIMIZE TABLE message_status;
OPTIMIZE TABLE user_presence;
OPTIMIZE TABLE chat_settings;
