import React, { useState, useRef, useEffect, memo } from 'react';
import { logDebug, logError } from '../utils/ProductionLogger';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  width?: number;
  height?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: string;
  fallback?: string;
  onLoad?: () => void;
  onError?: (error: Event) => void;
  quality?: number; // 1-100
  format?: 'webp' | 'jpeg' | 'png' | 'auto';
  sizes?: string;
  priority?: boolean;
}

const OptimizedImage = memo(({
  src,
  alt,
  className = '',
  style,
  width,
  height,
  loading = 'lazy',
  placeholder,
  fallback,
  onLoad,
  onError,
  quality = 80,
  format = 'auto',
  sizes,
  priority = false,
}: OptimizedImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isInView, setIsInView] = useState(!loading || loading === 'eager' || priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Generate optimized image URL
  const generateOptimizedUrl = (originalSrc: string): string => {
    try {
      // For theadtip.in URLs, don't add optimization parameters for now
      // as they might be causing loading issues
      if (originalSrc.includes('theadtip.in')) {
        return originalSrc;
      }

      // If it's a Cloudflare storage URL, we can add optimization parameters
      if (originalSrc.includes('cloudflarestorage.com')) {
        const url = new URL(originalSrc);

        // Add Cloudflare Image Resizing parameters if width/height specified
        if (width || height) {
          url.searchParams.set('width', width?.toString() || 'auto');
          url.searchParams.set('height', height?.toString() || 'auto');
          url.searchParams.set('fit', 'cover');
        }

        // Add quality parameter
        if (quality < 100) {
          url.searchParams.set('quality', quality.toString());
        }

        // Add format parameter
        if (format !== 'auto') {
          url.searchParams.set('format', format);
        }

        return url.toString();
      }

      // For other URLs, return as-is
      return originalSrc;
    } catch (error) {
      logError('OptimizedImage', 'Failed to generate optimized URL', error as Error);
      return originalSrc;
    }
  };

  // Set up intersection observer for lazy loading
  useEffect(() => {
    if (loading === 'lazy' && !priority && !isInView) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              setIsInView(true);
              observerRef.current?.disconnect();
            }
          });
        },
        {
          rootMargin: '50px', // Start loading 50px before the image enters viewport
          threshold: 0.1,
        }
      );

      if (imgRef.current) {
        observerRef.current.observe(imgRef.current);
      }
    }

    return () => {
      observerRef.current?.disconnect();
    };
  }, [loading, priority, isInView]);

  // Update current src when in view
  useEffect(() => {
    if (isInView && src) {
      const optimizedSrc = generateOptimizedUrl(src);
      setCurrentSrc(optimizedSrc);
      
      logDebug('OptimizedImage', 'Loading optimized image', {
        original: src,
        optimized: optimizedSrc,
        width,
        height,
        quality,
        format,
      });
    }
  }, [isInView, src, width, height, quality, format, generateOptimizedUrl]);

  const handleLoad = () => {
    setIsLoaded(true);
    setHasError(false);
    onLoad?.();
    
    logDebug('OptimizedImage', 'Image loaded successfully', {
      src: currentSrc,
      alt,
    });
  };

  const handleError = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    setHasError(true);
    setIsLoaded(false);

    logError('OptimizedImage', 'Image failed to load', new Error(`Failed to load image: ${currentSrc}`), {
      originalSrc: src,
      currentSrc,
      fallback,
      alt,
    });

    // Try fallback image if available and not already tried
    if (fallback && currentSrc !== fallback && !currentSrc.includes('placeholder') && !currentSrc.includes('via.placeholder')) {
      logDebug('OptimizedImage', 'Trying fallback image', { fallback, previousSrc: currentSrc });
      setCurrentSrc(fallback);
      setHasError(false);
      return;
    }

    // Try original source without optimization if it was optimized
    if (currentSrc !== src && !currentSrc.includes('placeholder') && !currentSrc.includes('via.placeholder')) {
      logDebug('OptimizedImage', 'Trying original source', { original: src, previousSrc: currentSrc });
      setCurrentSrc(src);
      setHasError(false);
      return;
    }

    // If we still have an error and no fallback worked, use a generic placeholder
    if (!currentSrc.includes('via.placeholder')) {
      const placeholderUrl = `https://via.placeholder.com/${width || 400}x${height || 300}/e5e7eb/9ca3af?text=Image+Not+Available`;
      logDebug('OptimizedImage', 'Using generic placeholder', { placeholderUrl });
      setCurrentSrc(placeholderUrl);
      setHasError(false);
      return;
    }

    onError?.(event.nativeEvent);
  };

  // Generate srcSet for responsive images
  const generateSrcSet = (): string | undefined => {
    if (!sizes || !currentSrc) return undefined;
    
    try {
      const baseUrl = new URL(currentSrc);
      const srcSet: string[] = [];
      
      // Generate different sizes (1x, 1.5x, 2x)
      const multipliers = [1, 1.5, 2];
      
      multipliers.forEach((multiplier) => {
        const scaledWidth = width ? Math.round(width * multiplier) : undefined;
        const scaledHeight = height ? Math.round(height * multiplier) : undefined;
        
        if (scaledWidth || scaledHeight) {
          const url = new URL(baseUrl);
          if (scaledWidth) url.searchParams.set('width', scaledWidth.toString());
          if (scaledHeight) url.searchParams.set('height', scaledHeight.toString());
          
          srcSet.push(`${url.toString()} ${multiplier}x`);
        }
      });
      
      return srcSet.length > 0 ? srcSet.join(', ') : undefined;
    } catch (error) {
      logError('OptimizedImage', 'Failed to generate srcSet', error as Error);
      return undefined;
    }
  };

  // Render placeholder while loading
  const renderPlaceholder = () => {
    if (placeholder) {
      return (
        <img
          src={placeholder}
          alt=""
          className={`${className} transition-opacity duration-300 ${isLoaded ? 'opacity-0' : 'opacity-100'}`}
          style={{
            ...style,
            position: isLoaded ? 'absolute' : 'static',
            top: isLoaded ? 0 : 'auto',
            left: isLoaded ? 0 : 'auto',
            width,
            height,
          }}
        />
      );
    }
    
    // Default placeholder
    return (
      <div
        className={`bg-gray-200 animate-pulse flex items-center justify-center ${className} ${isLoaded ? 'hidden' : ''}`}
        style={{
          width: width || '100%',
          height: height || '200px',
          ...style,
        }}
      >
        <svg
          className="w-8 h-8 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>
    );
  };

  // Render error state
  const renderError = () => (
    <div
      className={`bg-gray-100 border border-gray-300 flex items-center justify-center text-gray-500 ${className}`}
      style={{
        width: width || '100%',
        height: height || '200px',
        ...style,
      }}
    >
      <div className="text-center">
        <svg
          className="w-8 h-8 mx-auto mb-2 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <p className="text-sm">Failed to load image</p>
      </div>
    </div>
  );

  if (hasError && !fallback) {
    return renderError();
  }

  return (
    <div className="relative" style={{ width, height }}>
      {/* Placeholder */}
      {!isLoaded && renderPlaceholder()}
      
      {/* Main image */}
      {isInView && currentSrc && (
        <img
          ref={imgRef}
          src={currentSrc}
          srcSet={generateSrcSet()}
          sizes={sizes}
          alt={alt}
          className={`${className} transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
          style={{
            ...style,
            width,
            height,
            objectFit: style?.objectFit || 'cover',
          }}
          loading={priority ? 'eager' : loading}
          onLoad={handleLoad}
          onError={handleError}
          decoding="async"
        />
      )}
    </div>
  );
});

OptimizedImage.displayName = 'OptimizedImage';

export default OptimizedImage;
