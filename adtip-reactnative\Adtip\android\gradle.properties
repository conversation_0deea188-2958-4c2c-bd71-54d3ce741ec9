# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a

# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=true

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true

android.useFullClasspathForDexingTransform=true

# ================================================================================================
# PRODUCTION OPTIMIZATION SETTINGS
# ================================================================================================

# Enable R8 full mode with safer configuration
# Note: R8 full mode is enabled but with safer rules in proguard-rules-r8.pro
android.enableR8.fullMode=true

# Enable resource shrinking
android.enableResourceOptimizations=true

# Enable build cache for faster builds
org.gradle.caching=true

# Enable parallel builds
org.gradle.parallel=true

# Configure workers for parallel execution
org.gradle.workers.max=4

# Enable configuration cache
org.gradle.configuration-cache=false

# Enable build scan for performance insights
org.gradle.build-scan.enabled=false

# Optimize DEX compilation
android.enableDexingArtifactTransform=true

# Enable incremental annotation processing
kapt.incremental.apt=true

# Enable incremental compilation
kotlin.incremental=true

# Use incremental DEX compilation
android.enableIncrementalDesugaring=true

# Enable non-transitive R class generation
android.nonTransitiveRClass=true

# Enable non-final resource IDs
android.nonFinalResIds=true

# Optimize PNG files
android.enablePngCrunchInReleaseBuilds=true

# Enable AAPT2 for better resource processing
android.enableAapt2=true

# Use legacy multidex for compatibility
android.useAndroidX=true
android.enableJetifier=true

# Disable unnecessary features for production
#android.enableBuildCache=true
android.enableD8.desugaring=true

# React Native specific optimizations - ARM only (real devices)
reactNativeArchitectures=armeabi-v7a,arm64-v8a

# Hermes optimizations
hermesEnabled=true

# New architecture optimizations
newArchEnabled=true

# Flipper disabled for production
FLIPPER_VERSION=0.182.0