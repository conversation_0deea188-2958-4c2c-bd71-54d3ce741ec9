# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_SEQ_FOLD_LEFT_512_HPP
# define BOOST_PREPROCESSOR_SEQ_FOLD_LEFT_512_HPP
#
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_257(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_258(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_259(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_260(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_261(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_262(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_263(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_264(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_265(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_266(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_267(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_268(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_269(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_270(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_271(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_272(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_273(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_274(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_275(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_276(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_277(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_278(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_279(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_280(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_281(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_282(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_283(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_284(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_285(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_286(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_287(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_288(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_289(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_290(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_291(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_292(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_293(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_294(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_295(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_296(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_297(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_298(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_299(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_300(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_301(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_302(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_303(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_304(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_305(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_306(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_307(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_308(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_309(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_310(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_311(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_312(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_313(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_314(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_315(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_316(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_317(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_318(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_319(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_320(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_321(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_322(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_323(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_324(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_325(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_326(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_327(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_328(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_329(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_330(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_331(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_332(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_333(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_334(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_335(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_336(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_337(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_338(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_339(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_340(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_341(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_342(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_343(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_344(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_345(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_346(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_347(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_348(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_349(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_350(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_351(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_352(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_353(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_354(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_355(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_356(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_357(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_358(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_359(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_360(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_361(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_362(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_363(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_364(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_365(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_366(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_367(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_368(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_369(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_370(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_371(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_372(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_373(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_374(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_375(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_376(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_377(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_378(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_379(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_380(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_381(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_382(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_383(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_384(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_385(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_386(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_387(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_388(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_389(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_390(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_391(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_392(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_393(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_394(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_395(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_396(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_397(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_398(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_399(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_400(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_401(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_402(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_403(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_404(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_405(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_406(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_407(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_408(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_409(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_410(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_411(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_412(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_413(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_414(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_415(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_416(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_417(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_418(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_419(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_420(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_421(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_422(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_423(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_424(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_425(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_426(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_427(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_428(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_429(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_430(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_431(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_432(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_433(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_434(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_435(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_436(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_437(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_438(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_439(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_440(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_441(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_442(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_443(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_444(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_445(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_446(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_447(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_448(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_449(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_450(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_451(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_452(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_453(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_454(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_455(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_456(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_457(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_458(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_459(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_460(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_461(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_462(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_463(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_464(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_465(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_466(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_467(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_468(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_469(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_470(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_471(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_472(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_473(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_474(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_475(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_476(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_477(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_478(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_479(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_480(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_481(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_482(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_483(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_484(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_485(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_486(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_487(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_488(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_489(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_490(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_491(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_492(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_493(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_494(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_495(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_496(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_497(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_498(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_499(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_500(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_501(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_502(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_503(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_504(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_505(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_506(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_507(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_508(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_509(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_510(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_511(op, st, ss, sz) 0
# define BOOST_PP_SEQ_FOLD_LEFT_CHECK_BOOST_PP_SEQ_FOLD_LEFT_I_512(op, st, ss, sz) 0
#
# define BOOST_PP_SEQ_FOLD_LEFT_257(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_257(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_258(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_258(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_259(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_259(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_260(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_260(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_261(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_261(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_262(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_262(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_263(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_263(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_264(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_264(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_265(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_265(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_266(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_266(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_267(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_267(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_268(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_268(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_269(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_269(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_270(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_270(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_271(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_271(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_272(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_272(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_273(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_273(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_274(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_274(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_275(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_275(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_276(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_276(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_277(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_277(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_278(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_278(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_279(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_279(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_280(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_280(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_281(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_281(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_282(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_282(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_283(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_283(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_284(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_284(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_285(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_285(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_286(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_286(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_287(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_287(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_288(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_288(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_289(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_289(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_290(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_290(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_291(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_291(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_292(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_292(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_293(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_293(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_294(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_294(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_295(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_295(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_296(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_296(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_297(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_297(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_298(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_298(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_299(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_299(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_300(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_300(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_301(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_301(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_302(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_302(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_303(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_303(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_304(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_304(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_305(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_305(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_306(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_306(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_307(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_307(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_308(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_308(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_309(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_309(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_310(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_310(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_311(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_311(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_312(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_312(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_313(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_313(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_314(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_314(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_315(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_315(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_316(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_316(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_317(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_317(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_318(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_318(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_319(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_319(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_320(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_320(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_321(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_321(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_322(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_322(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_323(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_323(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_324(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_324(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_325(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_325(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_326(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_326(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_327(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_327(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_328(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_328(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_329(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_329(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_330(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_330(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_331(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_331(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_332(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_332(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_333(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_333(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_334(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_334(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_335(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_335(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_336(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_336(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_337(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_337(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_338(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_338(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_339(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_339(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_340(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_340(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_341(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_341(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_342(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_342(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_343(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_343(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_344(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_344(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_345(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_345(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_346(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_346(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_347(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_347(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_348(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_348(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_349(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_349(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_350(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_350(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_351(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_351(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_352(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_352(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_353(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_353(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_354(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_354(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_355(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_355(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_356(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_356(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_357(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_357(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_358(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_358(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_359(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_359(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_360(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_360(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_361(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_361(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_362(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_362(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_363(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_363(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_364(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_364(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_365(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_365(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_366(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_366(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_367(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_367(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_368(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_368(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_369(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_369(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_370(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_370(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_371(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_371(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_372(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_372(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_373(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_373(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_374(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_374(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_375(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_375(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_376(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_376(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_377(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_377(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_378(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_378(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_379(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_379(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_380(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_380(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_381(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_381(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_382(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_382(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_383(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_383(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_384(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_384(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_385(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_385(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_386(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_386(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_387(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_387(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_388(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_388(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_389(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_389(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_390(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_390(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_391(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_391(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_392(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_392(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_393(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_393(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_394(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_394(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_395(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_395(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_396(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_396(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_397(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_397(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_398(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_398(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_399(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_399(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_400(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_400(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_401(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_401(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_402(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_402(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_403(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_403(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_404(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_404(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_405(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_405(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_406(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_406(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_407(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_407(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_408(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_408(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_409(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_409(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_410(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_410(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_411(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_411(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_412(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_412(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_413(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_413(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_414(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_414(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_415(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_415(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_416(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_416(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_417(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_417(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_418(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_418(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_419(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_419(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_420(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_420(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_421(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_421(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_422(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_422(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_423(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_423(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_424(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_424(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_425(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_425(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_426(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_426(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_427(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_427(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_428(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_428(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_429(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_429(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_430(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_430(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_431(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_431(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_432(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_432(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_433(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_433(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_434(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_434(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_435(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_435(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_436(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_436(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_437(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_437(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_438(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_438(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_439(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_439(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_440(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_440(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_441(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_441(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_442(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_442(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_443(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_443(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_444(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_444(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_445(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_445(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_446(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_446(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_447(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_447(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_448(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_448(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_449(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_449(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_450(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_450(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_451(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_451(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_452(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_452(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_453(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_453(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_454(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_454(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_455(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_455(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_456(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_456(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_457(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_457(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_458(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_458(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_459(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_459(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_460(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_460(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_461(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_461(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_462(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_462(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_463(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_463(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_464(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_464(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_465(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_465(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_466(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_466(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_467(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_467(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_468(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_468(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_469(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_469(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_470(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_470(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_471(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_471(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_472(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_472(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_473(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_473(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_474(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_474(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_475(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_475(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_476(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_476(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_477(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_477(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_478(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_478(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_479(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_479(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_480(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_480(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_481(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_481(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_482(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_482(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_483(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_483(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_484(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_484(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_485(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_485(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_486(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_486(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_487(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_487(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_488(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_488(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_489(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_489(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_490(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_490(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_491(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_491(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_492(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_492(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_493(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_493(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_494(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_494(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_495(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_495(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_496(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_496(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_497(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_497(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_498(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_498(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_499(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_499(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_500(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_500(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_501(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_501(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_502(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_502(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_503(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_503(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_504(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_504(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_505(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_505(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_506(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_506(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_507(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_507(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_508(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_508(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_509(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_509(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_510(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_510(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_511(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_511(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
# define BOOST_PP_SEQ_FOLD_LEFT_512(op, st, ss) BOOST_PP_SEQ_FOLD_LEFT_I_512(op, st, ss, BOOST_PP_SEQ_SIZE(ss))
#
# define BOOST_PP_SEQ_FOLD_LEFT_I_257(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_258, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(258, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_258(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_259, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(259, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_259(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_260, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(260, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_260(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_261, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(261, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_261(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_262, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(262, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_262(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_263, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(263, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_263(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_264, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(264, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_264(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_265, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(265, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_265(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_266, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(266, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_266(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_267, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(267, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_267(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_268, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(268, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_268(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_269, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(269, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_269(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_270, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(270, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_270(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_271, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(271, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_271(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_272, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(272, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_272(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_273, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(273, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_273(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_274, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(274, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_274(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_275, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(275, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_275(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_276, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(276, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_276(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_277, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(277, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_277(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_278, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(278, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_278(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_279, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(279, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_279(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_280, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(280, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_280(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_281, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(281, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_281(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_282, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(282, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_282(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_283, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(283, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_283(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_284, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(284, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_284(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_285, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(285, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_285(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_286, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(286, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_286(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_287, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(287, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_287(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_288, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(288, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_288(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_289, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(289, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_289(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_290, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(290, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_290(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_291, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(291, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_291(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_292, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(292, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_292(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_293, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(293, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_293(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_294, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(294, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_294(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_295, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(295, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_295(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_296, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(296, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_296(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_297, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(297, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_297(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_298, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(298, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_298(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_299, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(299, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_299(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_300, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(300, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_300(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_301, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(301, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_301(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_302, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(302, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_302(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_303, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(303, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_303(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_304, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(304, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_304(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_305, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(305, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_305(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_306, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(306, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_306(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_307, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(307, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_307(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_308, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(308, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_308(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_309, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(309, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_309(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_310, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(310, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_310(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_311, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(311, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_311(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_312, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(312, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_312(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_313, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(313, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_313(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_314, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(314, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_314(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_315, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(315, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_315(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_316, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(316, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_316(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_317, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(317, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_317(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_318, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(318, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_318(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_319, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(319, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_319(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_320, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(320, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_320(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_321, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(321, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_321(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_322, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(322, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_322(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_323, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(323, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_323(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_324, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(324, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_324(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_325, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(325, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_325(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_326, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(326, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_326(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_327, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(327, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_327(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_328, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(328, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_328(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_329, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(329, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_329(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_330, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(330, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_330(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_331, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(331, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_331(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_332, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(332, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_332(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_333, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(333, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_333(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_334, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(334, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_334(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_335, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(335, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_335(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_336, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(336, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_336(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_337, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(337, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_337(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_338, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(338, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_338(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_339, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(339, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_339(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_340, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(340, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_340(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_341, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(341, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_341(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_342, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(342, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_342(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_343, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(343, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_343(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_344, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(344, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_344(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_345, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(345, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_345(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_346, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(346, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_346(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_347, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(347, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_347(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_348, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(348, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_348(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_349, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(349, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_349(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_350, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(350, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_350(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_351, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(351, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_351(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_352, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(352, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_352(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_353, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(353, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_353(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_354, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(354, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_354(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_355, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(355, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_355(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_356, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(356, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_356(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_357, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(357, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_357(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_358, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(358, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_358(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_359, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(359, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_359(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_360, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(360, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_360(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_361, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(361, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_361(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_362, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(362, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_362(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_363, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(363, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_363(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_364, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(364, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_364(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_365, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(365, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_365(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_366, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(366, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_366(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_367, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(367, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_367(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_368, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(368, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_368(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_369, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(369, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_369(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_370, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(370, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_370(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_371, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(371, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_371(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_372, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(372, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_372(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_373, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(373, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_373(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_374, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(374, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_374(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_375, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(375, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_375(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_376, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(376, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_376(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_377, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(377, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_377(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_378, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(378, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_378(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_379, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(379, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_379(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_380, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(380, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_380(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_381, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(381, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_381(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_382, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(382, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_382(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_383, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(383, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_383(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_384, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(384, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_384(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_385, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(385, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_385(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_386, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(386, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_386(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_387, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(387, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_387(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_388, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(388, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_388(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_389, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(389, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_389(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_390, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(390, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_390(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_391, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(391, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_391(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_392, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(392, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_392(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_393, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(393, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_393(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_394, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(394, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_394(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_395, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(395, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_395(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_396, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(396, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_396(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_397, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(397, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_397(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_398, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(398, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_398(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_399, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(399, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_399(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_400, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(400, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_400(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_401, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(401, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_401(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_402, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(402, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_402(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_403, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(403, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_403(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_404, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(404, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_404(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_405, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(405, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_405(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_406, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(406, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_406(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_407, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(407, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_407(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_408, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(408, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_408(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_409, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(409, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_409(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_410, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(410, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_410(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_411, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(411, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_411(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_412, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(412, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_412(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_413, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(413, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_413(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_414, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(414, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_414(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_415, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(415, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_415(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_416, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(416, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_416(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_417, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(417, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_417(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_418, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(418, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_418(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_419, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(419, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_419(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_420, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(420, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_420(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_421, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(421, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_421(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_422, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(422, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_422(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_423, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(423, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_423(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_424, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(424, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_424(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_425, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(425, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_425(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_426, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(426, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_426(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_427, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(427, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_427(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_428, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(428, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_428(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_429, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(429, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_429(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_430, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(430, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_430(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_431, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(431, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_431(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_432, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(432, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_432(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_433, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(433, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_433(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_434, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(434, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_434(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_435, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(435, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_435(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_436, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(436, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_436(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_437, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(437, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_437(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_438, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(438, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_438(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_439, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(439, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_439(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_440, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(440, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_440(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_441, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(441, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_441(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_442, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(442, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_442(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_443, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(443, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_443(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_444, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(444, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_444(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_445, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(445, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_445(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_446, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(446, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_446(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_447, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(447, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_447(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_448, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(448, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_448(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_449, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(449, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_449(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_450, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(450, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_450(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_451, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(451, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_451(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_452, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(452, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_452(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_453, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(453, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_453(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_454, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(454, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_454(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_455, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(455, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_455(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_456, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(456, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_456(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_457, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(457, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_457(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_458, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(458, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_458(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_459, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(459, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_459(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_460, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(460, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_460(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_461, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(461, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_461(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_462, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(462, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_462(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_463, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(463, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_463(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_464, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(464, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_464(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_465, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(465, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_465(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_466, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(466, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_466(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_467, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(467, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_467(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_468, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(468, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_468(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_469, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(469, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_469(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_470, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(470, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_470(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_471, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(471, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_471(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_472, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(472, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_472(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_473, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(473, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_473(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_474, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(474, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_474(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_475, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(475, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_475(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_476, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(476, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_476(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_477, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(477, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_477(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_478, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(478, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_478(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_479, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(479, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_479(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_480, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(480, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_480(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_481, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(481, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_481(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_482, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(482, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_482(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_483, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(483, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_483(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_484, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(484, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_484(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_485, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(485, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_485(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_486, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(486, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_486(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_487, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(487, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_487(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_488, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(488, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_488(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_489, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(489, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_489(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_490, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(490, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_490(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_491, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(491, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_491(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_492, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(492, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_492(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_493, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(493, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_493(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_494, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(494, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_494(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_495, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(495, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_495(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_496, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(496, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_496(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_497, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(497, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_497(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_498, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(498, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_498(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_499, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(499, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_499(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_500, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(500, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_500(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_501, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(501, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_501(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_502, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(502, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_502(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_503, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(503, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_503(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_504, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(504, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_504(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_505, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(505, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_505(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_506, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(506, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_506(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_507, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(507, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_507(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_508, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(508, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_508(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_509, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(509, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_509(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_510, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(510, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_510(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_511, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(511, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_511(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_512, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(512, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
# define BOOST_PP_SEQ_FOLD_LEFT_I_512(op, st, ss, sz) BOOST_PP_IF(BOOST_PP_DEC(sz), BOOST_PP_SEQ_FOLD_LEFT_I_513, BOOST_PP_SEQ_FOLD_LEFT_F)(op, op(513, st, BOOST_PP_SEQ_HEAD(ss)), BOOST_PP_SEQ_TAIL(ss), BOOST_PP_DEC(sz))
#
# endif
