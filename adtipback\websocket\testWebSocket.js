/*

const WebSocket = require("ws");
// WebSocket server URL
const WS_URL = "ws://25.56.596.54:8000"; // Replace with "ws://localhost:8000" if testing locally

// Simulate 4 players
const playerIds = [1, 2, 3, 4];
const challengeAmount = 5; // Dynamic challenge amount for testing
const clients = [];

function createClient(userId) {
  const ws = new WebSocket(WS_URL);

  ws.on("open", () => {
    console.log(`Player ${userId} connected to WebSocket server`);
    // Send join message
    ws.send(
      JSON.stringify({
        type: "join",
        userId: userId,
        challengeAmount: challengeAmount,
      })
    );
  });

  ws.on("message", (message) => {
    const data = JSON.parse(message.toString());
    console.log(`Player ${userId} received message:`, data);

    // Handle different message types
    if (data.type === "joined") {
      console.log(`Player ${userId} joined room ${data.roomId}`);
    } else if (data.type === "start") {
      console.log(`Game started for Player ${userId}! Room: ${data.roomId}, Game ID: ${data.gameId}`);
      // Simulate a move after the game starts
      setTimeout(() => {
        ws.send(
          JSON.stringify({
            type: "move",
            roomId: data.roomId,
            userId: userId,
            move: { position: userId * 10, piece: 1, isWinner: userId === 1 }, // Player 1 wins for testing
          })
        );
      }, 1000);
    } else if (data.type === "move") {
      console.log(`Move by Player ${data.userId}:`, data.move);
    } else if (data.type === "game-over") {
      console.log(`Game over for Player ${userId}! Winner: ${data.winner}`);
      // Close the connection after the game ends
      ws.close();
    } else if (data.type === "error") {
      console.error(`Error for Player ${userId}: ${data.message}`);
      ws.close();
    }
  });

  ws.on("close", () => {
    console.log(`Player ${userId} disconnected`);
  });

  ws.on("error", (err) => {
    console.error(`WebSocket error for Player ${userId}:`, err.message);
  });

  return ws;
}

// Create 4 clients (players)
playerIds.forEach((userId) => {
  const client = createClient(userId);
  clients.push(client);
});

// Simulate a player quitting (optional test case)
setTimeout(() => {
  console.log("Simulating Player 2 quitting...");
  clients[1].send(
    JSON.stringify({
      type: "quit",
      userId: 2,
    })
  );
}, 5000); // Quit after 5 seconds (comment this out to test a normal game flow)

*/