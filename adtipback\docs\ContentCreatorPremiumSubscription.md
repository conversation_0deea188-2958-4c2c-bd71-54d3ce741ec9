# Content Creator Premium Subscription System

This document describes the content creator premium subscription system that follows the same pattern as the user premium subscription system.

## Database Tables

### 1. `content_creator_subscriptions`
Stores the main subscription details for content creators.

**Fields:**
- `id` - Primary key
- `user_id` - Foreign key to users table
- `razorpay_plan_id` - Razorpay plan ID (e.g., "plan_QngKCqLAUkhxbH")
- `razorpay_subscription_id` - Unique Razorpay subscription ID
- `status` - Subscription status (created, active, authenticated, inactive, cancelled, completed, halted)
- `total_count` - Total number of billing cycles (default: 60)
- `paid_count` - Number of successful payments
- `current_start_at` - Start of current billing cycle
- `current_end_at` - End of current billing cycle
- `charge_at` - When the subscription was last charged
- `start_at` - When subscription started
- `ended_at` - When subscription ended
- `customer_notify` - Whether to notify customer
- `notes` - Additional metadata (JSON)
- `created_at` - Record creation timestamp
- `updated_at` - Record update timestamp

### 2. `content_creator_subscription_transactions`
Stores individual payment transactions for content creator subscriptions.

**Fields:**
- `id` - Primary key
- `user_id` - Foreign key to users table
- `subscription_id` - Razorpay subscription ID
- `order_id` - Razorpay order ID
- `payment_id` - Razorpay payment ID
- `amount` - Payment amount
- `currency` - Payment currency (default: INR)
- `status` - Transaction status (pending, success, failed, cancelled)
- `transaction_for` - Transaction purpose (default: 'content_creator_premium_subscription')
- `plan_id` - Razorpay plan ID
- `billing_cycle` - Human readable billing cycle (e.g., "1 month", "3 months")
- `created_at` - Record creation timestamp
- `updated_at` - Record update timestamp

### 3. `content_creator_razorpay_plans`
Caches Razorpay plan details for better performance.

**Fields:**
- `id` - Razorpay plan ID (primary key)
- `name` - Plan name
- `description` - Plan description
- `amount` - Plan amount
- `currency` - Plan currency
- `period` - Number of periods
- `interval` - Billing interval (monthly, yearly, etc.)
- `billing_cycle` - Human readable billing cycle
- `is_active` - Whether plan is active
- `created_at` - Record creation timestamp
- `updated_at` - Record update timestamp

### 4. Users Table Updates
Added two new columns to the users table:
- `content_creator_premium_status` - ENUM('inactive', 'active', 'expired')
- `content_creator_premium_expires_at` - TIMESTAMP

## Available Plans

The system supports 4 content creator premium plans:

1. **1 Month Plan** - `plan_QngI8gqKZrHLbK` - ₹149/month
2. **3 Months Plan** - `plan_QngId7JYoii1wE` - ₹349/3 months
3. **6 Months Plan** - `plan_QngJ4kIVdFaRUI` - ₹599/6 months
4. **12 Months Plan** - `plan_QngKCqLAUkhxbH` - ₹999/12 months

## API Endpoints

### Get Available Plans
```
GET /content-premium-plans
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "plans": [
    {
      "id": "plan_QngI8gqKZrHLbK",
      "name": "Content Creator Premium - 1 Month",
      "amount": 149,
      "currency": "INR",
      "period": 1,
      "interval": "monthly",
      "description": "Access to premium content creation features for 1 month"
    }
  ]
}
```

### Create Subscription
```
POST /content-premium-subscription
Authorization: Bearer <token>
Content-Type: application/json

{
  "plan_id": "plan_QngI8gqKZrHLbK",
  "user_id": 123
}
```

**Response:**
```json
{
  "status": true,
  "subscription_id": "sub_ABC123",
  "key_id": "rzp_test_..."
}
```

### Get Subscription Status
```
GET /content-premium-status/:userId
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "data": {
    "status": "active",
    "current_end_at": "2024-02-15T10:30:00.000Z",
    "razorpay_plan_id": "plan_QngI8gqKZrHLbK",
    "razorpay_subscription_id": "sub_ABC123",
    "paid_count": 1,
    "total_count": 60,
    "plan_name": "Content Creator Premium - 1 Month",
    "plan_description": "Access to premium content creation features for 1 month",
    "amount": 149,
    "currency": "INR",
    "billing_cycle": "1 month",
    "is_active": true
  }
}
```

### Cancel Subscription
```
POST /content-premium-cancel
Authorization: Bearer <token>
Content-Type: application/json

{
  "user_id": 123
}
```

**Response:**
```json
{
  "status": true,
  "message": "Content creator subscription cancellation initiated. It will be fully cancelled at the end of the current billing cycle."
}
```

### Webhook Handler
```
POST /content-premium-webhook
Content-Type: application/json
X-Razorpay-Signature: <signature>
```

Handles Razorpay webhook events for subscription updates.

## Webhook Events

The system handles the following Razorpay webhook events:

1. **subscription.charged** - When a subscription payment is successful
2. **subscription.cancelled** - When a subscription is cancelled
3. **subscription.halted** - When a subscription is halted
4. **subscription.completed** - When a subscription is completed

## Implementation Notes

1. **Subscription Type**: All content creator subscriptions include `subscription_type: 'content_creator_premium'` in the notes to distinguish them from user premium subscriptions.

2. **User Status Tracking**: The system automatically updates the user's `content_creator_premium_status` and `content_creator_premium_expires_at` fields when subscriptions are charged or cancelled.

3. **Transaction Recording**: All successful payments are recorded in the `content_creator_subscription_transactions` table for audit purposes.

4. **Backward Compatibility**: Legacy endpoints are maintained for backward compatibility with existing frontend code.

## Setup Instructions

1. Run the SQL script `database/content_creator_subscription_tables.sql` to create the required tables.

2. Ensure the Razorpay plan IDs are correctly configured in your Razorpay dashboard.

3. Set up the webhook URL in your Razorpay dashboard to point to `/content-premium-webhook`.

4. Configure the `RAZORPAY_WEBHOOK_SECRET` environment variable for webhook signature verification.

## Frontend Integration

The frontend can integrate with this system using the same pattern as the user premium subscription:

1. Fetch available plans using `/content-premium-plans`
2. Create subscription using `/content-premium-subscription`
3. Check subscription status using `/content-premium-status/:userId`
4. Cancel subscription using `/content-premium-cancel`

The subscription flow follows the standard Razorpay subscription integration pattern. 