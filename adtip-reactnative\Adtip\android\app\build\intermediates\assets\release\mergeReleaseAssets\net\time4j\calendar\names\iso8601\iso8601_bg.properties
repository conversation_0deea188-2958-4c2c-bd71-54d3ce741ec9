# months
M(a)_1=яну
M(a)_2=фев
M(a)_3=март
M(a)_4=апр
M(a)_5=май
M(a)_6=юни
M(a)_7=юли
M(a)_8=авг
M(a)_9=сеп
M(a)_10=окт
M(a)_11=ное
M(a)_12=дек

M(n)_1=я
M(n)_2=ф
M(n)_3=м
M(n)_4=а
M(n)_5=м
M(n)_6=ю
M(n)_7=ю
M(n)_8=а
M(n)_9=с
M(n)_10=о
M(n)_11=н
M(n)_12=д

M(w)_1=януари
M(w)_2=февруари
M(w)_3=март
M(w)_4=април
M(w)_5=май
M(w)_6=юни
M(w)_7=юли
M(w)_8=август
M(w)_9=септември
M(w)_10=октомври
M(w)_11=ноември
M(w)_12=декември

M(A)_1=яну
M(A)_2=фев
M(A)_3=март
M(A)_4=апр
M(A)_5=май
M(A)_6=юни
M(A)_7=юли
M(A)_8=авг
M(A)_9=сеп
M(A)_10=окт
M(A)_11=ное
M(A)_12=дек

M(N)_1=я
M(N)_2=ф
M(N)_3=м
M(N)_4=а
M(N)_5=м
M(N)_6=ю
M(N)_7=ю
M(N)_8=а
M(N)_9=с
M(N)_10=о
M(N)_11=н
M(N)_12=д

M(W)_1=януари
M(W)_2=февруари
M(W)_3=март
M(W)_4=април
M(W)_5=май
M(W)_6=юни
M(W)_7=юли
M(W)_8=август
M(W)_9=септември
M(W)_10=октомври
M(W)_11=ноември
M(W)_12=декември

# weekdays
D(a)_1=пн
D(a)_2=вт
D(a)_3=ср
D(a)_4=чт
D(a)_5=пт
D(a)_6=сб
D(a)_7=нд

D(n)_1=п
D(n)_2=в
D(n)_3=с
D(n)_4=ч
D(n)_5=п
D(n)_6=с
D(n)_7=н

D(s)_1=пн
D(s)_2=вт
D(s)_3=ср
D(s)_4=чт
D(s)_5=пт
D(s)_6=сб
D(s)_7=нд

D(w)_1=понеделник
D(w)_2=вторник
D(w)_3=сряда
D(w)_4=четвъртък
D(w)_5=петък
D(w)_6=събота
D(w)_7=неделя

D(A)_1=пн
D(A)_2=вт
D(A)_3=ср
D(A)_4=чт
D(A)_5=пт
D(A)_6=сб
D(A)_7=нд

D(N)_1=п
D(N)_2=в
D(N)_3=с
D(N)_4=ч
D(N)_5=п
D(N)_6=с
D(N)_7=н

D(S)_1=пн
D(S)_2=вт
D(S)_3=ср
D(S)_4=чт
D(S)_5=пт
D(S)_6=сб
D(S)_7=нд

D(W)_1=понеделник
D(W)_2=вторник
D(W)_3=сряда
D(W)_4=четвъртък
D(W)_5=петък
D(W)_6=събота
D(W)_7=неделя

# quarters
Q(a)_1=1. трим.
Q(a)_2=2. трим.
Q(a)_3=3. трим.
Q(a)_4=4. трим.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. тримесечие
Q(w)_2=2. тримесечие
Q(w)_3=3. тримесечие
Q(w)_4=4. тримесечие

Q(A)_1=1. трим.
Q(A)_2=2. трим.
Q(A)_3=3. трим.
Q(A)_4=4. трим.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1. тримесечие
Q(W)_2=2. тримесечие
Q(W)_3=3. тримесечие
Q(W)_4=4. тримесечие

# day-period-rules
T0400=morning1
T1100=morning2
T1400=afternoon1
T1800=evening1
T2200=night1

# day-period-translations
P(a)_midnight=полунощ
P(a)_am=am
P(a)_pm=pm
P(a)_morning1=сутринта
P(a)_morning2=на обяд
P(a)_afternoon1=следобед
P(a)_evening1=вечерта
P(a)_night1=през нощта

P(n)_midnight=полунощ
P(n)_am=am
P(n)_pm=pm
P(n)_morning1=сутринта
P(n)_morning2=на обяд
P(n)_afternoon1=следобед
P(n)_evening1=вечерта
P(n)_night1=през нощта

P(w)_midnight=полунощ
P(w)_am=пр.об.
P(w)_pm=сл.об.
P(w)_morning1=сутринта
P(w)_morning2=на обяд
P(w)_afternoon1=следобед
P(w)_evening1=вечерта
P(w)_night1=през нощта

P(A)_midnight=полунощ
P(A)_am=am
P(A)_pm=pm
P(A)_morning1=сутринта
P(A)_morning2=на обяд
P(A)_afternoon1=следобед
P(A)_evening1=вечерта
P(A)_night1=през нощта

P(N)_midnight=полунощ
P(N)_am=am
P(N)_pm=pm
P(N)_morning1=сутринта
P(N)_morning2=на обяд
P(N)_afternoon1=следобед
P(N)_evening1=вечерта
P(N)_night1=през нощта

P(W)_midnight=полунощ
P(W)_am=am
P(W)_pm=pm
P(W)_morning1=сутринта
P(W)_morning2=на обяд
P(W)_afternoon1=следобед
P(W)_evening1=вечерта
P(W)_night1=през нощта

# eras
E(w)_0=преди Христа
E(w|alt)_0=преди новата ера
E(w)_1=след Христа
E(w|alt)_1=след новата ера

E(a)_0=пр.Хр.
E(a|alt)_0=пр.н.е.
E(a)_1=сл.Хр.
E(a|alt)_1=сл.н.е.

# format patterns
F(f)_d=EEEE, d MMMM y 'г'.
F(l)_d=d MMMM y 'г'.
F(m)_d=d.MM.y 'г'.
F(s)_d=d.MM.yy 'г'.

F(alt)=HH:mm:ss

F(f)_t=H:mm:ss 'ч'. zzzz
F(l)_t=H:mm:ss 'ч'. z
F(m)_t=H:mm:ss 'ч'.
F(s)_t=H:mm 'ч'.

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h 'ч'. B
F_Bhm=h:mm 'ч'. B
F_Bhms=h:mm:ss 'ч'. B
F_h=h 'ч'. a
F_H=H 'ч'.
F_hm=h:mm 'ч'. a
F_Hm=H:mm 'ч'.
F_hms=h:mm:ss 'ч'. a
F_Hms=H:mm:ss 'ч'.

F_Md=d.MM
F_MMMd=d.MM
F_MMMMd=d MMMM
F_y=y 'г'.
F_yM=MM.y 'г'.
F_yMMM=MM.y 'г'.
F_yMMMM=MMMM y 'г'.
F_yQQQ=QQQ y 'г'.
F_yQQQQ=QQQQ y 'г'.
F_yw='седмица' w 'от' Y 'г'.

I={0} – {1}

# labels of elements
L_era=ера
L_year=година
L_quarter=тримесечие
L_month=месец
L_week=седмица
L_day=ден
L_weekday=ден от седмицата
L_dayperiod=пр.об./сл.об.
L_hour=час
L_minute=минута
L_second=секунда
L_zone=часова зона
