{"cells": [{"cell_type": "code", "execution_count": null, "id": "a58b8f19", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["npm i\n", "npm run server\n", "pm2 start index.js -i 4 max --name adtip-server"]}, {"cell_type": "code", "execution_count": null, "id": "83d125a7", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["pm2 start ecosystem.config.js --env staging"]}, {"cell_type": "code", "execution_count": null, "id": "ac87c410", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["pm2 start ecosystem.config.js --env staging"]}, {"cell_type": "markdown", "id": "297087d7", "metadata": {}, "source": ["npm i\n", "npm run server\n"]}, {"cell_type": "markdown", "id": "49e1f6e1", "metadata": {}, "source": ["# for testing "]}, {"cell_type": "code", "execution_count": null, "id": "3a95a643", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["index.test.js\n", "ecosystem.config.test.js\n", ".env.test"]}, {"cell_type": "code", "execution_count": null, "id": "38c3e437", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["NODE_ENV=test pm2 start ecosystem.config.test.js"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}