const express = require('express');
const router = express.Router();
const {
  generateDeepLink,
  validateDeepLink,
  getShareMetadata,
  handleUniversalLink
} = require('../controllers/DeepLinkController');

// Middleware to add queryRunner to request
const { queryRunner } = require('../dbConfig/queryRunner');

const addQueryRunner = (req, res, next) => {
  req.queryRunner = queryRunner;
  next();
};

// Deep link generation and validation routes
router.post('/generate', generateDeepLink);
router.post('/validate', validateDeepLink);
router.get('/metadata/:type/:id', addQueryRunner, getShareMetadata);

// Universal link handling routes
router.get('/post/:id', addQueryRunner, handleUniversalLink);
router.get('/user/:id', addQueryRunner, handleUniversalLink);
router.get('/short/:id', addQueryRunner, handleUniversalLink);
router.get('/video/:id', addQueryRunner, handleUniversalLink);
router.get('/shop/product/:id', addQueryRunner, handleUniversalLink);
router.get('/call/:id', addQueryRunner, handleUniversalLink);
router.get('/chat/:id', addQueryRunner, handleUniversalLink);
router.get('/ludo/:id', addQueryRunner, handleUniversalLink);

// App download page
router.get('/download', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Download Adtip</title>
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          text-align: center;
          padding: 50px 20px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          min-height: 100vh;
          margin: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
        }
        .logo {
          width: 120px;
          height: 120px;
          margin: 0 auto 30px;
          background: white;
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 48px;
          font-weight: bold;
          color: #667eea;
        }
        h1 {
          font-size: 2.5em;
          margin-bottom: 20px;
        }
        p {
          font-size: 1.2em;
          margin-bottom: 40px;
          opacity: 0.9;
        }
        .download-buttons {
          display: flex;
          gap: 20px;
          justify-content: center;
          flex-wrap: wrap;
        }
        .download-btn {
          display: inline-block;
          padding: 15px 30px;
          background: rgba(255, 255, 255, 0.2);
          border: 2px solid white;
          border-radius: 10px;
          color: white;
          text-decoration: none;
          font-weight: bold;
          transition: all 0.3s ease;
        }
        .download-btn:hover {
          background: white;
          color: #667eea;
        }
        .features {
          margin-top: 60px;
          text-align: left;
        }
        .feature {
          margin-bottom: 20px;
          padding: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 10px;
        }
        .feature h3 {
          margin: 0 0 10px 0;
          font-size: 1.3em;
        }
        .feature p {
          margin: 0;
          opacity: 0.8;
          font-size: 1em;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="logo">A</div>
        <h1>Download Adtip</h1>
        <p>The ultimate social platform for earning and entertainment!</p>
        
        <div class="download-buttons">
          <a href="https://apps.apple.com/app/adtip/id123456789" class="download-btn">
            📱 Download for iOS
          </a>
          <a href="https://play.google.com/store/apps/details?id=com.adtip.app.adtip_app&hl=en_IN" class="download-btn">
            🤖 Download for Android
          </a>
        </div>

        <div class="features">
          <div class="feature">
            <h3>🎥 Create & Share</h3>
            <p>Share your moments with short videos, posts, and live streams</p>
          </div>
          <div class="feature">
            <h3>💰 Earn While You Play</h3>
            <p>Play games, watch videos, and earn real money</p>
          </div>
          <div class="feature">
            <h3>📞 Video Calls</h3>
            <p>Connect with friends through high-quality video calls</p>
          </div>
          <div class="feature">
            <h3>🛍️ Shop & Sell</h3>
            <p>Discover amazing products and sell your own items</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `);
});

module.exports = router;
