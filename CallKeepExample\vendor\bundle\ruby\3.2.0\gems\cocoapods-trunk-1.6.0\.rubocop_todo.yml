# This configuration was generated by `rubocop --auto-gen-config`
# on 2014-08-20 17:00:42 +0200 using RuboCop version 0.25.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 1
Metrics/CyclomaticComplexity:
  Max: 8

# Offense count: 9
# Configuration parameters: AllowURI.
Metrics/LineLength:
  Max: 105

# Offense count: 7
# Configuration parameters: CountComments.
Metrics/MethodLength:
  Max: 42

# Offense count: 1
Metrics/PerceivedComplexity:
  Max: 9

# Offense count: 1
Style/ClassVars:
  Enabled: false

# Offense count: 1
# Configuration parameters: Keywords.
Style/CommentAnnotation:
  Enabled: false
