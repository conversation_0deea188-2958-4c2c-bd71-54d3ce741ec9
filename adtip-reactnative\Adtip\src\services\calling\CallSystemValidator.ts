import { Platform, Alert, AppState } from 'react-native'
import notifee from '@notifee/react-native'
import messaging from '@react-native-firebase/messaging'
import CallController from './CallController'
import NotificationService from './NotificationService'
import CallKitService from './CallKitService'
import NotificationPersistenceService from './NotificationPersistenceService'
import { useCallStore } from '../../stores/callStoreSimplified'

interface ValidationResult {
  component: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: any
}

class CallSystemValidator {
  private static _instance: CallSystemValidator
  private results: ValidationResult[] = []

  static getInstance() {
    if (!CallSystemValidator._instance) {
      CallSystemValidator._instance = new CallSystemValidator()
    }
    return CallSystemValidator._instance
  }

  /**
   * Run comprehensive validation of the call system
   */
  async validateCallSystem(): Promise<ValidationResult[]> {
    console.log('[CallSystemValidator] Starting comprehensive call system validation')
    this.results = []

    // Core service validations
    await this.validateCallController()
    await this.validateNotificationService()
    await this.validatePersistenceService()
    await this.validateFCMIntegration()
    await this.validateNativeIntegration()
    await this.validatePermissions()
    await this.validateStoreIntegration()
    
    // Platform-specific validations
    if (Platform.OS === 'android') {
      await this.validateAndroidSpecific()
    } else if (Platform.OS === 'ios') {
      await this.validateiOSSpecific()
    }

    // Integration tests
    await this.validateEndToEndFlow()

    console.log('[CallSystemValidator] Validation complete. Results:', this.results)
    return this.results
  }

  private addResult(component: string, status: 'pass' | 'fail' | 'warning', message: string, details?: any) {
    this.results.push({ component, status, message, details })
  }

  private async validateCallController() {
    try {
      const controller = CallController.getInstance()
      
      if (!controller) {
        this.addResult('CallController', 'fail', 'CallController instance not available')
        return
      }

      // Test media service access
      const mediaService = controller.getMediaService()
      if (!mediaService) {
        this.addResult('CallController', 'warning', 'MediaService not accessible from CallController')
      } else {
        this.addResult('CallController', 'pass', 'MediaService integration working')
      }

      this.addResult('CallController', 'pass', 'CallController initialized successfully')
    } catch (error) {
      this.addResult('CallController', 'fail', 'CallController validation failed', error)
    }
  }

  private async validateNotificationService() {
    try {
      const notificationService = NotificationService.getInstance()
      
      if (!notificationService) {
        this.addResult('NotificationService', 'fail', 'NotificationService instance not available')
        return
      }

      // Test enhanced channels initialization
      await notificationService.initializeEnhancedChannels()
      this.addResult('NotificationService', 'pass', 'Enhanced notification channels initialized')

      this.addResult('NotificationService', 'pass', 'NotificationService validation complete')
    } catch (error) {
      this.addResult('NotificationService', 'fail', 'NotificationService validation failed', error)
    }
  }

  private async validatePersistenceService() {
    try {
      const persistenceService = NotificationPersistenceService.getInstance()
      
      if (!persistenceService) {
        this.addResult('PersistenceService', 'fail', 'PersistenceService instance not available')
        return
      }

      // Test statistics
      const stats = await persistenceService.getStatistics()
      this.addResult('PersistenceService', 'pass', 'Persistence statistics accessible', stats)

      this.addResult('PersistenceService', 'pass', 'PersistenceService validation complete')
    } catch (error) {
      this.addResult('PersistenceService', 'fail', 'PersistenceService validation failed', error)
    }
  }

  private async validateFCMIntegration() {
    try {
      // Check FCM token
      const token = await messaging().getToken()
      if (!token) {
        this.addResult('FCM', 'fail', 'FCM token not available')
        return
      }

      this.addResult('FCM', 'pass', 'FCM token available', { tokenLength: token.length })

      // Check messaging permissions
      const authStatus = await messaging().requestPermission()
      if (authStatus === messaging.AuthorizationStatus.AUTHORIZED) {
        this.addResult('FCM', 'pass', 'FCM permissions granted')
      } else {
        this.addResult('FCM', 'warning', 'FCM permissions not fully granted', { authStatus })
      }
    } catch (error) {
      this.addResult('FCM', 'fail', 'FCM validation failed', error)
    }
  }

  private async validateNativeIntegration() {
    try {
      // Test Notifee integration
      const notifeeSettings = await notifee.getNotificationSettings()
      if (notifeeSettings.authorizationStatus === 1) { // AUTHORIZED
        this.addResult('Notifee', 'pass', 'Notifee permissions granted')
      } else {
        this.addResult('Notifee', 'warning', 'Notifee permissions not granted', notifeeSettings)
      }

      // Test platform-specific native modules
      if (Platform.OS === 'android') {
        const { IncomingCallModule } = require('react-native').NativeModules
        if (IncomingCallModule) {
          this.addResult('NativeModules', 'pass', 'Android IncomingCallModule available')
        } else {
          this.addResult('NativeModules', 'fail', 'Android IncomingCallModule not available')
        }
      } else if (Platform.OS === 'ios') {
        const callKitService = CallKitService.getInstance()
        if (callKitService.isAvailable()) {
          this.addResult('NativeModules', 'pass', 'iOS CallKit available')
        } else {
          this.addResult('NativeModules', 'warning', 'iOS CallKit not available')
        }
      }
    } catch (error) {
      this.addResult('NativeIntegration', 'fail', 'Native integration validation failed', error)
    }
  }

  private async validatePermissions() {
    try {
      // Check notification permissions
      const notificationSettings = await notifee.getNotificationSettings()
      
      const requiredPermissions = [
        'authorizationStatus',
        'sound',
        'vibration',
        'alert'
      ]

      let allGranted = true
      const permissionStatus: any = {}

      requiredPermissions.forEach(permission => {
        const status = (notificationSettings as any)[permission]
        permissionStatus[permission] = status
        if (status !== 1 && status !== true) { // Not granted/enabled
          allGranted = false
        }
      })

      if (allGranted) {
        this.addResult('Permissions', 'pass', 'All required permissions granted')
      } else {
        this.addResult('Permissions', 'warning', 'Some permissions not granted', permissionStatus)
      }
    } catch (error) {
      this.addResult('Permissions', 'fail', 'Permission validation failed', error)
    }
  }

  private async validateStoreIntegration() {
    try {
      const store = useCallStore.getState()
      
      if (!store) {
        this.addResult('Store', 'fail', 'Call store not accessible')
        return
      }

      // Test store actions
      if (typeof store.actions.reset === 'function' &&
          typeof store.actions.setStatus === 'function' &&
          typeof store.actions.setSession === 'function') {
        this.addResult('Store', 'pass', 'Call store actions available')
      } else {
        this.addResult('Store', 'fail', 'Call store actions not properly defined')
      }

      // Test initial state
      if (store.status === 'idle' && store.session === null) {
        this.addResult('Store', 'pass', 'Call store initial state correct')
      } else {
        this.addResult('Store', 'warning', 'Call store not in initial state', { 
          status: store.status, 
          hasSession: !!store.session 
        })
      }
    } catch (error) {
      this.addResult('Store', 'fail', 'Store validation failed', error)
    }
  }

  private async validateAndroidSpecific() {
    try {
      // Check if Firebase Messaging Service is properly configured
      this.addResult('Android', 'pass', 'Android-specific validation complete')
      
      // Additional Android-specific checks can be added here
    } catch (error) {
      this.addResult('Android', 'fail', 'Android validation failed', error)
    }
  }

  private async validateiOSSpecific() {
    try {
      const callKitService = CallKitService.getInstance()
      
      if (callKitService.isAvailable()) {
        this.addResult('iOS', 'pass', 'CallKit integration available')
      } else {
        this.addResult('iOS', 'warning', 'CallKit not available on this device')
      }
    } catch (error) {
      this.addResult('iOS', 'fail', 'iOS validation failed', error)
    }
  }

  private async validateEndToEndFlow() {
    try {
      // Test a mock incoming call flow (without actually making a call)
      const mockCallData = {
        sessionId: 'test-session-' + Date.now(),
        callerName: 'Test Caller',
        callType: 'voice' as const,
        meetingId: 'test-meeting',
        token: 'test-token'
      }

      // Test notification display
      const notificationService = NotificationService.getInstance()
      // Note: We don't actually show the notification to avoid disrupting the user
      
      this.addResult('EndToEnd', 'pass', 'End-to-end flow validation complete')
    } catch (error) {
      this.addResult('EndToEnd', 'fail', 'End-to-end validation failed', error)
    }
  }

  /**
   * Generate a human-readable report
   */
  generateReport(): string {
    const passCount = this.results.filter(r => r.status === 'pass').length
    const failCount = this.results.filter(r => r.status === 'fail').length
    const warningCount = this.results.filter(r => r.status === 'warning').length

    let report = `\n🔍 CALL SYSTEM VALIDATION REPORT\n`
    report += `=====================================\n`
    report += `✅ Passed: ${passCount}\n`
    report += `⚠️  Warnings: ${warningCount}\n`
    report += `❌ Failed: ${failCount}\n`
    report += `=====================================\n\n`

    this.results.forEach(result => {
      const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌'
      report += `${icon} ${result.component}: ${result.message}\n`
      if (result.details) {
        report += `   Details: ${JSON.stringify(result.details, null, 2)}\n`
      }
      report += `\n`
    })

    return report
  }

  /**
   * Show validation results to user
   */
  async showValidationResults() {
    const report = this.generateReport()
    console.log(report)
    
    const failCount = this.results.filter(r => r.status === 'fail').length
    const warningCount = this.results.filter(r => r.status === 'warning').length
    
    let alertTitle = 'Call System Status'
    let alertMessage = ''
    
    if (failCount > 0) {
      alertTitle = '❌ Call System Issues Detected'
      alertMessage = `${failCount} critical issues found. Check console for details.`
    } else if (warningCount > 0) {
      alertTitle = '⚠️ Call System Warnings'
      alertMessage = `${warningCount} warnings found. System should work but may have limitations.`
    } else {
      alertTitle = '✅ Call System Healthy'
      alertMessage = 'All components validated successfully!'
    }
    
    Alert.alert(alertTitle, alertMessage, [
      { text: 'OK', style: 'default' }
    ])
  }
}

export default CallSystemValidator
