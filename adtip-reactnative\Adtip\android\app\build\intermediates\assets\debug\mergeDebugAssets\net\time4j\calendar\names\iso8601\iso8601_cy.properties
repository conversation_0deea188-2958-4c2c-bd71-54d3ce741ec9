# months
M(a)_1=Ion
M(a)_2=Chwef
M(a)_3=Maw
M(a)_4=Ebrill
M(a)_5=Mai
M(a)_6=Meh
M(a)_7=Gorff
M(a)_8=Awst
M(a)_9=Medi
M(a)_10=Hyd
M(a)_11=Tach
M(a)_12=Rhag

M(n)_1=I
M(n)_2=Ch
M(n)_3=M
M(n)_4=E
M(n)_5=M
M(n)_6=M
M(n)_7=G
M(n)_8=A
M(n)_9=M
M(n)_10=H
M(n)_11=T
M(n)_12=Rh

M(w)_1=Ionawr
M(w)_2=Chwefror
M(w)_3=Mawrth
M(w)_4=Ebrill
M(w)_5=Mai
M(w)_6=Mehefin
M(w)_7=Gorffennaf
M(w)_8=Awst
M(w)_9=Medi
M(w)_10=Hydref
M(w)_11=<PERSON><PERSON><PERSON><PERSON>
M(w)_12=Rhagfyr

M(A)_1=Ion
M(A)_2=Chw
M(A)_3=Maw
M(A)_4=Ebr
M(A)_5=Mai
M(A)_6=Meh
M(A)_7=Gor
M(A)_8=Awst
M(A)_9=Medi
M(A)_10=Hyd
M(A)_11=Tach
M(A)_12=Rhag

M(N)_1=I
M(N)_2=Ch
M(N)_3=M
M(N)_4=E
M(N)_5=M
M(N)_6=M
M(N)_7=G
M(N)_8=A
M(N)_9=M
M(N)_10=H
M(N)_11=T
M(N)_12=Rh

M(W)_1=Ionawr
M(W)_2=Chwefror
M(W)_3=Mawrth
M(W)_4=Ebrill
M(W)_5=Mai
M(W)_6=Mehefin
M(W)_7=Gorffennaf
M(W)_8=Awst
M(W)_9=Medi
M(W)_10=Hydref
M(W)_11=Tachwedd
M(W)_12=Rhagfyr

# weekdays
D(a)_1=Llun
D(a)_2=Maw
D(a)_3=Mer
D(a)_4=Iau
D(a)_5=Gwen
D(a)_6=Sad
D(a)_7=Sul

D(n)_1=Ll
D(n)_2=M
D(n)_3=M
D(n)_4=I
D(n)_5=G
D(n)_6=S
D(n)_7=S

D(s)_1=Ll
D(s)_2=Ma
D(s)_3=Me
D(s)_4=Ia
D(s)_5=Gw
D(s)_6=Sa
D(s)_7=Su

D(w)_1=Dydd Llun
D(w)_2=Dydd Mawrth
D(w)_3=Dydd Mercher
D(w)_4=Dydd Iau
D(w)_5=Dydd Gwener
D(w)_6=Dydd Sadwrn
D(w)_7=Dydd Sul

D(A)_1=Llun
D(A)_2=Maw
D(A)_3=Mer
D(A)_4=Iau
D(A)_5=Gwe
D(A)_6=Sad
D(A)_7=Sul

D(N)_1=Ll
D(N)_2=M
D(N)_3=M
D(N)_4=I
D(N)_5=G
D(N)_6=S
D(N)_7=S

D(S)_1=Ll
D(S)_2=Ma
D(S)_3=Me
D(S)_4=Ia
D(S)_5=Gw
D(S)_6=Sa
D(S)_7=Su

D(W)_1=Dydd Llun
D(W)_2=Dydd Mawrth
D(W)_3=Dydd Mercher
D(W)_4=Dydd Iau
D(W)_5=Dydd Gwener
D(W)_6=Dydd Sadwrn
D(W)_7=Dydd Sul

# quarters
Q(a)_1=Ch1
Q(a)_2=Ch2
Q(a)_3=Ch3
Q(a)_4=Ch4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=chwarter 1af
Q(w)_2=2il chwarter
Q(w)_3=3ydd chwarter
Q(w)_4=4ydd chwarter

Q(A)_1=Ch1
Q(A)_2=Ch2
Q(A)_3=Ch3
Q(A)_4=Ch4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=chwarter 1af
Q(W)_2=2il chwarter
Q(W)_3=3ydd chwarter
Q(W)_4=4ydd chwarter

# day-period-rules
T0000=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=canol nos
P(a)_am=yb
P(a)_noon=canol dydd
P(a)_pm=yh
P(a)_morning1=y bore
P(a)_afternoon1=y prynhawn
P(a)_evening1=yr hwyr

P(n)_midnight=canol nos
P(n)_am=b
P(n)_noon=canol dydd
P(n)_pm=h
P(n)_morning1=yn y bore
P(n)_afternoon1=yn y prynhawn
P(n)_evening1=min nos

P(w)_midnight=canol nos
P(w)_am=yb
P(w)_noon=canol dydd
P(w)_pm=yh
P(w)_morning1=y bore
P(w)_afternoon1=y prynhawn
P(w)_evening1=yr hwyr

P(A)_midnight=canol nos
P(A)_am=yb
P(A)_noon=canol dydd
P(A)_pm=yh
P(A)_morning1=bore
P(A)_afternoon1=prynhawn
P(A)_evening1=yr hwyr

P(N)_midnight=canol nos
P(N)_am=yb
P(N)_noon=canol dydd
P(N)_pm=yh
P(N)_morning1=bore
P(N)_afternoon1=prynhawn
P(N)_evening1=min nos

P(W)_midnight=canol nos
P(W)_am=yb
P(W)_noon=canol dydd
P(W)_pm=yh
P(W)_morning1=y bore
P(W)_afternoon1=y prynhawn
P(W)_evening1=yr hwyr

# eras
E(w)_0=Cyn Crist
E(w|alt)_0=Cyn Cyfnod Cyffredin
E(w)_1=Oed Crist
E(w|alt)_1=Cyfnod Cyffredin

E(a)_0=CC
E(a|alt)_0=CCC
E(a)_1=OC
E(a|alt)_1=CYCY

E(n)_0=C
E(n)_1=O

# format patterns
F(f)_d=EEEE, d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd/MM/yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'am' {0}
F(l)_dt={1} 'am' {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d MMM
F_MMMMd=MMMM d
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQ=Q y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw='wythnos' w 'o' Y

I={0} – {1}

# labels of elements
L_era=oes
L_year=blwyddyn
L_quarter=chwarter
L_month=mis
L_week=wythnos
L_day=diwrnod
L_weekday=diwrnod o’r wythnos
L_dayperiod=AM/PM
L_hour=awr
L_minute=munud
L_second=eiliad
L_zone=cylchfa amser
