const ManualWalletService = require('../services/ManualWalletService');

exports.manualAddFunds = async (req, res) => {
  try {
    const { userId, amount, note } = req.body;
    if (!userId || !amount) {
      return res.status(400).json({ status: false, message: 'userId and amount are required' });
    }
    const result = await ManualWalletService.manualAddFunds(userId, amount, note);
    res.json({ status: true, message: 'Funds credited manually', data: result });
  } catch (err) {
    res.status(500).json({ status: false, message: 'Error crediting funds', error: err.message });
  }
}; 