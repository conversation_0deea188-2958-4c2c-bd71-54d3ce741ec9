/**
 * Chat System Verification Script
 * 
 * This script verifies that the chat system correctly handles sender/recipient identification
 * and that messages are stored with proper user IDs in the database.
 */

const queryRunner = require('./dbConfig/queryRunner');

async function verifyChatSystem() {
  console.log('🔍 Starting Chat System Verification...\n');

  try {
    // Test 1: Check if messages table exists and has correct structure
    console.log('📋 Test 1: Verifying messages table structure...');
    const tableStructure = await queryRunner.queryRunner(`
      DESCRIBE messages
    `);
    
    const requiredFields = ['sender_id', 'recipient_id', 'chat_id', 'content', 'sender_name'];
    const existingFields = tableStructure.map(field => field.Field);
    
    const missingFields = requiredFields.filter(field => !existingFields.includes(field));
    if (missingFields.length > 0) {
      console.log('❌ Missing required fields:', missingFields);
      return false;
    }
    console.log('✅ Messages table structure is correct\n');

    // Test 2: Check for messages with same sender_id and recipient_id (the bug)
    console.log('📋 Test 2: Checking for messages with same sender_id and recipient_id...');
    const duplicateMessages = await queryRunner.queryRunner(`
      SELECT id, chat_id, sender_id, recipient_id, content, created_at
      FROM messages 
      WHERE sender_id = recipient_id 
      LIMIT 10
    `);
    
    if (duplicateMessages.length > 0) {
      console.log('❌ Found messages with same sender_id and recipient_id:');
      duplicateMessages.forEach(msg => {
        console.log(`   Message ID: ${msg.id}, Chat: ${msg.chat_id}, User: ${msg.sender_id}, Content: "${msg.content.substring(0, 30)}..."`);
      });
      console.log(`   Total problematic messages: ${duplicateMessages.length}\n`);
    } else {
      console.log('✅ No messages found with same sender_id and recipient_id\n');
    }

    // Test 3: Verify chat_id format consistency
    console.log('📋 Test 3: Verifying chat_id format consistency...');
    const chatIdFormats = await queryRunner.queryRunner(`
      SELECT DISTINCT chat_id, 
             CASE 
               WHEN chat_id LIKE 'chat_%_%' THEN 'user_based'
               WHEN chat_id LIKE 'conv_%' THEN 'conversation_based'
               ELSE 'unknown'
             END as format_type
      FROM messages 
      ORDER BY created_at DESC 
      LIMIT 20
    `);
    
    console.log('Recent chat_id formats:');
    chatIdFormats.forEach(chat => {
      console.log(`   ${chat.chat_id} (${chat.format_type})`);
    });
    console.log('');

    // Test 4: Check recent messages for proper sender/recipient logic
    console.log('📋 Test 4: Analyzing recent messages for sender/recipient logic...');
    const recentMessages = await queryRunner.queryRunner(`
      SELECT m.id, m.chat_id, m.sender_id, m.recipient_id, m.sender_name,
             s.name as actual_sender_name, r.name as actual_recipient_name,
             m.content, m.created_at
      FROM messages m
      LEFT JOIN users s ON m.sender_id = s.id
      LEFT JOIN users r ON m.recipient_id = r.id
      ORDER BY m.created_at DESC 
      LIMIT 10
    `);
    
    console.log('Recent messages analysis:');
    recentMessages.forEach(msg => {
      const senderMatch = msg.sender_name === msg.actual_sender_name;
      const hasValidRecipient = msg.recipient_id && msg.recipient_id !== msg.sender_id;
      
      console.log(`   Message ${msg.id}:`);
      console.log(`     Chat: ${msg.chat_id}`);
      console.log(`     Sender: ${msg.sender_id} (${msg.sender_name}) ${senderMatch ? '✅' : '❌'}`);
      console.log(`     Recipient: ${msg.recipient_id} (${msg.actual_recipient_name || 'N/A'}) ${hasValidRecipient ? '✅' : '❌'}`);
      console.log(`     Content: "${msg.content.substring(0, 30)}..."`);
      console.log('');
    });

    // Test 5: Check inbox functionality
    console.log('📋 Test 5: Testing inbox query functionality...');
    const inboxSample = await queryRunner.queryRunner(`
      SELECT m.id, m.chat_id, m.sender_id, m.content, m.message_type,
             u.name as sender_name, u.profile_image as sender_avatar
      FROM messages m
      INNER JOIN users u ON m.sender_id = u.id
      WHERE m.recipient_id = (SELECT id FROM users LIMIT 1)
        AND m.is_deleted = 0
      ORDER BY m.created_at DESC
      LIMIT 5
    `);
    
    if (inboxSample.length > 0) {
      console.log('✅ Inbox query working correctly');
      console.log(`   Found ${inboxSample.length} messages for sample user`);
    } else {
      console.log('⚠️  No messages found for sample user (this might be normal)');
    }
    console.log('');

    // Summary
    console.log('📊 VERIFICATION SUMMARY:');
    console.log('========================');
    
    if (duplicateMessages.length > 0) {
      console.log('❌ ISSUE FOUND: Messages with same sender_id and recipient_id detected');
      console.log('   This indicates the chat system bug is still present');
      console.log('   Recommendation: Check which chat service is being used and ensure proper recipient_id handling');
    } else {
      console.log('✅ No sender/recipient ID conflicts found');
    }
    
    const userBasedChats = chatIdFormats.filter(c => c.format_type === 'user_based').length;
    const conversationBasedChats = chatIdFormats.filter(c => c.format_type === 'conversation_based').length;
    
    console.log(`📈 Chat format distribution: ${userBasedChats} user-based, ${conversationBasedChats} conversation-based`);
    
    if (userBasedChats > conversationBasedChats) {
      console.log('✅ System is primarily using user-based chat (recommended)');
    } else {
      console.log('⚠️  System is using mixed or primarily conversation-based chat');
    }

    return duplicateMessages.length === 0;

  } catch (error) {
    console.error('❌ Verification failed:', error);
    return false;
  }
}

// Run verification if called directly
if (require.main === module) {
  verifyChatSystem()
    .then(success => {
      console.log('\n🏁 Verification completed:', success ? 'PASSED' : 'FAILED');
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Verification crashed:', error);
      process.exit(1);
    });
}

module.exports = { verifyChatSystem };
