<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upgrade Content Premium - Razorpay Payment</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background-color: #f4f4f4;
        }
        .plan-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
        }
        .plan-card {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            width: 200px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        .plan-card:hover {
            transform: scale(1.05);
        }
        .plan-card h3 {
            margin: 0;
            color: #333;
        }
        .plan-card p {
            margin: 10px 0;
            font-size: 1.2em;
            color: #F37254;
        }
        .plan-card button {
            background: #F37254;
            color: #fff;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
        }
        .plan-card button:hover {
            background: #d65a3f;
        }
    </style>
</head>
<body>
    <h1>Choose Your Content Premium Plan</h1>
    <div class="plan-container">
        <div class="plan-card" data-plan-id="1" data-amount="2500">
            <h3>1 Month Plan</h3>
            <p>₹2500</p>
            <button class="pay-btn">Select Plan</button>
        </div>
        <div class="plan-card" data-plan-id="2" data-amount="6000">
            <h3>3 Month Plan</h3>
            <p>₹6000</p>
            <button class="pay-btn">Select Plan</button>
        </div>
        <div class="plan-card" data-plan-id="3" data-amount="12000">
            <h3>6 Month Plan</h3>
            <p>₹12000</p>
            <button class="pay-btn">Select Plan</button>
        </div>
        <div class="plan-card" data-plan-id="4" data-amount="22000">
            <h3>12 Month Plan</h3>
            <p>₹22000</p>
            <button class="pay-btn">Select Plan</button>
        </div>
    </div>

    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:7082/api';
        const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.CP2hoyHw7dOjB8A6uIifbdfNsztf0Pt1BSw8pEdM92Q'; // Replace with your auth token
        const USER_ID = 50816;
        const RAZORPAY_KEY = 'rzp_test_ojNkCSTYuUL3w9';

        document.querySelectorAll('.pay-btn').forEach(button => {
            button.onclick = async function () {
                const planCard = button.closest('.plan-card');
                const plan_id = planCard.dataset.planId;
                const amount = parseInt(planCard.dataset.amount);

                try {
                    // Step 1: Create Razorpay order
                    const orderResponse = await fetch(`${API_BASE_URL}/razorpay-order`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': AUTH_TOKEN
                        },
                        body: JSON.stringify({
                            amount: amount,
                            currency: 'INR',
                            user_id: USER_ID
                        })
                    });
                    const orderData = await orderResponse.json();

                    if (!orderData.status) {
                        alert('Order creation failed: ' + orderData.message);
                        return;
                    }

                    // Step 2: Open Razorpay checkout
                    const options = {
                        key: RAZORPAY_KEY,
                        amount: orderData.data.amount,
                        currency: 'INR',
                        name: 'Your Business Name',
                        description: `Content Premium - ${planCard.querySelector('h3').textContent}`,
                        order_id: orderData.data.id,
                        handler: async function (response) {
                            console.log('Payment Successful:', response);

                            // Step 3: Verify payment with /razorpay-verification
                            try {
                                const verificationResponse = await fetch(`${API_BASE_URL}/razorpay-verification`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': AUTH_TOKEN
                                    },
                                    body: JSON.stringify({
                                        transaction_for: 'content_creator_premium',
                                        order_id: response.razorpay_order_id,
                                        razorpay_payment_id: response.razorpay_payment_id,
                                        razorpay_signature: response.razorpay_signature,
                                        amount: orderData.data.amount / 100,
                                        currency: 'INR',
                                        user_id: USER_ID,
                                        payment_status: 'success',
                                        plan_id: parseInt(plan_id)
                                    })
                                });
                                const verificationData = await verificationResponse.json();

                                if (!verificationData.status) {
                                    alert('Payment verification failed: ' + (verificationData.message || 'Unknown error'));
                                    return;
                                }

                                // Step 4: Call /upgrade-content-premium to activate plan
                                const upgradePremiumResponse = await fetch(`${API_BASE_URL}/upgrade-content-premium`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': AUTH_TOKEN
                                    },
                                    body: JSON.stringify({
                                        payment_status: 'success',
                                        user_id: USER_ID,
                                        plan_id: parseInt(plan_id),
                                        order_id: response.razorpay_order_id,
                                        payment_id: response.razorpay_payment_id,
                                        coupon_code: null,
                                        isCron: false
                                    })
                                });
                                const upgradeData = await upgradePremiumResponse.json();

                                if (upgradeData.status) {
                                    alert('Content Premium plan activated successfully!');
                                } else {
                                    alert('Content Premium activation failed: ' + (upgradeData.message || 'Unknown error'));
                                }
                            } catch (err) {
                                console.error('Error:', err);
                                alert('Error processing payment: ' + err.message);
                            }
                        },
                        prefill: {
                            name: 'Test User',
                            email: '<EMAIL>',
                            contact: '9999999999'
                        },
                        theme: {
                            color: '#F37254'
                        }
                    };

                    const rzp = new Razorpay(options);
                    rzp.open();
                } catch (err) {
                    console.error('Error:', err);
                    alert('Error occurred: ' + err.message);
                }
            };
        });
    </script>
</body>
</html>