let AdService = require("../services/AdService");

module.exports = {
  getAdForShortVideo: (req, res, next) => {
    AdService.getAdForShortVideo(req.params.userId)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAdModels: (req, res, next) => {
    AdService.getAdModels()
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.imagePath = `${req.headers.host}/api/file/${data.modelImage}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAdds: (req, res, next) => {
    if (!req.params.userId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getAdvModel(req.params.userId)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  //getAdDetailsForQr
  getAdDetailsForQr: (req, res, next) => {
    AdService.getAdDetailsForQr(req.params.adId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getTargetProfession: (req, res, next) => {
    AdService.getTargetProfession()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getTargetAreas: (req, res, next) => {
    AdService.getTargetAreas()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getButtons: (req, res, next) => {
    AdService.getButtons()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getCompanyButton: (req, res, next) => {
    AdService.getCompanyButton()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAnimations: (req, res, next) => {
    AdService.getAnimations()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveFirstAdModel: (req, res, next) => {
    return AdService.saveFirstAdModel(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveSecondPageAdmodel: (req, res, next) => {
    return AdService.saveSecondPageAdmodel(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveCelebrationAddModels: (req, res, next) => {
    return AdService.saveCelebrationAddModels(req.body, req.files)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveThirdPageAdmodel: (req, res, next) => {
    return AdService.saveThirdPageAdmodel(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  requestdemo: (req, res, next) => {
    return AdService.requesdemoservice(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  validateCoupon: (req, res, next) => {
    return AdService.validateCoupon(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getCoupon: (req, res, next) => {
    return AdService.getCoupon(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getrequestdemoads: (req, res, next) => {
    return AdService.getrequestdemoads(req)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getFilterAds: (req, res, next) => {
    if (
      !req.body.gender &&
      !req.body.age &&
      !req.body.maritalStatus &&
      !req.body.modelType &&
      !req.body.targetProfession
    )
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getFilterAds(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getMasterFilterAds: (req, res, next) => {
    if (
      !req.body.gender &&
      !req.body.age &&
      !req.body.maritalStatus &&
      !req.body.modelType &&
      !req.body.userId
    )
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getMasterFilterAds(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getMasterAdsPagination: (req, res, next) => {
    if (!req.body.page)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getMasterAdsPagination(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getCelebrationAds: (req, res, next) => {
    if (
      !req.body.gender &&
      !req.body.age &&
      !req.body.maritalStatus &&
      !req.body.targetLocation &&
      !req.body.userId
    )
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getCelebrationAds(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getBuisnessAds: (req, res, next) => {
    if (
      !req.body.gender &&
      !req.body.age &&
      !req.body.maritalStatus &&
      !req.body.targetLocation &&
      !req.body.userId
    )
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getBuisnessAds(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAdHubsAds: (req, res, next) => {
    if (
      !req.body.gender &&
      !req.body.age &&
      !req.body.maritalStatus &&
      !req.body.userId
    )
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getAdHubsAds(req.body)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.companyProfileFilename}`;
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAdViewsAndLike: (req, res, next) => {
    // if(!req.params.userId && !req.params.id) return  res.status(400).send({ status: 400,message: "Invalid request.",data: []});
    AdService.getAdViewsAndLike(req.params)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveAdViewsAndLikes: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return AdService.saveAdViewsAndLikes(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveAdLikeAmount: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return AdService.saveAdLikeAmount(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveAdViewAmount: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return AdService.saveAdViewAmount(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addAmountToWallet: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return AdService.addAmountToWallet(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveFallowCompany: (req, res, next) => {
    if (!req.body.companyId && !req.body.userId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return AdService.saveFallowCompany(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getFallowCompany: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getFallowCompany(req.params.userid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.profileFilename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getMyLikesAds: (req, res, next) => {
    if (!req.params.userId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getMyLikesAds(req.params.userId, req.params.page)
      .then((result) => {
        if (result && result.status === 200) {
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveBussinessAdModel: (req, res, next) => {
    return AdService.saveBussinessAdModel(req.body, req.files)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveCelebrationAdView: (req, res, next) => {
    if (!req.body.adId && !req.body.userId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return AdService.saveCelebrationAdView(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getLossViewAmount: (req, res, next) => {
    if (!req.params.adId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return AdService.getLossViewAmount(req.params.adId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getLastAdDetails: (req, res, next) => {
    if (!req.params.userid && !req.params.companyId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getLastAdDetails(req.params.userid, req.params.companyId)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.companyProfileFilename}`;
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
            data.addUrl = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAdDetailsforVideo: (req, res, next) => {
    if (!req.params.adid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getAdDetailsforVideo(req.params.adid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.companyProfileFilename}`;
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
            data.addUrl = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getAdDetails: (req, res, next) => {
    if (!req.params.userid && !req.params.adid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getAdDetails(req.params.adid, req.params.userid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.companyProfileFilename}`;
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
            data.addUrl = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getOrderTracking: (req, res, next) => {
    if (!req.params.adid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getOrderTracking(req.params.adid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.companyProfileFilename}`;
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
            data.addUrl = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getUserLikeAds: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getUserLikeAds(req.params.userid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.companyProfileFilename}`;
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
            data.addUrl = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getLikeAdsbyCompanyUserId: (req, res, next) => {
    if (!req.params.companyuserid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdService.getLikeAdsbyCompanyUserId(req.params.companyuserid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.companyProfileFilename}`;
            data.imagePath = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
            data.addUrl = `${req.headers.host}/api/video/${data.ad_upload_filename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getGraphData: (req, res, next) => {
    if (!req.body)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    AdService.getGraphData(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAdPassBook: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    AdService.getAdPassBook(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveBlockad: (req, res, next) => {
    if (!req.body)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    AdService.saveBlockad(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getBlockAdbyCompany: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    AdService.getBlockAdbyCompany(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getBlockAdCompanyByUser: (req, res, next) => {
    if (!req.params.userid)
      return res.status(400).send({
        status: 400,
        message: "Invalid request.",
        data: req.params.userid,
      });
    AdService.getBlockAdCompanyByUser(req.params.userid)
      .then((result) => {
        result.data.forEach((data) => {
          if (data.coverFilename != null)
            data.coverImage = `${req.headers.host}/api/photo/${data.coverFilename}`;
          if (data.profileFilename != null)
            data.profileImage = `${req.headers.host}/api/photo/${data.profileFilename}`;
        });
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveAdPauseCountinueStatus: (req, res, next) => {
    if (!req.body)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    AdService.saveAdPauseCountinueStatus(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAdView: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return AdService.getAdview(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  // saveFourPageAdmodel: (req, res, next) => {
  //     return AdService.saveFourPageAdmodel(req.body)
  //         .then(result => {
  //             res.status(result.status || 200).send(result);
  //         })
  //         .catch(err => {
  //             res.status(err.status || 500).send({
  //                 status: err.status || 500,
  //                 message: err.message ? err.message : "Internal server error.",
  //                 data: []
  //             });
  //         });
  // },
  // saveFivePageAdmodel: (req, res, next) => {
  //     return AdService.saveFivePageAdmodel(req.body)
  //         .then(result => {
  //             res.status(result.status || 200).send(result);
  //         })
  //         .catch(err => {
  //             res.status(err.status || 500).send({
  //                 status: err.status || 500,
  //                 message: err.message ? err.message : "Internal server error.",
  //                 data: []
  //             });
  //         });
  // },
  // getAllSaveAds: (req, res, next) => {
  //     return AdService.getAllSaveAds(req.body)
  //         .then(result => {
  //             res.status(result.status || 200).send(result);
  //         })
  //         .catch(err => {
  //             res.status(err.status || 500).send({
  //                 status: err.status || 500,
  //                 message: err.message ? err.message : "Internal server error.",
  //                 data: []
  //             });
  //         });
  // },
};
