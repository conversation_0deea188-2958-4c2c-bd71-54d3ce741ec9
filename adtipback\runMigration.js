// adtipback/runMigration.js
// <PERSON>ript to run database migration for Cloudflare Stream fields

const dbQuery = require('./dbConfig/queryRunner');
const fs = require('fs');

async function runMigration() {
  try {
    console.log('📊 Running database migration for Cloudflare Stream fields...');
    
    // Read the migration file
    const migrationSQL = fs.readFileSync('./migrations/add_stream_fields.sql', 'utf8');
    
    // Split by semicolon and filter out empty statements
    const statements = migrationSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement && !statement.startsWith('--')) {
        console.log(`Executing statement ${i + 1}/${statements.length}...`);
        try {
          const result = await dbQuery.queryRunner(statement);
          console.log('✅ Success:', statement.substring(0, 50) + '...');
        } catch (error) {
          if (error.message.includes('Duplicate column name')) {
            console.log('⚠️  Column already exists:', statement.substring(0, 50) + '...');
          } else {
            console.error('❌ Error:', error.message);
          }
        }
      }
    }
    
    console.log('🎉 Migration completed!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
