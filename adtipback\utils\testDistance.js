let util = require("./utils");
const currentLocation = { latitude: 20.593684, longitude: 78.96288 }; // New York City
const targetLocations = [
  { latitude: 17.6868, longitude: 83.2185 }, // Los Angeles
  { latitude: 41.8781, longitude: -87.6298 }, // Chicago
  { latitude: 29.7604, longitude: -95.3698 }, // Houston
];
const radius = 200; // Radius in km

if (util.isWithinRadius(currentLocation, targetLocations)) {
  console.log(
    "Current location is within the radius of at least one target location."
  );
} else {
  console.log(
    "Current location is not within the radius of any target locations."
  );
}
