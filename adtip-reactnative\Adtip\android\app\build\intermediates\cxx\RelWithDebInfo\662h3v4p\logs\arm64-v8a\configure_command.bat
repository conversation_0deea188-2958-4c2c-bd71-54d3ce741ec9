@echo off
"F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HF:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=arm64-v8a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a" ^
  "-DANDROID_NDK=F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a" ^
  "-DCMAKE_BUILD_TYPE=RelWithDebInfo" ^
  "-DCMAKE_FIND_ROOT_PATH=F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\prefab\\arm64-v8a\\prefab" ^
  "-BF:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\arm64-v8a" ^
  -GNinja ^
  "-DPROJECT_BUILD_DIR=F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build" ^
  "-DPROJECT_ROOT_DIR=F:\\A1\\adtip-reactnative\\Adtip\\android" ^
  "-DREACT_ANDROID_DIR=F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid" ^
  "-DANDROID_STL=c++_shared" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
