let config = require("../config/appconfig.json");
let appenvconfig = require("../config/appenvconfig.js");
let request = require("request");
const QRCode = require("qrcode");
const path = require("path");
const geolib = require("geolib");

module.exports = {
  isWithinRadius: (currentLocation, targetLocations) => {
    return targetLocations.some((targetLocation) => {
      const distance = geolib.getDistance(
        {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
        },
        {
          latitude: targetLocation.latitude,
          longitude: targetLocation.longitude,
        }
      );
      return distance <= 200 * 1000; // Convert km to meters
    });
  },
  sendOtpSms: (mobileNumber, otp) =>
    new Promise((resolve, reject) => {
      if (process.env.MODE === "development") {
        return resolve({
          Status: "Success",
          Details: "m11adacat8uk4zpua5-117878239cc54cb",
        });
      }

      let options = {
        method: "POST",
        url: `https://2factor.in/API/V1/${config.sms.account_id}/SMS/${mobileNumber}/${otp}/sms-otp1`,
      };
      request(options, (error, response) => {
        if (error) reject(error);
        responceData = { data: "" };
        responceData.data = resolve(JSON.parse(response.body));
        return resolve(responceData);
      });
    }),
  checkId: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.body] });
    next();
  },

  generateQRCode: (QRJsonData, qrcodename) =>
    new Promise((resolve, reject) => {
      QRCode.toFile(
        path.join(appenvconfig.application_qr_code_path, qrcodename),
        QRJsonData,
        (err) => {
          if (err) throw err;
          console.log(QRJsonData);
        }
      );
    }),
  sendFcmNotification: (userData) =>
    new Promise((resolve, reject) => {
      let message = {
        to: userData.registrationToken,
        notification: {
          title: userData.title ? userData.title : "",
          body: userData.message ? userData.message : "",
        },
        data: {
          title: userData.title ? userData.title : "",
          body: JSON.stringify(userData),
        },
      };
      // resolve({
      //     status: 200,
      //     message: "User notification sent.",
      //     data: []
      // });

      fcm.send(message, function (err, response) {
        if (err) {
          console.log("Something has gone wrong!" + err);
          console.log("Respponse:! " + response);
          resolve({
            status: 200,
            message: "User notification not sent",
            data: err,
          });
        } else {
          console.log("Successfully sent with response: ", response);
          resolve({
            status: 200,
            message: "User notification sent.",
            data: response,
          });
        }
      });
    }),
  calculateDaysBetweenDates: function calculateDaysBetweenDates(
    startDateTime,
    endDateTime
  ) {
    // Parse the start and end date-time strings into Date objects
    const start = new Date(startDateTime);
    const end = new Date(endDateTime);

    // Calculate the difference in time (milliseconds)
    const timeDifference = end - start;

    // Convert the time difference from milliseconds to days
    const daysDifference = timeDifference / (1000 * 60 * 60 * 24);

    return daysDifference;
  },
  calculateNoOfDaysRemaining: function calculateDaysRemaining(
    startDateTime,
    endDateTime
  ) {
    // Parse the start and end date-time strings into Date objects
    const startDate = new Date(startDateTime);
    const endDate = new Date(endDateTime);

    // Get the current date
    const currentDate = new Date();

    // Check if the current date has passed the end date
    if (currentDate > endDate) {
      return -1;
    }

    // Check if the current date is before the start date
    const effectiveDate = currentDate < startDate ? startDate : currentDate;

    // Calculate the difference in time (milliseconds) between the effective date and the end date
    const timeDifference = endDate - effectiveDate;

    // Convert the time difference from milliseconds to days
    const daysRemaining = timeDifference / (1000 * 60 * 60 * 24);

    return Math.ceil(daysRemaining); // Return the number of full days remaining
  },
};
