com.callkeepexample.app-lifecycle-runtime-2.6.2-0 /Users/<USER>/.gradle/caches/8.14.1/transforms/17365bb8d44a96e8cb68a585945c3ff1/transformed/lifecycle-runtime-2.6.2/res
com.callkeepexample.app-emoji2-1.3.0-1 /Users/<USER>/.gradle/caches/8.14.1/transforms/184371a37a31633bc799675a52e2ed56/transformed/emoji2-1.3.0/res
com.callkeepexample.app-react-android-0.80.1-debug-2 /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/res
com.callkeepexample.app-lifecycle-livedata-2.6.2-3 /Users/<USER>/.gradle/caches/8.14.1/transforms/1e8ac3c34f4ad66978c651e59c9ade22/transformed/lifecycle-livedata-2.6.2/res
com.callkeepexample.app-lifecycle-process-2.6.2-4 /Users/<USER>/.gradle/caches/8.14.1/transforms/22777817996210683c00c27568dea84f/transformed/lifecycle-process-2.6.2/res
com.callkeepexample.app-core-ktx-1.13.1-5 /Users/<USER>/.gradle/caches/8.14.1/transforms/457018029e361bb933ad4db9f52194f2/transformed/core-ktx-1.13.1/res
com.callkeepexample.app-activity-1.7.0-6 /Users/<USER>/.gradle/caches/8.14.1/transforms/5104fe6d5a278875704c5617fdc0d9bb/transformed/activity-1.7.0/res
com.callkeepexample.app-appcompat-resources-1.7.0-7 /Users/<USER>/.gradle/caches/8.14.1/transforms/5bca0cfaae241dea23eba63d623ea919/transformed/appcompat-resources-1.7.0/res
com.callkeepexample.app-lifecycle-viewmodel-2.6.2-8 /Users/<USER>/.gradle/caches/8.14.1/transforms/5f7cdf5cb9559c5edcdf8f836411df92/transformed/lifecycle-viewmodel-2.6.2/res
com.callkeepexample.app-appcompat-1.7.0-9 /Users/<USER>/.gradle/caches/8.14.1/transforms/625ed137c6a3f5343b71917adedb437c/transformed/appcompat-1.7.0/res
com.callkeepexample.app-emoji2-views-helper-1.3.0-10 /Users/<USER>/.gradle/caches/8.14.1/transforms/6f803d8da21c4dc075759336e7569e71/transformed/emoji2-views-helper-1.3.0/res
com.callkeepexample.app-lifecycle-viewmodel-savedstate-2.6.2-11 /Users/<USER>/.gradle/caches/8.14.1/transforms/824d221d5c56526c3c6ddbae0b799560/transformed/lifecycle-viewmodel-savedstate-2.6.2/res
com.callkeepexample.app-savedstate-1.2.1-12 /Users/<USER>/.gradle/caches/8.14.1/transforms/875a7da5ac63e51a49a89062b608b501/transformed/savedstate-1.2.1/res
com.callkeepexample.app-tracing-1.1.0-13 /Users/<USER>/.gradle/caches/8.14.1/transforms/8e4ed2d5ba987a30decdd4c587d8ba80/transformed/tracing-1.1.0/res
com.callkeepexample.app-drawee-3.6.0-14 /Users/<USER>/.gradle/caches/8.14.1/transforms/956159063502be591b57ff4f5aaf7fef/transformed/drawee-3.6.0/res
com.callkeepexample.app-core-1.13.1-15 /Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/res
com.callkeepexample.app-autofill-1.1.0-16 /Users/<USER>/.gradle/caches/8.14.1/transforms/a3cb7bf04a84099f6bc24510b14a8d32/transformed/autofill-1.1.0/res
com.callkeepexample.app-localbroadcastmanager-1.1.0-17 /Users/<USER>/.gradle/caches/8.14.1/transforms/aa47678c27f278fb59b12debc3f458b4/transformed/localbroadcastmanager-1.1.0/res
com.callkeepexample.app-profileinstaller-1.3.1-18 /Users/<USER>/.gradle/caches/8.14.1/transforms/ab6840840bad5eecbd4248abd2d5c8a3/transformed/profileinstaller-1.3.1/res
com.callkeepexample.app-swiperefreshlayout-1.1.0-19 /Users/<USER>/.gradle/caches/8.14.1/transforms/bfef782026e93cfc945e58eb2e9dfb5e/transformed/swiperefreshlayout-1.1.0/res
com.callkeepexample.app-annotation-experimental-1.4.0-20 /Users/<USER>/.gradle/caches/8.14.1/transforms/c3096af9d9b6d92e9258ff81f7684ec0/transformed/annotation-experimental-1.4.0/res
com.callkeepexample.app-core-runtime-2.2.0-21 /Users/<USER>/.gradle/caches/8.14.1/transforms/cfda843d221c7111e1c82fff2513522e/transformed/core-runtime-2.2.0/res
com.callkeepexample.app-lifecycle-livedata-core-2.6.2-22 /Users/<USER>/.gradle/caches/8.14.1/transforms/dd91f2fa8a18639f14a2c2b25180debc/transformed/lifecycle-livedata-core-2.6.2/res
com.callkeepexample.app-fragment-1.5.4-23 /Users/<USER>/.gradle/caches/8.14.1/transforms/dec5fcd1d70b95b517bd22f6c9324073/transformed/fragment-1.5.4/res
com.callkeepexample.app-startup-runtime-1.1.1-24 /Users/<USER>/.gradle/caches/8.14.1/transforms/f8caf368debc3686ade571a4ec56cfe6/transformed/startup-runtime-1.1.1/res
com.callkeepexample.app-pngs-25 /Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/res/pngs/debug
com.callkeepexample.app-resValues-26 /Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/res/resValues/debug
com.callkeepexample.app-packageDebugResources-27 /Users/<USER>/Desktop/CallKeepExample/android/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
com.callkeepexample.app-packageDebugResources-28 /Users/<USER>/Desktop/CallKeepExample/android/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.callkeepexample.app-debug-29 /Users/<USER>/Desktop/CallKeepExample/android/app/build/intermediates/merged_res/debug/mergeDebugResources
com.callkeepexample.app-debug-30 /Users/<USER>/Desktop/CallKeepExample/android/app/src/debug/res
com.callkeepexample.app-main-31 /Users/<USER>/Desktop/CallKeepExample/android/app/src/main/res
com.callkeepexample.app-debug-32 /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-callkeep/android/build/intermediates/packaged_res/debug/packageDebugResources
com.callkeepexample.app-debug-33 /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native-get-random-values/android/build/intermediates/packaged_res/debug/packageDebugResources
