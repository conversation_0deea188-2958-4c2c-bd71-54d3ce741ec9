const AnalyticsService = require('../services/AnalyticsService');

const AnalyticsController = {
  /**
   * Track analytics events
   */
  trackAnalytics: async (req, res) => {
    try {
      const { events } = req.body;
      const userId = req.user?.user_id;

      if (!events || !Array.isArray(events)) {
        return res.status(400).json({
          status: false,
          message: "Invalid events data. Expected an array of events.",
          data: []
        });
      }

      console.log(`[AnalyticsController] Tracking ${events.length} events for user ${userId}`);

      // Process each event
      const results = [];
      for (const event of events) {
        try {
          const result = await AnalyticsService.trackEvent({
            ...event,
            userId: userId,
            timestamp: event.timestamp || Date.now()
          });
          results.push(result);
        } catch (error) {
          console.error(`[AnalyticsController] Error tracking event ${event.eventName}:`, error);
          results.push({
            eventName: event.eventName,
            success: false,
            error: error.message
          });
        }
      }

      return res.status(200).json({
        status: true,
        message: "Analytics events processed",
        data: {
          totalEvents: events.length,
          successfulEvents: results.filter(r => r.success !== false).length,
          results: results
        }
      });

    } catch (error) {
      console.error('[AnalyticsController] Error in trackAnalytics:', error);
      return res.status(500).json({
        status: false,
        message: "Internal server error while tracking analytics",
        data: []
      });
    }
  },

  /**
   * Get analytics data for a user or channel
   */
  getAnalytics: async (req, res) => {
    try {
      const { type, id, startDate, endDate } = req.query;
      const userId = req.user?.user_id;

      if (!type || !id) {
        return res.status(400).json({
          status: false,
          message: "Missing required parameters: type and id",
          data: []
        });
      }

      const analyticsData = await AnalyticsService.getAnalytics({
        type,
        id,
        userId,
        startDate,
        endDate
      });

      return res.status(200).json({
        status: true,
        message: "Analytics data retrieved successfully",
        data: analyticsData
      });

    } catch (error) {
      console.error('[AnalyticsController] Error in getAnalytics:', error);
      return res.status(500).json({
        status: false,
        message: "Internal server error while retrieving analytics",
        data: []
      });
    }
  }
};

module.exports = AnalyticsController;
