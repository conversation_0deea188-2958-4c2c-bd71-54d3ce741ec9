// API Response Types for AdTip Web Application

// Base API Response Structure
export interface BaseApiResponse {
  status: boolean | number;
  message: string;
}

// Pagination Information
export interface PaginationInfo {
  current_page: number;
  total_page: number;
  per_page: number;
  total_items: number;
  has_next: boolean;
  has_prev: boolean;
}

// Generic API Response with Data
export interface ApiResponse<T = any> extends BaseApiResponse {
  data: T;
  pagination?: PaginationInfo;
}

// User Related Types
export interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  profile_image?: string;
  accessToken: string;
  wallet_balance?: number;
  is_premium?: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: number;
  user_id: number;
  bio?: string;
  location?: string;
  website?: string;
  followers_count: number;
  following_count: number;
  posts_count: number;
}

// Wallet Related Types
export interface WalletResponse extends BaseApiResponse {
  availableBalance: string;
  currency?: string;
  last_updated?: string;
}

export interface WalletTransaction {
  id: number;
  user_id: number;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  status: 'pending' | 'completed' | 'failed';
  created_at: string;
}

// Post Related Types
export interface Post {
  id: number;
  user_id: number;
  user_name: string;
  user_profile_image?: string;
  title: string;
  content: string;
  media_url?: string;
  media_type: 'image' | 'video' | 'none';
  thumbnail_url?: string;
  duration?: string;
  category_id: number;
  category_name?: string;
  address: string;
  likeCount: number;
  commentCount: number;
  shareCount?: number;
  views: number;
  is_promoted: boolean;
  is_premium: boolean;
  created_at: string;
  updated_at: string;
}

export interface PostsListResponse extends BaseApiResponse {
  data: Post[];
  pagination: PaginationInfo;
}

// Category Related Types
export interface Category {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  posts_count?: number;
  is_active: boolean;
  created_at: string;
}

export interface CategoriesResponse extends BaseApiResponse {
  data: Category[];
}

// Comment Related Types
export interface Comment {
  id: number;
  post_id: number;
  user_id: number;
  user_name: string;
  user_profile_image?: string;
  content: string;
  parent_id?: number;
  likes_count: number;
  replies_count: number;
  created_at: string;
  updated_at: string;
}

export interface CommentsResponse extends BaseApiResponse {
  data: Comment[];
  pagination?: PaginationInfo;
}

// Marketplace Related Types
export interface Product {
  id: number;
  seller_id: number;
  seller_name: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  category_id: number;
  category_name: string;
  images: string[];
  stock_quantity: number;
  is_active: boolean;
  rating: number;
  reviews_count: number;
  created_at: string;
  updated_at: string;
}

export interface ProductsResponse extends BaseApiResponse {
  data: Product[];
  pagination?: PaginationInfo;
}

// Order Related Types
export interface Order {
  id: number;
  user_id: number;
  product_id: number;
  quantity: number;
  total_amount: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  shipping_address: string;
  payment_method: string;
  payment_status: 'pending' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
}

// Notification Related Types
export interface Notification {
  id: number;
  user_id: number;
  type: 'like' | 'comment' | 'follow' | 'post' | 'system';
  title: string;
  message: string;
  data?: Record<string, any>;
  is_read: boolean;
  created_at: string;
}

export interface NotificationsResponse extends BaseApiResponse {
  data: Notification[];
  unread_count: number;
}

// Authentication Related Types
export interface LoginRequest {
  email?: string;
  phone?: string;
  password?: string;
  otp?: string;
}

export interface LoginResponse extends BaseApiResponse {
  data: {
    user: User;
    token: string;
    expires_in: number;
  };
}

export interface OTPRequest {
  phone: string;
  type: 'login' | 'register' | 'forgot_password';
}

export interface OTPResponse extends BaseApiResponse {
  data: {
    otp_id: string;
    expires_in: number;
  };
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: Record<string, any>;
}

// Request Types
export interface PostsListRequest {
  category?: number;
  page?: string;
  limit?: string;
  loggined_user_id?: string;
  search?: string;
  sort_by?: 'latest' | 'popular' | 'trending';
}

export interface CreatePostRequest {
  title: string;
  content: string;
  category_id: number;
  media_file?: File;
  media_type?: 'image' | 'video';
  is_premium?: boolean;
}

// Upload Related Types
export interface UploadResponse extends BaseApiResponse {
  data: {
    url: string;
    filename: string;
    size: number;
    type: string;
  };
}

// Search Related Types
export interface SearchRequest {
  query: string;
  type?: 'posts' | 'users' | 'products' | 'all';
  page?: number;
  limit?: number;
}

export interface SearchResponse extends BaseApiResponse {
  data: {
    posts?: Post[];
    users?: User[];
    products?: Product[];
  };
  pagination?: PaginationInfo;
}

// Type Guards
export const isApiError = (error: any): error is ApiError => {
  return error && typeof error.message === 'string';
};

export const isApiResponse = <T>(response: any): response is ApiResponse<T> => {
  return response && 
         (typeof response.status === 'boolean' || typeof response.status === 'number') &&
         typeof response.message === 'string';
};

// Utility Types
export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface ApiRequestConfig {
  method: ApiMethod;
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
}

export default {
  BaseApiResponse,
  ApiResponse,
  User,
  Post,
  Category,
  Product,
  Order,
  Notification,
  WalletResponse,
  ApiError,
};
