{"name": "adtip-node-app", "version": "1.0.0", "description": "adtip-node-app", "author": "bajarang kadam", "main": "index.js", "scripts": {"server": "nodemon index.js", "migrate-chat": "node scripts/migrate_chat_schema.js"}, "engines": {"node": "16.x"}, "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.623.0", "@aws-sdk/s3-request-presigner": "^3.623.0", "@aws-sdk/types": "^3.609.0", "@google-cloud/appengine-admin": "^2.1.2", "agora-token": "^2.0.4", "apn": "^2.2.0", "aws-sdk": "^2.1667.0", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "crypto": "^1.0.1", "date-and-time": "^3.1.0", "dotenv": "^16.3.1", "express": "^4.19.2", "express-fileupload": "^1.2.0", "express-formidable": "^1.2.0", "firebase-admin": "^10.3.0", "firebase-functions": "^3.24.1", "geolib": "^3.3.4", "googleapis": "^140.0.0", "helmet": "^6.0.0", "ioredis": "^5.6.1", "jsonwebtoken": "^8.5.1", "libphonenumber-js": "^1.12.8", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "morgan": "^1.10.0", "multer": "^1.4.4", "mysql": "^2.18.1", "mysql2": "^3.14.2", "node-api-doc-generator": "^1.1.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "node-fetch": "^2.6.1", "nodemon": "^2.0.20", "qrcode": "^1.5.3", "razorpay": "^2.9.5", "redis": "^5.1.1", "request": "^2.88.2", "socket.io": "^4.8.1", "swagger-autogen": "^2.23.7", "swagger-ui-express": "^5.0.1", "twilio": "^3.83.3", "underscore": "^1.13.6", "uuid": "^8.3.2", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.18.2"}}