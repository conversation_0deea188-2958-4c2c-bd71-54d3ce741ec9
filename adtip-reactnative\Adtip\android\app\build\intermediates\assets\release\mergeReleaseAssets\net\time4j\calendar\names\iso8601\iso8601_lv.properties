# months
M(a)_1=janv.
M(a)_2=febr.
M(a)_3=marts
M(a)_4=apr.
M(a)_5=maijs
M(a)_6=jūn.
M(a)_7=jūl.
M(a)_8=aug.
M(a)_9=sept.
M(a)_10=okt.
M(a)_11=nov.
M(a)_12=dec.

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=janvāris
M(w)_2=februāris
M(w)_3=marts
M(w)_4=aprīlis
M(w)_5=maijs
M(w)_6=jūnijs
M(w)_7=jūlijs
M(w)_8=augusts
M(w)_9=septembris
M(w)_10=oktobris
M(w)_11=novembris
M(w)_12=decembris

M(A)_1=janv.
M(A)_2=febr.
M(A)_3=marts
M(A)_4=apr.
M(A)_5=maijs
M(A)_6=jūn.
M(A)_7=jūl.
M(A)_8=aug.
M(A)_9=sept.
M(A)_10=okt.
M(A)_11=nov.
M(A)_12=dec.

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=janvāris
M(W)_2=februāris
M(W)_3=marts
M(W)_4=aprīlis
M(W)_5=maijs
M(W)_6=jūnijs
M(W)_7=jūlijs
M(W)_8=augusts
M(W)_9=septembris
M(W)_10=oktobris
M(W)_11=novembris
M(W)_12=decembris

# weekdays
D(a)_1=pirmd.
D(a)_2=otrd.
D(a)_3=trešd.
D(a)_4=ceturtd.
D(a)_5=piektd.
D(a)_6=sestd.
D(a)_7=svētd.

D(n)_1=P
D(n)_2=O
D(n)_3=T
D(n)_4=C
D(n)_5=P
D(n)_6=S
D(n)_7=S

D(s)_1=Pr
D(s)_2=Ot
D(s)_3=Tr
D(s)_4=Ce
D(s)_5=Pk
D(s)_6=Se
D(s)_7=Sv

D(w)_1=pirmdiena
D(w)_2=otrdiena
D(w)_3=trešdiena
D(w)_4=ceturtdiena
D(w)_5=piektdiena
D(w)_6=sestdiena
D(w)_7=svētdiena

D(A)_1=Pirmd.
D(A)_2=Otrd.
D(A)_3=Trešd.
D(A)_4=Ceturtd.
D(A)_5=Piektd.
D(A)_6=Sestd.
D(A)_7=Svētd.

D(N)_1=P
D(N)_2=O
D(N)_3=T
D(N)_4=C
D(N)_5=P
D(N)_6=S
D(N)_7=S

D(S)_1=Pr
D(S)_2=Ot
D(S)_3=Tr
D(S)_4=Ce
D(S)_5=Pk
D(S)_6=Se
D(S)_7=Sv

D(W)_1=Pirmdiena
D(W)_2=Otrdiena
D(W)_3=Trešdiena
D(W)_4=Ceturtdiena
D(W)_5=Piektdiena
D(W)_6=Sestdiena
D(W)_7=Svētdiena

# quarters
Q(a)_1=1. cet.
Q(a)_2=2. cet.
Q(a)_3=3. cet.
Q(a)_4=4. cet.

Q(n)_1=1.
Q(n)_2=2.
Q(n)_3=3.
Q(n)_4=4.

Q(w)_1=1. ceturksnis
Q(w)_2=2. ceturksnis
Q(w)_3=3. ceturksnis
Q(w)_4=4. ceturksnis

Q(A)_1=1. cet.
Q(A)_2=2. cet.
Q(A)_3=3. cet.
Q(A)_4=4. cet.

Q(N)_1=1.
Q(N)_2=2.
Q(N)_3=3.
Q(N)_4=4.

Q(W)_1=1. ceturksnis
Q(W)_2=2. ceturksnis
Q(W)_3=3. ceturksnis
Q(W)_4=4. ceturksnis

# day-period-rules
T0600=morning1
T1200=afternoon1
T1800=evening1
T2300=night1

# day-period-translations
P(a)_midnight=pusnaktī
P(a)_am=priekšp.
P(a)_noon=pusd.
P(a)_pm=pēcp.
P(a)_morning1=no rīta
P(a)_afternoon1=pēcpusd.
P(a)_evening1=vakarā
P(a)_night1=naktī

P(n)_midnight=pusnaktī
P(n)_am=priekšp.
P(n)_noon=pusd.
P(n)_pm=pēcp.
P(n)_morning1=no rīta
P(n)_afternoon1=pēcpusd.
P(n)_evening1=vakarā
P(n)_night1=naktī

P(w)_midnight=pusnaktī
P(w)_am=priekšpusdienā
P(w)_noon=pusdienlaikā
P(w)_pm=pēcpusdienā
P(w)_morning1=no rīta
P(w)_afternoon1=pēcpusdienā
P(w)_evening1=vakarā
P(w)_night1=naktī

P(A)_midnight=pusnakts
P(A)_am=priekšp.
P(A)_noon=pusd.
P(A)_pm=pēcpusd.
P(A)_morning1=rīts
P(A)_afternoon1=pēcpusdiena
P(A)_evening1=vakars
P(A)_night1=nakts

P(N)_midnight=pusnakts
P(N)_am=priekšp.
P(N)_noon=pusd.
P(N)_pm=pēcpusd.
P(N)_morning1=rīts
P(N)_afternoon1=pēcpusd.
P(N)_evening1=vakars
P(N)_night1=nakts

P(W)_midnight=pusnakts
P(W)_am=priekšpusdiena
P(W)_noon=pusdienlaiks
P(W)_pm=pēcpusdiena
P(W)_morning1=rīts
P(W)_afternoon1=pēcpusdiena
P(W)_evening1=vakars
P(W)_night1=nakts

# eras
E(w)_0=pirms mūsu ēras
E(w)_1=mūsu ērā

E(a)_0=p.m.ē.
E(a)_1=m.ē.

E(n)_0=p.m.ē.
E(n|alt)_0=pmē
E(n)_1=m.ē.
E(n|alt)_1=mē

# format patterns
F(f)_d=EEEE, y. 'gada' d. MMMM
F(l)_d=y. 'gada' d. MMMM
F(m)_d=y. 'gada' d. MMM
F(s)_d=dd.MM.yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd.MM.
F_MMMd=d. MMM
F_MMMMd=d. MMMM
F_y=y. 'g'.
F_yM=MM.y.
F_yMMM=y. 'g'. MMM
F_yMMMM=y. 'g'. MMMM
F_yQQQ=y. 'g'. QQQ
F_yQQQQ=y. 'g'. QQQQ
F_yw=Y. 'g'. w. 'nedēļa'

I={0} - {1}

# labels of elements
L_era=ēra
L_year=gads
L_quarter=ceturksnis
L_month=mēnesis
L_week=nedēļa
L_day=diena
L_weekday=nedēļas diena
L_dayperiod=priekšpusdienā/pēcpusdienā
L_hour=stundas
L_minute=minūtes
L_second=sekundes
L_zone=laika josla
