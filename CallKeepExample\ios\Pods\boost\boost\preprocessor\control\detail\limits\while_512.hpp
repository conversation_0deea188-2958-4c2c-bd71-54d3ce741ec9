# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_CONTROL_DETAIL_WHILE_512_HPP
# define BOOST_PREPROCESSOR_CONTROL_DETAIL_WHILE_512_HPP
#
# define BOOST_PP_WHILE_257(p, o, s) BOOST_PP_WHILE_257_C(BOOST_PP_BOOL(p(258, s)), p, o, s)
# define BOOST_PP_WHILE_258(p, o, s) BOOST_PP_WHILE_258_C(BOOST_PP_BOOL(p(259, s)), p, o, s)
# define BOOST_PP_WHILE_259(p, o, s) BOOST_PP_WHILE_259_C(BOOST_PP_BOOL(p(260, s)), p, o, s)
# define BOOST_PP_WHILE_260(p, o, s) BOOST_PP_WHILE_260_C(BOOST_PP_BOOL(p(261, s)), p, o, s)
# define BOOST_PP_WHILE_261(p, o, s) BOOST_PP_WHILE_261_C(BOOST_PP_BOOL(p(262, s)), p, o, s)
# define BOOST_PP_WHILE_262(p, o, s) BOOST_PP_WHILE_262_C(BOOST_PP_BOOL(p(263, s)), p, o, s)
# define BOOST_PP_WHILE_263(p, o, s) BOOST_PP_WHILE_263_C(BOOST_PP_BOOL(p(264, s)), p, o, s)
# define BOOST_PP_WHILE_264(p, o, s) BOOST_PP_WHILE_264_C(BOOST_PP_BOOL(p(265, s)), p, o, s)
# define BOOST_PP_WHILE_265(p, o, s) BOOST_PP_WHILE_265_C(BOOST_PP_BOOL(p(266, s)), p, o, s)
# define BOOST_PP_WHILE_266(p, o, s) BOOST_PP_WHILE_266_C(BOOST_PP_BOOL(p(267, s)), p, o, s)
# define BOOST_PP_WHILE_267(p, o, s) BOOST_PP_WHILE_267_C(BOOST_PP_BOOL(p(268, s)), p, o, s)
# define BOOST_PP_WHILE_268(p, o, s) BOOST_PP_WHILE_268_C(BOOST_PP_BOOL(p(269, s)), p, o, s)
# define BOOST_PP_WHILE_269(p, o, s) BOOST_PP_WHILE_269_C(BOOST_PP_BOOL(p(270, s)), p, o, s)
# define BOOST_PP_WHILE_270(p, o, s) BOOST_PP_WHILE_270_C(BOOST_PP_BOOL(p(271, s)), p, o, s)
# define BOOST_PP_WHILE_271(p, o, s) BOOST_PP_WHILE_271_C(BOOST_PP_BOOL(p(272, s)), p, o, s)
# define BOOST_PP_WHILE_272(p, o, s) BOOST_PP_WHILE_272_C(BOOST_PP_BOOL(p(273, s)), p, o, s)
# define BOOST_PP_WHILE_273(p, o, s) BOOST_PP_WHILE_273_C(BOOST_PP_BOOL(p(274, s)), p, o, s)
# define BOOST_PP_WHILE_274(p, o, s) BOOST_PP_WHILE_274_C(BOOST_PP_BOOL(p(275, s)), p, o, s)
# define BOOST_PP_WHILE_275(p, o, s) BOOST_PP_WHILE_275_C(BOOST_PP_BOOL(p(276, s)), p, o, s)
# define BOOST_PP_WHILE_276(p, o, s) BOOST_PP_WHILE_276_C(BOOST_PP_BOOL(p(277, s)), p, o, s)
# define BOOST_PP_WHILE_277(p, o, s) BOOST_PP_WHILE_277_C(BOOST_PP_BOOL(p(278, s)), p, o, s)
# define BOOST_PP_WHILE_278(p, o, s) BOOST_PP_WHILE_278_C(BOOST_PP_BOOL(p(279, s)), p, o, s)
# define BOOST_PP_WHILE_279(p, o, s) BOOST_PP_WHILE_279_C(BOOST_PP_BOOL(p(280, s)), p, o, s)
# define BOOST_PP_WHILE_280(p, o, s) BOOST_PP_WHILE_280_C(BOOST_PP_BOOL(p(281, s)), p, o, s)
# define BOOST_PP_WHILE_281(p, o, s) BOOST_PP_WHILE_281_C(BOOST_PP_BOOL(p(282, s)), p, o, s)
# define BOOST_PP_WHILE_282(p, o, s) BOOST_PP_WHILE_282_C(BOOST_PP_BOOL(p(283, s)), p, o, s)
# define BOOST_PP_WHILE_283(p, o, s) BOOST_PP_WHILE_283_C(BOOST_PP_BOOL(p(284, s)), p, o, s)
# define BOOST_PP_WHILE_284(p, o, s) BOOST_PP_WHILE_284_C(BOOST_PP_BOOL(p(285, s)), p, o, s)
# define BOOST_PP_WHILE_285(p, o, s) BOOST_PP_WHILE_285_C(BOOST_PP_BOOL(p(286, s)), p, o, s)
# define BOOST_PP_WHILE_286(p, o, s) BOOST_PP_WHILE_286_C(BOOST_PP_BOOL(p(287, s)), p, o, s)
# define BOOST_PP_WHILE_287(p, o, s) BOOST_PP_WHILE_287_C(BOOST_PP_BOOL(p(288, s)), p, o, s)
# define BOOST_PP_WHILE_288(p, o, s) BOOST_PP_WHILE_288_C(BOOST_PP_BOOL(p(289, s)), p, o, s)
# define BOOST_PP_WHILE_289(p, o, s) BOOST_PP_WHILE_289_C(BOOST_PP_BOOL(p(290, s)), p, o, s)
# define BOOST_PP_WHILE_290(p, o, s) BOOST_PP_WHILE_290_C(BOOST_PP_BOOL(p(291, s)), p, o, s)
# define BOOST_PP_WHILE_291(p, o, s) BOOST_PP_WHILE_291_C(BOOST_PP_BOOL(p(292, s)), p, o, s)
# define BOOST_PP_WHILE_292(p, o, s) BOOST_PP_WHILE_292_C(BOOST_PP_BOOL(p(293, s)), p, o, s)
# define BOOST_PP_WHILE_293(p, o, s) BOOST_PP_WHILE_293_C(BOOST_PP_BOOL(p(294, s)), p, o, s)
# define BOOST_PP_WHILE_294(p, o, s) BOOST_PP_WHILE_294_C(BOOST_PP_BOOL(p(295, s)), p, o, s)
# define BOOST_PP_WHILE_295(p, o, s) BOOST_PP_WHILE_295_C(BOOST_PP_BOOL(p(296, s)), p, o, s)
# define BOOST_PP_WHILE_296(p, o, s) BOOST_PP_WHILE_296_C(BOOST_PP_BOOL(p(297, s)), p, o, s)
# define BOOST_PP_WHILE_297(p, o, s) BOOST_PP_WHILE_297_C(BOOST_PP_BOOL(p(298, s)), p, o, s)
# define BOOST_PP_WHILE_298(p, o, s) BOOST_PP_WHILE_298_C(BOOST_PP_BOOL(p(299, s)), p, o, s)
# define BOOST_PP_WHILE_299(p, o, s) BOOST_PP_WHILE_299_C(BOOST_PP_BOOL(p(300, s)), p, o, s)
# define BOOST_PP_WHILE_300(p, o, s) BOOST_PP_WHILE_300_C(BOOST_PP_BOOL(p(301, s)), p, o, s)
# define BOOST_PP_WHILE_301(p, o, s) BOOST_PP_WHILE_301_C(BOOST_PP_BOOL(p(302, s)), p, o, s)
# define BOOST_PP_WHILE_302(p, o, s) BOOST_PP_WHILE_302_C(BOOST_PP_BOOL(p(303, s)), p, o, s)
# define BOOST_PP_WHILE_303(p, o, s) BOOST_PP_WHILE_303_C(BOOST_PP_BOOL(p(304, s)), p, o, s)
# define BOOST_PP_WHILE_304(p, o, s) BOOST_PP_WHILE_304_C(BOOST_PP_BOOL(p(305, s)), p, o, s)
# define BOOST_PP_WHILE_305(p, o, s) BOOST_PP_WHILE_305_C(BOOST_PP_BOOL(p(306, s)), p, o, s)
# define BOOST_PP_WHILE_306(p, o, s) BOOST_PP_WHILE_306_C(BOOST_PP_BOOL(p(307, s)), p, o, s)
# define BOOST_PP_WHILE_307(p, o, s) BOOST_PP_WHILE_307_C(BOOST_PP_BOOL(p(308, s)), p, o, s)
# define BOOST_PP_WHILE_308(p, o, s) BOOST_PP_WHILE_308_C(BOOST_PP_BOOL(p(309, s)), p, o, s)
# define BOOST_PP_WHILE_309(p, o, s) BOOST_PP_WHILE_309_C(BOOST_PP_BOOL(p(310, s)), p, o, s)
# define BOOST_PP_WHILE_310(p, o, s) BOOST_PP_WHILE_310_C(BOOST_PP_BOOL(p(311, s)), p, o, s)
# define BOOST_PP_WHILE_311(p, o, s) BOOST_PP_WHILE_311_C(BOOST_PP_BOOL(p(312, s)), p, o, s)
# define BOOST_PP_WHILE_312(p, o, s) BOOST_PP_WHILE_312_C(BOOST_PP_BOOL(p(313, s)), p, o, s)
# define BOOST_PP_WHILE_313(p, o, s) BOOST_PP_WHILE_313_C(BOOST_PP_BOOL(p(314, s)), p, o, s)
# define BOOST_PP_WHILE_314(p, o, s) BOOST_PP_WHILE_314_C(BOOST_PP_BOOL(p(315, s)), p, o, s)
# define BOOST_PP_WHILE_315(p, o, s) BOOST_PP_WHILE_315_C(BOOST_PP_BOOL(p(316, s)), p, o, s)
# define BOOST_PP_WHILE_316(p, o, s) BOOST_PP_WHILE_316_C(BOOST_PP_BOOL(p(317, s)), p, o, s)
# define BOOST_PP_WHILE_317(p, o, s) BOOST_PP_WHILE_317_C(BOOST_PP_BOOL(p(318, s)), p, o, s)
# define BOOST_PP_WHILE_318(p, o, s) BOOST_PP_WHILE_318_C(BOOST_PP_BOOL(p(319, s)), p, o, s)
# define BOOST_PP_WHILE_319(p, o, s) BOOST_PP_WHILE_319_C(BOOST_PP_BOOL(p(320, s)), p, o, s)
# define BOOST_PP_WHILE_320(p, o, s) BOOST_PP_WHILE_320_C(BOOST_PP_BOOL(p(321, s)), p, o, s)
# define BOOST_PP_WHILE_321(p, o, s) BOOST_PP_WHILE_321_C(BOOST_PP_BOOL(p(322, s)), p, o, s)
# define BOOST_PP_WHILE_322(p, o, s) BOOST_PP_WHILE_322_C(BOOST_PP_BOOL(p(323, s)), p, o, s)
# define BOOST_PP_WHILE_323(p, o, s) BOOST_PP_WHILE_323_C(BOOST_PP_BOOL(p(324, s)), p, o, s)
# define BOOST_PP_WHILE_324(p, o, s) BOOST_PP_WHILE_324_C(BOOST_PP_BOOL(p(325, s)), p, o, s)
# define BOOST_PP_WHILE_325(p, o, s) BOOST_PP_WHILE_325_C(BOOST_PP_BOOL(p(326, s)), p, o, s)
# define BOOST_PP_WHILE_326(p, o, s) BOOST_PP_WHILE_326_C(BOOST_PP_BOOL(p(327, s)), p, o, s)
# define BOOST_PP_WHILE_327(p, o, s) BOOST_PP_WHILE_327_C(BOOST_PP_BOOL(p(328, s)), p, o, s)
# define BOOST_PP_WHILE_328(p, o, s) BOOST_PP_WHILE_328_C(BOOST_PP_BOOL(p(329, s)), p, o, s)
# define BOOST_PP_WHILE_329(p, o, s) BOOST_PP_WHILE_329_C(BOOST_PP_BOOL(p(330, s)), p, o, s)
# define BOOST_PP_WHILE_330(p, o, s) BOOST_PP_WHILE_330_C(BOOST_PP_BOOL(p(331, s)), p, o, s)
# define BOOST_PP_WHILE_331(p, o, s) BOOST_PP_WHILE_331_C(BOOST_PP_BOOL(p(332, s)), p, o, s)
# define BOOST_PP_WHILE_332(p, o, s) BOOST_PP_WHILE_332_C(BOOST_PP_BOOL(p(333, s)), p, o, s)
# define BOOST_PP_WHILE_333(p, o, s) BOOST_PP_WHILE_333_C(BOOST_PP_BOOL(p(334, s)), p, o, s)
# define BOOST_PP_WHILE_334(p, o, s) BOOST_PP_WHILE_334_C(BOOST_PP_BOOL(p(335, s)), p, o, s)
# define BOOST_PP_WHILE_335(p, o, s) BOOST_PP_WHILE_335_C(BOOST_PP_BOOL(p(336, s)), p, o, s)
# define BOOST_PP_WHILE_336(p, o, s) BOOST_PP_WHILE_336_C(BOOST_PP_BOOL(p(337, s)), p, o, s)
# define BOOST_PP_WHILE_337(p, o, s) BOOST_PP_WHILE_337_C(BOOST_PP_BOOL(p(338, s)), p, o, s)
# define BOOST_PP_WHILE_338(p, o, s) BOOST_PP_WHILE_338_C(BOOST_PP_BOOL(p(339, s)), p, o, s)
# define BOOST_PP_WHILE_339(p, o, s) BOOST_PP_WHILE_339_C(BOOST_PP_BOOL(p(340, s)), p, o, s)
# define BOOST_PP_WHILE_340(p, o, s) BOOST_PP_WHILE_340_C(BOOST_PP_BOOL(p(341, s)), p, o, s)
# define BOOST_PP_WHILE_341(p, o, s) BOOST_PP_WHILE_341_C(BOOST_PP_BOOL(p(342, s)), p, o, s)
# define BOOST_PP_WHILE_342(p, o, s) BOOST_PP_WHILE_342_C(BOOST_PP_BOOL(p(343, s)), p, o, s)
# define BOOST_PP_WHILE_343(p, o, s) BOOST_PP_WHILE_343_C(BOOST_PP_BOOL(p(344, s)), p, o, s)
# define BOOST_PP_WHILE_344(p, o, s) BOOST_PP_WHILE_344_C(BOOST_PP_BOOL(p(345, s)), p, o, s)
# define BOOST_PP_WHILE_345(p, o, s) BOOST_PP_WHILE_345_C(BOOST_PP_BOOL(p(346, s)), p, o, s)
# define BOOST_PP_WHILE_346(p, o, s) BOOST_PP_WHILE_346_C(BOOST_PP_BOOL(p(347, s)), p, o, s)
# define BOOST_PP_WHILE_347(p, o, s) BOOST_PP_WHILE_347_C(BOOST_PP_BOOL(p(348, s)), p, o, s)
# define BOOST_PP_WHILE_348(p, o, s) BOOST_PP_WHILE_348_C(BOOST_PP_BOOL(p(349, s)), p, o, s)
# define BOOST_PP_WHILE_349(p, o, s) BOOST_PP_WHILE_349_C(BOOST_PP_BOOL(p(350, s)), p, o, s)
# define BOOST_PP_WHILE_350(p, o, s) BOOST_PP_WHILE_350_C(BOOST_PP_BOOL(p(351, s)), p, o, s)
# define BOOST_PP_WHILE_351(p, o, s) BOOST_PP_WHILE_351_C(BOOST_PP_BOOL(p(352, s)), p, o, s)
# define BOOST_PP_WHILE_352(p, o, s) BOOST_PP_WHILE_352_C(BOOST_PP_BOOL(p(353, s)), p, o, s)
# define BOOST_PP_WHILE_353(p, o, s) BOOST_PP_WHILE_353_C(BOOST_PP_BOOL(p(354, s)), p, o, s)
# define BOOST_PP_WHILE_354(p, o, s) BOOST_PP_WHILE_354_C(BOOST_PP_BOOL(p(355, s)), p, o, s)
# define BOOST_PP_WHILE_355(p, o, s) BOOST_PP_WHILE_355_C(BOOST_PP_BOOL(p(356, s)), p, o, s)
# define BOOST_PP_WHILE_356(p, o, s) BOOST_PP_WHILE_356_C(BOOST_PP_BOOL(p(357, s)), p, o, s)
# define BOOST_PP_WHILE_357(p, o, s) BOOST_PP_WHILE_357_C(BOOST_PP_BOOL(p(358, s)), p, o, s)
# define BOOST_PP_WHILE_358(p, o, s) BOOST_PP_WHILE_358_C(BOOST_PP_BOOL(p(359, s)), p, o, s)
# define BOOST_PP_WHILE_359(p, o, s) BOOST_PP_WHILE_359_C(BOOST_PP_BOOL(p(360, s)), p, o, s)
# define BOOST_PP_WHILE_360(p, o, s) BOOST_PP_WHILE_360_C(BOOST_PP_BOOL(p(361, s)), p, o, s)
# define BOOST_PP_WHILE_361(p, o, s) BOOST_PP_WHILE_361_C(BOOST_PP_BOOL(p(362, s)), p, o, s)
# define BOOST_PP_WHILE_362(p, o, s) BOOST_PP_WHILE_362_C(BOOST_PP_BOOL(p(363, s)), p, o, s)
# define BOOST_PP_WHILE_363(p, o, s) BOOST_PP_WHILE_363_C(BOOST_PP_BOOL(p(364, s)), p, o, s)
# define BOOST_PP_WHILE_364(p, o, s) BOOST_PP_WHILE_364_C(BOOST_PP_BOOL(p(365, s)), p, o, s)
# define BOOST_PP_WHILE_365(p, o, s) BOOST_PP_WHILE_365_C(BOOST_PP_BOOL(p(366, s)), p, o, s)
# define BOOST_PP_WHILE_366(p, o, s) BOOST_PP_WHILE_366_C(BOOST_PP_BOOL(p(367, s)), p, o, s)
# define BOOST_PP_WHILE_367(p, o, s) BOOST_PP_WHILE_367_C(BOOST_PP_BOOL(p(368, s)), p, o, s)
# define BOOST_PP_WHILE_368(p, o, s) BOOST_PP_WHILE_368_C(BOOST_PP_BOOL(p(369, s)), p, o, s)
# define BOOST_PP_WHILE_369(p, o, s) BOOST_PP_WHILE_369_C(BOOST_PP_BOOL(p(370, s)), p, o, s)
# define BOOST_PP_WHILE_370(p, o, s) BOOST_PP_WHILE_370_C(BOOST_PP_BOOL(p(371, s)), p, o, s)
# define BOOST_PP_WHILE_371(p, o, s) BOOST_PP_WHILE_371_C(BOOST_PP_BOOL(p(372, s)), p, o, s)
# define BOOST_PP_WHILE_372(p, o, s) BOOST_PP_WHILE_372_C(BOOST_PP_BOOL(p(373, s)), p, o, s)
# define BOOST_PP_WHILE_373(p, o, s) BOOST_PP_WHILE_373_C(BOOST_PP_BOOL(p(374, s)), p, o, s)
# define BOOST_PP_WHILE_374(p, o, s) BOOST_PP_WHILE_374_C(BOOST_PP_BOOL(p(375, s)), p, o, s)
# define BOOST_PP_WHILE_375(p, o, s) BOOST_PP_WHILE_375_C(BOOST_PP_BOOL(p(376, s)), p, o, s)
# define BOOST_PP_WHILE_376(p, o, s) BOOST_PP_WHILE_376_C(BOOST_PP_BOOL(p(377, s)), p, o, s)
# define BOOST_PP_WHILE_377(p, o, s) BOOST_PP_WHILE_377_C(BOOST_PP_BOOL(p(378, s)), p, o, s)
# define BOOST_PP_WHILE_378(p, o, s) BOOST_PP_WHILE_378_C(BOOST_PP_BOOL(p(379, s)), p, o, s)
# define BOOST_PP_WHILE_379(p, o, s) BOOST_PP_WHILE_379_C(BOOST_PP_BOOL(p(380, s)), p, o, s)
# define BOOST_PP_WHILE_380(p, o, s) BOOST_PP_WHILE_380_C(BOOST_PP_BOOL(p(381, s)), p, o, s)
# define BOOST_PP_WHILE_381(p, o, s) BOOST_PP_WHILE_381_C(BOOST_PP_BOOL(p(382, s)), p, o, s)
# define BOOST_PP_WHILE_382(p, o, s) BOOST_PP_WHILE_382_C(BOOST_PP_BOOL(p(383, s)), p, o, s)
# define BOOST_PP_WHILE_383(p, o, s) BOOST_PP_WHILE_383_C(BOOST_PP_BOOL(p(384, s)), p, o, s)
# define BOOST_PP_WHILE_384(p, o, s) BOOST_PP_WHILE_384_C(BOOST_PP_BOOL(p(385, s)), p, o, s)
# define BOOST_PP_WHILE_385(p, o, s) BOOST_PP_WHILE_385_C(BOOST_PP_BOOL(p(386, s)), p, o, s)
# define BOOST_PP_WHILE_386(p, o, s) BOOST_PP_WHILE_386_C(BOOST_PP_BOOL(p(387, s)), p, o, s)
# define BOOST_PP_WHILE_387(p, o, s) BOOST_PP_WHILE_387_C(BOOST_PP_BOOL(p(388, s)), p, o, s)
# define BOOST_PP_WHILE_388(p, o, s) BOOST_PP_WHILE_388_C(BOOST_PP_BOOL(p(389, s)), p, o, s)
# define BOOST_PP_WHILE_389(p, o, s) BOOST_PP_WHILE_389_C(BOOST_PP_BOOL(p(390, s)), p, o, s)
# define BOOST_PP_WHILE_390(p, o, s) BOOST_PP_WHILE_390_C(BOOST_PP_BOOL(p(391, s)), p, o, s)
# define BOOST_PP_WHILE_391(p, o, s) BOOST_PP_WHILE_391_C(BOOST_PP_BOOL(p(392, s)), p, o, s)
# define BOOST_PP_WHILE_392(p, o, s) BOOST_PP_WHILE_392_C(BOOST_PP_BOOL(p(393, s)), p, o, s)
# define BOOST_PP_WHILE_393(p, o, s) BOOST_PP_WHILE_393_C(BOOST_PP_BOOL(p(394, s)), p, o, s)
# define BOOST_PP_WHILE_394(p, o, s) BOOST_PP_WHILE_394_C(BOOST_PP_BOOL(p(395, s)), p, o, s)
# define BOOST_PP_WHILE_395(p, o, s) BOOST_PP_WHILE_395_C(BOOST_PP_BOOL(p(396, s)), p, o, s)
# define BOOST_PP_WHILE_396(p, o, s) BOOST_PP_WHILE_396_C(BOOST_PP_BOOL(p(397, s)), p, o, s)
# define BOOST_PP_WHILE_397(p, o, s) BOOST_PP_WHILE_397_C(BOOST_PP_BOOL(p(398, s)), p, o, s)
# define BOOST_PP_WHILE_398(p, o, s) BOOST_PP_WHILE_398_C(BOOST_PP_BOOL(p(399, s)), p, o, s)
# define BOOST_PP_WHILE_399(p, o, s) BOOST_PP_WHILE_399_C(BOOST_PP_BOOL(p(400, s)), p, o, s)
# define BOOST_PP_WHILE_400(p, o, s) BOOST_PP_WHILE_400_C(BOOST_PP_BOOL(p(401, s)), p, o, s)
# define BOOST_PP_WHILE_401(p, o, s) BOOST_PP_WHILE_401_C(BOOST_PP_BOOL(p(402, s)), p, o, s)
# define BOOST_PP_WHILE_402(p, o, s) BOOST_PP_WHILE_402_C(BOOST_PP_BOOL(p(403, s)), p, o, s)
# define BOOST_PP_WHILE_403(p, o, s) BOOST_PP_WHILE_403_C(BOOST_PP_BOOL(p(404, s)), p, o, s)
# define BOOST_PP_WHILE_404(p, o, s) BOOST_PP_WHILE_404_C(BOOST_PP_BOOL(p(405, s)), p, o, s)
# define BOOST_PP_WHILE_405(p, o, s) BOOST_PP_WHILE_405_C(BOOST_PP_BOOL(p(406, s)), p, o, s)
# define BOOST_PP_WHILE_406(p, o, s) BOOST_PP_WHILE_406_C(BOOST_PP_BOOL(p(407, s)), p, o, s)
# define BOOST_PP_WHILE_407(p, o, s) BOOST_PP_WHILE_407_C(BOOST_PP_BOOL(p(408, s)), p, o, s)
# define BOOST_PP_WHILE_408(p, o, s) BOOST_PP_WHILE_408_C(BOOST_PP_BOOL(p(409, s)), p, o, s)
# define BOOST_PP_WHILE_409(p, o, s) BOOST_PP_WHILE_409_C(BOOST_PP_BOOL(p(410, s)), p, o, s)
# define BOOST_PP_WHILE_410(p, o, s) BOOST_PP_WHILE_410_C(BOOST_PP_BOOL(p(411, s)), p, o, s)
# define BOOST_PP_WHILE_411(p, o, s) BOOST_PP_WHILE_411_C(BOOST_PP_BOOL(p(412, s)), p, o, s)
# define BOOST_PP_WHILE_412(p, o, s) BOOST_PP_WHILE_412_C(BOOST_PP_BOOL(p(413, s)), p, o, s)
# define BOOST_PP_WHILE_413(p, o, s) BOOST_PP_WHILE_413_C(BOOST_PP_BOOL(p(414, s)), p, o, s)
# define BOOST_PP_WHILE_414(p, o, s) BOOST_PP_WHILE_414_C(BOOST_PP_BOOL(p(415, s)), p, o, s)
# define BOOST_PP_WHILE_415(p, o, s) BOOST_PP_WHILE_415_C(BOOST_PP_BOOL(p(416, s)), p, o, s)
# define BOOST_PP_WHILE_416(p, o, s) BOOST_PP_WHILE_416_C(BOOST_PP_BOOL(p(417, s)), p, o, s)
# define BOOST_PP_WHILE_417(p, o, s) BOOST_PP_WHILE_417_C(BOOST_PP_BOOL(p(418, s)), p, o, s)
# define BOOST_PP_WHILE_418(p, o, s) BOOST_PP_WHILE_418_C(BOOST_PP_BOOL(p(419, s)), p, o, s)
# define BOOST_PP_WHILE_419(p, o, s) BOOST_PP_WHILE_419_C(BOOST_PP_BOOL(p(420, s)), p, o, s)
# define BOOST_PP_WHILE_420(p, o, s) BOOST_PP_WHILE_420_C(BOOST_PP_BOOL(p(421, s)), p, o, s)
# define BOOST_PP_WHILE_421(p, o, s) BOOST_PP_WHILE_421_C(BOOST_PP_BOOL(p(422, s)), p, o, s)
# define BOOST_PP_WHILE_422(p, o, s) BOOST_PP_WHILE_422_C(BOOST_PP_BOOL(p(423, s)), p, o, s)
# define BOOST_PP_WHILE_423(p, o, s) BOOST_PP_WHILE_423_C(BOOST_PP_BOOL(p(424, s)), p, o, s)
# define BOOST_PP_WHILE_424(p, o, s) BOOST_PP_WHILE_424_C(BOOST_PP_BOOL(p(425, s)), p, o, s)
# define BOOST_PP_WHILE_425(p, o, s) BOOST_PP_WHILE_425_C(BOOST_PP_BOOL(p(426, s)), p, o, s)
# define BOOST_PP_WHILE_426(p, o, s) BOOST_PP_WHILE_426_C(BOOST_PP_BOOL(p(427, s)), p, o, s)
# define BOOST_PP_WHILE_427(p, o, s) BOOST_PP_WHILE_427_C(BOOST_PP_BOOL(p(428, s)), p, o, s)
# define BOOST_PP_WHILE_428(p, o, s) BOOST_PP_WHILE_428_C(BOOST_PP_BOOL(p(429, s)), p, o, s)
# define BOOST_PP_WHILE_429(p, o, s) BOOST_PP_WHILE_429_C(BOOST_PP_BOOL(p(430, s)), p, o, s)
# define BOOST_PP_WHILE_430(p, o, s) BOOST_PP_WHILE_430_C(BOOST_PP_BOOL(p(431, s)), p, o, s)
# define BOOST_PP_WHILE_431(p, o, s) BOOST_PP_WHILE_431_C(BOOST_PP_BOOL(p(432, s)), p, o, s)
# define BOOST_PP_WHILE_432(p, o, s) BOOST_PP_WHILE_432_C(BOOST_PP_BOOL(p(433, s)), p, o, s)
# define BOOST_PP_WHILE_433(p, o, s) BOOST_PP_WHILE_433_C(BOOST_PP_BOOL(p(434, s)), p, o, s)
# define BOOST_PP_WHILE_434(p, o, s) BOOST_PP_WHILE_434_C(BOOST_PP_BOOL(p(435, s)), p, o, s)
# define BOOST_PP_WHILE_435(p, o, s) BOOST_PP_WHILE_435_C(BOOST_PP_BOOL(p(436, s)), p, o, s)
# define BOOST_PP_WHILE_436(p, o, s) BOOST_PP_WHILE_436_C(BOOST_PP_BOOL(p(437, s)), p, o, s)
# define BOOST_PP_WHILE_437(p, o, s) BOOST_PP_WHILE_437_C(BOOST_PP_BOOL(p(438, s)), p, o, s)
# define BOOST_PP_WHILE_438(p, o, s) BOOST_PP_WHILE_438_C(BOOST_PP_BOOL(p(439, s)), p, o, s)
# define BOOST_PP_WHILE_439(p, o, s) BOOST_PP_WHILE_439_C(BOOST_PP_BOOL(p(440, s)), p, o, s)
# define BOOST_PP_WHILE_440(p, o, s) BOOST_PP_WHILE_440_C(BOOST_PP_BOOL(p(441, s)), p, o, s)
# define BOOST_PP_WHILE_441(p, o, s) BOOST_PP_WHILE_441_C(BOOST_PP_BOOL(p(442, s)), p, o, s)
# define BOOST_PP_WHILE_442(p, o, s) BOOST_PP_WHILE_442_C(BOOST_PP_BOOL(p(443, s)), p, o, s)
# define BOOST_PP_WHILE_443(p, o, s) BOOST_PP_WHILE_443_C(BOOST_PP_BOOL(p(444, s)), p, o, s)
# define BOOST_PP_WHILE_444(p, o, s) BOOST_PP_WHILE_444_C(BOOST_PP_BOOL(p(445, s)), p, o, s)
# define BOOST_PP_WHILE_445(p, o, s) BOOST_PP_WHILE_445_C(BOOST_PP_BOOL(p(446, s)), p, o, s)
# define BOOST_PP_WHILE_446(p, o, s) BOOST_PP_WHILE_446_C(BOOST_PP_BOOL(p(447, s)), p, o, s)
# define BOOST_PP_WHILE_447(p, o, s) BOOST_PP_WHILE_447_C(BOOST_PP_BOOL(p(448, s)), p, o, s)
# define BOOST_PP_WHILE_448(p, o, s) BOOST_PP_WHILE_448_C(BOOST_PP_BOOL(p(449, s)), p, o, s)
# define BOOST_PP_WHILE_449(p, o, s) BOOST_PP_WHILE_449_C(BOOST_PP_BOOL(p(450, s)), p, o, s)
# define BOOST_PP_WHILE_450(p, o, s) BOOST_PP_WHILE_450_C(BOOST_PP_BOOL(p(451, s)), p, o, s)
# define BOOST_PP_WHILE_451(p, o, s) BOOST_PP_WHILE_451_C(BOOST_PP_BOOL(p(452, s)), p, o, s)
# define BOOST_PP_WHILE_452(p, o, s) BOOST_PP_WHILE_452_C(BOOST_PP_BOOL(p(453, s)), p, o, s)
# define BOOST_PP_WHILE_453(p, o, s) BOOST_PP_WHILE_453_C(BOOST_PP_BOOL(p(454, s)), p, o, s)
# define BOOST_PP_WHILE_454(p, o, s) BOOST_PP_WHILE_454_C(BOOST_PP_BOOL(p(455, s)), p, o, s)
# define BOOST_PP_WHILE_455(p, o, s) BOOST_PP_WHILE_455_C(BOOST_PP_BOOL(p(456, s)), p, o, s)
# define BOOST_PP_WHILE_456(p, o, s) BOOST_PP_WHILE_456_C(BOOST_PP_BOOL(p(457, s)), p, o, s)
# define BOOST_PP_WHILE_457(p, o, s) BOOST_PP_WHILE_457_C(BOOST_PP_BOOL(p(458, s)), p, o, s)
# define BOOST_PP_WHILE_458(p, o, s) BOOST_PP_WHILE_458_C(BOOST_PP_BOOL(p(459, s)), p, o, s)
# define BOOST_PP_WHILE_459(p, o, s) BOOST_PP_WHILE_459_C(BOOST_PP_BOOL(p(460, s)), p, o, s)
# define BOOST_PP_WHILE_460(p, o, s) BOOST_PP_WHILE_460_C(BOOST_PP_BOOL(p(461, s)), p, o, s)
# define BOOST_PP_WHILE_461(p, o, s) BOOST_PP_WHILE_461_C(BOOST_PP_BOOL(p(462, s)), p, o, s)
# define BOOST_PP_WHILE_462(p, o, s) BOOST_PP_WHILE_462_C(BOOST_PP_BOOL(p(463, s)), p, o, s)
# define BOOST_PP_WHILE_463(p, o, s) BOOST_PP_WHILE_463_C(BOOST_PP_BOOL(p(464, s)), p, o, s)
# define BOOST_PP_WHILE_464(p, o, s) BOOST_PP_WHILE_464_C(BOOST_PP_BOOL(p(465, s)), p, o, s)
# define BOOST_PP_WHILE_465(p, o, s) BOOST_PP_WHILE_465_C(BOOST_PP_BOOL(p(466, s)), p, o, s)
# define BOOST_PP_WHILE_466(p, o, s) BOOST_PP_WHILE_466_C(BOOST_PP_BOOL(p(467, s)), p, o, s)
# define BOOST_PP_WHILE_467(p, o, s) BOOST_PP_WHILE_467_C(BOOST_PP_BOOL(p(468, s)), p, o, s)
# define BOOST_PP_WHILE_468(p, o, s) BOOST_PP_WHILE_468_C(BOOST_PP_BOOL(p(469, s)), p, o, s)
# define BOOST_PP_WHILE_469(p, o, s) BOOST_PP_WHILE_469_C(BOOST_PP_BOOL(p(470, s)), p, o, s)
# define BOOST_PP_WHILE_470(p, o, s) BOOST_PP_WHILE_470_C(BOOST_PP_BOOL(p(471, s)), p, o, s)
# define BOOST_PP_WHILE_471(p, o, s) BOOST_PP_WHILE_471_C(BOOST_PP_BOOL(p(472, s)), p, o, s)
# define BOOST_PP_WHILE_472(p, o, s) BOOST_PP_WHILE_472_C(BOOST_PP_BOOL(p(473, s)), p, o, s)
# define BOOST_PP_WHILE_473(p, o, s) BOOST_PP_WHILE_473_C(BOOST_PP_BOOL(p(474, s)), p, o, s)
# define BOOST_PP_WHILE_474(p, o, s) BOOST_PP_WHILE_474_C(BOOST_PP_BOOL(p(475, s)), p, o, s)
# define BOOST_PP_WHILE_475(p, o, s) BOOST_PP_WHILE_475_C(BOOST_PP_BOOL(p(476, s)), p, o, s)
# define BOOST_PP_WHILE_476(p, o, s) BOOST_PP_WHILE_476_C(BOOST_PP_BOOL(p(477, s)), p, o, s)
# define BOOST_PP_WHILE_477(p, o, s) BOOST_PP_WHILE_477_C(BOOST_PP_BOOL(p(478, s)), p, o, s)
# define BOOST_PP_WHILE_478(p, o, s) BOOST_PP_WHILE_478_C(BOOST_PP_BOOL(p(479, s)), p, o, s)
# define BOOST_PP_WHILE_479(p, o, s) BOOST_PP_WHILE_479_C(BOOST_PP_BOOL(p(480, s)), p, o, s)
# define BOOST_PP_WHILE_480(p, o, s) BOOST_PP_WHILE_480_C(BOOST_PP_BOOL(p(481, s)), p, o, s)
# define BOOST_PP_WHILE_481(p, o, s) BOOST_PP_WHILE_481_C(BOOST_PP_BOOL(p(482, s)), p, o, s)
# define BOOST_PP_WHILE_482(p, o, s) BOOST_PP_WHILE_482_C(BOOST_PP_BOOL(p(483, s)), p, o, s)
# define BOOST_PP_WHILE_483(p, o, s) BOOST_PP_WHILE_483_C(BOOST_PP_BOOL(p(484, s)), p, o, s)
# define BOOST_PP_WHILE_484(p, o, s) BOOST_PP_WHILE_484_C(BOOST_PP_BOOL(p(485, s)), p, o, s)
# define BOOST_PP_WHILE_485(p, o, s) BOOST_PP_WHILE_485_C(BOOST_PP_BOOL(p(486, s)), p, o, s)
# define BOOST_PP_WHILE_486(p, o, s) BOOST_PP_WHILE_486_C(BOOST_PP_BOOL(p(487, s)), p, o, s)
# define BOOST_PP_WHILE_487(p, o, s) BOOST_PP_WHILE_487_C(BOOST_PP_BOOL(p(488, s)), p, o, s)
# define BOOST_PP_WHILE_488(p, o, s) BOOST_PP_WHILE_488_C(BOOST_PP_BOOL(p(489, s)), p, o, s)
# define BOOST_PP_WHILE_489(p, o, s) BOOST_PP_WHILE_489_C(BOOST_PP_BOOL(p(490, s)), p, o, s)
# define BOOST_PP_WHILE_490(p, o, s) BOOST_PP_WHILE_490_C(BOOST_PP_BOOL(p(491, s)), p, o, s)
# define BOOST_PP_WHILE_491(p, o, s) BOOST_PP_WHILE_491_C(BOOST_PP_BOOL(p(492, s)), p, o, s)
# define BOOST_PP_WHILE_492(p, o, s) BOOST_PP_WHILE_492_C(BOOST_PP_BOOL(p(493, s)), p, o, s)
# define BOOST_PP_WHILE_493(p, o, s) BOOST_PP_WHILE_493_C(BOOST_PP_BOOL(p(494, s)), p, o, s)
# define BOOST_PP_WHILE_494(p, o, s) BOOST_PP_WHILE_494_C(BOOST_PP_BOOL(p(495, s)), p, o, s)
# define BOOST_PP_WHILE_495(p, o, s) BOOST_PP_WHILE_495_C(BOOST_PP_BOOL(p(496, s)), p, o, s)
# define BOOST_PP_WHILE_496(p, o, s) BOOST_PP_WHILE_496_C(BOOST_PP_BOOL(p(497, s)), p, o, s)
# define BOOST_PP_WHILE_497(p, o, s) BOOST_PP_WHILE_497_C(BOOST_PP_BOOL(p(498, s)), p, o, s)
# define BOOST_PP_WHILE_498(p, o, s) BOOST_PP_WHILE_498_C(BOOST_PP_BOOL(p(499, s)), p, o, s)
# define BOOST_PP_WHILE_499(p, o, s) BOOST_PP_WHILE_499_C(BOOST_PP_BOOL(p(500, s)), p, o, s)
# define BOOST_PP_WHILE_500(p, o, s) BOOST_PP_WHILE_500_C(BOOST_PP_BOOL(p(501, s)), p, o, s)
# define BOOST_PP_WHILE_501(p, o, s) BOOST_PP_WHILE_501_C(BOOST_PP_BOOL(p(502, s)), p, o, s)
# define BOOST_PP_WHILE_502(p, o, s) BOOST_PP_WHILE_502_C(BOOST_PP_BOOL(p(503, s)), p, o, s)
# define BOOST_PP_WHILE_503(p, o, s) BOOST_PP_WHILE_503_C(BOOST_PP_BOOL(p(504, s)), p, o, s)
# define BOOST_PP_WHILE_504(p, o, s) BOOST_PP_WHILE_504_C(BOOST_PP_BOOL(p(505, s)), p, o, s)
# define BOOST_PP_WHILE_505(p, o, s) BOOST_PP_WHILE_505_C(BOOST_PP_BOOL(p(506, s)), p, o, s)
# define BOOST_PP_WHILE_506(p, o, s) BOOST_PP_WHILE_506_C(BOOST_PP_BOOL(p(507, s)), p, o, s)
# define BOOST_PP_WHILE_507(p, o, s) BOOST_PP_WHILE_507_C(BOOST_PP_BOOL(p(508, s)), p, o, s)
# define BOOST_PP_WHILE_508(p, o, s) BOOST_PP_WHILE_508_C(BOOST_PP_BOOL(p(509, s)), p, o, s)
# define BOOST_PP_WHILE_509(p, o, s) BOOST_PP_WHILE_509_C(BOOST_PP_BOOL(p(510, s)), p, o, s)
# define BOOST_PP_WHILE_510(p, o, s) BOOST_PP_WHILE_510_C(BOOST_PP_BOOL(p(511, s)), p, o, s)
# define BOOST_PP_WHILE_511(p, o, s) BOOST_PP_WHILE_511_C(BOOST_PP_BOOL(p(512, s)), p, o, s)
# define BOOST_PP_WHILE_512(p, o, s) BOOST_PP_WHILE_512_C(BOOST_PP_BOOL(p(513, s)), p, o, s)
#
# define BOOST_PP_WHILE_257_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_258, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(258, s))
# define BOOST_PP_WHILE_258_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_259, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(259, s))
# define BOOST_PP_WHILE_259_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_260, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(260, s))
# define BOOST_PP_WHILE_260_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_261, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(261, s))
# define BOOST_PP_WHILE_261_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_262, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(262, s))
# define BOOST_PP_WHILE_262_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_263, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(263, s))
# define BOOST_PP_WHILE_263_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_264, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(264, s))
# define BOOST_PP_WHILE_264_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_265, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(265, s))
# define BOOST_PP_WHILE_265_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_266, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(266, s))
# define BOOST_PP_WHILE_266_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_267, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(267, s))
# define BOOST_PP_WHILE_267_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_268, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(268, s))
# define BOOST_PP_WHILE_268_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_269, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(269, s))
# define BOOST_PP_WHILE_269_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_270, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(270, s))
# define BOOST_PP_WHILE_270_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_271, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(271, s))
# define BOOST_PP_WHILE_271_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_272, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(272, s))
# define BOOST_PP_WHILE_272_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_273, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(273, s))
# define BOOST_PP_WHILE_273_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_274, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(274, s))
# define BOOST_PP_WHILE_274_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_275, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(275, s))
# define BOOST_PP_WHILE_275_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_276, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(276, s))
# define BOOST_PP_WHILE_276_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_277, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(277, s))
# define BOOST_PP_WHILE_277_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_278, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(278, s))
# define BOOST_PP_WHILE_278_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_279, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(279, s))
# define BOOST_PP_WHILE_279_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_280, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(280, s))
# define BOOST_PP_WHILE_280_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_281, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(281, s))
# define BOOST_PP_WHILE_281_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_282, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(282, s))
# define BOOST_PP_WHILE_282_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_283, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(283, s))
# define BOOST_PP_WHILE_283_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_284, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(284, s))
# define BOOST_PP_WHILE_284_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_285, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(285, s))
# define BOOST_PP_WHILE_285_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_286, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(286, s))
# define BOOST_PP_WHILE_286_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_287, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(287, s))
# define BOOST_PP_WHILE_287_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_288, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(288, s))
# define BOOST_PP_WHILE_288_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_289, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(289, s))
# define BOOST_PP_WHILE_289_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_290, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(290, s))
# define BOOST_PP_WHILE_290_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_291, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(291, s))
# define BOOST_PP_WHILE_291_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_292, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(292, s))
# define BOOST_PP_WHILE_292_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_293, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(293, s))
# define BOOST_PP_WHILE_293_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_294, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(294, s))
# define BOOST_PP_WHILE_294_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_295, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(295, s))
# define BOOST_PP_WHILE_295_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_296, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(296, s))
# define BOOST_PP_WHILE_296_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_297, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(297, s))
# define BOOST_PP_WHILE_297_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_298, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(298, s))
# define BOOST_PP_WHILE_298_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_299, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(299, s))
# define BOOST_PP_WHILE_299_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_300, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(300, s))
# define BOOST_PP_WHILE_300_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_301, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(301, s))
# define BOOST_PP_WHILE_301_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_302, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(302, s))
# define BOOST_PP_WHILE_302_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_303, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(303, s))
# define BOOST_PP_WHILE_303_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_304, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(304, s))
# define BOOST_PP_WHILE_304_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_305, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(305, s))
# define BOOST_PP_WHILE_305_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_306, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(306, s))
# define BOOST_PP_WHILE_306_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_307, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(307, s))
# define BOOST_PP_WHILE_307_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_308, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(308, s))
# define BOOST_PP_WHILE_308_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_309, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(309, s))
# define BOOST_PP_WHILE_309_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_310, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(310, s))
# define BOOST_PP_WHILE_310_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_311, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(311, s))
# define BOOST_PP_WHILE_311_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_312, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(312, s))
# define BOOST_PP_WHILE_312_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_313, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(313, s))
# define BOOST_PP_WHILE_313_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_314, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(314, s))
# define BOOST_PP_WHILE_314_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_315, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(315, s))
# define BOOST_PP_WHILE_315_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_316, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(316, s))
# define BOOST_PP_WHILE_316_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_317, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(317, s))
# define BOOST_PP_WHILE_317_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_318, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(318, s))
# define BOOST_PP_WHILE_318_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_319, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(319, s))
# define BOOST_PP_WHILE_319_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_320, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(320, s))
# define BOOST_PP_WHILE_320_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_321, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(321, s))
# define BOOST_PP_WHILE_321_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_322, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(322, s))
# define BOOST_PP_WHILE_322_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_323, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(323, s))
# define BOOST_PP_WHILE_323_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_324, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(324, s))
# define BOOST_PP_WHILE_324_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_325, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(325, s))
# define BOOST_PP_WHILE_325_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_326, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(326, s))
# define BOOST_PP_WHILE_326_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_327, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(327, s))
# define BOOST_PP_WHILE_327_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_328, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(328, s))
# define BOOST_PP_WHILE_328_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_329, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(329, s))
# define BOOST_PP_WHILE_329_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_330, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(330, s))
# define BOOST_PP_WHILE_330_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_331, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(331, s))
# define BOOST_PP_WHILE_331_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_332, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(332, s))
# define BOOST_PP_WHILE_332_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_333, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(333, s))
# define BOOST_PP_WHILE_333_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_334, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(334, s))
# define BOOST_PP_WHILE_334_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_335, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(335, s))
# define BOOST_PP_WHILE_335_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_336, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(336, s))
# define BOOST_PP_WHILE_336_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_337, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(337, s))
# define BOOST_PP_WHILE_337_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_338, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(338, s))
# define BOOST_PP_WHILE_338_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_339, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(339, s))
# define BOOST_PP_WHILE_339_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_340, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(340, s))
# define BOOST_PP_WHILE_340_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_341, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(341, s))
# define BOOST_PP_WHILE_341_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_342, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(342, s))
# define BOOST_PP_WHILE_342_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_343, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(343, s))
# define BOOST_PP_WHILE_343_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_344, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(344, s))
# define BOOST_PP_WHILE_344_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_345, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(345, s))
# define BOOST_PP_WHILE_345_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_346, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(346, s))
# define BOOST_PP_WHILE_346_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_347, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(347, s))
# define BOOST_PP_WHILE_347_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_348, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(348, s))
# define BOOST_PP_WHILE_348_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_349, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(349, s))
# define BOOST_PP_WHILE_349_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_350, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(350, s))
# define BOOST_PP_WHILE_350_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_351, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(351, s))
# define BOOST_PP_WHILE_351_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_352, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(352, s))
# define BOOST_PP_WHILE_352_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_353, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(353, s))
# define BOOST_PP_WHILE_353_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_354, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(354, s))
# define BOOST_PP_WHILE_354_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_355, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(355, s))
# define BOOST_PP_WHILE_355_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_356, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(356, s))
# define BOOST_PP_WHILE_356_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_357, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(357, s))
# define BOOST_PP_WHILE_357_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_358, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(358, s))
# define BOOST_PP_WHILE_358_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_359, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(359, s))
# define BOOST_PP_WHILE_359_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_360, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(360, s))
# define BOOST_PP_WHILE_360_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_361, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(361, s))
# define BOOST_PP_WHILE_361_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_362, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(362, s))
# define BOOST_PP_WHILE_362_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_363, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(363, s))
# define BOOST_PP_WHILE_363_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_364, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(364, s))
# define BOOST_PP_WHILE_364_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_365, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(365, s))
# define BOOST_PP_WHILE_365_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_366, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(366, s))
# define BOOST_PP_WHILE_366_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_367, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(367, s))
# define BOOST_PP_WHILE_367_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_368, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(368, s))
# define BOOST_PP_WHILE_368_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_369, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(369, s))
# define BOOST_PP_WHILE_369_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_370, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(370, s))
# define BOOST_PP_WHILE_370_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_371, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(371, s))
# define BOOST_PP_WHILE_371_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_372, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(372, s))
# define BOOST_PP_WHILE_372_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_373, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(373, s))
# define BOOST_PP_WHILE_373_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_374, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(374, s))
# define BOOST_PP_WHILE_374_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_375, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(375, s))
# define BOOST_PP_WHILE_375_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_376, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(376, s))
# define BOOST_PP_WHILE_376_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_377, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(377, s))
# define BOOST_PP_WHILE_377_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_378, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(378, s))
# define BOOST_PP_WHILE_378_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_379, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(379, s))
# define BOOST_PP_WHILE_379_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_380, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(380, s))
# define BOOST_PP_WHILE_380_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_381, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(381, s))
# define BOOST_PP_WHILE_381_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_382, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(382, s))
# define BOOST_PP_WHILE_382_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_383, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(383, s))
# define BOOST_PP_WHILE_383_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_384, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(384, s))
# define BOOST_PP_WHILE_384_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_385, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(385, s))
# define BOOST_PP_WHILE_385_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_386, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(386, s))
# define BOOST_PP_WHILE_386_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_387, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(387, s))
# define BOOST_PP_WHILE_387_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_388, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(388, s))
# define BOOST_PP_WHILE_388_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_389, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(389, s))
# define BOOST_PP_WHILE_389_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_390, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(390, s))
# define BOOST_PP_WHILE_390_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_391, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(391, s))
# define BOOST_PP_WHILE_391_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_392, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(392, s))
# define BOOST_PP_WHILE_392_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_393, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(393, s))
# define BOOST_PP_WHILE_393_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_394, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(394, s))
# define BOOST_PP_WHILE_394_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_395, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(395, s))
# define BOOST_PP_WHILE_395_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_396, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(396, s))
# define BOOST_PP_WHILE_396_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_397, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(397, s))
# define BOOST_PP_WHILE_397_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_398, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(398, s))
# define BOOST_PP_WHILE_398_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_399, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(399, s))
# define BOOST_PP_WHILE_399_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_400, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(400, s))
# define BOOST_PP_WHILE_400_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_401, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(401, s))
# define BOOST_PP_WHILE_401_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_402, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(402, s))
# define BOOST_PP_WHILE_402_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_403, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(403, s))
# define BOOST_PP_WHILE_403_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_404, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(404, s))
# define BOOST_PP_WHILE_404_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_405, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(405, s))
# define BOOST_PP_WHILE_405_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_406, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(406, s))
# define BOOST_PP_WHILE_406_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_407, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(407, s))
# define BOOST_PP_WHILE_407_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_408, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(408, s))
# define BOOST_PP_WHILE_408_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_409, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(409, s))
# define BOOST_PP_WHILE_409_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_410, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(410, s))
# define BOOST_PP_WHILE_410_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_411, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(411, s))
# define BOOST_PP_WHILE_411_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_412, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(412, s))
# define BOOST_PP_WHILE_412_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_413, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(413, s))
# define BOOST_PP_WHILE_413_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_414, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(414, s))
# define BOOST_PP_WHILE_414_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_415, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(415, s))
# define BOOST_PP_WHILE_415_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_416, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(416, s))
# define BOOST_PP_WHILE_416_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_417, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(417, s))
# define BOOST_PP_WHILE_417_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_418, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(418, s))
# define BOOST_PP_WHILE_418_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_419, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(419, s))
# define BOOST_PP_WHILE_419_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_420, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(420, s))
# define BOOST_PP_WHILE_420_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_421, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(421, s))
# define BOOST_PP_WHILE_421_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_422, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(422, s))
# define BOOST_PP_WHILE_422_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_423, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(423, s))
# define BOOST_PP_WHILE_423_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_424, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(424, s))
# define BOOST_PP_WHILE_424_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_425, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(425, s))
# define BOOST_PP_WHILE_425_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_426, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(426, s))
# define BOOST_PP_WHILE_426_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_427, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(427, s))
# define BOOST_PP_WHILE_427_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_428, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(428, s))
# define BOOST_PP_WHILE_428_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_429, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(429, s))
# define BOOST_PP_WHILE_429_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_430, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(430, s))
# define BOOST_PP_WHILE_430_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_431, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(431, s))
# define BOOST_PP_WHILE_431_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_432, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(432, s))
# define BOOST_PP_WHILE_432_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_433, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(433, s))
# define BOOST_PP_WHILE_433_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_434, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(434, s))
# define BOOST_PP_WHILE_434_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_435, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(435, s))
# define BOOST_PP_WHILE_435_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_436, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(436, s))
# define BOOST_PP_WHILE_436_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_437, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(437, s))
# define BOOST_PP_WHILE_437_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_438, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(438, s))
# define BOOST_PP_WHILE_438_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_439, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(439, s))
# define BOOST_PP_WHILE_439_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_440, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(440, s))
# define BOOST_PP_WHILE_440_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_441, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(441, s))
# define BOOST_PP_WHILE_441_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_442, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(442, s))
# define BOOST_PP_WHILE_442_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_443, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(443, s))
# define BOOST_PP_WHILE_443_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_444, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(444, s))
# define BOOST_PP_WHILE_444_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_445, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(445, s))
# define BOOST_PP_WHILE_445_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_446, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(446, s))
# define BOOST_PP_WHILE_446_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_447, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(447, s))
# define BOOST_PP_WHILE_447_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_448, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(448, s))
# define BOOST_PP_WHILE_448_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_449, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(449, s))
# define BOOST_PP_WHILE_449_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_450, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(450, s))
# define BOOST_PP_WHILE_450_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_451, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(451, s))
# define BOOST_PP_WHILE_451_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_452, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(452, s))
# define BOOST_PP_WHILE_452_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_453, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(453, s))
# define BOOST_PP_WHILE_453_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_454, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(454, s))
# define BOOST_PP_WHILE_454_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_455, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(455, s))
# define BOOST_PP_WHILE_455_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_456, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(456, s))
# define BOOST_PP_WHILE_456_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_457, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(457, s))
# define BOOST_PP_WHILE_457_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_458, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(458, s))
# define BOOST_PP_WHILE_458_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_459, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(459, s))
# define BOOST_PP_WHILE_459_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_460, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(460, s))
# define BOOST_PP_WHILE_460_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_461, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(461, s))
# define BOOST_PP_WHILE_461_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_462, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(462, s))
# define BOOST_PP_WHILE_462_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_463, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(463, s))
# define BOOST_PP_WHILE_463_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_464, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(464, s))
# define BOOST_PP_WHILE_464_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_465, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(465, s))
# define BOOST_PP_WHILE_465_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_466, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(466, s))
# define BOOST_PP_WHILE_466_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_467, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(467, s))
# define BOOST_PP_WHILE_467_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_468, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(468, s))
# define BOOST_PP_WHILE_468_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_469, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(469, s))
# define BOOST_PP_WHILE_469_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_470, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(470, s))
# define BOOST_PP_WHILE_470_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_471, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(471, s))
# define BOOST_PP_WHILE_471_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_472, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(472, s))
# define BOOST_PP_WHILE_472_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_473, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(473, s))
# define BOOST_PP_WHILE_473_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_474, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(474, s))
# define BOOST_PP_WHILE_474_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_475, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(475, s))
# define BOOST_PP_WHILE_475_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_476, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(476, s))
# define BOOST_PP_WHILE_476_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_477, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(477, s))
# define BOOST_PP_WHILE_477_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_478, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(478, s))
# define BOOST_PP_WHILE_478_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_479, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(479, s))
# define BOOST_PP_WHILE_479_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_480, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(480, s))
# define BOOST_PP_WHILE_480_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_481, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(481, s))
# define BOOST_PP_WHILE_481_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_482, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(482, s))
# define BOOST_PP_WHILE_482_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_483, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(483, s))
# define BOOST_PP_WHILE_483_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_484, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(484, s))
# define BOOST_PP_WHILE_484_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_485, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(485, s))
# define BOOST_PP_WHILE_485_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_486, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(486, s))
# define BOOST_PP_WHILE_486_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_487, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(487, s))
# define BOOST_PP_WHILE_487_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_488, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(488, s))
# define BOOST_PP_WHILE_488_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_489, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(489, s))
# define BOOST_PP_WHILE_489_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_490, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(490, s))
# define BOOST_PP_WHILE_490_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_491, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(491, s))
# define BOOST_PP_WHILE_491_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_492, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(492, s))
# define BOOST_PP_WHILE_492_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_493, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(493, s))
# define BOOST_PP_WHILE_493_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_494, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(494, s))
# define BOOST_PP_WHILE_494_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_495, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(495, s))
# define BOOST_PP_WHILE_495_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_496, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(496, s))
# define BOOST_PP_WHILE_496_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_497, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(497, s))
# define BOOST_PP_WHILE_497_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_498, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(498, s))
# define BOOST_PP_WHILE_498_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_499, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(499, s))
# define BOOST_PP_WHILE_499_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_500, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(500, s))
# define BOOST_PP_WHILE_500_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_501, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(501, s))
# define BOOST_PP_WHILE_501_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_502, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(502, s))
# define BOOST_PP_WHILE_502_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_503, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(503, s))
# define BOOST_PP_WHILE_503_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_504, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(504, s))
# define BOOST_PP_WHILE_504_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_505, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(505, s))
# define BOOST_PP_WHILE_505_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_506, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(506, s))
# define BOOST_PP_WHILE_506_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_507, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(507, s))
# define BOOST_PP_WHILE_507_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_508, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(508, s))
# define BOOST_PP_WHILE_508_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_509, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(509, s))
# define BOOST_PP_WHILE_509_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_510, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(510, s))
# define BOOST_PP_WHILE_510_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_511, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(511, s))
# define BOOST_PP_WHILE_511_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_512, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(512, s))
# define BOOST_PP_WHILE_512_C(c, p, o, s) BOOST_PP_IIF(c, BOOST_PP_WHILE_513, s BOOST_PP_TUPLE_EAT_3)(p, o, BOOST_PP_IIF(c, o, BOOST_PP_NIL BOOST_PP_TUPLE_EAT_2)(513, s))
#
# endif
