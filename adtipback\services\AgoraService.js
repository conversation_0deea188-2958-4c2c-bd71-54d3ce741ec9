const { Rtc<PERSON><PERSON>Builder,RtmTokenBuilder , RtcRole } = require("agora-token");

// Generate random string for channel name
const generateRandomChannelName = () => {
  return 'channel_' + Math.random().toString(36).substr(2, 10);
};

const getAgoraTokenforCaller = async (uid) => {
  try {
    // Get environment variables
    const appId = process.env.AGORA_APP_ID;
    const appCertificate = process.env.AGORA_APP_CERTIFICATE;
    
    // Validate environment variables
    if (!appId || !appCertificate) {
      throw new Error("AGORA_APP_ID and AGORA_APP_CERTIFICATE must be set in environment variables");
    }

    // Configuration
    const channelName = generateRandomChannelName();
    const role = RtcRole.PUBLISHER;
    const tokenExpirationInSecond = 3600;
    const privilegeExpirationInSecond = 3600;

    // Generate token
    const token = RtcTokenBuilder.buildTokenWithUid(
      appId,
      appCertificate,
      channelName,
      uid,
      role,
      tokenExpirationInSecond,
      privilegeExpirationInSecond
    );

    return {
      token,
      channelName,
      uid,
      expiresAt: new Date(Date.now() + tokenExpirationInSecond * 1000).toISOString()
    };
  } catch (error) {
    console.error("Error generating Agora token:", error.message);
    throw error;
  }
};

const getAgoraTokenForCallee = async (uid, channelName) => {
  try {
    // Get environment variables
    const appId = process.env.AGORA_APP_ID;
    const appCertificate = process.env.AGORA_APP_CERTIFICATE;
    
    // Validate environment variables
    if (!appId || !appCertificate) {
      throw new Error("AGORA_APP_ID and AGORA_APP_CERTIFICATE must be set in environment variables");
    }

    // Configuration
    const role = RtcRole.SUBSCRIBER; // Using SUBSCRIBER role for callee
    const tokenExpirationInSecond = 3600;
    const privilegeExpirationInSecond = 3600;

    // Generate token
    const token = RtcTokenBuilder.buildTokenWithUid(
      appId,
      appCertificate,
      channelName,
      uid,
      role,
      tokenExpirationInSecond,
      privilegeExpirationInSecond
    );

    return {
      token,
      channelName,
      uid,
      expiresAt: new Date(Date.now() + tokenExpirationInSecond * 1000).toISOString()
    };
  } catch (error) {
    console.error("Error generating Agora token for callee:", error.message);
    throw error;
  }
};

const getAgoraRtmToken = async (userId) => {
  try {
    // Get environment variables
    const appId = process.env.AGORA_APP_ID;
    const appCertificate = process.env.AGORA_APP_CERTIFICATE;

    // Validate environment variables
    if (!appId || !appCertificate) {
      throw new Error("AGORA_APP_ID and AGORA_APP_CERTIFICATE must be set in environment variables");
    }
    if (!userId || typeof userId !== 'string') {
      // RTM user IDs are typically strings. If you receive a number, convert it.
      // For consistency with Agora's RTM SDK, ensure it's a string.
      throw new Error("Valid string User ID (userId) is required for RTM token generation");
    }

    // Configuration for RTM token
    const tokenExpirationInSeconds = 3600; // e.g., 1 hour
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const privilegeExpiredTs = currentTimestamp + tokenExpirationInSeconds;

    console.log("[RTM Token Gen Service] Generating RTM token for UID: ${userId}, App ID: ${appId}");
    // Generate RTM token
    // The RtmTokenBuilder.buildToken method is used for RTM.
    // The 'role' parameter is not explicitly passed to RtmTokenBuilder like in RTC,
    // as RTM token generation is simpler and doesn't have the same publisher/subscriber roles.
    const token = RtmTokenBuilder.buildToken(
      appId,
      appCertificate,
      userId, // User ID must be a string
      privilegeExpiredTs // Expiration timestamp
    );
    console.log("[RTM Token Gen Service] Successfully generated RTM token for UID: ${userId}");

    return {
      token: token,
      userId: userId, // Return the userId used for token generation
      // expiresAt: new Date(Date.now() + tokenExpirationInSeconds * 1000).toISOString() // Optional: if client needs it
    };
  } catch (error) {
    console.error("Error generating Agora RTM token:", error.message);
    throw error; // Re-throw the error to be caught by the route handler
  }
};

//module.exports = { getAgoraRtmToken };

module.exports = { getAgoraTokenforCaller , getAgoraRtmToken, getAgoraTokenForCallee };