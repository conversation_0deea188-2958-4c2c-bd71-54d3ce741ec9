
import React from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Mail, Phone, MapPin, Clock } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

const ContactUs = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleEmailSupport = () => {
    window.location.href = "mailto:<EMAIL>";
  };

  return (
    <div className="pb-20 md:pb-0 bg-white min-h-screen">

      <div className="max-w-md mx-auto p-4">
        {/* Hero section */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">We're here to help</h2>
          <p className="text-gray-600">
            Get in touch with our support team through any of these channels
          </p>
        </div>

        {/* Contact Options */}
        <div className="space-y-4">
          {/* Call Us */}

          {/* Email Us */}
          <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="bg-blue-100 p-3 rounded-full mr-3">
                <Mail className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-800">Email Us</h3>
                <p className="text-sm text-gray-600">Send us your questions</p>
              </div>
            </div>
            <Button 
              className="w-full bg-blue-600 hover:bg-blue-700"
              onClick={handleEmailSupport}
            >
              Email <EMAIL>
            </Button>
          </div>

          {/* Business Hours */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Clock className="h-5 w-5 text-gray-600 mr-3" />
              <h3 className="font-medium text-gray-800">Business Hours</h3>
            </div>
            <p className="text-sm text-gray-600 ml-8">Monday to Friday: 9 AM - 6 PM IST</p>
            <p className="text-sm text-gray-600 ml-8">Saturday: 10 AM - 2 PM IST</p>
            <p className="text-sm text-gray-600 ml-8">Sunday: Closed</p>
          </div>
          
          {/* Location */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <MapPin className="h-5 w-5 text-gray-600 mr-3" />
              <h3 className="font-medium text-gray-800">Office Address</h3>
            </div>
            <p className="text-sm text-gray-600 ml-8">
              AdTip Headquarters<br/>
              Vishakapatnam, Andhra Pradesh 531002<br/>
              India
            </p>
          </div>
        </div>
        
        {/* FAQs Link */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600 mb-2">Looking for quick answers?</p>
          <Button 
            variant="outline" 
            className="text-teal-600 border-teal-300"
            onClick={() => navigate("/how-to-earn-user")}
          >
            Check our FAQs
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ContactUs;
