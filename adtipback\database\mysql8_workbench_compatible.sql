-- =====================================================
-- MYSQL 8 WORKBENCH COMPATIBLE FRESH CHAT DEPLOYMENT
-- Fixed all syntax issues for MySQL 8 Workbench execution
-- =====================================================

-- =====================================================
-- STEP 1: DISABLE FOREIGN KEY CHECKS
-- =====================================================
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';

-- =====================================================
-- STEP 2: DROP ALL EXISTING CHAT-RELATED TABLES
-- =====================================================

DROP TABLE IF EXISTS `message_status`;
DROP TABLE IF EXISTS `message_delivery_failures`;
DROP TABLE IF EXISTS `message_delivery_stats`;
DROP TABLE IF EXISTS `messages`;
DROP TABLE IF EXISTS `conversation_participants`;
DROP TABLE IF EXISTS `conversations`;
DROP TABLE IF EXISTS `user_chat`;
DROP TABLE IF EXISTS `user_chat_details`;
DROP TABLE IF EXISTS `user_chat_backup_1752766664455`;
DROP TABLE IF EXISTS `user_chat_backup_1752766739714`;
DROP TABLE IF EXISTS `user_chat_backup_1752766853519`;
DROP TABLE IF EXISTS `chat_settings`;
DROP TABLE IF EXISTS `messages_local_sync`;
DROP TABLE IF EXISTS `user_chat_metadata`;
DROP TABLE IF EXISTS `message_delivery_status`;
-- Drop existing objects
DROP TRIGGER IF EXISTS `update_user_chat_metadata_on_message`;
DROP TRIGGER IF EXISTS `create_delivery_status_on_message`;
DROP FUNCTION IF EXISTS `GenerateChatId`;
DROP PROCEDURE IF EXISTS `MarkMessagesAsRead`;
DROP PROCEDURE IF EXISTS `GetChatMessages`;
DROP VIEW IF EXISTS `user_chat_list`;
DROP VIEW IF EXISTS `message_details`;

SELECT 'All existing chat tables and objects dropped successfully' as status;

-- =====================================================
-- STEP 3: CREATE NEW MESSAGES TABLE
-- =====================================================

CREATE TABLE `messages` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `chat_id` VARCHAR(255) NOT NULL COMMENT 'Format: chat_userId1_userId2 (sorted)',
    `sender_id` INT NOT NULL COMMENT 'User who sent the message',
    `recipient_id` INT NOT NULL COMMENT 'User who receives the message',
    `sender_name` VARCHAR(255) NOT NULL COMMENT 'Sender display name',
    `sender_avatar` VARCHAR(500) NULL COMMENT 'Sender profile image URL',
    
    `content` TEXT NOT NULL COMMENT 'Message text content',
    `message_type` ENUM('text', 'image', 'video', 'audio', 'file', 'system') DEFAULT 'text',
    
    `file_url` VARCHAR(500) NULL COMMENT 'URL for media/file messages',
    `file_size` INT NULL COMMENT 'File size in bytes',
    `file_name` VARCHAR(255) NULL COMMENT 'Original file name',
    `file_mime_type` VARCHAR(100) NULL COMMENT 'MIME type of the file',
    `thumbnail_url` VARCHAR(500) NULL COMMENT 'Thumbnail URL for images/videos',
    
    `reply_to_message_id` INT NULL COMMENT 'ID of message being replied to',
    
    `external_id` VARCHAR(255) NULL COMMENT 'External ID from FCM or other systems',
    `temp_id` VARCHAR(255) NULL COMMENT 'Temporary ID from client for sync tracking',
    `fcm_message_id` VARCHAR(255) NULL COMMENT 'FCM message ID for tracking',
    
    `status` ENUM('sending', 'sent', 'delivered', 'read', 'failed') DEFAULT 'sent',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    `is_deleted` BOOLEAN DEFAULT FALSE,
    `deleted_at` TIMESTAMP NULL,
    `deleted_by` INT NULL COMMENT 'User who deleted the message',
    
    `is_edited` BOOLEAN DEFAULT FALSE,
    `edited_at` TIMESTAMP NULL,
    `original_content` TEXT NULL COMMENT 'Original content before editing'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add indexes separately to avoid syntax issues
ALTER TABLE `messages` 
ADD INDEX `idx_chat_id_created` (`chat_id`, `created_at` DESC),
ADD INDEX `idx_sender_recipient` (`sender_id`, `recipient_id`),
ADD INDEX `idx_sender_created` (`sender_id`, `created_at` DESC),
ADD INDEX `idx_recipient_created` (`recipient_id`, `created_at` DESC),
ADD INDEX `idx_external_id` (`external_id`),
ADD INDEX `idx_temp_id` (`temp_id`),
ADD INDEX `idx_fcm_message_id` (`fcm_message_id`),
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_deleted` (`is_deleted`, `deleted_at`),
ADD INDEX `idx_reply_to` (`reply_to_message_id`);

-- Add foreign keys separately
ALTER TABLE `messages`
ADD CONSTRAINT `fk_messages_sender` FOREIGN KEY (`sender_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_messages_recipient` FOREIGN KEY (`recipient_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_messages_reply_to` FOREIGN KEY (`reply_to_message_id`) REFERENCES `messages`(`id`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_messages_deleted_by` FOREIGN KEY (`deleted_by`) REFERENCES `users`(`id`) ON DELETE SET NULL;

SELECT 'Messages table created successfully' as status;

-- =====================================================
-- STEP 4: CREATE MESSAGES LOCAL SYNC TABLE
-- =====================================================

CREATE TABLE `messages_local_sync` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `message_id` INT NOT NULL COMMENT 'Reference to messages table',
    `chat_id` VARCHAR(255) NOT NULL COMMENT 'Chat ID for grouping',
    `local_message_id` VARCHAR(255) NULL COMMENT 'Local/temp message ID from client',
    `local_timestamp` TIMESTAMP NOT NULL COMMENT 'Timestamp when message was created locally',
    `server_timestamp` TIMESTAMP NULL COMMENT 'Timestamp when message was synced to server',
    `sync_status` ENUM('pending', 'synced', 'conflict', 'failed') DEFAULT 'pending',
    `sync_attempts` INT DEFAULT 0 COMMENT 'Number of sync attempts',
    `last_sync_attempt` TIMESTAMP NULL COMMENT 'Last time sync was attempted',
    `error_message` TEXT NULL COMMENT 'Error details if sync failed',
    `client_version` VARCHAR(50) NULL COMMENT 'Client app version for debugging'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE `messages_local_sync`
ADD INDEX `idx_message_id` (`message_id`),
ADD INDEX `idx_chat_id` (`chat_id`),
ADD INDEX `idx_local_message_id` (`local_message_id`),
ADD INDEX `idx_sync_status` (`sync_status`),
ADD INDEX `idx_sync_attempts` (`sync_attempts`),
ADD INDEX `idx_last_sync_attempt` (`last_sync_attempt`);

ALTER TABLE `messages_local_sync`
ADD CONSTRAINT `fk_sync_message` FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`) ON DELETE CASCADE;

SELECT 'Messages local sync table created successfully' as status;

-- =====================================================
-- STEP 5: CREATE USER CHAT METADATA TABLE
-- =====================================================

CREATE TABLE `user_chat_metadata` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `chat_id` VARCHAR(255) NOT NULL COMMENT 'Format: chat_userId1_userId2',
    `other_user_id` INT NOT NULL COMMENT 'The other participant in the chat',
    `other_user_name` VARCHAR(255) NOT NULL COMMENT 'Display name of other user',
    `other_user_avatar` VARCHAR(500) NULL COMMENT 'Profile image of other user',
    
    `unread_count` INT DEFAULT 0,
    `last_read_message_id` INT NULL,
    `last_message_id` INT NULL,
    `last_message_content` TEXT NULL,
    `last_message_time` TIMESTAMP NULL,
    
    `is_muted` BOOLEAN DEFAULT FALSE,
    `is_blocked` BOOLEAN DEFAULT FALSE,
    `is_archived` BOOLEAN DEFAULT FALSE,
    `is_pinned` BOOLEAN DEFAULT FALSE,
    
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `last_activity_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE `user_chat_metadata`
ADD UNIQUE KEY `unique_user_chat` (`user_id`, `chat_id`),
ADD INDEX `idx_user_id` (`user_id`),
ADD INDEX `idx_chat_id` (`chat_id`),
ADD INDEX `idx_other_user_id` (`other_user_id`),
ADD INDEX `idx_last_activity` (`last_activity_at` DESC),
ADD INDEX `idx_unread_count` (`unread_count`),
ADD INDEX `idx_user_activity` (`user_id`, `last_activity_at` DESC);

ALTER TABLE `user_chat_metadata`
ADD CONSTRAINT `fk_chat_meta_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_chat_meta_other_user` FOREIGN KEY (`other_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_chat_meta_last_read` FOREIGN KEY (`last_read_message_id`) REFERENCES `messages`(`id`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_chat_meta_last_message` FOREIGN KEY (`last_message_id`) REFERENCES `messages`(`id`) ON DELETE SET NULL;

SELECT 'User chat metadata table created successfully' as status;

-- =====================================================
-- STEP 6: CREATE MESSAGE DELIVERY STATUS TABLE
-- =====================================================

CREATE TABLE `message_delivery_status` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `message_id` INT NOT NULL,
    `recipient_id` INT NOT NULL,
    `status` ENUM('sent', 'delivered', 'read') NOT NULL,
    `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `fcm_message_id` VARCHAR(255) NULL COMMENT 'FCM message ID for tracking',
    `device_info` TEXT NULL COMMENT 'Device information for debugging'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

ALTER TABLE `message_delivery_status`
ADD UNIQUE KEY `unique_message_recipient_status` (`message_id`, `recipient_id`, `status`),
ADD INDEX `idx_message_id` (`message_id`),
ADD INDEX `idx_recipient_id` (`recipient_id`),
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_timestamp` (`timestamp`),
ADD INDEX `idx_fcm_message_id` (`fcm_message_id`);

ALTER TABLE `message_delivery_status`
ADD CONSTRAINT `fk_delivery_message` FOREIGN KEY (`message_id`) REFERENCES `messages`(`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_delivery_recipient` FOREIGN KEY (`recipient_id`) REFERENCES `users`(`id`) ON DELETE CASCADE;

SELECT 'Message delivery status table created successfully' as status;

-- =====================================================
-- STEP 7: CREATE HELPER FUNCTION (MySQL 8 Compatible)
-- =====================================================

DELIMITER $$
CREATE FUNCTION GenerateChatId(user_id_1 INT, user_id_2 INT)
RETURNS VARCHAR(255)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE chat_id VARCHAR(255);

    IF user_id_1 < user_id_2 THEN
        SET chat_id = CONCAT('chat_', user_id_1, '_', user_id_2);
    ELSE
        SET chat_id = CONCAT('chat_', user_id_2, '_', user_id_1);
    END IF;

    RETURN chat_id;
END$$
DELIMITER ;

SELECT 'GenerateChatId function created successfully' as status;

-- =====================================================
-- STEP 8: CREATE STORED PROCEDURES (MySQL 8 Compatible)
-- =====================================================

DELIMITER $$
CREATE PROCEDURE MarkMessagesAsRead(
    IN p_user_id INT,
    IN p_chat_id VARCHAR(255),
    IN p_last_read_message_id INT
)
BEGIN
    UPDATE user_chat_metadata
    SET unread_count = 0,
        last_read_message_id = p_last_read_message_id,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id AND chat_id = p_chat_id;

    INSERT INTO message_delivery_status (message_id, recipient_id, status)
    SELECT m.id, p_user_id, 'read'
    FROM messages m
    WHERE m.chat_id = p_chat_id
      AND m.recipient_id = p_user_id
      AND m.id <= p_last_read_message_id
      AND NOT EXISTS (
          SELECT 1 FROM message_delivery_status mds
          WHERE mds.message_id = m.id
            AND mds.recipient_id = p_user_id
            AND mds.status = 'read'
      );
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE GetChatMessages(
    IN p_chat_id VARCHAR(255),
    IN p_limit INT,
    IN p_offset INT,
    IN p_before_message_id INT
)
BEGIN
    SET p_limit = IFNULL(p_limit, 50);
    SET p_offset = IFNULL(p_offset, 0);

    SELECT
        m.*,
        mds.status as delivery_status,
        mds.timestamp as delivery_timestamp
    FROM messages m
    LEFT JOIN message_delivery_status mds ON m.id = mds.message_id
    WHERE m.chat_id = p_chat_id
      AND m.is_deleted = FALSE
      AND (p_before_message_id IS NULL OR m.id < p_before_message_id)
    ORDER BY m.created_at DESC, m.id DESC
    LIMIT p_limit OFFSET p_offset;
END$$
DELIMITER ;

SELECT 'Stored procedures created successfully' as status;

-- =====================================================
-- STEP 9: CREATE TRIGGERS (MySQL 8 Compatible)
-- =====================================================

DELIMITER $$
CREATE TRIGGER update_user_chat_metadata_on_message
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    DECLARE sender_display_name VARCHAR(255) DEFAULT 'Unknown User';
    DECLARE recipient_display_name VARCHAR(255) DEFAULT 'Unknown User';

    SELECT COALESCE(name, username, 'Unknown User') INTO sender_display_name
    FROM users WHERE id = NEW.sender_id LIMIT 1;

    SELECT COALESCE(name, username, 'Unknown User') INTO recipient_display_name
    FROM users WHERE id = NEW.recipient_id LIMIT 1;

    INSERT INTO user_chat_metadata (
        user_id, chat_id, other_user_id, other_user_name,
        last_message_id, last_message_content, last_message_time, last_activity_at
    ) VALUES (
        NEW.sender_id, NEW.chat_id, NEW.recipient_id, recipient_display_name,
        NEW.id, NEW.content, NEW.created_at, NEW.created_at
    ) ON DUPLICATE KEY UPDATE
        last_message_id = NEW.id,
        last_message_content = NEW.content,
        last_message_time = NEW.created_at,
        last_activity_at = NEW.created_at,
        updated_at = CURRENT_TIMESTAMP;

    INSERT INTO user_chat_metadata (
        user_id, chat_id, other_user_id, other_user_name,
        unread_count, last_message_id, last_message_content, last_message_time, last_activity_at
    ) VALUES (
        NEW.recipient_id, NEW.chat_id, NEW.sender_id, sender_display_name,
        1, NEW.id, NEW.content, NEW.created_at, NEW.created_at
    ) ON DUPLICATE KEY UPDATE
        unread_count = unread_count + 1,
        last_message_id = NEW.id,
        last_message_content = NEW.content,
        last_message_time = NEW.created_at,
        last_activity_at = NEW.created_at,
        updated_at = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

DELIMITER $$
CREATE TRIGGER create_delivery_status_on_message
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    INSERT INTO message_delivery_status (message_id, recipient_id, status)
    VALUES (NEW.id, NEW.recipient_id, 'sent');
END$$
DELIMITER ;

SELECT 'Triggers created successfully' as status;

-- =====================================================
-- STEP 10: CREATE VIEWS (MySQL 8 Compatible)
-- =====================================================

CREATE VIEW user_chat_list AS
SELECT
    ucm.*,
    u.name as other_user_display_name,
    u.profile_image as other_user_profile_image,
    u.online_status as other_user_online_status,
    m.content as last_message_preview,
    m.message_type as last_message_type,
    m.created_at as last_message_timestamp
FROM user_chat_metadata ucm
JOIN users u ON ucm.other_user_id = u.id
LEFT JOIN messages m ON ucm.last_message_id = m.id
WHERE ucm.is_archived = FALSE
ORDER BY ucm.last_activity_at DESC;

CREATE VIEW message_details AS
SELECT
    m.*,
    sender.name as sender_display_name,
    sender.profile_image as sender_profile_image,
    recipient.name as recipient_display_name,
    recipient.profile_image as recipient_profile_image,
    reply_msg.content as reply_to_content,
    reply_sender.name as reply_to_sender_name
FROM messages m
JOIN users sender ON m.sender_id = sender.id
JOIN users recipient ON m.recipient_id = recipient.id
LEFT JOIN messages reply_msg ON m.reply_to_message_id = reply_msg.id
LEFT JOIN users reply_sender ON reply_msg.sender_id = reply_sender.id
WHERE m.is_deleted = FALSE;

SELECT 'Views created successfully' as status;

-- =====================================================
-- STEP 11: RE-ENABLE FOREIGN KEY CHECKS
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- STEP 12: VERIFICATION
-- =====================================================

SELECT '🎉 MYSQL 8 WORKBENCH COMPATIBLE DEPLOYMENT COMPLETED! 🎉' as final_status;

-- Test function
SELECT GenerateChatId(1, 2) as test_chat_id_1_2;
SELECT GenerateChatId(2, 1) as test_chat_id_2_1;

-- Show created objects
SHOW TABLES LIKE '%message%';
SHOW TABLES LIKE '%user_chat%';
SHOW FUNCTION STATUS WHERE Name = 'GenerateChatId';
SHOW PROCEDURE STATUS WHERE Name LIKE '%Message%';

SELECT 'All tables, functions, procedures, triggers, and views created successfully!' as success_message;
