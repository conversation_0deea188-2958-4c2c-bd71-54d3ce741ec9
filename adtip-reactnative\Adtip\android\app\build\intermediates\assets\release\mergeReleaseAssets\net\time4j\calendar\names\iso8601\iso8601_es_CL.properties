# months
M(A)_1=ene.
M(A)_2=feb.
M(A)_3=mar.
M(A)_4=abr.
M(A)_5=may.
M(A)_6=jun.
M(A)_7=jul.
M(A)_8=ago.
M(A)_9=sept.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=dic.

# weekdays
D(s)_1=lu
D(s)_2=ma
D(s)_3=mi
D(s)_4=ju
D(s)_5=vi
D(s)_6=sá
D(s)_7=do

# quarters
Q(W)_1=1.° trimestre
Q(W)_2=2.° trimestre
Q(W)_3=3.° trimestre
Q(W)_4=4.º trimestre

# day-period-translations
P(a)_am=a. m.
P(a)_pm=p. m.

P(w)_am=a. m.
P(w)_pm=p. m.

P(A)_am=a. m.
P(A)_pm=p. m.

P(N)_am=a. m.
P(N)_pm=p. m.

P(W)_am=a. m.
P(W)_pm=p. m.

# format patterns
F(m)_d=dd-MM-y
F(s)_d=dd-MM-yy

F_Md=dd-MM
F_yM=MM-y

I={0} a el {1}

# labels of elements
L_dayperiod=a. m./p. m.
