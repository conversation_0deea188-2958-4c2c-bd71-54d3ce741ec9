const { queryRunner } = require('../dbConfig/queryRunner');
const cron = require('node-cron');

async function creditRemainingAdBudgets() {
  try {
    // 1. Find all eligible promoted posts
    const posts = await queryRunner(`
      SELECT id, user_id, remaining_budget
      FROM posts
      WHERE is_promoted = 1
        AND remaining_budget > 0
        AND adtip_wallet_credit_status = 0
        AND DATE_ADD(created_at, INTERVAL duration_days DAY) < NOW()
    `);

    if (!posts.length) {
      console.log(`[${new Date().toISOString()}] No eligible posts found for crediting.`);
      return;
    }

    for (const post of posts) {
      const { id: postId, user_id, remaining_budget } = post;
      // 2. Credit remaining_budget to post owner's wallet
      const walletRows = await queryRunner('SELECT id, totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1', [user_id]);
      let prevBalance = walletRows.length ? parseFloat(walletRows[0].totalBalance) : 0;
      const newWalletBalance = prevBalance + parseFloat(remaining_budget);
      if (walletRows.length) {
        await queryRunner('UPDATE wallet SET totalBalance = ? WHERE id = ?', [newWalletBalance, walletRows[0].id]);
      } else {
        await queryRunner('INSERT INTO wallet (createdby, totalBalance) VALUES (?, ?)', [user_id, newWalletBalance]);
      }
      // 3. Mark post as credited
      await queryRunner('UPDATE posts SET adtip_wallet_credit_status = 1 WHERE id = ?', [postId]);
      console.log(`[${new Date().toISOString()}] Credited ₹${remaining_budget} to user ${user_id} for post ${postId}`);
    }
    console.log(`[${new Date().toISOString()}] All eligible posts processed.`);
  } catch (err) {
    console.error(`[${new Date().toISOString()}] Error in creditRemainingAdBudgets:`, err);
  }
}

// Schedule the job to run every day at 2:00 AM
cron.schedule('0 2 * * *', () => {
  console.log(`[${new Date().toISOString()}] Starting scheduled creditRemainingAdBudgets job...`);
  creditRemainingAdBudgets();
});

// If run directly, execute once immediately
if (require.main === module) {
  creditRemainingAdBudgets();
} 