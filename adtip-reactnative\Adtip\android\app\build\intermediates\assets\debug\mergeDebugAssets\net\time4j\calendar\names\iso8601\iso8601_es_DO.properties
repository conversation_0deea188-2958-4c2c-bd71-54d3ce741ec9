
# weekdays
D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(A)_1=Q1
Q(A)_2=Q2
Q(A)_3=Q3
Q(A)_4=Q4

# day-period-translations
P(a)_am=a. m.
P(a)_pm=p. m.

P(n)_noon=mediodía
P(n)_morning1=día
P(n)_morning2=mañana
P(n)_evening1=tarde
P(n)_night1=noche

P(w)_am=a. m.
P(w)_pm=p. m.

P(A)_am=a. m.
P(A)_pm=p. m.

P(N)_am=a. m.
P(N)_noon=m.
P(N)_pm=p. m.

P(W)_am=a. m.
P(W)_pm=p. m.

# eras
E(w|alt)_0=antes de la Era Común
E(w|alt)_1=Era Común

# format patterns
F(alt)=hh:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a

# labels of elements
L_era=Era
L_year=Año
L_quarter=Trimestre
L_month=Mes
L_week=Semana
L_day=Día
L_weekday=Día de la semana
L_dayperiod=a. m./p. m.
L_minute=Minuto
L_second=Segundo
