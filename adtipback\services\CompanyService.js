let dbQuery = require("../dbConfig/queryRunner");
const utils = require("../utils/utils");
let appConfig = require("../config/appenvconfig.js");

let addCompanyGst = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `UPDATE company set company_name_in_gst="${data.company_name_in_gst}", gst_number="${data.gst_number}",gst_img="${data.gst_img}",bank_name="${data.bank_name}",account_name="${data.account_name}",account_number="${data.account_number}",ifsc_code="${data.ifsc_code}",pan_number="${data.pan_number}",pan_img="${data.pan_img}",is_verified=0 where id=${data.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "updated",
            data: data,
          });
        } else {
          reject({
            status: 400,
            message: "not updated",
            data: data,
          });
        }
      })
      .catch((er) => {
        reject({
          status: 500,
          message: `${er}`,
          data: [],
        });
      });
  });
};

let checkCompanyNameExist = (name) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT name From company where name="${name}"`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "company name already taken",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "company name not taken",
            data: [],
          });
        }
      })
      .catch((er) => {
        reject({
          status: 500,
          message: "Server Error",
          data: [],
        });
      });
  });
};

let getAllCompanyPosts = (userId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * From company_post where createdby=${userId} AND is_active=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Post  found",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Post not found",
            data: [],
          });
        }
      })
      .catch((er) => {
        reject({
          status: 500,
          message: "Server Error",
          data: [],
        });
      });
  });
};

let saveCompany = (companyData, companyFiles) =>
  new Promise((resolve, reject) => {
    let coverImage = null,
      profileImage = null,
      coverFilename = null,
      profileFilename;
    if (
      companyFiles &&
      companyFiles.coverImage &&
      companyFiles.coverImage.length > 0
    ) {
      coverImage = companyFiles.coverImage[0].originalname;
      coverFilename = companyFiles.coverImage[0].filename;
    }
    if (
      companyFiles &&
      companyFiles.profileImage &&
      companyFiles.profileImage.length > 0
    ) {
      profileImage = companyFiles.profileImage[0].originalname;
      profileFilename = companyFiles.profileImage[0].filename;
    }
    coverImage = coverImage == null ? null : `'${coverImage}'`;
    profileImage = profileImage == null ? null : `'${profileImage}'`;
    coverFilename = coverFilename == null ? null : `'${coverFilename}'`;
    profileFilename = profileFilename == null ? null : `'${profileFilename}'`;
    let sql = `INSERT INTO company (name, email, website, location,phone,industry,about,button,coverimage,profileimage,coverFilename,profileFilename, createdby,is_active,createddate)
     VALUES('${companyData.name}','${companyData.email}', '${companyData.website}', '${companyData.location}','${companyData.phone}','${companyData.industry}','${companyData.about}',
     '${companyData.button}',${coverimage},${profileimage},${coverFilename},${profileFilename},${companyData.createdby},1, NOW())`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          companyData.id = result.insertId;
          companyData.coverImage =
            coverImage != null
              ? companyFiles.coverImage[0].filename
              : coverImage;
          companyData.profileImage =
            profileImage != null
              ? companyFiles.profileImage[0].filename
              : profileFilename;
          companyData.coverFilename = coverFilename;
          companyData.profileFilename = profileFilename;

          // generate QR Code
          data = `http://${appConfig.application_url_host}:${appConfig.application_port}/api/getcompany/${result.insertId}/${companyData.createdby}`;
          qrcodename = `user_${companyData.id}_qrcode.png`;
          utils.generateQRCode(data, qrcodename);
          dbQuery.queryRunner(
            `update company set qr_code_image = '${qrcodename}' where id=${companyData.id};`
          );

          resolve({
            status: 200,
            message: "Save company successfully.",
            data: [companyData],
          });
        } else {
          reject({
            status: 400,
            message: "Company not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Company name already exists.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });
const updateCompanyNew = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `UPDATE company set name="${data.name}",email="${data.email}",website="${data.website}",location="${data.location}",about="${data.about}",profileimage="${data.profileimage}" where id=${data.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Company updatedd successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "unable to update",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};
const createCompany = (companyData) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO company (name, email, website, location, phone, industry, about, button, coverimage, profileimage, createdby, is_active, createddate) VALUES ('${companyData.name}','${companyData.email}','${companyData.website}','${companyData.location}','${companyData.phone}','${companyData.industry}','${companyData.about}','${companyData.button}','${companyData.coverimage}','${companyData.profileimage}',${companyData.createdby},1, NOW())`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.insertId) {
          companyData.id = result.insertId;

          resolve({
            status: 200,
            message: "Company saved successfully.",
            data: [companyData],
          });
        } else {
          reject({
            status: 400,
            message: "Failed to save company.",
            data: result,
          });
        }
      })
      .catch((err) => {
        // Handle errors
        let message = err.message || "Failed to save company.";
        reject({
          status: 500,
          message: message,
          data: [],
        });
      });
  });
};
let deletePost = (id) => {
  return new Promise((resolve, reject) => {
    // Add return here
    let sql = `DELETE from company_post where id=${id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.affectedRows > 0) {
          resolve({
            status: 200,
            message: "Delete post successfully.",
            data: [],
          });
        } else {
          reject({
            status: 404,
            message: "Post not found or not deleted.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = err.message || "Internal server error.";
        reject({
          status: 500,
          message: message,
          data: [],
        });
      });
  });
};

let savePost = (companyData) =>
  new Promise((resolve, reject) => {
    let sql = "";
    if (companyData.id && companyData.id > 0) {
      sql = `UPDATE company_post set PostName='${companyData.PostName}', 
        PostDiscription='${companyData.PostDescription}', 
        buttonid=${companyData.buttonid},
        website='${companyData.website}',
        image_path='${companyData.image_path}'
        WHERE id=${companyData.id} AND createdby=${companyData.createdby} AND company_id=${companyData.company_id}`;
    } else {
      sql = `INSERT INTO company_post (PostName, PostDiscription, buttonid, website,image_path, createdby, company_id, is_active, createddate)
        VALUES('${companyData.PostName}','${companyData.PostDescription}', ${companyData.buttonid}, '${companyData.website}','${companyData.image_path}','${companyData.createdby}','${companyData.company_id}',1, NOW())`;
    }
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          companyData.id = result.insertId;
          resolve({
            status: 200,
            message: "Save company Post successfully.",
            data: [companyData],
          });
        } else {
          reject({
            status: 400,
            message: "Company Post not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Company name already exists.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getCompanyList = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select * from company where createdby=${userId} and is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch data successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Data not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
let getPostDetails = (postId) => {
  return new Promise((resolve, reject) => {
    let sql = `select * from company_post where id=${postId} and is_active=1;`;
    // let sql1 = `select * from company where id=${companyId} and is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          var companyID = result[0]["company_id"];
          let sql1 = `select * from company where id=${companyID} and is_active=1;`;
          dbQuery.queryRunner(sql1).then((result1) => {
            if (result1 && result1.length != 0) {
              console.log("result 1", result1);
              resolve({
                status: 200,
                message: "Post  found",
                data: [
                  {
                    company_id: result[0]["company_id"],
                    company_name: result1[0]["name"],
                    company_about: result1[0]["about"],
                    company_profile: result1[0]["profileimage"],
                    post_id: postId,
                    post_name: result[0]["PostName"],
                    post_desc: result[0]["PostDiscription"],
                    post_image: result[0]["image_path"],
                  },
                ],
              });
            } else {
              reject({
                status: 400,
                message: "Post not found",
                data: [],
              });
            }
          });
        } else {
          reject({
            status: 400,
            message: "Post not found",
            data: [],
          });
        }
      })
      .catch((e) => {
        reject({
          status: 500,
          message: "Server error",
          data: [],
        });
      });
  });
};

let getCompanyPost = (companyId) =>
  new Promise((resolve, reject) => {
    let sql = `select * from company_post where company_id=${companyId} and is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch data successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Data not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getOtherCompanyList = (userId, isfollow) =>
  new Promise((resolve, reject) => {
    let subQuery = isfollow == 2 ? `` : ` AND cd.is_follow=${isfollow}`;

    if (isfollow == 2) {
      subQuery = ``;
    } else if (isfollow == 1) {
      subQuery = ` AND cd.is_follow=1`;
    } else {
      subQuery = ` AND cd.is_follow is null`;
    }

    let sql = `SELECT c.*,cd.company_id, cd.user_id,IFNULL(cd.is_follow,0) as is_follow FROM company c 
    LEFT JOIN company_details cd ON c.id=cd.company_id where c.is_active=1 and c.createdby!=${userId}  
    ${subQuery} 
    GROUP BY c.id order by c.createddate desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch data successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Data not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getAllCompanyList = () =>
  new Promise((resolve, reject) => {
    let sql = `SELECT c.*,cd.company_id, cd.user_id,IFNULL(cd.is_follow,0) as is_follow FROM company c LEFT JOIN company_details cd ON c.id=cd.company_id where c.is_active=1 GROUP BY c.id order by c.createddate desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch data successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Data not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getCompany = (id, userId) =>
  new Promise((resolve, reject) => {
    let sql = `select *,(select is_Follow from company_details where company_id=${id} and user_id=${userId}) as is_Follow  from company where id=${id} and is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch data successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Company not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
let updateCompany = (companyData) =>
  new Promise((resolve, reject) => {
    // let coverImage = null,
    //   profileImage = null,
    //   coverFilename = null,
    //   profileFilename;
    // if (
    //   companyFiles &&
    //   companyFiles.coverImage &&
    //   companyFiles.coverImage.length > 0
    // ) {
    //   coverImage = companyFiles.coverImage[0].originalname;
    //   coverFilename = companyFiles.coverImage[0].filename;
    // }
    // if (
    //   companyFiles &&
    //   companyFiles.profileImage &&
    //   companyFiles.profileImage.length > 0
    // ) {
    //   profileImage = companyFiles.profileImage[0].originalname;
    //   profileFilename = companyFiles.profileImage[0].filename;
    // }
    // coverImage = coverImage == null ? null : `'${coverImage}'`;
    // profileImage = profileImage == null ? null : `'${profileImage}'`;
    // coverFilename = coverFilename == null ? null : `'${coverFilename}'`;
    // profileFilename = profileFilename == null ? null : `'${profileFilename}'`;
    let sql = `update company set `;
    if (companyData.name) sql += `name='${companyData.name}',`;
    if (companyData.email) sql += `email='${companyData.email}',`;
    if (companyData.website) sql += ` website='${companyData.website}',`;
    if (companyData.location) sql += ` location='${companyData.location}',`;
    if (companyData.phone) sql += ` phone='${companyData.phone}',`;
    if (companyData.industry) sql += ` industry='${companyData.industry}',`;
    if (companyData.about) sql += ` about='${companyData.about}',`;

    if (companyData.button) sql += `button='${companyData.button}',`;
    if (companyData.coverImage)
      sql += `coverimage='${companyData.coverImage}',`;
    if (companyData.profileImage)
      sql += `profileimage='${companyData.profileImage}',`;
    // if (profileFilename) sql += ` profileFilename=${profileFilename},`;
    // if (coverFilename) sql += ` coverFilename=${coverFilename},`;
    sql += ` updatedate=NOW(),`;
    if (sql !== "") sql = sql.substring(0, sql.length - 1);
    sql += `  where id=${companyData.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          // companyData.coverImage = coverImage != null ? companyFiles.coverImage[0].filename : coverImage;
          // companyData.profileImage = profileImage != null ? companyFiles.profileImage[0].filename : profileFilename;
          // companyData.coverFilename = coverFilename;
          // companyData.profileFilename = profileFilename;
          resolve({
            status: 200,
            message: "Update company successfully.",
            data: [companyData],
          });
        } else {
          reject({
            status: 400,
            message: "company not update.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate company not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [companyData],
        });
      });
  });

let updateCompanyDetails = (companyData) =>
  new Promise((resolve, reject) => {
    let sql = "";
    if (companyData.isFollow) {
      sql += `INSERT INTO company_details (company_id,user_id,is_follow,createddate,company_follow_date) VALUES(${
        companyData.id
      },${companyData.userId},${
        companyData.isFollow
      },NOW(),NOW()) ON DUPLICATE KEY UPDATE is_follow = ${
        companyData.isFollow ? companyData.isFollow : 0
      },company_follow_date=NOW();`;
      if (companyData.isFollow === "1")
        dbQuery.queryRunner(
          `update company set followers = followers + 1 where id=${companyData.id};`
        );
      if (companyData.isFollow === "0")
        dbQuery.queryRunner(
          `update company set followers = followers - 1 where id=${companyData.id};`
        );
    }
    if (companyData.rating) {
      sql += `INSERT INTO company_details (company_id,user_id,rating_value,createddate) VALUES(${
        companyData.id
      },${companyData.userId},${
        companyData.rating
      },NOW()) ON DUPLICATE KEY UPDATE rating_value=${
        companyData.rating ? companyData.rating : 0
      };`;
      dbQuery.queryRunner(
        `update company set rating = rating + 1 , updatedate = NOW() where id=${companyData.id};`
      );
    }

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Update company successfully.",
            data: [companyData],
          });
        } else {
          reject({
            status: 400,
            message: "company not update.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate company not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [companyData],
        });
      });
  });

let deleteCompany = (id) =>
  new Promise((resolve, reject) => {
    let sql = `update company set is_active=0,updatedate=NOW() where id=${id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Delete company successfully.",
            data: [],
          });
        } else {
          reject({
            status: 400,
            message: "company not deleted.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate company not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

module.exports = {
  //checkCompanyNameExist
  //addCompanyGst
  //updateCompanyNew
  updateCompanyNew: (data) => {
    return new Promise((resolve, reject) => {
      updateCompanyNew(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  addCompanyGst: (data) => {
    return new Promise((resolve, reject) => {
      addCompanyGst(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  checkCompanyNameExist: (name) => {
    return new Promise((resolve, reject) => {
      checkCompanyNameExist(name)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  getPostDetails: (postId) => {
    return new Promise((resolve, reject) => {
      getPostDetails(postId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  getAllCompanyPosts: (userId) => {
    return new Promise((resolve, reject) => {
      getAllCompanyPosts(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  },

  getPremium: () => {
    return new Promise(async (resolve, reject) => {
      try {
        const query = `
        SELECT
          id AS PlanID,
          PlanName,
          AdRevenuePerView,
          PlatformChargePercentage,
          TipCallRate,
          MaxEarnings,
          WithdrawProcessingDays,
          TipCallAcceptReward,
          MinWithdrawAmount,
          Description 
      FROM subscription_premiums`;
        const rows = await dbQuery.queryRunner(query);
        resolve({
          status: 200,
          message: "fetch successfully premium",
          data: rows,
        });
      } catch (error) {
        console.log(error);
        reject({
          status: 400,
          message: "something went wrong",
          data: [],
        });
      }
    });
  },

  getPremiumPlans: () => {
    return new Promise(async (resolve, reject) => {
      try {
        const query = `SELECT * FROM subscription_plans WHERE is_active=true`;
        const rows = await dbQuery.queryRunner(query);
        resolve({
          status: 200,
          message: "fetch successfully premium",
          data: rows,
        });
      } catch (error) {
        console.log(error);
        reject({
          status: 400,
          message: "something went wrong",
          data: [],
        });
      }
    });
  },

  processPayment: (paymentDetails) => {
    return new Promise(async (resolve, reject) => {
      try {
        // Example check: Verify if the status of the payment exists
        if (!paymentDetails.status) {
          throw new Error("Status is missing");
        }

        // Additional logic, e.g., verifying payment with external API, updating DB, etc.
        if (paymentDetails.status === "success") {
          // You can add further steps like saving the payment status to a database
          resolve({ status: "success" });
        } else {
          resolve({ status: "failure" });
        }
      } catch (error) {
        reject(new Error("Payment processing failed: " + error.message));
      }
    });
  },

  deletePost: (id) => {
    return deletePost(id)
      .then((result) => {
        if (result && result.status == 200) {
          return result;
        } else {
          throw result;
        }
      })
      .catch((err) => {
        throw err;
      });
  },
  createCompany: (companyData) =>
    new Promise((resolve, reject) => {
      return createCompany(companyData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  saveCompany: (companyData, companyFiles) =>
    new Promise((resolve, reject) => {
      return saveCompany(companyData, companyFiles)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  savePost: (companyData) =>
    new Promise((resolve, reject) => {
      return savePost(companyData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getCompanyList: (userId) =>
    new Promise((resolve, reject) => {
      return getCompanyList(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getCompanyPost: (companyId) =>
    new Promise((resolve, reject) => {
      return getCompanyPost(companyId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getOtherCompanyList: (userId, isfollow) =>
    new Promise((resolve, reject) => {
      return getOtherCompanyList(userId, isfollow)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getAllCompanyList: () =>
    new Promise((resolve, reject) => {
      return getAllCompanyList()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getCompany: (id, userId) =>
    new Promise((resolve, reject) => {
      return getCompany(id, userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  updateCompany: (companyData) =>
    new Promise((resolve, reject) => {
      return updateCompany(companyData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  updateCompanyDetails: (companyData) =>
    new Promise((resolve, reject) => {
      return updateCompanyDetails(companyData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  deleteCompany: (id) =>
    new Promise((resolve, reject) => {
      return deleteCompany(id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getMediaFiles: async (file_type, user_id) => {
    try {
      let query = "SELECT * FROM media_files";
      let conditions = [];

      if (file_type) {
        conditions.push(`file_type = '${file_type}'`);
      }
      if (user_id) {
        conditions.push(`user_id = ${user_id}`);
      }

      if (conditions.length > 0) {
        query += " WHERE " + conditions.join(" AND ");
      }
      console.log("query", query);
      const results = await dbQuery.queryRunner(query);
      return results;
    } catch (error) {
      console.error("Error fetching media files:", error);
      throw new Error("Database query failed");
    }
  },
  getMediaFilesIndia: async (file_type, user_id) => {
    try {
      let query = "SELECT * FROM media_files_india";
      let conditions = [];

      if (file_type) {
        conditions.push(`file_type = '${file_type}'`);
      }
      if (user_id) {
        conditions.push(`user_id = ${user_id}`);
      }

      if (conditions.length > 0) {
        query += " WHERE " + conditions.join(" AND ");
      }
      console.log("query", query);
      const results = await dbQuery.queryRunner(query);
      return results;
    } catch (error) {
      console.error("Error fetching media files:", error);
      throw new Error("Database query failed");
    }
  },

  getMediaFilesIndiaTipCall: async (file_type, user_id) => {
    try {
      let query = "select * from media_files_india where id=1";
      let conditions = [];

      if (file_type) {
        conditions.push(`file_type = '${file_type}'`);  
      }
      if (user_id) {
        conditions.push(`user_    id = ${user_id}`);
      }

      if (conditions.length > 0) {
        query += " WHERE " + conditions.join(" AND ");
      }
      console.log("query", query);
      const results = await dbQuery.queryRunner(query);
      return results;
    } catch (error) {   
      console.error("Error fetching media files:", error);
      throw new Error("Database query failed");
    }
  },

  getAllCategories: async () => {
    try {
      const query =
        "SELECT id AS category_id, category_name FROM video_categories";
      const categories = await dbQuery.queryRunner(query);
      console.log("categories", categories);

      if (categories.length == 0) {
        return { error: "No categories found" };
      }
      return { status: 200, data: categories, message: "catgories list" };
    } catch (error) {
      console.error("Service Error:", error);
      return { error: "Failed to fetch categories" };
    }
  },
};
