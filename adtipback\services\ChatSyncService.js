/**
 * Chat Sync Service
 * 
 * Handles synchronization between frontend WatermelonDB and backend database.
 * Provides conflict resolution and data consistency for user-based messaging.
 */

const queryRunner = require('../dbConfig/queryRunner');
const UserBasedChatService = require('./UserBasedChatService');

class ChatSyncService {

  /**
   * Sync messages from client to server
   */
  static async syncMessagesFromClient(userId, messages) {
    try {
      console.log(`[ChatSyncService] Starting sync for user ${userId} with ${messages.length} messages`);

      const syncResults = [];
      let successCount = 0;
      let failureCount = 0;

      for (const messageData of messages) {
        try {
          const syncResult = await this.syncSingleMessage(userId, messageData);
          syncResults.push(syncResult);
          
          if (syncResult.status === 'synced' || syncResult.status === 'already_exists') {
            successCount++;
          } else {
            failureCount++;
          }

        } catch (error) {
          console.error(`[ChatSyncService] Error syncing message ${messageData.tempId}:`, error);
          syncResults.push({
            tempId: messageData.tempId,
            status: 'failed',
            error: error.message
          });
          failureCount++;
        }
      }

      console.log(`[ChatSyncService] Sync completed: ${successCount} success, ${failureCount} failures`);

      return {
        syncResults,
        totalMessages: messages.length,
        successCount,
        failureCount,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('[ChatSyncService] Error in syncMessagesFromClient:', error);
      throw error;
    }
  }

  /**
   * Sync a single message
   */
  static async syncSingleMessage(userId, messageData) {
    try {
      const { tempId, chatId, recipientId, content, messageType, timestamp, replyToMessageId } = messageData;

      // Validate required fields
      if (!tempId || !chatId || !recipientId || !content) {
        throw new Error('Missing required message fields');
      }

      // Check if message already exists by tempId or external_id
      const existingQuery = `
        SELECT id, temp_id, external_id 
        FROM messages 
        WHERE temp_id = ? OR external_id = ? OR id = ?
      `;
      const existing = await queryRunner.queryRunner(existingQuery, [tempId, tempId, tempId]);

      if (existing.length > 0) {
        return {
          tempId,
          status: 'already_exists',
          serverId: existing[0].id,
          message: 'Message already exists in database'
        };
      }

      // Verify users exist
      const userQuery = 'SELECT id, name, profile_image FROM users WHERE id IN (?, ?)';
      const users = await queryRunner.queryRunner(userQuery, [userId, recipientId]);

      if (users.length !== 2) {
        throw new Error('Sender or recipient not found');
      }

      const sender = users.find(u => u.id == userId);
      const recipient = users.find(u => u.id == recipientId);

      // Insert message into database
      const messageQuery = `
        INSERT INTO messages (
          chat_id, sender_id, recipient_id, sender_name, sender_avatar,
          content, message_type, reply_to_message_id, temp_id, external_id,
          status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'sent', ?)
      `;

      const messageValues = [
        chatId,
        userId,
        recipientId,
        sender.name || 'Unknown User',
        sender.profile_image,
        content,
        messageType || 'text',
        replyToMessageId || null,
        tempId,
        tempId, // Use tempId as external_id for tracking
        new Date(timestamp || Date.now())
      ];

      const result = await queryRunner.queryRunner(messageQuery, messageValues);
      const messageId = result.insertId;

      // Create sync tracking entry
      const syncQuery = `
        INSERT INTO messages_local_sync (
          message_id, chat_id, local_message_id, local_timestamp,
          server_timestamp, sync_status, sync_attempts
        ) VALUES (?, ?, ?, ?, ?, 'synced', 1)
      `;

      await queryRunner.queryRunner(syncQuery, [
        messageId,
        chatId,
        tempId,
        new Date(timestamp || Date.now()),
        new Date()
      ]);

      console.log(`[ChatSyncService] Successfully synced message ${tempId} -> ${messageId}`);

      return {
        tempId,
        status: 'synced',
        serverId: messageId,
        message: 'Message synced successfully'
      };

    } catch (error) {
      console.error(`[ChatSyncService] Error syncing single message:`, error);
      throw error;
    }
  }

  /**
   * Get messages for a chat with pagination
   */
  static async getMessagesForChat(userId, chatId, page = 1, limit = 50) {
    try {
      // Verify user is participant in this chat
      const chatParts = chatId.split('_');
      if (chatParts.length !== 3 || chatParts[0] !== 'chat') {
        throw new Error('Invalid chat ID format');
      }

      const [, userId1, userId2] = chatParts;
      if (userId.toString() !== userId1 && userId.toString() !== userId2) {
        throw new Error('User is not authorized to access this chat');
      }

      const offset = (page - 1) * limit;

      // Get messages with delivery status
      const messagesQuery = `
        SELECT 
          m.*,
          mds.status as delivery_status,
          mds.timestamp as delivery_timestamp
        FROM messages m
        LEFT JOIN message_delivery_status mds ON m.id = mds.message_id AND mds.recipient_id = ?
        WHERE m.chat_id = ? AND m.is_deleted = FALSE
        ORDER BY m.created_at DESC, m.id DESC
        LIMIT ? OFFSET ?
      `;

      const messages = await queryRunner.queryRunner(messagesQuery, [userId, chatId, limit, offset]);

      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM messages m
        WHERE m.chat_id = ? AND m.is_deleted = FALSE
      `;
      const [countResult] = await queryRunner.queryRunner(countQuery, [chatId]);

      const totalMessages = countResult.total;
      const totalPages = Math.ceil(totalMessages / limit);

      return {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalMessages,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };

    } catch (error) {
      console.error('[ChatSyncService] Error getting messages for chat:', error);
      throw error;
    }
  }

  /**
   * Get sync status for a user
   */
  static async getSyncStatus(userId) {
    try {
      // Get pending sync count
      const pendingSyncQuery = `
        SELECT COUNT(*) as pending_count
        FROM messages_local_sync mls
        JOIN messages m ON mls.message_id = m.id
        WHERE (m.sender_id = ? OR m.recipient_id = ?) 
          AND mls.sync_status = 'pending'
      `;
      const [pendingResult] = await queryRunner.queryRunner(pendingSyncQuery, [userId, userId]);

      // Get failed sync count
      const failedSyncQuery = `
        SELECT COUNT(*) as failed_count
        FROM messages_local_sync mls
        JOIN messages m ON mls.message_id = m.id
        WHERE (m.sender_id = ? OR m.recipient_id = ?) 
          AND mls.sync_status = 'failed'
      `;
      const [failedResult] = await queryRunner.queryRunner(failedSyncQuery, [userId, userId]);

      // Get last sync time
      const lastSyncQuery = `
        SELECT MAX(mls.server_timestamp) as last_sync
        FROM messages_local_sync mls
        JOIN messages m ON mls.message_id = m.id
        WHERE (m.sender_id = ? OR m.recipient_id = ?) 
          AND mls.sync_status = 'synced'
      `;
      const [lastSyncResult] = await queryRunner.queryRunner(lastSyncQuery, [userId, userId]);

      return {
        pendingSync: pendingResult.pending_count || 0,
        failedSync: failedResult.failed_count || 0,
        lastSyncAt: lastSyncResult.last_sync,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('[ChatSyncService] Error getting sync status:', error);
      throw error;
    }
  }

  /**
   * Retry failed syncs
   */
  static async retryFailedSyncs(userId) {
    try {
      console.log(`[ChatSyncService] Retrying failed syncs for user ${userId}`);

      // Get failed sync entries
      const failedSyncsQuery = `
        SELECT mls.*, m.*
        FROM messages_local_sync mls
        JOIN messages m ON mls.message_id = m.id
        WHERE (m.sender_id = ? OR m.recipient_id = ?) 
          AND mls.sync_status = 'failed'
          AND mls.sync_attempts < 5
        ORDER BY mls.last_sync_attempt ASC
        LIMIT 10
      `;

      const failedSyncs = await queryRunner.queryRunner(failedSyncsQuery, [userId, userId]);

      if (failedSyncs.length === 0) {
        return {
          retriedCount: 0,
          message: 'No failed syncs to retry'
        };
      }

      let retriedCount = 0;
      for (const syncEntry of failedSyncs) {
        try {
          // Update sync attempt count
          const updateAttemptQuery = `
            UPDATE messages_local_sync 
            SET sync_attempts = sync_attempts + 1, 
                last_sync_attempt = CURRENT_TIMESTAMP
            WHERE id = ?
          `;
          await queryRunner.queryRunner(updateAttemptQuery, [syncEntry.id]);

          // Try to process the sync again
          // For now, just mark as synced if the message exists
          const updateStatusQuery = `
            UPDATE messages_local_sync 
            SET sync_status = 'synced', 
                server_timestamp = CURRENT_TIMESTAMP,
                error_message = NULL
            WHERE id = ?
          `;
          await queryRunner.queryRunner(updateStatusQuery, [syncEntry.id]);

          retriedCount++;
          console.log(`[ChatSyncService] Retried sync for message ${syncEntry.message_id}`);

        } catch (error) {
          console.error(`[ChatSyncService] Failed to retry sync for message ${syncEntry.message_id}:`, error);
          
          // Update error message
          const updateErrorQuery = `
            UPDATE messages_local_sync 
            SET error_message = ?
            WHERE id = ?
          `;
          await queryRunner.queryRunner(updateErrorQuery, [error.message, syncEntry.id]);
        }
      }

      return {
        retriedCount,
        totalFailed: failedSyncs.length,
        message: `Retried ${retriedCount} out of ${failedSyncs.length} failed syncs`
      };

    } catch (error) {
      console.error('[ChatSyncService] Error retrying failed syncs:', error);
      throw error;
    }
  }

  /**
   * Clean up old sync entries
   */
  static async cleanupOldSyncEntries(daysOld = 30) {
    try {
      console.log(`[ChatSyncService] Cleaning up sync entries older than ${daysOld} days`);

      const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);

      const deleteQuery = `
        DELETE FROM messages_local_sync 
        WHERE sync_status = 'synced' 
          AND server_timestamp < ?
      `;

      const result = await queryRunner.queryRunner(deleteQuery, [cutoffDate]);

      console.log(`[ChatSyncService] Cleaned up ${result.affectedRows} old sync entries`);

      return {
        deletedCount: result.affectedRows,
        cutoffDate: cutoffDate.toISOString(),
        message: `Cleaned up ${result.affectedRows} sync entries older than ${daysOld} days`
      };

    } catch (error) {
      console.error('[ChatSyncService] Error cleaning up old sync entries:', error);
      throw error;
    }
  }
}

module.exports = ChatSyncService;
