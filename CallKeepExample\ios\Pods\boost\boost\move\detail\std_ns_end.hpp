#//////////////////////////////////////////////////////////////////////////////
#//
#// (C) Copyright Ion Gaztanaga 2015-2015.
#// Distributed under the Boost Software License, Version 1.0.
#// (See accompanying file LICENSE_1_0.txt or copy at
#// http://www.boost.org/LICENSE_1_0.txt)
#//
#// See http://www.boost.org/libs/move for documentation.
#//
#//////////////////////////////////////////////////////////////////////////////
#ifdef BOOST_MOVE_STD_NS_GCC_DIAGNOSTIC_PUSH
   #pragma GCC diagnostic pop
   #undef BOOST_MOVE_STD_NS_GCC_DIAGNOSTIC_PUSH
#elif defined(_MSC_VER) && (_MSC_VER >= 1915)
   #pragma warning (pop)
#endif   //BOOST_MOVE_STD_NS_GCC_DIAGNOSTIC_PUSH
