﻿# -------------------------------------------------------------------------
# Legend:
# 
# Short keys: M=MONTH_OF_YEAR, D=DAY_OF_DECADE, S=SANSCULOTTIDES, E=ERA
# Format mode: w=WIDE, a=ABBREVIATED, s=SHORT, n=NARROW
# Standalone mode: W=WIDE, A=ABBREVIATED, S=SHORT, N=NARROW
# Display mode: f=FULL, l=LONG, m=MEDIUM, s= SHORT
# -------------------------------------------------------------------------

# supported list of languages
languages=ca de en es eu fr it ja pt ru

# property key format (first letter only if true - relevant for: M, D, S, E)
useShortKeys=true

# months
M(w)_1=01
M(w)_2=02
M(w)_3=03
M(w)_4=04
M(w)_5=05
M(w)_6=06
M(w)_7=07
M(w)_8=08
M(w)_9=09
M(w)_10=10
M(w)_11=11
M(w)_12=12

M(a)_1=01
M(a)_2=02
M(a)_3=03
M(a)_4=04
M(a)_5=05
M(a)_6=06
M(a)_7=07
M(a)_8=08
M(a)_9=09
M(a)_10=10
M(a)_11=11
M(a)_12=12

M(N)_1=1
M(N)_2=2
M(N)_3=3
M(N)_4=4
M(N)_5=5
M(N)_6=6
M(N)_7=7
M(N)_8=8
M(N)_9=9
M(N)_10=10
M(N)_11=11
M(N)_12=12

# decade
D(w)_1=primidi
D(w)_2=duodi
D(w)_3=tridi
D(w)_4=quartidi
D(w)_5=quintidi
D(w)_6=sextidi
D(w)_7=septidi
D(w)_8=octidi
D(w)_9=nonidi
D(w)_10=décadi

D(W)_1=Primidi
D(W)_2=Duodi
D(W)_3=Tridi
D(W)_4=Quartidi
D(W)_5=Quintidi
D(W)_6=Sextidi
D(W)_7=Septidi
D(W)_8=Octidi
D(W)_9=Nonidi
D(W)_10=Décadi

D(N)_1=P
D(N)_2=D
D(N)_3=T
D(N)_4=Q
D(N)_5=Q
D(N)_6=S
D(N)_7=S
D(N)_8=O
D(N)_9=N
D(N)_10=D

# sansculottides
S(w)_1=1
S(w)_2=2
S(w)_3=3
S(w)_4=4
S(w)_5=5
S(w)_6=6

# eras
E(a)_0=RF
