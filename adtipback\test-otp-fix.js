const otpService = require('./services/OTPService');
const utils = require('./utils/utils');
const dbQuery = require('./dbConfig/queryRunner');

const testMobileNumber = '6303390566';
const testMessageId = 'test-fix-' + Date.now();

async function testOTPFix() {
  console.log('🧪 Testing OTP Fix - Complete Flow');
  console.log('=====================================');
  
  try {
    // Step 1: Generate OTP (simulating the old flow)
    console.log('\n1️⃣ Generating OTP...');
    const otp = Math.floor(Math.random() * 899999 + 100000);
    console.log(`   Generated OTP: ${otp}`);
    
    // Step 2: Send SMS (simulating SMS sending)
    console.log('\n2️⃣ Sending SMS...');
    const smsResult = await utils.sendOtpSms(testMobileNumber, otp);
    console.log(`   SMS Result:`, smsResult);
    
    // Step 3: Update database with the same OTP (using the new fix)
    console.log('\n3️⃣ Updating database with existing OTP...');
    const updateResult = await otpService.updateOTPWithExistingOTP(testMobileNumber, otp, testMessageId);
    console.log(`   Database Update Result:`, updateResult);
    
    // Step 4: Verify the OTP is stored correctly
    console.log('\n4️⃣ Verifying OTP storage...');
    const checkQuery = `
      SELECT otp, message_id, otp_created_at 
      FROM users 
      WHERE mobile_number = ?
    `;
    const storedData = await dbQuery.queryRunner(checkQuery, [testMobileNumber]);
    console.log(`   Stored OTP: ${storedData[0]?.otp}`);
    console.log(`   Stored Message ID: ${storedData[0]?.message_id}`);
    console.log(`   OTP Created At: ${storedData[0]?.otp_created_at}`);
    
    // Step 5: Verify OTP verification works
    console.log('\n5️⃣ Testing OTP verification...');
    console.log(`   Attempting to verify OTP: ${otp}`);
    const verifyResult = await otpService.verifyOTP(testMobileNumber, otp);
    console.log(`   Verification Result:`, verifyResult);
    
    // Step 6: Check that OTP is cleared after verification
    console.log('\n6️⃣ Checking OTP clearance...');
    const clearedData = await dbQuery.queryRunner(checkQuery, [testMobileNumber]);
    console.log(`   OTP after verification: ${clearedData[0]?.otp}`);
    console.log(`   Message ID after verification: ${clearedData[0]?.message_id}`);
    
    console.log('\n✅ Test completed successfully!');
    console.log('   The OTP fix is working correctly.');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  }
}

// Run the test
testOTPFix(); 