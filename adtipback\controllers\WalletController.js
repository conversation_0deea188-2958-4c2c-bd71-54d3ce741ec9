let walletService = require("../services/WalletService");
const dbQuery = require("../dbConfig/queryRunner");
const moment = require("moment");

// controllers/WalletController.js
module.exports = {
  saveFunds: async (req, res, next) => {
    console.log(`WalletController.saveFunds - Request body: ${JSON.stringify(req.body)}`);
    const { createdby, amount, transactionStatus, transaction_type, order_id, payment_id, isCron } = req.body;

    // Validate required fields
    if (!createdby || !Number.isInteger(createdby)) {
      return res.status(400).send({
        status: 400,
        message: "Invalid or missing createdby.",
        data: [],
      });
    }

    if (!amount || typeof amount !== "number" || amount <= 0) {
      return res.status(400).send({
        status: 400,
        message: "Invalid or missing amount. Must be a positive number.",
        data: [],
      });
    }

    if (!transaction_type || !["Deposite", "call_deposit", "withdraw"].includes(transaction_type)) {
      return res.status(400).send({
        status: 400,
        message: "Invalid transaction_type. Must be 'Deposite', 'call_deposit', or 'withdraw'.",
        data: [],
      });
    }

    if (!transactionStatus || !["1", "2", "3"].includes(transactionStatus.toString())) {
      return res.status(400).send({
        status: 400,
        message: "Invalid transaction status. Must be 1, 2, or 3.",
        data: [],
      });
    }

    if (!order_id || !payment_id) {
      return res.status(400).send({
        status: 400,
        message: "Missing order_id or payment_id.",
        data: [],
      });
    }

    // Validate transaction in transactions table
    const transactionQuery = `
      SELECT status, order_id, payment_id
      FROM transactions
      WHERE order_id = ? AND payment_id = ? AND status = 'success'
    `;
    const transaction = await dbQuery.queryRunner(transactionQuery, [order_id, payment_id]);

    if (!transaction.length || transaction[0].order_id !== order_id || transaction[0].payment_id !== payment_id) {
      return res.status(400).send({
        status: 400,
        message: "Invalid or unverified transaction.",
        data: [],
      });
    }

    walletService
      .saveFunds(req.body)
      .then((result) => {
        // If not called by cron, update is_funds_added to 1
        if (!isCron) {
          const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
          dbQuery.queryRunner(`
            UPDATE transactions
            SET is_funds_added = 1, updated_at = '${currentTime}'
            WHERE order_id = '${order_id}' AND payment_id = '${payment_id}'
          `);
        }
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  },

  getFunds: (req, res, next) => {
    console.log(`WalletController.getFunds - User ID from params: ${req.params.id}`);
    const userId = parseInt(req.params.id, 10);

    if (!userId || isNaN(userId)) {
      return res.status(400).send({
        status: 400,
        message: "Invalid or missing user ID.",
        data: [],
      });
    }

    walletService
      .getFunds(userId)
      .then((result) => {
        if (result && result.status === 200 && result.data.length > 0) {
          res.status(200).send({
            status: 200,
            message: "Fetched latest balance successfully.",
            availableBalance: result.data[0].totalBalance.toFixed(2),
          });
        } else {
          res.status(200).send({
            status: 200,
            message: "No balance found for this user.",
            availableBalance: "0.00",
          });
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  },

  withdrawFund: (req, res, next) => {
    walletService
      .withdrawFund(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateWithdrawFund: (req, res, next) => {
    walletService
      .updateWithdrawFund(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  changeStatusPaid: (req, res, next) => {
    walletService
      .changeStatusPaid(req.body)
      .then((result) => {
        console.log("rseult 2", result);
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getPaidFunds: (req, res, next) => {
    walletService
      .getPaidFunds()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  /**
   * Credit ad reward to user's wallet (no transaction record)
   * @route POST /api/wallet/credit-ad-reward
   * @body { userId, amount }
   */
  creditAdReward: async (req, res) => {
    const { userId, amount } = req.body;
    if (!userId || isNaN(userId) || !amount || isNaN(amount)) {
      return res.status(400).send({ status: 400, message: 'Invalid userId or amount.' });
    }
    try {
      const result = await walletService.creditAdReward(Number(userId), Number(amount));
      res.status(result.status).send(result);
    } catch (err) {
      res.status(err.status || 500).send({ status: err.status || 500, message: err.message || 'Error crediting wallet.' });
    }
  },
};
