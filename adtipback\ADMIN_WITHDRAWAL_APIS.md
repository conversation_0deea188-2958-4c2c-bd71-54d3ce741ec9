# Admin Withdrawal Management APIs

This document details all admin panel APIs for managing withdrawal requests across all types (wallet, referral, coupon, channel).

---

## Authentication
All endpoints require admin authentication (`Auth.verifyToken`).

---

## API Endpoints Overview

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/admin/withdrawals/wallet` | GET | List wallet withdrawal requests |
| `/admin/withdrawals/wallet/:id/status` | PATCH | Update wallet withdrawal status |
| `/admin/withdrawals/wallet/export` | GET | Export wallet withdrawals (CSV) |
| `/admin/withdrawals/referral` | GET | List referral withdrawal requests |
| `/admin/withdrawals/referral/:id/status` | PATCH | Update referral withdrawal status |
| `/admin/withdrawals/referral/export` | GET | Export referral withdrawals (CSV) |
| `/admin/withdrawals/coupon` | GET | List coupon withdrawal requests |
| `/admin/withdrawals/coupon/:id/status` | PATCH | Update coupon withdrawal status |
| `/admin/withdrawals/coupon/export` | GET | Export coupon withdrawals (CSV) |
| `/admin/withdrawals/channel` | GET | List channel withdrawal requests |
| `/admin/withdrawals/channel/:id/status` | PATCH | Update channel withdrawal status |
| `/admin/withdrawals/channel/export` | GET | Export channel withdrawals (CSV) |

---

## 1. List Withdrawals

### Endpoint
`GET /admin/withdrawals/{type}`

### Description
List withdrawal requests by type, with filtering and pagination.

### Query Parameters
- `page`: number (optional, default: 1)
- `limit`: number (optional, default: 20)
- `status`: `pending` | `approved` | `rejected` | `paid` (optional)
- `userId`: number (optional)
- `fromDate`: `YYYY-MM-DD` (optional)
- `toDate`: `YYYY-MM-DD` (optional)
- `premium`: `1` (premium only) or `0` (non-premium only) (optional)

### Example Request
```bash
GET /admin/withdrawals/wallet?page=1&limit=20&status=pending&premium=1
```

### Response
```json
{
  "status": true,
  "message": "Wallet withdrawals fetched",
  "data": {
    "withdrawals": [
      {
        "id": 101,
        "user_id": 1,
        "amount": 1500,
        "status": "pending",
        "payment_method": "bank",
        "account_details": "{\"accountNumber\":\"**********\",\"ifsc\":\"SBIN0001234\"}",
        "admin_notes": null,
        "created_at": "2024-06-01T10:00:00Z",
        "updated_at": null,
        "firstName": "John",
        "lastName": "Doe",
        "mobile_number": "**********",
        "emailId": "<EMAIL>",
        "is_premium": true,
        "profile_pic": "profile.jpg"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 100,
      "itemsPerPage": 20
    }
  }
}
```

---

## 2. Update Withdrawal Status

### Endpoint
`PATCH /admin/withdrawals/{type}/{id}/status`

### Description
Update the status of a withdrawal request (approve/reject/paid).

### Request Body
```json
{
  "status": "approved",
  "adminNotes": "All details verified. Payment processed.",
  "adminId": 10
}
```

### Status Values
- `pending`: Request submitted, awaiting review
- `approved`: Admin approved, ready for payment
- `rejected`: Admin rejected the request
- `paid`: Payment completed

### Example Request
```bash
PATCH /admin/withdrawals/wallet/101/status
Content-Type: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN

{
  "status": "approved",
  "adminNotes": "Bank details verified. Processing payment.",
  "adminId": 10
}
```

### Response
```json
{
  "status": true,
  "message": "Wallet withdrawal status updated",
  "data": {
    "withdrawalId": 101,
    "status": "approved"
  }
}
```

---

## 3. Export Withdrawals (CSV)

### Endpoint
`GET /admin/withdrawals/{type}/export`

### Description
Export withdrawal requests as CSV file (filtered by status/date).

### Query Parameters
- `status`: `pending` | `approved` | `rejected` | `paid` (optional)
- `fromDate`: `YYYY-MM-DD` (optional)
- `toDate`: `YYYY-MM-DD` (optional)

### Example Request
```bash
GET /admin/withdrawals/wallet/export?status=pending&fromDate=2024-06-01&toDate=2024-06-30
```

### Response
- **Content-Type:** `text/csv`
- **Content-Disposition:** `attachment; filename=wallet_withdrawals_2024-06-01.csv`
- **Body:** CSV data with headers: ID,User ID,Amount,Status,Payment Method,Account Details,Admin Notes,Created At,User Name,Mobile,Email,Premium

---

## Supported Withdrawal Types

| Type | Table Name | Description |
|------|------------|-------------|
| `wallet` | `wallet_withdrawals` | Wallet balance withdrawals |
| `referral` | `referral_withdrawals` | Referral earnings withdrawals |
| `coupon` | `coupon_withdrawals` | Coupon earnings withdrawals |
| `channel` | `channel_withdrawals` | Channel/content creator withdrawals |

---

## Admin Panel Usage Examples

### 1. View Pending Wallet Withdrawals
```javascript
// Frontend API call
const response = await fetch('/admin/withdrawals/wallet?status=pending&page=1&limit=20', {
  headers: {
    'Authorization': `Bearer ${adminToken}`
  }
});
const data = await response.json();
```

### 2. Approve a Withdrawal
```javascript
// Frontend API call
const response = await fetch('/admin/withdrawals/wallet/101/status', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${adminToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    status: 'approved',
    adminNotes: 'All details verified',
    adminId: currentAdminId
  })
});
```

### 3. Export Pending Withdrawals
```javascript
// Frontend API call
const response = await fetch('/admin/withdrawals/wallet/export?status=pending', {
  headers: {
    'Authorization': `Bearer ${adminToken}`
  }
});
const blob = await response.blob();
const url = window.URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'wallet_withdrawals.csv';
a.click();
```

---

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "status": false,
  "message": "Invalid withdrawal ID",
  "data": []
}
```

**401 Unauthorized:**
```json
{
  "status": false,
  "message": "Admin authentication required",
  "data": []
}
```

**404 Not Found:**
```json
{
  "status": false,
  "message": "Withdrawal not found",
  "data": []
}
```

**500 Internal Server Error:**
```json
{
  "status": false,
  "message": "Error updating withdrawal status",
  "data": []
}
```

---

## Audit Trail

All admin actions are automatically logged in the `withdrawal_audit_log` table with:
- `withdrawal_id`: The withdrawal being modified
- `withdrawal_type`: wallet/referral/coupon/channel
- `old_status`: Previous status
- `new_status`: New status
- `admin_id`: Admin who made the change
- `notes`: Admin notes
- `created_at`: Timestamp

---

## Business Rules

1. **Status Flow:** pending → approved → paid (or pending → rejected)
2. **Admin Notes:** Required for status changes
3. **Audit Logging:** All changes are logged
4. **Export Filtering:** Can filter by status and date range
5. **Pagination:** Default 20 items per page, max 100

---

## Testing Checklist

- [ ] List withdrawals with different filters
- [ ] Update status (approve/reject/paid)
- [ ] Export CSV with filters
- [ ] Test pagination
- [ ] Verify audit logging
- [ ] Test error scenarios
- [ ] Check authentication requirements

---

For implementation details, see the backend code in:
- `controllers/AdminController.js`
- `services/AdminService.js`
- `routes/api-routes.js` 