{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-te/values-te.xml", "map": [{"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "58,59,60,61,62,63,64,275", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4470,4572,4680,4782,4883,4989,5096,23084", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "4567,4675,4777,4878,4984,5091,5215,23180"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5610,5722,5876,6006,6121,6258,6383,6488,6726,6875,6987,7140,7272,7423,7586,7650,7720", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "5717,5871,6001,6116,6253,6378,6483,6583,6870,6982,7135,7267,7418,7581,7645,7715,7799"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "77", "endOffsets": "279"}, "to": {"startLines": "280", "startColumns": "4", "startOffsets": "23480", "endColumns": "81", "endOffsets": "23557"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,291,345,412,483,590,654,807,948,1086,1137,1202,1313,1414,1464,1560,1600,1641,1705,1794,1843", "endColumns": "41,49,53,66,70,106,63,152,140,137,50,64,110,100,49,95,39,40,63,88,48,55", "endOffsets": "240,290,344,411,482,589,653,806,947,1085,1136,1201,1312,1413,1463,1559,1599,1640,1704,1793,1842,1898"}, "to": {"startLines": "237,238,239,240,241,242,243,244,245,246,247,248,249,250,255,256,257,258,259,260,261,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19965,20011,20065,20123,20194,20269,20380,20448,20605,20750,20892,20947,21016,21131,21557,21611,21711,21755,21800,21868,21961,23562", "endColumns": "45,53,57,70,74,110,67,156,144,141,54,68,114,104,53,99,43,44,67,92,52,59", "endOffsets": "20006,20060,20118,20189,20264,20375,20443,20600,20745,20887,20942,21011,21126,21231,21606,21706,21750,21795,21863,21956,22009,23617"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "966,1083,1195,1308,1398,1503,1622,1700,1776,1867,1960,2055,2149,2249,2342,2437,2532,2623,2714,2803,2917,3021,3120,3235,3340,3455,3617,22095", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "1078,1190,1303,1393,1498,1617,1695,1771,1862,1955,2050,2144,2244,2337,2432,2527,2618,2709,2798,2912,3016,3115,3230,3335,3450,3612,3715,22173"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,276,365,455,541,639,726,830,946,1037,1098,1164,1258,1325,1387,1480,1544,1612,1675,1749,1814,1868,1989,2046,2108,2162,2241,2369,2457,2538,2636,2719,2811,2956,3036,3118,3243,3331,3413,3473,3525,3591,3666,3744,3815,3894,3967,4043,4124,4193,4313,4418,4495,4586,4679,4753,4830,4922,4979,5060,5126,5210,5296,5359,5424,5488,5557,5667,5775,5874,5980,6044,6100,6183,6280,6358", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,88,89,85,97,86,103,115,90,60,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82,96,77,73", "endOffsets": "271,360,450,536,634,721,825,941,1032,1093,1159,1253,1320,1382,1475,1539,1607,1670,1744,1809,1863,1984,2041,2103,2157,2236,2364,2452,2533,2631,2714,2806,2951,3031,3113,3238,3326,3408,3468,3520,3586,3661,3739,3810,3889,3962,4038,4119,4188,4308,4413,4490,4581,4674,4748,4825,4917,4974,5055,5121,5205,5291,5354,5419,5483,5552,5662,5770,5869,5975,6039,6095,6178,6275,6353,6427"}, "to": {"startLines": "19,53,54,55,56,57,65,66,67,89,90,160,165,168,170,171,172,173,174,175,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,251,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "790,4020,4109,4199,4285,4383,5220,5324,5440,8003,8064,13672,14164,14383,14513,14606,14670,14738,14801,14875,14940,14994,15115,15172,15234,15288,15367,16234,16322,16403,16501,16584,16676,16821,16901,16983,17108,17196,17278,17338,17390,17456,17531,17609,17680,17759,17832,17908,17989,18058,18178,18283,18360,18451,18544,18618,18695,18787,18844,18925,18991,19075,19161,19224,19289,19353,19422,19532,19640,19739,19845,19909,21236,22178,22275,22353", "endLines": "22,53,54,55,56,57,65,66,67,89,90,160,165,168,170,171,172,173,174,175,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,251,264,265,266", "endColumns": "12,88,89,85,97,86,103,115,90,60,65,93,66,61,92,63,67,62,73,64,53,120,56,61,53,78,127,87,80,97,82,91,144,79,81,124,87,81,59,51,65,74,77,70,78,72,75,80,68,119,104,76,90,92,73,76,91,56,80,65,83,85,62,64,63,68,109,107,98,105,63,55,82,96,77,73", "endOffsets": "961,4104,4194,4280,4378,4465,5319,5435,5526,8059,8125,13761,14226,14440,14601,14665,14733,14796,14870,14935,14989,15110,15167,15229,15283,15362,15490,16317,16398,16496,16579,16671,16816,16896,16978,17103,17191,17273,17333,17385,17451,17526,17604,17675,17754,17827,17903,17984,18053,18173,18278,18355,18446,18539,18613,18690,18782,18839,18920,18986,19070,19156,19219,19284,19348,19417,19527,19635,19734,19840,19904,19960,21314,22270,22348,22422"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6588", "endColumns": "137", "endOffsets": "6721"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,206,278,348,430,498,565,640,716,801,883,954,1035,1115,1198,1284,1372,1450,1526,1601,1692,1764,1843,1912", "endColumns": "71,78,71,69,81,67,66,74,75,84,81,70,80,79,82,85,87,77,75,74,90,71,78,68,74", "endOffsets": "122,201,273,343,425,493,560,635,711,796,878,949,1030,1110,1193,1279,1367,1445,1521,1596,1687,1759,1838,1907,1982"}, "to": {"startLines": "50,68,164,166,167,169,189,190,191,252,253,254,262,267,268,269,270,271,272,273,274,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3720,5531,14092,14231,14301,14445,16016,16083,16158,21319,21404,21486,22014,22427,22507,22590,22676,22764,22842,22918,22993,23185,23257,23336,23405", "endColumns": "71,78,71,69,81,67,66,74,75,84,81,70,80,79,82,85,87,77,75,74,90,71,78,68,74", "endOffsets": "3787,5605,14159,14296,14378,14508,16078,16153,16229,21399,21481,21552,22090,22502,22585,22671,22759,22837,22913,22988,23079,23252,23331,23400,23475"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,249,328,420,519,612,689,780,882,971,1043,1151,1229,1332,1419,1509,1578,1679,1753,1826,1910,2004,2100", "endColumns": "87,105,78,91,98,92,76,90,101,88,71,107,77,102,86,89,68,100,73,72,83,93,95,99", "endOffsets": "138,244,323,415,514,607,684,775,877,966,1038,1146,1224,1327,1414,1504,1573,1674,1748,1821,1905,1999,2095,2195"}, "to": {"startLines": "88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,183,184,185,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7915,8130,8236,8315,8407,8506,8599,8676,8767,8869,8958,9030,9138,9216,9319,9406,9496,9565,15495,15569,15642,15726,15820,15916", "endColumns": "87,105,78,91,98,92,76,90,101,88,71,107,77,102,86,89,68,100,73,72,83,93,95,99", "endOffsets": "7998,8231,8310,8402,8501,8594,8671,8762,8864,8953,9025,9133,9211,9314,9401,9491,9560,9661,15564,15637,15721,15815,15911,16011"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "87,161,162,163", "startColumns": "4,4,4,4", "startOffsets": "7804,13766,13874,13985", "endColumns": "110,107,110,106", "endOffsets": "7910,13869,13980,14087"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,502,695,787,879,968,1068,1172,1249,1314,1406,1498,1569,1639,1700,1770,1907,2039,2174,2249,2333,2408,2479,2573,2667,2731,2810,2863,2921,2969,3030,3097,3159,3224,3291,3350,3412,3478,3542,3609,3663,3723,3797,3871", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,91,88,99,103,76,64,91,91,70,69,60,69,136,131,134,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "281,497,690,782,874,963,1063,1167,1244,1309,1401,1493,1564,1634,1695,1765,1902,2034,2169,2244,2328,2403,2474,2568,2662,2726,2805,2858,2916,2964,3025,3092,3154,3219,3286,3345,3407,3473,3537,3604,3658,3718,3792,3866,3920"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,597,9666,9758,9850,9939,10039,10143,10220,10285,10377,10469,10540,10610,10671,10741,10878,11010,11145,11220,11304,11379,11450,11544,11638,11702,12491,12544,12602,12650,12711,12778,12840,12905,12972,13031,13093,13159,13223,13290,13344,13404,13478,13552", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,91,91,88,99,103,76,64,91,91,70,69,60,69,136,131,134,74,83,74,70,93,93,63,78,52,57,47,60,66,61,64,66,58,61,65,63,66,53,59,73,73,53", "endOffsets": "376,592,785,9753,9845,9934,10034,10138,10215,10280,10372,10464,10535,10605,10666,10736,10873,11005,11140,11215,11299,11374,11445,11539,11633,11697,11776,12539,12597,12645,12706,12773,12835,12900,12967,13026,13088,13154,13218,13285,13339,13399,13473,13547,13601"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,113", "endOffsets": "164,278"}, "to": {"startLines": "51,52", "startColumns": "4,4", "startOffsets": "3792,3906", "endColumns": "113,113", "endOffsets": "3901,4015"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-te\\values-te.xml", "from": {"startLines": "59", "startColumns": "4", "startOffsets": "3794", "endColumns": "65", "endOffsets": "3855"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "13606", "endColumns": "65", "endOffsets": "13667"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11781,11853,11921,11994,12062,12142,12219,12320,12413", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "11848,11916,11989,12057,12137,12214,12315,12408,12486"}}]}]}