# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 20ms
  generate-prefab-packages
    exec-prefab 555ms
    [gap of 20ms]
  generate-prefab-packages completed in 580ms
  execute-generate-process
    [gap of 15ms]
    exec-configure 1318ms
    [gap of 153ms]
  execute-generate-process completed in 1486ms
  [gap of 17ms]
  remove-unexpected-so-files 21ms
  [gap of 57ms]
generate_cxx_metadata completed in 2209ms

