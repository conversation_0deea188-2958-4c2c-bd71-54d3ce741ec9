import React from 'react';
import { usePosts, usePremiumPosts, useWalletBalance } from '../hooks/useApi';
import { useAuth } from '../contexts/AuthContext';

const TanStackQueryTest: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const userId = user?.id?.toString() || "";

  // Test authenticated posts
  const {
    data: postsData,
    isLoading: postsLoading,
    error: postsError,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = usePosts(0, user?.id, { enabled: isAuthenticated });

  // Test premium posts for guests
  const {
    data: premiumPostsData,
    isLoading: premiumPostsLoading,
    error: premiumPostsError,
  } = usePremiumPosts({ enabled: !isAuthenticated });

  // Test wallet balance
  const {
    data: walletData,
    isLoading: walletLoading,
    error: walletError,
  } = useWalletBalance(userId, { enabled: isAuthenticated && !!userId });

  const posts = isAuthenticated 
    ? postsData?.pages?.flatMap(page => page?.data || []) || []
    : premiumPostsData?.data || [];

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h2 className="text-xl font-bold mb-4">TanStack Query Test</h2>
      
      <div className="space-y-4">
        <div>
          <h3 className="font-semibold">Authentication Status:</h3>
          <p>{isAuthenticated ? 'Authenticated' : 'Guest'}</p>
          {isAuthenticated && <p>User ID: {user?.id}</p>}
        </div>

        <div>
          <h3 className="font-semibold">Posts:</h3>
          {(isAuthenticated ? postsLoading : premiumPostsLoading) && (
            <p className="text-blue-600">Loading posts...</p>
          )}
          {(isAuthenticated ? postsError : premiumPostsError) && (
            <p className="text-red-600">
              Error: {(isAuthenticated ? postsError : premiumPostsError)?.message}
            </p>
          )}
          <p>Posts loaded: {posts.length}</p>
          {isAuthenticated && hasNextPage && (
            <button
              onClick={() => fetchNextPage()}
              disabled={isFetchingNextPage}
              className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
            >
              {isFetchingNextPage ? 'Loading...' : 'Load More'}
            </button>
          )}
        </div>

        {isAuthenticated && (
          <div>
            <h3 className="font-semibold">Wallet:</h3>
            {walletLoading && <p className="text-blue-600">Loading wallet...</p>}
            {walletError && (
              <p className="text-red-600">Wallet Error: {walletError.message}</p>
            )}
            {walletData && (
              <p>Balance: ₹{walletData.availableBalance || 0}</p>
            )}
          </div>
        )}

        <div>
          <h3 className="font-semibold">Sample Posts:</h3>
          <div className="max-h-40 overflow-y-auto">
            {posts.slice(0, 3).map((post: any) => (
              <div key={post.id} className="p-2 border-b">
                <p className="font-medium">{post.title}</p>
                <p className="text-sm text-gray-600">{post.user_name}</p>
                <p className="text-xs text-gray-500">
                  Likes: {post.total_likes || 0} | Comments: {post.total_comments || 0}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TanStackQueryTest;
