# GST Implementation Guide for Subscription Plans

## Overview

This guide explains how to implement 18% GST on subscription plans while keeping the UI display unchanged. The system now provides two sets of APIs:

1. **UI Display APIs** - Show original amounts without GST
2. **Razorpay Checkout APIs** - Include GST calculations for payment processing

## API Endpoints

### User Premium Subscription

#### For UI Display (No GST)
```
GET /api/subscription-plans
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "plans": [
    {
      "id": "plan_Qjrw31WPrhunxz",
      "name": "Premium - 1 Month",
      "amount": 200,
      "currency": "INR",
      "period": 1,
      "interval": "monthly",
      "description": "Access to premium features for 1 month"
    }
  ]
}
```

#### For Razorpay Checkout (With GST)
```
GET /api/subscription-plans-with-gst
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": true,
  "plans": [
    {
      "id": "plan_Qjrw31WPrhunxz",
      "name": "Premium - 1 Month",
      "amount": 200,
      "amount_with_gst": 23600,
      "currency": "INR",
      "period": 1,
      "interval": "monthly",
      "description": "Access to premium features for 1 month",
      "gst_amount": 3600,
      "gst_percentage": 18
    }
  ]
}
```

### Content Creator Premium Subscription

#### For UI Display (No GST)
```
GET /api/content-premium-plans
Authorization: Bearer <token>
```

#### For Razorpay Checkout (With GST)
```
GET /api/content-premium-plans-with-gst
Authorization: Bearer <token>
```

## GST Calculation Details

### Base Amount Examples

| Plan | Base Amount | GST (18%) | Total Amount |
|------|-------------|-----------|--------------|
| 1 Month User Premium | ₹200 | ₹36 | ₹236 |
| 6 Month User Premium | ₹1,200 | ₹216 | ₹1,416 |
| 12 Month User Premium | ₹2,400 | ₹432 | ₹2,832 |
| 1 Month Content Creator | ₹2,500 | ₹450 | ₹2,950 |
| 3 Month Content Creator | ₹6,000 | ₹1,080 | ₹7,080 |
| 6 Month Content Creator | ₹12,000 | ₹2,160 | ₹14,160 |
| 12 Month Content Creator | ₹24,000 | ₹4,320 | ₹28,320 |

### Amount Conversion for Razorpay

- **Base amounts** are in rupees (e.g., 200)
- **Razorpay amounts** are in paise (e.g., 23600 for ₹236)
- **GST amounts** are also in paise for consistency

## Frontend Implementation

### For UI Display
Use the regular endpoints (`/subscription-plans`, `/content-premium-plans`) to show prices to users:

```javascript
// Display original price
const displayPrice = plan.amount; // e.g., 200
console.log(`Price: ₹${displayPrice}`);
```

### For Razorpay Checkout
Use the GST-enabled endpoints (`/subscription-plans-with-gst`, `/content-premium-plans-with-gst`) for payment processing:

```javascript
// Use GST-inclusive amount for Razorpay
const razorpayAmount = plan.amount_with_gst; // e.g., 23600 (₹236 in paise)
const gstAmount = plan.gst_amount; // e.g., 3600 (₹36 in paise)

// Show breakdown to user
console.log(`Base Price: ₹${plan.amount}`);
console.log(`GST (${plan.gst_percentage}%): ₹${plan.gst_amount / 100}`);
console.log(`Total: ₹${plan.amount_with_gst / 100}`);
```

## Subscription Creation

When creating subscriptions, the backend automatically calculates GST and includes it in the Razorpay subscription notes:

```javascript
// POST /api/subscriptions/create
const response = await fetch('/api/subscriptions/create', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    plan_id: 'plan_Qjrw31WPrhunxz',
    user_id: 123
  })
});

const data = await response.json();
console.log('Amount Details:', data.amount_details);
// {
//   base_amount: 200,
//   gst_amount: 36,
//   total_amount: 236,
//   gst_percentage: 18
// }
```

## Database Storage

GST details are stored in the subscription notes for audit purposes:

```json
{
  "user_id": "123",
  "subscription_type": "user_premium",
  "base_amount": 200,
  "gst_amount": 36,
  "total_amount": 236,
  "gst_percentage": 18
}
```

## Utility Functions

The GST calculation is centralized in `utils/gstCalculator.js`:

```javascript
const { calculateGST, formatGSTForAPI, addGSTToPlans } = require('../utils/gstCalculator');

// Calculate GST for a single amount
const gstDetails = calculateGST(200);
// { base_amount: 200, gst_amount: 36, total_amount: 236, ... }

// Add GST to all plans
const plansWithGST = addGSTToPlans(plans);
```

## Testing

### Test Plans
For testing purposes, use the test endpoints:

- `/api/subscription-plans-test` - Test plans without GST
- `/api/subscription-plans-test-with-gst` - Test plans with GST

### GST Verification
To verify GST calculations:

```javascript
// Example verification
const baseAmount = 200;
const expectedGST = baseAmount * 0.18; // 36
const expectedTotal = baseAmount + expectedGST; // 236

// Verify the calculation matches
console.log(`Base: ₹${baseAmount}`);
console.log(`GST: ₹${expectedGST}`);
console.log(`Total: ₹${expectedTotal}`);
```

## Migration Notes

### Existing Subscriptions
- Existing subscriptions in the database remain unchanged
- New subscriptions will include GST details in notes
- Webhook handlers can access GST information from subscription notes

### Frontend Updates Required
1. Update payment flow to use GST-enabled endpoints
2. Display price breakdown (Base + GST = Total)
3. Use `amount_with_gst` for Razorpay integration
4. Keep UI display using original `amount` field

### Backend Updates
1. ✅ GST utility functions created
2. ✅ New API endpoints added
3. ✅ Subscription creation updated
4. ✅ Webhook handlers can access GST details

## Error Handling

The system includes proper error handling for GST calculations:

```javascript
try {
  const gstDetails = formatGSTForAPI(baseAmount);
  // Use gstDetails for payment processing
} catch (error) {
  console.error('GST calculation error:', error);
  // Fallback to base amount or show error
}
```

## Compliance

- GST percentage is configurable in `utils/gstCalculator.js`
- All GST calculations are logged for audit purposes
- Amount breakdown is stored in subscription notes
- Tax invoices can be generated using stored GST details 