-- Fix promotional_price data type in reels table
-- Change from INT to DECIMAL(10,2) for proper currency handling

-- Check current data type
-- SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_NAME = 'reels' AND COLUMN_NAME = 'promotional_price';

-- Alter the column to use proper decimal type for currency
ALTER TABLE reels 
MODIFY COLUMN promotional_price DECIMAL(10,2) DEFAULT NULL 
COMMENT 'Price per view in INR (supports up to 99999999.99)';

-- Verify the change
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'reels' AND COLUMN_NAME = 'promotional_price';
