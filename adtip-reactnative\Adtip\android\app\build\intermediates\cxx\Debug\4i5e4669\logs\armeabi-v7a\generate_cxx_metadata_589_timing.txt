# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    exec-prefab 731ms
    [gap of 218ms]
  generate-prefab-packages completed in 956ms
  execute-generate-process
    [gap of 61ms]
    exec-configure 7540ms
    [gap of 140ms]
  execute-generate-process completed in 7741ms
  [gap of 27ms]
  remove-unexpected-so-files 29ms
  [gap of 98ms]
  write-metadata-json-to-file 10ms
  [gap of 10ms]
generate_cxx_metadata completed in 8939ms

