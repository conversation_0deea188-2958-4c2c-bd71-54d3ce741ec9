const mysql = require('mysql2/promise');
require('dotenv').config();

const config = require('./config/appenvconfig.js');

async function testConnection() {
  try {
    console.log('Testing database connection...');
    console.log('Config:', {
      host: config.database.host,
      user: config.database.user,
      database: config.database.database,
      port: config.database.port
    });
    
    const connection = await mysql.createConnection({
      host: config.database.host,
      user: config.database.user,
      password: config.database.password,
      database: config.database.database,
      port: config.database.port || 3306
    });
    
    console.log('✅ Database connection successful!');
    
    // Test a simple query
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM user_chat');
    console.log('✅ Query test successful. Current user_chat records:', rows[0].count);

    // Check if new tables exist
    try {
      const [convRows] = await connection.execute('SELECT COUNT(*) as count FROM conversations');
      console.log('✅ New conversations table exists with', convRows[0].count, 'records');

      const [msgRows] = await connection.execute('SELECT COUNT(*) as count FROM messages');
      console.log('✅ New messages table exists with', msgRows[0].count, 'records');

      const [partRows] = await connection.execute('SELECT COUNT(*) as count FROM conversation_participants');
      console.log('✅ New participants table exists with', partRows[0].count, 'records');

    } catch (error) {
      console.log('⚠️  New chat tables not found - migration may not have run yet');
    }
    
    await connection.end();
    console.log('✅ Connection closed successfully');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Error details:', error);
  }
}

testConnection();
