# months
M(a)_1=қаң.
M(a)_2=ақп.
M(a)_3=нау.
M(a)_4=сәу.
M(a)_5=мам.
M(a)_6=мау.
M(a)_7=шіл.
M(a)_8=там.
M(a)_9=қыр.
M(a)_10=қаз.
M(a)_11=қар.
M(a)_12=жел.

M(n)_1=Қ
M(n)_2=А
M(n)_3=Н
M(n)_4=С
M(n)_5=М
M(n)_6=М
M(n)_7=Ш
M(n)_8=Т
M(n)_9=Қ
M(n)_10=Қ
M(n)_11=Қ
M(n)_12=Ж

M(w)_1=қаңтар
M(w)_2=ақпан
M(w)_3=наурыз
M(w)_4=сәуір
M(w)_5=мамыр
M(w)_6=маусым
M(w)_7=шілде
M(w)_8=тамыз
M(w)_9=қыркүйек
M(w)_10=қазан
M(w)_11=қараша
M(w)_12=желтоқсан

M(A)_1=қаң.
M(A)_2=ақп.
M(A)_3=нау.
M(A)_4=сәу.
M(A)_5=мам.
M(A)_6=мау.
M(A)_7=шіл.
M(A)_8=там.
M(A)_9=қыр.
M(A)_10=қаз.
M(A)_11=қар.
M(A)_12=жел.

M(N)_1=Қ
M(N)_2=А
M(N)_3=Н
M(N)_4=С
M(N)_5=М
M(N)_6=М
M(N)_7=Ш
M(N)_8=Т
M(N)_9=Қ
M(N)_10=Қ
M(N)_11=Қ
M(N)_12=Ж

M(W)_1=Қаңтар
M(W)_2=Ақпан
M(W)_3=Наурыз
M(W)_4=Сәуір
M(W)_5=Мамыр
M(W)_6=Маусым
M(W)_7=Шілде
M(W)_8=Тамыз
M(W)_9=Қыркүйек
M(W)_10=Қазан
M(W)_11=Қараша
M(W)_12=Желтоқсан

# weekdays
D(a)_1=дс
D(a)_2=сс
D(a)_3=ср
D(a)_4=бс
D(a)_5=жм
D(a)_6=сб
D(a)_7=жс

D(n)_1=Д
D(n)_2=С
D(n)_3=С
D(n)_4=Б
D(n)_5=Ж
D(n)_6=С
D(n)_7=Ж

D(s)_1=дс
D(s)_2=сс
D(s)_3=ср
D(s)_4=бс
D(s)_5=жм
D(s)_6=сб
D(s)_7=жс

D(w)_1=дүйсенбі
D(w)_2=сейсенбі
D(w)_3=сәрсенбі
D(w)_4=бейсенбі
D(w)_5=жұма
D(w)_6=сенбі
D(w)_7=жексенбі

D(A)_1=дс
D(A)_2=сс
D(A)_3=ср
D(A)_4=бс
D(A)_5=жм
D(A)_6=сб
D(A)_7=жс

D(N)_1=Д
D(N)_2=С
D(N)_3=С
D(N)_4=Б
D(N)_5=Ж
D(N)_6=С
D(N)_7=Ж

D(S)_1=дс
D(S)_2=сс
D(S)_3=ср
D(S)_4=бс
D(S)_5=жм
D(S)_6=сб
D(S)_7=жс

D(W)_1=дүйсенбі
D(W)_2=сейсенбі
D(W)_3=сәрсенбі
D(W)_4=бейсенбі
D(W)_5=жұма
D(W)_6=сенбі
D(W)_7=жексенбі

# quarters
Q(a)_1=І тқс.
Q(a)_2=ІІ тқс.
Q(a)_3=ІІІ тқс.
Q(a)_4=IV тқс.

Q(n)_1=I
Q(n)_2=II
Q(n)_3=III
Q(n)_4=IV

Q(w)_1=І тоқсан
Q(w)_2=ІІ тоқсан
Q(w)_3=ІІІ тоқсан
Q(w)_4=IV тоқсан

Q(A)_1=І тқс.
Q(A)_2=ІІ тқс.
Q(A)_3=ІІІ тқс.
Q(A)_4=IV тқс.

Q(N)_1=I
Q(N)_2=II
Q(N)_3=III
Q(N)_4=IV

Q(W)_1=І тоқсан
Q(W)_2=ІІ тоқсан
Q(W)_3=ІІІ тоқсан
Q(W)_4=IV тоқсан

# day-period-rules
T0600=morning1
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=түн жарымы
P(a)_am=AM
P(a)_noon=түскі
P(a)_pm=PM
P(a)_morning1=таңғы
P(a)_afternoon1=түстен кейінгі
P(a)_evening1=кешкі
P(a)_night1=түнгі

P(n)_midnight=түнгі
P(n)_am=AM
P(n)_noon=түскі
P(n)_pm=PM
P(n)_morning1=таңғы
P(n)_afternoon1=түстен кейінгі
P(n)_evening1=кешкі
P(n)_night1=түнгі

P(w)_midnight=түн жарымы
P(w)_am=AM
P(w)_noon=түскі
P(w)_pm=PM
P(w)_morning1=таңғы
P(w)_afternoon1=түстен кейінгі
P(w)_evening1=кешкі
P(w)_night1=түнгі

P(A)_midnight=түн жарымы
P(A)_am=AM
P(A)_noon=талтүс
P(A)_pm=PM
P(A)_morning1=таң
P(A)_afternoon1=түстен кейін
P(A)_evening1=кеш
P(A)_night1=түн

P(N)_midnight=түн жарымы
P(N)_am=AM
P(N)_noon=талтүс
P(N)_pm=PM
P(N)_morning1=таң
P(N)_afternoon1=түстен кейін
P(N)_evening1=кеш
P(N)_night1=түн

P(W)_midnight=түн жарымы
P(W)_am=AM
P(W)_noon=талтүс
P(W)_pm=PM
P(W)_morning1=таң
P(W)_afternoon1=түстен кейін
P(W)_evening1=кеш
P(W)_night1=түн

# eras
E(w)_0=Біздің заманымызға дейін
E(w|alt)_0=біздің заманымызға дейін
E(w)_1=біздің заманымыз
E(w|alt)_1=Біздің заманымыз

E(a)_0=б.з.д.
E(a|alt)_0=BCE
E(a)_1=б.з.
E(a|alt)_1=CE

# format patterns
F(f)_d=y 'ж'. d MMMM, EEEE
F(l)_d=y 'ж'. d MMMM
F(m)_d=y 'ж'. dd MMM
F(s)_d=dd.MM.yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=B h
F_Bhm=B h:mm
F_Bhms=B h:mm:ss
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd.MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM.y
F_yMMM=y 'ж'. MMM
F_yMMMM=y 'ж'. MMMM
F_yQQQ=y 'ж'. QQQ
F_yQQQQ=y 'ж'. QQQQ
F_yw=Y 'жылдың' w-'аптасы'

I={0} - {1}

# labels of elements
L_era=дәуір
L_year=жыл
L_quarter=ширек
L_month=ай
L_week=апта
L_day=күн
L_weekday=апта күні
L_dayperiod=АМ/РМ
L_hour=сағат
L_minute=минут
L_second=секунд
L_zone=уақыт белдеуі
