-- Analytics tables for tracking user events and video analytics

-- Table to store all analytics events
CREATE TABLE IF NOT EXISTS analytics_events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_name VARCHAR(100) NOT NULL,
    user_id INT NULL,
    event_data JSON NULL,
    event_timestamp TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_event_name (event_name),
    INDEX idx_user_id (user_id),
    INDEX idx_event_timestamp (event_timestamp),
    INDEX idx_created_at (created_at),
    INDEX idx_event_user_time (event_name, user_id, created_at)
);

-- Table to store video analytics summary
CREATE TABLE IF NOT EXISTS video_analytics_summary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    video_id INT NOT NULL,
    total_views INT DEFAULT 0,
    paid_views INT DEFAULT 0,
    normal_views INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Unique constraint to prevent duplicates
    UNIQUE KEY unique_video (video_id),
    
    -- Indexes for performance
    INDEX idx_video_id (video_id),
    INDEX idx_total_views (total_views),
    INDEX idx_last_updated (last_updated),
    
    -- Foreign key constraint (if reels table exists)
    FOREIGN KEY (video_id) REFERENCES reels(id) ON DELETE CASCADE
);

-- Table to store daily analytics aggregations
CREATE TABLE IF NOT EXISTS daily_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    analytics_date DATE NOT NULL,
    event_name VARCHAR(100) NOT NULL,
    user_id INT NULL,
    event_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint for daily aggregations
    UNIQUE KEY unique_daily_event (analytics_date, event_name, user_id),
    
    -- Indexes for performance
    INDEX idx_analytics_date (analytics_date),
    INDEX idx_event_name (event_name),
    INDEX idx_user_id (user_id),
    INDEX idx_date_event (analytics_date, event_name)
);

-- Table to store user session analytics
CREATE TABLE IF NOT EXISTS user_session_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_end TIMESTAMP NULL,
    session_duration INT NULL COMMENT 'Duration in seconds',
    videos_watched INT DEFAULT 0,
    paid_videos_watched INT DEFAULT 0,
    ads_viewed INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_user_id (user_id),
    INDEX idx_session_start (session_start),
    INDEX idx_session_end (session_end),
    INDEX idx_user_session_start (user_id, session_start)
);

-- Display success message
SELECT 'Analytics tables created successfully!' as message;
