let AdminService = require("../services/AdminService");

module.exports = {
  getAdminAccessList: (req, res, next) => {
    AdminService.getAdminAccessList()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveAdmin: (req, res, next) => {
    AdminService.saveAdmin(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAllAdminList: (req, res, next) => {
    if (!req.params.createdby)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    AdminService.getAllAdminList(req.params.createdby)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateAdmin: (req, res, next) => {
    AdminService.updateAdmin(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  deleteAdmin: (req, res, next) => {
    AdminService.deleteAdmin(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getAllIndustryList: (req, res, next) => {
    AdminService.getAllIndustryList()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  authenticate: (req, res, next) => {
    if (!req.body.user_name)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    AdminService.getUserByName(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getallusers: (req, res, next) => {
    AdminService.getAllusers(req.params.page, req.params.limit)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getallChannels: (req, res, next) => {
    AdminService.getAllChannels(req.params.page, req.params.limit)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getwithdrawRequest: (req, res, next) => {
    AdminService.getAllwithdrawRequest(req.params.page, req.params.limit)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updteVerifyUser: (req, res, next) => {
    console.log(req.body);
    AdminService.updateVerifyUser(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.log(err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addUser: (req, res, next) => {
    console.log(req.body);
    AdminService.addUser(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.log(err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAllAdminUsers: (req, res, next) => {
    AdminService.getAllAdminUsers(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAllQrWebUniqueCompanyId: (req, res, next) => {
    AdminService.getAllQrWebUniqueCompanyId(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getQrUnpaidDataByCompanyId: (req, res, next) => {
    AdminService.getQrUnpaidDataByCompanyId(
      req.params.page,
      req.params.limit,
      req.params.id
    )
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getQrPaidDataByCompanyId: (req, res, next) => {
    AdminService.getQrPaidDataByCompanyId(
      req.params.page,
      req.params.limit,
      req.params.id
    )
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  changeQrWebUnpaidToPaid: (req, res, next) => {
    AdminService.changeQrWebUnpaidToPaid(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getDemoList: (req, res, next) => {
    AdminService.getDemoList(req.params.page, req.params.limit)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  //getReferalUnpaid
  //changeUnpaidToPaidReferal
  //getReferalPaid
  //getChannelUnpaid
  //changeUnpaidToPaidChannel
  //getChannelPaid
  getReferalUnpaid: (req, res, next) => {
    AdminService.getReferalUnpaid(req.params.page)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getReferalPaid: (req, res, next) => {
    AdminService.getReferalPaid(req.params.page)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  changeUnpaidToPaidReferal: (req, res, next) => {
    AdminService.changeUnpaidToPaidReferal(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getChannelUnpaid: (req, res, next) => {
    AdminService.getChannelUnpaid(req.params.page)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getChannelPaid: (req, res, next) => {
    AdminService.getChannelPaid(req.params.page)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  changeUnpaidToPaidChannel: (req, res, next) => {
    AdminService.changeUnpaidToPaidChannel(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAdOrder: (req, res, next) => {
    AdminService.getAdOrder(req.params.page, req.params.limit)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAllVideos: (req, res, next) => {
    return AdminService.getAllVideos(req.params.page)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getVideoBySearch: (req, res, next) => {
    return AdminService.getVideoBySearch(req.params.search)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deleteVideoById: (req, res, next) => {
    return AdminService.deleteVideoById(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  ///ecommerce
  //getAllProducts
  getAllOrders: (req, res, next) => {
    AdminService.getAllOrders(req.params.page)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAllProducts: (req, res, next) => {
    AdminService.getAllProducts(req.params.page)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  verifyCompany: (req, res, next) => {
    AdminService.verifyCompany(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  // ==================== WITHDRAWAL MANAGEMENT METHODS ====================
  
  getWalletWithdrawals: async (req, res, next) => {
    try {
      const filters = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        userId: req.query.userId,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
        premium: req.query.premium,
      };
      const result = await AdminService.getWalletWithdrawals(filters);
      res.status(200).send({ status: true, message: 'Wallet withdrawals fetched', data: result });
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  updateWalletWithdrawalStatus: async (req, res, next) => {
    try {
      const withdrawalId = req.params.id;
      const { status, adminNotes, adminId } = req.body;
      const result = await AdminService.updateWalletWithdrawalStatus(withdrawalId, status, adminNotes, adminId);
      res.status(200).send({ status: true, message: 'Wallet withdrawal status updated', data: result });
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  exportWalletWithdrawals: async (req, res, next) => {
    try {
      const filters = {
        status: req.query.status,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
      };
      const result = await AdminService.exportWalletWithdrawals(filters);
      res.setHeader('Content-disposition', `attachment; filename=${result.data.filename}`);
      res.set('Content-Type', 'text/csv');
      res.status(200).send(result.data.csvData);
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  getReferralWithdrawals: async (req, res, next) => {
    try {
      const filters = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        userId: req.query.userId,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
        premium: req.query.premium,
      };
      const result = await AdminService.getReferralWithdrawals(filters);
      res.status(200).send({ status: true, message: 'Referral withdrawals fetched', data: result });
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  updateReferralWithdrawalStatus: async (req, res, next) => {
    try {
      const withdrawalId = req.params.id;
      const { status, adminNotes, adminId } = req.body;
      const result = await AdminService.updateReferralWithdrawalStatus(withdrawalId, status, adminNotes, adminId);
      res.status(200).send({ status: true, message: 'Referral withdrawal status updated', data: result });
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  exportReferralWithdrawals: async (req, res, next) => {
    try {
      const filters = {
        status: req.query.status,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
      };
      const result = await AdminService.exportReferralWithdrawals(filters);
      res.setHeader('Content-disposition', `attachment; filename=${result.data.filename}`);
      res.set('Content-Type', 'text/csv');
      res.status(200).send(result.data.csvData);
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  getCouponWithdrawals: async (req, res, next) => {
    try {
      const filters = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        userId: req.query.userId,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
        premium: req.query.premium,
      };
      const result = await AdminService.getCouponWithdrawals(filters);
      res.status(200).send({ status: true, message: 'Coupon withdrawals fetched', data: result });
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  updateCouponWithdrawalStatus: async (req, res, next) => {
    try {
      const withdrawalId = req.params.id;
      const { status, adminNotes, adminId } = req.body;
      const result = await AdminService.updateCouponWithdrawalStatus(withdrawalId, status, adminNotes, adminId);
      res.status(200).send({ status: true, message: 'Coupon withdrawal status updated', data: result });
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  exportCouponWithdrawals: async (req, res, next) => {
    try {
      const filters = {
        status: req.query.status,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
      };
      const result = await AdminService.exportCouponWithdrawals(filters);
      res.setHeader('Content-disposition', `attachment; filename=${result.data.filename}`);
      res.set('Content-Type', 'text/csv');
      res.status(200).send(result.data.csvData);
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  getChannelWithdrawals: async (req, res, next) => {
    try {
      const filters = {
        page: req.query.page,
        limit: req.query.limit,
        status: req.query.status,
        userId: req.query.userId,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
        premium: req.query.premium,
      };
      const result = await AdminService.getChannelWithdrawals(filters);
      res.status(200).send({ status: true, message: 'Channel withdrawals fetched', data: result });
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  updateChannelWithdrawalStatus: async (req, res, next) => {
    try {
      const withdrawalId = req.params.id;
      const { status, adminNotes, adminId } = req.body;
      const result = await AdminService.updateChannelWithdrawalStatus(withdrawalId, status, adminNotes, adminId);
      res.status(200).send({ status: true, message: 'Channel withdrawal status updated', data: result });
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },

  exportChannelWithdrawals: async (req, res, next) => {
    try {
      const filters = {
        status: req.query.status,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
      };
      const result = await AdminService.exportChannelWithdrawals(filters);
      res.setHeader('Content-disposition', `attachment; filename=${result.data.filename}`);
      res.set('Content-Type', 'text/csv');
      res.status(200).send(result.data.csvData);
    } catch (err) {
      res.status(500).send({ status: false, message: err.message || 'Internal server error', data: [] });
    }
  },
};


