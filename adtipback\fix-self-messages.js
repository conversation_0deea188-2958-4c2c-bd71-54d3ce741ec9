/**
 * Fix Self-Messages Script
 * 
 * This script identifies and fixes messages where sender_id equals recipient_id
 * (self-messages) which should not exist in a proper chat system.
 */

const queryRunner = require('./dbConfig/queryRunner');

async function fixSelfMessages() {
  console.log('🔧 Starting Self-Messages Fix...\n');

  try {
    // Step 1: Identify problematic messages
    console.log('📋 Step 1: Identifying messages with same sender_id and recipient_id...');
    const selfMessages = await queryRunner.queryRunner(`
      SELECT id, chat_id, sender_id, recipient_id, content, created_at
      FROM messages 
      WHERE sender_id = recipient_id
      ORDER BY created_at DESC
    `);

    if (selfMessages.length === 0) {
      console.log('✅ No self-messages found. System is clean!');
      return true;
    }

    console.log(`❌ Found ${selfMessages.length} self-messages:`);
    selfMessages.forEach(msg => {
      console.log(`   Message ID: ${msg.id}, Chat: ${msg.chat_id}, User: ${msg.sender_id}, Content: "${msg.content.substring(0, 30)}..."`);
    });
    console.log('');

    // Step 2: Analyze the problematic messages
    console.log('📋 Step 2: Analyzing self-messages...');
    
    for (const msg of selfMessages) {
      console.log(`\n🔍 Analyzing Message ID ${msg.id}:`);
      console.log(`   Chat ID: ${msg.chat_id}`);
      console.log(`   User ID: ${msg.sender_id}`);
      console.log(`   Content: "${msg.content}"`);
      console.log(`   Created: ${msg.created_at}`);

      // Check if this is a malformed chat_id
      if (msg.chat_id.startsWith('chat_')) {
        const parts = msg.chat_id.split('_');
        if (parts.length === 3 && parts[1] === parts[2]) {
          console.log(`   ❌ Malformed chat_id: ${msg.chat_id} (same user ID twice)`);
          console.log(`   🗑️  Recommendation: DELETE this message (invalid self-chat)`);
        }
      }
    }

    // Step 3: Offer to delete the problematic messages
    console.log('\n📋 Step 3: Cleanup Options');
    console.log('==========================');
    
    console.log('🗑️  Option 1: DELETE all self-messages (RECOMMENDED)');
    console.log('   - These messages represent invalid self-chats');
    console.log('   - They should not exist in a proper chat system');
    console.log('   - Deleting them will clean up the database');
    
    console.log('\n⚠️  Option 2: KEEP messages (NOT RECOMMENDED)');
    console.log('   - Messages will remain in database');
    console.log('   - May cause confusion in chat UI');
    console.log('   - Verification will continue to fail');

    // For automated cleanup, we'll delete the messages
    console.log('\n🤖 Proceeding with automated cleanup...');
    
    // Delete self-messages
    const deleteResult = await queryRunner.queryRunner(`
      DELETE FROM messages 
      WHERE sender_id = recipient_id
    `);

    console.log(`✅ Deleted ${deleteResult.affectedRows} self-messages`);

    // Step 4: Verify cleanup
    console.log('\n📋 Step 4: Verifying cleanup...');
    const remainingSelfMessages = await queryRunner.queryRunner(`
      SELECT COUNT(*) as count
      FROM messages 
      WHERE sender_id = recipient_id
    `);

    if (remainingSelfMessages[0].count === 0) {
      console.log('✅ Cleanup successful! No self-messages remain.');
      return true;
    } else {
      console.log(`❌ Cleanup incomplete. ${remainingSelfMessages[0].count} self-messages still exist.`);
      return false;
    }

  } catch (error) {
    console.error('❌ Fix failed:', error);
    return false;
  }
}

// Run fix if called directly
if (require.main === module) {
  fixSelfMessages()
    .then(success => {
      console.log('\n🏁 Self-messages fix completed:', success ? 'SUCCESS' : 'FAILED');
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Fix crashed:', error);
      process.exit(1);
    });
}

module.exports = { fixSelfMessages };
