import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "../contexts/AuthContext";
import { logDebug, logWarn, logError } from "../utils/ProductionLogger";
import { Post } from "../types/api";
import CategoryBar from "../components/CategoryBar";
import PostsList from "../components/PostsList";
import { BannerAdComponent } from "../components/ads";
import { usePosts, usePremiumPosts, useWalletBalance } from "../hooks/useApi";
import TanStackQueryTest from "../components/TanStackQueryTest";

// TypeScript interfaces are now imported from types/api.ts

const popularCategories = [
  { name: "All", id: 0 },
  { name: "<PERSON>", id: 9 },
  { name: "Beauty", id: 10 },
  { name: "Business", id: 11 },
  { name: "Fashion", id: 12 },
  { name: "Fitness", id: 14 },
  { name: "Food", id: 15 },
  { name: "Gaming", id: 16 },
  { name: "Music", id: 17 },
  { name: "Tech", id: 20 },
  { name: "Travel", id: 21 },
];

// TODO: Implement banner functionality
// const bannerData = [
//   {
//     title: "Watch & Earn",
//     description: "Earn rewards by watching videos",
//     gradient: "from-[#7F7FD5] via-[#86A8E7] to-[#91EAE4]",
//     icon: "🎬",
//   },
//   {
//     title: "Play & Earn",
//     description: "Earn money by playing games",
//     gradient: "from-[#43e97b] via-[#38f9d7] to-[#38f9d7]",
//     icon: "🎮",
//   },
//   {
//     title: "Refer & Earn",
//     description: "Invite friends and earn bonuses",
//     gradient: "from-[#f7971e] via-[#ffd200] to-[#f7971e]",
//     icon: "🤝",
//   },
// ];

/*const BannerCarousel = ({ userId, isAuthenticated }: { userId: string | number | null, isAuthenticated: boolean }) => {
  const [current, setCurrent] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      setCurrent((prev) => (prev + 1) % bannerData.length);
    }, 4000);
    return () => { if (timeoutRef.current) clearTimeout(timeoutRef.current); };
  }, [current]);
  const handleEarnClick = () => {
    if (isAuthenticated && userId) {
      window.open(`https://wow.pubscale.com/?app_id=39604779&user_id=${userId}`, "_blank");
    } else {
      window.location.href = "/login";
    }
  };

  return (
    <div className="relative w-full max-w-2xl mx-auto mb-6">
      <div
        className={`rounded-2xl p-6 flex items-center justify-between shadow-lg bg-gradient-to-r ${bannerData[current].gradient} transition-all duration-700`}
      >
        <div>
          <div className="text-3xl mb-2">{bannerData[current].icon}</div>
          <h3 className="font-bold text-lg mb-1 text-white drop-shadow">{bannerData[current].title}</h3>
          <p className="text-white/90 text-sm mb-3 drop-shadow">{bannerData[current].description}</p>
          <button
            onClick={handleEarnClick}
            className="px-6 py-2 rounded-full font-bold text-white bg-gradient-to-r from-[#ff512f] to-[#dd2476] shadow-lg hover:scale-105 active:scale-95 transition-transform"
          >
            Earn
          </button>
        </div>
      </div>
      <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-2">
        {bannerData.map((_, idx) => (
          <span
            key={idx}
            className={`w-2 h-2 rounded-full ${idx === current ? "bg-white/90" : "bg-white/40"}`}
          />
        ))}
      </div>
    </div>
  );
};*/

const Home = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>("All");
  const [postViewCount, setPostViewCount] = useState<number>(0);
  const { isAuthenticated, user } = useAuth();

  const userId = user?.id || null;
  const userIdString = userId ? userId.toString() : "";

  // Get category ID
  const categoryObj = popularCategories.find((cat) => cat.name === selectedCategory);
  const categoryId = categoryObj ? categoryObj.id : 0;

  // TanStack Query hooks
  const {
    data: postsData,
    isLoading: postsLoading,
    error: postsError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = usePosts(categoryId, userId || undefined, { enabled: isAuthenticated });

  const {
    data: premiumPostsData,
    isLoading: premiumPostsLoading,
    error: premiumPostsError,
  } = usePremiumPosts({ enabled: !isAuthenticated });

  const {
    data: walletData,
    isLoading: walletLoading,
  } = useWalletBalance(userIdString, { enabled: isAuthenticated && !!userId });

  // Transform posts data
  const feedData = useMemo(() => {
    if (!isAuthenticated) {
      return premiumPostsData?.data || [];
    }
    return postsData?.pages?.flatMap(page => page?.data || []) || [];
  }, [isAuthenticated, postsData, premiumPostsData]);

  const loading = isAuthenticated ? postsLoading : premiumPostsLoading;
  const error = isAuthenticated ? postsError : premiumPostsError;
  const hasMore = isAuthenticated ? hasNextPage : false;

  // Log component rendering and authentication status
  useEffect(() => {
    logDebug("Home", "Rendering Home component", {
      isAuthenticated,
      userId,
      selectedCategory,
    });
  }, [isAuthenticated, userId, selectedCategory]);

  // Handle load more posts
  const handleLoadMore = () => {
    if (hasMore && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  // Handle post click
  const handlePostClick = (postId: number) => {
    setPostViewCount(prev => prev + 1);
    // Navigate to post detail or handle post interaction
    logDebug("Home", "Post clicked", { postId, viewCount: postViewCount + 1 });
  };

  // Check if user is viewing posts and prompt login
  useEffect(() => {
    if (!isAuthenticated && postViewCount >= 2) {
      // TODO: Implement login prompt
      // setShowLoginPrompt(true);
      logDebug("Home", "User should be prompted to login", { postViewCount });
    }
  }, [postViewCount, isAuthenticated]);

  // Category selection handler
  const handleCategorySelect = (categoryName: string) => {
    setSelectedCategory(categoryName);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Categories Bar */}
      <CategoryBar
        categories={popularCategories}
        selectedCategory={selectedCategory}
        onCategorySelect={handleCategorySelect}
      />

      {/* Main content */}
      <div className="max-w-screen-md mx-auto px-4 py-4">
        <Tabs defaultValue="for-you" className="mb-6">
          <TabsList className="grid grid-cols-2 w-full">
            <TabsTrigger value="for-you">
              For You
            </TabsTrigger>
            <TabsTrigger value="following">
              Following
            </TabsTrigger>
          </TabsList>

          <TabsContent value="for-you">
            {/* TanStack Query Test Component */}
            <div className="mb-6">
              <TanStackQueryTest />
            </div>

            {/* Top Banner Ad */}
            <div className="mb-6 flex justify-center">
              <BannerAdComponent size="leaderboard" />
            </div>

            {/* Posts List with all states handled */}
            <PostsList
              posts={feedData as any[]}
              loading={loading}
              error={error?.message || null}
              hasMore={hasMore}
              onPostClick={handlePostClick}
              onLoadMore={handleLoadMore}
            />

            {/* Bottom Banner Ad */}
            {feedData.length > 0 && (
              <div className="mt-6 flex justify-center">
                <BannerAdComponent size="banner" />
              </div>
            )}
          </TabsContent>

          <TabsContent value="following">
            <div className="text-center py-10">
              <h3 className="text-xl font-semibold mb-4">
                Start following creators
              </h3>
              <p className="text-gray-500 mb-6">
                Follow creators to see their content in your feed
              </p>
              <Link to="/discover">
                <Button className="teal-button">Discover Creators</Button>
              </Link>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      {/* Google Play Store Banner - fixed bottom right, desktop only */}
      <div
        className="hidden md:flex fixed z-40 bottom-6 right-6 items-center gap-0 select-none"
        style={{ pointerEvents: 'auto' }}
      >
        <a
          href="https://play.google.com/store/apps/details?id=com.adtip.app.adtip_app&hl=en_IN"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center group"
          style={{ textDecoration: 'none' }}
        >
          <div
            className="rounded-l-2xl pl-5 pr-4 py-3 flex items-center bg-gradient-to-r from-[#e0e7ef] via-[#d1f1e6] to-[#f7e7fa] shadow-lg border border-gray-200 hover:from-[#d1e7f7] hover:to-[#e7f7e7] transition-colors duration-300"
            style={{ minWidth: 120 }}
          >
            <span className="font-semibold text-gray-700 text-base tracking-wide drop-shadow-sm mr-2">Install now</span>
          </div>
          <div
            className="rounded-r-2xl bg-white p-2 pl-1 pr-3 flex items-center shadow-lg border-t border-b border-r border-gray-200 hover:bg-gray-50 transition-colors duration-300"
          >
            <img
              src="/playstore.png"
              alt="Google Play Store"
              className="w-8 h-8 object-contain mr-1"
              style={{ filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.08))' }}
            />
          </div>
        </a>
      </div>

      {/* Google Play Store Logo - fixed bottom right, mobile only */}
      <a
        href="https://play.google.com/store/apps/details?id=com.adtip.app.adtip_app&hl=en_IN"
        target="_blank"
        rel="noopener noreferrer"
        className="flex md:hidden fixed z-40 right-5 items-center select-none"
        style={{ pointerEvents: 'auto', bottom: '10%' }}
      >
        <img
          src="/playstore.png"
          alt="Google Play Store"
          className="w-14 h-14 object-contain drop-shadow-lg rounded-2xl border border-gray-200 bg-white p-2"
        />
      </a>
    </div>
  );
};

export default Home;