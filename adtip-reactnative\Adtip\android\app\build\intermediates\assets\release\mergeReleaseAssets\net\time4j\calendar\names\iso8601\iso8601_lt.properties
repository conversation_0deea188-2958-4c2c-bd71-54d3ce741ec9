# months
M(a)_1=saus.
M(a)_2=vas.
M(a)_3=kov.
M(a)_4=bal.
M(a)_5=geg.
M(a)_6=birž.
M(a)_7=liep.
M(a)_8=rugp.
M(a)_9=rugs.
M(a)_10=spal.
M(a)_11=lapkr.
M(a)_12=gruod.

M(n)_1=S
M(n)_2=V
M(n)_3=K
M(n)_4=B
M(n)_5=G
M(n)_6=B
M(n)_7=L
M(n)_8=R
M(n)_9=R
M(n)_10=S
M(n)_11=L
M(n)_12=G

M(w)_1=sausio
M(w)_2=vasario
M(w)_3=kovo
M(w)_4=balandžio
M(w)_5=gegužės
M(w)_6=birželio
M(w)_7=liepos
M(w)_8=rugpjūčio
M(w)_9=rugs<PERSON><PERSON>
M(w)_10=spalio
M(w)_11=lapkričio
M(w)_12=gruodžio

M(A)_1=saus.
M(A)_2=vas.
M(A)_3=kov.
M(A)_4=bal.
M(A)_5=geg.
M(A)_6=birž.
M(A)_7=liep.
M(A)_8=rugp.
M(A)_9=rugs.
M(A)_10=spal.
M(A)_11=lapkr.
M(A)_12=gruod.

M(N)_1=S
M(N)_2=V
M(N)_3=K
M(N)_4=B
M(N)_5=G
M(N)_6=B
M(N)_7=L
M(N)_8=R
M(N)_9=R
M(N)_10=S
M(N)_11=L
M(N)_12=G

M(W)_1=sausis
M(W)_2=vasaris
M(W)_3=kovas
M(W)_4=balandis
M(W)_5=gegužė
M(W)_6=birželis
M(W)_7=liepa
M(W)_8=rugpjūtis
M(W)_9=rugsėjis
M(W)_10=spalis
M(W)_11=lapkritis
M(W)_12=gruodis

# weekdays
D(a)_1=pr
D(a)_2=an
D(a)_3=tr
D(a)_4=kt
D(a)_5=pn
D(a)_6=št
D(a)_7=sk

D(n)_1=P
D(n)_2=A
D(n)_3=T
D(n)_4=K
D(n)_5=P
D(n)_6=Š
D(n)_7=S

D(s)_1=Pr
D(s)_2=An
D(s)_3=Tr
D(s)_4=Kt
D(s)_5=Pn
D(s)_6=Št
D(s)_7=Sk

D(w)_1=pirmadienis
D(w)_2=antradienis
D(w)_3=trečiadienis
D(w)_4=ketvirtadienis
D(w)_5=penktadienis
D(w)_6=šeštadienis
D(w)_7=sekmadienis

D(A)_1=pr
D(A)_2=an
D(A)_3=tr
D(A)_4=kt
D(A)_5=pn
D(A)_6=št
D(A)_7=sk

D(N)_1=P
D(N)_2=A
D(N)_3=T
D(N)_4=K
D(N)_5=P
D(N)_6=Š
D(N)_7=S

D(S)_1=Pr
D(S)_2=An
D(S)_3=Tr
D(S)_4=Kt
D(S)_5=Pn
D(S)_6=Št
D(S)_7=Sk

D(W)_1=pirmadienis
D(W)_2=antradienis
D(W)_3=trečiadienis
D(W)_4=ketvirtadienis
D(W)_5=penktadienis
D(W)_6=šeštadienis
D(W)_7=sekmadienis

# quarters
Q(a)_1=I k.
Q(a)_2=II k.
Q(a)_3=III k.
Q(a)_4=IV k.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=I ketvirtis
Q(w)_2=II ketvirtis
Q(w)_3=III ketvirtis
Q(w)_4=IV ketvirtis

Q(A)_1=I ketv.
Q(A)_2=II ketv.
Q(A)_3=III ketv.
Q(A)_4=IV ketv.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=I ketvirtis
Q(W)_2=II ketvirtis
Q(W)_3=III ketvirtis
Q(W)_4=IV ketvirtis

# day-period-rules
T0000=night1
T0600=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=vidurnaktis
P(a)_am=priešpiet
P(a)_noon=perpiet
P(a)_pm=popiet
P(a)_morning1=rytas
P(a)_afternoon1=popietė
P(a)_evening1=vakaras
P(a)_night1=naktis

P(n)_midnight=vidurnaktis
P(n)_am=pr. p.
P(n)_noon=perpiet
P(n)_pm=pop.
P(n)_morning1=rytas
P(n)_afternoon1=popietė
P(n)_evening1=vakaras
P(n)_night1=naktis

P(w)_midnight=vidurnaktis
P(w)_am=priešpiet
P(w)_noon=perpiet
P(w)_pm=popiet
P(w)_morning1=rytas
P(w)_afternoon1=popietė
P(w)_evening1=vakaras
P(w)_night1=naktis

P(A)_midnight=vidurnaktis
P(A)_am=priešpiet
P(A)_noon=vidurdienis
P(A)_pm=popiet
P(A)_morning1=rytas
P(A)_afternoon1=diena
P(A)_evening1=vakaras
P(A)_night1=naktis

P(N)_midnight=vidurnaktis
P(N)_am=pr. p.
P(N)_noon=vidurdienis
P(N)_pm=pop.
P(N)_morning1=rytas
P(N)_afternoon1=diena
P(N)_evening1=vakaras
P(N)_night1=naktis

P(W)_midnight=vidurnaktis
P(W)_am=priešpiet
P(W)_noon=vidurdienis
P(W)_pm=popiet
P(W)_morning1=rytas
P(W)_afternoon1=diena
P(W)_evening1=vakaras
P(W)_night1=naktis

# eras
E(w)_0=prieš Kristų
E(w|alt)_0=prieš mūsų erą
E(w)_1=po Kristaus
E(w|alt)_1=mūsų eroje

E(a)_0=pr. Kr.
E(a|alt)_0=pr. m. e.
E(a)_1=po Kr.
E(a|alt)_1=mūsų eroje

E(n)_0=pr. Kr.
E(n|alt)_0=prme
E(n)_1=po Kr.
E(n|alt)_1=pome

# format patterns
F(f)_d=y 'm'. MMMM d 'd'., EEEE
F(l)_d=y 'm'. MMMM d 'd'.
F(m)_d=y-MM-dd
F(s)_d=y-MM-dd

F(alt)=HH.mm.ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=hh a
F_H=HH
F_hm=hh:mm a
F_Hm=HH:mm
F_hms=hh:mm:ss a
F_Hms=HH:mm:ss

F_Md=MM-d
F_MMMd=MM-dd
F_MMMMd=MMMM d 'd'.
F_y=y
F_yM=y-MM
F_yMMM=y-MM
F_yMMMM=y 'm'. LLLL
F_yQQQ=y QQQ
F_yQQQQ=y QQQQ
F_yw=Y w 'sav'.

I={0} – {1}

# labels of elements
L_era=era
L_year=metai
L_quarter=ketvirtis
L_month=mėnuo
L_week=savaitė
L_day=diena
L_weekday=savaitės diena
L_dayperiod=iki pietų / po pietų
L_hour=valanda
L_minute=minutė
L_second=sekundė
L_zone=laiko juosta
