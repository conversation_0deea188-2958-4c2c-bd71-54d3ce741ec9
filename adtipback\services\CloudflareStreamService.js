// adtipback/services/CloudflareStreamService.js
// Cloudflare Stream API Integration Service
// Handles video uploads to Cloudflare Stream for adaptive streaming and mobile optimization

const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

class CloudflareStreamService {
  constructor() {
    // Use environment variables or fallback to hardcoded values
    this.accountId = process.env.CLOUDFLARE_ACCOUNT_ID || '94e2ffe1e7d5daf0d3de8d11c55dd2d6';
    this.apiToken = process.env.CLOUDFLARE_STREAM_API_TOKEN || process.env.CLOUD_FARE_TOKEN_VALUE || '6h1Svn_NmQpWHuLZD8o7OEq23PXy5Y-UneEH9rUu';
    this.customerCode = process.env.CLOUDFLARE_CUSTOMER_CODE || this.accountId;
    this.baseUrl = `https://api.cloudflare.com/client/v4/accounts/${this.accountId}/stream`;

    console.log('[CloudflareStreamService] Initialized with account:', this.accountId);
  }

  /**
   * Test connection to Cloudflare Stream API
   */
  async testConnection() {
    try {
      const response = await fetch(this.baseUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiToken}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      console.log('[CloudflareStreamService] Connection test:', {
        status: response.status,
        success: result.success
      });

      return {
        success: response.status === 200,
        status: response.status,
        data: result
      };
    } catch (error) {
      console.error('[CloudflareStreamService] Connection test failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a direct upload URL for frontend uploads
   * Reference: https://developers.cloudflare.com/stream/uploading-videos/direct-creator-uploads/
   * @param {Object} options - Upload configuration
   * @returns {Promise<Object>} Upload URL and video ID
   */
  async createDirectUploadUrl(options = {}) {
    try {
      const {
        maxDurationSeconds = 300,
        allowedOrigins = [],
        requireSignedURLs = false,
        watermark = null,
        thumbnailTimestampPct = 0.5,
        meta = {}
      } = options;

      const requestBody = {
        maxDurationSeconds,
        allowedOrigins,
        requireSignedURLs,
        thumbnailTimestampPct,
        meta
      };

      // Add watermark if provided
      if (watermark) {
        requestBody.watermark = watermark;
      }

      console.log('[CloudflareStreamService] Creating direct upload URL:', requestBody);

      const response = await fetch(`${this.baseUrl}/direct_upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('[CloudflareStreamService] Direct upload URL created:', {
          uid: data.result.uid,
          uploadURL: data.result.uploadURL?.substring(0, 50) + '...'
        });
        return { success: true, result: data.result };
      } else {
        console.error('[CloudflareStreamService] Failed to create upload URL:', data);
        return {
          success: false,
          error: data.errors?.[0]?.message || `HTTP ${response.status}`
        };
      }
    } catch (error) {
      console.error('[CloudflareStreamService] Error creating upload URL:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Upload video file to Cloudflare Stream
   * @param {string} filePath - Path to video file
   * @param {object} metadata - Video metadata (name, description, etc.)
   */
  async uploadVideo(filePath, metadata = {}) {
    try {
      console.log('[CloudflareStreamService] Starting upload:', { filePath, metadata });
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const formData = new FormData();
      formData.append('file', fs.createReadStream(filePath));
      
      // Add metadata if provided
      if (metadata.name || metadata.description) {
        const meta = {};
        if (metadata.name) meta.name = metadata.name;
        if (metadata.description) meta.description = metadata.description;
        formData.append('meta', JSON.stringify(meta));
      }

      // Set webhook URL for encoding completion notifications
      const webhookUrl = `${process.env.application_host || 'https://api.adtip.in'}/stream-webhook`;
      formData.append('webhookUrl', webhookUrl);

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: { 
          'Authorization': `Bearer ${this.apiToken}`,
          ...formData.getHeaders()
        },
        body: formData
      });

      const result = await response.json();
      
      console.log('[CloudflareStreamService] Upload response:', {
        status: response.status,
        success: result.success,
        uid: result.result?.uid
      });

      if (!result.success) {
        throw new Error(`Stream upload failed: ${JSON.stringify(result.errors)}`);
      }

      return {
        success: true,
        uid: result.result.uid,
        status: result.result.status,
        created: result.result.created,
        data: result.result
      };
    } catch (error) {
      console.error('[CloudflareStreamService] Upload error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get video status and details from Stream
   * @param {string} videoId - Stream video ID
   */
  async getVideoStatus(videoId) {
    try {
      const response = await fetch(`${this.baseUrl}/${videoId}`, {
        headers: { 'Authorization': `Bearer ${this.apiToken}` }
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`Failed to get video status: ${JSON.stringify(result.errors)}`);
      }

      return {
        success: true,
        uid: result.result.uid,
        status: result.result.status,
        duration: result.result.duration,
        input: result.result.input,
        playback: result.result.playback,
        data: result.result
      };
    } catch (error) {
      console.error('[CloudflareStreamService] Get status error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get Stream player iframe URL
   * @param {string} videoId - Stream video ID
   * @param {object} options - Player options (autoplay, muted, controls)
   */
  getStreamPlayerUrl(videoId, options = {}) {
    const { autoplay = false, muted = true, controls = false } = options;
    return `https://customer-${this.customerCode}.cloudflarestream.com/${videoId}/iframe?autoplay=${autoplay}&muted=${muted}&controls=${controls}`;
  }

  /**
   * Get adaptive streaming manifest URL
   * @param {string} videoId - Stream video ID
   * @param {string} format - 'hls' or 'dash'
   */
  getManifestUrl(videoId, format = 'hls') {
    const extension = format === 'hls' ? 'm3u8' : 'mpd';
    return `https://customer-${this.customerCode}.cloudflarestream.com/${videoId}/manifest/video.${extension}`;
  }

  /**
   * Get direct video URL for different qualities
   * @param {string} videoId - Stream video ID
   * @param {string} quality - 'auto', '360p', '720p', '1080p'
   */
  getVideoUrl(videoId, quality = 'auto') {
    if (quality === 'auto') {
      return this.getManifestUrl(videoId, 'hls');
    }
    return `https://customer-${this.customerCode}.cloudflarestream.com/${videoId}/${quality}/video.mp4`;
  }

  /**
   * Delete video from Stream
   * @param {string} videoId - Stream video ID
   */
  async deleteVideo(videoId) {
    try {
      const response = await fetch(`${this.baseUrl}/${videoId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${this.apiToken}` }
      });
      
      const result = await response.json();
      
      console.log('[CloudflareStreamService] Delete response:', {
        status: response.status,
        success: result.success
      });

      return {
        success: result.success,
        data: result
      };
    } catch (error) {
      console.error('[CloudflareStreamService] Delete error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * List all videos in Stream
   * @param {object} options - Query options (limit, before, after)
   */
  async listVideos(options = {}) {
    try {
      const queryParams = new URLSearchParams();
      if (options.limit) queryParams.append('limit', options.limit);
      if (options.before) queryParams.append('before', options.before);
      if (options.after) queryParams.append('after', options.after);

      const url = `${this.baseUrl}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
      
      const response = await fetch(url, {
        headers: { 'Authorization': `Bearer ${this.apiToken}` }
      });
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`Failed to list videos: ${JSON.stringify(result.errors)}`);
      }

      return {
        success: true,
        videos: result.result,
        total: result.result.length
      };
    } catch (error) {
      console.error('[CloudflareStreamService] List videos error:', error);
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
module.exports = new CloudflareStreamService();
