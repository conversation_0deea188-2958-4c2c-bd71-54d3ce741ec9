#!/usr/bin/env node

/**
 * FCM Message Queue Setup Script
 * 
 * This script sets up the database tables and structures needed for FCM message queue
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const config = require('../config/appenvconfig.js');

const dbConfig = {
  host: config.database.host,
  user: config.database.user,
  password: config.database.password,
  database: config.database.database,
  port: parseInt(config.database.port) || 3306,
  charset: 'utf8mb4'
};

class FCMMessageQueueSetup {
  constructor() {
    this.connection = null;
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(dbConfig);
      console.log('✅ Connected to database');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log('✅ Database connection closed');
    }
  }

  async executeSQLFile(filePath, description) {
    try {
      console.log(`📄 ${description}...`);
      
      if (!fs.existsSync(filePath)) {
        throw new Error(`SQL file not found: ${filePath}`);
      }

      const sqlContent = fs.readFileSync(filePath, 'utf8');
      
      // Split SQL content by semicolons and execute each statement
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await this.connection.execute(statement);
          } catch (error) {
            // Log warning for non-critical errors (like column already exists)
            if (error.code === 'ER_DUP_FIELDNAME' || error.code === 'ER_TABLE_EXISTS_ERROR') {
              console.log(`⚠️  Warning: ${error.message}`);
            } else {
              throw error;
            }
          }
        }
      }
      
      console.log(`✅ ${description} completed successfully`);
    } catch (error) {
      console.error(`❌ ${description} failed:`, error.message);
      throw error;
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check if messages table exists
    const [messagesTable] = await this.connection.execute(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'messages'",
      [dbConfig.database]
    );
    
    if (messagesTable[0].count === 0) {
      throw new Error('Messages table not found. Please run chat schema migration first.');
    }
    
    // Check if conversations table exists
    const [conversationsTable] = await this.connection.execute(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'conversations'",
      [dbConfig.database]
    );
    
    if (conversationsTable[0].count === 0) {
      throw new Error('Conversations table not found. Please run chat schema migration first.');
    }
    
    console.log('✅ Prerequisites check passed');
  }

  async setupFCMMessageQueue() {
    const schemaPath = path.join(__dirname, '../database/fcm_message_queue_tables.sql');
    return await this.executeSQLFile(schemaPath, 'Setting up FCM message queue tables');
  }

  async verifySetup() {
    console.log('🔍 Verifying FCM message queue setup...');
    
    // Check if message_delivery_failures table was created
    const [failuresTable] = await this.connection.execute(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'message_delivery_failures'",
      [dbConfig.database]
    );
    
    if (failuresTable[0].count === 0) {
      throw new Error('message_delivery_failures table was not created');
    }
    
    // Check if delivery_status column was added to messages table
    const [deliveryColumn] = await this.connection.execute(
      "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = ? AND table_name = 'messages' AND column_name = 'delivery_status'",
      [dbConfig.database]
    );
    
    if (deliveryColumn[0].count === 0) {
      throw new Error('delivery_status column was not added to messages table');
    }
    
    // Check if view was created
    const [deliveryView] = await this.connection.execute(
      "SELECT COUNT(*) as count FROM information_schema.views WHERE table_schema = ? AND table_name = 'message_delivery_analysis'",
      [dbConfig.database]
    );
    
    if (deliveryView[0].count === 0) {
      console.log('⚠️  Warning: message_delivery_analysis view was not created');
    }
    
    console.log('✅ FCM message queue setup verification completed');
  }

  async run() {
    const startTime = Date.now();
    
    try {
      console.log('=====================================');
      console.log('🚀 Starting FCM Message Queue Setup');
      console.log('=====================================');
      
      // Connect to database
      await this.connect();
      
      // Check prerequisites
      await this.checkPrerequisites();
      
      // Start transaction
      await this.connection.execute('START TRANSACTION');
      
      try {
        // Setup FCM message queue
        await this.setupFCMMessageQueue();
        
        // Verify setup
        await this.verifySetup();
        
        // Commit transaction
        await this.connection.execute('COMMIT');
        
        const duration = ((Date.now() - startTime) / 1000).toFixed(2);
        console.log('=====================================');
        console.log(`🎉 FCM Message Queue setup completed successfully in ${duration}s`);
        console.log('✅ FCM message queue is ready for use');
        console.log('=====================================');
        
      } catch (error) {
        // Rollback transaction on error
        await this.connection.execute('ROLLBACK');
        throw error;
      }
      
    } catch (error) {
      console.error('=====================================');
      console.error('❌ FCM Message Queue setup failed:', error.message);
      console.error('=====================================');
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  const setup = new FCMMessageQueueSetup();
  setup.run();
}

module.exports = FCMMessageQueueSetup;
