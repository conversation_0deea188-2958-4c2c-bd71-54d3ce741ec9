import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

interface SidebarContextType {
  state: 'expanded' | 'collapsed';
  open: boolean;
  setOpen: (open: boolean) => void;
  openMobile: boolean;
  setOpenMobile: (open: boolean) => void;
  isMobile: boolean;
  toggleSidebar: () => void;
  isCollapsed: boolean;
  closeMobileSidebar: () => void;
}

const SidebarContext = createContext<SidebarContextType | null>(null);

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

export const SidebarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isMobile = useIsMobile();
  const [state, setState] = useState<'expanded' | 'collapsed'>(() => {
    const stored = localStorage.getItem('sidebarState');
    return isMobile ? 'collapsed' : stored === 'collapsed' ? 'collapsed' : 'expanded';
  });
  const [open, setOpen] = useState(!isMobile);
  const [openMobile, setOpenMobile] = useState(false);

  useEffect(() => {
    if (!isMobile) {
      localStorage.setItem('sidebarState', state);
      setOpenMobile(false); // Close mobile sidebar when switching to desktop
    }
  }, [state, isMobile]);

  // Close mobile sidebar on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isMobile && openMobile) {
        setOpenMobile(false);
      }
    };

    if (isMobile && openMobile) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when sidebar is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = '';
    };
  }, [isMobile, openMobile]);

  const toggleSidebar = () => {
    if (isMobile) {
      setOpenMobile(prev => !prev);
    } else {
      setState(prev => (prev === 'expanded' ? 'collapsed' : 'expanded'));
      setOpen(prev => !prev);
    }
  };

  const closeMobileSidebar = useCallback(() => {
    if (isMobile) {
      setOpenMobile(false);
    }
  }, [isMobile]);

  return (
    <SidebarContext.Provider value={{
        state,
        open,
        setOpen,
        openMobile,
        setOpenMobile,
        isMobile,
        toggleSidebar,
        isCollapsed: state === 'collapsed',
        closeMobileSidebar,
      }}
    >
      {children}
    </SidebarContext.Provider>
  );
};
