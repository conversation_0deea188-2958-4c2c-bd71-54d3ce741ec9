const mysqlConnection = require('../dbConfig/dbconnection');
const { checkDatabaseHealth, getQueryStats } = require('../dbConfig/queryRunner');
const logger = require('../utils/logger');

class DatabaseHealthService {
  constructor() {
    this.isMonitoring = false;
    this.healthCheckInterval = null;
    this.lastHealthCheck = null;
    this.consecutiveFailures = 0;
    this.maxConsecutiveFailures = 3;
    this.healthCheckIntervalMs = 30000; // 30 seconds
    this.autoRecoveryEnabled = true;
    
    // Health status
    this.healthStatus = {
      isHealthy: true,
      lastCheck: null,
      consecutiveFailures: 0,
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      averageResponseTime: 0,
      lastError: null
    };
  }

  // Start health monitoring
  startMonitoring() {
    if (this.isMonitoring) {
      logger.info('Database health monitoring is already running');
      return;
    }

    this.isMonitoring = true;
    logger.info('Starting database health monitoring');

    // Perform initial health check
    this.performHealthCheck();

    // Set up periodic health checks
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.healthCheckIntervalMs);

    // Set up connection pool monitoring
    this.monitorConnectionPool();
  }

  // Stop health monitoring
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    logger.info('Database health monitoring stopped');
  }

  // Perform health check
  async performHealthCheck() {
    const startTime = Date.now();
    
    try {
      const healthResult = await checkDatabaseHealth();
      const responseTime = Date.now() - startTime;

      this.healthStatus.lastCheck = new Date();
      this.healthStatus.totalChecks++;
      this.healthStatus.averageResponseTime = 
        (this.healthStatus.averageResponseTime * (this.healthStatus.totalChecks - 1) + responseTime) / this.healthStatus.totalChecks;

      if (healthResult.status === 'healthy') {
        this.healthStatus.isHealthy = true;
        this.healthStatus.successfulChecks++;
        this.healthStatus.consecutiveFailures = 0;
        this.healthStatus.lastError = null;

        logger.info(`Database health check passed (${responseTime}ms)`);
      } else {
        this.handleHealthCheckFailure(healthResult.error, responseTime);
      }
    } catch (error) {
      this.handleHealthCheckFailure(error.message, Date.now() - startTime);
    }
  }

  // Handle health check failure
  handleHealthCheckFailure(error, responseTime) {
    this.healthStatus.isHealthy = false;
    this.healthStatus.failedChecks++;
    this.healthStatus.consecutiveFailures++;
    this.healthStatus.lastError = error;

    logger.error(`Database health check failed (${responseTime}ms): ${error}`);

    // Check if we should attempt auto-recovery
    if (this.autoRecoveryEnabled && this.healthStatus.consecutiveFailures >= this.maxConsecutiveFailures) {
      logger.warn(`Attempting database auto-recovery after ${this.healthStatus.consecutiveFailures} consecutive failures`);
      this.attemptAutoRecovery();
    }
  }

  // Attempt auto-recovery
  async attemptAutoRecovery() {
    try {
      logger.info('Starting database auto-recovery process');

      // Step 1: Try to reset the connection pool
      await mysqlConnection.resetPool();
      logger.info('Connection pool reset completed');

      // Step 2: Wait a moment for connections to stabilize
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Step 3: Perform a health check
      const healthResult = await checkDatabaseHealth();
      
      if (healthResult.status === 'healthy') {
        logger.info('Database auto-recovery successful');
        this.healthStatus.consecutiveFailures = 0;
        this.healthStatus.isHealthy = true;
      } else {
        logger.error('Database auto-recovery failed');
      }
    } catch (error) {
      logger.error('Database auto-recovery error:', error);
    }
  }

  // Monitor connection pool
  monitorConnectionPool() {
    setInterval(() => {
      try {
        const poolStats = mysqlConnection.getPoolStats();
        logger.info('Connection pool stats:', poolStats);
      } catch (error) {
        logger.error('Error getting pool stats:', error);
      }
    }, 60000); // Every minute
  }

  // Get current health status
  getHealthStatus() {
    return {
      ...this.healthStatus,
      isMonitoring: this.isMonitoring,
      uptime: this.healthStatus.lastCheck ? 
        Math.floor((Date.now() - this.healthStatus.lastCheck.getTime()) / 1000) : 0,
      queryStats: getQueryStats()
    };
  }

  // Force a health check
  async forceHealthCheck() {
    logger.info('Forcing database health check');
    await this.performHealthCheck();
    return this.getHealthStatus();
  }

  // Enable/disable auto-recovery
  setAutoRecovery(enabled) {
    this.autoRecoveryEnabled = enabled;
    logger.info(`Database auto-recovery ${enabled ? 'enabled' : 'disabled'}`);
  }

  // Set health check interval
  setHealthCheckInterval(intervalMs) {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    this.healthCheckIntervalMs = intervalMs;
    
    if (this.isMonitoring) {
      this.healthCheckInterval = setInterval(() => {
        this.performHealthCheck();
      }, this.healthCheckIntervalMs);
    }
    
    logger.info(`Health check interval set to ${intervalMs}ms`);
  }

  // Emergency database reset
  async emergencyReset() {
    logger.warn('EMERGENCY: Performing database connection reset');
    
    try {
      await mysqlConnection.closePool();
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // The pool will be recreated automatically on next use
      await mysqlConnection.healthCheck();
      
      logger.info('Emergency database reset completed successfully');
      return true;
    } catch (error) {
      logger.error('Emergency database reset failed:', error);
      return false;
    }
  }
}

// Create singleton instance
const databaseHealthService = new DatabaseHealthService();

module.exports = databaseHealthService; 