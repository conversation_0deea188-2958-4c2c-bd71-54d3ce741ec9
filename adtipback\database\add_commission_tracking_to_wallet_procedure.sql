-- Add commission tracking fields to wallet table
-- This migration adds support for tracking platform commission on earnings
-- MySQL 8 compatible version using stored procedure

<PERSON><PERSON><PERSON>ITER $$

DROP PROCEDURE IF EXISTS AddCommissionTrackingColumns$$

CREATE PROCEDURE AddCommissionTrackingColumns()
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE idx_exists INT DEFAULT 0;
    
    -- Check and add gross_amount column
    SELECT COUNT(*) INTO col_exists 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'wallet' 
    AND COLUMN_NAME = 'gross_amount'
    AND TABLE_SCHEMA = DATABASE();
    
    IF col_exists = 0 THEN
        ALTER TABLE wallet 
        ADD COLUMN gross_amount DECIMAL(10,2) DEFAULT NULL 
        COMMENT 'Gross earning amount before commission';
        SELECT 'Added gross_amount column' as message;
    ELSE
        SELECT 'gross_amount column already exists' as message;
    END IF;
    
    -- Check and add platform_commission column
    SELECT COUNT(*) INTO col_exists 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'wallet' 
    AND COLUMN_NAME = 'platform_commission'
    AND TABLE_SCHEMA = DATABASE();
    
    IF col_exists = 0 THEN
        ALTER TABLE wallet 
        ADD COLUMN platform_commission DECIMAL(10,2) DEFAULT NULL 
        COMMENT 'Platform commission deducted';
        SELECT 'Added platform_commission column' as message;
    ELSE
        SELECT 'platform_commission column already exists' as message;
    END IF;
    
    -- Check and add commission_rate column
    SELECT COUNT(*) INTO col_exists 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'wallet' 
    AND COLUMN_NAME = 'commission_rate'
    AND TABLE_SCHEMA = DATABASE();
    
    IF col_exists = 0 THEN
        ALTER TABLE wallet 
        ADD COLUMN commission_rate DECIMAL(5,4) DEFAULT NULL 
        COMMENT 'Commission rate applied (0.30 for premium, 0.60 for regular)';
        SELECT 'Added commission_rate column' as message;
    ELSE
        SELECT 'commission_rate column already exists' as message;
    END IF;
    
    -- Check and add description column
    SELECT COUNT(*) INTO col_exists 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'wallet' 
    AND COLUMN_NAME = 'description'
    AND TABLE_SCHEMA = DATABASE();
    
    IF col_exists = 0 THEN
        ALTER TABLE wallet 
        ADD COLUMN description TEXT DEFAULT NULL 
        COMMENT 'Transaction description';
        SELECT 'Added description column' as message;
    ELSE
        SELECT 'description column already exists' as message;
    END IF;
    
    -- Check and create index
    SELECT COUNT(*) INTO idx_exists 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_NAME = 'wallet' 
    AND INDEX_NAME = 'idx_wallet_commission'
    AND TABLE_SCHEMA = DATABASE();
    
    IF idx_exists = 0 THEN
        CREATE INDEX idx_wallet_commission ON wallet(createdby, gross_amount, platform_commission);
        SELECT 'Created idx_wallet_commission index' as message;
    ELSE
        SELECT 'idx_wallet_commission index already exists' as message;
    END IF;
    
    SELECT 'Commission tracking migration completed successfully' as status;
    
END$$

DELIMITER ;

-- Execute the procedure
CALL AddCommissionTrackingColumns();

-- Drop the procedure after use
DROP PROCEDURE AddCommissionTrackingColumns;

-- Verify the changes
SELECT 
  COLUMN_NAME, 
  DATA_TYPE, 
  IS_NULLABLE, 
  COLUMN_DEFAULT, 
  COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'wallet' 
AND COLUMN_NAME IN ('gross_amount', 'platform_commission', 'commission_rate', 'description')
AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
