-- Fix promotional video upload database constraints
-- Run this script to fix missing foreign key constraints and indexes

-- Add missing foreign key constraint for post_target_gender.post_id
ALTER TABLE post_target_gender 
ADD CONSTRAINT `post_target_gender_ibfk_3` 
FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE;

-- Add missing foreign key constraint for post_target_locations.post_id  
ALTER TABLE post_target_locations 
ADD CONSTRAINT `post_target_locations_ibfk_1` 
FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE;

-- Add missing foreign key constraint for post_target_locations.user_id
ALTER TABLE post_target_locations 
ADD CONSTRAINT `post_target_locations_ibfk_2` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- Add missing foreign key constraint for post_target_locations.location_id
-- Note: This assumes a locations table exists. If not, create it first.
-- ALTER TABLE post_target_locations 
-- ADD CONSTRAINT `post_target_locations_ibfk_3` 
-- FOREI<PERSON>N KEY (`location_id`) REFERENCES `locations` (`id`) ON DELETE CASCADE;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_posts_is_promoted ON posts(is_promoted);
CREATE INDEX IF NOT EXISTS idx_posts_media_type ON posts(media_type);
CREATE INDEX IF NOT EXISTS idx_posts_is_active ON posts(is_active);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON posts(created_at);

-- Add indexes for post promotions
CREATE INDEX IF NOT EXISTS idx_post_promotions_created_at ON post_promotions(created_at);
CREATE INDEX IF NOT EXISTS idx_post_promotions_pay_per_view ON post_promotions(pay_per_view);
CREATE INDEX IF NOT EXISTS idx_post_promotions_reach_goal ON post_promotions(reach_goal);

-- Update existing posts to be active if they have valid data
UPDATE posts 
SET is_active = 1 
WHERE is_active = 0 
  AND user_id IS NOT NULL 
  AND title IS NOT NULL 
  AND title != ''
  AND media_url IS NOT NULL 
  AND media_url != '';

-- Display completion message
SELECT 'Promotional video database constraints fixed successfully!' as message;
