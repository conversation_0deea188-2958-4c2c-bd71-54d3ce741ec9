{"name": "React-ImageManager", "version": "0.80.1", "summary": "<PERSON><PERSON><PERSON> for React Native.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.1"}, "source_files": "**/*.{m,mm,cpp,h}", "header_dir": "react/renderer/imagemanager", "pod_target_xcconfig": {"USE_HEADERMAP": "NO", "HEADER_SEARCH_PATHS": ["\"$(PODS_TARGET_SRCROOT)/../../../\"", "\"$(PODS_TARGET_SRCROOT)\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-utils/React_utils.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "DEFINES_MODULE": "YES"}, "dependencies": {"React-Core/Default": [], "React-Fabric": [], "React-graphics": [], "React-debug": [], "React-utils": [], "React-rendererdebug": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}