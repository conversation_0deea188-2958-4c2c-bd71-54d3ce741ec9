-- =====================================================
-- SIMPLIFIED CHAT DATA MIGRATION SCRIPT
-- =====================================================
-- This script migrates data from the old user_chat table to the new chat schema
-- Simplified version without complex queries

-- =====================================================
-- STEP 1: BACKUP OLD DATA (SAFETY FIRST)
-- =====================================================
-- Backup is handled by the migration script

-- =====================================================
-- STEP 2: MIGRATE CONVERSATIONS
-- =====================================================
-- Create conversations from unique sender-receiver pairs
INSERT INTO conversations (type, created_by, created_at, last_activity_at)
SELECT DISTINCT
    'direct' as type,
    LEAST(sender, receiver) as created_by,
    MIN(createddate) as created_at,
    MAX(createddate) as last_activity_at
FROM user_chat 
WHERE sender IS NOT NULL 
  AND receiver IS NOT NULL 
  AND receiver != 0
  AND is_active = 1
GROUP BY LEAST(sender, receiver), GREATEST(sender, receiver);

-- =====================================================
-- STEP 3: MIGRATE CONVERSATION PARTICIPANTS
-- =====================================================
-- Insert participants for each conversation (first user)
INSERT INTO conversation_participants (conversation_id, user_id, joined_at)
SELECT DISTINCT
    c.id as conversation_id,
    c.created_by as user_id,
    c.created_at as joined_at
FROM conversations c;

-- Insert participants for each conversation (second user)
INSERT INTO conversation_participants (conversation_id, user_id, joined_at)
SELECT DISTINCT
    c.id as conversation_id,
    CASE 
        WHEN uc.sender = c.created_by THEN uc.receiver
        ELSE uc.sender
    END as user_id,
    MIN(uc.createddate) as joined_at
FROM conversations c
JOIN user_chat uc ON (
    (uc.sender = c.created_by OR uc.receiver = c.created_by)
    AND uc.sender IS NOT NULL 
    AND uc.receiver IS NOT NULL 
    AND uc.receiver != 0
    AND uc.is_active = 1
)
WHERE CASE 
    WHEN uc.sender = c.created_by THEN uc.receiver
    ELSE uc.sender
END != c.created_by
GROUP BY c.id, CASE 
    WHEN uc.sender = c.created_by THEN uc.receiver
    ELSE uc.sender
END;

-- =====================================================
-- STEP 4: MIGRATE MESSAGES
-- =====================================================
-- Insert messages into new messages table
INSERT INTO messages (
    conversation_id,
    sender_id,
    message_type,
    content,
    file_url,
    file_name,
    reply_to_message_id,
    created_at,
    updated_at,
    is_deleted
)
SELECT 
    c.id as conversation_id,
    uc.sender as sender_id,
    CASE 
        WHEN uc.message_file IS NOT NULL AND uc.message_file != '' THEN
            CASE 
                WHEN LOWER(uc.message_file) LIKE '%.jpg' OR LOWER(uc.message_file) LIKE '%.jpeg' OR LOWER(uc.message_file) LIKE '%.png' OR LOWER(uc.message_file) LIKE '%.gif' THEN 'image'
                WHEN LOWER(uc.message_file) LIKE '%.mp4' OR LOWER(uc.message_file) LIKE '%.avi' OR LOWER(uc.message_file) LIKE '%.mov' THEN 'video'
                WHEN LOWER(uc.message_file) LIKE '%.mp3' OR LOWER(uc.message_file) LIKE '%.wav' OR LOWER(uc.message_file) LIKE '%.m4a' THEN 'audio'
                ELSE 'file'
            END
        ELSE 'text'
    END as message_type,
    uc.message as content,
    uc.message_file as file_url,
    CASE 
        WHEN uc.message_file IS NOT NULL AND uc.message_file != '' THEN
            SUBSTRING_INDEX(uc.message_file, '/', -1)
        ELSE NULL
    END as file_name,
    uc.parent_id as reply_to_message_id,
    uc.createddate as created_at,
    COALESCE(uc.updateddate, uc.createddate) as updated_at,
    CASE 
        WHEN uc.is_deleted_for_everyone = 1 THEN TRUE
        ELSE FALSE
    END as is_deleted
FROM user_chat uc
JOIN conversations c ON (
    (c.created_by = LEAST(uc.sender, uc.receiver))
)
WHERE uc.sender IS NOT NULL 
  AND uc.receiver IS NOT NULL 
  AND uc.receiver != 0
  AND uc.is_active = 1
ORDER BY uc.createddate;

-- =====================================================
-- STEP 5: MIGRATE MESSAGE STATUS
-- =====================================================
-- Insert message status for delivered messages
INSERT INTO message_status (message_id, user_id, status, timestamp)
SELECT 
    m.id as message_id,
    cp.user_id,
    'delivered' as status,
    m.created_at as timestamp
FROM messages m
JOIN conversation_participants cp ON m.conversation_id = cp.conversation_id
WHERE cp.user_id != m.sender_id;

-- Insert read status for messages that were seen
INSERT INTO message_status (message_id, user_id, status, timestamp)
SELECT DISTINCT
    m.id as message_id,
    CASE 
        WHEN m.sender_id = uc.sender THEN uc.receiver
        ELSE uc.sender
    END as user_id,
    'read' as status,
    COALESCE(uc.updateddate, m.created_at) as timestamp
FROM messages m
JOIN user_chat uc ON (
    m.content = uc.message 
    AND m.sender_id = uc.sender 
    AND DATE(m.created_at) = DATE(uc.createddate)
)
WHERE (uc.is_seen = 1 OR uc.is_receiver_seen = 1)
  AND CASE 
    WHEN m.sender_id = uc.sender THEN uc.receiver
    ELSE uc.sender
  END != m.sender_id;

-- =====================================================
-- STEP 6: UPDATE CONVERSATION LAST MESSAGE
-- =====================================================
-- Update last_message_id for each conversation
UPDATE conversations c
SET last_message_id = (
    SELECT m.id 
    FROM messages m 
    WHERE m.conversation_id = c.id 
    ORDER BY m.created_at DESC 
    LIMIT 1
);

-- =====================================================
-- STEP 7: INITIALIZE USER PRESENCE
-- =====================================================
-- Insert initial presence data for all users who have sent messages
INSERT INTO user_presence (user_id, status, last_seen_at)
SELECT DISTINCT 
    u.id as user_id,
    CASE 
        WHEN u.online_status = 1 THEN 'online'
        WHEN u.online_status = 2 THEN 'offline'
        ELSE 'offline'
    END as status,
    COALESCE(
        (SELECT MAX(createddate) FROM user_chat WHERE sender = u.id),
        u.updated_date
    ) as last_seen_at
FROM users u
WHERE u.id IN (
    SELECT DISTINCT sender FROM user_chat 
    UNION 
    SELECT DISTINCT receiver FROM user_chat WHERE receiver != 0
)
ON DUPLICATE KEY UPDATE
    status = VALUES(status),
    last_seen_at = VALUES(last_seen_at);

-- =====================================================
-- STEP 8: INITIALIZE CHAT SETTINGS
-- =====================================================
-- Create default chat settings for all users
INSERT INTO chat_settings (user_id)
SELECT DISTINCT u.id
FROM users u
WHERE u.id IN (
    SELECT DISTINCT sender FROM user_chat 
    UNION 
    SELECT DISTINCT receiver FROM user_chat WHERE receiver != 0
)
ON DUPLICATE KEY UPDATE user_id = VALUES(user_id);

-- =====================================================
-- STEP 9: UPDATE UNREAD COUNTS
-- =====================================================
-- Calculate and update unread counts for each participant
UPDATE conversation_participants cp
SET unread_count = (
    SELECT COUNT(*)
    FROM messages m
    LEFT JOIN message_status ms ON (
        m.id = ms.message_id 
        AND ms.user_id = cp.user_id 
        AND ms.status = 'read'
    )
    WHERE m.conversation_id = cp.conversation_id
    AND m.sender_id != cp.user_id
    AND ms.id IS NULL
    AND m.is_deleted = FALSE
);
