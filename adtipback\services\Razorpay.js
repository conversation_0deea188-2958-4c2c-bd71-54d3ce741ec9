const razorpayInstance = require("razorpay");
const crypto = require("crypto");
const { logError } = require("../dbConfig/errorLog");
const dbQuery = require("../dbConfig/queryRunner");
const { RAZOR_PAY_KEY_SECRET, RAZOR_PAY_KEY_ID } = process.env;
const moment = require("moment");
// Get current time and add 5 hours 30 minute
const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
const WalletService = require("../services/WalletService");

// Razorpay Service Function
const createOrder = async (amount, currency = "INR", user_id) => {
  try {
    const razorpay = new razorpayInstance({
      key_id: RAZOR_PAY_KEY_ID,
      key_secret: RAZOR_PAY_KEY_SECRET,
    });

    const uniqueReceipt = `receipt_${Date.now()}_${user_id}`;
    const options = {
      amount: amount * 100,
      currency: currency,
      receipt: uniqueReceipt,
    };

    const orderData = await razorpay.orders.create(options);
    orderData.user_id = user_id;

    // Get current Indian time (inside function)
    const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    const insertOrderQuery = `
      INSERT INTO orders (user_id, razorpay_order_id, amount, currency, receipt, status, created_at, updated_at)
      VALUES (${user_id}, '${orderData.id}', ${amount}, '${currency}', '${uniqueReceipt}', '${orderData.status}', '${currentTime}', '${currentTime}')
    `;

    await dbQuery.queryRunner(insertOrderQuery);

    return {
      status: true,
      message: "Order is created and stored in the database",
      orderData,
    };
  } catch (error) {
    console.error("Error in Razorpay Service:", error);
    const requestBody = { amount, currency, user_id };
    await logError(error, "razorpay_order_creation", requestBody);

    throw {
      status: false,
      message: "Failed to create Razorpay order",
      error,
    };
  }
};


/**
 * Verify Razorpay payment, update/create transaction, and handle premium plan based on payment_status
 * 
 * 
 *  
 *  order_id,
  razorpay_payment_id,
  razorpay_signature,
  amount,
  currency,
  user_id,
  plan_id,
  payment_status
 */
  const verifyAndUpsertTransaction = async (data) => {
    try {
      let is_verified = false;
      const body = `${data.order_id}|${data.razorpay_payment_id}`;
      const expectedSignature = crypto
        .createHmac("sha256", RAZOR_PAY_KEY_SECRET)
        .update(body.toString())
        .digest("hex");
  
      if (expectedSignature !== data.razorpay_signature) {
        return {
          status: false,
          statusCode: 400,
          message: "Invalid Razorpay payment signature.",
        };
      }
      is_verified = true;
  
      const existingTransaction = await dbQuery.queryRunner(
        `SELECT * FROM transactions WHERE payment_id ='${data.razorpay_payment_id}'`
      );
  
      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
  
      let transaction;
  
      if (existingTransaction.length > 0) {
        await dbQuery.queryRunner(
          `UPDATE transactions 
           SET status = '${data.payment_status}', updated_at = '${currentTime}'
           WHERE payment_id = '${data.razorpay_payment_id}'`
        );
  
        transaction = existingTransaction[0];
        transaction.status = "success";
      } else {
        const insertQuery = `
          INSERT INTO transactions 
          (total, currency, status, order_id, user_id, payment_id, transaction_for, is_active, created_at, updated_at)
          VALUES (${data.amount}, '${data.currency}', '${data.payment_status}', '${data.order_id}', ${data.user_id}, '${data.razorpay_payment_id}', '${data.transaction_for}', true, '${currentTime}', '${currentTime}')
        `;
        
        transaction = await dbQuery.queryRunner(insertQuery);
      }
  
      return {
        status: true,
        statusCode: 200,
        message:
          existingTransaction.length > 0
            ? "Transaction verified and status updated."
            : "Transaction verified and created successfully.",
        transactionId: transaction.insertId,
        is_verified: is_verified,
      };
    } catch (error) {
      console.error("Error in verifyAndUpsertTransaction:", error);
      await logError(error, "razorpay_verify_sign", data);
      return {
        status: false,
        statusCode: 400,
        message: "Failed to verify and upsert transaction.",
      };
    }
  };
  
  

module.exports = { createOrder, verifyAndUpsertTransaction };