# Cocoapods::Search Changelog

## 1.0.1 (2021-08-13)

##### Enhancements

* None.  

##### Bug Fixes

* Fix running with `--web`.  
  [<PERSON>](https://github.com/segiddin<PERSON>)
  [#25](https://github.com/CocoaPods/cocoapods-search/issues/25)


## 1.0.0 (2016-05-10)

##### Enhancements

* None.  

##### Bug Fixes

* None.  


## 1.0.0.rc.1 (2016-04-30)

##### Enhancements

* None.  

##### Bug Fixes

* None.  


## 1.0.0.beta.2 (2016-04-14)

##### Bug Fixes

* Compatibility with CocoaPods 1.0.0.beta.7.  
  [<PERSON>](https://github.com/segiddins)


## 1.0.0.beta.1 (2015-12-30)

##### Enhancements

* Perform full search as default, add `--simple` option to search only by
  name.  
  [Muhammed <PERSON>vuz <PERSON>umlalı](https://github.com/manuyavuz)
  [#13](https://github.com/CocoaPods/cocoapods-search/issues/13)

* Add support for tvOS and any possible future platforms.  
  [Muhammed Yavuz Nuzumlalı](https://github.com/manuyavuz)
  [#11](https://github.com/CocoaPods/cocoapods-search/issues/11)

##### Bug Fixes

* Print output in reverse order.  
  [Muhammed Yavuz Nuzumlalı](https://github.com/manuyavuz)

* Perform regexp escape on individual query words before joining them.  
  [Muhammed Yavuz Nuzumlalı](https://github.com/manuyavuz)
  [#8](https://github.com/CocoaPods/cocoapods-search/issues/8)


## 0.1.0 (2015-09-03)

* Version number must not collide with old gem called cocoapods-search 0.0.7


## 0.0.1 (2015-09-03)

* Initial implementation. This version is an extraction from [CocoaPods](https://github.com/CocoaPods/CocoaPods).

Original creators:
[Eloy Durán](https://github.com/alloy)
[Fabio Pelosin](https://github.com/fabiopelosin)

Extractor:
[Emma Koszinowski](http://github.com/emkosz)
