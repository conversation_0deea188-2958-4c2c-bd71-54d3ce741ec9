
# weekdays
D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

# quarters
Q(a)_1=1er. trim.
Q(a)_2=2º. trim.
Q(a)_3=3er. trim.
Q(a)_4=4º trim.

Q(n)_1=1T
Q(n)_2=2T
Q(n)_3=3T
Q(n)_4=4T

Q(w)_1=1.er trimestre
Q(w)_2=2º. trimestre
Q(w)_3=3.er trimestre
Q(w)_4=4o. trimestre

Q(A)_1=1er. trim.
Q(A)_2=2º. trim.
Q(A)_3=3er. trim.
Q(A)_4=4º trim.

Q(N)_1=1T
Q(N)_2=2T
Q(N)_3=3T
Q(N)_4=4T

Q(W)_1=1.er trimestre
Q(W)_2=2º. trimestre
Q(W)_3=3.er trimestre
Q(W)_4=4º trimestre

# day-period-translations
P(a)_am=a. m.
P(a)_pm=p. m.

P(n)_noon=del mediodía
P(n)_morning1=de la madrugada
P(n)_morning2=mañana
P(n)_evening1=de la tarde
P(n)_night1=de la noche

P(w)_am=a. m.
P(w)_pm=p. m.

P(A)_am=a. m.
P(A)_pm=p. m.

P(N)_am=a. m.
P(N)_pm=p. m.

P(W)_am=a. m.
P(W)_pm=p. m.

# format patterns
F(s)_d=dd/MM/yy

F(alt)=hh:mm:ss a

F(f)_t=H:mm:ss zzzz
F(l)_t=H:mm:ss z
F(m)_t=H:mm:ss
F(s)_t=H:mm
F_Hm=H:mm
F_Hms=H:mm:ss
F_MMd=d/MM
F_yMM=MM/y
F_yQQQ=QQQ y

I={0} – {1}

# labels of elements
L_dayperiod=a. m./p. m.
