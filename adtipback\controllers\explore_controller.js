const exploreService = require('../services/explore_service');

const exploreController = async (req, res) => {
  try {
    const { page, limit, loggined_user_id } = req.body;

    // Default pagination values
    const pageNumber = page && page > 0 ? parseInt(page) : 1;
    const limitNumber = limit && limit > 0 ? parseInt(limit) : 20;

    // Call the service function
    const response = await exploreService.getExploreContent(
      loggined_user_id,
      pageNumber,
      limitNumber
    );

    if (response.error) {
      return res.status(400).json({ status: false, message: response.error });
    }
    return res.status(200).json({
      status: true,
      message: response.message,
      data: response.data,
      pagination: response.pagination,
    });
  } catch (error) {
    console.error('Explore Controller Error:', error);
    return res.status(500).json({ status: false, message: 'Internal Server Error' });
  }
};

module.exports = exploreController;