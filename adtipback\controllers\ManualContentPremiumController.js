const ManualContentPremiumService = require('../services/ManualContentPremiumService');

exports.manualCreateContentPremium = async (req, res) => {
  try {
    const { userId, planId, duration, note } = req.body;
    if (!userId || !planId || !duration) {
      return res.status(400).json({ status: false, message: 'userId, planId, and duration are required' });
    }
    const result = await ManualContentPremiumService.manualCreateContentPremium(userId, planId, duration, note);
    res.json({ status: true, message: 'Content creator premium created manually', data: result });
  } catch (err) {
    res.status(500).json({ status: false, message: 'Error creating content creator premium', error: err.message });
  }
};

exports.manualCancelContentPremium = async (req, res) => {
  try {
    const { userId, note } = req.body;
    if (!userId) {
      return res.status(400).json({ status: false, message: 'userId is required' });
    }
    const result = await ManualContentPremiumService.manualCancelContentPremium(userId, note);
    res.json({ status: true, message: 'Content creator premium cancelled manually', data: result });
  } catch (err) {
    res.status(500).json({ status: false, message: 'Error cancelling content creator premium', error: err.message });
  }
}; 