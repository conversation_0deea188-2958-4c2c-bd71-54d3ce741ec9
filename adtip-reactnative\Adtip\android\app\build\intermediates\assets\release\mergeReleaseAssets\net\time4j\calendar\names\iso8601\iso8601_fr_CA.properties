# months
M(a)_1=janv.
M(a)_2=févr.
M(a)_3=mars
M(a)_4=avr.
M(a)_5=mai
M(a)_6=juin
M(a)_7=juill.
M(a)_8=août
M(a)_9=sept.
M(a)_10=oct.
M(a)_11=nov.
M(a)_12=déc.

M(A)_1=janv.
M(A)_2=févr.
M(A)_3=mars
M(A)_4=avr.
M(A)_5=mai
M(A)_6=juin
M(A)_7=juill.
M(A)_8=août
M(A)_9=sept.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=déc.

# weekdays
D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

D(N)_1=L
D(N)_2=M
D(N)_3=M
D(N)_4=J
D(N)_5=V
D(N)_6=S
D(N)_7=D

# day-period-translations
P(a)_midnight=minuit
P(a)_am=a.m.
P(a)_noon=midi
P(a)_pm=p.m.
P(a)_morning1=du mat.
P(a)_afternoon1=après-midi
P(a)_evening1=du soir
P(a)_night1=de nuit

P(n)_am=a
P(n)_noon=midi
P(n)_pm=p
P(n)_afternoon1=après-midi
P(n)_night1=nuit

P(w)_am=a.m.
P(w)_pm=p.m.

P(A)_am=a.m.
P(A)_pm=p.m.
P(A)_morning1=mat.
P(A)_afternoon1=après-midi

P(N)_am=a.m.
P(N)_pm=p.m.
P(N)_afternoon1=après-midi
P(N)_evening1=soir
P(N)_night1=nuit

P(W)_am=a.m.
P(W)_pm=p.m.

# eras
E(w|alt)_0=avant l’ère chrétienne
E(w|alt)_1=de l’ère chrétienne

# format patterns
F(s)_d=yy-MM-dd

F(alt)=H' h 'mm

F(f)_t=HH 'h' mm 'min' ss 's' zzzz
F(l)_t=HH 'h' mm 'min' ss 's' z
F(m)_t=HH 'h' mm 'min' ss 's'
F(s)_t=HH 'h' mm
F(m)_dt={1} {0}

F_Bh=h 'h' B
F_Bhm=h 'h' mm B
F_Bhms=h 'h' mm 'min' ss 's' B
F_h=h 'h' a
F_hm=h 'h' mm a
F_Hm=HH 'h' mm
F_hms=h 'h' mm 'min' ss 's' a
F_Hms=HH 'h' mm 'min' ss 's'

F_Md=M-d
F_MMd=MM-d
F_yM=y-MM
F_yMM=y-MM

# day-period-translations
P(A)_night1=nuit
