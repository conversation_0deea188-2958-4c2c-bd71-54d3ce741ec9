#!/usr/bin/env node

/**
 * Step-by-step <PERSON><PERSON> Script
 * 
 * This script migrates the chat system step by step to identify any issues
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

const config = require('../config/appenvconfig.js');

const dbConfig = {
  host: config.database.host,
  user: config.database.user,
  password: config.database.password,
  database: config.database.database,
  port: parseInt(config.database.port) || 3306,
  charset: 'utf8mb4'
};

class StepByStepMigration {
  constructor() {
    this.connection = null;
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(dbConfig);
      console.log('✅ Connected to database successfully');
      return true;
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log('✅ Database connection closed');
    }
  }

  async executeQuery(sql, description) {
    try {
      console.log(`🔄 ${description}...`);
      const [result] = await this.connection.query(sql);
      console.log(`✅ ${description} completed successfully`);
      return result;
    } catch (error) {
      console.error(`❌ ${description} failed:`, error.message);
      throw error;
    }
  }

  async step1_CreateTables() {
    console.log('\n=== STEP 1: Creating Tables ===');
    
    // Create conversations table
    await this.executeQuery(`
      CREATE TABLE IF NOT EXISTS conversations (
        id INT PRIMARY KEY AUTO_INCREMENT,
        type ENUM('direct', 'group') DEFAULT 'direct',
        title VARCHAR(255) NULL,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_message_id INT NULL,
        last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        
        INDEX idx_created_by (created_by),
        INDEX idx_last_activity (last_activity_at DESC),
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `, 'Creating conversations table');

    // Create conversation_participants table
    await this.executeQuery(`
      CREATE TABLE IF NOT EXISTS conversation_participants (
        id INT PRIMARY KEY AUTO_INCREMENT,
        conversation_id INT NOT NULL,
        user_id INT NOT NULL,
        joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        left_at TIMESTAMP NULL,
        role ENUM('member', 'admin') DEFAULT 'member',
        is_muted BOOLEAN DEFAULT FALSE,
        is_blocked BOOLEAN DEFAULT FALSE,
        last_read_message_id INT NULL,
        unread_count INT DEFAULT 0,
        
        UNIQUE KEY unique_conversation_user (conversation_id, user_id),
        INDEX idx_conversation_id (conversation_id),
        INDEX idx_user_id (user_id),
        
        FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `, 'Creating conversation_participants table');

    // Create messages table
    await this.executeQuery(`
      CREATE TABLE IF NOT EXISTS messages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        conversation_id INT NOT NULL,
        sender_id INT NOT NULL,
        message_type ENUM('text', 'image', 'video', 'audio', 'file', 'system') DEFAULT 'text',
        content TEXT,
        file_url VARCHAR(500) NULL,
        file_size INT NULL,
        file_name VARCHAR(255) NULL,
        reply_to_message_id INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_deleted BOOLEAN DEFAULT FALSE,
        deleted_at TIMESTAMP NULL,
        
        INDEX idx_conversation_created (conversation_id, created_at DESC),
        INDEX idx_sender_id (sender_id),
        
        FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
        FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `, 'Creating messages table');

    // Create message_status table
    await this.executeQuery(`
      CREATE TABLE IF NOT EXISTS message_status (
        id INT PRIMARY KEY AUTO_INCREMENT,
        message_id INT NOT NULL,
        user_id INT NOT NULL,
        status ENUM('sent', 'delivered', 'read') NOT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_message_user_status (message_id, user_id, status),
        INDEX idx_message_id (message_id),
        INDEX idx_user_id (user_id),
        
        FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `, 'Creating message_status table');

    // Create user_presence table
    await this.executeQuery(`
      CREATE TABLE IF NOT EXISTS user_presence (
        user_id INT PRIMARY KEY,
        status ENUM('online', 'offline', 'away') DEFAULT 'offline',
        last_seen_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_typing_in_conversation_id INT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `, 'Creating user_presence table');

    // Create chat_settings table
    await this.executeQuery(`
      CREATE TABLE IF NOT EXISTS chat_settings (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        notification_enabled BOOLEAN DEFAULT TRUE,
        sound_enabled BOOLEAN DEFAULT TRUE,
        read_receipts_enabled BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_user_settings (user_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `, 'Creating chat_settings table');
  }

  async step2_MigrateConversations() {
    console.log('\n=== STEP 2: Migrating Conversations ===');
    
    await this.executeQuery(`
      INSERT INTO conversations (type, created_by, created_at, last_activity_at)
      SELECT DISTINCT
          'direct' as type,
          LEAST(sender, receiver) as created_by,
          MIN(createddate) as created_at,
          MAX(createddate) as last_activity_at
      FROM user_chat 
      WHERE sender IS NOT NULL 
        AND receiver IS NOT NULL 
        AND receiver != 0
        AND is_active = 1
      GROUP BY LEAST(sender, receiver), GREATEST(sender, receiver)
    `, 'Migrating conversations');
  }

  async step3_MigrateParticipants() {
    console.log('\n=== STEP 3: Migrating Participants ===');
    
    // First user (creator)
    await this.executeQuery(`
      INSERT INTO conversation_participants (conversation_id, user_id, joined_at)
      SELECT DISTINCT
          c.id as conversation_id,
          c.created_by as user_id,
          c.created_at as joined_at
      FROM conversations c
    `, 'Migrating conversation creators');

    // Second user
    await this.executeQuery(`
      INSERT IGNORE INTO conversation_participants (conversation_id, user_id, joined_at)
      SELECT DISTINCT
          c.id as conversation_id,
          CASE 
              WHEN uc.sender = c.created_by THEN uc.receiver
              ELSE uc.sender
          END as user_id,
          MIN(uc.createddate) as joined_at
      FROM conversations c
      JOIN user_chat uc ON (
          (uc.sender = c.created_by OR uc.receiver = c.created_by)
          AND uc.sender IS NOT NULL 
          AND uc.receiver IS NOT NULL 
          AND uc.receiver != 0
          AND uc.is_active = 1
      )
      WHERE CASE 
          WHEN uc.sender = c.created_by THEN uc.receiver
          ELSE uc.sender
      END != c.created_by
      GROUP BY c.id, CASE 
          WHEN uc.sender = c.created_by THEN uc.receiver
          ELSE uc.sender
      END
    `, 'Migrating conversation participants');
  }

  async step4_MigrateMessages() {
    console.log('\n=== STEP 4: Migrating Messages ===');
    
    await this.executeQuery(`
      INSERT INTO messages (
          conversation_id,
          sender_id,
          message_type,
          content,
          file_url,
          created_at,
          updated_at
      )
      SELECT 
          c.id as conversation_id,
          uc.sender as sender_id,
          'text' as message_type,
          uc.message as content,
          uc.message_file as file_url,
          uc.createddate as created_at,
          COALESCE(uc.updateddate, uc.createddate) as updated_at
      FROM user_chat uc
      JOIN conversations c ON (
          c.created_by = LEAST(uc.sender, uc.receiver)
      )
      WHERE uc.sender IS NOT NULL 
        AND uc.receiver IS NOT NULL 
        AND uc.receiver != 0
        AND uc.is_active = 1
      ORDER BY uc.createddate
    `, 'Migrating messages');
  }

  async step5_UpdateLastMessages() {
    console.log('\n=== STEP 5: Updating Last Messages ===');
    
    await this.executeQuery(`
      UPDATE conversations c
      SET last_message_id = (
          SELECT m.id 
          FROM messages m 
          WHERE m.conversation_id = c.id 
          ORDER BY m.created_at DESC 
          LIMIT 1
      )
    `, 'Updating last message references');
  }

  async verifyMigration() {
    console.log('\n=== VERIFICATION ===');
    
    const [conversations] = await this.connection.query('SELECT COUNT(*) as count FROM conversations');
    const [participants] = await this.connection.query('SELECT COUNT(*) as count FROM conversation_participants');
    const [messages] = await this.connection.query('SELECT COUNT(*) as count FROM messages');
    const [originalMessages] = await this.connection.query('SELECT COUNT(*) as count FROM user_chat WHERE is_active = 1');
    
    console.log(`📊 Migration Results:`);
    console.log(`   Conversations: ${conversations[0].count}`);
    console.log(`   Participants: ${participants[0].count}`);
    console.log(`   Messages: ${messages[0].count}`);
    console.log(`   Original messages: ${originalMessages[0].count}`);
    
    return messages[0].count > 0;
  }

  async run() {
    try {
      console.log('🚀 Starting Step-by-Step Chat Migration');
      console.log('=====================================');
      
      if (!(await this.connect())) {
        process.exit(1);
      }
      
      await this.step1_CreateTables();
      await this.step2_MigrateConversations();
      await this.step3_MigrateParticipants();
      await this.step4_MigrateMessages();
      await this.step5_UpdateLastMessages();
      
      const success = await this.verifyMigration();
      
      if (success) {
        console.log('=====================================');
        console.log('🎉 Migration completed successfully!');
      } else {
        throw new Error('Migration verification failed');
      }
      
    } catch (error) {
      console.log('=====================================');
      console.error('💥 Migration failed:', error.message);
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  const migration = new StepByStepMigration();
  migration.run();
}

module.exports = StepByStepMigration;
