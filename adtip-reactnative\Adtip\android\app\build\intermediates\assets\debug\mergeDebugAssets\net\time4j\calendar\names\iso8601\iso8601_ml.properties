# months
M(a)_1=ജനു
M(a)_2=ഫെബ്രു
M(a)_3=മാർ
M(a)_4=ഏപ്രി
M(a)_5=മേയ്
M(a)_6=ജൂൺ
M(a)_7=ജൂലൈ
M(a)_8=ഓഗ
M(a)_9=സെപ്റ്റം
M(a)_10=ഒക്ടോ
M(a)_11=നവം
M(a)_12=ഡിസം

M(n)_1=ജ
M(n)_2=ഫെ
M(n)_3=മാ
M(n)_4=ഏ
M(n)_5=മെ
M(n)_6=ജൂൺ
M(n)_7=ജൂ
M(n)_8=ഓ
M(n)_9=സെ
M(n)_10=ഒ
M(n)_11=ന
M(n)_12=ഡി

M(w)_1=ജനുവരി
M(w)_2=ഫെബ്രുവരി
M(w)_3=മാർച്ച്
M(w)_4=ഏപ്രിൽ
M(w)_5=മേയ്
M(w)_6=ജൂൺ
M(w)_7=ജൂലൈ
M(w)_8=ഓഗസ്റ്റ്
M(w)_9=സെപ്റ്റംബർ
M(w)_10=ഒക്‌ടോബർ
M(w)_11=നവംബർ
M(w)_12=ഡിസംബർ

M(A)_1=ജനു
M(A)_2=ഫെബ്രു
M(A)_3=മാർ
M(A)_4=ഏപ്രി
M(A)_5=മേയ്
M(A)_6=ജൂൺ
M(A)_7=ജൂലൈ
M(A)_8=ഓഗ
M(A)_9=സെപ്റ്റം
M(A)_10=ഒക്ടോ
M(A)_11=നവം
M(A)_12=ഡിസം

M(N)_1=ജ
M(N)_2=ഫെ
M(N)_3=മാ
M(N)_4=ഏ
M(N)_5=മെ
M(N)_6=ജൂൺ
M(N)_7=ജൂ
M(N)_8=ഓ
M(N)_9=സെ
M(N)_10=ഒ
M(N)_11=ന
M(N)_12=ഡി

M(W)_1=ജനുവരി
M(W)_2=ഫെബ്രുവരി
M(W)_3=മാർച്ച്
M(W)_4=ഏപ്രിൽ
M(W)_5=മേയ്
M(W)_6=ജൂൺ
M(W)_7=ജൂലൈ
M(W)_8=ഓഗസ്റ്റ്
M(W)_9=സെപ്റ്റംബർ
M(W)_10=ഒക്‌ടോബർ
M(W)_11=നവംബർ
M(W)_12=ഡിസംബർ

# weekdays
D(a)_1=തിങ്കൾ
D(a)_2=ചൊവ്വ
D(a)_3=ബുധൻ
D(a)_4=വ്യാഴം
D(a)_5=വെള്ളി
D(a)_6=ശനി
D(a)_7=ഞായർ

D(n)_1=തി
D(n)_2=ചൊ
D(n)_3=ബു
D(n)_4=വ്യാ
D(n)_5=വെ
D(n)_6=ശ
D(n)_7=ഞ

D(s)_1=തി
D(s)_2=ചൊ
D(s)_3=ബു
D(s)_4=വ്യാ
D(s)_5=വെ
D(s)_6=ശ
D(s)_7=ഞാ

D(w)_1=തിങ്കളാഴ്‌ച
D(w)_2=ചൊവ്വാഴ്ച
D(w)_3=ബുധനാഴ്‌ച
D(w)_4=വ്യാഴാഴ്‌ച
D(w)_5=വെള്ളിയാഴ്‌ച
D(w)_6=ശനിയാഴ്‌ച
D(w)_7=ഞായറാഴ്‌ച

D(A)_1=തിങ്കൾ
D(A)_2=ചൊവ്വ
D(A)_3=ബുധൻ
D(A)_4=വ്യാഴം
D(A)_5=വെള്ളി
D(A)_6=ശനി
D(A)_7=ഞായർ

D(N)_1=തി
D(N)_2=ചൊ
D(N)_3=ബു
D(N)_4=വ്യാ
D(N)_5=വെ
D(N)_6=ശ
D(N)_7=ഞാ

D(S)_1=തി
D(S)_2=ചൊ
D(S)_3=ബു
D(S)_4=വ്യാ
D(S)_5=വെ
D(S)_6=ശ
D(S)_7=ഞാ

D(W)_1=തിങ്കളാഴ്‌ച
D(W)_2=ചൊവ്വാഴ്‌ച
D(W)_3=ബുധനാഴ്‌ച
D(W)_4=വ്യാഴാഴ്‌ച
D(W)_5=വെള്ളിയാഴ്‌ച
D(W)_6=ശനിയാഴ്‌ച
D(W)_7=ഞായറാഴ്‌ച

# quarters
Q(a)_1=ഒന്നാം പാദം
Q(a)_2=രണ്ടാം പാദം
Q(a)_3=മൂന്നാം പാദം
Q(a)_4=നാലാം പാദം

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=ഒന്നാം പാദം
Q(w)_2=രണ്ടാം പാദം
Q(w)_3=മൂന്നാം പാദം
Q(w)_4=നാലാം പാദം

Q(A)_1=ഒന്നാം പാദം
Q(A)_2=രണ്ടാം പാദം
Q(A)_3=മൂന്നാം പാദം
Q(A)_4=നാലാം പാദം

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=ഒന്നാം പാദം
Q(W)_2=രണ്ടാം പാദം
Q(W)_3=മൂന്നാം പാദം
Q(W)_4=നാലാം പാദം

# day-period-rules
T0300=morning1
T0600=morning2
T1200=afternoon1
T1400=afternoon2
T1500=evening1
T1800=evening2
T1900=night1

# day-period-translations
P(a)_midnight=അർദ്ധരാത്രി
P(a)_am=AM
P(a)_noon=ഉച്ച
P(a)_pm=PM
P(a)_morning1=പുലർച്ചെ
P(a)_morning2=രാവിലെ
P(a)_afternoon1=ഉച്ചയ്ക്ക്
P(a)_afternoon2=ഉച്ചതിരിഞ്ഞ്
P(a)_evening1=വൈകുന്നേരം
P(a)_evening2=സന്ധ്യ
P(a)_night1=രാത്രി

P(n)_midnight=അ
P(n)_am=AM
P(n)_noon=ഉച്ച
P(n)_pm=PM
P(n)_morning1=പുലർച്ചെ
P(n)_morning2=രാവിലെ
P(n)_afternoon1=ഉച്ചയ്ക്ക്
P(n)_afternoon2=ഉച്ചതിരിഞ്ഞ്
P(n)_evening1=വൈകുന്നേരം
P(n)_evening2=സന്ധ്യ
P(n)_night1=രാത്രി

P(w)_midnight=അർദ്ധരാത്രി
P(w)_am=AM
P(w)_noon=ഉച്ച
P(w)_pm=PM
P(w)_morning1=പുലർച്ചെ
P(w)_morning2=രാവിലെ
P(w)_afternoon1=ഉച്ചയ്ക്ക്
P(w)_afternoon2=ഉച്ചതിരിഞ്ഞ്
P(w)_evening1=വൈകുന്നേരം
P(w)_evening2=സന്ധ്യ
P(w)_night1=രാത്രി

P(A)_midnight=അർദ്ധരാത്രി
P(A)_am=AM
P(A)_noon=ഉച്ച
P(A)_pm=PM
P(A)_morning1=പുലർച്ചെ
P(A)_morning2=രാവിലെ
P(A)_afternoon1=ഉച്ചയ്ക്ക്
P(A)_afternoon2=ഉച്ചതിരിഞ്ഞ്
P(A)_evening1=വൈകുന്നേരം
P(A)_evening2=സന്ധ്യ
P(A)_night1=രാത്രി

P(N)_midnight=അർദ്ധരാത്രി
P(N)_am=AM
P(N)_noon=ഉച്ച
P(N)_pm=PM
P(N)_morning1=പുലർച്ചെ
P(N)_morning2=രാവിലെ
P(N)_afternoon1=ഉച്ചയ്ക്ക്
P(N)_afternoon2=ഉച്ചതിരിഞ്ഞ്
P(N)_evening1=വൈകുന്നേരം
P(N)_evening2=സന്ധ്യ
P(N)_night1=രാത്രി

P(W)_midnight=അർദ്ധരാത്രി
P(W)_am=AM
P(W)_noon=ഉച്ച
P(W)_pm=PM
P(W)_morning1=പുലർച്ചെ
P(W)_morning2=രാവിലെ
P(W)_afternoon1=ഉച്ചയ്ക്ക്
P(W)_afternoon2=ഉച്ചതിരിഞ്ഞ്
P(W)_evening1=വൈകുന്നേരം
P(W)_evening2=സന്ധ്യ
P(W)_night1=രാത്രി

# eras
E(w)_0=ക്രിസ്‌തുവിന് മുമ്പ്
E(w|alt)_0=ബി.സി.ഇ.
E(w)_1=ആന്നോ ഡൊമിനി
E(w|alt)_1=സി.ഇ.

E(a)_0=ക്രി.മു.
E(a|alt)_0=ബിസിഇ
E(a)_1=എഡി
E(a|alt)_1=സിഇ

E(n)_0=ക്രി.മു.
E(n|alt)_0=ബിസിഇ
E(n)_1=എഡി
E(n|alt)_1=സിഇ

# format patterns
F(f)_d=y, MMMM d, EEEE
F(l)_d=y, MMMM d
F(m)_d=y, MMM d
F(s)_d=d/M/yy

F(alt)=h:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=B h
F_Bhm=B h:mm
F_Bhms=B h:mm:ss
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=MMM d
F_MMMMd=MMMM d
F_y=y
F_yM=y-MM
F_yMM=y-MM
F_yMMM=y MMM
F_yMMMM=y MMMM
F_yQQQ=y QQQ
F_yQQQQ=y QQQQ
F_yw=Y-ലെ ആഴ്ച w

I={0} - {1}

# labels of elements
L_era=കാലഘട്ടം
L_year=വർഷം
L_quarter=പാദം
L_month=മാസം
L_week=ആഴ്ച
L_day=ദിവസം
L_weekday=ആഴ്ചയിലെ ദിവസം
L_dayperiod=AM/PM
L_hour=മണിക്കൂർ
L_minute=മിനിറ്റ്
L_second=സെക്കൻഡ്
L_zone=സമയ മേഖല
