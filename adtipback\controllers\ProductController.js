const md5 = require("md5");
const querystring = require("querystring");
const moment = require("moment");
let productService = require("../services/ProductService");
const pubscaleService = require("../services/pubscaleService");

//for pubscale
const ALLOWED_IP = "*************"; // PubScale server IP
const SECRET_KEY = "decdfc95-bd89-4226-a060-163af9fdb065"; // secret key

module.exports = {
  //getCategoryProduct
  //getProductListByAdvertiserId
  //getProductByCategoryId
  //getSingleProductById
  //addToCart
  //getCartItems
  //addAddress
  //deliveryAddress
  //selectDeliveryAddress
  //selectedDeliveryAddress
  //placeOrder
  //payUsingWallet
  //addToBargain
  //getBaraginItems
  //updateBargin
  //deleteCartSingleItem
  //deleteCartAllItems
  //getSellerOrders
  //getUserOrders
  //updateSellerShippingDetails
  //addToWishList
  //getWishList
  //deleteWishListItem
  //addProductToPopular
  //getPopularProducts
  //getSearchProducts
  //getSearchCompany
  getSearchCompany: (req, res, next) => {
    productService
      .getSearchCompany(req.params.search)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getSearchProducts: (req, res, next) => {
    productService
      .getSearchProducts(req.params.search)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getPopularProducts: (req, res, next) => {
    productService
      .getPopularProducts()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addProductToPopular: (req, res, next) => {
    productService
      .addProductToPopular(req.params.Id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deleteWishListItem: (req, res, next) => {
    productService
      .deleteWishListItem(req.params.Id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getWishList: (req, res, next) => {
    productService
      .getWishList(req.params.Id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addToWishList: (req, res, next) => {
    productService
      .addToWishList(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateSellerShippingDetails: (req, res, next) => {
    productService
      .updateSellerShippingDetails(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getUserOrders: (req, res, next) => {
    productService
      .getUserOrders(req.params.userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getSellerOrders: (req, res, next) => {
    productService
      .getSellerOrders(req.params.userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deleteCartAllItems: (req, res, next) => {
    productService
      .deleteCartAllItems(req.params.userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deleteCartSingleItem: (req, res, next) => {
    productService
      .deleteCartSingleItem(req.params.cartId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateBargin: (req, res, next) => {
    productService
      .updateBargin(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getBaraginItems: (req, res, next) => {
    productService
      .getBaraginItems(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addToBargain: (req, res, next) => {
    productService
      .addToBargain(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  payUsingWallet: (req, res, next) => {
    productService
      .payUsingWallet(req.params.userId, req.params.requestAmount)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  placeOrder: (req, res, next) => {
    productService
      .placeOrder(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  selectedDeliveryAddress: (req, res, next) => {
    productService
      .selectedDeliveryAddress(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  selectDeliveryAddress: (req, res, next) => {
    productService
      .selectDeliveryAddress(req.params.addressId, req.params.userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deliveryAddress: (req, res, next) => {
    productService
      .deliveryAddress(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addAddress: (req, res, next) => {
    productService
      .addAddress(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getCartItems: (req, res, next) => {
    productService
      .getCartItems(req.params.Id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addToCart: (req, res, next) => {
    productService
      .addToCart(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getSingleProductById: (req, res, next) => {
    productService
      .getSingleProductById(req.params.Id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getProductByCategoryId: (req, res, next) => {
    productService
      .getProductByCategoryId(req.params.Id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getProductListByAdvertiserId: (req, res, next) => {
    productService
      .getProductListByAdvertiserId(req.params.advertiserId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getCategoryProduct: (req, res, next) => {
    productService
      .getCategoryProduct()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  addCategory: (req, res, next) => {
    productService
      .addCategory(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveProduct: (req, res, next) => {
    productService
      .saveProduct(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateProduct: (req, res, next) => {
    productService
      .updateProduct(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deleteProduct: (req, res, next) => {
    productService
      .deleteProduct(req.body.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getProductList: (req, res, next) => {
    productService
      .getProductList(req.params.companyId)
      .then((result) => {
        res.status(result.status || 200).send(result);
        //  if (result && result.status === 200) {
        //     result.data.forEach(data => {
        //          if (data.images != null && data.images.length != 0) {
        //             data.images = JSON.parse(data.images);
        //         }
        //      });
        //  res.status(result.status || 200).send(result);
        // } else {
        //      res.status(result.status || 200).send(result);
        // }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getAllProductList: (req, res, next) => {
    productService
      .getAllProductList()
      .then((result) => {
        res.status(result.status || 200).send(result);
        //  if (result && result.status === 200) {
        //     result.data.forEach(data => {
        //          if (data.images != null && data.images.length != 0) {
        //             data.images = JSON.parse(data.images);
        //         }
        //      });
        //  res.status(result.status || 200).send(result);
        // } else {
        //      res.status(result.status || 200).send(result);
        // }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getProductCategory: (req, res, next) => {
    productService
      .getProductCategory()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getProductByProductId: (req, res, next) => {
    if (!req.params.id && !req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getProductByProductId(req.params.id, req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getProductByUserId: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getProductByUserId(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getProductByCategory: (req, res, next) => {
    if (!req.params.categoryid && !req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getProductByCategory(req.params.categoryid, req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getProductByCompanyId: (req, res, next) => {
    if (!req.params.companyid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getProductByCompanyId(req.params.companyid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getNewProduct: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getNewProduct(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getSearchList: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getSearchList(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveCartItem: (req, res, next) => {
    if (
      !req.body[0].productId &&
      !req.body[0].quantity &&
      !req.body[0].createdBy
    )
      return res
        .status(400)
        .send({ status: 400, message: "Invalid Body.", data: [req.body] });
    productService
      .saveCartItem(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveSearchProduct: (req, res, next) => {
    if (!req.body.userId && !req.body.name)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid Body.", data: [req.body] });
    productService
      .saveSearchProduct(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getSearchResult: (req, res, next) => {
    if (!req.body.userId && !req.body.name)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid Body.", data: [req.body] });
    productService
      .getSearchResult(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getSimilarsearch: (req, res, next) => {
    if (!req.body.userId && !req.body.name)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid Body.", data: [req.body] });
    productService
      .getSimilarsearch(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveProductDetails: (req, res, next) => {
    return productService
      .saveProductDetails(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getCartItemByUser: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getCartItemByUser(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getProductAds: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getProductAds(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getRecentlyViewProducts: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getRecentlyViewProducts(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  deleteCartItembyCartId: (req, res, next) => {
    if (!req.params.cartid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .deleteCartItembyCartId(req.params.cartid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getOrderList: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getOrderList(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveBargainRequest: (req, res, next) => {
    return productService
      .saveBargainRequest(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getBargainRequestByUser: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getBargainRequestByUser(req.params.userid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.companyProfileFilePath = `${req.headers.host}/api/photo/${data.companyProfileFilename}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getBargainRequestByCompany: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getBargainRequestByCompany(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveUserAddress: (req, res, next) => {
    return productService
      .saveUserAddress(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getUserAddressByUser: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getUserAddressByUser(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveProductOrder: (req, res, next) => {
    return productService
      .saveProductOrder(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  updateBarginStatus: (req, res, next) => {
    return productService
      .updateBarginStatus(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getWishlistProductByUserId: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getWishlistProductByUserId(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  updateCartProduct: (req, res, next) => {
    if (
      !req.body.productId &&
      !req.body.quantity &&
      !req.body.size &&
      !req.body.createdBy
    )
      return res
        .status(400)
        .send({ status: 400, message: "Invalid Body.", data: [req.body] });
    productService
      .updateCartProduct(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getCouponsbyCategory: (req, res, next) => {
    if (!req.params.category)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getCouponsbyCategory(req.params.category)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getOrdersByUserId: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .getOrdersByUserId(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  deleteUserAddress: (req, res, next) => {
    if (!req.params.id)
      return res
        .status(400)
        .send({ status: 400, message: "Id not found.", data: [req.params] });
    productService
      .deleteUserAddress(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  //pubsclae:
  handlePubscaleCallback: (req, res) => {
    // IP Whitelisting
    /*
    const clientIp = req.ip || req.connection.remoteAddress;
    if (clientIp !== ALLOWED_IP && clientIp !== `::ffff:${ALLOWED_IP}`) {
      return res.status(403).send({
        status: 403,
        message: "Unauthorized IP address",
        data: [],
      });
    }
    */

    const { user_id, value, token, signature } = req.query;

    if (!user_id || !value || !token || !signature) {
      return res.status(400).send({
        status: 400,
        message: "Missing required parameters",
        data: [],
      });
    }

    // Validate hash
    const integerValue = Math.trunc(parseFloat(value));
    const template = `${SECRET_KEY}.${user_id}.${integerValue}.${token}`;
    const calculatedHash = md5(template);

    if (calculatedHash !== signature) {
      return res.status(401).send({
        status: 401,
        message: "Invalid signature",
        data: [],
      });
    }

    pubscaleService
      .processCallback({
        user_id,
        value: parseFloat(value),
        token,
        signature,
      })
      .then((result) => {
        if (!result.status) {
          return res.status(result.statusCode || 400).send({
            status: result.statusCode || 400,
            message: result.message,
            data: [],
          });
        }

        return res.status(200).send({
          status: 200,
          message: "Callback processed successfully",
          data: [{ token }],
        });
      })
      .catch((err) => {
        console.error("Error in PubScale callback:", err);
        return res.status(500).send({
          status: 500,
          message: err.message || "Internal server error",
          data: [],
        });
      });
  },

};
