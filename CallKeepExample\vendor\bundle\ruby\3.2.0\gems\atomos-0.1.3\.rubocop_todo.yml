# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2018-02-02 08:32:23 -0800 using RuboCop version 0.52.1.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 1
# Configuration parameters: Include.
# Include: **/*.gemspec
Gemspec/RequiredRubyVersion:
  Exclude:
    - 'atomos.gemspec'

# Offense count: 1
# Configuration parameters: CountComments.
Metrics/MethodLength:
  Max: 14

# Offense count: 1
Style/Documentation:
  Exclude:
    - 'spec/**/*'
    - 'test/**/*'
    - 'lib/atomos.rb'

# Offense count: 7
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Metrics/LineLength:
  Max: 97
