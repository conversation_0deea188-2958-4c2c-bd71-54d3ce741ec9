const crypto = require('crypto');
const { queryRunner } = require('../dbConfig/queryRunner');
const razorpayInstance = require('../services/Razorpay');

// Get available content creator premium plans
exports.getContentPremiumPlans = async (req, res) => {
  try {
    const query = 'SELECT * FROM content_creator_razorpay_plans WHERE is_active = TRUE ORDER BY amount ASC';
    const plans = await queryRunner(query);
    res.status(200).json({ status: true, plans });
  } catch (error) {
    console.error('Error fetching content creator premium plans:', error);
    res.status(500).json({ status: false, message: 'Error fetching plans' });
  }
};

// Create a new content creator premium subscription
exports.createContentPremiumSubscription = async (req, res) => {
  const { plan_id, user_id } = req.body;
  if (!plan_id || !user_id) {
    return res.status(400).json({ status: false, message: 'plan_id and user_id are required' });
  }
  try {
    // Fetch plan details
    const planQuery = 'SELECT * FROM content_creator_razorpay_plans WHERE id = ?';
    const plan = await queryRunner(planQuery, [plan_id]);
    if (!plan.length) {
      return res.status(404).json({ status: false, message: 'Plan not found' });
    }
    // Create subscription on Razorpay
    const notes = { user_id: user_id.toString(), type: 'content_creator_premium' };
    const subscription = await razorpayInstance.subscriptions.create({
      plan_id,
      customer_notify: 1,
      total_count: plan[0].period ? plan[0].period * 5 : 60, // default 5 years
      notes
    });
    res.status(201).json({ status: true, subscription_id: subscription.id });
  } catch (error) {
    console.error('Error creating content creator premium subscription:', error);
    res.status(500).json({ status: false, message: 'Error creating subscription' });
  }
};

// Cancel a content creator premium subscription
exports.cancelContentPremiumSubscription = async (req, res) => {
  const { user_id } = req.body;
  if (!user_id) {
    return res.status(400).json({ status: false, message: 'user_id is required' });
  }
  try {
    // Find the active subscription for the user
    const findQuery = "SELECT razorpay_subscription_id FROM content_creator_subscriptions WHERE user_id = ? AND status = 'active'";
    const subscriptions = await queryRunner(findQuery, [user_id]);
    if (subscriptions.length === 0) {
      return res.status(404).json({ status: false, message: 'No active content creator subscription found for this user.' });
    }
    const subId = subscriptions[0].razorpay_subscription_id;
    // Cancel on Razorpay
    await razorpayInstance.subscriptions.cancel(subId, { cancel_at_cycle_end: true });
    // Update database status to cancelled
    const updateQuery = "UPDATE content_creator_subscriptions SET status = 'cancelled', updated_at = NOW() WHERE razorpay_subscription_id = ?";
    await queryRunner(updateQuery, [subId]);
    // Update user's content creator premium status
    const updateUserQuery = "UPDATE users SET content_creator_premium_status = FALSE, content_creator_premium_expires_at = NULL WHERE id = ?";
    await queryRunner(updateUserQuery, [user_id]);
    res.status(200).json({ status: true, message: 'Content creator subscription cancellation initiated. It will be fully cancelled at the end of the current billing cycle.' });
  } catch (error) {
    console.error('Error cancelling content creator subscription:', error);
    res.status(500).json({ status: false, message: 'Error cancelling content creator subscription', error: error.message });
  }
};

// Get content creator premium subscription status
exports.getContentPremiumStatus = async (req, res) => {
  const { userId } = req.params;
  if (!userId) {
    return res.status(400).json({ status: false, message: 'User ID is required' });
  }
  try {
    const query = `
      SELECT 
        s.status, 
        s.current_end_at,
        s.razorpay_plan_id,
        s.razorpay_subscription_id,
        s.paid_count,
        s.total_count,
        s.start_at,
        s.ended_at,
        s.created_at,
        p.name as plan_name,
        p.description as plan_description,
        p.amount,
        p.currency,
        p.plan_interval,
        p.billing_cycle,
        u.content_creator_premium_status,
        u.content_creator_premium_expires_at
      FROM content_creator_subscriptions s
      LEFT JOIN content_creator_razorpay_plans p ON s.razorpay_plan_id = p.id
      LEFT JOIN users u ON s.user_id = u.id
      WHERE s.user_id = ?
      ORDER BY s.created_at DESC
      LIMIT 1
    `;
    const result = await queryRunner(query, [userId]);
    if (result.length > 0) {
      const subscription = result[0];
      const isActive = subscription.status === 'active' && 
                      subscription.current_end_at && 
                      new Date(subscription.current_end_at) > new Date();
      
      // Fallback plans for content creator premium (same as regular premium but with content creator names)
      const fallbackPlans = {
        'plan_QngI8gqKZrHLbK': {
          name: 'Content Creator Premium - 1 Month',
          description: 'Access to premium content creation features for 1 month',
          amount: 2500,
          currency: 'INR',
          plan_interval: 'monthly',
          billing_cycle: '1 month'
        },
        'plan_QngId7JYoii1wE': {
          name: 'Content Creator Premium - 3 Months',
          description: 'Access to premium content creation features for 3 months',
          amount: 6000,
          currency: 'INR',
          plan_interval: 'monthly',
          billing_cycle: '3 months'
        },
        'plan_QngJ4kIVdFaRUI': {
          name: 'Content Creator Premium - 6 Months',
          description: 'Access to premium content creation features for 6 months',
          amount: 12000,
          currency: 'INR',
          plan_interval: 'monthly',
          billing_cycle: '6 months'
        },
        'plan_QngKCqLAUkhxbH': {
          name: 'Content Creator Premium - 12 Months',
          description: 'Access to premium content creation features for 12 months',
          amount: 24000,
          currency: 'INR',
          plan_interval: 'monthly',
          billing_cycle: '12 months'
        }
      };

      // If plan details are missing, populate from fallback
      if (!subscription.plan_name && subscription.razorpay_plan_id && fallbackPlans[subscription.razorpay_plan_id]) {
        const fallbackPlan = fallbackPlans[subscription.razorpay_plan_id];
        subscription.plan_name = fallbackPlan.name;
        subscription.plan_description = fallbackPlan.description;
        subscription.amount = fallbackPlan.amount;
        subscription.currency = fallbackPlan.currency;
        subscription.plan_interval = fallbackPlan.plan_interval;
        subscription.billing_cycle = fallbackPlan.billing_cycle;
      }

      res.status(200).json({ 
        status: true, 
        data: {
          ...subscription,
          is_active: isActive
        }
      });
    } else {
      res.status(200).json({ status: false, message: 'No content creator subscription found', data: null });
    }
  } catch (error) {
    console.error('Error getting content creator subscription status:', error);
    res.status(500).json({ status: false, message: 'Error fetching content creator subscription status' });
  }
};

// Get Razorpay key for frontend
exports.getContentPremiumRazorpayDetails = async (req, res) => {
  try {
    res.status(200).json({ status: true, api_key: process.env.RAZOR_PAY_KEY_ID });
  } catch (error) {
    console.error('Error getting Razorpay details:', error);
    res.status(500).json({ status: false, message: 'Error fetching Razorpay details' });
  }
};

// Razorpay webhook for content creator premium
exports.handleContentPremiumWebhook = async (req, res) => {
  const secret = process.env.RAZORPAY_WEBHOOK_SECRET || 'adtip_webhook_2024_secret';
  const signature = req.headers["x-razorpay-signature"];
  try {
    // Log every webhook event for debugging
    console.log('Received Razorpay webhook event (content creator):', req.body.event, JSON.stringify(req.body, null, 2));
    // Verify webhook signature
    const shasum = crypto.createHmac("sha256", secret);
    shasum.update(JSON.stringify(req.body));
    const digest = shasum.digest("hex");
    if (digest !== signature) {
      return res.status(400).json({ status: "Signature is not valid" });
    }
    const event = req.body.event;
    const payload = req.body.payload;
    if (event === "subscription.charged") {
      const sub = payload.subscription.entity;
      const payment = payload.payment.entity;
      if (payment.status === 'captured') {
        // Check if subscription already exists
        const checkQuery = "SELECT id FROM content_creator_subscriptions WHERE razorpay_subscription_id = ?";
        const existingSub = await queryRunner(checkQuery, [sub.id]);
        if (existingSub.length === 0) {
          // First successful payment - create new subscription record
          const insertQuery = `
            INSERT INTO content_creator_subscriptions (
              user_id, razorpay_plan_id, razorpay_subscription_id, status, 
              total_count, paid_count, current_start_at, current_end_at, 
              charge_at, start_at, customer_notify, notes
            )
            VALUES (?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?), FROM_UNIXTIME(?), FROM_UNIXTIME(?), FROM_UNIXTIME(?), ?, ?)
          `;
          await queryRunner(insertQuery, [
            sub.notes.user_id,
            sub.plan_id,
            sub.id,
            sub.status,
            sub.total_count,
            sub.paid_count,
            sub.current_start,
            sub.current_end,
            sub.charge_at,
            sub.start_at,
            sub.customer_notify,
            JSON.stringify(sub.notes)
          ]);
        } else {
          // Subsequent payment - update existing subscription
          const updateQuery = `
            UPDATE content_creator_subscriptions 
            SET status = ?, paid_count = ?, current_start_at = FROM_UNIXTIME(?), 
                current_end_at = FROM_UNIXTIME(?), charge_at = FROM_UNIXTIME(?), updated_at = NOW()
            WHERE razorpay_subscription_id = ?
          `;
          await queryRunner(updateQuery, [
            sub.status,
            sub.paid_count,
            sub.current_start,
            sub.current_end,
            sub.charge_at,
            sub.id
          ]);
        }
        // Update user's content creator premium status
        const updateUserQuery = `
          UPDATE users 
          SET content_creator_premium_status = TRUE, 
              content_creator_premium_expires_at = FROM_UNIXTIME(?)
          WHERE id = ?
        `;
        await queryRunner(updateUserQuery, [sub.current_end, sub.notes.user_id]);
      }
      // Note: Razorpay will only charge up to total_count (e.g., 60 months = 5 years)
    } else if (event === 'subscription.cancelled' || event === 'subscription.halted' || event === 'subscription.completed') {
      const sub = payload.subscription.entity;
      // Update subscription status
      const updateQuery = `
        UPDATE content_creator_subscriptions 
        SET status = ?, ended_at = FROM_UNIXTIME(?), updated_at = NOW()
        WHERE razorpay_subscription_id = ?
      `;
      await queryRunner(updateQuery, [sub.status, sub.ended_at, sub.id]);
      // Update user's content creator premium status to expired
      if (sub.notes && sub.notes.user_id) {
        const updateUserQuery = "UPDATE users SET content_creator_premium_status = FALSE, content_creator_premium_expires_at = NULL WHERE id = ?";
        await queryRunner(updateUserQuery, [sub.notes.user_id]);
      }
    }
    res.status(200).json({ status: "ok" });
  } catch (error) {
    console.error("Content creator webhook handling error:", error);
    res.status(500).json({ status: "error" });
  }
}; 