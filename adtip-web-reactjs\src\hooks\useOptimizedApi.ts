// Custom hooks for optimized API calls with React Query integration
// Combines React Query with our OptimizedApiService for maximum performance

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { optimizedApiService } from '../services/OptimizedApiService';
import { queryKeys } from '../lib/queryClient';
import { Post, WalletResponse, ApiResponse } from '../types/api';
import { logDebug } from '../utils/ProductionLogger';

// Generic optimized query hook
export function useOptimizedQuery<T = any>(
  queryKey: any[],
  url: string,
  options?: {
    params?: Record<string, any>;
    cacheStrategy?: 'cache-first' | 'network-first' | 'cache-only' | 'network-only';
    cacheTTL?: number;
    enabled?: boolean;
    staleTime?: number;
    refetchInterval?: number;
  } & Omit<UseQueryOptions<T>, 'queryKey' | 'queryFn'>
) {
  const {
    params,
    cacheStrategy = 'cache-first',
    cacheTTL = 5 * 60 * 1000, // 5 minutes
    enabled = true,
    staleTime,
    refetchInterval,
    ...queryOptions
  } = options || {};

  return useQuery<T>({
    queryKey: params ? [...queryKey, params] : queryKey,
    queryFn: async ({ signal }) => {
      logDebug('useOptimizedQuery', 'Fetching data', { url, params, cacheStrategy });
      
      const response = await optimizedApiService.get<T>(url, {
        params,
        signal,
        cache: {
          strategy: cacheStrategy,
          ttl: cacheTTL,
        },
        dedupe: true,
        retry: {
          attempts: 3,
          exponentialBackoff: true,
        },
      });
      
      return response.data;
    },
    enabled,
    staleTime: staleTime || (cacheStrategy === 'cache-first' ? cacheTTL : 0),
    refetchInterval,
    ...queryOptions,
  });
}

// Posts-related hooks
export function usePosts(
  category?: string,
  page: number = 1,
  userId?: string,
  options?: {
    cacheStrategy?: 'cache-first' | 'network-first';
    enabled?: boolean;
  }
) {
  const params = {
    category: category === 'All' ? undefined : category,
    page: page.toString(),
    loggined_user_id: userId,
  };

  return useOptimizedQuery<ApiResponse<Post[]>>(
    queryKeys.posts.list(params),
    '/api/list-posts',
    {
      params,
      cacheStrategy: options?.cacheStrategy || 'cache-first',
      cacheTTL: 3 * 60 * 1000, // 3 minutes for posts
      enabled: options?.enabled,
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
}

export function usePremiumPosts(options?: { enabled?: boolean }) {
  return useOptimizedQuery<ApiResponse<Post[]>>(
    queryKeys.posts.premium(),
    '/api/list-premium-posts',
    {
      cacheStrategy: 'cache-first',
      cacheTTL: 5 * 60 * 1000, // 5 minutes for premium posts
      enabled: options?.enabled,
      staleTime: 3 * 60 * 1000, // 3 minutes
    }
  );
}

// Wallet-related hooks
export function useWalletBalance(
  userId: string,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
  }
) {
  return useOptimizedQuery<WalletResponse>(
    queryKeys.user.wallet(userId),
    `/api/getfunds/${userId}`,
    {
      cacheStrategy: 'network-first', // Always get fresh wallet data
      cacheTTL: 1 * 60 * 1000, // 1 minute cache for wallet
      enabled: options?.enabled && !!userId,
      staleTime: 30 * 1000, // 30 seconds
      refetchInterval: options?.refetchInterval || 60 * 1000, // Refetch every minute
    }
  );
}

// User profile hooks
export function useUserProfile(userId: string, options?: { enabled?: boolean }) {
  return useOptimizedQuery(
    queryKeys.user.profile(userId),
    `/api/getuserbyid/${userId}`,
    {
      cacheStrategy: 'cache-first',
      cacheTTL: 10 * 60 * 1000, // 10 minutes for user profiles
      enabled: options?.enabled && !!userId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Optimized mutation hook
export function useOptimizedMutation<TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: {
    onSuccess?: (data: TData, variables: TVariables) => void;
    onError?: (error: any, variables: TVariables) => void;
    invalidateQueries?: any[][];
    optimisticUpdate?: {
      queryKey: any[];
      updater: (oldData: any, variables: TVariables) => any;
    };
  } & Omit<UseMutationOptions<TData, any, TVariables>, 'mutationFn'>
) {
  const queryClient = useQueryClient();
  const { invalidateQueries, optimisticUpdate, onSuccess, onError, ...mutationOptions } = options || {};

  return useMutation<TData, any, TVariables>({
    mutationFn,
    onMutate: async (variables) => {
      // Optimistic update
      if (optimisticUpdate) {
        await queryClient.cancelQueries({ queryKey: optimisticUpdate.queryKey });
        const previousData = queryClient.getQueryData(optimisticUpdate.queryKey);
        
        queryClient.setQueryData(
          optimisticUpdate.queryKey,
          optimisticUpdate.updater(previousData, variables)
        );
        
        return { previousData };
      }
    },
    onSuccess: (data, variables, context) => {
      // Invalidate related queries
      if (invalidateQueries) {
        invalidateQueries.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
      
      onSuccess?.(data, variables);
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update on error
      if (optimisticUpdate && context?.previousData) {
        queryClient.setQueryData(optimisticUpdate.queryKey, context.previousData);
      }
      
      onError?.(error, variables);
    },
    ...mutationOptions,
  });
}

// Like post mutation with optimistic updates
export function useLikePost() {
  const queryClient = useQueryClient();

  return useOptimizedMutation(
    async ({ postId, userId, isLiked }: { postId: number; userId: string; isLiked: boolean }) => {
      const response = await optimizedApiService.post('/api/save-user-post-like', {
        post_id: postId,
        user_id: userId,
        is_liked: isLiked ? 1 : 0,
      });
      return response.data;
    },
    {
      onSuccess: () => {
        // Invalidate posts queries to refresh like counts
        queryClient.invalidateQueries({ queryKey: queryKeys.posts.all });
      },
      optimisticUpdate: {
        queryKey: queryKeys.posts.all,
        updater: (oldData: any, variables) => {
          if (!oldData?.data) return oldData;
          
          return {
            ...oldData,
            data: oldData.data.map((post: Post) =>
              post.id === variables.postId
                ? {
                    ...post,
                    likeCount: variables.isLiked 
                      ? post.likeCount + 1 
                      : Math.max(0, post.likeCount - 1),
                  }
                : post
            ),
          };
        },
      },
    }
  );
}

// Upload file mutation
export function useUploadFile() {
  return useOptimizedMutation(
    async ({ file, folder, userId }: { file: File; folder: string; userId?: number }) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', folder);
      if (userId) formData.append('userId', userId.toString());

      const response = await optimizedApiService.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        cache: {
          strategy: 'network-only', // Never cache uploads
        },
        dedupe: false, // Don't dedupe uploads
      });
      
      return response.data;
    }
  );
}

// Prefetch utilities
export const prefetchUtils = {
  // Prefetch posts for next page
  prefetchNextPage: async (category: string, currentPage: number, userId?: string, queryClient: any) => {
    const nextPage = currentPage + 1;
    
    const params = {
      category: category === 'All' ? undefined : category,
      page: nextPage.toString(),
      loggined_user_id: userId,
    };

    await queryClient.prefetchQuery({
      queryKey: queryKeys.posts.list(params),
      queryFn: async () => {
        const response = await optimizedApiService.get<ApiResponse<Post[]>>('/api/list-posts', {
          params,
          cache: {
            strategy: 'cache-first',
            ttl: 3 * 60 * 1000,
          },
        });
        return response.data;
      },
      staleTime: 2 * 60 * 1000,
    });
  },

  // Prefetch user wallet
  prefetchWallet: async (userId: string, queryClient: any) => {
    
    await queryClient.prefetchQuery({
      queryKey: queryKeys.user.wallet(userId),
      queryFn: async () => {
        const response = await optimizedApiService.get<WalletResponse>(`/api/getfunds/${userId}`, {
          cache: {
            strategy: 'network-first',
            ttl: 1 * 60 * 1000,
          },
        });
        return response.data;
      },
      staleTime: 30 * 1000,
    });
  },
};

export default {
  useOptimizedQuery,
  usePosts,
  usePremiumPosts,
  useWalletBalance,
  useUserProfile,
  useOptimizedMutation,
  useLikePost,
  useUploadFile,
  prefetchUtils,
};
