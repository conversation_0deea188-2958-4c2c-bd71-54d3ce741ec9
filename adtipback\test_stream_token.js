const streamService = require('./services/CloudflareStreamService');

async function testNewToken() {
  try {
    console.log('🧪 Testing new Cloudflare Stream API token...');
    console.log('Account ID:', streamService.accountId);
    console.log('API Token:', '***' + streamService.apiToken.slice(-4));
    
    const result = await streamService.testConnection();
    
    if (result.success) {
      console.log('✅ Stream API connection successful!');
      console.log('Response:', result.data);
    } else {
      console.log('❌ Stream API connection failed:', result.error);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testNewToken();
