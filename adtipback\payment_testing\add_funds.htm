<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Razorpay Payment</title>
</head>
<body>
    <button id="pay-btn">Pay ₹200</button>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:7082/api';
        const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.CP2hoyHw7dOjB8A6uIifbdfNsztf0Pt1BSw8pEdM92Q'; // Replace with your auth token
        const USER_ID = 50816; // Ensure this matches createdby in /addfunds
        const RAZORPAY_KEY = 'rzp_test_Wpj489Mi7DAY7c'; // Replace with your Razorpay Key ID

        document.getElementById('pay-btn').onclick = async function (e) {
            try {
                // Step 1: Create Razorpay order
                const orderResponse = await fetch(`${API_BASE_URL}/razorpay-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': AUTH_TOKEN
                    },
                    body: JSON.stringify({
                        amount: 200, // Amount in INR
                        currency: 'INR',
                        user_id: USER_ID
                    })
                });
                const orderData = await orderResponse.json();

                if (!orderData.status) {
                    alert('Order creation failed: ' + orderData.message);
                    console.error('Order creation failed:', orderData);
                    return;
                }

                // Step 2: Initialize Razorpay checkout
                const options = {
                    key: RAZORPAY_KEY,
                    amount: orderData.data.amount, // Amount in paise
                    currency: 'INR',
                    name: 'Your Business Name',
                    description: 'Test Payment',
                    order_id: orderData.data.id,
                    handler: async function (response) {
                        console.log('Payment Successful:', response);

                        // Step 3: Verify payment with backend
                        const verificationResponse = await fetch(`${API_BASE_URL}/razorpay-verification`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': AUTH_TOKEN
                            },
                            body: JSON.stringify({
                                order_id: response.razorpay_order_id,
                                razorpay_payment_id: response.razorpay_payment_id,
                                razorpay_signature: response.razorpay_signature,
                                amount: orderData.data.amount / 100, // Convert paise to INR
                                currency: 'INR',
                                user_id: USER_ID,
                                payment_status: 'success'
                            })
                        });
                        const verificationData = await verificationResponse.json();

                        if (!verificationData.status) {
                            alert('Payment verification failed: ' + verificationData.message);
                            console.error('Verification failed:', verificationData);
                            return;
                        }

                        // Step 4: Add funds to wallet (only after successful verification)
                        const addFundsResponse = await fetch(`${API_BASE_URL}/addfunds`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': AUTH_TOKEN
                            },
                            body: JSON.stringify({
                                createdby: USER_ID,
                                amount: orderData.data.amount / 100, // amount in INR
                                transaction_type: "AddFunds-Deposite",
                                transactionStatus: "1"
                            })
                        });
                        const addFundsData = await addFundsResponse.json();

                        if (addFundsData.status === 200) {
                            alert('Funds added successfully: ' + addFundsData.message);
                            console.log('Add funds response:', addFundsData);
                        } else {
                            alert('Failed to add funds: ' + addFundsData.message);
                            console.error('Add funds failed:', addFundsData);
                        }
                    },
                    prefill: {
                        name: 'Test User',
                        email: '<EMAIL>',
                        contact: '9999999999'
                    },
                    theme: {
                        color: '#F37254'
                    }
                };

                const rzp = new Razorpay(options);
                rzp.open();
            } catch (err) {
                console.error('Payment process error:', err);
                alert('An error occurred: ' + err.message);
            }
        };
    </script>
</body>
</html>
