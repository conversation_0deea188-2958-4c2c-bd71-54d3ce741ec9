<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Socket.IO SQL Chat</title>
    <style>
      body {
        font: 13px Helvetica, Arial;
      }
      form {
        background: #000;
        padding: 3px;
        position: fixed;
        bottom: 0;
        width: 100%;
      }
      form input {
        border: 0;
        padding: 10px;
        width: 90%;
        margin-right: 0.5%;
      }
      form button {
        width: 9%;
        background: #0d6efd;
        border: none;
        padding: 10px;
      }
      ul {
        list-style-type: none;
        margin: 0;
        padding: 0;
      }
      ul li {
        padding: 8px;
        margin-bottom: 2px;
        background: #f4f4f4;
        border-radius: 3px;
      }
    </style>
  </head>
  <body>
    <ul id="messages"></ul>
    <form id="form" action="">
      <input id="input" autocomplete="off" /><button>Send</button>
    </form>

    <script src="/socket.io/socket.io.js"></script>
    <script>
      var socket = io();

      // Prompt for user ID
      var userId = prompt("Enter your user ID:");

      // Load previous messages
      socket.on("previous messages", function (messages) {
        messages.forEach(function (message) {
          var item = document.createElement("li");
          item.textContent = message.userId + ": " + message.message;
          document.getElementById("messages").appendChild(item);
        });
        window.scrollTo(0, document.body.scrollHeight);
      });

      // Submit form and send message to server
      var form = document.getElementById("form");
      var input = document.getElementById("input");

      form.addEventListener("submit", function (e) {
        e.preventDefault();
        if (input.value) {
          socket.emit("chat message", {
            userId: userId,
            message: input.value,
          });
          input.value = "";
        }
      });

      // Listen for messages from server
      socket.on("chat message", function (msg) {
        var item = document.createElement("li");
        item.textContent = msg.userId + ": " + msg.message;
        document.getElementById("messages").appendChild(item);
        window.scrollTo(0, document.body.scrollHeight);
      });
    </script>
  </body>
</html>
