const { Server } = require("socket.io");
const UsersService = require("./UsersService");

module.exports = {
  chatWebSocket: (server) => {
    const io = new Server(server);

    io.on("connection", (socket) => {
      console.log("A user connected");

      let loginUser;
      let chattingUser;

      // Initialize users when a client connects
      socket.on("initialize", (userDate) => {
        loginUser = userDate.login;
        chattingUser = userDate.chatting;
        console.log(`Login User: ${loginUser}, Chatting User: ${chattingUser}`);

        // Fetch and send previous messages
        UsersService.getMessage({
          loginuserid: loginUser,
          chattinguserid: chattingUser,
        })
          .then((messages) => {
            console.log("messages", messages);
            socket.emit("previous messages", messages);
          })
          .catch((error) => {
            console.error("Error fetching messages:", error);
          });
      });

      // Handle incoming messages
      socket.on("user-message", (messageData) => {
        console.log("New message:", messageData.message);

        //Optionally save message to the database
        UsersService.savemessages({
          message: messageData.message,
          userId: messageData.userId,
          receiverId: messageData.receiverId,
          parentId: 0,
          chat_type: "company",
          chat_type_id_value: "23",
        })
          .then((result) => {
            io.emit("message", {
              message: messageData.message,
              sender: messageData.userId,
              receiver: messageData.receiverId,
              date: new Date(),
            });
          })
          .catch((error) => {
            console.error("Error saving message:", error);
          });
      });

      socket.on("disconnect", (reason, details) => {
        console.log("Client disconnected", reason, details);
      });
    });
  },
};
