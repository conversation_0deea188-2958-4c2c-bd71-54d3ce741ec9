{"buildFiles": ["F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\RelWithDebInfo\\662h3v4p\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_rnviewshot::@0ba03d237e60b9258a87": {"artifactName": "react_codegen_rnviewshot", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNFastImageSpec::@5f53d33017f3c907f455": {"artifactName": "react_codegen_RNFastImageSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNPermissionsSpec::@7ad697819b753921c957": {"artifactName": "react_codegen_RNPermissionsSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNCImageCropPickerSpec::@702b02f8d2524609d414": {"artifactName": "react_codegen_RNCImageCropPickerSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"artifactName": "react_codegen_RNCWebViewSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2": {"artifactName": "react_codegen_RNDateTimePickerCGen", "abi": "arm64-v8a", "runtimeFiles": []}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c": {"artifactName": "react_codegen_RNGoogleMobileAdsSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnskia::@376d8504f62839611b97": {"artifactName": "react_codegen_rnskia", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "arm64-v8a", "output": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_Compressor::@408161e29a6d5b274579": {"artifactName": "react_codegen_Compressor", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnclipboard::@6385240493dfcaf22ab7": {"artifactName": "react_codegen_rnclipboard", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_pagerview::@7032a8921530ec438d60": {"artifactName": "react_codegen_pagerview", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnblurview::@9a34ffec6e39b5c9049e": {"artifactName": "react_codegen_rnblurview", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4": {"artifactName": "react_codegen_RNImagePickerSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\662h3v4p\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0dbfeb2f307611649a8567893eebd290\\transformed\\jetified-react-android-0.79.2-release\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb": {"artifactName": "react_codegen_lottiereactnative", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_RNDatePickerSpecs::@c00f981c6e74346c63d4": {"artifactName": "react_codegen_RNDatePickerSpecs", "abi": "arm64-v8a", "runtimeFiles": []}}}