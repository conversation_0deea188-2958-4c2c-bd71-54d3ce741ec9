const { queryRunner } = require('../dbConfig/queryRunner');
const moment = require('moment');

/**
 * Handles a promoted post view and rewards the user accordingly.
 * - Non-premium: ₹0.03 per view
 * - Premium: full pay_per_view
 * - Updates remaining_budget, views_count, and user wallet
 * - If budget exhausted, no payout
 * - No new wallet transaction rows, just update balance
 */
exports.handlePromotedPostView = async (req, res) => {
  try {
    console.log('Received /view-promoted-post request:', req.body);
    const { user_id, post_id } = req.body;
    if (!user_id || !post_id) {
      console.log('Missing user_id or post_id');
      return res.status(400).json({ status: false, message: 'user_id and post_id are required', earned_amount: 0, remaining_budget: 0, user_wallet_balance: 0 });
    }

    // 0. Check if user already viewed this promoted post
    const alreadyViewed = await queryRunner('SELECT 1 FROM promoted_post_views WHERE user_id = ? AND post_id = ?', [user_id, post_id]);
    if (alreadyViewed.length) {
      // Already rewarded, do not reward again
      // Fetch latest wallet balance for response
      const walletRows = await queryRunner('SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1', [user_id]);
      const walletBalance = walletRows.length ? parseFloat(walletRows[0].totalBalance) : 0;
      const resp = {
        status: false,
        message: 'Already rewarded for this post',
        earned_amount: 0,
        remaining_budget: 0,
        user_wallet_balance: walletBalance
      };
      console.log('Response:', resp);
      return res.status(200).json(resp);
    }

    // 1. Fetch post
    const postRows = await queryRunner('SELECT * FROM posts WHERE id = ?', [post_id]);
    if (!postRows.length) {
      console.log('Post not found');
      return res.status(404).json({ status: false, message: 'Post not found', earned_amount: 0, remaining_budget: 0, user_wallet_balance: 0 });
    }
    const post = postRows[0];
    if (!post.is_promoted) {
      console.log('Not a promoted post');
      return res.status(400).json({ status: false, message: 'Not a promoted post', earned_amount: 0, remaining_budget: 0, user_wallet_balance: 0 });
    }

    // 2. Fetch user premium status
    const userRows = await queryRunner('SELECT id, is_premium FROM users WHERE id = ?', [user_id]);
    if (!userRows.length) {
      console.log('User not found');
      return res.status(404).json({ status: false, message: 'User not found', earned_amount: 0, remaining_budget: 0, user_wallet_balance: 0 });
    }
    const user = userRows[0];

    // 3. Calculate payout
    let payout = 0.03;
    if (user.is_premium) {
      payout = parseFloat(post.pay_per_view ? post.pay_per_view.toString().replace(/,/g, '') : '0.03');
    }

    // 4. Check promotion days
    const now = new Date();
    const promoStart = new Date(post.start_date || post.created_at);
    const promoEnd = new Date(post.end_date || promoStart.getTime() + (post.duration_days || 0) * 24 * 60 * 60 * 1000);
    if (now < promoStart || now > promoEnd) {
      return res.status(400).json({ status: false, message: 'Promotion not active', earned_amount: 0, remaining_budget: 0, user_wallet_balance: 0 });
    }

    // 5. Determine available budget (post or user wallet)
    let remaining_budget = post.remaining_budget !== null && post.remaining_budget !== undefined
      ? parseFloat(post.remaining_budget.toString().replace(/,/g, ''))
      : null;
    let useWallet = false;
    if (remaining_budget === null) {
      // Fallback to user wallet
      const walletRows = await queryRunner('SELECT id, totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1', [post.user_id]);
      remaining_budget = walletRows.length ? parseFloat(walletRows[0].totalBalance) : 0;
      useWallet = true;
    }

    if (remaining_budget < payout) {
      const walletRows = await queryRunner('SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1', [user_id]);
      const walletBalance = walletRows.length ? parseFloat(walletRows[0].totalBalance) : 0;
      const resp = {
        status: false,
        message: 'Ad budget exhausted',
        earned_amount: 0,
        remaining_budget,
        user_wallet_balance: walletBalance
      };
      console.log('Response:', resp);
      return res.status(200).json(resp);
    }

    // 6. Deduct from post remaining_budget or user wallet
    if (useWallet) {
      // Deduct from post owner's wallet
      const walletRows = await queryRunner('SELECT id, totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1', [post.user_id]);
      if (!walletRows.length || walletRows[0].totalBalance < payout) {
        return res.status(400).json({ status: false, message: 'Ad owner wallet exhausted', earned_amount: 0, remaining_budget, user_wallet_balance: walletRows.length ? parseFloat(walletRows[0].totalBalance) : 0 });
      }
      const newOwnerBalance = parseFloat(walletRows[0].totalBalance) - payout;
      await queryRunner('UPDATE wallet SET totalBalance = ? WHERE id = ?', [newOwnerBalance, walletRows[0].id]);
    } else {
      // Deduct from post remaining_budget
      const newRemainingBudget = remaining_budget - payout;
      const newViewsCount = (post.views_count || 0) + 1;
      await queryRunner('UPDATE posts SET remaining_budget = ?, views_count = ? WHERE id = ?', [newRemainingBudget, newViewsCount, post_id]);
    }

    // 7. Credit to viewer's wallet
    const walletRows = await queryRunner('SELECT id, totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1', [user_id]);
    let prevBalance = walletRows.length ? parseFloat(walletRows[0].totalBalance) : 0;
    const newWalletBalance = prevBalance + payout;
    if (walletRows.length) {
      await queryRunner('UPDATE wallet SET totalBalance = ? WHERE id = ?', [newWalletBalance, walletRows[0].id]);
    } else {
      await queryRunner('INSERT INTO wallet (createdby, totalBalance) VALUES (?, ?)', [user_id, newWalletBalance]);
    }

    // 8. Insert promoted_post_views
    await queryRunner('INSERT INTO promoted_post_views (user_id, post_id) VALUES (?, ?)', [user_id, post_id]);

    // 9. Respond
    const resp = {
      status: true,
      message: 'View rewarded',
      earned_amount: payout,
      remaining_budget: useWallet ? null : (remaining_budget - payout),
      user_wallet_balance: newWalletBalance
    };
    console.log('Response:', resp);
    return res.status(200).json(resp);
  } catch (err) {
    console.error('Error in handlePromotedPostView:', err);
    return res.status(500).json({ status: false, message: 'Internal server error', error: err.message, earned_amount: 0, remaining_budget: 0, user_wallet_balance: 0 });
  }
}; 