const router = require("express").Router();
const dbQuery = require("../dbConfig/queryRunner");

router.post("/list", async (req, res, next) => {
  try {
    const reqObj = req.body;

    const keyword = reqObj.searchTerm || "";
    const languages = reqObj.languages;
    const interests = reqObj.interests;
    const limit = reqObj.limit;
    const offset = reqObj.offset;

    // Construct placeholders dynamically for languages
    const langPlaceholders = languages.join(",");
    const interestsConditions = interests
      .map((item) => `interests LIKE '%${item}%'`)
      .join(" OR ");

    const query = `
      SELECT
        id,
        name,
        profile_image,
        mobile_number,
        gender,
        language,
        interests
      FROM users
      WHERE
        isOtpVerified=1
        AND (name LIKE '%${keyword}%')
      ORDER BY id ASC
      LIMIT ${limit} OFFSET ${offset};
    `;

    const result = await dbQuery.queryRunner(query);

    res.send({
      status: true,
      data: {
        users: result.map((item) => ({
          ...item,
          interests: item.interests
            ? JSON.parse(item.interests)[0]?.split(",") || null
            : null,
        })),
      },
    });
  } catch (error) {
    console.error(error);
    next(error);
  }
});

module.exports = router;
