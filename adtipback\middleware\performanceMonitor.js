const logger = require('../utils/logger');

// Performance monitoring configuration
const config = {
  slowRequestThreshold: 2000, // 2 seconds
  logAllRequests: process.env.NODE_ENV === 'development',
  trackMemoryUsage: true,
  trackDatabaseQueries: true
};

// Performance statistics
const stats = {
  totalRequests: 0,
  slowRequests: 0,
  failedRequests: 0,
  averageResponseTime: 0,
  startTime: Date.now(),
  endpoints: new Map(),
  memoryUsage: []
};

// Memory usage tracking
let memoryTrackingInterval;

const startMemoryTracking = () => {
  if (config.trackMemoryUsage) {
    memoryTrackingInterval = setInterval(() => {
      const memUsage = process.memoryUsage();
      stats.memoryUsage.push({
        timestamp: Date.now(),
        ...memUsage
      });
      
      // Keep only last 100 memory readings
      if (stats.memoryUsage.length > 100) {
        stats.memoryUsage.shift();
      }
    }, 30000); // Every 30 seconds
  }
};

const stopMemoryTracking = () => {
  if (memoryTrackingInterval) {
    clearInterval(memoryTrackingInterval);
    memoryTrackingInterval = null;
  }
};

// Performance monitoring middleware
const performanceMonitor = (req, res, next) => {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(7);
  
  // Add request metadata
  req.requestId = requestId;
  req.startTime = startTime;
  
  // Track endpoint statistics
  const endpoint = `${req.method} ${req.path}`;
  if (!stats.endpoints.has(endpoint)) {
    stats.endpoints.set(endpoint, {
      count: 0,
      totalTime: 0,
      averageTime: 0,
      slowCount: 0,
      errorCount: 0
    });
  }
  
  const endpointStats = stats.endpoints.get(endpoint);
  endpointStats.count++;
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const responseTime = Date.now() - startTime;
    
    // Update global statistics
    stats.totalRequests++;
    stats.averageResponseTime = (stats.averageResponseTime * (stats.totalRequests - 1) + responseTime) / stats.totalRequests;
    
    // Update endpoint statistics
    endpointStats.totalTime += responseTime;
    endpointStats.averageTime = endpointStats.totalTime / endpointStats.count;
    
    // Check for slow requests
    if (responseTime > config.slowRequestThreshold) {
      stats.slowRequests++;
      endpointStats.slowCount++;
      
      logger.warn(`SLOW REQUEST [${requestId}]: ${endpoint} took ${responseTime}ms`, {
        requestId,
        endpoint,
        responseTime,
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
    
    // Check for failed requests
    if (res.statusCode >= 400) {
      stats.failedRequests++;
      endpointStats.errorCount++;
      
      logger.error(`FAILED REQUEST [${requestId}]: ${endpoint} returned ${res.statusCode}`, {
        requestId,
        endpoint,
        statusCode: res.statusCode,
        responseTime,
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      });
    }
    
    // Log all requests in development
    if (config.logAllRequests) {
      logger.info(`REQUEST [${requestId}]: ${endpoint} - ${res.statusCode} (${responseTime}ms)`);
    }
    
    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

// Database query monitoring
const databaseQueryMonitor = (query, params, startTime) => {
  const queryTime = Date.now() - startTime;
  
  if (queryTime > 1000) { // Log queries taking more than 1 second
    logger.warn(`SLOW DATABASE QUERY: ${queryTime}ms`, {
      query: query.substring(0, 200) + (query.length > 200 ? '...' : ''),
      params,
      queryTime
    });
  }
};

// Get performance statistics
const getPerformanceStats = () => {
  const uptime = Date.now() - stats.startTime;
  const requestsPerSecond = stats.totalRequests / (uptime / 1000);
  
  // Get top slow endpoints
  const slowEndpoints = Array.from(stats.endpoints.entries())
    .filter(([_, data]) => data.slowCount > 0)
    .sort((a, b) => b[1].slowCount - a[1].slowCount)
    .slice(0, 10)
    .map(([endpoint, data]) => ({
      endpoint,
      count: data.count,
      averageTime: data.averageTime,
      slowCount: data.slowCount,
      errorCount: data.errorCount
    }));
  
  // Get top error endpoints
  const errorEndpoints = Array.from(stats.endpoints.entries())
    .filter(([_, data]) => data.errorCount > 0)
    .sort((a, b) => b[1].errorCount - a[1].errorCount)
    .slice(0, 10)
    .map(([endpoint, data]) => ({
      endpoint,
      count: data.count,
      errorCount: data.errorCount,
      errorRate: ((data.errorCount / data.count) * 100).toFixed(2) + '%'
    }));
  
  // Get memory usage statistics
  const memoryStats = stats.memoryUsage.length > 0 ? {
    current: stats.memoryUsage[stats.memoryUsage.length - 1],
    average: {
      rss: stats.memoryUsage.reduce((sum, mem) => sum + mem.rss, 0) / stats.memoryUsage.length,
      heapUsed: stats.memoryUsage.reduce((sum, mem) => sum + mem.heapUsed, 0) / stats.memoryUsage.length,
      heapTotal: stats.memoryUsage.reduce((sum, mem) => sum + mem.heapTotal, 0) / stats.memoryUsage.length,
      external: stats.memoryUsage.reduce((sum, mem) => sum + mem.external, 0) / stats.memoryUsage.length
    }
  } : null;
  
  return {
    uptime: Math.floor(uptime / 1000), // in seconds
    totalRequests,
    requestsPerSecond: requestsPerSecond.toFixed(2),
    averageResponseTime: Math.round(stats.averageResponseTime),
    slowRequests: stats.slowRequests,
    failedRequests: stats.failedRequests,
    successRate: stats.totalRequests > 0 ? 
      ((stats.totalRequests - stats.failedRequests) / stats.totalRequests * 100).toFixed(2) + '%' : '0%',
    slowEndpoints,
    errorEndpoints,
    memoryStats,
    endpointsCount: stats.endpoints.size
  };
};

// Reset performance statistics
const resetPerformanceStats = () => {
  Object.assign(stats, {
    totalRequests: 0,
    slowRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    startTime: Date.now(),
    endpoints: new Map(),
    memoryUsage: []
  });
  
  logger.info('Performance statistics reset');
};

// Start performance monitoring
const startPerformanceMonitoring = () => {
  startMemoryTracking();
  logger.info('Performance monitoring started');
};

// Stop performance monitoring
const stopPerformanceMonitoring = () => {
  stopMemoryTracking();
  logger.info('Performance monitoring stopped');
};

module.exports = {
  performanceMonitor,
  databaseQueryMonitor,
  getPerformanceStats,
  resetPerformanceStats,
  startPerformanceMonitoring,
  stopPerformanceMonitoring
}; 