const utils = require("../utils/utils");
const ReelsService = require("../services/ReelsService");

module.exports = {
  generatePresignedUrl: (req, res) => {
    ReelsService.generatePresignedUrl(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  },


  saveChannelWithdrawRequest: (req, res, next) => {
    return ReelsService.saveChannelWithdrawRequest(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getTransactions: (req, res, next) => {
    return ReelsService.getTransactions(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  uploadShot: (req, res, next) => {
    return ReelsService.uploadShot(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getShots: (req, res, next) => {
    if (!req.params.userid) {
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    }

    return ReelsService.getShots(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getPublicShots: (req, res, next) => {
  return ReelsService.getPublicShots()
    .then((result) => {
      res.status(result.status || 200).send(result);
    })
    .catch((err) => {
      res.status(err.status || 500).send({
        status: err.status || 500,
        message: err.message || "Internal server error.",
        data: [],
      });
    });
},

  getVideoCatagory: (req, res, next) => {
    return ReelsService.getVideoCatagory()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveVideoLike: (req, res, next) => {
    return ReelsService.saveVideoLike(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getUserVideoViewLikeDetails: (req, res, next) => {
    return ReelsService.getUserVideoViewLikeDetails(
      req.params.userId,
      req.params.page
    )
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveVideoDetails: (req, res, next) => {
    return ReelsService.saveVideoDetails(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveChannelFollowers: (req, res, next) => {
    return ReelsService.saveChannelFollowers(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveChannelDetails: (req, res, next) => {
    return ReelsService.saveChannelDetails(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveMyChannels: (req, res, next) => {
    return ReelsService.saveMyChannel(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getWatchingVideo: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getWatchingVideo(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getAllVideos: (req, res, next) => {
    if (!req.params.userid && !req.params.categoryid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });

    return ReelsService.getAllVideos(
      req.params.userid,
      req.params.categoryid,
      req.params.offset
    )
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getPublicVideos: (req, res, next) => {
  if (!req.params.categoryid || !req.params.offset) {
    return res
      .status(400)
      .send({ status: 400, message: "Invalid request.", data: [] });
  }

  return ReelsService.getPublicVideos(req.params.categoryid, req.params.offset)
    .then((result) => {
      res.status(result.status || 200).send(result);
    })
    .catch((err) => {
      res.status(err.status || 500).send({
        status: err.status || 500,
        message: err.message ? err.message : "Internal server error.",
        data: [],
      });
    });
},
  // getCommentOfVideo
  getCommentOfVideo: (req, res, next) => {
    if (!req.params.videoId && !req.params.page && !req.params.limit)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getCommentOfVideo(
      req.params.videoId,
      req.params.page,
      req.params.limit
    )
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          total: 0,
          data: [],
        });
      });
  },

  getcommentsofvideos: (req, res, next) => {
    if (!req.params.userId && !req.params.videoId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });

    return ReelsService.getcommentsofvideos(
      req.params.userId,
      req.params.videoId
    )
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  deletecommentbyid: (req, res, next) => {
    if (!req.params.commentId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.deletecommentbyid(req.params.commentId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getVideoDetails: (req, res, next) => {
    if (!req.params.userid && !req.params.videoid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getVideoDetails(req.params.userid, req.params.videoid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  // Get single video by ID (for deep linking)
  getSingleVideo: (req, res, next) => {
    const { videoId } = req.params;
    const { userId } = req.query;

    if (!videoId) {
      return res.status(400).send({
        status: 400,
        message: "Video ID is required",
        data: []
      });
    }

    console.log('[getSingleVideo] Request received for videoId:', videoId, 'userId:', userId);

    // Use default userId of 0 if not provided (for guest access)
    const userIdToUse = userId || 0;

    return ReelsService.getVideoDetails(userIdToUse, videoId)
      .then((result) => {
        console.log('[getSingleVideo] Service response:', result);
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.error('[getSingleVideo] Service error:', err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  // editVideo
  editVideo: (req, res, next) => {
    // Add userId from JWT token for security
    const videoData = {
      ...req.body,
      userId: req.user?.user_id
    };

    return ReelsService.editVideo(videoData)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  // deleteVideo
  deleteVideo: (req, res, next) => {
    if (!req.params.videoId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });

    // Pass userId from JWT token for security
    const userId = req.user?.user_id;

    return ReelsService.deleteVideo(req.params.videoId, userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getVideoByChannel: (req, res, next) => {
    if (!req.params.userid && !req.params.channelid && !req.params.videoType)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getVideoByChannel(
      req.params.userid,
      req.params.channelid,
      req.params.videoType
    )
      .then((result) => {
        //res.status(result.status || 200).send(result);
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            if (data && data.createdby === req.params.userid) {
              data.is_like = data.is_like ? data.is_like : 0;
              data.is_unlike = data.is_like === 0 ? 1 : 0;
              data.is_view = data.is_view ? 1 : 0;
            } else {
              data.is_like = 0;
              data.is_unlike = 0;
              data.is_view = 0;
            }
            data.adUrl = `${req.headers.host}/api/video/${data.adUrl}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getWatchingLaterVideo: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getWatchingLaterVideo(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getMyChannelList: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getMyChannelByUserID(req.params.userid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.channelUrl = `${req.headers.host}/api/channel/${data.channelId}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  channelShortsVideos: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.channelShortsVideos(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  // getShortById
  getShortById: (req, res, next) => {
    if (!req.params.userId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getShortById(req.params.userId, req.params.reelId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getPopularVideo: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getPopularVideo(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
        // if (result && result.status === 200) {
        //     result.data.forEach(data => {
        //         data.channelUrl = `${req.headers.host}/api/channel/${data.video_channel}`
        //         data.adUrl = `${req.headers.host}/api/video/${data.adUrl}`
        //     });
        //     res.status(result.status || 200).send(result);
        // } else {
        //     res.status(result.status || 400).send(result);
        // }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getPopularShort: (req, res, next) => {
    if (!req.params.userid && !req.params.videoType)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getPopularShort(req.params.userid, req.params.videoType)
      .then((result) => {
        res.status(result.status || 200).send(result);
        // if (result && result.status === 200) {
        //     result.data.forEach(data => {
        //         data.channelUrl = `${req.headers.host}/api/channel/${data.video_channel}`
        //         data.adUrl = `${req.headers.host}/api/video/${data.adUrl}`
        //     });
        //     res.status(result.status || 200).send(result);
        // } else {
        //     res.status(result.status || 400).send(result);
        // }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getRecentlyUploaded: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getRecentlyUploaded(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getAllRecentlyUploaded: (req, res, next) => {
    if (!req.params.page)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getAllRecentlyUploaded(req.params.page)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  searchfuntube: (req, res, next) => {
    if (!req.body.searchname)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.searchfuntube(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getRecentlyUploadedShort: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getRecentlyUploadedShort(req.params.userid)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.channelUrl = `${req.headers.host}/api/channel/${data.video_channel}`;
            data.adUrl = `${req.headers.host}/api/video/${data.adUrl}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getAllRecentlyUploadedShort: (req, res, next) => {
    if (!req.params.page)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getAllRecentlyUploadedShort(req.params.page)
      .then((result) => {
        if (result && result.status === 200) {
          result.data.forEach((data) => {
            data.channelUrl = `${req.headers.host}/api/channel/${data.video_channel}`;
            data.adUrl = `${req.headers.host}/api/video/${data.adUrl}`;
          });
          res.status(result.status || 200).send(result);
        } else {
          res.status(result.status || 400).send(result);
        }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getListOfFollowedChannelByUser: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getListOfFollowedChannelByUser(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
        // if (result && result.status === 200) {
        //     result.data.forEach(data => {
        //         data.channelUrl = `${req.headers.host}/api/channel/${data.id}`;
        //         data.adUrl = `${req.headers.host}/api/video/${data.adUrl}`
        //     });
        //     res.status(result.status || 200).send(result);
        // } else {
        //     res.status(result.status || 400).send(result);
        // }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getTrendingVideosOfAllChannel: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getTrendingVideosOfAllChannel(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
        // if (result && result.status === 200) {
        //     result.data.forEach(data => {
        //         data.channelUrl = `${req.headers.host}/api/channel/${data.video_channel}`
        //         data.adUrl = `${req.headers.host}/api/video/${data.adUrl}`
        //     });
        //     res.status(result.status || 200).send(result);
        // } else {
        //     res.status(result.status || 400).send(result);
        // }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getWeekVideoAllChannelFollowedByUser: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getWeekVideoAllChannelFollowedByUser(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
        // if (result && result.status === 200) {
        //     result.data.forEach(data => {
        //         data.channelUrl = `${req.headers.host}/api/channel/${data.video_channel}`
        //         data.adUrl = `${req.headers.host}/api/video/${data.adUrl}`
        //     });
        //     res.status(result.status || 200).send(result);
        // } else {
        //     res.status(result.status || 400).send(result);
        // }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getMonthVideoAllChannelFollowedByUser: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getMonthVideoAllChannelFollowedByUser(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getChannelByID: (req, res, next) => {
    //   if (!req.data.id) return res.status(400).send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getChannelByID(req.params.id, req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
        // if (result && result.status === 200) {
        //     result.data.forEach(data => {
        //         // if(data && !data.hasOwnProperty("is_like")){
        //         //     data.is_like = 0
        //         //     // data.is_unlike =  0
        //         //     data.is_view =0
        //         //     data.channel_follow = 0
        //         // }
        //         data.channelUrl = `${req.headers.host}/api/channel/${data.channelId}`
        //     });
        //     res.status(result.status || 200).send(result);
        // } else {
        //     res.status(result.status || 400).send(result);
        // }
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  updateChannel: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.updateChannel(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  createPlayList: (req, res, next) => {
    if (!req.body.name && !req.body.channelId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.createPlayList(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getPlayListByChannelById: (req, res, next) => {
    if (!req.params.channelid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.getPlayListByChannelById(req.params.channelid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveVideoComment: (req, res, next) => {
    if (
      !req.body.comment &&
      !req.body.videoId &&
      !req.body.createdBy &&
      !req.body.parentCommetId
    )
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.saveVideoComment(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveVideoCommentLike: (req, res, next) => {
    if (!req.body.commentId && !req.body.userId)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    return ReelsService.saveVideoCommentLike(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  generateQRCode: (req, res, next) => {
    companyService
      .generateQRCode(generateQRCodedata)
      .then((result) => {
        return utils.generateQRCode(generateQRCodedata);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getAnalytics: async (req, res) => {
    const channelId = req.params.channelId;

    try {
      const analyticsData = await ReelsService.getChannelAnalytics(channelId);

      if (!analyticsData) {
        return res.status(404).json({
          status: false,
          message: "Channel not found or data unavailable",
        });
      }

      res.status(200).json({
        status: true,
        message: "success",
        data: analyticsData,
      });
    } catch (error) {
      console.error("Error in Analytics Controller:", error);
      res.status(500).json({
        status: false,
        message:
          "An error occurred while processing your request. Please try again.",
      });
    }
  },

  viewPaidVideo: async (req, res) => {
    try {
      const { reelId } = req.body;
      const viewer_id = req.user?.user_id;

      console.log(`viewPaidVideo - reelId: ${reelId}, viewer_id: ${viewer_id}`);

      if (!reelId || isNaN(reelId)) {
        return res.status(400).json({
          status: false,
          message: "Missing or invalid reelId",
          data: []
        });
      }

      if (!viewer_id || isNaN(viewer_id)) {
        return res.status(401).json({
          status: false,
          message: "Unauthorized: Missing user_id",
          data: []
        });
      }

      const result = await ReelsService.watchPaidVideo({ video_id: parseInt(reelId), viewer_id: parseInt(viewer_id) });

      if (result.statusCode === 403 || result.statusCode === 404) {
        return res.status(result.statusCode).json({
          status: false,
          message: result.message,
          data: []
        });
      }

      // Handle both success and insufficient balance cases with statusCode 200
      if (result.statusCode === 200) {
        return res.status(200).json({
          status: result.status !== undefined ? result.status : true,
          message: result.message,
          data: result.data.video_link ? { video_link: result.data.video_link } : {}
        });
      }

      // Handle other error cases (e.g., video not paid)
      return res.status(result.statusCode).json({
        status: false,
        message: result.message,
        data: []
      });
    } catch (error) {
      console.error("Error in viewPaidVideo:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
        data: []
      });
    }
  },

  watchPaidVideo: async (req, res) => {
    try {
      const { video_id } = req.body;
      const viewer_id = req.user.user_id; // Extract user_id from token payload

      if (!video_id) {
        return res.status(400).json({
          status: false,
          message: "Missing video_id",
          data: []
        });
      }

      const result = await ReelsService.watchPaidVideo({ video_id, viewer_id });

      if (result.statusCode === 400 || result.statusCode === 403 || result.statusCode === 404) {
        return res.status(result.statusCode).json({
          status: false,
          message: result.message,
          data: []
        });
      }

      return res.status(200).json({
        status: true,
        message: result.message,
        data: { signed_url: result.signed_url }
      });
    } catch (error) {
      console.error("Error in watchPaidVideo:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
        data: []
      });
    }
  },

  watchNormalVideo: async (req, res) => {
    try {
      const { reelId } = req.body;
      const viewer_id = req.user?.user_id;

      console.log(`watchNormalVideo - reelId: ${reelId}, viewer_id: ${viewer_id}`);

      if (!reelId || isNaN(reelId)) {
        return res.status(400).json({
          status: false,
          message: "Missing or invalid reelId",
          data: []
        });
      }

      if (!viewer_id || isNaN(viewer_id)) {
        return res.status(401).json({
          status: false,
          message: "Unauthorized: Missing user_id",
          data: []
        });
      }

      const result = await ReelsService.watchNormalVideo({ video_id: parseInt(reelId), viewer_id: parseInt(viewer_id) });

      if (result.statusCode === 400 || result.statusCode === 403 || result.statusCode === 404) {
        return res.status(result.statusCode).json({
          status: false,
          message: result.message,
          data: []
        });
      }

      return res.status(200).json({
        status: true,
        message: result.message,
        data: { video_link: result.data.video_link }
      });
    } catch (error) {
      console.error("Error in watchNormalVideo:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
        data: []
      });
    }
  },

  viewSubscriptionPaidVideo: async (req, res) => {
    try {
      const { reelId } = req.body;
      const viewer_id = req.user?.user_id;

      if (!reelId || isNaN(reelId)) {
        return res.status(400).json({ status: false, message: "Missing or invalid reelId", data: [] });
      }
      if (!viewer_id || isNaN(viewer_id)) {
        return res.status(401).json({ status: false, message: "Unauthorized: Missing user_id", data: [] });
      }

      // Call the new service method
      const result = await ReelsService.watchSubscriptionPaidVideo({ video_id: parseInt(reelId), viewer_id: parseInt(viewer_id) });

      if (result.statusCode === 403 || result.statusCode === 404) {
        return res.status(result.statusCode).json({ status: false, message: result.message, data: [] });
      }
      if (result.statusCode === 200) {
        return res.status(200).json({
          status: result.status !== undefined ? result.status : true,
          message: result.message,
          data: result.data.video_link ? { video_link: result.data.video_link } : {}
        });
      }
      return res.status(result.statusCode).json({ status: false, message: result.message, data: [] });
    } catch (error) {
      console.error("Error in viewSubscriptionPaidVideo:", error);
      return res.status(500).json({ status: false, message: "Internal Server Error", data: [] });
    }
  },

  viewPaidVideoNoPremium: async (req, res) => {
    try {
      const { reelId } = req.body;
      const viewer_id = req.user?.user_id;

      if (!reelId || isNaN(reelId)) {
        return res.status(400).json({ status: false, message: "Missing or invalid reelId", data: [] });
      }
      if (!viewer_id || isNaN(viewer_id)) {
        return res.status(401).json({ status: false, message: "Unauthorized: Missing user_id", data: [] });
      }

      // Call the new service method
      const result = await ReelsService.watchPaidVideoNoPremium({ video_id: parseInt(reelId), viewer_id: parseInt(viewer_id) });

      if (result.statusCode === 403 || result.statusCode === 404) {
        return res.status(result.statusCode).json({ status: false, message: result.message, data: [] });
      }
      if (result.statusCode === 200) {
        return res.status(200).json({
          status: result.status !== undefined ? result.status : true,
          message: result.message,
          data: result.data.video_link ? { video_link: result.data.video_link } : {}
        });
      }
      return res.status(result.statusCode).json({ status: false, message: result.message, data: [] });
    } catch (error) {
      console.error("Error in viewPaidVideoNoPremium:", error);
      return res.status(500).json({ status: false, message: "Internal Server Error", data: [] });
    }
  },


};
