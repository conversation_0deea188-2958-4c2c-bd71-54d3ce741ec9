# Voice & Video Call System - Subscription-Based

## Overview

The new Voice & Video call system replaces the old premium plan-based calling system with a modern subscription-based approach. This system integrates with the new subscription management system and provides better pricing tiers based on user subscription status.

## Key Features

- **Subscription-based pricing**: Different rates for users with active subscriptions vs. non-subscribers
- **Real-time balance checking**: Automatic calculation of maximum call duration based on wallet balance
- **Comprehensive call tracking**: Full call history with detailed transaction records
- **Wallet integration**: Automatic debit/credit operations for call charges and earnings

## API Endpoints

### 1. Voice Call Endpoints

#### 1.1. Initiate Voice Call
```
POST /api/voice-call
```

**Request Body:**
```json
{
  "callerId": 123,
  "receiverId": 456,
  "action": "start" | "end" | "missed",
  "callId": 789 // Required only for "end" action
}
```

**Response (Start Call):**
```json
{
  "status": true,
  "statusCode": 200,
  "is_call_ended": false,
  "startTime": "2024-01-15 10:30:00",
  "maxCallLimitTime": 60,
  "maxCallLimitDateTime": "2024-01-15 11:30:00",
  "callId": 789,
  "duration_seconds": 3600,
  "caller_charge_per_minute": 7,
  "caller_balance": 500.00,
  "caller_subscription_status": {
    "hasActiveSubscription": true,
    "planName": "Premium - 1 Month",
    "amount": 200,
    "isPremium": true
  },
  "message": "VideoSDK call started successfully"
}
```

**Response (End Call):**
```json
{
  "status": true,
  "statusCode": 200,
  "caller_user_id": 123,
  "caller_user_name": "John Doe",
  "receiver_user_id": 456,
  "receiver_user_name": "Jane Smith",
  "caller_debited_charge": "35.00",
  "receiver_credited_charge": "20.00",
  "total_duration_seconds": 300,
  "available_caller_balance": "465.00",
  "available_receiver_balance": "520.00",
  "caller_subscription_status": {...},
  "receiver_subscription_status": {...},
  "message": "VideoSDK call ended, transactions recorded successfully",
  "is_call_ended": true
}
```

#### 1.2. Get Voice Call Balance
```
GET /api/voice-call/balance/:userId
```

**Response:**
```json
{
  "status": true,
  "statusCode": 200,
  "data": {
    "wallet_balance": 500.00,
    "subscription_status": {
      "hasActiveSubscription": true,
      "planName": "Premium - 1 Month",
      "amount": 200,
      "isPremium": true
    },
    "charge_per_minute": 7,
    "max_call_minutes": 71,
    "has_active_subscription": true
  }
}
```

#### 1.3. Get Voice Call History
```
GET /api/voice-call/history/:userId?page=1&limit=10
```

### 2. Video Call Endpoints

#### 2.1. Initiate Video Call
```
POST /api/video-call
```

**Request Body:**
```json
{
  "callerId": 123,
  "receiverId": 456,
  "action": "start"
}
```

**Response (Start Call):**
```json
{
  "status": true,
  "statusCode": 200,
  "is_call_ended": false,
  "startTime": "2024-01-15 10:30:00",
  "maxCallLimitTime": 50,
  "maxCallLimitDateTime": "2024-01-15 11:20:00",
  "callId": 789,
  "duration_seconds": 3000,
  "caller_charge_per_minute": 10,
  "caller_balance": 500.00,
  "caller_subscription_status": {
    "hasActiveSubscription": true,
    "planName": "Premium - 1 Month",
    "amount": 200,
    "isPremium": true
  },
  "message": "Video call started successfully"
}
```

#### 2.2. Get Video Call Balance
```
GET /api/video-call/balance/:userId
```

**Response:**
```json
{
  "status": true,
  "statusCode": 200,
  "data": {
    "wallet_balance": 500.00,
    "subscription_status": {
      "hasActiveSubscription": true,
      "planName": "Premium - 1 Month",
      "amount": 200,
      "isPremium": true
    },
    "charge_per_minute": 10,
    "max_call_minutes": 50,
    "has_active_subscription": true
  }
}
```

#### 2.3. Get Video Call History
```
GET /api/video-call/history/:userId?page=1&limit=10
```

**Response:**
```json
{
  "status": true,
  "statusCode": 200,
  "data": {
    "calls": [
      {
        "call_id": 789,
        "caller_user_id": 123,
        "receiver_user_id": 456,
        "start_time": "2024-01-15 10:30:00",
        "end_time": "2024-01-15 10:35:00",
        "duration": 300,
        "status": "completed",
        "call_type": "video-call",
        "caller_name": "John Doe",
        "receiver_name": "Jane Smith"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_records": 50,
      "limit": 10
    }
  }
}
```

## Pricing Structure

### Voice Call Pricing

#### Caller Charges (per minute)
- **With Active Subscription**: ₹7/minute
- **Without Subscription**: ₹12/minute

#### Receiver Earnings (per minute)
- **With Active Subscription**: ₹4/minute
- **Without Subscription**: ₹2/minute

### Video Call Pricing

#### Caller Charges (per minute)
- **With Active Subscription**: ₹10/minute
- **Without Subscription**: ₹15/minute

#### Receiver Earnings (per minute)
- **With Active Subscription**: ₹6/minute
- **Without Subscription**: ₹3/minute

## Database Schema

### user_video_calls Table
```sql
CREATE TABLE user_video_calls (
  call_id INT AUTO_INCREMENT PRIMARY KEY,
  caller_user_id INT NOT NULL,
  receiver_user_id INT NOT NULL,
  start_time DATETIME NOT NULL,
  end_time DATETIME NULL,
  duration_seconds INT NULL,
  max_call_limit_time DATETIME NULL,
  duration INT NULL DEFAULT 0,
  call_type ENUM('video-call', 'missed-video-call', 'voice-call') DEFAULT 'video-call',
  created_at DATETIME NOT NULL,
  channel_name VARCHAR(255) NULL,
  FOREIGN KEY (caller_user_id) REFERENCES users(id),
  FOREIGN KEY (receiver_user_id) REFERENCES users(id)
);
```

### call_transactions Table
```sql
CREATE TABLE call_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  caller_id INT NOT NULL,
  receiver_id INT NOT NULL,
  call_id INT NOT NULL,
  duration_in_minutes DECIMAL(10,2),
  charge_amount DECIMAL(10,2),
  status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  call_type ENUM('video-call', 'missed-video-call', 'voice-call') DEFAULT 'video-call',
  FOREIGN KEY (caller_id) REFERENCES users(id),
  FOREIGN KEY (receiver_id) REFERENCES users(id),
  FOREIGN KEY (call_id) REFERENCES user_video_calls(call_id)
);
```

## Frontend Integration

### React Native Hook
```typescript
import { useVideoSDKCall } from '../hooks/useVideoSDKCall';

const { startCall, endCall, getCallBalance, getCallHistory, loading, error } = useVideoSDKCall();

// Start a call
const handleStartCall = async () => {
  try {
    const result = await startCall(callerId, receiverId);
    console.log('Call started:', result);
  } catch (error) {
    console.error('Failed to start call:', error);
  }
};

// End a call
const handleEndCall = async () => {
  try {
    const result = await endCall(callerId, receiverId, callId);
    console.log('Call ended:', result);
  } catch (error) {
    console.error('Failed to end call:', error);
  }
};
```

### API Service Methods
```typescript
// Initiate VideoSDK call
ApiService.initiateVideoSDKCall({
  callerId: 123,
  receiverId: 456,
  action: 'start' | 'end' | 'missed',
  callId?: number
});

// Get call balance
ApiService.getVideoSDKCallBalance(userId);

// Get call history
ApiService.getVideoSDKCallHistory(userId, page, limit);
```

## Migration from Old System

The new system replaces the old premium plan-based calling system. Key changes:

1. **Removed dependency on `premium_plan_id`** from users table
2. **Uses subscription status** from `user_subscriptions` table
3. **Uses existing call type** `video-call` (compatible with current database schema)
4. **Enhanced transaction tracking** with detailed subscription information

### Database Schema Update (Optional)

To add support for the `videosdk-call` enum value in the future, run the migration script:

```sql
-- Run the migration script: scripts/add_videosdk_call_type.sql
ALTER TABLE user_video_calls 
MODIFY COLUMN call_type ENUM('call', 'video-call', 'videosdk-call') DEFAULT 'call';
```

## Error Handling

Common error responses:

```json
{
  "status": false,
  "statusCode": 400,
  "message": "Insufficient balance to complete the video call."
}
```

```json
{
  "status": false,
  "statusCode": 400,
  "message": "Video call not allowed due to blocking."
}
```

```json
{
  "status": false,
  "statusCode": 400,
  "message": "User has enabled DND"
}
```

## Security Features

- **Authentication required** for all endpoints
- **User validation** before call initiation
- **Blocking check** between users
- **DND (Do Not Disturb)** status check
- **Balance validation** before call start

## Monitoring and Analytics

The system provides comprehensive data for:
- Call duration tracking
- Revenue analysis
- Subscription impact on usage
- User behavior patterns
- Transaction history

## Future Enhancements

- Call quality metrics
- Advanced analytics dashboard
- Bulk call operations
- Call scheduling
- Group call support 