/**
 * Migration script to add external_id column to messages table
 * Run this script to add the external_id column for linking FCM messages to database messages
 */

const fs = require('fs');
const path = require('path');
const queryRunner = require('../dbConfig/queryRunner');

async function runMigration() {
  try {
    console.log('Starting migration: Add external_id to messages table...');

    // Read the SQL migration file
    const sqlFile = path.join(__dirname, '../database/add_external_id_to_messages.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');

    // Split SQL statements (remove comments and empty lines)
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--'));

    // Execute each statement
    for (const statement of statements) {
      if (statement.trim()) {
        console.log('Executing:', statement.substring(0, 100) + '...');
        await queryRunner.queryRunner(statement);
        console.log('✅ Statement executed successfully');
      }
    }

    console.log('✅ Migration completed successfully!');
    console.log('The messages table now has an external_id column for linking FCM messages.');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration()
  .then(() => {
    console.log('Migration script finished.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
