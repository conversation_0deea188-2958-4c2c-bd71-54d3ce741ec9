require("dotenv").config({ path: `.env.${process.env.NODE_ENV || "dev"}` });

const app = require("./config/app.js");
const cors = require("cors");
const server = require("http").createServer(app);
const moment = require("moment");
const updatedTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

app.use(cors());
app.use("/api", require("./routes/api-routes").router);
app.use("/", require("./routes/render.js"));

const {
  apiErrorHandler,
  unexpectedErrorHandler,
} = require("./config/error.js");
app.use(apiErrorHandler);
unexpectedErrorHandler();

// Cron job
const cron = require("node-cron");
const { checkPlanExpiry } = require("./scripts/checkPlanExpiry");
cron.schedule("0 0 * * *", async () => {
  console.log("Running plan expiry check...");
  await checkPlanExpiry();
});

server.listen(process.env.SERVER_PORT, () => {
  console.log("MOMENT UPDATED TIME.....", updatedTime);
  console.log(`Server started and running on port: ${process.env.SERVER_PORT}`);
});
