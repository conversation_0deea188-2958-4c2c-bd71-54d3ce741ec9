# months
M(a)_1=sty
M(a)_2=lut
M(a)_3=mar
M(a)_4=kwi
M(a)_5=maj
M(a)_6=cze
M(a)_7=lip
M(a)_8=sie
M(a)_9=wrz
M(a)_10=paź
M(a)_11=lis
M(a)_12=gru

M(n)_1=s
M(n)_2=l
M(n)_3=m
M(n)_4=k
M(n)_5=m
M(n)_6=c
M(n)_7=l
M(n)_8=s
M(n)_9=w
M(n)_10=p
M(n)_11=l
M(n)_12=g

M(w)_1=stycznia
M(w)_2=lutego
M(w)_3=marca
M(w)_4=kwietnia
M(w)_5=maja
M(w)_6=czerwca
M(w)_7=lipca
M(w)_8=sierpnia
M(w)_9=września
M(w)_10=października
M(w)_11=listopada
M(w)_12=grudnia

M(A)_1=sty
M(A)_2=lut
M(A)_3=mar
M(A)_4=kwi
M(A)_5=maj
M(A)_6=cze
M(A)_7=lip
M(A)_8=sie
M(A)_9=wrz
M(A)_10=paź
M(A)_11=lis
M(A)_12=gru

M(N)_1=S
M(N)_2=L
M(N)_3=M
M(N)_4=K
M(N)_5=M
M(N)_6=C
M(N)_7=L
M(N)_8=S
M(N)_9=W
M(N)_10=P
M(N)_11=L
M(N)_12=G

M(W)_1=styczeń
M(W)_2=luty
M(W)_3=marzec
M(W)_4=kwiecień
M(W)_5=maj
M(W)_6=czerwiec
M(W)_7=lipiec
M(W)_8=sierpień
M(W)_9=wrzesień
M(W)_10=październik
M(W)_11=listopad
M(W)_12=grudzień

# weekdays
D(a)_1=pon.
D(a)_2=wt.
D(a)_3=śr.
D(a)_4=czw.
D(a)_5=pt.
D(a)_6=sob.
D(a)_7=niedz.

D(n)_1=p
D(n)_2=w
D(n)_3=ś
D(n)_4=c
D(n)_5=p
D(n)_6=s
D(n)_7=n

D(s)_1=pon
D(s)_2=wto
D(s)_3=śro
D(s)_4=czw
D(s)_5=pią
D(s)_6=sob
D(s)_7=nie

D(w)_1=poniedziałek
D(w)_2=wtorek
D(w)_3=środa
D(w)_4=czwartek
D(w)_5=piątek
D(w)_6=sobota
D(w)_7=niedziela

D(A)_1=pon.
D(A)_2=wt.
D(A)_3=śr.
D(A)_4=czw.
D(A)_5=pt.
D(A)_6=sob.
D(A)_7=niedz.

D(N)_1=P
D(N)_2=W
D(N)_3=Ś
D(N)_4=C
D(N)_5=P
D(N)_6=S
D(N)_7=N

D(S)_1=pon
D(S)_2=wto
D(S)_3=śro
D(S)_4=czw
D(S)_5=pią
D(S)_6=sob
D(S)_7=nie

D(W)_1=poniedziałek
D(W)_2=wtorek
D(W)_3=środa
D(W)_4=czwartek
D(W)_5=piątek
D(W)_6=sobota
D(W)_7=niedziela

# quarters
Q(a)_1=I kw.
Q(a)_2=II kw.
Q(a)_3=III kw.
Q(a)_4=IV kw.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=I kwartał
Q(w)_2=II kwartał
Q(w)_3=III kwartał
Q(w)_4=IV kwartał

Q(A)_1=I kw.
Q(A)_2=II kw.
Q(A)_3=III kw.
Q(A)_4=IV kw.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=I kwartał
Q(W)_2=II kwartał
Q(W)_3=III kwartał
Q(W)_4=IV kwartał

# day-period-rules
T0600=morning1
T1000=morning2
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=o północy
P(a)_am=AM
P(a)_noon=w południe
P(a)_pm=PM
P(a)_morning1=rano
P(a)_morning2=przed południem
P(a)_afternoon1=po południu
P(a)_evening1=wieczorem
P(a)_night1=w nocy

P(n)_midnight=o półn.
P(n)_am=a
P(n)_noon=w poł.
P(n)_pm=p
P(n)_morning1=rano
P(n)_morning2=przed poł.
P(n)_afternoon1=po poł.
P(n)_evening1=wiecz.
P(n)_night1=w nocy

P(w)_midnight=o północy
P(w)_am=AM
P(w)_noon=w południe
P(w)_pm=PM
P(w)_morning1=rano
P(w)_morning2=przed południem
P(w)_afternoon1=po południu
P(w)_evening1=wieczorem
P(w)_night1=w nocy

P(A)_midnight=północ
P(A)_am=AM
P(A)_noon=południe
P(A)_pm=PM
P(A)_morning1=rano
P(A)_morning2=przedpołudnie
P(A)_afternoon1=popołudnie
P(A)_evening1=wieczór
P(A)_night1=noc

P(N)_midnight=półn.
P(N)_am=a
P(N)_noon=poł.
P(N)_pm=p
P(N)_morning1=rano
P(N)_morning2=przedpoł.
P(N)_afternoon1=popoł.
P(N)_evening1=wiecz.
P(N)_night1=noc

P(W)_midnight=północ
P(W)_am=AM
P(W)_noon=południe
P(W)_pm=PM
P(W)_morning1=rano
P(W)_morning2=przedpołudnie
P(W)_afternoon1=popołudnie
P(W)_evening1=wieczór
P(W)_night1=noc

# eras
E(w)_0=przed naszą erą
E(w|alt)_0=p.n.e.
E(w)_1=naszej ery
E(w|alt)_1=n.e.

E(a)_0=p.n.e.
E(a|alt)_0=BCE
E(a)_1=n.e.
E(a|alt)_1=CE

# format patterns
F(f)_d=EEEE, d MMMM y
F(l)_d=d MMMM y
F(m)_d=d MMM y
F(s)_d=dd.MM.y

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM.y
F_yMMM=LLL y
F_yMMMM=LLLL y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=Y, 'tydz'. w

I={0}–{1}

# labels of elements
L_era=era
L_year=rok
L_quarter=kwartał
L_month=miesiąc
L_week=tydzień
L_day=dzień
L_weekday=dzień tygodnia
L_dayperiod=rano / po południu / wieczorem
L_hour=godzina
L_minute=minuta
L_second=sekunda
L_zone=strefa czasowa
