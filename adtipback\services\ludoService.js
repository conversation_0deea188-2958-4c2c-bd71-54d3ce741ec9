const dbQuery = require("../dbConfig/queryRunner");
const moment = require("moment");
const redisClient = require('../config/redis');
const logger = require('../utils/logger');
const crypto = require('crypto');

// Helper function to add a delay
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// Helper: Generate random username
function generateRandomUsername() {
  return 'Player_' + Math.random().toString(36).substring(2, 8);
}

// Helper: Generate rejoin token
function generateRejoinToken() {
  return crypto.randomBytes(24).toString('hex');
}

// Helper: Get available color for a room
async function getAvailableColor(roomId) {
  const sql = 'SELECT color FROM ludo_room_players WHERE room_id = ?';
  const usedColors = (await dbQuery.queryRunner(sql, [roomId])).map(r => r.color);
  const allColors = ['R', 'B', 'G', 'Y'];
  return allColors.find(c => !usedColors.includes(c));
}

const searchPlayers = async (challengeAmount) => {
  const maxAttempts = 36; // Retry up to 36 times
  const delayBetweenAttempts = 5000; // 5 second delay between attempts

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const result = await new Promise((resolve, reject) => {
        const sql = `
          SELECT u.id, u.name, u.gender, w.totalBalance
          FROM users u
          JOIN wallet w ON w.createdby = u.id
          WHERE w.totalBalance >= ? AND u.is_available = 1 AND u.is_active = 1
          LIMIT 4
        `;
        dbQuery.queryRunner(sql, [challengeAmount])
          .then((result) => {
            if (result.length < 4) {
              reject({
                status: 400,
                message: "Not enough players available to start the game.",
              });
            } else {
              resolve(result);
            }
          })
          .catch((err) => {
            reject({
              status: 500,
              message: err.message || "Database error while searching for players.",
            });
          });
      });
      return result;
    } catch (err) {
      if (attempt === maxAttempts) {
        throw err;
      }
      console.log(`Attempt ${attempt} failed: ${err.message}. Retrying...`);
      await delay(delayBetweenAttempts);
    }
  }
};

const deductMoneyForGame = (gameId) => {
  return new Promise(async (resolve, reject) => {
    try {
      await dbQuery.queryRunner("START TRANSACTION");

      const gameSql = `
        SELECT player_ids, challenge_amount
        FROM ludo_games
        WHERE id = ?
      `;
      const gameResult = await dbQuery.queryRunner(gameSql, [gameId]);
      if (!gameResult || gameResult.length === 0) {
        throw { status: 404, message: "Game not found." };
      }

      const { player_ids, challenge_amount } = gameResult[0];
      const playerIds = player_ids.split(",").map(Number);

      // Skip wallet operations if challenge_amount is 0
      if (challenge_amount > 0) {
        // Fetch latest balances for all players
        const balanceSql = `
          SELECT createdby, totalBalance
          FROM wallet
          WHERE createdby IN (?)
          ORDER BY id DESC
        `;
        const balanceResult = await dbQuery.queryRunner(balanceSql, [playerIds]);
        const balanceMap = new Map();
        for (const row of balanceResult) {
          // Only take the latest balance for each user
          if (!balanceMap.has(row.createdby)) {
            balanceMap.set(row.createdby, row.totalBalance);
          }
        }

        // Validate balances
        for (const userId of playerIds) {
          const balance = balanceMap.get(Number(userId));
          if (balance === undefined || balance < challenge_amount) {
            throw { status: 400, message: `User ${userId} has insufficient balance.` };
          }
        }

        // Update balances directly
        const updateSql = `
          UPDATE wallet
          SET totalBalance = totalBalance - ?, updateddate = NOW()
          WHERE createdby = ?
        `;
        for (const userId of playerIds) {
          await dbQuery.queryRunner(updateSql, [challenge_amount, userId]);
        }
      }

      await dbQuery.queryRunner("COMMIT");

      resolve({
        gameId,
        playerIds,
        amountDeductedPerPlayer: challenge_amount,
      });
    } catch (err) {
      await dbQuery.queryRunner("ROLLBACK");
      reject({
        status: err.status || 500,
        message: err.message || "Error deducting money for game.",
      });
    }
  });
};

const addMoney = (userId, amount, gameId) => {
  return new Promise(async (resolve, reject) => {
    try {
      await dbQuery.queryRunner("START TRANSACTION");

      // Skip wallet update if amount is 0
      if (amount > 0) {
        // Fetch the latest balance for the user
        const balanceSql = `
          SELECT totalBalance
          FROM wallet
          WHERE createdby = ?
          ORDER BY id DESC
          LIMIT 1
        `;
        const balanceResult = await dbQuery.queryRunner(balanceSql, [userId]);
        if (!balanceResult || balanceResult.length === 0) {
          throw { status: 404, message: `Wallet not found for user ${userId}.` };
        }

        // Update balance directly
        const updateSql = `
          UPDATE wallet
          SET totalBalance = totalBalance + ?, updateddate = NOW()
          WHERE createdby = ?
        `;
        await dbQuery.queryRunner(updateSql, [amount, userId]);
      }

      // Update game winner
      const updateGameSql = `
        UPDATE ludo_games
        SET winner_id = ?
        WHERE id = ?
      `;
      await dbQuery.queryRunner(updateGameSql, [userId, gameId]);

      // Record company share only if amount > 0
      if (amount > 0) {
        const companySql = `
          INSERT INTO company_wallet_funds (amount, game_id, created_date)
          VALUES (?, ?, ?)
        `;
        const istDate = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
        await dbQuery.queryRunner(companySql, [amount, gameId, istDate]);
      }

      await dbQuery.queryRunner("COMMIT");

      resolve({ userId, amountAdded: amount });
    } catch (err) {
      await dbQuery.queryRunner("ROLLBACK");
      reject({
        status: err.status || 500,
        message: err.message || "Error adding money.",
      });
    }
  });
};

const logGame = (playerIds, challengeAmount) => {
  return new Promise((resolve, reject) => {
    const totalAmount = challengeAmount * 4;
    const istDate = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

    const sql = `
      INSERT INTO ludo_games (player_ids, challenge_amount, total_amount, played_date)
      VALUES (?, ?, ?, ?)
    `;
    dbQuery.queryRunner(sql, [playerIds.join(","), challengeAmount, totalAmount, istDate])
      .then((result) => {
        resolve(result.insertId);
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message || "Error logging game.",
        });
      });
  });
};

const logMove = (gameId, userId, diceValue, tokens) => {
  return new Promise((resolve, reject) => {
    const istDate = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
    const tokensJson = JSON.stringify(tokens);

    const sql = `
      INSERT INTO ludo_moves (game_id, user_id, dice_value, tokens, move_date)
      VALUES (?, ?, ?, ?, ?)
    `;
    dbQuery.queryRunner(sql, [gameId, userId, diceValue, tokensJson, istDate])
      .then(() => {
        resolve();
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message || "Error logging move.",
        });
      });
  });
};

const getGameHistory = async (userIds) => {
  return new Promise((resolve, reject) => {
    const sql = `
      SELECT lg.id AS game_id, lg.player_ids, lg.played_date, lg.winner_id,
             GROUP_CONCAT(u.name) AS player_usernames
      FROM ludo_games lg
      JOIN users u ON FIND_IN_SET(u.id, lg.player_ids)
      WHERE ${userIds.map(() => "FIND_IN_SET(?, lg.player_ids)").join(" OR ")}
      GROUP BY lg.id, lg.player_ids, lg.played_date, lg.winner_id
    `;
    dbQuery.queryRunner(sql, userIds)
      .then((games) => {
        const history = userIds.map((userId) => {
          const userGames = games.filter((game) =>
            game.player_ids.split(",").includes(String(userId))
          );
          return {
            userId,
            username: games
              .find((game) => game.player_ids.split(",").includes(String(userId)))
              ?.player_usernames.split(",")
              .find((_, idx) => game.player_ids.split(",")[idx] === String(userId)) || "Unknown",
            games: userGames.map((game) => ({
              gameId: game.game_id,
              playedDate: game.played_date,
              players: game.player_ids.split(",").map((id, idx) => ({
                userId: Number(id),
                username: game.player_usernames.split(",")[idx],
              })),
              winnerId: game.winner_id || null,
            })),
          };
        });
        resolve(history);
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message || "Error fetching game history.",
        });
      });
  });
};

// 1. Get list of all room types, open rooms, and status
const getRoomList = async () => {
  try {
    const roomTypes = ['free', '10', '100', '500', '1000', '2000', '4000', '5000', '8000', '10000'];
    const sql = `SELECT room_type, status, COUNT(*) as count FROM ludo_rooms WHERE status IN ('waiting', 'active') GROUP BY room_type, status`;
    const rows = await dbQuery.queryRunner(sql);
    const result = roomTypes.map(type => {
      const waiting = rows.find(r => r.room_type === type && r.status === 'waiting');
      const active = rows.find(r => r.room_type === type && r.status === 'active');
      return {
        roomType: type,
        waitingRooms: waiting ? waiting.count : 0,
        activeRooms: active ? active.count : 0
      };
    });
    return result;
  } catch (err) {
    logger.error('getRoomList error: ' + err.message);
    throw { status: 500, message: 'Failed to fetch room list.' };
  }
};

// 2. Join a room of a given type
const joinRoom = async (userId, roomType) => {
  const connection = await dbQuery.queryRunner;
  try {
    // Check if user is already in a waiting/active room of this type
    const checkSql = `SELECT rp.*, r.status as room_status FROM ludo_room_players rp JOIN ludo_rooms r ON rp.room_id = r.id WHERE rp.user_id = ? AND r.room_type = ? AND r.status IN ('waiting', 'active') AND rp.status IN ('joined', 'active', 'disconnected')`;
    const existing = await dbQuery.queryRunner(checkSql, [userId, roomType]);
    if (existing.length > 0) {
      throw { status: 400, message: 'User already in a game of this type.' };
    }
    // Find a waiting room with <4 players
    const findRoomSql = `SELECT id FROM ludo_rooms WHERE room_type = ? AND status = 'waiting' ORDER BY created_at ASC LIMIT 1`;
    const rooms = await dbQuery.queryRunner(findRoomSql, [roomType]);
    let roomId;
    if (rooms.length > 0) {
      roomId = rooms[0].id;
    } else {
      // Create new room
      const roomCode = `LUDO-${roomType}-${Date.now().toString().slice(-5)}`;
      const insertRoomSql = `INSERT INTO ludo_rooms (room_code, room_type, challenge_amount, status) VALUES (?, ?, ?, 'waiting')`;
      const challengeAmount = roomType === 'free' ? 0 : parseInt(roomType);
      const result = await dbQuery.queryRunner(insertRoomSql, [roomCode, roomType, challengeAmount]);
      roomId = result.insertId;
    }
    // Check player count
    const countSql = `SELECT COUNT(*) as cnt FROM ludo_room_players WHERE room_id = ? AND status IN ('joined', 'active', 'disconnected')`;
    const countRes = await dbQuery.queryRunner(countSql, [roomId]);
    if (countRes[0].cnt >= 4) {
      throw { status: 400, message: 'Room is full.' };
    }
    // Check wallet for paid games
    if (roomType !== 'free') {
      const walletSql = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      const walletRes = await dbQuery.queryRunner(walletSql, [userId]);
      if (!walletRes.length || walletRes[0].totalBalance < parseInt(roomType)) {
        throw { status: 400, message: 'Insufficient wallet balance.' };
      }
    }
    // Get user name
    let username = null;
    const userSql = `SELECT name FROM users WHERE id = ?`;
    const userRes = await dbQuery.queryRunner(userSql, [userId]);
    if (userRes.length && userRes[0].name) {
      username = userRes[0].name;
    } else {
      username = generateRandomUsername();
    }
    // Assign color
    const color = await getAvailableColor(roomId);
    if (!color) {
      throw { status: 400, message: 'No available color in room.' };
    }
    // Generate rejoin token
    const rejoinToken = generateRejoinToken();
    // Add player to room
    const insertPlayerSql = `INSERT INTO ludo_room_players (room_id, user_id, username, color, status, rejoin_token) VALUES (?, ?, ?, ?, 'joined', ?)`;
    await dbQuery.queryRunner(insertPlayerSql, [roomId, userId, username, color, rejoinToken]);
    // Add session to Redis
    await redisClient.set(`ludo:session:${userId}`, JSON.stringify({ roomId, rejoinToken }), 'EX', 60 * 60 * 2); // 2 hours expiry
    logger.info(`User ${userId} joined room ${roomId} as ${color}`);
    return { roomId, userId, username, color, rejoinToken };
  } catch (err) {
    logger.error('joinRoom error: ' + err.message);
    throw err.status ? err : { status: 500, message: 'Failed to join room.' };
  }
};

// 3. Continue a disconnected game
const continueGame = async (userId) => {
  try {
    // Check Redis for session
    const sessionStr = await redisClient.get(`ludo:session:${userId}`);
    if (!sessionStr) {
      throw { status: 404, message: 'No disconnected game found.' };
    }
    const session = JSON.parse(sessionStr);
    // Check if player is still in the room and status is disconnected
    const sql = `SELECT rp.*, r.room_type, r.status as room_status FROM ludo_room_players rp JOIN ludo_rooms r ON rp.room_id = r.id WHERE rp.user_id = ? AND rp.status = 'disconnected' AND r.status IN ('waiting', 'active')`;
    const res = await dbQuery.queryRunner(sql, [userId]);
    if (!res.length) {
      throw { status: 404, message: 'No disconnected game found.' };
    }
    logger.info(`User ${userId} can continue game in room ${session.roomId}`);
    return { userId, roomId: session.roomId, rejoinToken: session.rejoinToken, canContinue: true };
  } catch (err) {
    logger.error('continueGame error: ' + err.message);
    return { userId, canContinue: false, message: err.message || 'No disconnected game found.' };
  }
};

// Get lobby counts for different room types (entry fees)
const getLobbyCounts = async () => {
  try {
    // Query to get current player counts in waiting rooms by room type
    const sql = `
      SELECT 
        r.room_type,
        COUNT(DISTINCT rp.user_id) as current_players
      FROM ludo_rooms r
      LEFT JOIN ludo_room_players rp ON r.id = rp.room_id 
        AND rp.status IN ('joined', 'active', 'disconnected')
      WHERE r.status = 'waiting'
      GROUP BY r.room_type
    `;
    
    const rows = await dbQuery.queryRunner(sql);
    
    // Map room types to entry fees and format the response
    const lobbyCounts = {};
    
    // Define the mapping of room types to entry fees
    const roomTypeToEntryFee = {
      'free': 0,
      '1': 1,
      '5': 5,
      '10': 10,
      '25': 25,
      '50': 50,
      '100': 100
    };
    
    // Initialize all entry fees with 0 current players
    Object.values(roomTypeToEntryFee).forEach(entryFee => {
      lobbyCounts[entryFee] = { current: 0, total: 4 };
    });
    
    // Update with actual counts from database
    rows.forEach(row => {
      const entryFee = roomTypeToEntryFee[row.room_type];
      if (entryFee !== undefined) {
        lobbyCounts[entryFee] = {
          current: Math.min(row.current_players || 0, 4), // Cap at 4 players max
          total: 4
        };
      }
    });
    
    logger.info('Lobby counts retrieved successfully');
    return lobbyCounts;
  } catch (err) {
    logger.error('getLobbyCounts error: ' + err.message);
    throw { status: 500, message: 'Failed to fetch lobby counts.' };
  }
};

module.exports = {
  searchPlayers,
  deductMoneyForGame,
  addMoney,
  logGame,
  logMove,
  getGameHistory,
  getRoomList,
  joinRoom,
  continueGame,
  getLobbyCounts,
  dbQuery
};