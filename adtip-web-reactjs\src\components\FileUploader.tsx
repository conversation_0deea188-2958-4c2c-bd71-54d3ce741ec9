import React, { useState, useRef, useCallback } from 'react';
import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { Upload, X, FileImage, FileVideo, AlertCircle, CheckCircle2 } from 'lucide-react';
import cloudflareUploadService, { UploadProgress, UploadResult } from '../services/CloudflareUploadService';
import { SUPPORTED_FORMATS, FILE_SIZE_LIMITS, UPLOAD_FOLDERS } from '../config/cloudflareConfig';
import { logDebug, logError } from '../utils/ProductionLogger';
import { useAuth } from '../contexts/AuthContext';

interface FileUploaderProps {
  onUploadComplete: (result: UploadResult) => void;
  onUploadError?: (error: string) => void;
  acceptedFileTypes?: 'image' | 'video' | 'all';
  maxFileSize?: number;
  folder?: string;
  className?: string;
  buttonText?: string;
  dropzoneText?: string;
}

const FileUploader: React.FC<FileUploaderProps> = ({
  onUploadComplete,
  onUploadError,
  acceptedFileTypes = 'all',
  maxFileSize,
  folder,
  className = '',
  buttonText = 'Upload File',
  dropzoneText = 'Drag & drop files here or click to browse',
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { user } = useAuth();

  // Determine accepted file types
  const getAcceptedTypes = (): string => {
    switch (acceptedFileTypes) {
      case 'image':
        return SUPPORTED_FORMATS.IMAGE.map(ext => `.${ext}`).join(',');
      case 'video':
        return SUPPORTED_FORMATS.VIDEO.map(ext => `.${ext}`).join(',');
      case 'all':
      default:
        return [
          ...SUPPORTED_FORMATS.IMAGE.map(ext => `.${ext}`),
          ...SUPPORTED_FORMATS.VIDEO.map(ext => `.${ext}`),
        ].join(',');
    }
  };

  // Determine max file size
  const getMaxFileSize = (): number => {
    if (maxFileSize) return maxFileSize;
    
    switch (acceptedFileTypes) {
      case 'image':
        return FILE_SIZE_LIMITS.IMAGE_MAX;
      case 'video':
        return FILE_SIZE_LIMITS.VIDEO_MAX;
      case 'all':
      default:
        return FILE_SIZE_LIMITS.VIDEO_MAX; // Use largest limit
    }
  };

  // Determine upload folder
  const getUploadFolder = (): string => {
    if (folder) return folder;
    
    switch (acceptedFileTypes) {
      case 'image':
        return UPLOAD_FOLDERS.IMAGES;
      case 'video':
        return UPLOAD_FOLDERS.VIDEOS;
      case 'all':
      default:
        return UPLOAD_FOLDERS.TEMP;
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;
    
    // Validate file
    const validationError = validateFile(selectedFile);
    if (validationError) {
      setError(validationError);
      setFile(null);
      return;
    }
    
    setFile(selectedFile);
    setError(null);
    setUploadResult(null);
  };

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > getMaxFileSize()) {
      const maxSizeMB = Math.round(getMaxFileSize() / (1024 * 1024));
      return `File size exceeds ${maxSizeMB}MB limit`;
    }
    
    // Check file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension) return 'Invalid file type';
    
    if (acceptedFileTypes === 'image' && !SUPPORTED_FORMATS.IMAGE.includes(fileExtension)) {
      return `Only ${SUPPORTED_FORMATS.IMAGE.join(', ')} images are supported`;
    }
    
    if (acceptedFileTypes === 'video' && !SUPPORTED_FORMATS.VIDEO.includes(fileExtension)) {
      return `Only ${SUPPORTED_FORMATS.VIDEO.join(', ')} videos are supported`;
    }
    
    return null;
  };

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    const droppedFile = event.dataTransfer.files?.[0];
    if (!droppedFile) return;
    
    // Validate file
    const validationError = validateFile(droppedFile);
    if (validationError) {
      setError(validationError);
      setFile(null);
      return;
    }
    
    setFile(droppedFile);
    setError(null);
    setUploadResult(null);
  }, [acceptedFileTypes, validateFile]);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  const handleUpload = async () => {
    if (!file) return;
    
    setIsUploading(true);
    setError(null);
    setUploadResult(null);
    
    try {
      logDebug('FileUploader', 'Starting file upload', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        folder: getUploadFolder(),
      });
      
      // Determine upload method based on file type
      const isImage = SUPPORTED_FORMATS.IMAGE.includes(file.name.split('.').pop()?.toLowerCase() || '');
      const isVideo = SUPPORTED_FORMATS.VIDEO.includes(file.name.split('.').pop()?.toLowerCase() || '');
      
      let result: UploadResult;
      
      if (isImage) {
        result = await cloudflareUploadService.uploadImage(
          file,
          getUploadFolder(),
          undefined, // Use original filename
          user?.id,
          true, // Enable compression
          setUploadProgress
        );
      } else if (isVideo) {
        result = await cloudflareUploadService.uploadVideo(
          file,
          getUploadFolder(),
          undefined, // Use original filename
          user?.id,
          setUploadProgress
        );
      } else {
        result = await cloudflareUploadService.uploadFile(
          file,
          getUploadFolder(),
          undefined, // Use original filename
          user?.id,
          setUploadProgress
        );
      }
      
      if (result.success) {
        setUploadResult(result);
        onUploadComplete(result);
        logDebug('FileUploader', 'Upload completed successfully', {
          url: result.url,
          key: result.key,
        });
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown upload error';
      setError(errorMessage);
      onUploadError?.(errorMessage);
      logError('FileUploader', 'Upload failed', error as Error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setFile(null);
    setError(null);
    setUploadResult(null);
    setUploadProgress(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const getFileIcon = () => {
    if (!file) return null;
    
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension) return <FileImage className="w-8 h-8 text-gray-400" />;
    
    if (SUPPORTED_FORMATS.IMAGE.includes(fileExtension)) {
      return <FileImage className="w-8 h-8 text-blue-500" />;
    }
    
    if (SUPPORTED_FORMATS.VIDEO.includes(fileExtension)) {
      return <FileVideo className="w-8 h-8 text-purple-500" />;
    }
    
    return <FileImage className="w-8 h-8 text-gray-400" />;
  };

  return (
    <div className={`w-full ${className}`}>
      {/* File input (hidden) */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept={getAcceptedTypes()}
        className="hidden"
      />
      
      {/* Dropzone */}
      {!file && (
        <div
          className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer bg-gray-50"
          onClick={() => fileInputRef.current?.click()}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">{dropzoneText}</p>
          <p className="mt-1 text-xs text-gray-500">
            {acceptedFileTypes === 'image' && `Supported formats: ${SUPPORTED_FORMATS.IMAGE.join(', ')}`}
            {acceptedFileTypes === 'video' && `Supported formats: ${SUPPORTED_FORMATS.VIDEO.join(', ')}`}
            {acceptedFileTypes === 'all' && 'Supported formats: Images and Videos'}
          </p>
          <p className="mt-1 text-xs text-gray-500">
            Max size: {formatFileSize(getMaxFileSize())}
          </p>
          <Button
            type="button"
            className="mt-4"
            onClick={(e) => {
              e.stopPropagation();
              fileInputRef.current?.click();
            }}
          >
            {buttonText}
          </Button>
        </div>
      )}
      
      {/* Selected file */}
      {file && (
        <div className="border rounded-lg p-4 bg-white">
          <div className="flex items-center">
            {getFileIcon()}
            <div className="ml-3 flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
              <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
            </div>
            <button
              type="button"
              onClick={handleCancel}
              className="ml-2 text-gray-400 hover:text-gray-500"
              disabled={isUploading}
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          {/* Upload progress */}
          {isUploading && uploadProgress && (
            <div className="mt-4">
              <div className="flex justify-between text-xs text-gray-500 mb-1">
                <span>Uploading...</span>
                <span>{uploadProgress.percentage}%</span>
              </div>
              <Progress value={uploadProgress.percentage} className="h-2" />
              {uploadProgress.speed && (
                <p className="text-xs text-gray-500 mt-1">
                  {formatFileSize(uploadProgress.speed)}/s
                  {uploadProgress.timeRemaining && ` • ${Math.ceil(uploadProgress.timeRemaining)}s remaining`}
                </p>
              )}
            </div>
          )}
          
          {/* Error message */}
          {error && (
            <div className="mt-4 flex items-center text-red-500 text-sm">
              <AlertCircle className="h-4 w-4 mr-1" />
              <span>{error}</span>
            </div>
          )}
          
          {/* Success message */}
          {uploadResult?.success && (
            <div className="mt-4 flex items-center text-green-500 text-sm">
              <CheckCircle2 className="h-4 w-4 mr-1" />
              <span>Upload successful</span>
            </div>
          )}
          
          {/* Upload button */}
          {!isUploading && !uploadResult?.success && (
            <Button
              type="button"
              className="mt-4 w-full"
              onClick={handleUpload}
              disabled={isUploading}
            >
              {isUploading ? 'Uploading...' : 'Upload'}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default FileUploader;
