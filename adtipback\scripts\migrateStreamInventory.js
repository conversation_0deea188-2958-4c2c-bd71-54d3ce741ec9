// adtipback/scripts/migrateStreamInventory.js
// <PERSON>ript to migrate Cloudflare Stream inventory to database

const dbQuery = require('../dbConfig/queryRunner');
const CloudflareStreamService = require('../services/CloudflareStreamService');
const VideoInventoryAnalyzer = require('./analyzeVideoInventory');

class StreamInventoryMigrator {
  constructor() {
    this.streamService = CloudflareStreamService;
    this.analyzer = new VideoInventoryAnalyzer();
    this.dryRun = true; // Set to false to actually execute updates
  }

  /**
   * Main migration function
   */
  async migrate(options = {}) {
    const { dryRun = true, batchSize = 50 } = options;
    this.dryRun = dryRun;

    console.log(`\n🚀 STARTING STREAM INVENTORY MIGRATION (${dryRun ? 'DRY RUN' : 'LIVE RUN'})\n`);

    try {
      // Step 1: Analyze current state
      const report = await this.analyzer.generateMigrationReport();
      
      // Step 2: Get all ready Stream videos
      const readyStreamVideos = report.stream.streamDetails.filter(
        video => video.status === 'ready'
      );

      console.log(`\n📋 Found ${readyStreamVideos.length} ready Stream videos to process`);

      // Step 3: Get database videos that need Stream IDs
      const videosNeedingStream = await this.getVideosNeedingStreamIds();
      
      console.log(`📋 Found ${videosNeedingStream.length} database videos needing Stream IDs`);

      // Step 4: Match videos based on filename/metadata
      const matches = await this.matchVideosToStream(videosNeedingStream, readyStreamVideos);
      
      console.log(`\n🔗 Found ${matches.length} potential matches`);

      // Step 5: Process matches in batches
      if (matches.length > 0) {
        await this.processBatches(matches, batchSize);
      }

      // Step 6: Handle unmatched videos
      await this.handleUnmatchedVideos(videosNeedingStream, readyStreamVideos, matches);

      console.log('\n✅ Migration process completed!');
      
      return {
        success: true,
        processed: matches.length,
        dryRun: this.dryRun
      };

    } catch (error) {
      console.error('\n❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Get database videos that need Stream IDs
   */
  async getVideosNeedingStreamIds() {
    const sql = `
      SELECT 
        id,
        name,
        video_link,
        video_Thumbnail,
        video_desciption,
        total_views,
        total_likes,
        video_channel,
        stream_video_id,
        stream_status,
        createddate,
        is_shot
      FROM reels 
      WHERE is_active = 1 
        AND (stream_video_id IS NULL OR stream_status != 'ready')
        AND video_link IS NOT NULL
        AND video_link LIKE '%theadtip.in%'
      ORDER BY total_views DESC, createddate DESC
    `;

    return await dbQuery.queryRunner(sql);
  }

  /**
   * Match database videos to Stream videos
   */
  async matchVideosToStream(dbVideos, streamVideos) {
    console.log('\n🔍 Matching videos to Stream inventory...');
    
    const matches = [];
    const usedStreamIds = new Set();

    for (const dbVideo of dbVideos) {
      const match = this.findBestStreamMatch(dbVideo, streamVideos, usedStreamIds);
      
      if (match) {
        matches.push({
          dbVideo,
          streamVideo: match,
          confidence: this.calculateMatchConfidence(dbVideo, match)
        });
        usedStreamIds.add(match.uid);
        
        console.log(`✅ Matched DB video ${dbVideo.id} to Stream ${match.uid} (confidence: ${this.calculateMatchConfidence(dbVideo, match)})`);
      }
    }

    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Find best Stream match for a database video
   */
  findBestStreamMatch(dbVideo, streamVideos, usedStreamIds) {
    let bestMatch = null;
    let bestScore = 0;

    for (const streamVideo of streamVideos) {
      if (usedStreamIds.has(streamVideo.uid)) continue;

      const score = this.calculateMatchScore(dbVideo, streamVideo);
      
      if (score > bestScore && score > 0.3) { // Minimum confidence threshold
        bestScore = score;
        bestMatch = streamVideo;
      }
    }

    return bestMatch;
  }

  /**
   * Calculate match score between database video and Stream video
   */
  calculateMatchScore(dbVideo, streamVideo) {
    let score = 0;
    let factors = 0;

    // Check filename similarity
    if (streamVideo.filename && dbVideo.name) {
      const filenameSimilarity = this.calculateStringSimilarity(
        streamVideo.filename.toLowerCase(),
        dbVideo.name.toLowerCase()
      );
      score += filenameSimilarity * 0.4;
      factors += 0.4;
    }

    // Check metadata if available
    if (streamVideo.meta) {
      if (streamVideo.meta.name && dbVideo.name) {
        const metaSimilarity = this.calculateStringSimilarity(
          streamVideo.meta.name.toLowerCase(),
          dbVideo.name.toLowerCase()
        );
        score += metaSimilarity * 0.3;
        factors += 0.3;
      }

      if (streamVideo.meta.description && dbVideo.video_desciption) {
        const descSimilarity = this.calculateStringSimilarity(
          streamVideo.meta.description.toLowerCase(),
          dbVideo.video_desciption.toLowerCase()
        );
        score += descSimilarity * 0.2;
        factors += 0.2;
      }
    }

    // Check creation date proximity (if available)
    if (streamVideo.created && dbVideo.createddate) {
      const streamDate = new Date(streamVideo.created);
      const dbDate = new Date(dbVideo.createddate);
      const daysDiff = Math.abs(streamDate - dbDate) / (1000 * 60 * 60 * 24);
      
      if (daysDiff <= 7) { // Within a week
        score += (7 - daysDiff) / 7 * 0.1;
        factors += 0.1;
      }
    }

    return factors > 0 ? score / factors : 0;
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  calculateStringSimilarity(str1, str2) {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    for (let i = 0; i <= len2; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= len1; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= len2; i++) {
      for (let j = 1; j <= len1; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : (maxLen - matrix[len2][len1]) / maxLen;
  }

  /**
   * Calculate match confidence
   */
  calculateMatchConfidence(dbVideo, streamVideo) {
    return Math.round(this.calculateMatchScore(dbVideo, streamVideo) * 100);
  }

  /**
   * Process matches in batches
   */
  async processBatches(matches, batchSize) {
    console.log(`\n📦 Processing ${matches.length} matches in batches of ${batchSize}...`);

    for (let i = 0; i < matches.length; i += batchSize) {
      const batch = matches.slice(i, i + batchSize);
      
      console.log(`\n🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(matches.length / batchSize)}`);
      
      await this.processBatch(batch);
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  /**
   * Process a single batch of matches
   */
  async processBatch(batch) {
    const updates = [];

    for (const match of batch) {
      const { dbVideo, streamVideo } = match;
      
      const updateSql = `
        UPDATE reels SET 
          stream_video_id = ?,
          stream_status = 'ready',
          adaptive_manifest_url = ?,
          stream_ready_at = NOW()
        WHERE id = ?
      `;

      const manifestUrl = `https://customer-${this.streamService.customerCode}.cloudflarestream.com/${streamVideo.uid}/manifest/video.m3u8`;
      const params = [streamVideo.uid, manifestUrl, dbVideo.id];

      if (this.dryRun) {
        console.log(`[DRY RUN] Would update video ${dbVideo.id} with Stream ID ${streamVideo.uid}`);
      } else {
        updates.push({ sql: updateSql, params });
      }
    }

    if (!this.dryRun && updates.length > 0) {
      // Execute all updates in a transaction
      try {
        await dbQuery.queryRunner('START TRANSACTION');
        
        for (const update of updates) {
          await dbQuery.queryRunner(update.sql, update.params);
        }
        
        await dbQuery.queryRunner('COMMIT');
        console.log(`✅ Successfully updated ${updates.length} videos`);
        
      } catch (error) {
        await dbQuery.queryRunner('ROLLBACK');
        console.error('❌ Batch update failed:', error);
        throw error;
      }
    }
  }

  /**
   * Handle unmatched videos
   */
  async handleUnmatchedVideos(dbVideos, streamVideos, matches) {
    const matchedDbIds = new Set(matches.map(m => m.dbVideo.id));
    const matchedStreamIds = new Set(matches.map(m => m.streamVideo.uid));
    
    const unmatchedDb = dbVideos.filter(v => !matchedDbIds.has(v.id));
    const unmatchedStream = streamVideos.filter(v => !matchedStreamIds.has(v.uid));

    console.log(`\n⚠️  UNMATCHED ITEMS:`);
    console.log(`Database videos without Stream match: ${unmatchedDb.length}`);
    console.log(`Stream videos without database match: ${unmatchedStream.length}`);

    if (unmatchedDb.length > 0) {
      console.log('\n📋 Unmatched database videos (top 10):');
      unmatchedDb.slice(0, 10).forEach(video => {
        console.log(`  - ID: ${video.id}, Name: ${video.name}`);
      });
    }

    if (unmatchedStream.length > 0) {
      console.log('\n📋 Unmatched Stream videos (top 10):');
      unmatchedStream.slice(0, 10).forEach(video => {
        console.log(`  - UID: ${video.uid}, Filename: ${video.filename}`);
      });
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  const migrator = new StreamInventoryMigrator();
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--live');
  const batchSize = parseInt(args.find(arg => arg.startsWith('--batch='))?.split('=')[1]) || 50;

  console.log(`Running migration with dryRun=${dryRun}, batchSize=${batchSize}`);
  console.log('Use --live flag to execute actual updates');
  console.log('Use --batch=N to set batch size');

  migrator.migrate({ dryRun, batchSize })
    .then(result => {
      console.log('\n✅ Migration completed:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Migration failed:', error);
      process.exit(1);
    });
}

module.exports = StreamInventoryMigrator;
