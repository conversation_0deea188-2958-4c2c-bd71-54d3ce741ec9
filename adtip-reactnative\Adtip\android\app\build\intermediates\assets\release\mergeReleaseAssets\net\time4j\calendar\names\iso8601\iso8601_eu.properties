# months
M(a)_1=urt.
M(a)_2=ots.
M(a)_3=mar.
M(a)_4=api.
M(a)_5=mai.
M(a)_6=eka.
M(a)_7=uzt.
M(a)_8=abu.
M(a)_9=ira.
M(a)_10=urr.
M(a)_11=aza.
M(a)_12=abe.

M(n)_1=U
M(n)_2=O
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=E
M(n)_7=U
M(n)_8=A
M(n)_9=I
M(n)_10=U
M(n)_11=A
M(n)_12=A

M(w)_1=urtarrila
M(w)_2=otsaila
M(w)_3=martxoa
M(w)_4=apirila
M(w)_5=maiatza
M(w)_6=ekaina
M(w)_7=uztaila
M(w)_8=abuztua
M(w)_9=iraila
M(w)_10=urria
M(w)_11=azaroa
M(w)_12=abendua

M(A)_1=urt.
M(A)_2=ots.
M(A)_3=mar.
M(A)_4=api.
M(A)_5=mai.
M(A)_6=eka.
M(A)_7=uzt.
M(A)_8=abu.
M(A)_9=ira.
M(A)_10=urr.
M(A)_11=aza.
M(A)_12=abe.

M(N)_1=U
M(N)_2=O
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=E
M(N)_7=U
M(N)_8=A
M(N)_9=I
M(N)_10=U
M(N)_11=A
M(N)_12=A

M(W)_1=urtarrila
M(W)_2=otsaila
M(W)_3=martxoa
M(W)_4=apirila
M(W)_5=maiatza
M(W)_6=ekaina
M(W)_7=uztaila
M(W)_8=abuztua
M(W)_9=iraila
M(W)_10=urria
M(W)_11=azaroa
M(W)_12=abendua

# weekdays
D(a)_1=al.
D(a)_2=ar.
D(a)_3=az.
D(a)_4=og.
D(a)_5=or.
D(a)_6=lr.
D(a)_7=ig.

D(n)_1=A
D(n)_2=A
D(n)_3=A
D(n)_4=O
D(n)_5=O
D(n)_6=L
D(n)_7=I

D(s)_1=al.
D(s)_2=ar.
D(s)_3=az.
D(s)_4=og.
D(s)_5=or.
D(s)_6=lr.
D(s)_7=ig.

D(w)_1=astelehena
D(w)_2=asteartea
D(w)_3=asteazkena
D(w)_4=osteguna
D(w)_5=ostirala
D(w)_6=larunbata
D(w)_7=igandea

D(A)_1=al.
D(A)_2=ar.
D(A)_3=az.
D(A)_4=og.
D(A)_5=or.
D(A)_6=lr.
D(A)_7=ig.

D(N)_1=A
D(N)_2=A
D(N)_3=A
D(N)_4=O
D(N)_5=O
D(N)_6=L
D(N)_7=I

D(S)_1=al.
D(S)_2=ar.
D(S)_3=az.
D(S)_4=og.
D(S)_5=or.
D(S)_6=lr.
D(S)_7=ig.

D(W)_1=astelehena
D(W)_2=asteartea
D(W)_3=asteazkena
D(W)_4=osteguna
D(W)_5=ostirala
D(W)_6=larunbata
D(W)_7=igandea

# quarters
Q(a)_1=1Hh
Q(a)_2=2Hh
Q(a)_3=3Hh
Q(a)_4=4Hh

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. hiruhilekoa
Q(w)_2=2. hiruhilekoa
Q(w)_3=3. hiruhilekoa
Q(w)_4=4. hiruhilekoa

Q(A)_1=1Hh
Q(A)_2=2Hh
Q(A)_3=3Hh
Q(A)_4=4Hh

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1. hiruhilekoa
Q(W)_2=2. hiruhilekoa
Q(W)_3=3. hiruhilekoa
Q(W)_4=4. hiruhilekoa

# day-period-rules
T0000=morning1
T0600=morning2
T1200=afternoon1
T1400=afternoon2
T1900=evening1
T2100=night1

# day-period-translations
P(a)_midnight=gauerdia
P(a)_am=AM
P(a)_pm=PM
P(a)_morning1=goizald.
P(a)_morning2=goizeko
P(a)_afternoon1=eguerd.
P(a)_afternoon2=arrats.
P(a)_evening1=iluntz.
P(a)_night1=gaueko

P(n)_midnight=gauerdia
P(n)_am=g
P(n)_pm=a
P(n)_morning1=goizald.
P(n)_morning2=goizeko
P(n)_afternoon1=eguerd.
P(n)_afternoon2=arrats.
P(n)_evening1=iluntz.
P(n)_night1=gaueko

P(w)_midnight=gauerdia
P(w)_am=AM
P(w)_pm=PM
P(w)_morning1=goizaldeko
P(w)_morning2=goizeko
P(w)_afternoon1=eguerdiko
P(w)_afternoon2=arratsaldeko
P(w)_evening1=iluntzeko
P(w)_night1=gaueko

P(A)_midnight=gauerdia
P(A)_am=AM
P(A)_pm=PM
P(A)_morning1=goiz.
P(A)_morning2=goiza
P(A)_afternoon1=eguerd.
P(A)_afternoon2=arrats.
P(A)_evening1=iluntz.
P(A)_night1=gaua

P(N)_midnight=gauerdia
P(N)_am=AM
P(N)_pm=PM
P(N)_morning1=goizald.
P(N)_morning2=goiza
P(N)_afternoon1=eguerd.
P(N)_afternoon2=arrats.
P(N)_evening1=iluntz.
P(N)_night1=gaua

P(W)_midnight=gauerdia
P(W)_am=AM
P(W)_pm=PM
P(W)_morning1=goizaldea
P(W)_morning2=goiza
P(W)_afternoon1=eguerdia
P(W)_afternoon2=arratsaldea
P(W)_evening1=iluntzea
P(W)_night1=gaua

# eras
E(w)_0=K.a.
E(w|alt)_0=Gure aroaren aurretik
E(w)_1=Kristo ondoren
E(w|alt)_1=Gure aroa

E(a)_0=K.a.
E(a|alt)_0=G.a.a.
E(a)_1=K.o.
E(a|alt)_1=G.a.

# format patterns
F(f)_d=y('e')'ko' MMMM'ren' d('a'), EEEE
F(l)_d=y('e')'ko' MMMM'ren' d('a')
F(m)_d=y('e')'ko' MMM d('a')
F(s)_d=yy/M/d

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss (zzzz)
F(l)_t=HH:mm:ss (z)
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=B h
F_Bhm=B h:mm
F_Bhms=B h:mm:ss
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=M/d
F_MMMd=MMM d
F_MMMMd=MMMM d
F_y=y
F_yM=y/M
F_yMMM=y MMM
F_yMMMM=y('e')'ko' MMMM
F_yQQQ=y('e')'ko' QQQ
F_yQQQQ=y('e')'ko' QQQQ
F_yw=Y. 'urteko' w. 'astea'

I={0} – {1}

# labels of elements
L_era=aroa
L_year=urtea
L_quarter=hiruhilekoa
L_month=hilabetea
L_week=astea
L_day=eguna
L_weekday=asteguna
L_dayperiod=AM/PM
L_hour=ordua
L_minute=minutua
L_second=segundoa
L_zone=ordu-zona
