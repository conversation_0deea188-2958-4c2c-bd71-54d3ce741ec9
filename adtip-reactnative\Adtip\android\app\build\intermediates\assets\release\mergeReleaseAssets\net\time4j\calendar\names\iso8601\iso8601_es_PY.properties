# months
M(a)_1=ene.
M(a)_2=feb.
M(a)_3=mar.
M(a)_4=abr.
M(a)_5=may.
M(a)_6=jun.
M(a)_7=jul.
M(a)_8=ago.
M(a)_9=sept.
M(a)_10=oct.
M(a)_11=nov.
M(a)_12=dic.

M(A)_1=ene.
M(A)_2=feb.
M(A)_3=mar.
M(A)_4=abr.
M(A)_5=may.
M(A)_6=jun.
M(A)_7=jul.
M(A)_8=ago.
M(A)_9=sept.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=dic.

# weekdays
D(s)_1=lu
D(s)_2=ma
D(s)_3=mi
D(s)_4=ju
D(s)_5=vi
D(s)_6=sa
D(s)_7=do

D(S)_1=Lu
D(S)_2=Ma
D(S)_3=Mi
D(S)_4=Ju
D(S)_5=Vi
D(S)_6=Sa
D(S)_7=Do

# day-period-translations
P(a)_am=a. m.
P(a)_pm=p. m.

P(w)_am=a. m.
P(w)_pm=p. m.

P(A)_am=a. m.
P(A)_pm=p. m.

P(N)_am=a. m.
P(N)_noon=m.
P(N)_pm=p. m.

P(W)_am=a. m.
P(W)_pm=p. m.
