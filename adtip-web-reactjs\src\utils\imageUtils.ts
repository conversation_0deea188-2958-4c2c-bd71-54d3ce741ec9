import { logDebug, logError } from './ProductionLogger';

/**
 * Validates if an image URL is accessible
 */
export const validateImageUrl = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { 
      method: 'HEAD',
      mode: 'cors',
      cache: 'no-cache'
    });
    return response.ok;
  } catch (error) {
    logError('ImageUtils', 'Failed to validate image URL', error as Error, { url });
    return false;
  }
};

/**
 * Gets the full image URL, handling relative paths and null values
 */
export const getFullImageUrl = (url?: string | null, baseUrl?: string): string | null => {
  if (!url || url === 'null' || url === 'undefined' || url.trim() === '') {
    return null;
  }

  // If it's already a full URL, return as-is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // If we have a base URL, construct the full URL
  if (baseUrl) {
    const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    const cleanPath = url.startsWith('/') ? url : `/${url}`;
    return `${cleanBaseUrl}${cleanPath}`;
  }

  // Default fallback
  return url;
};

/**
 * Generates a placeholder image URL based on dimensions
 */
export const getPlaceholderImageUrl = (width: number = 400, height: number = 300): string => {
  return `https://via.placeholder.com/${width}x${height}/e5e7eb/9ca3af?text=Loading...`;
};

/**
 * Generates a random avatar URL
 */
export const getRandomAvatarUrl = (seed?: string | number): string => {
  const seedValue = seed ? encodeURIComponent(seed.toString()) : Math.random().toString();
  return `https://avatar.iran.liara.run/public?seed=${seedValue}`;
};

/**
 * Checks if a URL is from a trusted domain
 */
export const isTrustedImageDomain = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const trustedDomains = [
      'theadtip.in',
      'cloudflarestorage.com',
      'cloudflare.com',
      'via.placeholder.com',
      'avatar.iran.liara.run',
      'picsum.photos',
      'unsplash.com',
      'images.unsplash.com'
    ];
    
    return trustedDomains.some(domain => urlObj.hostname.includes(domain));
  } catch (error) {
    logError('ImageUtils', 'Failed to parse URL for domain check', error as Error, { url });
    return false;
  }
};

/**
 * Preloads an image to check if it's accessible
 */
export const preloadImage = (src: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    
    img.onload = () => {
      logDebug('ImageUtils', 'Image preloaded successfully', { src });
      resolve(true);
    };
    
    img.onerror = (error) => {
      logError('ImageUtils', 'Image preload failed', new Error('Image load error'), { src });
      resolve(false);
    };
    
    // Set a timeout to avoid hanging
    setTimeout(() => {
      logError('ImageUtils', 'Image preload timeout', new Error('Timeout'), { src });
      resolve(false);
    }, 10000); // 10 second timeout
    
    img.src = src;
  });
};

/**
 * Gets the best available image URL with fallbacks
 */
export const getBestImageUrl = async (
  primaryUrl?: string | null,
  fallbackUrl?: string | null,
  placeholderDimensions?: { width: number; height: number }
): Promise<string> => {
  // Try primary URL first
  if (primaryUrl) {
    const fullPrimaryUrl = getFullImageUrl(primaryUrl);
    if (fullPrimaryUrl && isTrustedImageDomain(fullPrimaryUrl)) {
      const isAccessible = await preloadImage(fullPrimaryUrl);
      if (isAccessible) {
        return fullPrimaryUrl;
      }
    }
  }

  // Try fallback URL
  if (fallbackUrl) {
    const fullFallbackUrl = getFullImageUrl(fallbackUrl);
    if (fullFallbackUrl && isTrustedImageDomain(fullFallbackUrl)) {
      const isAccessible = await preloadImage(fullFallbackUrl);
      if (isAccessible) {
        return fullFallbackUrl;
      }
    }
  }

  // Return placeholder
  if (placeholderDimensions) {
    return getPlaceholderImageUrl(placeholderDimensions.width, placeholderDimensions.height);
  }

  return getPlaceholderImageUrl();
};

/**
 * Extracts image dimensions from URL if available
 */
export const extractImageDimensions = (url: string): { width?: number; height?: number } => {
  try {
    const urlObj = new URL(url);
    const width = urlObj.searchParams.get('width') || urlObj.searchParams.get('w');
    const height = urlObj.searchParams.get('height') || urlObj.searchParams.get('h');
    
    return {
      width: width ? parseInt(width) : undefined,
      height: height ? parseInt(height) : undefined,
    };
  } catch (error) {
    return {};
  }
};

/**
 * Optimizes image URL for better loading performance
 */
export const optimizeImageUrl = (
  url: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  } = {}
): string => {
  try {
    // For theadtip.in URLs, return as-is for now to avoid loading issues
    if (url.includes('theadtip.in')) {
      return url;
    }

    // For other Cloudflare URLs, add optimization parameters
    if (url.includes('cloudflarestorage.com')) {
      const urlObj = new URL(url);
      
      if (options.width) urlObj.searchParams.set('width', options.width.toString());
      if (options.height) urlObj.searchParams.set('height', options.height.toString());
      if (options.quality) urlObj.searchParams.set('quality', options.quality.toString());
      if (options.format) urlObj.searchParams.set('format', options.format);
      
      return urlObj.toString();
    }

    return url;
  } catch (error) {
    logError('ImageUtils', 'Failed to optimize image URL', error as Error, { url, options });
    return url;
  }
};
