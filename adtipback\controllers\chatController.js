const ChatService = require("../services/chatService");

const ChatController = {
  sendmessage: (req, res, next) => {
    ChatService.savemessages(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  },

  getMessages: (req, res, next) => {
    const { userId, chattinguserid } = req.query;
    ChatService.getMessages(userId, chattinguserid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  },

  getUnreadMessageCount: (req, res, next) => {
    const { userId } = req.params;
    if (!userId) {
      return res.status(400).send({
        status: 400,
        message: "User ID is required",
        data: [],
      });
    }

    ChatService.getUnreadMessageCount(userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  },

  markMessagesAsRead: (req, res, next) => {
    const { userId, senderId } = req.body;
    if (!userId || !senderId) {
      return res.status(400).send({
        status: 400,
        message: "User ID and Sender ID are required",
        data: [],
      });
    }

    ChatService.markMessagesAsRead(userId, senderId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message || "Internal server error.",
          data: [],
        });
      });
  },
};

module.exports = ChatController;