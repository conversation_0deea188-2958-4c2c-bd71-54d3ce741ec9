const { queryRunner } = require('../dbConfig/queryRunner');

/**
 * Inbox Controller
 * Handles inbox-related operations for the messaging system
 */
const InboxController = {
  /**
   * Get conversations for a specific user (inbox view with grouped messages)
   * GET /api/inbox/conversations/:userId
   */
  getInboxConversations: async (req, res) => {
    try {
      const userId = req.params.userId;
      const {
        page = 1,
        limit = 20,
        filter = 'all', // 'all', 'unread', 'read'
        sortOrder = 'desc' // 'asc', 'desc'
      } = req.query;

      // Validate user authorization
      if (req.user.user_id !== parseInt(userId)) {
        return res.status(403).json({
          status: 403,
          message: 'Access denied. You can only view your own inbox.',
          data: []
        });
      }

      const offset = (page - 1) * limit;
      const limitClause = parseInt(limit);

      // Filter condition
      let filterCondition = '';
      if (filter === 'unread') {
        filterCondition = 'AND (mds.status != "read" OR mds.status IS NULL)';
      } else if (filter === 'read') {
        filterCondition = 'AND mds.status = "read"';
      }

      // Sort clause
      const sortClause = `ORDER BY latest_created_at ${sortOrder.toUpperCase()}`;

      // Main query to get conversations grouped by sender
      const conversationsQuery = `
        SELECT
          m.sender_id,
          u.name as sender_name,
          u.profile_image as sender_avatar,
          latest_msg.id as latest_message_id,
          latest_msg.chat_id,
          latest_msg.content as latest_content,
          latest_msg.message_type as latest_message_type,
          latest_msg.created_at as latest_created_at,
          COUNT(DISTINCT m.id) as total_messages,
          COUNT(DISTINCT CASE
            WHEN (mds.status != 'read' OR mds.status IS NULL)
            THEN m.id
          END) as unread_count
        FROM messages m
        INNER JOIN users u ON m.sender_id = u.id
        LEFT JOIN message_delivery_status mds ON m.id = mds.message_id AND mds.recipient_id = ?
        INNER JOIN (
          SELECT sender_id, MAX(created_at) as max_created_at
          FROM messages
          WHERE recipient_id = ? AND is_deleted = 0
          GROUP BY sender_id
        ) latest ON m.sender_id = latest.sender_id
        INNER JOIN messages latest_msg ON latest_msg.sender_id = latest.sender_id
          AND latest_msg.created_at = latest.max_created_at
          AND latest_msg.recipient_id = ?
        WHERE m.recipient_id = ?
          AND m.is_deleted = 0
          ${filterCondition}
        GROUP BY m.sender_id, u.name, u.profile_image, latest_msg.id, latest_msg.chat_id,
                 latest_msg.content, latest_msg.message_type, latest_msg.created_at
        ${sortClause}
        LIMIT ? OFFSET ?
      `;

      // Count query for pagination
      const countQuery = `
        SELECT COUNT(DISTINCT m.sender_id) as total
        FROM messages m
        LEFT JOIN message_delivery_status mds ON m.id = mds.message_id AND mds.recipient_id = ?
        WHERE m.recipient_id = ?
          AND m.is_deleted = 0
          ${filterCondition}
      `;

      // Execute queries
      const [conversations, countResult] = await Promise.all([
        queryRunner(conversationsQuery, [userId, userId, userId, userId, limitClause, offset]),
        queryRunner(countQuery, [userId, userId])
      ]);

      const total = countResult[0]?.total || 0;
      const totalPages = Math.ceil(total / limitClause);

      // Format conversations for response
      const formattedConversations = conversations.map(conversation => ({
        id: conversation.latest_message_id,
        chatId: conversation.chat_id,
        senderId: conversation.sender_id,
        senderName: conversation.sender_name,
        senderAvatar: conversation.sender_avatar,
        latestContent: conversation.latest_content,
        latestMessageType: conversation.latest_message_type,
        latestCreatedAt: conversation.latest_created_at,
        totalMessages: conversation.total_messages,
        unreadCount: conversation.unread_count,
        // Truncate content for preview (max 100 chars)
        preview: conversation.latest_content.length > 100
          ? conversation.latest_content.substring(0, 100) + '...'
          : conversation.latest_content
      }));

      res.status(200).json({
        status: 200,
        message: 'Conversations retrieved successfully',
        data: {
          conversations: formattedConversations,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: total,
            itemsPerPage: limitClause,
            hasNextPage: parseInt(page) < totalPages,
            hasPreviousPage: parseInt(page) > 1
          }
        }
      });

    } catch (error) {
      console.error('[InboxController] Error fetching inbox conversations:', error);
      res.status(500).json({
        status: 500,
        message: 'Failed to fetch inbox conversations',
        data: []
      });
    }
  },

  /**
   * Get all messages for a specific user (inbox view)
   * GET /api/inbox/:userId
   */
  getInboxMessages: async (req, res) => {
    try {
      const userId = req.params.userId;
      const { 
        page = 1, 
        limit = 20, 
        filter = 'all', // 'all', 'unread', 'read'
        sortBy = 'date', // 'date', 'sender'
        sortOrder = 'desc' // 'asc', 'desc'
      } = req.query;

      // Validate user authorization
      if (req.user.user_id !== parseInt(userId)) {
        return res.status(403).json({
          status: 403,
          message: 'Access denied. You can only view your own inbox.',
          data: []
        });
      }

      // Calculate pagination
      const offset = (parseInt(page) - 1) * parseInt(limit);
      const limitClause = parseInt(limit);

      // Build filter conditions
      let filterCondition = '';
      if (filter === 'unread') {
        filterCondition = `AND (mds.status IS NULL OR mds.status != 'read')`;
      } else if (filter === 'read') {
        filterCondition = `AND mds.status = 'read'`;
      }

      // Build sort clause
      let sortClause = '';
      if (sortBy === 'sender') {
        sortClause = `ORDER BY u.name ${sortOrder.toUpperCase()}, m.created_at DESC`;
      } else {
        sortClause = `ORDER BY m.created_at ${sortOrder.toUpperCase()}`;
      }

      // Main query to get inbox messages with sender details
      const inboxQuery = `
        SELECT 
          m.id,
          m.chat_id,
          m.sender_id,
          m.content,
          m.message_type,
          m.file_url,
          m.thumbnail_url,
          m.created_at,
          m.status as message_status,
          u.name as sender_name,
          u.profile_image as sender_avatar,
          CASE 
            WHEN mds.status = 'read' THEN 'read'
            WHEN mds.status = 'delivered' THEN 'delivered'
            WHEN mds.status = 'sent' THEN 'sent'
            ELSE 'unread'
          END as read_status,
          mds.timestamp as read_at
        FROM messages m
        INNER JOIN users u ON m.sender_id = u.id
        LEFT JOIN message_delivery_status mds ON m.id = mds.message_id AND mds.recipient_id = ?
        WHERE m.recipient_id = ? 
          AND m.is_deleted = 0
          ${filterCondition}
        ${sortClause}
        LIMIT ? OFFSET ?
      `;

      // Count query for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM messages m
        LEFT JOIN message_delivery_status mds ON m.id = mds.message_id AND mds.recipient_id = ?
        WHERE m.recipient_id = ? 
          AND m.is_deleted = 0
          ${filterCondition}
      `;

      // Execute queries
      const [messages, countResult] = await Promise.all([
        queryRunner(inboxQuery, [userId, userId, limitClause, offset]),
        queryRunner(countQuery, [userId, userId])
      ]);

      const total = countResult[0]?.total || 0;
      const totalPages = Math.ceil(total / limitClause);

      // Format messages for response
      const formattedMessages = messages.map(message => ({
        id: message.id,
        chatId: message.chat_id,
        senderId: message.sender_id,
        senderName: message.sender_name,
        senderAvatar: message.sender_avatar,
        content: message.content,
        messageType: message.message_type,
        fileUrl: message.file_url,
        thumbnailUrl: message.thumbnail_url,
        createdAt: message.created_at,
        messageStatus: message.message_status,
        readStatus: message.read_status,
        readAt: message.read_at,
        // Truncate content for preview (max 100 chars)
        preview: message.content.length > 100 
          ? message.content.substring(0, 100) + '...' 
          : message.content
      }));

      res.status(200).json({
        status: 200,
        message: 'Inbox messages retrieved successfully',
        data: {
          messages: formattedMessages,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalMessages: total,
            hasNextPage: parseInt(page) < totalPages,
            hasPrevPage: parseInt(page) > 1
          },
          filters: {
            filter,
            sortBy,
            sortOrder
          }
        }
      });

    } catch (error) {
      console.error('[InboxController] Error getting inbox messages:', error);
      res.status(500).json({
        status: 500,
        message: 'Failed to retrieve inbox messages',
        data: []
      });
    }
  },

  /**
   * Mark a specific message as read
   * PUT /api/inbox/mark-read/:messageId
   */
  markMessageAsRead: async (req, res) => {
    try {
      const messageId = req.params.messageId;
      const userId = req.user.user_id;

      // Verify the message exists and user is the recipient
      const messageQuery = `
        SELECT id, recipient_id, sender_id, chat_id 
        FROM messages 
        WHERE id = ? AND recipient_id = ? AND is_deleted = 0
      `;
      
      const messageResult = await queryRunner(messageQuery, [messageId, userId]);
      
      if (messageResult.length === 0) {
        return res.status(404).json({
          status: 404,
          message: 'Message not found or access denied',
          data: {}
        });
      }

      const message = messageResult[0];

      // Check if already marked as read
      const existingStatusQuery = `
        SELECT status FROM message_delivery_status 
        WHERE message_id = ? AND recipient_id = ? AND status = 'read'
      `;
      
      const existingStatus = await queryRunner(existingStatusQuery, [messageId, userId]);
      
      if (existingStatus.length > 0) {
        return res.status(200).json({
          status: 200,
          message: 'Message already marked as read',
          data: { messageId, readStatus: 'read' }
        });
      }

      // Insert or update delivery status
      const updateStatusQuery = `
        INSERT INTO message_delivery_status (message_id, recipient_id, status, timestamp)
        VALUES (?, ?, 'read', NOW())
        ON DUPLICATE KEY UPDATE 
          status = 'read',
          timestamp = NOW()
      `;
      
      await queryRunner(updateStatusQuery, [messageId, userId]);

      // Update user_chat_metadata to decrease unread count
      const updateMetadataQuery = `
        UPDATE user_chat_metadata 
        SET 
          unread_count = GREATEST(0, unread_count - 1),
          last_read_message_id = ?,
          updated_at = NOW()
        WHERE user_id = ? AND chat_id = ?
      `;
      
      await queryRunner(updateMetadataQuery, [messageId, userId, message.chat_id]);

      res.status(200).json({
        status: 200,
        message: 'Message marked as read successfully',
        data: {
          messageId,
          readStatus: 'read',
          readAt: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error('[InboxController] Error marking message as read:', error);
      res.status(500).json({
        status: 500,
        message: 'Failed to mark message as read',
        data: {}
      });
    }
  },

  /**
   * Get unread message count for a user
   * GET /api/inbox/unread-count/:userId
   */
  getUnreadCount: async (req, res) => {
    try {
      const userId = req.params.userId;

      // Validate user authorization
      if (req.user.user_id !== parseInt(userId)) {
        return res.status(403).json({
          status: 403,
          message: 'Access denied. You can only view your own unread count.',
          data: { unreadCount: 0 }
        });
      }

      // Get total unread count from user_chat_metadata
      const unreadCountQuery = `
        SELECT COALESCE(SUM(unread_count), 0) as total_unread
        FROM user_chat_metadata 
        WHERE user_id = ?
      `;
      
      const result = await queryRunner(unreadCountQuery, [userId]);
      const unreadCount = result[0]?.total_unread || 0;

      res.status(200).json({
        status: 200,
        message: 'Unread count retrieved successfully',
        data: {
          userId: parseInt(userId),
          unreadCount: parseInt(unreadCount)
        }
      });

    } catch (error) {
      console.error('[InboxController] Error getting unread count:', error);
      res.status(500).json({
        status: 500,
        message: 'Failed to retrieve unread count',
        data: { unreadCount: 0 }
      });
    }
  }
};

module.exports = InboxController;
