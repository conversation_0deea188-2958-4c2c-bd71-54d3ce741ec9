# Message Cleanup Cron Job Configuration
# 
# This cron configuration runs the message cleanup script automatically
# to maintain database health and enforce message retention policies.
#
# Installation Instructions:
# 1. Copy this file to your server
# 2. Update the paths to match your deployment
# 3. Add to crontab: crontab -e
# 4. Add the lines below (uncomment and modify paths)
#
# Log files will be created in /var/log/adtip/ directory
# Make sure the directory exists and has proper permissions:
#   sudo mkdir -p /var/log/adtip
#   sudo chown www-data:www-data /var/log/adtip
#   sudo chmod 755 /var/log/adtip

# =====================================================
# DAILY MESSAGE CLEANUP (Recommended)
# =====================================================
# Runs every day at 2:00 AM to clean up messages older than 7 days
# Adjust the time to avoid peak usage hours
# 0 2 * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 7 >> /var/log/adtip/message-cleanup.log 2>&1

# =====================================================
# WEEKLY DEEP CLEANUP (Recommended)
# =====================================================
# Runs every Sunday at 3:00 AM for comprehensive cleanup including hard deletes
# 0 3 * * 0 /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 7 --hard-delete-days 30 --verbose >> /var/log/adtip/message-cleanup-weekly.log 2>&1

# =====================================================
# HOURLY STATISTICS (Optional)
# =====================================================
# Runs every hour to log database statistics for monitoring
# Useful for tracking database growth and cleanup effectiveness
# 0 * * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --stats-only >> /var/log/adtip/message-stats.log 2>&1

# =====================================================
# CUSTOM CONFIGURATIONS
# =====================================================

# Conservative cleanup (14 days retention) - for high-traffic systems
# 0 2 * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 14 >> /var/log/adtip/message-cleanup.log 2>&1

# Aggressive cleanup (3 days retention) - for low-storage systems
# 0 2 * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 3 >> /var/log/adtip/message-cleanup.log 2>&1

# Test run (dry-run mode) - for testing before deployment
# 0 2 * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --dry-run --verbose >> /var/log/adtip/message-cleanup-test.log 2>&1

# =====================================================
# LOG ROTATION SETUP
# =====================================================
# Add this to /etc/logrotate.d/adtip-message-cleanup
#
# /var/log/adtip/message-cleanup*.log {
#     daily
#     rotate 30
#     compress
#     delaycompress
#     missingok
#     notifempty
#     create 644 www-data www-data
# }

# =====================================================
# MONITORING AND ALERTS
# =====================================================
# You can add monitoring by checking the exit code of the cleanup script
# and sending alerts if it fails:
#
# 0 2 * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 7 >> /var/log/adtip/message-cleanup.log 2>&1 || echo "Message cleanup failed" | mail -s "Adtip Message Cleanup Failed" <EMAIL>

# =====================================================
# ENVIRONMENT-SPECIFIC CONFIGURATIONS
# =====================================================

# Development Environment (more frequent, shorter retention)
# */30 * * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 1 >> /var/log/adtip/message-cleanup-dev.log 2>&1

# Staging Environment (daily, moderate retention)
# 0 1 * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 3 >> /var/log/adtip/message-cleanup-staging.log 2>&1

# Production Environment (daily, standard retention)
# 0 2 * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 7 >> /var/log/adtip/message-cleanup-prod.log 2>&1

# =====================================================
# BACKUP INTEGRATION
# =====================================================
# Run cleanup after database backup to ensure backed up data includes
# messages that will be deleted
# 0 4 * * * /path/to/backup-script.sh && /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --retention-days 7 >> /var/log/adtip/message-cleanup.log 2>&1

# =====================================================
# HEALTH CHECK
# =====================================================
# Simple health check to ensure the cleanup script is working
# This creates a heartbeat file that monitoring systems can check
# 5 2 * * * /usr/bin/node /path/to/adtipback/scripts/cleanup-old-messages.js --stats-only > /tmp/adtip-cleanup-heartbeat && echo "$(date): Cleanup script healthy" >> /var/log/adtip/cleanup-heartbeat.log

# =====================================================
# EXAMPLE PRODUCTION SETUP
# =====================================================
# Uncomment and modify these lines for a typical production setup:

# Daily cleanup at 2 AM
# 0 2 * * * cd /var/www/adtipback && /usr/bin/node scripts/cleanup-old-messages.js --retention-days 7 >> /var/log/adtip/message-cleanup.log 2>&1

# Weekly deep cleanup on Sunday at 3 AM
# 0 3 * * 0 cd /var/www/adtipback && /usr/bin/node scripts/cleanup-old-messages.js --retention-days 7 --hard-delete-days 30 --verbose >> /var/log/adtip/message-cleanup-weekly.log 2>&1

# Hourly stats for monitoring
# 0 * * * * cd /var/www/adtipback && /usr/bin/node scripts/cleanup-old-messages.js --stats-only >> /var/log/adtip/message-stats.log 2>&1

# =====================================================
# TROUBLESHOOTING
# =====================================================
# If the cron job is not running:
# 1. Check if cron service is running: sudo systemctl status cron
# 2. Check cron logs: sudo tail -f /var/log/cron
# 3. Verify script permissions: chmod +x /path/to/cleanup-old-messages.js
# 4. Test script manually: node /path/to/cleanup-old-messages.js --dry-run --verbose
# 5. Check log files for errors: tail -f /var/log/adtip/message-cleanup.log
#
# Common issues:
# - Node.js not in PATH: Use full path to node binary
# - Database connection issues: Check database credentials and connectivity
# - Permission issues: Ensure cron user has access to script and log directories
# - Memory issues: Monitor system resources during cleanup
