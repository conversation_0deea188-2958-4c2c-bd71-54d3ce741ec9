{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake"}, {"isExternal": true, "path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake"}, {"isExternal": true, "path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake"}, {"isExternal": true, "path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake"}, {"isExternal": true, "path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake"}, {"isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake"}, {"isExternal": true, "path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isExternal": true, "path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake"}, {"isExternal": true, "path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/CMakeLists.txt"}, {"isExternal": true, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/arm64-v8a", "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 1, "minor": 0}}