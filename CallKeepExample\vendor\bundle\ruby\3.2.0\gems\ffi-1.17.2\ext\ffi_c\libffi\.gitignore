.libs
.deps
*.o
*.lo
*.wasm
*.js
*.test
*.log
.dirstamp
*.la
Makefile
!testsuite/libffi.bhaible/Makefile
Makefile.in
aclocal.m4
compile
!.ci/compile
configure
depcomp
doc/libffi.info
*~
fficonfig.h.in
fficonfig.h
include/ffi.h
include/ffitarget.h
install-sh
libffi.pc
libtool
ltmain.sh
m4/libtool.m4
m4/lt*.m4
mdate-sh
missing
stamp-h1
libffi*gz
autom4te.cache
libffi.xcodeproj/xcuserdata
libffi.xcodeproj/project.xcworkspace
build_*/
darwin_*/
src/arm/trampoline.S
**/texinfo.tex
target/
__pycache__
.docker_home
emsdk
test-results
