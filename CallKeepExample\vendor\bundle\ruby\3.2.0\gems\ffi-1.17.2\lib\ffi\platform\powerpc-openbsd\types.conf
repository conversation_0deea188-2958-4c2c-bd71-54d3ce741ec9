rbx.platform.typedef.__blkcnt_t = long_long
rbx.platform.typedef.__blksize_t = int
rbx.platform.typedef.__clock_t = long_long
rbx.platform.typedef.__clockid_t = int
rbx.platform.typedef.__cpuid_t = ulong
rbx.platform.typedef.__dev_t = int
rbx.platform.typedef.__fd_mask = uint
rbx.platform.typedef.__fixpt_t = uint
rbx.platform.typedef.__fsblkcnt_t = ulong_long
rbx.platform.typedef.__fsfilcnt_t = ulong_long
rbx.platform.typedef.__gid_t = uint
rbx.platform.typedef.__id_t = uint
rbx.platform.typedef.__in_addr_t = uint
rbx.platform.typedef.__in_port_t = ushort
rbx.platform.typedef.__ino_t = ulong_long
rbx.platform.typedef.__int16_t = short
rbx.platform.typedef.__int32_t = int
rbx.platform.typedef.__int64_t = long_long
rbx.platform.typedef.__int8_t = char
rbx.platform.typedef.__int_fast16_t = int
rbx.platform.typedef.__int_fast32_t = int
rbx.platform.typedef.__int_fast64_t = long_long
rbx.platform.typedef.__int_fast8_t = int
rbx.platform.typedef.__int_least16_t = short
rbx.platform.typedef.__int_least32_t = int
rbx.platform.typedef.__int_least64_t = long_long
rbx.platform.typedef.__int_least8_t = char
rbx.platform.typedef.__intmax_t = long_long
rbx.platform.typedef.__intptr_t = long
rbx.platform.typedef.__key_t = long
rbx.platform.typedef.__mode_t = uint
rbx.platform.typedef.__nlink_t = uint
rbx.platform.typedef.__off_t = long_long
rbx.platform.typedef.__paddr_t = ulong
rbx.platform.typedef.__pid_t = int
rbx.platform.typedef.__psize_t = ulong
rbx.platform.typedef.__ptrdiff_t = long
rbx.platform.typedef.__register_t = long
rbx.platform.typedef.__rlim_t = ulong_long
rbx.platform.typedef.__rune_t = int
rbx.platform.typedef.__sa_family_t = uchar
rbx.platform.typedef.__segsz_t = int
rbx.platform.typedef.__size_t = ulong
rbx.platform.typedef.__socklen_t = uint
rbx.platform.typedef.__ssize_t = long
rbx.platform.typedef.__suseconds_t = long
rbx.platform.typedef.__swblk_t = int
rbx.platform.typedef.__time_t = long_long
rbx.platform.typedef.__timer_t = int
rbx.platform.typedef.__uid_t = uint
rbx.platform.typedef.__uint16_t = ushort
rbx.platform.typedef.__uint32_t = uint
rbx.platform.typedef.__uint64_t = ulong_long
rbx.platform.typedef.__uint8_t = uchar
rbx.platform.typedef.__uint_fast16_t = uint
rbx.platform.typedef.__uint_fast32_t = uint
rbx.platform.typedef.__uint_fast64_t = ulong_long
rbx.platform.typedef.__uint_fast8_t = uint
rbx.platform.typedef.__uint_least16_t = ushort
rbx.platform.typedef.__uint_least32_t = uint
rbx.platform.typedef.__uint_least64_t = ulong_long
rbx.platform.typedef.__uint_least8_t = uchar
rbx.platform.typedef.__uintmax_t = ulong_long
rbx.platform.typedef.__uintptr_t = ulong
rbx.platform.typedef.__useconds_t = uint
rbx.platform.typedef.__vaddr_t = ulong
rbx.platform.typedef.__vsize_t = ulong
rbx.platform.typedef.__wchar_t = int
rbx.platform.typedef.__wctrans_t = pointer
rbx.platform.typedef.__wctype_t = pointer
rbx.platform.typedef.__wint_t = int
rbx.platform.typedef.blkcnt_t = long_long
rbx.platform.typedef.blksize_t = int
rbx.platform.typedef.caddr_t = string
rbx.platform.typedef.clock_t = long_long
rbx.platform.typedef.clockid_t = int
rbx.platform.typedef.cpuid_t = ulong
rbx.platform.typedef.daddr32_t = int
rbx.platform.typedef.daddr_t = long_long
rbx.platform.typedef.dev_t = int
rbx.platform.typedef.fixpt_t = uint
rbx.platform.typedef.fsblkcnt_t = ulong_long
rbx.platform.typedef.fsfilcnt_t = ulong_long
rbx.platform.typedef.gid_t = uint
rbx.platform.typedef.id_t = uint
rbx.platform.typedef.in_addr_t = uint
rbx.platform.typedef.in_port_t = ushort
rbx.platform.typedef.ino_t = ulong_long
rbx.platform.typedef.int16_t = short
rbx.platform.typedef.int32_t = int
rbx.platform.typedef.int64_t = long_long
rbx.platform.typedef.int8_t = char
rbx.platform.typedef.int_fast16_t = int
rbx.platform.typedef.int_fast32_t = int
rbx.platform.typedef.int_fast64_t = long_long
rbx.platform.typedef.int_fast8_t = int
rbx.platform.typedef.int_least16_t = short
rbx.platform.typedef.int_least32_t = int
rbx.platform.typedef.int_least64_t = long_long
rbx.platform.typedef.int_least8_t = char
rbx.platform.typedef.intmax_t = long_long
rbx.platform.typedef.intptr_t = long
rbx.platform.typedef.key_t = long
rbx.platform.typedef.mode_t = uint
rbx.platform.typedef.nlink_t = uint
rbx.platform.typedef.off_t = long_long
rbx.platform.typedef.paddr_t = ulong
rbx.platform.typedef.pid_t = int
rbx.platform.typedef.psize_t = ulong
rbx.platform.typedef.ptrdiff_t = long
rbx.platform.typedef.quad_t = long_long
rbx.platform.typedef.register_t = long
rbx.platform.typedef.rlim_t = ulong_long
rbx.platform.typedef.sa_family_t = uchar
rbx.platform.typedef.segsz_t = int
rbx.platform.typedef.sigset_t = uint
rbx.platform.typedef.size_t = ulong
rbx.platform.typedef.socklen_t = uint
rbx.platform.typedef.ssize_t = long
rbx.platform.typedef.suseconds_t = long
rbx.platform.typedef.swblk_t = int
rbx.platform.typedef.time_t = long_long
rbx.platform.typedef.timer_t = int
rbx.platform.typedef.u_char = uchar
rbx.platform.typedef.u_int = uint
rbx.platform.typedef.u_int16_t = ushort
rbx.platform.typedef.u_int32_t = uint
rbx.platform.typedef.u_int64_t = ulong_long
rbx.platform.typedef.u_int8_t = uchar
rbx.platform.typedef.u_long = ulong
rbx.platform.typedef.u_quad_t = ulong_long
rbx.platform.typedef.u_short = ushort
rbx.platform.typedef.uid_t = uint
rbx.platform.typedef.uint = uint
rbx.platform.typedef.uint16_t = ushort
rbx.platform.typedef.uint32_t = uint
rbx.platform.typedef.uint64_t = ulong_long
rbx.platform.typedef.uint8_t = uchar
rbx.platform.typedef.uint_fast16_t = uint
rbx.platform.typedef.uint_fast32_t = uint
rbx.platform.typedef.uint_fast64_t = ulong_long
rbx.platform.typedef.uint_fast8_t = uint
rbx.platform.typedef.uint_least16_t = ushort
rbx.platform.typedef.uint_least32_t = uint
rbx.platform.typedef.uint_least64_t = ulong_long
rbx.platform.typedef.uint_least8_t = uchar
rbx.platform.typedef.uintmax_t = ulong_long
rbx.platform.typedef.uintptr_t = ulong
rbx.platform.typedef.ulong = ulong
rbx.platform.typedef.unchar = uchar
rbx.platform.typedef.useconds_t = uint
rbx.platform.typedef.ushort = ushort
rbx.platform.typedef.vaddr_t = ulong
rbx.platform.typedef.vsize_t = ulong
rbx.platform.typedef.wchar_t = int
rbx.platform.typedef.wint_t = int
