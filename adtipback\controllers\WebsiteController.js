let websiteService = require("../services/websiteService");

module.exports = {
  saveWebsiteMessage: (req, res, next) => {
    websiteService
      .saveWebsiteMessage(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        console.log("err", err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  googleplace: (req, res, next) => {
    websiteService
      .googlePlace(req.query.input)
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        console.log("err", err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  googlePlaceDetails: (req, res, next) => {
    websiteService
      .googlePlaceDetails(req.query.placeid)
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        console.log("err", err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  checkAdPendingBalance: (req, res, next) => {
    websiteService
      .checkAdPendingBalance(req.params.id)
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        console.log("err", err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  insertQRScanWebData: (req, res, next) => {
    websiteService
      .insertQRScanWebData(req.body)
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        console.log("err", err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getQrAdWebByAdIdForAdvertiser: (req, res, next) => {
    websiteService
      .getQrAdWebByAdIdForAdvertiser(req.params.adId)
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        console.log("err", err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getQrAdWebAllForAdmin: (req, res, next) => {
    websiteService
      .getQrAdWebAllForAdmin()
      .then((result) => {
        res.status(200).send(result);
      })
      .catch((err) => {
        console.log("err", err);
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
};
