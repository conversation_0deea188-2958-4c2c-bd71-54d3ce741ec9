const VideoSDKCallService = require('../services/VideoSDKCallService');
const { queryRunner } = require('../dbConfig/queryRunner');

// @route   POST /api/VideoSDKCall-call
// @desc    Initiate a VideoSDK call with subscription-based pricing
exports.initiateVideoSDKCall = async (req, res) => {
  try {
    const { callerId, receiverId, action, callId } = req.body;

    // Check if required parameters are provided
    if (!callerId || !receiverId || !action) {
      return res.status(400).json({ 
        status: false, 
        message: "Missing required parameters: callerId, receiverId, action" 
      });
    }

    // Handle different call actions
    switch (action) {
      case "start":
        const startResult = await VideoSDKCallService.startVideoSDKCall(callerId, receiverId);
        return res.status(startResult.statusCode || 200).json(startResult);

      case "end":
        if (!callId) {
          return res.status(400).json({ 
            status: false, 
            message: "callId is required for ending a call" 
          });
        }
        const endResult = await VideoSDKCallService.endVideoSDKCall(callerId, receiverId, callId);
        return res.status(endResult.statusCode || 200).json(endResult);

      case "missed":
        const missedResult = await VideoSDKCallService.missedVideoSDKCall(callerId, receiverId);
        return res.status(missedResult.statusCode || 200).json(missedResult);

      default:
        return res.status(400).json({ 
          status: false, 
          message: "Invalid action. Use 'start', 'end', or 'missed'" 
        });
    }
  } catch (error) {
    console.error("Error in initiateVideoSDKCall:", error);
    return res.status(500).json({ 
      status: false, 
      message: "Internal server error", 
      error: error.message 
    });
  }
};

// @route   GET /api/VideoSDKCall-call/balance/:userId
// @desc    Get user's call balance and subscription status
exports.getCallBalance = async (req, res) => {
  try {
    const { userId } = req.params;
    
    if (!userId) {
      return res.status(400).json({ 
        status: false, 
        message: "User ID is required" 
      });
    }

    const balanceResult = await VideoSDKCallService.getCallBalance(userId);
    return res.status(balanceResult.statusCode || 200).json(balanceResult);
  } catch (error) {
    console.error("Error in getCallBalance:", error);
    return res.status(500).json({ 
      status: false, 
      message: "Internal server error", 
      error: error.message 
    });
  }
};

// @route   GET /api/VideoSDKCall-call/history/:userId
// @desc    Get user's call history
exports.getCallHistory = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    if (!userId) {
      return res.status(400).json({ 
        status: false, 
        message: "User ID is required" 
      });
    }

    const historyResult = await VideoSDKCallService.getCallHistory(userId, parseInt(page), parseInt(limit));
    return res.status(historyResult.statusCode || 200).json(historyResult);
  } catch (error) {
    console.error("Error in getCallHistory:", error);
    return res.status(500).json({ 
      status: false, 
      message: "Internal server error", 
      error: error.message 
    });
  }
}; 