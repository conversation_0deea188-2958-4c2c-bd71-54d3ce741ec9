# frozen_string_literal: true

Gem::Specification.new do |spec|
  spec.name          = 'atomos'
  spec.version       = File.read(File.expand_path('../VERSION', __FILE__))
  spec.authors       = ['<PERSON>']
  spec.email         = ['segi<PERSON><PERSON>@segiddins.me']

  spec.summary       = 'A simple gem to atomically write files'
  spec.homepage      = 'https://github.com/segiddins/atomos'
  spec.license       = 'MIT'

  spec.files         = `git ls-files -z`.split("\x0").reject do |f|
    f.match(%r{^(test|spec|features)/})
  end
  spec.bindir        = 'exe'
  spec.executables   = spec.files.grep(%r{^exe/}) { |f| File.basename(f) }
  spec.require_paths = ['lib']

  spec.required_ruby_version = '>= 2.0'

  spec.add_development_dependency 'bundler', '~> 1.16'
  spec.add_development_dependency 'rake', '~> 10.0'
  spec.add_development_dependency 'rspec', '~> 3.0'
  spec.add_development_dependency 'rubocop'
end
