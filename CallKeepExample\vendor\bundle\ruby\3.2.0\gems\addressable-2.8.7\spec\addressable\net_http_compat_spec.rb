# frozen_string_literal: true

# Copyright (C) <PERSON>
#
#    Licensed under the Apache License, Version 2.0 (the "License");
#    you may not use this file except in compliance with the License.
#    You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.


require "spec_helper"

require "addressable/uri"
require "net/http"

describe Net::HTTP do
  it "should be compatible with Addressable" do
    response_body =
      Net::HTTP.get(Addressable::URI.parse('http://www.google.com/'))
    expect(response_body).not_to be_nil
  end
end
