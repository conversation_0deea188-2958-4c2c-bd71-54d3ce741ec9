# VideoSDK Sandbox Setup Guide

## What is VideoSDK Sandbox?

A **sandbox** is VideoSDK's testing environment where you can:
- Test video calling features safely
- Use test credentials without affecting production
- Debug integration issues
- Validate your implementation before going live

## Step 1: Get VideoSDK Sandbox Credentials

1. Go to [VideoSDK Dashboard](https://app.videosdk.live/)
2. Sign up/Login to your account
3. Navigate to **API Keys** section
4. Create a new project or use existing one
5. Copy your **API Key** and **Secret Key**

## Step 2: Create Environment File

Create a `.env` file in your backend root (`adtipback/.env`):

```env
# VideoSDK Configuration
# Sandbox Environment (for testing)
VIDEOSDK_API_KEY=your_sandbox_api_key_here
VIDEOSDK_SECRET_KEY=your_sandbox_secret_key_here
VIDEOSDK_API_ENDPOINT=https://api.videosdk.live/v2

# Production Environment (when ready)
# VIDEOSDK_API_KEY=your_production_api_key_here
# VIDEOSDK_SECRET_KEY=your_production_secret_key_here
# VIDEOSDK_API_ENDPOINT=https://api.videosdk.live/v2

# Other existing variables...
NODE_ENV=development
HTTP_PORT=7082
```

## Step 3: Update Frontend Configuration

In your React Native app, update the VideoSDK configuration:

### Update `src/services/videosdk/VideoSDKService.ts`:

```typescript
export interface VideoSDKConfig {
  token?: string;
  apiKey?: string;
  region?: 'sg001' | 'us001' | 'eu001';
  // Add sandbox mode
  sandbox?: boolean;
}

class VideoSDKService {
  private config: VideoSDKConfig = {
    region: 'us001',
    sandbox: true, // Enable sandbox mode for testing
  };

  async initialize(config: VideoSDKConfig = {}): Promise<boolean> {
    try {
      console.log('[VideoSDK] Initializing in sandbox mode...');
      
      // Update config with sandbox settings
      this.config = { ...this.config, ...config };
      
      // Register with VideoSDK
      await register();
      
      console.log('[VideoSDK] Sandbox initialization complete');
      return true;
    } catch (error) {
      console.error('[VideoSDK] Sandbox initialization failed:', error);
      return false;
    }
  }
}
```

## Step 4: Test Sandbox Environment

### Test API Endpoints:

1. **Generate Token (Sandbox)**:
```bash
curl -X POST http://localhost:7082/api/generate-token/videosdk \
  -H "Content-Type: application/json" \
  -d '{"userId": "test_user_123"}'
```

2. **Create Meeting (Sandbox)**:
```bash
curl -X POST http://localhost:7082/api/create-meeting/videosdk \
  -H "Content-Type: application/json" \
  -d '{"token": "your_generated_token"}'
```

## Step 5: Sandbox vs Production

### Sandbox Environment:
- ✅ Safe for testing
- ✅ No real charges
- ✅ Limited features
- ✅ Test data only

### Production Environment:
- ⚠️ Real charges apply
- ⚠️ Real user data
- ⚠️ Full features
- ⚠️ Requires proper setup

## Step 6: Switch to Production

When ready for production:

1. Get production API keys from VideoSDK dashboard
2. Update `.env` file with production credentials
3. Set `sandbox: false` in VideoSDKService
4. Test thoroughly before going live

## Troubleshooting

### Common Issues:

1. **"Invalid API Key"**:
   - Check if you're using sandbox keys for sandbox environment
   - Verify API key format

2. **"Meeting creation failed"**:
   - Check network connectivity
   - Verify API endpoint URL
   - Check token generation

3. **"WebSocket connection failed"**:
   - Check if VideoSDK is properly initialized
   - Verify region settings

### Debug Mode:

Add this to your backend for debugging:

```javascript
// In videosdk_service.js
console.log('[VideoSDK Sandbox] API Key:', process.env.VIDEOSDK_API_KEY ? 'Set' : 'Missing');
console.log('[VideoSDK Sandbox] Secret:', process.env.VIDEOSDK_SECRET_KEY ? 'Set' : 'Missing');
console.log('[VideoSDK Sandbox] Endpoint:', process.env.VIDEOSDK_API_ENDPOINT);
```

## Next Steps

1. Get your VideoSDK sandbox credentials
2. Create the `.env` file with the credentials
3. Test the integration
4. Share the sandbox environment with VideoSDK support team

This setup will allow VideoSDK's support team to test your integration in a controlled environment. 