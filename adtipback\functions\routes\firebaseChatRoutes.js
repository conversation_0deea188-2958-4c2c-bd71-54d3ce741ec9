const express = require("express");
// eslint-disable-next-line new-cap
const router = express.Router();
const {v4: uuidv4} = require("uuid");
const admin = require("firebase-admin");

/**
 * Send chat message via FCM
 * POST /api/chat/send-message
 */
router.post("/send-message", async (req, res) => {
  const {
    senderId,
    senderName,
    recipientId,
    recipientToken,
    conversationId,
    content,
    messageType,
    replyToMessageId
  } = req.body;

  // Validate request body
  if (!senderId || !senderName || !recipientId || !recipientToken || !conversationId || !content) {
    return res.status(400).json({error: "Missing required fields"});
  }

  // Validate FCM token format
  if (!recipientToken || recipientToken.length < 10) {
    return res.status(400).json({error: "Invalid FCM token"});
  }

  const messageId = uuidv4();
  const timestamp = new Date().toISOString();

  // Create FCM message payload similar to call format
  const info = JSON.stringify({
    type: "chat_message",
    messageId,
    conversationId,
    senderId,
    senderName,
    content,
    messageType: messageType || "text",
    timestamp,
    replyToMessageId: replyToMessageId || null
  });

  const message = {
    data: {
      info,
    },
    notification: {
      title: senderName,
      body: content.length > 100 ? content.substring(0, 100) + "..." : content,
    },
    token: recipientToken,
  };

  // Platform-specific configuration (similar to call routes)
  // Note: We don't have platform info in chat payload, so we'll use Android defaults
  message.android = {
    priority: "high",
    notification: {
      channelId: "chat_messages",
      priority: "high",
      defaultSound: true,
      defaultVibrateTimings: true,
      clickAction: "FLUTTER_NOTIFICATION_CLICK",
      icon: "ic_notification",
      color: "#FF6B35"
    }
  };

  message.apns = {
    headers: {
      "apns-priority": "10",
    },
    payload: {
      aps: {
        badge: 1,
        sound: "default",
        "content-available": 1,
        category: "CHAT_MESSAGE"
      },
    },
  };

  try {
    // Send message
    const response = await admin.messaging().send(message);
    
    console.log("[ChatAPI] FCM message sent successfully:", {
      messageId: response,
      conversationId,
      senderId,
      recipientId
    });

    res.status(200).json({
      messageId: response,
      success: true,
      data: {
        messageId,
        conversationId,
        timestamp
      }
    });
  } catch (error) {
    console.error("[ChatAPI] FCM Error:", error);
    
    if (error.code === "messaging/invalid-registration-token" ||
        error.code === "messaging/registration-token-not-registered") {
      return res.status(400).json({error: "Invalid or unregistered FCM token"});
    }
    
    res.status(500).json({error: error.message});
  }
});

/**
 * Send message to multiple recipients (multicast)
 * POST /api/chat/send-message-multicast
 */
router.post("/send-message-multicast", async (req, res) => {
  const {
    senderId,
    senderName,
    recipientTokens,
    conversationId,
    content,
    messageType
  } = req.body;

  // Validate request body
  if (!senderId || !senderName || !recipientTokens || !Array.isArray(recipientTokens) || 
      !conversationId || !content) {
    return res.status(400).json({error: "Missing required fields or invalid recipientTokens array"});
  }

  // Validate FCM tokens
  const validTokens = recipientTokens.filter(token => token && token.length >= 10);
  if (validTokens.length === 0) {
    return res.status(400).json({error: "No valid FCM tokens provided"});
  }

  const messageId = uuidv4();
  const timestamp = new Date().toISOString();

  // Create FCM message payload
  const info = JSON.stringify({
    type: "chat_message",
    messageId,
    conversationId,
    senderId,
    senderName,
    content,
    messageType: messageType || "text",
    timestamp
  });

  const message = {
    data: {
      info,
    },
    notification: {
      title: senderName,
      body: content.length > 100 ? content.substring(0, 100) + "..." : content,
    },
    tokens: validTokens,
  };

  // Platform-specific configuration
  message.android = {
    priority: "high",
    notification: {
      channelId: "chat_messages",
      priority: "high",
      defaultSound: true,
      defaultVibrateTimings: true,
      clickAction: "FLUTTER_NOTIFICATION_CLICK",
      icon: "ic_notification",
      color: "#FF6B35"
    }
  };

  message.apns = {
    headers: {
      "apns-priority": "10",
    },
    payload: {
      aps: {
        badge: 1,
        sound: "default",
        "content-available": 1,
        category: "CHAT_MESSAGE"
      },
    },
  };

  try {
    // Send multicast message
    const response = await admin.messaging().sendEachForMulticast(message);
    
    console.log("[ChatAPI] Multicast FCM message sent:", {
      messageId,
      conversationId,
      senderId,
      successCount: response.successCount,
      failureCount: response.failureCount
    });

    res.status(200).json({
      messageId,
      success: true,
      data: {
        messageId,
        conversationId,
        timestamp,
        successCount: response.successCount,
        failureCount: response.failureCount,
        responses: response.responses
      }
    });
  } catch (error) {
    console.error("[ChatAPI] Multicast FCM Error:", error);
    res.status(500).json({error: error.message});
  }
});

/**
 * Health check endpoint
 * GET /api/chat/health
 */
router.get("/health", (req, res) => {
  res.status(200).json({
    status: "healthy",
    service: "chatApi",
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
