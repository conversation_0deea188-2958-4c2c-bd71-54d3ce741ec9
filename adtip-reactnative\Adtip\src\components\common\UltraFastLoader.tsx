/**
 * Ultra-Fast App Initialization Component
 * 
 * This component ensures instant app rendering while services initialize in the background.
 * It also determines the correct initial screen based on authentication state:
 * - Authenticated users see the home screen immediately
 * - Non-authenticated users see the onboarding screen
 * No loading screens, no blocking initialization - just immediate UI.
 */
import React, { useEffect, useState, useRef } from 'react';
import { View, StatusBar, BackHandler, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { safeAreaStyles, statusBarConfig } from '../../utils/SafeAreaUtils';
import { navigationRef, resetTo } from '../../navigation/NavigationService';
import Sidebar from '../sidebar/Sidebar';
import NavigationErrorBoundary from './NavigationErrorBoundary';
import { Logger } from '../../utils/ProductionLogger';

// Import navigation screens
import MainNavigator from '../../navigation/MainNavigator';
import AuthNavigator from '../../navigation/AuthNavigator';
import GuestNavigator from '../../navigation/GuestNavigator';
import { RootStackParamList } from '../../types/navigation';

// State machine for navigation
import { useNavigationMachine } from '../../hooks/useNavigationMachine';

// ✅ ENHANCED LOADING
import AppLaunchLoader from './AppLaunchLoader';
import { useNavigationErrorHandler } from '../../hooks/useNavigationErrorHandler';

// Navigation persistence
import NavigationPersistenceService from '../../services/NavigationPersistenceService';

// Simplified deep linking
import SimplifiedDeepLinkService from '../../services/SimplifiedDeepLinkService';

// Navigation analytics
import NavigationAnalyticsService from '../../services/NavigationAnalyticsService';

interface UltraFastLoaderProps {
  onInitializationComplete?: () => void;
}

// Create the RootStack inside UltraFastLoader
const RootStack = createNativeStackNavigator<RootStackParamList>();

// ✅ ENHANCED LOADING SCREEN - App logo with pulsing animation
const InitialLoadingScreen = () => {
  const { colors } = useTheme();

  return (
    <View style={{
      flex: 1,
      backgroundColor: colors.background,
    }}>
      <AppLaunchLoader message="Initializing..." />
    </View>
  );
};

const UltraFastLoader: React.FC<UltraFastLoaderProps> = ({
  onInitializationComplete
}) => {
  const { colors, isDarkMode } = useTheme();
  const { exitGuestMode } = useAuth();

  // ✅ PERFORMANCE FIX: Use navigation state machine instead of complex boolean logic
  const {
    currentState,
    navigatorComponent,
    isInitialized,
    shouldShowSidebar,
  } = useNavigationMachine();

  // ✅ RELIABILITY FIX: Add navigation error handling
  useNavigationErrorHandler();

  // ✅ PERSISTENCE: Add navigation state persistence
  const [initialState, setInitialState] = useState<any>(undefined);
  const [isStateRestored, setIsStateRestored] = useState(false);

  const [isNavReady, setIsNavReady] = useState(false);
  const onInitializationCompleteRef = useRef<(() => void) | null>(null);

  // Store onInitializationComplete in ref to avoid effect re-runs
  onInitializationCompleteRef.current = onInitializationComplete || null;

  // Global back button handler
  useEffect(() => {
    const backAction = () => {
      // Only handle back button when navigation is ready and initialized
      if (!isNavReady || !isInitialized) {
        return false;
      }

      // Check if navigation can go back
      if (navigationRef.isReady() && navigationRef.canGoBack()) {
        // Let the default back action happen
        return false;
      }

      // Handle the case when there's no previous screen to go back to
      if (currentState === 'mainApp') {
        // For logged-in users: Show exit app alert
        Alert.alert(
          'Exit App',
          'Do you want to exit the app?',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Exit', style: 'destructive', onPress: () => BackHandler.exitApp() }
          ]
        );
        return true; // Prevent default back action
      } else if (currentState === 'guestApp') {
        // For guest users: Exit guest mode and navigate back to onboarding screens
        try {
          Logger.debug('UltraFastLoader', 'Guest mode back button - exiting guest mode and returning to onboarding');
          exitGuestMode().then(() => {
            resetTo('Auth');
          }).catch((error) => {
            Logger.error('UltraFastLoader', 'Error exiting guest mode:', error);
            // Still try to navigate to onboarding even if exitGuestMode fails
            resetTo('Auth');
          });
          return true; // Prevent default back action
        } catch (error) {
          Logger.error('UltraFastLoader', 'Error handling guest mode back button:', error);
          return false;
        }
      }

      // For other cases (like onboarding), allow default behavior
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
    return () => backHandler.remove();
  }, [isNavReady, isInitialized, currentState, exitGuestMode]);

  // ✅ SIMPLIFIED: Log state changes using state machine
  useEffect(() => {
    Logger.debug('UltraFastLoader', 'Navigation state changed:', {
      currentState,
      navigatorComponent,
      isInitialized
    });
  }, [currentState, navigatorComponent, isInitialized]);

  // ✅ PERSISTENCE: Restore navigation state on app start
  useEffect(() => {
    const restoreState = async () => {
      try {
        if (NavigationPersistenceService.shouldRestoreState()) {
          const restoredState = await NavigationPersistenceService.restoreNavigationState();
          if (restoredState) {
            setInitialState(restoredState);
            Logger.debug('UltraFastLoader', 'Navigation state restored from persistence');
          }
        }
      } catch (error) {
        Logger.error('UltraFastLoader', 'Failed to restore navigation state:', error);
      } finally {
        setIsStateRestored(true);
      }
    };

    restoreState();
  }, []);

  // ✅ DEEP LINKING: Initialize simplified deep link service
  useEffect(() => {
    const cleanup = SimplifiedDeepLinkService.initialize();
    return cleanup;
  }, []);

  // ✅ ANALYTICS: Initialize navigation analytics
  useEffect(() => {
    const analytics = NavigationAnalyticsService.getInstance();
    analytics.loadPersistedEvents();
    analytics.resetSession();

    return () => {
      analytics.persistEvents();
    };
  }, []);

  // ✅ DEEP LINKING: Process pending links when navigation is ready
  useEffect(() => {
    if (isNavReady) {
      SimplifiedDeepLinkService.processPendingLink();
    }
  }, [isNavReady]);

  // ✅ SIMPLIFIED: Initialization handled by state machine
  useEffect(() => {
    // Notify completion if callback provided
    if (isInitialized && onInitializationCompleteRef.current) {
      onInitializationCompleteRef.current();
    }
  }, [isInitialized]);

  /*
  // Handle active call navigation
  useEffect(() => {
    console.log('[UltraFastLoader] activeCall changed:', activeCall);
    if (!isNavReady || !activeCall || !navigationRef.isReady()) return;
    
    // CRITICAL FIX: Check if call is in an ending state to prevent navigation back to MeetingScreen
    const isCallEnding = activeCall.status === 'ended';
    const isCallCleanup = activeCall.status === 'cleanup_pending';
    const isCallIdle = activeCall.status === 'idle';
    
    if (isCallEnding || isCallCleanup || isCallIdle) {
      console.log('[UltraFastLoader] Call is ending/ended, skipping navigation to avoid redirect back to MeetingScreen. Status:', activeCall.status);
      return;
    }
    
    // Only navigate if the call is in a state that requires the meeting screen
    const shouldNavigate = activeCall.status === 'connected' || 
                           activeCall.status === 'connecting' ||
                           (activeCall.status === 'ringing' && !activeCall.isInitiator) ||
                           (activeCall.status === 'dialing' && activeCall.isInitiator);

    if (shouldNavigate) {
      console.log('[UltraFastLoader] Active call detected, navigating to Meeting screen. Status:', activeCall.status);
      
      if (activeCall.meetingId && activeCall.token) {
        const navigationParams = {
          meetingId: activeCall.meetingId,
          token: activeCall.token,
          callType: activeCall.callType || 'voice',
          displayName: activeCall.isInitiator ? activeCall.recipientName : activeCall.callerName,
          recipientName: activeCall.recipientName || 'Participant',
          isInitiator: activeCall.isInitiator || false,
        };
        
        // Use a timeout to ensure the navigation container is fully ready
        setTimeout(() => {
          if (navigationRef.isReady()) {
            // Check current route to avoid redundant navigation
            const currentRoute = navigationRef.getCurrentRoute();
            if (currentRoute?.name !== 'Meeting') {
              console.log('[UltraFastLoader] Navigating to Meeting screen');
              (navigationRef as any).navigate('Main', {
                screen: 'Meeting',
                params: navigationParams,
              });
            } else {
              console.log('[UltraFastLoader] Already on Meeting screen, skipping navigation');
            }
          }
        }, 150);
      }
    }
  }, [activeCall, isNavReady]);
  */

  // ✅ REMOVED: Fallback logic handled by state machine

  // --- START: Replace the entire return logic with this ---
  return (
    <SafeAreaView
      style={[
        safeAreaStyles.container,
        { backgroundColor: colors.background }
      ]}
      edges={safeAreaStyles.container.edges}
    >
      <StatusBar
        {...(isDarkMode ? statusBarConfig.dark : statusBarConfig.light)}
      />

      <NavigationErrorBoundary>
        <NavigationContainer
          ref={navigationRef}
          initialState={initialState}
          onStateChange={(state) => {
            // Save navigation state for persistence
            if (state && isStateRestored) {
              NavigationPersistenceService.saveNavigationState(state);
            }

            // Track navigation analytics
            if (state) {
              const currentRoute = state.routes[state.index];
              if (currentRoute) {
                NavigationAnalyticsService.getInstance().trackScreenView(
                  currentRoute.name,
                  currentRoute.params
                );
              }
            }
          }}
          onReady={() => {
            Logger.info('UltraFastLoader', '✅ Navigation is ready');
            setIsNavReady(true);
          }}
          fallback={<InitialLoadingScreen />}
        >
        <RootStack.Navigator screenOptions={{ headerShown: false }}>
          {/* ✅ PERFORMANCE FIX: Simplified rendering using state machine */}
          {navigatorComponent === 'InitialLoading' && (
            <>
              {Logger.debug('UltraFastLoader', '🔄 Showing InitialLoading screen')}
              <RootStack.Screen name="InitialLoading" component={InitialLoadingScreen} />
            </>
          )}
          {navigatorComponent === 'Main' && (
            <>
              {Logger.debug('UltraFastLoader', '✅ Rendering MainNavigator for authenticated user')}
              <RootStack.Screen name="Main" component={MainNavigator} />
            </>
          )}
          {navigatorComponent === 'Guest' && (
            <>
              {Logger.debug('UltraFastLoader', '👤 Rendering GuestNavigator for guest user')}
              <RootStack.Screen name="Guest" component={GuestNavigator} />
            </>
          )}
          {navigatorComponent === 'Auth' && (
            <>
              {Logger.debug('UltraFastLoader', '🆕 Rendering AuthNavigator')}
              <RootStack.Screen name="Auth" component={AuthNavigator} />
            </>
          )}
        </RootStack.Navigator>
        
        {/* ✅ SIMPLIFIED: Show sidebar using state machine */}
        {shouldShowSidebar && <Sidebar />}
        </NavigationContainer>
      </NavigationErrorBoundary>
    </SafeAreaView>
  );
  // --- END: Replacement ---
};

export default UltraFastLoader;
