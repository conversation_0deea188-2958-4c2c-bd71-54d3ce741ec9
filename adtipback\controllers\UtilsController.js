const utils = require("../utils/fcm");

module.exports = {
  sendNotification: async (req, res, next) => {
    try {
      let result = await utils.sendNotification(req.body);
      res.status(200).send({
        status: 200,
        message: "Notification sent successfully.",
        data: [],
      });
    } catch (err) {
      res.status(500).send({
        status: 500,
        message: `Notification not sent ${err.data}`,
        data: [],
      });
    }
  },
  sendNotificationToAll: async (req, res, next) => {
    try {
      let result = await utils.sendNotificationToAll(req.body);
      res.status(200).send({
        status: 200,
        message: "Notification sent successfully.",
        data: [],
      });
    } catch (err) {
      res.status(500).send({
        status: 500,
        message: `Notification not sent ${err.data}`,
        data: [],
      });
    }
  },
};
