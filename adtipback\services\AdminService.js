let dbQuery = require("../dbConfig/queryRunner");
let usersService = require("./UsersService");

const jwt = require("jsonwebtoken");

let getAllIndustryList = () =>
  new Promise((resolve, reject) => {
    let sql = `select * from industry_master where is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch industry successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Industry list not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let deleteAdmin = (adminData) =>
  new Promise((resolve, reject) => {
    let sql = `update users set is_active=0 where id=${adminData.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Admin deleted successfully.",
            data: [],
          });
        } else {
          reject({
            status: 400,
            message: "Admin not update.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate MobileNumber not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [adminData],
        });
      });
  });

let updateAdmin = (adminData) =>
  new Promise((resolve, reject) => {
    let sql = `update users set `;
    if (adminData.firstName) sql += `firstName='${adminData.firstName}',`;
    if (adminData.lastName) sql += ` lastName='${adminData.lastName}',`;
    if (adminData.emailId) sql += ` emailId='${adminData.emailId}',`;
    if (adminData.accessId) sql += ` access_type='${adminData.accessId}',`;
    if (adminData.mobileNumber)
      sql += ` mobile_number='${adminData.mobileNumber}',`;
    if (adminData.onlineStatus)
      sql += ` online_status=${adminData.onlineStatus},`;
    if (sql !== "") sql = sql.substring(0, sql.length - 1);
    sql += ` where id=${adminData.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Update admin successfully.",
            data: [adminData],
          });
        } else {
          reject({
            status: 400,
            message: "Admin not update.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate MobileNumber not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid accessId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [adminData],
        });
      });
  });

let getAllAdminList = (createdby) =>
  new Promise((resolve, reject) => {
    let sql = `select a.id ,a.firstName,a.lastName,a.emailId,a.mobile_number as mobileNumber,acm.access_type as accessType,a.online_status as onlineStatus from users a
    JOIN admin_access_master acm ON a.access_type=acm.id
    where a.is_active=1 and acm.is_active=1 and a.createdby=${createdby};`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch admin successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Admin list not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let saveAdmin = (adminData) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO users (firstName, lastName, emailId,access_type,mobile_number, createdby,is_active,user_type,isOtpVerified,isSaveUserDetails) VALUES('${adminData.firstName}', '${adminData.lastName}', '${adminData.emailId}',${adminData.accessId},'${adminData.mobileNumber}',${adminData.createdBy},1,${adminData.accessId},0,0)`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          adminData.id = result.insertId;
          resolve({
            status: 200,
            message: "Save admin successfully.",
            data: [adminData],
          });
        } else {
          reject({
            status: 400,
            message: "Admin not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Duplicate MobileNumber not allowed.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid accessId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [adminData],
        });
      });
  });

let getAdminAccessList = () =>
  new Promise((resolve, reject) => {
    let sql = `select id,access_type as accessType, is_active as isActive from admin_access_master where is_active=${1} and is_admin=1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch admin access successfully.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Admin access list not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

/**
 * Get wallet withdrawal requests (with filtering, pagination)
 * @param {Object} filters - { page, limit, status, userId, fromDate, toDate, premium }
 * @returns {Promise<{count: number, withdrawals: Array}>}
 */
exports.getWalletWithdrawals = async (filters) => {
  const page = parseInt(filters.page, 10) || 0;
  const limit = parseInt(filters.limit, 10) || 20;
  const offset = page * limit;
  let where = 'WHERE 1=1';
  const params = [];

  if (filters.status) {
    where += ' AND w.status = ?';
    params.push(filters.status);
  }
  if (filters.userId) {
    where += ' AND w.user_id = ?';
    params.push(filters.userId);
  }
  if (filters.fromDate) {
    where += ' AND w.created_at >= ?';
    params.push(filters.fromDate);
  }
  if (filters.toDate) {
    where += ' AND w.created_at <= ?';
    params.push(filters.toDate);
  }
  // Join with users and premium tables for user info and premium status
  let sql = `SELECT w.*, u.name, u.emailId, u.mobile_number, p.is_premium
    FROM wallet_withdrawals w
    JOIN users u ON w.user_id = u.id
    LEFT JOIN user_premium p ON p.user_id = u.id
    ${where}
    ORDER BY w.created_at DESC
    LIMIT ? OFFSET ?`;
  params.push(limit, offset);

  let sqlCount = `SELECT COUNT(*) as total FROM wallet_withdrawals w ${where}`;

  const [withdrawals, countResult] = await Promise.all([
    dbQuery.queryRunner(sql, params),
    dbQuery.queryRunner(sqlCount, params.slice(0, -2)),
  ]);

  return {
    count: countResult[0]?.total || 0,
    withdrawals,
  };
};

/**
 * Update wallet withdrawal request status (approve/reject/paid)
 */
exports.updateWalletWithdrawalStatus = async (id, status, adminUser, notes) => {
  // TODO: Implement status update, audit trail
  throw new Error('Not implemented yet');
};

/**
 * Export wallet withdrawal requests (CSV/Excel)
 */
exports.exportWalletWithdrawals = async (filters) => {
  // TODO: Implement export logic
  throw new Error('Not implemented yet');
};

/**
 * Get referral withdrawal requests
 */
exports.getReferralWithdrawals = async (filters) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

/**
 * Update referral withdrawal request status
 */
exports.updateReferralWithdrawalStatus = async (id, status, adminUser, notes) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

/**
 * Export referral withdrawal requests
 */
exports.exportReferralWithdrawals = async (filters) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

/**
 * Get coupon withdrawal requests
 */
exports.getCouponWithdrawals = async (filters) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

/**
 * Update coupon withdrawal request status
 */
exports.updateCouponWithdrawalStatus = async (id, status, adminUser, notes) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

/**
 * Export coupon withdrawal requests
 */
exports.exportCouponWithdrawals = async (filters) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

/**
 * Get channel withdrawal requests
 */
exports.getChannelWithdrawals = async (filters) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

/**
 * Update channel withdrawal request status
 */
exports.updateChannelWithdrawalStatus = async (id, status, adminUser, notes) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

/**
 * Export channel withdrawal requests
 */
exports.exportChannelWithdrawals = async (filters) => {
  // TODO: Implement
  throw new Error('Not implemented yet');
};

module.exports = {
  getAdminAccessList: () =>
    new Promise((resolve, reject) => {
      return getAdminAccessList()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveAdmin: (adminData) =>
    new Promise((resolve, reject) => {
      return saveAdmin(adminData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getAllAdminList: (createdby) =>
    new Promise((resolve, reject) => {
      return getAllAdminList(createdby)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  updateAdmin: (adminData) =>
    new Promise((resolve, reject) => {
      return getUser(adminData.id)
        .then((result) => {
          if (result && result.status == 200) {
            return updateAdmin(adminData);
          } else {
            reject(result);
          }
        })
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  deleteAdmin: (adminData) =>
    new Promise((resolve, reject) => {
      return usersService
        .getUser(adminData.id)
        .then((result) => {
          if (result && result.status == 200) {
            return deleteAdmin(adminData);
          } else {
            reject(result);
          }
        })
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getAllIndustryList: () =>
    new Promise((resolve, reject) => {
      return getAllIndustryList()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getUserByName: (user) =>
    new Promise((resolve, reject) => {
      if (!user.user_name) {
        return res
          .status(200)
          .send({ error: true, message: "Please provide user_id" });
      }
      if (!user.password) {
        return res
          .status(200)
          .send({ error: true, message: "Please provide password" });
      }

      let sql = `SELECT * FROM application_users where user_email='${user.user_name}' OR mob_no='${user.user_name}'`;
      dbQuery.queryRunner(sql).then((result) => {
        console.log(result[0]);
        if (result[0] && result[0].user_password == user.password) {
          const token = jwt.sign(
            { user_id: result[0].id, email: result[0].user_email },
            process.env.JWT_KEY,
            { expiresIn: "365d" }
          );
          resolve({
            status: 200,
            error: false,
            user: result,
            message: "User Login Successfully",
            accessToken: token,
          });
        } else {
          return resolve({ error: true, message: "Invalid user password" });
        }
      });
    }),
  getAllusers: (page, limit) =>
    new Promise((resolve, reject) => {
      let offset = page * limit;
      let sql = `SELECT * from users limit ${limit} OFFSET ${offset}`;
      let sqlCount = `SELECT count(*) as total_users from users`;

      Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sqlCount)])
        .then((result) => {
          let resultdata = {};
          resultdata.counts = result[1][0].total_users;
          resultdata.users = result[0];
          resolve(resultdata);
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getAllChannels: (page, limit) =>
    new Promise((resolve, reject) => {
      let offset = page * limit;
      //let sql = `SELECT * from channels limit ${limit} OFFSET ${offset}`;
      //let sql = `SELECT c.*, u.* FROM channels c INNER JOIN users u ON c.createdby = u.id LIMIT ${limit} OFFSET ${offset};`;
      let sql = `
  SELECT 
      c.id AS channel_id,
      c.name AS channel_name,
      c.total_subscribers,
      c.total_videos,
      c.total_shorts,
      c.createdby,
      u.mobile_number
  FROM 
      (SELECT DISTINCT id, name, total_subscribers, total_videos, total_shorts, createdby FROM channels LIMIT ${limit} OFFSET ${offset}) c
  INNER JOIN 
      users u ON c.createdby = u.id;
`;

      let sqlCount = `SELECT count(*) as total_channels from channels`;

      Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sqlCount)])
        .then((result) => {
          let resultdata = {};
          resultdata.counts = result[1][0].total_channels;
          resultdata.channels = result[0];
          resolve(resultdata);
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  updateVerifyUser: (userData) =>
    new Promise((resolve, reject) => {
      let sql = `Update users set admin_verified='${userData.verify_status}' WHERE id=${userData.id}`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          resolve(result);
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  addUser: (userData) =>
    new Promise((resolve, reject) => {
      let sql1 = `SELECT * FROM application_users where mob_no='${userData.mobNo}' OR user_email='${userData.email}'`;
      dbQuery
        .queryRunner(sql1)
        .then((result) => {
          if (result[0]) {
            resolve({
              status: 200,
              error: true,
              user: result,
              message: "Mobile Number/Email Id already Registered",
            });
          } else {
            let sql = `INSERT INTO application_users(mob_no,user_email,first_name,last_name,user_role,user_access, user_password) VALUES ('${userData.mobNo}','${userData.email}','${userData.username}','','admin','All','${userData.password}')`;
            dbQuery
              .queryRunner(sql)
              .then((result) => {
                resolve({
                  status: 200,
                  error: false,
                  message: "User Registered Successfully",
                });
              })
              .catch((err) => {
                reject({
                  status: 500,
                  message: err,
                  data: [],
                });
              });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getAllAdminUsers: (userId) =>
    new Promise((resolve, reject) => {
      let sql = `SELECT * from application_users WHERE id !=${userId}`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          resolve(result);
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getAllwithdrawRequest: (userId) =>
    new Promise((resolve, reject) => {
      let sql = `SELECT * from wallet WHERE withdraw_req_amount='Unpaid' `;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          resolve(result);
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getAllwithdrawRequest: (page, limit) =>
    new Promise((resolve, reject) => {
      let offset = page * limit;
      let sql = `SELECT w.id as request_id,u.name,u.emailId,u.dob,u.profession,w.withdraw_req_amount,w.transaction_method
        ,w.upi_id,w.mobileNumber,w.bankName,w.accountNumber,w.IFSC,w.createdby,w.transaction_status,w.transaction_type from wallet w 
        join users u 
        on w.createdby=u.id 
        WHERE paid_status='Unpaid' order by createdDate desc 
        limit ${limit} OFFSET ${offset} `;

      let sqlCount = `SELECT count(*) as total_users_request from wallet WHERE paid_status='Unpaid'`;

      Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sqlCount)])
        .then((result) => {
          let resultdata = {};
          resultdata.counts = result[1][0].total_users_request;
          resultdata.usersWithdrawRequest = result[0];
          resolve(resultdata);
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getAllQrWebUniqueCompanyId: () =>
    new Promise((resolve, reject) => {
      let sql = `SELECT DISTINCT company_name, company_id FROM qr_scan_web;`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: " found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getReferalUnpaid: (page) => {
    let offset = page * 20;
    return new Promise((resolve, reject) => {
      let sql = `SELECT * from referal_withdraw_request where status="Unpaid" order by time limit 20 offset ${offset}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "data found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: e,
            data: [],
          });
        });
    });
  },
  changeUnpaidToPaidReferal: (id) => {
    return new Promise((resolve, reject) => {
      let sql = `UPDATE  referal_withdraw_request set status="Paid" , paid_time=NOW() where id=${id}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "data found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: e,
            data: [],
          });
        });
    });
  },
  getReferalPaid: (page) => {
    let offset = page * 20;
    return new Promise((resolve, reject) => {
      let sql = `SELECT * from referal_withdraw_request where status="Paid" order by time limit 20 offset ${offset}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "data found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: e,
            data: [],
          });
        });
    });
  },
  getChannelUnpaid: (page) => {
    let offset = page * 20;
    return new Promise((resolve, reject) => {
      let sql = `SELECT * from channel_withdraw_request where status="Unpaid" order by created_time limit 20 offset ${offset}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "data found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: e,
            data: [],
          });
        });
    });
  },
  changeUnpaidToPaidChannel: (id) => {
    return new Promise((resolve, reject) => {
      let sql = `UPDATE  channel_withdraw_request set status="Paid" , updated_time=NOW() where id=${id}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "data found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: e,
            data: [],
          });
        });
    });
  },
  getChannelPaid: (page) => {
    let offset = page * 20;
    return new Promise((resolve, reject) => {
      let sql = `SELECT * from channel_withdraw_request where status="Paid" order by created_time limit 20 offset ${offset}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "data found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    });
  },
  getQrUnpaidDataByCompanyId: (page, limit, id) => {
    let offset = page * limit;
    return new Promise((resolve, reject) => {
      let sql = `SELECT * from qr_scan_web where company_id=${id} and paid=0 order by created_date desc 
            limit ${limit} OFFSET ${offset}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "data found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: e,
            data: [],
          });
        });
    });
  },
  getQrPaidDataByCompanyId: (page, limit, id) => {
    let offset = page * limit;
    return new Promise((resolve, reject) => {
      let sql = `SELECT * from qr_scan_web where company_id=${id} and paid=1 order by created_date desc 
            limit ${limit} OFFSET ${offset}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Found",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: e,
            data: [],
          });
        });
    });
  },
  changeQrWebUnpaidToPaid: (id) => {
    return new Promise((resolve, reject) => {
      let sql = `UPDATE qr_scan_web set paid=1 where id=${id}`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Updated successfully",
              data: result,
            });
          } else {
            reject({
              status: 500,
              message: "Something went wrong.",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    });
  },
  getDemoList: (page, limit) => {
    let offset = page * limit;
    return new Promise((resolve, reject) => {
      let sql = `SELECT * from website_message order by createdDate desc 
            limit ${limit} OFFSET ${offset}`;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Found",
              data: result,
            });
          } else {
            resolve({
              status: 200,
              message: "Not found",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: e,
            data: [],
          });
        });
    });
  },
  getAdOrder: (page, limit) =>
    new Promise((resolve, reject) => {
      let offset = page * limit;
      let sql = `SELECT * from admodels order by createddate desc limit ${limit} OFFSET ${offset}`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "ad models founds",
              data: result,
            });
          } else {
            resolve({
              status: 200,
              message: "Empty ad models",
              data: [],
            });
          }
        })
        .catch((e) => {
          reject({
            status: 500,
            message: "Something went wrong",
            data: [],
          });
        });
    }),

  getAllVideos: (page) => {
    var offset = page * 20;
    return new Promise((resolve, reject) => {
      let sql = `
        SELECT r.id,r.name,r.video_Thumbnail, u.mobile_number
        FROM reels r 
        LEFT JOIN users u 
        ON u.id = r.createdby 
        ORDER BY r.createddate DESC 
        LIMIT 20 
        OFFSET ${offset}
      `;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Fetch videos successfully",
              data: result,
            });
          } else {
            resolve({
              status: 200,
              message: "no videos found",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    });
  },
  deleteVideoById: (videoId) => {
    return new Promise((resolve, reject) => {
      let sql = `DELETE from reels where id=${videoId}`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            reject({
              status: 200,
              message: "video deleted successfully",
              data: [],
            });
          } else {
            reject({
              status: 500,
              message: "unable to delete",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    });
  },
  getVideoBySearch: (searchTerm) => {
    return new Promise((resolve, reject) => {
      let sql = `SELECT r.id,r.name,r.video_Thumbnail, u.mobile_number
      FROM reels r 
      LEFT JOIN users u 
      ON u.id = r.createdby 
WHERE r.name LIKE '%${searchTerm}%' OR r.video_desciption LIKE '%${searchTerm}%'
      ORDER BY r.createddate DESC;
    `;

      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Fetch videos successfully",
              data: result,
            });
          } else {
            resolve({
              status: 200,
              message: "No videos found",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    });
  },
  ////ecommerce
  getAllOrders: (page) =>
    new Promise((resolve, reject) => {
      let offset = page * 20;
      let sql = `
        SELECT * from orders_products
        order by created_date desc
        LIMIT 20 
        OFFSET ${offset}
      `;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            let formattedResult = result.map((order) => {
              return {
                ...order,
                product_image: order.product_image
                  ? order.product_image.split(",")
                  : [],
              };
            });
            resolve({
              status: 200,
              message: "Fetch orders successfully",
              data: formattedResult,
            });
          } else {
            resolve({
              status: 200,
              message: "no orders found",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  verifyCompany: (id) =>
    new Promise((resolve, reject) => {
      let sql = `UPDATE company set is_verified=1 where id=${id}`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "updated company successfully",
              data: result,
            });
          } else {
            resolve({
              status: 200,
              message: "unable to update company",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),
  getAllProducts: (page) =>
    new Promise((resolve, reject) => {
      let offset = page * 20;
      let sql = `
        SELECT * from product
        order by createddate desc
        LIMIT 20 
        OFFSET ${offset}
      `;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            let formattedResult = result.map((product) => {
              return {
                ...product,
                images: product.images ? product.images.split(",") : [],
              };
            });
            resolve({
              status: 200,
              message: "Fetch products successfully",
              data: formattedResult,
            });
          } else {
            resolve({
              status: 200,
              message: "no products found",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }),

  // ==================== WITHDRAWAL MANAGEMENT METHODS ====================

  /**
   * Get wallet withdrawal requests with filtering and pagination
   * @param {Object} filters - Filter parameters
   * @returns {Promise} Promise with withdrawal data
   */
  getWalletWithdrawals: (filters) =>
    new Promise((resolve, reject) => {
      let page = parseInt(filters.page) || 1;
      let limit = parseInt(filters.limit) || 20;
      let offset = (page - 1) * limit;
      
      let whereConditions = ["ww.is_active = 1"];
      let params = [];

      // Add filters
      if (filters.status) {
        whereConditions.push("ww.status = ?");
        params.push(filters.status);
      }
      if (filters.userId) {
        whereConditions.push("ww.user_id = ?");
        params.push(filters.userId);
      }
      if (filters.premium !== undefined) {
        whereConditions.push("u.is_premium = ?");
        params.push(filters.premium ? 1 : 0);
      }
      if (filters.fromDate) {
        whereConditions.push("DATE(ww.created_at) >= ?");
        params.push(filters.fromDate);
      }
      if (filters.toDate) {
        whereConditions.push("DATE(ww.created_at) <= ?");
        params.push(filters.toDate);
      }

      let whereClause = whereConditions.join(" AND ");
      
      let sql = `
        SELECT 
          ww.id,
          ww.user_id,
          ww.amount,
          ww.status,
          ww.payment_method,
          ww.account_details,
          ww.admin_notes,
          ww.created_at,
          ww.updated_at,
          u.firstName,
          u.lastName,
          u.mobile_number,
          u.emailId,
          u.is_premium,
          u.profile_pic
        FROM wallet_withdrawals ww
        LEFT JOIN users u ON ww.user_id = u.id
        WHERE ${whereClause}
        ORDER BY ww.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      params.push(limit, offset);

      dbQuery
        .queryRunner(sql, params)
        .then((result) => {
          // Get total count for pagination
          let countSql = `
            SELECT COUNT(*) as total
            FROM wallet_withdrawals ww
            LEFT JOIN users u ON ww.user_id = u.id
            WHERE ${whereClause}
          `;
          
          return dbQuery.queryRunner(countSql, params.slice(0, -2));
        })
        .then((countResult) => {
          const total = countResult[0]?.total || 0;
          const totalPages = Math.ceil(total / limit);
          
          resolve({
            status: 200,
            message: "Wallet withdrawals fetched successfully",
            data: {
              withdrawals: result,
              pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit
              }
            }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error fetching wallet withdrawals",
            data: []
          });
        });
    }),

  /**
   * Update wallet withdrawal status
   * @param {number} withdrawalId - Withdrawal ID
   * @param {string} status - New status (approved/rejected/paid)
   * @param {string} adminNotes - Admin notes
   * @param {number} adminId - Admin user ID
   * @returns {Promise} Promise with update result
   */
  updateWalletWithdrawalStatus: (withdrawalId, status, adminNotes, adminId) =>
    new Promise((resolve, reject) => {
      let sql = `
        UPDATE wallet_withdrawals 
        SET status = ?, admin_notes = ?, updated_at = NOW()
        WHERE id = ? AND is_active = 1
      `;
      
      dbQuery
        .queryRunner(sql, [status, adminNotes, withdrawalId])
        .then((result) => {
          if (result.affectedRows > 0) {
            // Log the action
            let auditSql = `
              INSERT INTO withdrawal_audit_log 
              (withdrawal_id, withdrawal_type, old_status, new_status, admin_id, notes, created_at)
              VALUES (?, 'wallet', (SELECT status FROM wallet_withdrawals WHERE id = ?), ?, ?, ?, NOW())
            `;
            return dbQuery.queryRunner(auditSql, [withdrawalId, withdrawalId, status, adminId, adminNotes]);
          } else {
            throw new Error("Withdrawal not found or already processed");
          }
        })
        .then(() => {
          resolve({
            status: 200,
            message: "Withdrawal status updated successfully",
            data: { withdrawalId, status }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error updating withdrawal status",
            data: []
          });
        });
    }),

  /**
   * Export wallet withdrawals to CSV
   * @param {Object} filters - Filter parameters
   * @returns {Promise} Promise with CSV data
   */
  exportWalletWithdrawals: (filters) =>
    new Promise((resolve, reject) => {
      let whereConditions = ["ww.is_active = 1"];
      let params = [];

      // Add filters
      if (filters.status) {
        whereConditions.push("ww.status = ?");
        params.push(filters.status);
      }
      if (filters.fromDate) {
        whereConditions.push("DATE(ww.created_at) >= ?");
        params.push(filters.fromDate);
      }
      if (filters.toDate) {
        whereConditions.push("DATE(ww.created_at) <= ?");
        params.push(filters.toDate);
      }

      let whereClause = whereConditions.join(" AND ");
      
      let sql = `
        SELECT 
          ww.id,
          ww.user_id,
          ww.amount,
          ww.status,
          ww.payment_method,
          ww.account_details,
          ww.admin_notes,
          ww.created_at,
          u.firstName,
          u.lastName,
          u.mobile_number,
          u.emailId,
          u.is_premium
        FROM wallet_withdrawals ww
        LEFT JOIN users u ON ww.user_id = u.id
        WHERE ${whereClause}
        ORDER BY ww.created_at DESC
      `;

      dbQuery
        .queryRunner(sql, params)
        .then((result) => {
          // Convert to CSV format
          let csvData = "ID,User ID,Amount,Status,Payment Method,Account Details,Admin Notes,Created At,User Name,Mobile,Email,Premium\n";
          
          result.forEach(row => {
            csvData += `${row.id},${row.user_id},${row.amount},${row.status},${row.payment_method || ''},${row.account_details || ''},${row.admin_notes || ''},${row.created_at},${row.firstName} ${row.lastName},${row.mobile_number},${row.emailId},${row.is_premium ? 'Yes' : 'No'}\n`;
          });

          resolve({
            status: 200,
            message: "Wallet withdrawals exported successfully",
            data: {
              csvData,
              filename: `wallet_withdrawals_${new Date().toISOString().split('T')[0]}.csv`
            }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error exporting wallet withdrawals",
            data: []
          });
        });
    }),

  /**
   * Get referral withdrawal requests
   * @param {Object} filters - Filter parameters
   * @returns {Promise} Promise with withdrawal data
   */
  getReferralWithdrawals: (filters) =>
    new Promise((resolve, reject) => {
      let page = parseInt(filters.page) || 1;
      let limit = parseInt(filters.limit) || 20;
      let offset = (page - 1) * limit;
      
      let whereConditions = ["rw.is_active = 1"];
      let params = [];

      // Add filters
      if (filters.status) {
        whereConditions.push("rw.status = ?");
        params.push(filters.status);
      }
      if (filters.userId) {
        whereConditions.push("rw.user_id = ?");
        params.push(filters.userId);
      }
      if (filters.premium !== undefined) {
        whereConditions.push("u.is_premium = ?");
        params.push(filters.premium ? 1 : 0);
      }
      if (filters.fromDate) {
        whereConditions.push("DATE(rw.created_at) >= ?");
        params.push(filters.fromDate);
      }
      if (filters.toDate) {
        whereConditions.push("DATE(rw.created_at) <= ?");
        params.push(filters.toDate);
      }

      let whereClause = whereConditions.join(" AND ");
      
      let sql = `
        SELECT 
          rw.id,
          rw.user_id,
          rw.amount,
          rw.status,
          rw.payment_method,
          rw.account_details,
          rw.admin_notes,
          rw.created_at,
          rw.updated_at,
          u.firstName,
          u.lastName,
          u.mobile_number,
          u.emailId,
          u.is_premium,
          u.profile_pic
        FROM referral_withdrawals rw
        LEFT JOIN users u ON rw.user_id = u.id
        WHERE ${whereClause}
        ORDER BY rw.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      params.push(limit, offset);

      dbQuery
        .queryRunner(sql, params)
        .then((result) => {
          // Get total count for pagination
          let countSql = `
            SELECT COUNT(*) as total
            FROM referral_withdrawals rw
            LEFT JOIN users u ON rw.user_id = u.id
            WHERE ${whereClause}
          `;
          
          return dbQuery.queryRunner(countSql, params.slice(0, -2));
        })
        .then((countResult) => {
          const total = countResult[0]?.total || 0;
          const totalPages = Math.ceil(total / limit);
          
          resolve({
            status: 200,
            message: "Referral withdrawals fetched successfully",
            data: {
              withdrawals: result,
              pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit
              }
            }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error fetching referral withdrawals",
            data: []
          });
        });
    }),

  /**
   * Update referral withdrawal status
   * @param {number} withdrawalId - Withdrawal ID
   * @param {string} status - New status
   * @param {string} adminNotes - Admin notes
   * @param {number} adminId - Admin user ID
   * @returns {Promise} Promise with update result
   */
  updateReferralWithdrawalStatus: (withdrawalId, status, adminNotes, adminId) =>
    new Promise((resolve, reject) => {
      let sql = `
        UPDATE referral_withdrawals 
        SET status = ?, admin_notes = ?, updated_at = NOW()
        WHERE id = ? AND is_active = 1
      `;
      
      dbQuery
        .queryRunner(sql, [status, adminNotes, withdrawalId])
        .then((result) => {
          if (result.affectedRows > 0) {
            // Log the action
            let auditSql = `
              INSERT INTO withdrawal_audit_log 
              (withdrawal_id, withdrawal_type, old_status, new_status, admin_id, notes, created_at)
              VALUES (?, 'referral', (SELECT status FROM referral_withdrawals WHERE id = ?), ?, ?, ?, NOW())
            `;
            return dbQuery.queryRunner(auditSql, [withdrawalId, withdrawalId, status, adminId, adminNotes]);
          } else {
            throw new Error("Withdrawal not found or already processed");
          }
        })
        .then(() => {
          resolve({
            status: 200,
            message: "Referral withdrawal status updated successfully",
            data: { withdrawalId, status }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error updating referral withdrawal status",
            data: []
          });
        });
    }),

  /**
   * Export referral withdrawals to CSV
   * @param {Object} filters - Filter parameters
   * @returns {Promise} Promise with CSV data
   */
  exportReferralWithdrawals: (filters) =>
    new Promise((resolve, reject) => {
      let whereConditions = ["rw.is_active = 1"];
      let params = [];

      // Add filters
      if (filters.status) {
        whereConditions.push("rw.status = ?");
        params.push(filters.status);
      }
      if (filters.fromDate) {
        whereConditions.push("DATE(rw.created_at) >= ?");
        params.push(filters.fromDate);
      }
      if (filters.toDate) {
        whereConditions.push("DATE(rw.created_at) <= ?");
        params.push(filters.toDate);
      }

      let whereClause = whereConditions.join(" AND ");
      
      let sql = `
        SELECT 
          rw.id,
          rw.user_id,
          rw.amount,
          rw.status,
          rw.payment_method,
          rw.account_details,
          rw.admin_notes,
          rw.created_at,
          u.firstName,
          u.lastName,
          u.mobile_number,
          u.emailId,
          u.is_premium
        FROM referral_withdrawals rw
        LEFT JOIN users u ON rw.user_id = u.id
        WHERE ${whereClause}
        ORDER BY rw.created_at DESC
      `;

      dbQuery
        .queryRunner(sql, params)
        .then((result) => {
          // Convert to CSV format
          let csvData = "ID,User ID,Amount,Status,Payment Method,Account Details,Admin Notes,Created At,User Name,Mobile,Email,Premium\n";
          
          result.forEach(row => {
            csvData += `${row.id},${row.user_id},${row.amount},${row.status},${row.payment_method || ''},${row.account_details || ''},${row.admin_notes || ''},${row.created_at},${row.firstName} ${row.lastName},${row.mobile_number},${row.emailId},${row.is_premium ? 'Yes' : 'No'}\n`;
          });

          resolve({
            status: 200,
            message: "Referral withdrawals exported successfully",
            data: {
              csvData,
              filename: `referral_withdrawals_${new Date().toISOString().split('T')[0]}.csv`
            }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error exporting referral withdrawals",
            data: []
          });
        });
    }),

  /**
   * Get coupon withdrawal requests
   * @param {Object} filters - Filter parameters
   * @returns {Promise} Promise with withdrawal data
   */
  getCouponWithdrawals: (filters) =>
    new Promise((resolve, reject) => {
      let page = parseInt(filters.page) || 1;
      let limit = parseInt(filters.limit) || 20;
      let offset = (page - 1) * limit;
      
      let whereConditions = ["cw.is_active = 1"];
      let params = [];

      // Add filters
      if (filters.status) {
        whereConditions.push("cw.status = ?");
        params.push(filters.status);
      }
      if (filters.userId) {
        whereConditions.push("cw.user_id = ?");
        params.push(filters.userId);
      }
      if (filters.premium !== undefined) {
        whereConditions.push("u.is_premium = ?");
        params.push(filters.premium ? 1 : 0);
      }
      if (filters.fromDate) {
        whereConditions.push("DATE(cw.created_at) >= ?");
        params.push(filters.fromDate);
      }
      if (filters.toDate) {
        whereConditions.push("DATE(cw.created_at) <= ?");
        params.push(filters.toDate);
      }

      let whereClause = whereConditions.join(" AND ");
      
      let sql = `
        SELECT 
          cw.id,
          cw.user_id,
          cw.amount,
          cw.status,
          cw.payment_method,
          cw.account_details,
          cw.admin_notes,
          cw.created_at,
          cw.updated_at,
          u.firstName,
          u.lastName,
          u.mobile_number,
          u.emailId,
          u.is_premium,
          u.profile_pic
        FROM coupon_withdrawals cw
        LEFT JOIN users u ON cw.user_id = u.id
        WHERE ${whereClause}
        ORDER BY cw.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      params.push(limit, offset);

      dbQuery
        .queryRunner(sql, params)
        .then((result) => {
          // Get total count for pagination
          let countSql = `
            SELECT COUNT(*) as total
            FROM coupon_withdrawals cw
            LEFT JOIN users u ON cw.user_id = u.id
            WHERE ${whereClause}
          `;
          
          return dbQuery.queryRunner(countSql, params.slice(0, -2));
        })
        .then((countResult) => {
          const total = countResult[0]?.total || 0;
          const totalPages = Math.ceil(total / limit);
          
          resolve({
            status: 200,
            message: "Coupon withdrawals fetched successfully",
            data: {
              withdrawals: result,
              pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit
              }
            }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error fetching coupon withdrawals",
            data: []
          });
        });
    }),

  /**
   * Update coupon withdrawal status
   * @param {number} withdrawalId - Withdrawal ID
   * @param {string} status - New status
   * @param {string} adminNotes - Admin notes
   * @param {number} adminId - Admin user ID
   * @returns {Promise} Promise with update result
   */
  updateCouponWithdrawalStatus: (withdrawalId, status, adminNotes, adminId) =>
    new Promise((resolve, reject) => {
      let sql = `
        UPDATE coupon_withdrawals 
        SET status = ?, admin_notes = ?, updated_at = NOW()
        WHERE id = ? AND is_active = 1
      `;
      
      dbQuery
        .queryRunner(sql, [status, adminNotes, withdrawalId])
        .then((result) => {
          if (result.affectedRows > 0) {
            // Log the action
            let auditSql = `
              INSERT INTO withdrawal_audit_log 
              (withdrawal_id, withdrawal_type, old_status, new_status, admin_id, notes, created_at)
              VALUES (?, 'coupon', (SELECT status FROM coupon_withdrawals WHERE id = ?), ?, ?, ?, NOW())
            `;
            return dbQuery.queryRunner(auditSql, [withdrawalId, withdrawalId, status, adminId, adminNotes]);
          } else {
            throw new Error("Withdrawal not found or already processed");
          }
        })
        .then(() => {
          resolve({
            status: 200,
            message: "Coupon withdrawal status updated successfully",
            data: { withdrawalId, status }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error updating coupon withdrawal status",
            data: []
          });
        });
    }),

  /**
   * Export coupon withdrawals to CSV
   * @param {Object} filters - Filter parameters
   * @returns {Promise} Promise with CSV data
   */
  exportCouponWithdrawals: (filters) =>
    new Promise((resolve, reject) => {
      let whereConditions = ["cw.is_active = 1"];
      let params = [];

      // Add filters
      if (filters.status) {
        whereConditions.push("cw.status = ?");
        params.push(filters.status);
      }
      if (filters.fromDate) {
        whereConditions.push("DATE(cw.created_at) >= ?");
        params.push(filters.fromDate);
      }
      if (filters.toDate) {
        whereConditions.push("DATE(cw.created_at) <= ?");
        params.push(filters.toDate);
      }

      let whereClause = whereConditions.join(" AND ");
      
      let sql = `
        SELECT 
          cw.id,
          cw.user_id,
          cw.amount,
          cw.status,
          cw.payment_method,
          cw.account_details,
          cw.admin_notes,
          cw.created_at,
          u.firstName,
          u.lastName,
          u.mobile_number,
          u.emailId,
          u.is_premium
        FROM coupon_withdrawals cw
        LEFT JOIN users u ON cw.user_id = u.id
        WHERE ${whereClause}
        ORDER BY cw.created_at DESC
      `;

      dbQuery
        .queryRunner(sql, params)
        .then((result) => {
          // Convert to CSV format
          let csvData = "ID,User ID,Amount,Status,Payment Method,Account Details,Admin Notes,Created At,User Name,Mobile,Email,Premium\n";
          
          result.forEach(row => {
            csvData += `${row.id},${row.user_id},${row.amount},${row.status},${row.payment_method || ''},${row.account_details || ''},${row.admin_notes || ''},${row.created_at},${row.firstName} ${row.lastName},${row.mobile_number},${row.emailId},${row.is_premium ? 'Yes' : 'No'}\n`;
          });

          resolve({
            status: 200,
            message: "Coupon withdrawals exported successfully",
            data: {
              csvData,
              filename: `coupon_withdrawals_${new Date().toISOString().split('T')[0]}.csv`
            }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error exporting coupon withdrawals",
            data: []
          });
        });
    }),

  /**
   * Get channel withdrawal requests
   * @param {Object} filters - Filter parameters
   * @returns {Promise} Promise with withdrawal data
   */
  getChannelWithdrawals: (filters) =>
    new Promise((resolve, reject) => {
      let page = parseInt(filters.page) || 1;
      let limit = parseInt(filters.limit) || 20;
      let offset = (page - 1) * limit;
      
      let whereConditions = ["chw.is_active = 1"];
      let params = [];

      // Add filters
      if (filters.status) {
        whereConditions.push("chw.status = ?");
        params.push(filters.status);
      }
      if (filters.userId) {
        whereConditions.push("chw.user_id = ?");
        params.push(filters.userId);
      }
      if (filters.premium !== undefined) {
        whereConditions.push("u.is_premium = ?");
        params.push(filters.premium ? 1 : 0);
      }
      if (filters.fromDate) {
        whereConditions.push("DATE(chw.created_at) >= ?");
        params.push(filters.fromDate);
      }
      if (filters.toDate) {
        whereConditions.push("DATE(chw.created_at) <= ?");
        params.push(filters.toDate);
      }

      let whereClause = whereConditions.join(" AND ");
      
      let sql = `
        SELECT 
          chw.id,
          chw.user_id,
          chw.amount,
          chw.status,
          chw.payment_method,
          chw.account_details,
          chw.admin_notes,
          chw.created_at,
          chw.updated_at,
          u.firstName,
          u.lastName,
          u.mobile_number,
          u.emailId,
          u.is_premium,
          u.profile_pic
        FROM channel_withdrawals chw
        LEFT JOIN users u ON chw.user_id = u.id
        WHERE ${whereClause}
        ORDER BY chw.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      params.push(limit, offset);

      dbQuery
        .queryRunner(sql, params)
        .then((result) => {
          // Get total count for pagination
          let countSql = `
            SELECT COUNT(*) as total
            FROM channel_withdrawals chw
            LEFT JOIN users u ON chw.user_id = u.id
            WHERE ${whereClause}
          `;
          
          return dbQuery.queryRunner(countSql, params.slice(0, -2));
        })
        .then((countResult) => {
          const total = countResult[0]?.total || 0;
          const totalPages = Math.ceil(total / limit);
          
          resolve({
            status: 200,
            message: "Channel withdrawals fetched successfully",
            data: {
              withdrawals: result,
              pagination: {
                currentPage: page,
                totalPages,
                totalItems: total,
                itemsPerPage: limit
              }
            }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error fetching channel withdrawals",
            data: []
          });
        });
    }),

  /**
   * Update channel withdrawal status
   * @param {number} withdrawalId - Withdrawal ID
   * @param {string} status - New status
   * @param {string} adminNotes - Admin notes
   * @param {number} adminId - Admin user ID
   * @returns {Promise} Promise with update result
   */
  updateChannelWithdrawalStatus: (withdrawalId, status, adminNotes, adminId) =>
    new Promise((resolve, reject) => {
      let sql = `
        UPDATE channel_withdrawals 
        SET status = ?, admin_notes = ?, updated_at = NOW()
        WHERE id = ? AND is_active = 1
      `;
      
      dbQuery
        .queryRunner(sql, [status, adminNotes, withdrawalId])
        .then((result) => {
          if (result.affectedRows > 0) {
            // Log the action
            let auditSql = `
              INSERT INTO withdrawal_audit_log 
              (withdrawal_id, withdrawal_type, old_status, new_status, admin_id, notes, created_at)
              VALUES (?, 'channel', (SELECT status FROM channel_withdrawals WHERE id = ?), ?, ?, ?, NOW())
            `;
            return dbQuery.queryRunner(auditSql, [withdrawalId, withdrawalId, status, adminId, adminNotes]);
          } else {
            throw new Error("Withdrawal not found or already processed");
          }
        })
        .then(() => {
          resolve({
            status: 200,
            message: "Channel withdrawal status updated successfully",
            data: { withdrawalId, status }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error updating channel withdrawal status",
            data: []
          });
        });
    }),

  /**
   * Export channel withdrawals to CSV
   * @param {Object} filters - Filter parameters
   * @returns {Promise} Promise with CSV data
   */
  exportChannelWithdrawals: (filters) =>
    new Promise((resolve, reject) => {
      let whereConditions = ["chw.is_active = 1"];
      let params = [];

      // Add filters
      if (filters.status) {
        whereConditions.push("chw.status = ?");
        params.push(filters.status);
      }
      if (filters.fromDate) {
        whereConditions.push("DATE(chw.created_at) >= ?");
        params.push(filters.fromDate);
      }
      if (filters.toDate) {
        whereConditions.push("DATE(chw.created_at) <= ?");
        params.push(filters.toDate);
      }

      let whereClause = whereConditions.join(" AND ");
      
      let sql = `
        SELECT 
          chw.id,
          chw.user_id,
          chw.amount,
          chw.status,
          chw.payment_method,
          chw.account_details,
          chw.admin_notes,
          chw.created_at,
          u.firstName,
          u.lastName,
          u.mobile_number,
          u.emailId,
          u.is_premium
        FROM channel_withdrawals chw
        LEFT JOIN users u ON chw.user_id = u.id
        WHERE ${whereClause}
        ORDER BY chw.created_at DESC
      `;

      dbQuery
        .queryRunner(sql, params)
        .then((result) => {
          // Convert to CSV format
          let csvData = "ID,User ID,Amount,Status,Payment Method,Account Details,Admin Notes,Created At,User Name,Mobile,Email,Premium\n";
          
          result.forEach(row => {
            csvData += `${row.id},${row.user_id},${row.amount},${row.status},${row.payment_method || ''},${row.account_details || ''},${row.admin_notes || ''},${row.created_at},${row.firstName} ${row.lastName},${row.mobile_number},${row.emailId},${row.is_premium ? 'Yes' : 'No'}\n`;
          });

          resolve({
            status: 200,
            message: "Channel withdrawals exported successfully",
            data: {
              csvData,
              filename: `channel_withdrawals_${new Date().toISOString().split('T')[0]}.csv`
            }
          });
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err.message || "Error exporting channel withdrawals",
            data: []
          });
        });
    }),
};
