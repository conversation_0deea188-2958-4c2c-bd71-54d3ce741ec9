const { queryRunner } = require('../dbConfig/queryRunner');
const logger = require('../utils/logger');

async function cleanupOldMessages() {
  try {
    console.log('Starting cleanup of old chat messages...');
    
    const cleanupQuery = "DELETE FROM user_chat WHERE createddate < (NOW() - INTERVAL 7 DAY)";
    const result = await queryRunner(cleanupQuery);
    
    console.log(`✅ Cleanup completed: Deleted ${result.affectedRows || 0} old messages.`);
    logger.info(`Cleanup completed: Deleted ${result.affectedRows || 0} old messages.`);
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    logger.error('Cleanup failed:', error);
    throw error;
  }
}

// Run cleanup if called directly
if (require.main === module) {
  cleanupOldMessages()
    .then(() => {
      console.log('✅ Cleanup script completed successfully');
      process.exit(0);
    })
    .catch(err => {
      console.error('❌ Cleanup script failed:', err);
      process.exit(1);
    });
}

module.exports = { cleanupOldMessages }; 