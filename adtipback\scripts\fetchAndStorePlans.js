const razorpay = require("razorpay");
const { queryRunner } = require("../dbConfig/queryRunner");
require('dotenv').config({ path: '../.env' }); // Adjust path to .env if necessary

const razorpayInstance = new razorpay({
  key_id: process.env.RAZOR_PAY_KEY_ID,
  key_secret: process.env.RAZOR_PAY_KEY_SECRET,
});

// These should be the plan IDs you created in your Razorpay dashboard
const PLAN_IDS = [
    "plan_Qjrw31WPrhunxz", // 1 Month Plan
    "plan_Qjrx1d0dlYu8dI", // 6 Month Plan
    "plan_QjrxzWRWoYC5bl", // 12 Month Plan
];

async function fetchAndStorePlans() {
  console.log("Fetching plans from Razorpay...");
  try {
    for (const planId of PLAN_IDS) {
      try {
        console.log(`Fetching plan: ${planId}`);
        const plan = await razorpayInstance.plans.fetch(planId);
        
        console.log(`Storing plan: ${plan.id} - ${plan.item.name}`);

        const query = `
          INSERT INTO razorpay_plans (id, entity, interval_type, interval_count, item, notes, created_at_razorpay)
          VALUES (?, ?, ?, ?, ?, ?, FROM_UNIXTIME(?))
          ON DUPLICATE KEY UPDATE
            entity = VALUES(entity),
            interval_type = VALUES(interval_type),
            interval_count = VALUES(interval_count),
            item = VALUES(item),
            notes = VALUES(notes),
            updated_at = CURRENT_TIMESTAMP
        `;
        
        const params = [
          plan.id,
          plan.entity,
          plan.period,
          plan.interval,
          JSON.stringify(plan.item),
          JSON.stringify(plan.notes),
          plan.created_at,
        ];
        
        await queryRunner(query, params);
        console.log(`Successfully stored/updated plan: ${plan.id}`);
      } catch (error) {
        console.error(`Failed to fetch or store plan ${planId}:`, error);
      }
    }
    console.log("Finished fetching and storing plans.");
    process.exit(0);
  } catch (error) {
    console.error("An unexpected error occurred:", error);
    process.exit(1);
  }
}

fetchAndStorePlans(); 