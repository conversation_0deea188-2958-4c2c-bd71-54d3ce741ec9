const mysql = require('mysql2/promise');
const config = require('./config/appenvconfig.js');

async function verifyMigration() {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: config.database.host,
      user: config.database.user,
      password: config.database.password,
      database: config.database.database,
      port: parseInt(config.database.port) || 3306
    });
    
    console.log('🔍 VERIFYING CHAT DATABASE MIGRATION');
    console.log('=====================================');
    
    // Check new tables exist
    const [tables] = await connection.query(`
      SHOW TABLES LIKE 'conversations'
      UNION
      SHOW TABLES LIKE 'conversation_participants'
      UNION
      SHOW TABLES LIKE 'messages'
      UNION
      SHOW TABLES LIKE 'message_status'
      UNION
      SHOW TABLES LIKE 'user_presence'
      UNION
      SHOW TABLES LIKE 'chat_settings'
    `);
    
    console.log('📋 New Chat Tables:');
    tables.forEach(table => {
      console.log(`   ✅ ${Object.values(table)[0]}`);
    });
    
    // Check data counts
    const queries = [
      'SELECT COUNT(*) as count FROM conversations',
      'SELECT COUNT(*) as count FROM conversation_participants', 
      'SELECT COUNT(*) as count FROM messages',
      'SELECT COUNT(*) as count FROM message_status',
      'SELECT COUNT(*) as count FROM user_presence',
      'SELECT COUNT(*) as count FROM user_chat WHERE is_active = 1'
    ];
    
    const tableNames = ['conversations', 'participants', 'messages', 'message_status', 'user_presence', 'original_messages'];
    
    console.log('\n📊 Data Migration Results:');
    for (let i = 0; i < queries.length; i++) {
      const [result] = await connection.query(queries[i]);
      console.log(`   ${tableNames[i]}: ${result[0].count} records`);
    }
    
    // Check sample conversation
    const [sampleConv] = await connection.query(`
      SELECT c.id, c.type, c.created_by, 
             COUNT(cp.user_id) as participants,
             COUNT(m.id) as messages
      FROM conversations c
      LEFT JOIN conversation_participants cp ON c.id = cp.conversation_id
      LEFT JOIN messages m ON c.id = m.conversation_id
      GROUP BY c.id
      LIMIT 3
    `);
    
    console.log('\n💬 Sample Conversations:');
    sampleConv.forEach(conv => {
      console.log(`   Conversation ${conv.id}: ${conv.participants} participants, ${conv.messages} messages`);
    });
    
    console.log('\n✅ Migration verification complete!');
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

verifyMigration();
