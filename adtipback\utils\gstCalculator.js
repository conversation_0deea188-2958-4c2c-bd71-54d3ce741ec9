/**
 * GST Calculator Utility
 * Handles GST calculations for subscription plans
 */

const GST_PERCENTAGE = 18; // 18% GST

/**
 * Calculate GST amount and total for a given base amount
 * @param {number} baseAmount - The base amount in rupees
 * @returns {object} Object containing GST details
 */
const calculateGST = (baseAmount) => {
  const gstAmount = baseAmount * (GST_PERCENTAGE / 100);
  const totalAmount = baseAmount + gstAmount;
  
  return {
    base_amount: baseAmount,
    gst_amount: gstAmount,
    total_amount: totalAmount,
    gst_percentage: GST_PERCENTAGE,
    // Convert to paise for Razorpay (amount * 100)
    amount_with_gst_paise: Math.round(totalAmount * 100),
    gst_amount_paise: Math.round(gstAmount * 100)
  };
};

/**
 * Format GST details for API response
 * @param {number} baseAmount - The base amount in rupees
 * @returns {object} Formatted GST details for API response
 */
const formatGSTForAPI = (baseAmount) => {
  const gstDetails = calculateGST(baseAmount);
  
  return {
    base_amount: gstDetails.base_amount,
    gst_amount: gstDetails.gst_amount,
    total_amount: gstDetails.total_amount,
    gst_percentage: gstDetails.gst_percentage,
    amount_with_gst: gstDetails.amount_with_gst_paise,
    gst_amount_paise: gstDetails.gst_amount_paise
  };
};

/**
 * Add GST details to a plan object
 * @param {object} plan - The plan object
 * @returns {object} Plan object with GST details added
 */
const addGSTToPlan = (plan) => {
  const baseAmount = parseFloat(plan.amount);
  const gstDetails = formatGSTForAPI(baseAmount);
  
  return {
    ...plan,
    amount: baseAmount, // Keep original amount for reference
    ...gstDetails
  };
};

/**
 * Add GST details to an array of plans
 * @param {array} plans - Array of plan objects
 * @returns {array} Array of plan objects with GST details added
 */
const addGSTToPlans = (plans) => {
  return plans.map(plan => addGSTToPlan(plan));
};

module.exports = {
  calculateGST,
  formatGSTForAPI,
  addGSTToPlan,
  addGSTToPlans,
  GST_PERCENTAGE
}; 