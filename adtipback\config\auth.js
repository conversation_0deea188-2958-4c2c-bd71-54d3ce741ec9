const jwt = require("jsonwebtoken");

// exports.verifyToken = (req, res, next) => {
// 	const token = req.headers.authorization.split(" ")[1]

// 	if (!token || token == "null") {
// 		return res.status(200).send({ error: "you are not authorise to access this resource", status: 403 })
// 	} else {
// 		jwt.verify(token, process.env.JWT_KEY, (err) => {
// 			console.log("JWT token validation error: ", err)
// 			if (err) {
// 				res.status(200).send({ error: 'failed to authenticate token', status: 500 })
// 			} else {
// 				next()
// 			}
// 		})
// 	}
// }

exports.verifyToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization.split(" ")[1];

    if (!token || token === "null") {
      return res.status(401).send({
        error: "You are not authorized to access this resource",
        status: false,
        code: "NO_TOKEN"
      });
    }

    jwt.verify(token, process.env.JWT_KEY, async (err, decoded) => {
      if (err) {
        console.error("JWT token validation error: ", err);

        // Return 401 for token expiration/invalid token instead of 500
        if (err.name === 'TokenExpiredError') {
          return res.status(401).send({
            error: "Token has expired",
            status: false,
            code: "TOKEN_EXPIRED"
          });
        } else if (err.name === 'JsonWebTokenError') {
          return res.status(401).send({
            error: "Invalid token",
            status: false,
            code: "INVALID_TOKEN"
          });
        } else {
          return res.status(401).send({
            error: "Failed to authenticate token",
            status: false,
            code: "AUTH_FAILED"
          });
        }
      }

      // --- Single Device Login Enforcement ---
      const userId = decoded.user_id;
      const dbQuery = require('../dbConfig/queryRunner');
      const userRows = await dbQuery.queryRunner(`SELECT current_session_token FROM users WHERE id = ${userId}`);
      if (!userRows.length || userRows[0].current_session_token !== token) {
        return res.status(401).send({
          error: "Logged out due to login on another device.",
          status: false,
          code: "FORCED_LOGOUT"
        });
      }
      // Save user details in req.user
      req.user = decoded;
      next();
    });
  } catch (error) {
    console.error("Authorization error: ", error);
    return res.status(400).send({
      error: "Invalid token format",
      status: false,
      code: "INVALID_FORMAT"
    });
  }
};

// WebSocket token verification method
exports.verifyTokenWS = (token) => {
  return new Promise((resolve, reject) => {
    if (!token || token === "null") {
      return reject(new Error("No token provided"));
    }

    jwt.verify(token, process.env.JWT_KEY, (err, decoded) => {
      if (err) {
        console.error("WebSocket JWT token validation error: ", err);
        return reject(new Error("Invalid token"));
      }

      resolve(decoded);
    });
  });
};

// Token refresh method
exports.refreshToken = (req, res) => {
  try {
    const token = req.headers.authorization?.split(" ")[1];

    if (!token || token === "null") {
      return res.status(401).send({
        error: "No token provided for refresh",
        status: false,
        code: "NO_TOKEN"
      });
    }

    // Verify the token (even if expired, we can still decode it)
    jwt.verify(token, process.env.JWT_KEY, { ignoreExpiration: true }, (err, decoded) => {
      if (err && err.name !== 'TokenExpiredError') {
        console.error("Token refresh validation error: ", err);
        return res.status(401).send({
          error: "Invalid token for refresh",
          status: false,
          code: "INVALID_TOKEN"
        });
      }

      // Generate new token with same payload
      const newToken = jwt.sign(
        { user_id: decoded.user_id, email: decoded.email },
        process.env.JWT_KEY,
        { expiresIn: "7d" } // 7 days for refreshed tokens
      );

      return res.status(200).send({
        status: true,
        message: "Token refreshed successfully",
        accessToken: newToken,
        data: {
          user_id: decoded.user_id,
          email: decoded.email
        }
      });
    });
  } catch (error) {
    console.error("Token refresh error: ", error);
    return res.status(400).send({
      error: "Token refresh failed",
      status: false,
      code: "REFRESH_FAILED"
    });
  }
};