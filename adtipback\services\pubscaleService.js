const moment = require("moment");
const dbQuery = require('../dbConfig/queryRunner');

const pubscaleService = {
  processCallback: async ({ user_id, value, token, signature }) => {
    try {
      console.log("[PubscaleService] Processing callback:", {
        user_id,
        value,
        token: token ? `${token.substring(0, 8)}...` : 'null',
        signature: signature ? 'provided' : 'missing'
      });

      const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");

      // Validate required parameters
      if (!user_id || !value || !token) {
        console.error("[PubscaleService] Missing required parameters:", { user_id: !!user_id, value: !!value, token: !!token });
        return {
          status: false,
          statusCode: 400,
          message: "Missing required parameters: user_id, value, or token",
        };
      }

      // Validate value is a positive number
      const numericValue = parseFloat(value);
      if (isNaN(numericValue) || numericValue <= 0) {
        console.error("[PubscaleService] Invalid value:", value);
        return {
          status: false,
          statusCode: 400,
          message: "Invalid value: must be a positive number",
        };
      }

      // Check for duplicate token
      const tokenQuery = `
        SELECT id, token
        FROM pubscale_earnings
        WHERE token = ?
        LIMIT 1
      `;
      const existingToken = await dbQuery.queryRunner(tokenQuery, [token]);

      if (existingToken.length) {
        return {
          status: false,
          statusCode: 400,
          message: "Duplicate callback detected",
        };
      }

      // Validate user exists
      const userQuery = `
        SELECT id
        FROM users
        WHERE id = ?
        LIMIT 1
      `;
      const user = await dbQuery.queryRunner(userQuery, [user_id]);

      if (!user.length) {
        return {
          status: false,
          statusCode: 400,
          message: "Invalid user",
        };
      }

      // Fetch latest wallet balance
      const balanceQuery = `
        SELECT totalBalance
        FROM wallet
        WHERE createdby = ?
        ORDER BY id DESC
        LIMIT 1
      `;
      const wallet = await dbQuery.queryRunner(balanceQuery, [user_id]);
      const previousBalance = wallet.length ? parseFloat(wallet[0].totalBalance) : 0;
      const newBalance = previousBalance + parseFloat(value);

      // Save callback data to pubscale_earnings
      const earningsQuery = `
        INSERT INTO pubscale_earnings (
          user_id, value, token, signature, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, 'processed', ?, ?)
      `;
      await dbQuery.queryRunner(earningsQuery, [
        user_id,
        value,
        token,
        signature,
        currentTime,
        currentTime,
      ]);

      // Insert new wallet record with updated balance
      const walletQuery = `
        INSERT INTO wallet (
          totalBalance, transaction_status, amount, transaction_date, transaction_type, 
          createdby, createddate, updateddate, order_id, payment_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      const orderId = `PUBSCALE_${token}`; // Unique order_id for tracking
      const paymentId = token; // Use token as payment_id
      await dbQuery.queryRunner(walletQuery, [
        newBalance, // Updated total balance
        1, // transaction_status (1 for success)
        value, // amount (PubScale earnings)
        currentTime, // transaction_date
        "pubscale-credit", // transaction_type
        user_id, // createdby
        currentTime, // createddate
        currentTime, // updateddate
        orderId, // order_id
        paymentId, // payment_id
      ]);

      console.log(`[PubscaleService] ✅ Successfully processed callback for user ${user_id}: +${value} INR (Token: ${token.substring(0, 8)}...)`);

      return {
        status: true,
        statusCode: 200,
        message: "Callback processed and wallet updated successfully",
        data: {
          user_id,
          amount_credited: numericValue,
          new_balance: newBalance,
          transaction_id: orderId
        }
      };
    } catch (error) {
      console.error("[PubscaleService] ❌ Error processing callback:", error);
      return {
        status: false,
        statusCode: 500,
        message: "Internal Server Error",
        error: error.message
      };
    }
  },
};

module.exports = pubscaleService;