/*
Copyright Rene <PERSON> 2008-2015
Distributed under the Boost Software License, Version 1.0.
(See accompanying file LICENSE_1_0.txt or copy at
http://www.boost.org/LICENSE_1_0.txt)
*/

#if !defined(BOOST_PREDEF_LIBRARY_C_H) || defined(BOOST_PREDEF_INTERNAL_GENERATE_TESTS)
#ifndef BOOST_PREDEF_LIBRARY_C_H
#define BOOST_PREDEF_LIBRARY_C_H
#endif

#include <boost/predef/library/c/_prefix.h>

#include <boost/predef/library/c/cloudabi.h>
#include <boost/predef/library/c/gnu.h>
#include <boost/predef/library/c/uc.h>
#include <boost/predef/library/c/vms.h>
#include <boost/predef/library/c/zos.h>

#endif
