const dbQuery = require("../dbConfig/queryRunner");
const NodeCache = require("node-cache");
const categoryCache = new NodeCache({ stdTTL: 1296000 });

let getSearchCompany = (searchText) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from company where (name LIKE "%${searchText}%" OR email LIKE "%${searchText}%" OR website LIKE "%${searchText}%" OR location LIKE "%${searchText}%" OR about LIKE "%${searchText}%")AND is_active = 1 limit 10`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "company list",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "company list",
            data: [],
          });
        }
      })
      .catch((err) => {
        console.log("er", err);
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getSearchProducts = (searchText) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * FROM product 
    WHERE (name LIKE "%${searchText}%" 
    OR keyword LIKE "%${searchText}%" 
    OR brand LIKE "%${searchText}%" 
    OR description LIKE "%${searchText}%") 
    AND is_active = 1 limit 10`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let formattedResult = result.map((product) => {
            return {
              ...product,
              images: product.images ? product.images.split(",") : [],
            };
          });
          resolve({
            status: 200,
            message: "products",
            data: formattedResult,
          });
        } else {
          resolve({
            status: 200,
            message: "products",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getPopularProducts = () => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT p.*,pp.id as popular_product_id from popular_products pp left join product p ON p.id=pp.product_id where  pp.is_active=1 order by pp.created_date`;
    dbQuery.queryRunner(sql).then((result) => {
      if (result && result.length != 0) {
        let formattedResult = result.map((product) => {
          return {
            ...product,
            images: product.images ? product.images.split(",") : [],
          };
        });
        resolve({
          status: 200,
          message: "popular items",
          data: formattedResult,
        });
      } else {
        resolve({
          status: 200,
          message: "popular items not found",
          data: [],
        });
      }
    });
  }).catch((err) => {
    reject({
      status: 500,
      message: err,
      data: [],
    });
  });
};

let addProductToPopular = (productId) => {
  return new Promise((resolve, reject) => {
    let sql1 = `SELECT * From popular_products where product_id=${productId} and is_active=1`;
    let sql = `INSERT INTO popular_products (product_id,is_active,created_date,updated_date)VALUES(${productId},1,NOW(),NOW())`;
    dbQuery
      .queryRunner(sql1)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "already exist",
            data: [],
          });
        } else {
          dbQuery.queryRunner(sql).then((result) => {
            if (result && result.length != 0) {
              resolve({
                status: 200,
                message: "added",
                data: [],
              });
            } else {
              reject({
                status: 400,
                message: "not added",
                data: [],
              });
            }
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let deleteWishListItem = (Id) => {
  return new Promise((resolve, reject) => {
    let sql = `UPDATE  wishlist set is_active=0, updated_date=NOW() where id=${Id}`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "deleted wishlist item",
            data: [],
          });
        } else {
          resolve({
            status: 200,
            message: "unable to deleted wishlist item",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getWishList = (Id) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT p.*,w.id as wishlist_id from wishlist w left join product p ON p.id=w.product_id where w.createdby=${Id} and w.is_active=1`;
    dbQuery.queryRunner(sql).then((result) => {
      if (result && result.length != 0) {
        let formattedResult = result.map((product) => {
          return {
            ...product,
            images: product.images ? product.images.split(",") : [],
          };
        });
        resolve({
          status: 200,
          message: "wishlist items",
          data: formattedResult,
        });
      } else {
        resolve({
          status: 200,
          message: "wishlist items not found",
          data: [],
        });
      }
    });
  }).catch((err) => {
    reject({
      status: 500,
      message: err,
      data: [],
    });
  });
};

let addToWishList = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from wishlist where product_id=${data.product_id} and createdby=${data.createdBy} and is_active=1`;
    let sql1 = `INSERT INTO wishlist (product_id,is_active,createdby,created_date,updated_date)
     VALUES(${data.product_id},1,${data.createdBy},NOW(),NOW())`;
    dbQuery.queryRunner(sql).then((result) => {
      if (result && result.length != 0) {
        resolve({
          status: 200,
          message: "Item already exist in wishlist.",
          data: data,
        });
      } else {
        dbQuery.queryRunner(sql1).then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Item added in wishlist.",
              data: data,
            });
          } else {
            reject({
              status: 500,
              message: err,
              data: [],
            });
          }
        });
      }
    });
  }).catch((err) => {
    reject({
      status: 500,
      message: err,
      data: [],
    });
  });
};

let updateSellerShippingDetails = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `UPDATE orders_products set shipping_company="${data.shipping_company}", shipping_company_track_id="${data.shipping_company_track_id}",shipping_company_additional_information="${data.shipping_company_additional_information}" where id=${data.id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Shipping details updated",
            data: data,
          });
        } else {
          reject({
            status: 400,
            message: "Shipping details not updated",
            data: data,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getUserOrders = (userId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from orders_products where created_by=${userId} order by created_date desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let formattedResult = result.map((product) => {
            return {
              ...product,
              product_image: product.product_image
                ? product.product_image.split(",")
                : [],
            };
          });
          resolve({
            status: 200,
            message: "Orders found",
            data: formattedResult,
          });
        } else {
          resolve({
            status: 200,
            message: "Orders not found",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};
let getSellerOrders = (userId) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from orders_products where product_created_by=${userId} order by created_date desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let formattedResult = result.map((product) => {
            return {
              ...product,
              product_image: product.product_image
                ? product.product_image.split(",")
                : [],
            };
          });
          resolve({
            status: 200,
            message: "Orders found",
            data: formattedResult,
          });
        } else {
          resolve({
            status: 200,
            message: "Orders not found",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let deleteCartSingleItem = (cartId) => {
  return new Promise((resolve, reject) => {
    let sql = `UPDATE cart_item set is_active=0 where id=${cartId}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "cart item deleted.",
            data: [],
          });
        } else {
          reject({
            status: 400,
            message: "unable to delete cart item.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};
let deleteCartAllItems = (userId) => {
  return new Promise((resolve, reject) => {
    let sql = `UPDATE cart_item set is_active=0 where createdby=${userId}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "cart items deleted.",
            data: [],
          });
        } else {
          reject({
            status: 400,
            message: "unable to delete cart items.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let updateBargin = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `UPDATE bargain_product set is_bargain=1,bargain_amount=${data.bargain_amount},is_accepted=${data.is_accepted},updated_time=NOW() where id=${data.bargain_id} `;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "bargain added",
            data: [],
          });
        } else {
          reject({
            status: 400,
            message: "bargain not added",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getBaraginItems = (Id) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT p.*,bp.id as bargain_id,bp.is_bargain,bp.bargain_amount,bp.is_accepted from bargain_product bp left join product p ON p.id=bp.product_id where bp.created_by=${Id} and bp.is_active=1 order by bp.created_time desc`;
    dbQuery.queryRunner(sql).then((result) => {
      if (result && result.length != 0) {
        let formattedResult = result.map((product) => {
          return {
            ...product,
            images: product.images ? product.images.split(",") : [],
          };
        });
        resolve({
          status: 200,
          message: "bargain items",
          data: formattedResult,
        });
      } else {
        resolve({
          status: 200,
          message: "bargain items not found",
          data: [],
        });
      }
    });
  }).catch((err) => {
    reject({
      status: 500,
      message: err,
      data: [],
    });
  });
};

let addToBargain = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from bargain_product where product_id=${data.product_id} and created_by=${data.createdBy}`;
    let sql1 = `INSERT INTO bargain_product (product_id,is_active,created_by,is_bargain,bargain_amount,is_accepted,created_time,updated_time)
     VALUES(${data.product_id},1,${data.createdBy},0,null,0,NOW(),NOW())`;
    dbQuery.queryRunner(sql).then((result) => {
      if (result && result.length != 0) {
        resolve({
          status: 200,
          message: "Item already exist in bargain.",
          data: data,
        });
      } else {
        dbQuery.queryRunner(sql1).then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Item added in bargain.",
              data: data,
            });
          } else {
            reject({
              status: 500,
              message: err,
              data: [],
            });
          }
        });
      }
    });
  }).catch((err) => {
    reject({
      status: 500,
      message: err,
      data: [],
    });
  });
};

let payUsingWallet = (userId, requestAmount) => {
  return new Promise((resolve, reject) => {
    let sql = `select totalbalance from wallet where createdby=${userId} and transaction_status != 6 order by createdDate desc Limit 1;`;
    let sql1 = `UPDATE wallet set totalbalance=totalbalance-${requestAmount} where createdby=${userId} and transaction_status != 6 order by createdDate desc Limit 1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          console.log("total balance", result[0].totalbalance);
          if (result[0].totalbalance < requestAmount) {
            reject({
              status: 400,
              message: "Insufficient Balance",
              data: [],
            });
          } else {
            dbQuery
              .queryRunner(sql1)
              .then((result) => {
                if (result && result.length != 0) {
                  resolve({
                    status: 200,
                    message: "Product Done",
                    data: result,
                  });
                } else {
                  reject({
                    status: 400,
                    message: "Insufficient Balance",
                    data: [],
                  });
                }
              })
              .catch((err) => {
                reject({
                  status: 500,
                  message: err,
                  data: [],
                });
              });
          }
        } else {
          reject({
            status: 400,
            message: "Insufficient Balance",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let placeOrder = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO orders_products (product_name,product_id,product_image,product_price,discount_on_product,platform_fee,shipping_fee,payment_method,created_by,product_created_by,created_date,company_id,size,inches,	paid,liter,kg,quantity,address,total_amount,status)
    VALUES("${data.product_name}",${data.product_id},"${data.product_image}",${data.product_price},${data.discount_on_product},${data.platform_fee},${data.shipping_fee},"${data.payment_method}",${data.created_by},${data.product_created_by},NOW(),${data.company_id},"${data.size}",${data.inches},${data.paid},${data.liter},${data.kg},${data.quantity},"${data.address}",${data.total_amount},"Ordered")`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "order placed",
            data: data,
          });
        } else {
          reject({
            status: 400,
            message: "order not placed",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let selectedDeliveryAddress = (id) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from delivery_address where created_by=${id} and is_selected=1 limit 1`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "address found selected",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "address  not found",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let selectDeliveryAddress = (addressId, userId) => {
  return new Promise((resolve, reject) => {
    let sql = `UPDATE delivery_address set is_selected=1 where id=${addressId}`;
    let sql1 = `UPDATE delivery_address set is_selected=0 where created_by=${userId} and id!=${addressId}`;
    Promise.all([dbQuery.queryRunner(sql), dbQuery.queryRunner(sql1)])
      .then((result) => {
        console.log("address id", addressId);
        console.log("result", result);
        if (result && result.length != 0) {
          if (
            result[0] &&
            result[0].length != 0 &&
            result[1] &&
            result[1].length != 0
          ) {
            resolve({
              status: 200,
              message: "address  selected",
              data: [],
            });
          } else {
            resolve({
              status: 400,
              message: "address not  selected",
              data: [],
            });
          }
        } else {
          resolve({
            status: 400,
            message: "address not selected",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let deliveryAddress = (id) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from delivery_address where created_by=${id} and is_active=1 order by is_selected desc`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "address found",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "address not found",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let addAddress = (data) => {
  return new Promise((resolve, reject) => {
    let sql1 = `Select * from delivery_address where created_by=${data.created_by} and is_active=1`;
    let sql = `INSERT INTO delivery_address (name,mobile_number,pincode,address,
    locality,city,state,created_by,
    is_selected,is_active,created_date,updated_date) 
    VALUES("${data.name}",${data.mobile_number},${data.pincode},"${data.address}",
    "${data.locality}","${data.city}","${data.state}",${data.created_by},1,1,NOW(),NOW()
    )`;
    let sql2 = `UPDATE delivery_address set is_selected=0 where created_by=${data.created_by} and is_active=1`;

    dbQuery.queryRunner(sql1).then((result) => {
      if (result && result.length != 0) {
        dbQuery.queryRunner(sql2).then((result) => {
          if (result && result.length != 0) {
            dbQuery
              .queryRunner(sql)
              .then((result) => {
                if (result && result.length != 0) {
                  resolve({
                    status: 200,
                    message: "address added",
                    data: data,
                  });
                } else {
                  reject({
                    status: 400,
                    message: "address not added",
                    data: data,
                  });
                }
              })
              .catch((err) => {
                reject({
                  status: 500,
                  message: err,
                  data: [],
                });
              });
          } else {
            reject({
              status: 400,
              message: "address not added",
              data: [],
            });
          }
        });
      } else {
        dbQuery
          .queryRunner(sql)
          .then((result) => {
            if (result && result.length != 0) {
              resolve({
                status: 200,
                message: "address added",
                data: data,
              });
            } else {
              resolve({
                status: 200,
                message: "address not added",
                data: data,
              });
            }
          })
          .catch((err) => {
            reject({
              status: 500,
              message: err,
              data: [],
            });
          });
      }
    });
  });
};

let getCartItems = (Id) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT p.*,ci.id as cart_id from cart_item ci left join product p ON p.id=ci.product_id where ci.createdby=${Id} and ci.is_active=1`;
    dbQuery.queryRunner(sql).then((result) => {
      if (result && result.length != 0) {
        let formattedResult = result.map((product) => {
          return {
            ...product,
            images: product.images ? product.images.split(",") : [],
          };
        });
        resolve({
          status: 200,
          message: "cart items",
          data: formattedResult,
        });
      } else {
        resolve({
          status: 200,
          message: "cart items not found",
          data: [],
        });
      }
    });
  }).catch((err) => {
    reject({
      status: 500,
      message: err,
      data: [],
    });
  });
};

let addToCart = (data) => {
  return new Promise((resolve, reject) => {
    let sql = `SELECT * from cart_item where product_id=${data.product_id} and createdby=${data.createdBy} and is_active=1`;
    let sql1 = `INSERT INTO cart_item (product_id,product_quantity,is_active,createdby,createddate,updateddate)
     VALUES(${data.product_id},1,1,${data.createdBy},NOW(),NOW())`;
    dbQuery.queryRunner(sql).then((result) => {
      if (result && result.length != 0) {
        resolve({
          status: 200,
          message: "Item already exist in cart.",
          data: data,
        });
      } else {
        dbQuery.queryRunner(sql1).then((result) => {
          if (result && result.length != 0) {
            resolve({
              status: 200,
              message: "Item added in cart.",
              data: data,
            });
          } else {
            reject({
              status: 500,
              message: err,
              data: [],
            });
          }
        });
      }
    });
  }).catch((err) => {
    reject({
      status: 500,
      message: err,
      data: [],
    });
  });
};

let getSingleProductById = (Id) => {
  return new Promise((resolve, reject) => {
    let sql = `select * from product where id=${Id} and is_active=1`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let formattedResult = result.map((product) => {
            return {
              ...product,
              images: product.images ? product.images.split(",") : [],
            };
          });
          resolve({
            status: 200,
            message: "Fetch product successfully.",
            data: formattedResult,
          });
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getProductByCategoryId = (Id) => {
  return new Promise((resolve, reject) => {
    let sql = `select * from product where category_id=${Id} and is_active=1`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let formattedResult = result.map((product) => {
            return {
              ...product,
              images: product.images ? product.images.split(",") : [],
            };
          });
          resolve({
            status: 200,
            message: "Fetch products successfully.",
            data: formattedResult,
          });
        } else {
          resolve({
            status: 200,
            message: "products not found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
};

let getCategoryProduct = () =>
  new Promise((resolve, reject) => {
    const cachedData = categoryCache.get("productCategory");
    if (cachedData) {
      resolve({
        status: 200,
        message: "Fetch product category from cache.",
        data: cachedData,
      });
    } else {
      let sql = `select * from product_category where is_active=1;`;
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            categoryCache.set("productCategory", result);
            resolve({
              status: 200,
              message: "Fetch product catergory successfully.",
              data: result,
            });
          } else {
            resolve({
              status: 200,
              message: "product catergory not found.",
              data: [],
            });
          }
        })
        .catch((err) => {
          reject({
            status: 500,
            message: err,
            data: [],
          });
        });
    }
  });

let addCategory = (categoryData) => {
  return new Promise((resolve, reject) => {
    let sql = `INSERT INTO product_category (name,image,commission_to_user,
    commission_to_adtip,is_active,created_date)VALUES('${categoryData.name}','${categoryData.image}',
    ${categoryData.commission_to_user},${categoryData.commission_to_adtip},1,NOW()
    )`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "category added successfully.",
            data: [result],
          });
        } else {
          resolve({
            status: 200,
            message: "category not added .",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: `${err}`,
          data: [],
        });
      });
  });
};

let saveProduct = (productData) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO product (name, description, brand,category_id, delivery_time ,units,
    regular_price,market_price,size,images,company_id,is_active, createddate,updatedate,
    delivery_type,terms_condition,created_by,
    your_price,keyword,manufacturer_date,product_description,
    product_specification,inches,additional_accessories,stock,
    procurement_type,procurement_time,shipping_fee,replacement_days,
    warranty_days,sku_id,auto_bargain,bargain_minimum_price)
     VALUES ('${productData.name}', '${productData.description}', '${
      productData.brand
    }',${productData.categoryId},'${productData.deliveryTime}', ${
      productData.stock
    },'${productData.regularPrice}',
     '${productData.marketPrice}','${
      productData.size ? productData.size : ""
    }','${productData.images}',
     ${productData.companyId},1,NOW(),NOW(),'${
      productData.deliveryType ? productData.deliveryType : ""
    }','${productData.termsApply}',${productData.created_by},
    ${productData.your_price},'${productData.keyword}',
    '${productData.manufacturer_date}','${productData.product_description}',
    '${productData.product_specification}',${productData.inches},
    '${productData.additional_accessories}',${productData.stock},'${
      productData.procurement_type
    }',
    ${productData.procurement_time},${productData.shipping_fee},${
      productData.replacement_days
    },
    ${productData.warranty_days},
    '${productData.sku_id}',${productData.auto_bargain},${
      productData.bargain_minimum_price
    }
  )`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          productData.id = result.insertId;
          resolve({
            status: 200,
            message: "Product added successfully.",
            data: [productData],
          });
        } else {
          reject({
            status: 400,
            message: "Product not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        console.log("err", err);
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let updateProduct = (productData) =>
  new Promise((resolve, reject) => {
    let sql = `update product set `;
    if (productData.name) sql += `name='${productData.name}',`;
    if (productData.description)
      sql += ` description='${productData.description}',`;
    if (productData.images) sql += ` images='${productData.images}',`;
    if (productData.brand) sql += ` brand='${productData.brand}',`;

    if (productData.categoryId)
      sql += ` category_id='${productData.categoryId}',`;
    if (productData.deliveryTime)
      sql += ` delivery_time='${productData.deliveryTime}',`;
    if (productData.deliveryType)
      sql += ` delivery_type='${productData.deliveryType}',`;
    if (productData.termsApply)
      sql += ` terms_condition='${productData.termsApply}',`;

    if (productData.units) sql += ` units=${productData.units},`;
    if (productData.regularPrice)
      sql += ` regular_price='${productData.regularPrice}',`;
    if (productData.marketPrice)
      sql += ` market_price='${productData.marketPrice}',`;
    if (productData.size) sql += ` size='${productData.size}',`;

    if (productData.companyId) sql += ` company_id=${productData.companyId},`;
    if (productData.isActive) sql += ` is_active=${productData.isActive},`;

    sql += ` updatedate=NOW(),`;
    if (sql !== "") sql = sql.substring(0, sql.length - 1);
    sql += `  where id=${productData.id}`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          //if (productFile && productFile.images && productFile.images.length > 0) productData.images = productFile.images.map(o => o['filename']).toString();
          resolve({
            status: 200,
            message: "Product updated successfully.",
            data: [productData],
          });
        } else {
          reject({
            status: 400,
            message: "Product not updated.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getProductList = (companyId) =>
  new Promise((resolve, reject) => {
    let sql = `select id, name, 
    description,product_type as productType,images, regular_price,market_price,is_active as isActive
     from product where company_id=${companyId} and is_deleted!=1 order by createdDate desc;`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch Product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
let getProductListByAdvertiserId = (advertiserId) =>
  new Promise((resolve, reject) => {
    let sql = `select *
       from product where created_by=${advertiserId} and is_deleted!=1 order by createdDate desc;`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          let formattedResult = result.map((product) => {
            return {
              ...product,
              images: product.images ? product.images.split(",") : [],
            };
          });
          resolve({
            status: 200,
            message: "Fetch Product successfully.",
            data: formattedResult,
          });
        } else {
          resolve({
            status: 200,
            message: "Product not found.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getAllProductList = () =>
  new Promise((resolve, reject) => {
    let sql = `select id, name, description,product_type as productType,images, regular_price,market_price, is_active as isActive from product where is_deleted!=1 order by createdDate desc limit 20;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch Product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let deleteProduct = (id) =>
  new Promise((resolve, reject) => {
    let sql = `update product set is_deleted=1 WHERE id=${id};`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Delete Product successfully.",
            data: [],
          });
        } else {
          resolve({
            status: 200,
            message: "Product not deleted.",
            data: [],
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getProductCategory = () =>
  new Promise((resolve, reject) => {
    let sql = `select * from product_catergory where is_active=1;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch product catergory successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product catergory not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let saveProductDetails = (productData) =>
  new Promise((resolve, reject) => {
    let sql = "";

    if (productData.isLike) {
      sql += `INSERT INTO product_details (product_id,user_id,is_like,createdby,createddate)
         VALUES(${productData.productId},${productData.userId},${
        productData.isLike
      },
            ${productData.createdby},NOW()) ON DUPLICATE KEY 
            UPDATE is_like = ${
              productData.isLike ? productData.isLike : null
            }, updateddate=NOW()`;
      if (productData.isLike === "1")
        dbQuery.queryRunner(
          `update product set total_likes = total_likes + 1 where id=${productData.productId};`
        );
      if (productData.isLike === "0")
        dbQuery.queryRunner(
          `update product set total_likes = total_likes - 1 where id=${productData.productId};`
        );
    }
    if (productData.isView) {
      sql += `INSERT INTO product_details (product_id,user_id,is_view,createdby,createddate)
         VALUES(${productData.productId},${productData.userId},${
        productData.isView
      },
            ${productData.createdby},NOW()) ON DUPLICATE KEY 
            UPDATE is_view = ${
              productData.isView ? productData.isView : null
            }, updateddate=NOW();`;
      if (productData.isView === "1")
        dbQuery.queryRunner(
          `update product set total_views = total_views + 1 where id=${productData.productId};`
        );
      if (productData.isView === "0")
        dbQuery.queryRunner(
          `update product set total_views = total_views - 1 where id=${productData.productId};`
        );
    }

    if (productData.isWhishList) {
      sql += `INSERT INTO product_details (product_id,user_id,is_wishlist, createdby,createddate)
         VALUES(${productData.productId},${productData.userId},${
        productData.isWhishList
      },
            ${productData.createdby},NOW()) ON DUPLICATE KEY 
            UPDATE is_wishlist = ${
              productData.isWhishList ? productData.isWhishList : null
            } , updateddate=NOW();`;
      if (productData.isWhishList === "1")
        dbQuery.queryRunner(
          `update product set total_wish_list = total_wish_list + 1 where id=${productData.productId};`
        );
      if (productData.isWhishList === "0")
        dbQuery.queryRunner(
          `update product set total_wish_list = total_wish_list - 1 where id=${productData.productId};`
        );
    }

    if (productData.isReact) {
      sql += `INSERT INTO product_details (product_id,user_id,is_react,react_value,react_comments,createdby,createddate,react_createddate)
         VALUES(${productData.productId},${productData.userId},${
        productData.isReact
      },${productData.reactValue},'${
        productData.reactComments ? productData.reactComments : ""
      }',${productData.createdby},NOW(),NOW()) ON DUPLICATE KEY 
        UPDATE is_react = ${
          productData.isReact ? productData.isReact : null
        }, react_value = ${
        productData.reactValue ? productData.reactValue : null
      },react_comments='${
        productData.reactComments ? productData.reactComments : ""
      }',react_createddate=NOW(), updateddate=NOW();`;
      if (productData.isReact === "1")
        dbQuery.queryRunner(
          `update product set total_rating = total_rating + 1 where id=${productData.productId};`
        );
      if (productData.isReact === "0")
        dbQuery.queryRunner(
          `update product set total_rating = total_rating - 1 where id=${productData.productId};`
        );
    }

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Product data save successfully.",
            data: [productData],
          });
        } else {
          reject({
            status: 400,
            message: "Product not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Ad already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid referance Id.";
        if (err.message.includes("UNKNOWN_CODE_PLEASE_REPORT"))
          message = "Invalid referance Id.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getProductByProductId = (productId, userId) =>
  new Promise((resolve, reject) => {
    let productData = "";
    let sql = `select p.*,IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p left Join product_details pd on p.id=pd.product_id and pd.user_id = ${userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${userId}
    where p.is_active=1 and p.is_deleted=0 and p.id=${productId} group by p.id order by pd.createddate desc;`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            element.isAddedCart = element.cartProductQuantity != null ? 1 : 0;
            element.isBarginProduct = element.bargainId != null ? 1 : 0;
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          productData = result;
          let ratingSql = ` select u.id as userId, u.name as userName,u.profile_image as userProfileImage,pd.react_value as ratingValue, pd.react_comments as ratingComments, pd.react_createddate as ratingCreateddate
                from product_details pd INNER JOIN users u on pd.user_id=u.id where pd.product_id=${productId} and pd.is_react=1 order by pd.react_createddate desc`;
          return dbQuery.queryRunner(ratingSql);
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: result,
          });
        }
      })
      .then((result) => {
        productData[0].ratingData = result;
        resolve({
          status: 200,
          message: "Fetch product successfully.",
          data: productData,
        });
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getProductByUserId = (userId) =>
  new Promise((resolve, reject) => {
    let productData = "";
    let sql = `Select * FROM adtip_qa.product p join adtip_qa.company c on c.id = p.company_id where c.createdby = '${userId}' and p.is_deleted!=1 and p.is_active=1`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });

          resolve({
            status: 200,
            message: "Fetch product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: result,
          });
        }
      })
      .then((result) => {
        productData[0].ratingData = result;
        resolve({
          status: 200,
          message: "Fetch product successfully.",
          data: productData,
        });
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getProductByCategory = (categoryId, userId) =>
  new Promise((resolve, reject) => {
    let sql = `select p.*,IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p left Join product_details pd on p.id=pd.product_id and pd.user_id = ${userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${userId}
    where p.is_active=1 and p.is_deleted!=1 and p.category_id=${categoryId} group by p.id order by pd.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            element.isAddedCart = element.cartProductQuantity != null ? 1 : 0;
            element.isBarginProduct = element.bargainId != null ? 1 : 0;
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });

          resolve({
            status: 200,
            message: "Fetch product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getProductByCompanyId = (productId) =>
  new Promise((resolve, reject) => {
    let sql = `select * from product where is_active=1 and p.is_deleted!=1 and company_id=${productId};`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });

          resolve({
            status: 200,
            message: "Fetch product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getNewProduct = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select p.*,IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p left Join product_details pd on p.id=pd.product_id and pd.user_id = ${userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${userId}
    where p.is_active=1 and p.is_deleted!=1 group by p.id order by p.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            element.isAddedCart = element.cartProductQuantity != null ? 1 : 0;
            element.isBarginProduct = element.bargainId != null ? 1 : 0;
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element.images)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          resolve({
            status: 200,
            message: "Fetch product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getSearchList = (userid) =>
  new Promise((resolve, reject) => {
    let sql = `select * from product_search_history where createdby=${userid} and is_active=1 order by createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch product search history successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product search history not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });
let saveCartItem = (productData) =>
  new Promise((resolve, reject) => {
    if (productData && productData.length == 0) {
      return reject({
        status: 400,
        message: "Invalid body.",
        data: [productData],
      });
    } else {
      let sql = `INSERT INTO cart_item (product_id, product_size, product_quantity,is_active,createdby,createddate) VALUES`;
      productData.forEach((element) => {
        sql += ` (${element.productId}, '${element.productSize}', ${element.productQuantity},1,${element.createdBy}, NOW()),`;
      });
      if (sql !== "") sql = sql.substring(0, sql.length - 1);
      dbQuery
        .queryRunner(sql)
        .then((result) => {
          if (result && result.length != 0) {
            productData.id = result.insertId;
            resolve({
              status: 200,
              message: "Product added successfully.",
              data: [productData],
            });
          } else {
            reject({
              status: 400,
              message: "Product not saved.",
              data: result,
            });
          }
        })
        .catch((err) => {
          let message = "";
          if (err.message.includes("ER_DUP_ENTRY"))
            message = "Product already available.";
          if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
            message = "Invalid userId.";
          reject({
            status: 500,
            message: message != "" ? message : err.message,
            data: [],
          });
        });
    }
  });

let saveSearchProduct = (productData) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO product_search_history (name,is_active,createdby,createddate)
         VALUE ('${productData.name}',1,${productData.userId}, NOW())`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          productData.id = result.insertId;
          resolve({
            status: 200,
            message: "Product added successfully.",
            data: [productData],
          });
        } else {
          reject({
            status: 400,
            message: "Product not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getSearchResult = (productData) =>
  new Promise((resolve, reject) => {
    let sql = `select p.*,ci.id as cartId,ci.createddate as cartAddedDate,IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p left Join product_details pd on p.id=pd.product_id and pd.user_id = ${productData.userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${productData.userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${productData.userId}
    where p.is_active=1 and p.is_deleted!=1 and p.name like '%${productData.name}%'
    OR p.description like '%${productData.name}%' OR p.brand like '%${productData.name}%' order by p.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          productData.id = result.insertId;
          result.forEach((element) => {
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          resolve({
            status: 200,
            message: "Product found.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getSimilarsearch = (productData) =>
  new Promise((resolve, reject) => {
    let sql = `select p.*,c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename, IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p 
    INNER JOIN company c on c.id = p.company_id
    left Join product_details pd on p.id=pd.product_id and pd.user_id = ${productData.userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${productData.userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${productData.userId}
    where p.is_active=1 and p.is_deleted!=1 and p.name like '%${productData.name}%'
    OR p.description like '%${productData.name}%' OR p.brand like '%${productData.name}%' order by p.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            element.isAddedCart = element.cartProductQuantity != null ? 1 : 0;
            element.isBarginProduct = element.bargainId != null ? 1 : 0;
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element.images)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          resolve({
            status: 200,
            message: "Similar Product found.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Similar Product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });
let getCartItemByUser = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select p.*,ci.id as cartId,ci.createddate as cartAddedDate,IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p left Join product_details pd on p.id=pd.product_id and pd.user_id = ${userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${userId}
    where p.is_active=1 and p.is_deleted!=1 and ci.is_active=1 and ci.createdby=${userId} group by p.id order by pd.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          resolve({
            status: 200,
            message: "Fetch product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getProductAds = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `SELECT a.*, a.ad_upload_filename as adUrl, IFNULL(ad.is_view,0) as is_view,IFNULL(ad.is_like,0) as is_like FROM admodels a
    LEFT JOIN ad_details ad on ad.ad_id=a.id where ad.user_id=${userId} and FIND_IN_SET('adtipsreel',LOWER(a.ad_place_app)) or FIND_IN_SET('all',LOWER(a.ad_place_app)) and a.is_active=1 ORDER BY a.createddate desc LIMIT 10;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch product ads successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product ads not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getRecentlyViewProducts = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select p.*,IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p left Join product_details pd on p.id=pd.product_id and pd.user_id = ${userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${userId}
    where p.is_active=1 and p.is_deleted!=1 and pd.is_view = 1 group by p.id order by pd.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            element.isAddedCart = element.cartProductQuantity != null ? 1 : 0;
            element.isBarginProduct = element.bargainId != null ? 1 : 0;
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element.images)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          resolve({
            status: 200,
            message: "Fetch product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let deleteCartItembyCartId = (cartId) =>
  new Promise((resolve, reject) => {
    let sql = `delete from cart_item where id=${cartId};`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Delete cart item successfully.",
            data: [],
          });
        } else {
          resolve({
            status: 200,
            message: "cart item not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getOrderList = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select * from cart_item where id=${userId};`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Delete cart item successfully.",
            data: [],
          });
        } else {
          resolve({
            status: 200,
            message: "cart item not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let saveBargainRequest = (productData) =>
  new Promise((resolve, reject) => {
    let checkrequest = `select * from user_bargain where user_id=${productData.userId} 
    and product_id=${productData.productId} and bargin_status=0`;
    let newReq = `select * from user_bargain where user_id=${productData.userId} 
    and product_id=${productData.productId}`;
    let getRequestData = `select * from user_bargain where user_id=${productData.userId} and product_id=${productData.productId}`;

    let sql = `INSERT INTO user_bargain (product_id, user_id, bargain_price,is_active,createdby,createddate)
         VALUE (${productData.productId}, ${productData.userId}, '${productData.bargainPrice}',1,${productData.userId}, NOW())
         ON DUPLICATE KEY UPDATE bargain_price = '${productData.bargainPrice}',updateddate=NOW();`;
    dbQuery
      .queryRunner(newReq)
      .then((result) => {
        console.log();
        if (result && result.length != 0) {
          if (result[0].bargin_status == 0) {
            return dbQuery.queryRunner(sql);
          } else {
            return dbQuery.queryRunner(getRequestData);
          }

          // if company reject your request then user will send it again otherwise user cant send request again and again
        } else {
          return dbQuery.queryRunner(sql);
        }
      })
      .then((result) => {
        if (result && result.insertId) {
          productData.id = result.insertId;
          resolve({
            status: 200,
            message: "Request added successfully.",
            data: [productData],
          });
        } else {
          resolve({
            status: 200,
            message: "Check request status",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Request already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getBargainRequestByUser = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select p.*,c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
    c.profileFilename as companyProfileFilename, IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.user_id as bargainUserId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p 
    INNER JOIN company c on c.id = p.company_id
    left Join product_details pd on p.id=pd.product_id and pd.user_id = ${userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${userId}
    where p.is_active=1 and p.is_deleted!=1  and ub.id is not null and ub.user_id = ${userId} group by p.id order by pd.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            element.isAddedCart = element.cartProductQuantity != null ? 1 : 0;
            element.isBarginProduct = element.bargainId != null ? 1 : 0;
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element.images)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          resolve({
            status: 200,
            message: "Fetch bargain request successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "bargain request not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let getBargainRequestByCompany = (userId) =>
  new Promise((resolve, reject) => {
    /* let sql = `select p.*,IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.user_id as bargainUserId, ub.bargain_price, ub.bargin_status,
    ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p 
    left Join product_details pd on p.id=pd.product_id and pd.user_id = ${userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${userId}
    where p.is_active=1 and ub.id is not null and p.company_id in (select id from company where createdby=${userId}) group by p.id order by pd.createddate desc;`;
*/
    let sql = `select p.*,ub.id as bargainId, ub.user_id as bargainUserId, ub.bargain_price, ub.bargin_status,
    c.id as companyId, c.name as companyName, c.profileimage as companyPofileimage,
        c.profileFilename as companyProfileFilename 
        from user_bargain ub 
    INNER JOIN product p on ub.product_id=p.id
    INNER JOIN company c on c.id = p.company_id
    where c.createdby=${userId} and p.is_deleted!=1 order by ub.createddate desc, ub.updateddate desc;`;

    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            element.isAddedCart = element.cartProductQuantity != null ? 1 : 0;
            element.isBarginProduct = element.bargainId != null ? 1 : 0;
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element.images)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          resolve({
            status: 200,
            message: "Fetch bargain request successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Bargain request not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let saveUserAddress = (userAdreess) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO user_address (first_name, last_name, mobile_number,adreess,state,city,pincode,note,is_active,createdby,createddate)
         VALUE ('${userAdreess.firstName}', '${userAdreess.lastName}', '${userAdreess.mobileNumber}','${userAdreess.adreess}',
         '${userAdreess.state}', '${userAdreess.city}', ${userAdreess.pincode},'${userAdreess.note}',1,${userAdreess.userId}, NOW())`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          userAdreess.id = result.insertId;
          resolve({
            status: 200,
            message: "Adreess added successfully.",
            data: [userAdreess],
          });
        } else {
          reject({
            status: 400,
            message: "Adreess not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getUserAddressByUser = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select * from user_address where createdby = ${userId} order by createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Fetch user address successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "Adrress not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let saveProductOrder = (productData) =>
  new Promise((resolve, reject) => {
    let sql = `INSERT INTO product_orders (shipping_address_id,billing_address_id, delivery_notes, delivery_time, total_item,
            total_price,discount,transaction_id,coupancode,is_active,createdby,createddate) 
            VALUES (${productData.shippingAddressId},${productData.billingAddressId}, '${productData.deliveryNotes}', '${productData.deliveryTime}',
             ${productData.totalItem},'${productData.totalPrice}', '${productData.orderDiscount}','${productData.transactionId}',
             '${productData.coupancode}',1,${productData.createdBy}, NOW());`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          productData.id = result.insertId;
          let deleteCartSql = `DELETE FROM cart_item where createdby=${productData.createdBy} and product_id in (`;
          let orderSql = `INSERT INTO orders_item (order_id, product_id,product_size, quantity, item_price, is_active,createdby,createddate) VALUES`;
          productData.products.forEach((element) => {
            deleteCartSql += element.productId + ",";
            orderSql += ` (${result.insertId}, ${element.productId}, '${element.productSize}', ${element.productQuantity}, '${element.itemPrice}', 1,${productData.createdBy}, NOW()),`;
          });
          if (deleteCartSql !== "")
            deleteCartSql = deleteCartSql.substring(
              0,
              deleteCartSql.length - 1
            );
          deleteCartSql += `);`;
          if (orderSql !== "")
            orderSql = orderSql.substring(0, orderSql.length - 1);
          dbQuery.queryRunner(orderSql);

          let transactionSql = `INSERT INTO transaction (transaction_type, total, status, order_id,is_active,createdby,createddate) 
                    VALUES ('${productData.transactionType}', '${productData.totalPrice}', '${productData.paymentStatus}',${result.insertId},1,${productData.createdBy}, NOW());`;
          dbQuery.queryRunner(transactionSql);

          dbQuery.queryRunner(deleteCartSql);

          resolve({
            status: 200,
            message: "Product order added successfully.",
            data: [productData],
          });
        } else {
          reject({
            status: 400,
            message: "Product not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let updateBarginStatus = (productData) =>
  new Promise((resolve, reject) => {
    let sql = `Update user_bargain SET bargin_status=${productData.barginStatus} , bargain_price= '${productData.bargainPrice}', updateddate=NOW() where id=${productData.Id}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "update bargin status successfully.",
            data: [productData],
          });
        } else {
          reject({
            status: 400,
            message: "Bargin not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getWishlistProductByUserId = (userId) =>
  new Promise((resolve, reject) => {
    let sql = `select p.*,IFNULL(pd.is_like,0) as isLike,IFNULL(pd.is_view,0) as isView,IFNULL(pd.is_wishlist,0) as isWishlist,
    ub.id as bargainId, ub.bargain_price, ub.bargin_status, ci.product_size as cartProductSize,ci.product_quantity as cartProductQuantity
    from product p left Join product_details pd on p.id=pd.product_id and pd.user_id = ${userId}
    left Join user_bargain ub on ub.product_id=p.id and ub.user_id = ${userId}  
    LEFT JOIN cart_item ci ON ci.product_id=p.id and ci.createdby= ${userId}
    where p.is_active=1 and p.is_deleted!=1 and pd.is_wishlist=1 group by p.id order by p.createddate desc;`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          result.forEach((element) => {
            element.isAddedCart = element.cartProductQuantity != null ? 1 : 0;
            element.isBarginProduct = element.bargainId != null ? 1 : 0;
            if (element.size) {
              element.size = JSON.parse(JSON.stringify(element.size)).split(
                ","
              );
            } else {
              element.size = [];
            }
            if (element.images)
              element.images = JSON.parse(JSON.stringify(element.images)).split(
                ","
              );
          });
          resolve({
            status: 200,
            message: "Fetch product successfully.",
            data: result,
          });
        } else {
          resolve({
            status: 200,
            message: "product not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

let updateCartProduct = (productData) =>
  new Promise((resolve, reject) => {
    let sql = `UPDATE cart_item SET product_size='${productData.productSize}',product_quantity=${productData.productQuantity},updateddate=NOW() where product_id=${productData.productId} and createdby=${productData.createdBy};`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          productData.id = result.insertId;
          resolve({
            status: 200,
            message: "Product update successfully.",
            data: [productData],
          });
        } else {
          reject({
            status: 400,
            message: "Product not saved.",
            data: result,
          });
        }
      })
      .catch((err) => {
        let message = "";
        if (err.message.includes("ER_DUP_ENTRY"))
          message = "Product already available.";
        if (err.message.includes("ER_NO_REFERENCED_ROW_2"))
          message = "Invalid userId.";
        reject({
          status: 500,
          message: message != "" ? message : err.message,
          data: [],
        });
      });
  });

let getCouponsbyCategory = (category) =>
  new Promise((resolve, reject) => {
    let sql = `select id, coupon_code as couponCode,coupan_startdate as startDate,coupan_enddate as coupanEnddate, coupon_desc as couponDesc, coupon_discount as couponDiscount from coupon_master where isActive=1 and LOWER(coupon_type) = '${category.toLowerCase()}'`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Valid Coupon.",
            data: result,
          });
        } else {
          reject({
            status: 400,
            message: "Invalid coupon.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let getOrdersByUserId = (userId) =>
  new Promise((resolve, reject) => {
    let productDetails;
    let sql = `select po.*,ud.id as shippingId,ud.adreess as shippingAddress,ud.state as shippingState,ud.city as shippingCity,ud.pincode as shippingPincode, ud.note as shippingNote,
    ua.id as billingId, ua.adreess as billingAddress,ua.state as billingState,ua.city as billingCity,ua.pincode as billingPincode, ua.note as billingNote
    from product_orders po
    LEFT JOIN user_address ud ON ud.id=po.shipping_address_id
    LEFT JOIN user_address ua ON ua.id=po.billing_address_id
    where po.createdby= ${userId}`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          productDetails = result;
          let productSql = `select p.*, oi.order_id as orderId, oi.product_id as productId, oi.product_size as orderProductSize, 
                oi.quantity as orderProductQuantity, oi.item_price as orderItemPrice,c.name as companyName,c.profileimage as companyProfileImage,c.location as companyAddress
                from orders_item oi 
                LEFT JOIN product p ON p.id = oi.product_id
                LEFT JOIN company c ON p.company_id = c.id
                where oi.createdby= ${userId};`;
          return dbQuery.queryRunner(productSql);
        } else {
          reject({
            status: 400,
            message: "Order not found.",
            data: result,
          });
        }
      })
      .then((result) => {
        productDetails.forEach((product) => {
          let filterData = result.filter((data) => data.orderId === product.id);
          product.products = filterData;
        });
        resolve({
          status: 200,
          message: "Get order details successfully.",
          data: productDetails,
        });
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err.message,
          data: [],
        });
      });
  });

let deleteUserAddress = (id) =>
  new Promise((resolve, reject) => {
    let sql = `delete from user_address where id=${id};`;
    dbQuery
      .queryRunner(sql)
      .then((result) => {
        if (result && result.length != 0) {
          resolve({
            status: 200,
            message: "Delete address successfully.",
            data: [],
          });
        } else {
          resolve({
            status: 200,
            message: "bargain request not found.",
            data: result,
          });
        }
      })
      .catch((err) => {
        reject({
          status: 500,
          message: err,
          data: [],
        });
      });
  });

module.exports = {
  ///addCategory
  ///getCategoryProduct
  ///getProductListByAdvertiserId
  ///getProductByCategoryId
  ///getSingleProductById
  ///addToCart
  ///getCartItems
  ///addAddress
  ///selectDeliveryAddress
  ///deliveryAddress
  ///selectedDeliveryAddress
  ///selectedDeliveryAddress
  ///placeOrder
  ///payUsingWallet
  ///addToBargain
  ///getBaraginItems
  ///updateBaragin
  ///deleteCartSingleItem
  ///deleteCartAllItems
  ///getUserOrders
  ///getSellerOrders
  ///updateSellerShippingDetails
  ///addToWishList
  ///getWishList
  ///deleteWishListItem
  ///addProductToPopular
  ///getPopularProducts
  ///getSearchProducts
  ///getSearchCompany
  getSearchCompany: (search) =>
    new Promise((resolve, reject) => {
      return getSearchCompany(search)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getSearchProducts: (search) =>
    new Promise((resolve, reject) => {
      return getSearchProducts(search)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getPopularProducts: () =>
    new Promise((resolve, reject) => {
      return getPopularProducts()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  addProductToPopular: (Id) =>
    new Promise((resolve, reject) => {
      return addProductToPopular(Id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  deleteWishListItem: (Id) =>
    new Promise((resolve, reject) => {
      return deleteWishListItem(Id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getWishList: (Id) =>
    new Promise((resolve, reject) => {
      return getWishList(Id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  addToWishList: (data) =>
    new Promise((resolve, reject) => {
      return addToWishList(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  updateSellerShippingDetails: (data) =>
    new Promise((resolve, reject) => {
      return updateSellerShippingDetails(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getSellerOrders: (userId) =>
    new Promise((resolve, reject) => {
      return getSellerOrders(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getUserOrders: (userId) =>
    new Promise((resolve, reject) => {
      return getUserOrders(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  deleteCartAllItems: (userId) =>
    new Promise((resolve, reject) => {
      return deleteCartAllItems(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  deleteCartSingleItem: (cartId) =>
    new Promise((resolve, reject) => {
      return deleteCartSingleItem(cartId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  updateBargin: (data) =>
    new Promise((resolve, reject) => {
      return updateBargin(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getBaraginItems: (id) =>
    new Promise((resolve, reject) => {
      return getBaraginItems(id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  addToBargain: (data) =>
    new Promise((resolve, reject) => {
      return addToBargain(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  payUsingWallet: (userId, requestAmount) =>
    new Promise((resolve, reject) => {
      return payUsingWallet(userId, requestAmount)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  placeOrder: (data) =>
    new Promise((resolve, reject) => {
      return placeOrder(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  selectedDeliveryAddress: (id) =>
    new Promise((resolve, reject) => {
      return selectedDeliveryAddress(id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  deliveryAddress: (id) =>
    new Promise((resolve, reject) => {
      return deliveryAddress(id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  selectDeliveryAddress: (addressId, userId) =>
    new Promise((resolve, reject) => {
      return selectDeliveryAddress(addressId, userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  addAddress: (data) =>
    new Promise((resolve, reject) => {
      return addAddress(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getCartItems: (Id) =>
    new Promise((resolve, reject) => {
      return getCartItems(Id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  addToCart: (data) =>
    new Promise((resolve, reject) => {
      return addToCart(data)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getSingleProductById: (Id) =>
    new Promise((resolve, reject) => {
      return getSingleProductById(Id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getProductByCategoryId: (Id) =>
    new Promise((resolve, reject) => {
      return getProductByCategoryId(Id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getProductListByAdvertiserId: (advertiserId) =>
    new Promise((resolve, reject) => {
      return getProductListByAdvertiserId(advertiserId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getCategoryProduct: () =>
    new Promise((resolve, reject) => {
      return getCategoryProduct()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  addCategory: (categoryData) =>
    new Promise((resolve, reject) => {
      return addCategory(categoryData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  saveProduct: (productData) =>
    new Promise((resolve, reject) => {
      return saveProduct(productData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getProductList: (companyId) =>
    new Promise((resolve, reject) => {
      getProductList(companyId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getAllProductList: () =>
    new Promise((resolve, reject) => {
      getAllProductList()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  deleteProduct: (id) =>
    new Promise((resolve, reject) => {
      deleteProduct(id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  updateProduct: (productData) =>
    new Promise((resolve, reject) => {
      updateProduct(productData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getProductCategory: () =>
    new Promise((resolve, reject) => {
      getProductCategory()
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getProductByProductId: (productId, userId) =>
    new Promise((resolve, reject) => {
      getProductByProductId(productId, userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getProductByUserId: (userId) =>
    new Promise((resolve, reject) => {
      getProductByUserId(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getProductByCategory: (categoryId, userId) =>
    new Promise((resolve, reject) => {
      getProductByCategory(categoryId, userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getProductByCompanyId: (companyId) =>
    new Promise((resolve, reject) => {
      getProductByCompanyId(companyId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getNewProduct: (userid) =>
    new Promise((resolve, reject) => {
      getNewProduct(userid)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getSearchList: (userid) =>
    new Promise((resolve, reject) => {
      getSearchList(userid)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveCartItem: (productsData) =>
    new Promise((resolve, reject) => {
      saveCartItem(productsData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  saveSearchProduct: (productsData) =>
    new Promise((resolve, reject) => {
      saveSearchProduct(productsData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getSearchResult: (productsData) =>
    new Promise((resolve, reject) => {
      getSearchResult(productsData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getSimilarsearch: (productsData) =>
    new Promise((resolve, reject) => {
      getSimilarsearch(productsData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getCartItemByUser: (userId) =>
    new Promise((resolve, reject) => {
      getCartItemByUser(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  saveProductDetails: (productModel) =>
    new Promise((resolve, reject) => {
      return saveProductDetails(productModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getProductAds: (userId) =>
    new Promise((resolve, reject) => {
      getProductAds(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getRecentlyViewProducts: (userid) =>
    new Promise((resolve, reject) => {
      getRecentlyViewProducts(userid)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  deleteCartItembyCartId: (cartid) =>
    new Promise((resolve, reject) => {
      deleteCartItembyCartId(cartid)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getOrderList: (userId) =>
    new Promise((resolve, reject) => {
      getOrderList(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveBargainRequest: (bargainData) =>
    new Promise((resolve, reject) => {
      saveBargainRequest(bargainData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getBargainRequestByUser: (userId) =>
    new Promise((resolve, reject) => {
      getBargainRequestByUser(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getBargainRequestByCompany: (userId) =>
    new Promise((resolve, reject) => {
      getBargainRequestByCompany(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveUserAddress: (userAdreess) =>
    new Promise((resolve, reject) => {
      saveUserAddress(userAdreess)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getUserAddressByUser: (userId) =>
    new Promise((resolve, reject) => {
      getUserAddressByUser(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  saveProductOrder: (productsData) =>
    new Promise((resolve, reject) => {
      saveProductOrder(productsData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  updateBarginStatus: (productsData) =>
    new Promise((resolve, reject) => {
      updateBarginStatus(productsData)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getWishlistProductByUserId: (userId) =>
    new Promise((resolve, reject) => {
      getWishlistProductByUserId(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  updateCartProduct: (productModel) =>
    new Promise((resolve, reject) => {
      return updateCartProduct(productModel)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  getCouponsbyCategory: (category) =>
    new Promise((resolve, reject) => {
      getCouponsbyCategory(category)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
  getOrdersByUserId: (userId) =>
    new Promise((resolve, reject) => {
      getOrdersByUserId(userId)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),

  deleteUserAddress: (id) =>
    new Promise((resolve, reject) => {
      deleteUserAddress(id)
        .then((result) => {
          if (result && result.status == 200) {
            resolve(result);
          } else {
            reject(result);
          }
        })
        .catch((err) => {
          reject(err);
        });
    }),
};
