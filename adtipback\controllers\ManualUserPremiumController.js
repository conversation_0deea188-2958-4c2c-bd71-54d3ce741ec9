const ManualUserPremiumService = require('../services/ManualUserPremiumService');

exports.manualCreateUserPremium = async (req, res) => {
  try {
    const { userId, planId, duration, note } = req.body;
    if (!userId || !planId || !duration) {
      return res.status(400).json({ status: false, message: 'userId, planId, and duration are required' });
    }
    const result = await ManualUserPremiumService.manualCreateUserPremium(userId, planId, duration, note);
    res.json({ status: true, message: 'User premium created manually', data: result });
  } catch (err) {
    res.status(500).json({ status: false, message: 'Error creating user premium', error: err.message });
  }
};

exports.manualCancelUserPremium = async (req, res) => {
  try {
    const { userId, note } = req.body;
    if (!userId) {
      return res.status(400).json({ status: false, message: 'userId is required' });
    }
    const result = await ManualUserPremiumService.manualCancelUserPremium(userId, note);
    res.json({ status: true, message: 'User premium cancelled manually', data: result });
  } catch (err) {
    res.status(500).json({ status: false, message: 'Error cancelling user premium', error: err.message });
  }
}; 