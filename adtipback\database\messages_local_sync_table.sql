-- =====================================================
-- MESSAGES LOCAL SYNC TABLE
-- =====================================================
-- Tracks synchronization status between local database and server
-- Helps prevent data corruption during sync operations

CREATE TABLE IF NOT EXISTS messages_local_sync (
    id INT PRIMARY KEY AUTO_INCREMENT,
    message_id INT NOT NULL COMMENT 'Reference to messages table',
    conversation_id INT NOT NULL COMMENT 'Reference to conversations table',
    local_message_id VARCHAR(255) NULL COMMENT 'Local/temp message ID from client',
    local_timestamp TIMESTAMP NOT NULL COMMENT 'Timestamp when message was created locally',
    server_timestamp TIMESTAMP NULL COMMENT 'Timestamp when message was synced to server',
    sync_status ENUM('pending', 'synced', 'conflict', 'failed') DEFAULT 'pending',
    sync_attempts INT DEFAULT 0 COMMENT 'Number of sync attempts',
    last_sync_attempt TIMESTAMP NULL COMMENT 'Last time sync was attempted',
    error_message TEXT NULL COMMENT 'Error details if sync failed',
    client_data JSON NULL COMMENT 'Original client message data for conflict resolution',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_message_id (message_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sync_status (sync_status),
    INDEX idx_local_message_id (local_message_id),
    INDEX idx_local_timestamp (local_timestamp),
    INDEX idx_conversation_sync_status (conversation_id, sync_status),
    
    -- Unique constraint to prevent duplicate sync records
    UNIQUE KEY unique_message_sync (message_id, local_message_id),
    
    -- Foreign key constraints
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- INDEXES FOR EXISTING MESSAGES TABLE OPTIMIZATION
-- =====================================================
-- Add additional indexes to messages table for better sync performance

-- Index for finding messages by local/temp ID
ALTER TABLE messages 
ADD INDEX idx_temp_id (fcm_message_id),
ADD COLUMN temp_id VARCHAR(255) NULL COMMENT 'Temporary ID from client for sync tracking' AFTER fcm_message_id,
ADD INDEX idx_temp_id_lookup (temp_id);

-- =====================================================
-- SYNC STATUS TRACKING PROCEDURES
-- =====================================================

DELIMITER //

-- Procedure to mark message as synced
CREATE PROCEDURE MarkMessageSynced(
    IN p_message_id INT,
    IN p_local_message_id VARCHAR(255),
    IN p_conversation_id INT
)
BEGIN
    INSERT INTO messages_local_sync (
        message_id, 
        conversation_id, 
        local_message_id, 
        local_timestamp, 
        server_timestamp, 
        sync_status
    ) VALUES (
        p_message_id, 
        p_conversation_id, 
        p_local_message_id, 
        NOW(), 
        NOW(), 
        'synced'
    ) ON DUPLICATE KEY UPDATE
        server_timestamp = NOW(),
        sync_status = 'synced',
        sync_attempts = sync_attempts + 1,
        last_sync_attempt = NOW(),
        updated_at = NOW();
END //

-- Procedure to mark sync as failed
CREATE PROCEDURE MarkSyncFailed(
    IN p_message_id INT,
    IN p_local_message_id VARCHAR(255),
    IN p_conversation_id INT,
    IN p_error_message TEXT
)
BEGIN
    INSERT INTO messages_local_sync (
        message_id, 
        conversation_id, 
        local_message_id, 
        local_timestamp, 
        sync_status,
        sync_attempts,
        last_sync_attempt,
        error_message
    ) VALUES (
        p_message_id, 
        p_conversation_id, 
        p_local_message_id, 
        NOW(), 
        'failed',
        1,
        NOW(),
        p_error_message
    ) ON DUPLICATE KEY UPDATE
        sync_status = 'failed',
        sync_attempts = sync_attempts + 1,
        last_sync_attempt = NOW(),
        error_message = p_error_message,
        updated_at = NOW();
END //

DELIMITER ;

-- =====================================================
-- INITIAL DATA MIGRATION
-- =====================================================
-- Mark existing messages as synced to establish baseline

INSERT INTO messages_local_sync (
    message_id, 
    conversation_id, 
    local_message_id, 
    local_timestamp, 
    server_timestamp, 
    sync_status
)
SELECT 
    id,
    conversation_id,
    CONCAT('migrated_', id),
    created_at,
    created_at,
    'synced'
FROM messages 
WHERE NOT EXISTS (
    SELECT 1 FROM messages_local_sync 
    WHERE messages_local_sync.message_id = messages.id
);
