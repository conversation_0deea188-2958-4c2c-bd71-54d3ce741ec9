# future
Y+1={0} aasta pärast
M+1={0} kuu pärast
W+1={0} nädala pärast
D+1={0} päeva pärast
H+1={0} tunni pärast
N+1={0} minuti pärast
S+1={0} sekundi pärast

Y+5={0} aasta pärast
M+5={0} kuu pärast
W+5={0} nädala pärast
D+5={0} päeva pärast
H+5={0} tunni pärast
N+5={0} minuti pärast
S+5={0} sekundi pärast

# past
Y-1={0} aasta eest
M-1={0} kuu eest
W-1={0} nädala eest
D-1={0} päeva eest
H-1={0} tunni eest
N-1={0} minuti eest
S-1={0} sekundi eest

Y-5={0} aasta eest
M-5={0} kuu eest
W-5={0} nädala eest
D-5={0} päeva eest
H-5={0} tunni eest
N-5={0} minuti eest
S-5={0} sekundi eest

# current time
now=nüüd

# future (short)
y+1={0} a pärast
m+1={0} kuu pärast
w+1={0} näd pärast
d+1={0} p pärast
h+1={0} t pärast
n+1={0} min pärast
s+1={0} sek pärast

y+5={0} a pärast
m+5={0} kuu pärast
w+5={0} näd pärast
d+5={0} p pärast
h+5={0} t pärast
n+5={0} min pärast
s+5={0} sek pärast

# past (short)
y-1={0} a eest
m-1={0} kuu eest
w-1={0} näd eest
d-1={0} p eest
h-1={0} t eest
n-1={0} min eest
s-1={0} sek eest

y-5={0} a eest
m-5={0} kuu eest
w-5={0} näd eest
d-5={0} p eest
h-5={0} t eest
n-5={0} min eest
s-5={0} sek eest

# relative day
yesterday=eile
today=täna
tomorrow=homme

mon-=eelmine esmaspäev
mon+=järgmine esmaspäev
tue-=eelmine teisipäev
tue+=järgmine teisipäev
wed-=eelmine kolmapäev
wed+=järgmine kolmapäev
thu-=eelmine neljapäev
thu+=järgmine neljapäev
fri-=eelmine reede
fri+=järgmine reede
sat-=eelmine laupäev
sat+=järgmine laupäev
sun-=eelmine pühapäev
sun+=järgmine pühapäev
