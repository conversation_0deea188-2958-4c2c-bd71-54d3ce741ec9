import * as React from "react";
import { Outlet } from "react-router-dom";
import AdTipSidebar from "./components/ui/AdTipSidebar";
import Navbar from "./components/Navbar";
import { cn } from "./lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { SidebarProvider, useSidebar } from "./contexts/SidebarContext";

const AppLayoutContent = () => {
  const isMobile = useIsMobile();
  const { isCollapsed } = useSidebar();

  return (
    <div className={cn(
      "min-h-screen bg-gray-50 grid transition-all duration-300 ease-in-out",
      isMobile
        ? "grid-rows-[auto_1fr_auto] grid-cols-1"
        : "grid-rows-[auto_1fr] grid-cols-[auto_1fr]"
    )}>
      {/* Header */}
      <header className={cn(
        "z-50 bg-white shadow-sm border-b border-gray-200",
        isMobile ? "col-span-1" : "col-span-2"
      )}>
        <Navbar />
      </header>

      {/* Sidebar - Desktop only */}
      {!isMobile && (
        <aside className={cn(
          "bg-white border-r border-gray-200 overflow-y-auto transition-all duration-300",
          isCollapsed ? "w-16" : "w-64"
        )}>
          <AdTipSidebar />
        </aside>
      )}

      {/* Main Content */}
      <main className={cn(
        "overflow-y-auto bg-gray-50",
        isMobile ? "px-4 pb-20" : "px-6 py-4", // Add bottom padding for mobile nav
        !isMobile && isCollapsed ? "ml-0" : ""
      )}>
        <Outlet />
      </main>

      {/* Mobile Sidebar Overlay */}
      {isMobile && (
        <AdTipSidebar />
      )}
    </div>
  );
};

const AppLayout = () => (
  <SidebarProvider>
    <AppLayoutContent />
  </SidebarProvider>
);

export default AppLayout;
