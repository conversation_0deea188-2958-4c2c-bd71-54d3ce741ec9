# months
M(a)_1=янв.
M(a)_2=фев.
M(a)_3=мар.
M(a)_4=апр.
M(a)_5=май
M(a)_6=июн.
M(a)_7=июл.
M(a)_8=авг.
M(a)_9=сен.
M(a)_10=окт.
M(a)_11=ноя.
M(a)_12=дек.

M(n)_1=Я
M(n)_2=Ф
M(n)_3=М
M(n)_4=А
M(n)_5=М
M(n)_6=И
M(n)_7=И
M(n)_8=А
M(n)_9=С
M(n)_10=О
M(n)_11=Н
M(n)_12=Д

M(w)_1=январь
M(w)_2=февраль
M(w)_3=март
M(w)_4=апрель
M(w)_5=май
M(w)_6=июнь
M(w)_7=июль
M(w)_8=август
M(w)_9=сентябрь
M(w)_10=октябрь
M(w)_11=ноябрь
M(w)_12=декабрь

M(A)_1=Янв
M(A)_2=Фев
M(A)_3=Мар
M(A)_4=Апр
M(A)_5=Май
M(A)_6=Июн
M(A)_7=Июл
M(A)_8=Авг
M(A)_9=Сен
M(A)_10=Окт
M(A)_11=Ноя
M(A)_12=Дек

M(N)_1=Я
M(N)_2=Ф
M(N)_3=М
M(N)_4=А
M(N)_5=М
M(N)_6=И
M(N)_7=И
M(N)_8=А
M(N)_9=С
M(N)_10=О
M(N)_11=Н
M(N)_12=Д

M(W)_1=Январь
M(W)_2=Февраль
M(W)_3=Март
M(W)_4=Апрель
M(W)_5=Май
M(W)_6=Июнь
M(W)_7=Июль
M(W)_8=Август
M(W)_9=Сентябрь
M(W)_10=Октябрь
M(W)_11=Ноябрь
M(W)_12=Декабрь

# weekdays
D(a)_1=дүй.
D(a)_2=шейш.
D(a)_3=шарш.
D(a)_4=бейш.
D(a)_5=жума
D(a)_6=ишм.
D(a)_7=жек.

D(n)_1=Д
D(n)_2=Ш
D(n)_3=Ш
D(n)_4=Б
D(n)_5=Ж
D(n)_6=И
D(n)_7=Ж

D(s)_1=дш.
D(s)_2=шш.
D(s)_3=шр.
D(s)_4=бш.
D(s)_5=жм.
D(s)_6=иш.
D(s)_7=жш.

D(w)_1=дүйшөмбү
D(w)_2=шейшемби
D(w)_3=шаршемби
D(w)_4=бейшемби
D(w)_5=жума
D(w)_6=ишемби
D(w)_7=жекшемби

D(A)_1=дүй.
D(A)_2=шейш.
D(A)_3=шарш.
D(A)_4=бейш.
D(A)_5=жума
D(A)_6=ишм.
D(A)_7=жек.

D(N)_1=Д
D(N)_2=Ш
D(N)_3=Ш
D(N)_4=Б
D(N)_5=Ж
D(N)_6=И
D(N)_7=Ж

D(S)_1=дш.
D(S)_2=шш.
D(S)_3=шр.
D(S)_4=бш.
D(S)_5=жм.
D(S)_6=иш.
D(S)_7=жш.

D(W)_1=дүйшөмбү
D(W)_2=шейшемби
D(W)_3=шаршемби
D(W)_4=бейшемби
D(W)_5=жума
D(W)_6=ишемби
D(W)_7=жекшемби

# quarters
Q(a)_1=1-чей.
Q(a)_2=2-чей.
Q(a)_3=3-чей.
Q(a)_4=4-чей.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1-чейрек
Q(w)_2=2-чейрек
Q(w)_3=3-чейрек
Q(w)_4=4-чейрек

Q(A)_1=1-ч.
Q(A)_2=2-ч.
Q(A)_3=3-ч.
Q(A)_4=4-ч.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1-чейрек
Q(W)_2=2-чейрек
Q(W)_3=3-чейрек
Q(W)_4=4-чейрек

# day-period-rules
T0600=morning1
T1200=afternoon1
T1800=evening1
T2100=night1

# day-period-translations
P(a)_midnight=түн ортосу
P(a)_am=тң
P(a)_noon=чак түш
P(a)_pm=тк
P(a)_morning1=эртең менен
P(a)_afternoon1=түштөн кийин
P(a)_evening1=кечинде
P(a)_night1=түн ичинде

P(n)_midnight=түн орт
P(n)_am=тң
P(n)_noon=чт
P(n)_pm=тк
P(n)_morning1=эртң мн
P(n)_afternoon1=түшт кйн
P(n)_evening1=кечк
P(n)_night1=түн

P(w)_midnight=түн ортосу
P(w)_am=таңкы
P(w)_noon=чак түш
P(w)_pm=түштөн кийинки
P(w)_morning1=эртең менен
P(w)_afternoon1=түштөн кийин
P(w)_evening1=кечинде
P(w)_night1=түн ичинде

P(A)_midnight=түн ортосу
P(A)_am=тң
P(A)_noon=чак түш
P(A)_pm=тк
P(A)_morning1=эртең менен
P(A)_afternoon1=түштөн кийин
P(A)_evening1=кечкурун
P(A)_night1=түн

P(N)_midnight=түн ортосу
P(N)_am=тң
P(N)_noon=чак түш
P(N)_pm=тк
P(N)_morning1=эртең менен
P(N)_afternoon1=түштөн кийин
P(N)_evening1=кечкурун
P(N)_night1=түн

P(W)_midnight=түн ортосу
P(W)_am=таңкы
P(W)_noon=чак түш
P(W)_pm=түштөн кийинки
P(W)_morning1=эртең менен
P(W)_afternoon1=түштөн кийин
P(W)_evening1=кечкурун
P(W)_night1=түн

# eras
E(w)_0=биздин заманга чейин
E(w|alt)_0=б.з.ч.
E(w)_1=биздин заман

E(a)_0=б.з.ч.
E(a)_1=б.з.

E(n)_0=б.з.ч.
E(n)_1=б.з.

# format patterns
F(f)_d=y-'ж'., d-MMMM, EEEE
F(l)_d=y-'ж'., d-MMMM
F(m)_d=y-'ж'., d-MMM
F(s)_d=d/M/yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd-MM
F_MMMd=d-MMM
F_MMMMd=d-MMMM
F_y=y
F_yM=y-MM
F_yMMM=y-'ж'. MMM
F_yMMMM=y-'ж'., MMMM
F_yQQQ=y-'ж'., QQQ
F_yQQQQ=y-'ж'., QQQQ
F_yw=Y-'жылдын' w-'аптасы'

I={0} - {1}

# labels of elements
L_era=заман
L_year=жыл
L_quarter=чейрек
L_month=ай
L_week=апта
L_day=күн
L_weekday=аптанын күнү
L_dayperiod=ТЧ/ТК
L_hour=саат
L_minute=мүнөт
L_second=секунд
L_zone=убакыт алкагы
