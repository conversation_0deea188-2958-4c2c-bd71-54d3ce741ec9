
#ifndef BOOST_MPL_SET_SET0_HPP_INCLUDED
#define BOOST_MPL_SET_SET0_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2003-2004
// Copyright <PERSON> 2003-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

#include <boost/mpl/set/aux_/at_impl.hpp>
#include <boost/mpl/set/aux_/clear_impl.hpp>
//#include <boost/mpl/set/aux_/O1_size.hpp>
#include <boost/mpl/set/aux_/size_impl.hpp>
#include <boost/mpl/set/aux_/empty_impl.hpp>
#include <boost/mpl/set/aux_/insert_impl.hpp>
#include <boost/mpl/set/aux_/insert_range_impl.hpp>
#include <boost/mpl/set/aux_/erase_impl.hpp>
#include <boost/mpl/set/aux_/erase_key_impl.hpp>
#include <boost/mpl/set/aux_/has_key_impl.hpp>
#include <boost/mpl/set/aux_/key_type_impl.hpp>
#include <boost/mpl/set/aux_/value_type_impl.hpp>
#include <boost/mpl/set/aux_/begin_end_impl.hpp>
#include <boost/mpl/set/aux_/iterator.hpp>
#include <boost/mpl/set/aux_/item.hpp>
#include <boost/mpl/set/aux_/set0.hpp>
#include <boost/mpl/set/aux_/tag.hpp>

#endif // BOOST_MPL_SET_SET0_HPP_INCLUDED
