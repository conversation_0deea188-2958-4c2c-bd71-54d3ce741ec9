[{"directory": "/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/autolinking/src/main/jni -isystem /Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -o CMakeFiles/appmodules.dir/Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c /Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "file": "/Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"}, {"directory": "/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=aarch64-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/autolinking/src/main/jni -isystem /Users/<USER>/.gradle/caches/8.14.1/transforms/b6a6ccfe82730aba80016e7d9940f54a/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -o CMakeFiles/appmodules.dir/OnLoad.cpp.o -c /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp", "file": "/Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"}]