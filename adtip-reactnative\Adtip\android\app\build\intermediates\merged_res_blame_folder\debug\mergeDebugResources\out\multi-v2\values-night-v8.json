{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-night-v8/values-night-v8.xml", "map": [{"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "20,21,22,23,24,25,26,53", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "916,986,1070,1154,1250,1352,1454,4436", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "981,1065,1149,1245,1347,1449,1543,4520"}}, {"source": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,5", "startColumns": "4,4", "startOffsets": "55,200", "endLines": "4,7", "endColumns": "9,9", "endOffsets": "195,340"}, "to": {"startLines": "14,17", "startColumns": "4,4", "startOffsets": "626,771", "endLines": "16,19", "endColumns": "9,9", "endOffsets": "766,911"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1548,1623,1734,1823,1924,2031,2138,2237,2344,2447,2574,2662,2786,2888,2990,3106,3208,3322,3450,3566,3688,3824,3944,4078,4198,4310,4525,4642,4766,4896,5018,5156,5290,5406", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1618,1729,1818,1919,2026,2133,2232,2339,2442,2569,2657,2781,2883,2985,3101,3203,3317,3445,3561,3683,3819,3939,4073,4193,4305,4431,4637,4761,4891,5013,5151,5285,5401,5521"}}, {"source": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "6,12,7,4,2,3,11,13,5,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "254,544,305,157,57,105,499,591,204,355,402,451", "endColumns": "49,45,48,45,46,50,43,47,48,45,47,46", "endOffsets": "299,585,349,198,99,151,538,634,248,396,445,493"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,105,151,200,246,293,344,388,436,485,531,579", "endColumns": "49,45,48,45,46,50,43,47,48,45,47,46", "endOffsets": "100,146,195,241,288,339,383,431,480,526,574,621"}}]}]}