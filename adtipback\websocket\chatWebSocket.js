const redisClient = require("../config/redis");
const Auth = require("../config/auth"); // Assuming you have an Auth module for token verification
const ChatService = require("../services/chatService");

// Helper: Add a wsId to a user's session list in Redis
async function addUserWebSocket(userId, wsId) {
  let wsIds = JSON.parse(await redisClient.get(`user:${userId}`) || '[]');
  if (!wsIds.includes(wsId)) wsIds.push(wsId);
  await redisClient.set(`user:${userId}`, JSON.stringify(wsIds));
}

// Helper: Remove a wsId from a user's session list in Redis
async function removeUserWebSocket(userId, wsId) {
  let wsIds = JSON.parse(await redisClient.get(`user:${userId}`) || '[]');
  wsIds = wsIds.filter(id => id !== wsId);
  if (wsIds.length) {
    await redisClient.set(`user:${userId}`, JSON.stringify(wsIds));
  } else {
    await redisClient.del(`user:${userId}`);
  }
}

// Helper: Get all WebSocket clients for a user from the global clients set
function getUserWebSockets(userId, wss) {
  let sockets = [];
  if (wss && wss.clients) {
    wss.clients.forEach((client) => {
      if (client.isAlive && client.userId === userId) {
        sockets.push(client);
      }
    });
  }
  return sockets;
}

// Helper: Broadcast message to all connections of a specific user
function broadcastToUser(userId, message, wss, excludeWs = null) {
  if (!wss || !wss.clients) return;
  
  wss.clients.forEach((client) => {
    if (client.isAlive && 
        client.userId === userId && 
        client.readyState === 1 && // WebSocket.OPEN
        client !== excludeWs) {
      try {
        client.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error broadcasting to user:', error);
      }
    }
  });
}

const setupChatWebSocket = async (ws, req, wss) => {
  console.log("New WebSocket connection for chat");
  ws.isAlive = true;
  ws.on('pong', () => { ws.isAlive = true; });

  // Authenticate WebSocket connection using token
  const urlParams = new URLSearchParams(req.url.split('?')[1]);
  const token = urlParams.get('token');
  console.log('WebSocket connection attempt with token:', token ? 'Present' : 'Missing');
  
  if (!token) {
    console.error('No token provided for WebSocket connection');
    ws.send(JSON.stringify({ type: "error", message: "No token provided" }));
    ws.close();
    return;
  }

  let userId;
  try {
    const decoded = await Auth.verifyTokenWS(token); // Custom method to verify token
    console.log('Decoded token:', decoded);
    
    // Extract user ID from different possible locations in the token
    if (decoded.data && decoded.data.id) {
      userId = decoded.data.id; // Token format: { data: { id: 123 } }
    } else if (decoded.user && decoded.user.id) {
      userId = decoded.user.id; // Token format: { user: { id: 123 } }
    } else if (decoded.id) {
      userId = decoded.id; // Token format: { id: 123 }
    } else if (decoded.userId) {
      userId = decoded.userId; // Token format: { userId: 123 }
    } else if (decoded.user_id) {
      userId = decoded.user_id; // Token format: { user_id: 123 }
    } else if (decoded.sub) {
      userId = decoded.sub; // Standard JWT subject field
    }
    
    if (!userId) {
      console.error('No user ID found in token. Token structure:', JSON.stringify(decoded, null, 2));
      ws.send(JSON.stringify({ type: "error", message: "Invalid token - no user ID found" }));
      ws.close();
      return;
    }
    
    // Ensure userId is a number
    userId = parseInt(userId);
    console.log('WebSocket authenticated successfully for user:', userId);
  } catch (err) {
    console.error('WebSocket authentication failed:', err.message);
    ws.send(JSON.stringify({ type: "error", message: "Invalid token" }));
    ws.close();
    return;
  }

  ws.userId = userId;
  ws.wsId = ws._socket.remoteAddress + ':' + ws._socket.remotePort + ':' + Date.now();
  await addUserWebSocket(userId, ws.wsId);

  console.log(`User ${userId} connected to chat WebSocket`);
  ws.send(JSON.stringify({ type: "connection", message: "Connected to chat WebSocket" }));

  // Handle incoming messages
  ws.on("message", async (message) => {
    try {
      let data;
      try {
        data = JSON.parse(message);
      } catch (err) {
        ws.send(JSON.stringify({ type: "error", message: "Invalid JSON" }));
        return;
      }
      
      if (!data.type) {
        ws.send(JSON.stringify({ type: "error", message: "Missing message type" }));
        return;
      }
      
      switch (data.type) {
        case "message": {
          console.log('Processing message:', data);
          
          // Validate required fields
          if (!data.message || !data.receiverId) {
            console.error('Missing required fields:', { message: data.message, receiverId: data.receiverId });
            ws.send(JSON.stringify({ type: "error", message: "Missing message or receiverId" }));
            return;
          }
          
          if (!ws.userId) {
            console.error('No userId in WebSocket connection');
            // Try to use userId from the message as fallback
            if (data.userId && typeof data.userId === 'number') {
              console.log('Using userId from message as fallback:', data.userId);
              ws.userId = data.userId;
            } else {
              ws.send(JSON.stringify({ type: "error", message: "User not authenticated" }));
              return;
            }
          }
          
          // Save message to database
          const messageData = {
            message: data.message,
            userId: ws.userId,
            receiverId: parseInt(data.receiverId),
            parentId: data.parentId || 0,
            chat_type: (data.chat_type !== undefined && data.chat_type !== null && data.chat_type !== '') ? data.chat_type : "text",
            chat_type_id_value: (data.chat_type_id_value !== undefined && data.chat_type_id_value !== null) ? data.chat_type_id_value : 0,
          };
          
          console.log('Attempting to save message with data:', messageData);
          
          try {
            const savedMessage = await ChatService.savemessages(messageData);
            console.log('Message save result:', savedMessage);
            
            if (savedMessage && savedMessage.status === 200 && savedMessage.data && savedMessage.data[0]) {
              const messageToSend = {
                type: "message",
                data: {
                  id: savedMessage.data[0].messageId,
                  sender: ws.userId,
                  receiver: parseInt(data.receiverId),
                  message: data.message,
                  createddate: new Date().toISOString(),
                  is_seen: false,
                }
              };
              
              // Send confirmation to sender with tempId for tracking
              ws.send(JSON.stringify({
                type: "message_sent",
                data: messageToSend.data,
                tempId: data.tempId // Include tempId so frontend can update the right message
              }));
              
              // Broadcast to receiver if they're online
              broadcastToUser(parseInt(data.receiverId), messageToSend, wss, ws);
              
              console.log('Message saved and broadcasted successfully');
            } else {
              console.error('Failed to save message - invalid response:', savedMessage);
              ws.send(JSON.stringify({ 
                type: "error", 
                message: (savedMessage && savedMessage.message) || "Failed to save message - invalid response" 
              }));
            }
          } catch (error) {
            console.error('Error saving message - exception:', error);
            console.error('Error stack:', error.stack);
            ws.send(JSON.stringify({ 
              type: "error", 
              message: `Failed to save message: ${error.message}` 
            }));
          }
          break;
        }
        
        case "typing": {
          if (!data.receiverId) return;
          
          // Broadcast typing indicator to receiver
          const typingMessage = {
            type: "typing",
            userId: ws.userId,
            receiverId: data.receiverId,
          };
          
          broadcastToUser(data.receiverId, typingMessage, wss, ws);
          break;
        }
        
        case "read": {
          if (!data.messageId || !data.senderId) return;
          
          try {
            // Update read status
            const readData = { id: data.messageId, isSeen: 1 };
            const readResult = await ChatService.saveticks(readData);
            if (readResult.status === 200) {
              // Confirm to sender
              ws.send(JSON.stringify({
                type: "read_confirmed",
                messageId: data.messageId,
              }));
              
              // Notify message sender that their message was read
              const readMessage = {
                type: "read",
                messageId: data.messageId,
                readBy: ws.userId,
              };
              
              broadcastToUser(data.senderId, readMessage, wss, ws);
            }
          } catch (error) {
            console.error('Error marking message as read:', error);
          }
          break;
        }
        
        case "ping": {
          // Respond to ping to keep connection alive
          ws.send(JSON.stringify({ type: "pong" }));
          break;
        }
        
        default:
          ws.send(JSON.stringify({ type: "error", message: "Invalid message type" }));
      }
    } catch (err) {
      console.error('WebSocket message error:', err);
      ws.send(JSON.stringify({ type: "error", message: "Internal server error" }));
    }
  });

  // Handle disconnection
  ws.on("close", async (code, reason) => {
    console.log(`User ${userId} disconnected from chat WebSocket. Code: ${code}, Reason: ${reason}`);
    
    // Clear keep alive interval
    if (ws.keepAliveInterval) {
      clearInterval(ws.keepAliveInterval);
    }
    
    await removeUserWebSocket(userId, ws.wsId);
  });

  // Handle errors
  ws.on("error", (error) => {
    console.error(`WebSocket error for user ${userId}:`, error);
    // Don't close the connection on error, let it try to recover
  });

  // Keep connection alive with periodic ping
  const keepAliveInterval = setInterval(() => {
    if (ws.readyState === 1) { // WebSocket.OPEN
      try {
        ws.ping();
        console.log(`Ping sent to user ${userId}`);
      } catch (error) {
        console.error(`Error pinging user ${userId}:`, error);
        clearInterval(keepAliveInterval);
      }
    } else {
      console.log(`WebSocket not open for user ${userId}, clearing ping interval`);
      clearInterval(keepAliveInterval);
    }
  }, 25000); // Ping every 25 seconds (faster)

  // Store interval reference for cleanup
  ws.keepAliveInterval = keepAliveInterval;
};

module.exports = setupChatWebSocket;
