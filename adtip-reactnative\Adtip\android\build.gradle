buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
        kotlinVersion = "2.0.21"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath ('com.google.gms:google-services:4.4.2')
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
    }
}

apply plugin: "com.facebook.react.rootproject"

// Add repositories configuration for all projects
allprojects {
    repositories {
        google()
        mavenCentral()
        
        // Commented out PubScale integration - June 2, 2025
        // Add repository for PubScale SDK
        /* 
        maven {
            url "https://android-sdk.is.com/"
        }
        
        // Try alternative repository for PubScale
        maven {
            url "https://dl.bintray.com/ironsource-mobile/android-sdk"
        }
        */
        
        // Maven Central as a fallback
        maven {
            url "https://repo1.maven.org/maven2/"
        }
        
        // Fallback for JitPack packages
        maven { url 'https://jitpack.io' }
    }
}
