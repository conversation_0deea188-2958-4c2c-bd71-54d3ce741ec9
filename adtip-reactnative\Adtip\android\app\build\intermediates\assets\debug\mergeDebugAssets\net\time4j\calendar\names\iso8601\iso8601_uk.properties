# months
M(a)_1=січ.
M(a)_2=лют.
M(a)_3=бер.
M(a)_4=квіт.
M(a)_5=трав.
M(a)_6=черв.
M(a)_7=лип.
M(a)_8=серп.
M(a)_9=вер.
M(a)_10=жовт.
M(a)_11=лист.
M(a)_12=груд.

M(n)_1=с
M(n)_2=л
M(n)_3=б
M(n)_4=к
M(n)_5=т
M(n)_6=ч
M(n)_7=л
M(n)_8=с
M(n)_9=в
M(n)_10=ж
M(n)_11=л
M(n)_12=г

M(w)_1=січня
M(w)_2=лютого
M(w)_3=березня
M(w)_4=квітня
M(w)_5=травня
M(w)_6=червня
M(w)_7=липня
M(w)_8=серпня
M(w)_9=вересня
M(w)_10=жовтня
M(w)_11=листопада
M(w)_12=грудня

M(A)_1=січ
M(A)_2=лют
M(A)_3=бер
M(A)_4=кві
M(A)_5=тра
M(A)_6=чер
M(A)_7=лип
M(A)_8=сер
M(A)_9=вер
M(A)_10=жов
M(A)_11=лис
M(A)_12=гру

M(N)_1=С
M(N)_2=Л
M(N)_3=Б
M(N)_4=К
M(N)_5=Т
M(N)_6=Ч
M(N)_7=Л
M(N)_8=С
M(N)_9=В
M(N)_10=Ж
M(N)_11=Л
M(N)_12=Г

M(W)_1=січень
M(W)_2=лютий
M(W)_3=березень
M(W)_4=квітень
M(W)_5=травень
M(W)_6=червень
M(W)_7=липень
M(W)_8=серпень
M(W)_9=вересень
M(W)_10=жовтень
M(W)_11=листопад
M(W)_12=грудень

# weekdays
D(a)_1=пн
D(a)_2=вт
D(a)_3=ср
D(a)_4=чт
D(a)_5=пт
D(a)_6=сб
D(a)_7=нд

D(n)_1=П
D(n)_2=В
D(n)_3=С
D(n)_4=Ч
D(n)_5=П
D(n)_6=С
D(n)_7=Н

D(s)_1=пн
D(s)_2=вт
D(s)_3=ср
D(s)_4=чт
D(s)_5=пт
D(s)_6=сб
D(s)_7=нд

D(w)_1=понеділок
D(w)_2=вівторок
D(w)_3=середа
D(w)_4=четвер
D(w)_5=пʼятниця
D(w)_6=субота
D(w)_7=неділя

D(A)_1=пн
D(A)_2=вт
D(A)_3=ср
D(A)_4=чт
D(A)_5=пт
D(A)_6=сб
D(A)_7=нд

D(N)_1=П
D(N)_2=В
D(N)_3=С
D(N)_4=Ч
D(N)_5=П
D(N)_6=С
D(N)_7=Н

D(S)_1=пн
D(S)_2=вт
D(S)_3=ср
D(S)_4=чт
D(S)_5=пт
D(S)_6=сб
D(S)_7=нд

D(W)_1=понеділок
D(W)_2=вівторок
D(W)_3=середа
D(W)_4=четвер
D(W)_5=пʼятниця
D(W)_6=субота
D(W)_7=неділя

# quarters
Q(a)_1=1-й кв.
Q(a)_2=2-й кв.
Q(a)_3=3-й кв.
Q(a)_4=4-й кв.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1-й квартал
Q(w)_2=2-й квартал
Q(w)_3=3-й квартал
Q(w)_4=4-й квартал

Q(A)_1=1-й кв.
Q(A)_2=2-й кв.
Q(A)_3=3-й кв.
Q(A)_4=4-й кв.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1-й квартал
Q(W)_2=2-й квартал
Q(W)_3=3-й квартал
Q(W)_4=4-й квартал

# day-period-rules
T0000=night1
T0400=morning1
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=опівночі
P(a)_am=дп
P(a)_noon=пополудні
P(a)_pm=пп
P(a)_morning1=ранку
P(a)_afternoon1=дня
P(a)_evening1=вечора
P(a)_night1=ночі

P(n)_midnight=північ
P(n)_am=дп
P(n)_noon=п
P(n)_pm=пп
P(n)_morning1=ранку
P(n)_afternoon1=дня
P(n)_evening1=вечора
P(n)_night1=ночі

P(w)_midnight=опівночі
P(w)_am=дп
P(w)_noon=пополудні
P(w)_pm=пп
P(w)_morning1=ранку
P(w)_afternoon1=дня
P(w)_evening1=вечора
P(w)_night1=ночі

P(A)_midnight=північ
P(A)_am=дп
P(A)_noon=полудень
P(A)_pm=пп
P(A)_morning1=ранок
P(A)_afternoon1=день
P(A)_evening1=вечір
P(A)_night1=ніч

P(N)_midnight=північ
P(N)_am=дп
P(N)_noon=полудень
P(N)_pm=пп
P(N)_morning1=ранок
P(N)_afternoon1=день
P(N)_evening1=вечір
P(N)_night1=ніч

P(W)_midnight=опівніч
P(W)_am=дп
P(W)_noon=полудень
P(W)_pm=пп
P(W)_morning1=ранок
P(W)_afternoon1=день
P(W)_evening1=вечір
P(W)_night1=ніч

# eras
E(w)_0=до нашої ери
E(w|alt)_0=до нової ери
E(w)_1=нашої ери
E(w|alt)_1=нової ери

E(a)_0=до н. е.
E(a)_1=н. е.

E(n)_0=до н.е.
E(n)_1=н.е.

# format patterns
F(f)_d=EEEE, d MMMM y 'р'.
F(l)_d=d MMMM y 'р'.
F(m)_d=d MMM y 'р'.
F(s)_d=dd.MM.yy

F(f)_dt={1} 'о' {0}
F(l)_dt={1} 'о' {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=dd.MM
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=MM.y
F_yMMM=LLL y
F_yMMMM=LLLL y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y 'р'.
F_yw=w-'й' 'тиж'. Y 'р'.

I={0} – {1}

# labels of elements
L_era=ера
L_year=рік
L_quarter=квартал
L_month=місяць
L_week=тиждень
L_day=день
L_weekday=день тижня
L_dayperiod=дп/пп
L_hour=година
L_minute=хвилина
L_second=секунда
L_zone=часовий пояс
