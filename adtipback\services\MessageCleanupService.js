/**
 * Message Cleanup Service
 * 
 * Handles automated cleanup of old messages and maintenance of message retention policies.
 * Implements 1-week retention for backend and configurable retention for local storage.
 */

const queryRunner = require('../dbConfig/queryRunner');

class MessageCleanupService {

  /**
   * Clean up messages older than specified days
   */
  static async cleanupOldMessages(daysOld = 7) {
    try {
      console.log(`[MessageCleanupService] Starting cleanup of messages older than ${daysOld} days`);

      const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
      const startTime = Date.now();

      // Get count of messages to be deleted for logging
      const countQuery = `
        SELECT COUNT(*) as count
        FROM messages 
        WHERE created_at < ? AND is_deleted = FALSE
      `;
      const [countResult] = await queryRunner.queryRunner(countQuery, [cutoffDate]);
      const messagesToDelete = countResult.count;

      if (messagesToDelete === 0) {
        console.log('[MessageCleanupService] No old messages found to cleanup');
        return {
          deletedMessages: 0,
          deletedSyncEntries: 0,
          deletedDeliveryStatus: 0,
          deletedChatMetadata: 0,
          cutoffDate: cutoffDate.toISOString(),
          duration: Date.now() - startTime,
          message: 'No messages to cleanup'
        };
      }

      console.log(`[MessageCleanupService] Found ${messagesToDelete} messages to delete`);

      // Start transaction for atomic cleanup
      await queryRunner.queryRunner('START TRANSACTION');

      try {
        // 1. Delete message delivery status entries for old messages
        const deliveryStatusQuery = `
          DELETE mds FROM message_delivery_status mds
          JOIN messages m ON mds.message_id = m.id
          WHERE m.created_at < ? AND m.is_deleted = FALSE
        `;
        const deliveryResult = await queryRunner.queryRunner(deliveryStatusQuery, [cutoffDate]);
        const deletedDeliveryStatus = deliveryResult.affectedRows || 0;

        // 2. Delete sync tracking entries for old messages
        const syncQuery = `
          DELETE mls FROM messages_local_sync mls
          JOIN messages m ON mls.message_id = m.id
          WHERE m.created_at < ? AND m.is_deleted = FALSE
        `;
        const syncResult = await queryRunner.queryRunner(syncQuery, [cutoffDate]);
        const deletedSyncEntries = syncResult.affectedRows || 0;

        // 3. Soft delete messages (mark as deleted instead of hard delete)
        const softDeleteQuery = `
          UPDATE messages 
          SET is_deleted = TRUE, 
              deleted_at = CURRENT_TIMESTAMP,
              deleted_by = NULL
          WHERE created_at < ? AND is_deleted = FALSE
        `;
        const messageResult = await queryRunner.queryRunner(softDeleteQuery, [cutoffDate]);
        const deletedMessages = messageResult.affectedRows || 0;

        // 4. Clean up user chat metadata for chats with no recent messages
        const metadataQuery = `
          UPDATE user_chat_metadata ucm
          SET last_message_content = 'Message deleted',
              last_message_time = NULL
          WHERE ucm.last_message_id IN (
            SELECT id FROM messages 
            WHERE created_at < ? AND is_deleted = TRUE
          )
        `;
        const metadataResult = await queryRunner.queryRunner(metadataQuery, [cutoffDate]);
        const deletedChatMetadata = metadataResult.affectedRows || 0;

        // Commit transaction
        await queryRunner.queryRunner('COMMIT');

        const duration = Date.now() - startTime;

        console.log(`[MessageCleanupService] Cleanup completed successfully:`, {
          deletedMessages,
          deletedSyncEntries,
          deletedDeliveryStatus,
          deletedChatMetadata,
          duration: `${duration}ms`
        });

        return {
          deletedMessages,
          deletedSyncEntries,
          deletedDeliveryStatus,
          deletedChatMetadata,
          cutoffDate: cutoffDate.toISOString(),
          duration,
          message: `Successfully cleaned up ${deletedMessages} messages and related data`
        };

      } catch (error) {
        // Rollback transaction on error
        await queryRunner.queryRunner('ROLLBACK');
        throw error;
      }

    } catch (error) {
      console.error('[MessageCleanupService] Error during cleanup:', error);
      throw error;
    }
  }

  /**
   * Hard delete messages that have been soft deleted for more than specified days
   */
  static async hardDeleteOldMessages(daysOld = 30) {
    try {
      console.log(`[MessageCleanupService] Starting hard delete of messages deleted more than ${daysOld} days ago`);

      const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
      const startTime = Date.now();

      // Get count of messages to be hard deleted
      const countQuery = `
        SELECT COUNT(*) as count
        FROM messages 
        WHERE is_deleted = TRUE AND deleted_at < ?
      `;
      const [countResult] = await queryRunner.queryRunner(countQuery, [cutoffDate]);
      const messagesToDelete = countResult.count;

      if (messagesToDelete === 0) {
        console.log('[MessageCleanupService] No old deleted messages found for hard delete');
        return {
          hardDeletedMessages: 0,
          cutoffDate: cutoffDate.toISOString(),
          duration: Date.now() - startTime,
          message: 'No messages to hard delete'
        };
      }

      console.log(`[MessageCleanupService] Found ${messagesToDelete} deleted messages to hard delete`);

      // Hard delete messages
      const deleteQuery = `
        DELETE FROM messages 
        WHERE is_deleted = TRUE AND deleted_at < ?
      `;
      const result = await queryRunner.queryRunner(deleteQuery, [cutoffDate]);
      const hardDeletedMessages = result.affectedRows || 0;

      const duration = Date.now() - startTime;

      console.log(`[MessageCleanupService] Hard delete completed: ${hardDeletedMessages} messages permanently deleted`);

      return {
        hardDeletedMessages,
        cutoffDate: cutoffDate.toISOString(),
        duration,
        message: `Permanently deleted ${hardDeletedMessages} old messages`
      };

    } catch (error) {
      console.error('[MessageCleanupService] Error during hard delete:', error);
      throw error;
    }
  }

  /**
   * Clean up orphaned sync entries
   */
  static async cleanupOrphanedSyncEntries() {
    try {
      console.log('[MessageCleanupService] Cleaning up orphaned sync entries');

      const startTime = Date.now();

      // Delete sync entries that don't have corresponding messages
      const deleteQuery = `
        DELETE mls FROM messages_local_sync mls
        LEFT JOIN messages m ON mls.message_id = m.id
        WHERE m.id IS NULL
      `;

      const result = await queryRunner.queryRunner(deleteQuery);
      const deletedEntries = result.affectedRows || 0;

      const duration = Date.now() - startTime;

      console.log(`[MessageCleanupService] Cleaned up ${deletedEntries} orphaned sync entries`);

      return {
        deletedEntries,
        duration,
        message: `Cleaned up ${deletedEntries} orphaned sync entries`
      };

    } catch (error) {
      console.error('[MessageCleanupService] Error cleaning up orphaned sync entries:', error);
      throw error;
    }
  }

  /**
   * Clean up orphaned delivery status entries
   */
  static async cleanupOrphanedDeliveryStatus() {
    try {
      console.log('[MessageCleanupService] Cleaning up orphaned delivery status entries');

      const startTime = Date.now();

      // Delete delivery status entries that don't have corresponding messages
      const deleteQuery = `
        DELETE mds FROM message_delivery_status mds
        LEFT JOIN messages m ON mds.message_id = m.id
        WHERE m.id IS NULL
      `;

      const result = await queryRunner.queryRunner(deleteQuery);
      const deletedEntries = result.affectedRows || 0;

      const duration = Date.now() - startTime;

      console.log(`[MessageCleanupService] Cleaned up ${deletedEntries} orphaned delivery status entries`);

      return {
        deletedEntries,
        duration,
        message: `Cleaned up ${deletedEntries} orphaned delivery status entries`
      };

    } catch (error) {
      console.error('[MessageCleanupService] Error cleaning up orphaned delivery status:', error);
      throw error;
    }
  }

  /**
   * Get cleanup statistics
   */
  static async getCleanupStats() {
    try {
      console.log('[MessageCleanupService] Getting cleanup statistics');

      // Total messages
      const totalMessagesQuery = 'SELECT COUNT(*) as count FROM messages';
      const [totalMessages] = await queryRunner.queryRunner(totalMessagesQuery);

      // Active messages
      const activeMessagesQuery = 'SELECT COUNT(*) as count FROM messages WHERE is_deleted = FALSE';
      const [activeMessages] = await queryRunner.queryRunner(activeMessagesQuery);

      // Deleted messages
      const deletedMessagesQuery = 'SELECT COUNT(*) as count FROM messages WHERE is_deleted = TRUE';
      const [deletedMessages] = await queryRunner.queryRunner(deletedMessagesQuery);

      // Messages older than 7 days
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      const oldMessagesQuery = 'SELECT COUNT(*) as count FROM messages WHERE created_at < ? AND is_deleted = FALSE';
      const [oldMessages] = await queryRunner.queryRunner(oldMessagesQuery, [sevenDaysAgo]);

      // Sync entries
      const syncEntriesQuery = 'SELECT COUNT(*) as count FROM messages_local_sync';
      const [syncEntries] = await queryRunner.queryRunner(syncEntriesQuery);

      // Delivery status entries
      const deliveryStatusQuery = 'SELECT COUNT(*) as count FROM message_delivery_status';
      const [deliveryStatus] = await queryRunner.queryRunner(deliveryStatusQuery);

      // Database size (approximate)
      const sizeQuery = `
        SELECT 
          ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
          AND table_name IN ('messages', 'messages_local_sync', 'message_delivery_status', 'user_chat_metadata')
      `;
      const [sizeResult] = await queryRunner.queryRunner(sizeQuery);

      return {
        totalMessages: totalMessages.count,
        activeMessages: activeMessages.count,
        deletedMessages: deletedMessages.count,
        oldMessages: oldMessages.count,
        syncEntries: syncEntries.count,
        deliveryStatusEntries: deliveryStatus.count,
        databaseSizeMB: sizeResult.size_mb || 0,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('[MessageCleanupService] Error getting cleanup stats:', error);
      throw error;
    }
  }

  /**
   * Perform full cleanup (all cleanup operations)
   */
  static async performFullCleanup(options = {}) {
    try {
      const {
        messageRetentionDays = 7,
        hardDeleteDays = 30,
        cleanupOrphaned = true
      } = options;

      console.log('[MessageCleanupService] Starting full cleanup with options:', options);

      const startTime = Date.now();
      const results = {
        messageCleanup: null,
        hardDelete: null,
        orphanedSync: null,
        orphanedDelivery: null,
        totalDuration: 0,
        success: false,
        errors: []
      };

      try {
        // 1. Clean up old messages
        results.messageCleanup = await this.cleanupOldMessages(messageRetentionDays);
      } catch (error) {
        console.error('[MessageCleanupService] Message cleanup failed:', error);
        results.errors.push(`Message cleanup failed: ${error.message}`);
      }

      try {
        // 2. Hard delete very old messages
        results.hardDelete = await this.hardDeleteOldMessages(hardDeleteDays);
      } catch (error) {
        console.error('[MessageCleanupService] Hard delete failed:', error);
        results.errors.push(`Hard delete failed: ${error.message}`);
      }

      if (cleanupOrphaned) {
        try {
          // 3. Clean up orphaned sync entries
          results.orphanedSync = await this.cleanupOrphanedSyncEntries();
        } catch (error) {
          console.error('[MessageCleanupService] Orphaned sync cleanup failed:', error);
          results.errors.push(`Orphaned sync cleanup failed: ${error.message}`);
        }

        try {
          // 4. Clean up orphaned delivery status
          results.orphanedDelivery = await this.cleanupOrphanedDeliveryStatus();
        } catch (error) {
          console.error('[MessageCleanupService] Orphaned delivery cleanup failed:', error);
          results.errors.push(`Orphaned delivery cleanup failed: ${error.message}`);
        }
      }

      results.totalDuration = Date.now() - startTime;
      results.success = results.errors.length === 0;

      console.log('[MessageCleanupService] Full cleanup completed:', {
        success: results.success,
        duration: `${results.totalDuration}ms`,
        errors: results.errors.length
      });

      return results;

    } catch (error) {
      console.error('[MessageCleanupService] Full cleanup failed:', error);
      throw error;
    }
  }
}

module.exports = MessageCleanupService;
