const mysqlConnection = require('./dbConfig/dbconnection');
const { queryRunner, checkDatabaseHealth, getQueryStats } = require('./dbConfig/queryRunner');
const databaseHealthService = require('./services/DatabaseHealthService');

async function testDatabaseImprovements() {
  console.log('🧪 Testing Database Improvements...\n');

  try {
    // Test 1: Basic Connection
    console.log('1. Testing basic database connection...');
    const healthResult = await checkDatabaseHealth();
    console.log('✅ Database health check:', healthResult.status);
    console.log('   Message:', healthResult.message);

    // Test 2: Query Execution
    console.log('\n2. Testing query execution...');
    const testQuery = 'SELECT 1 as test_value, NOW() as now_time';
    const result = await queryRunner(testQuery);
    console.log('✅ Query executed successfully');
    console.log('   Result:', result[0]);

    // Test 3: Connection Pool Stats
    console.log('\n3. Testing connection pool statistics...');
    const poolStats = mysqlConnection.getPoolStats();
    console.log('✅ Pool statistics retrieved');
    console.log('   Connection limit:', poolStats.connectionLimit);
    console.log('   Queue limit:', poolStats.queueLimit);
    console.log('   Acquire timeout:', poolStats.acquireTimeout);

    // Test 4: Health Service
    console.log('\n4. Testing database health service...');
    const healthStatus = databaseHealthService.getHealthStatus();
    console.log('✅ Health service status retrieved');
    console.log('   Is monitoring:', healthStatus.isMonitoring);
    console.log('   Is healthy:', healthStatus.isHealthy);
    console.log('   Total checks:', healthStatus.totalChecks);

    // Test 5: Query Statistics
    console.log('\n5. Testing query statistics...');
    const queryStats = getQueryStats();
    console.log('✅ Query statistics retrieved');
    console.log('   Total queries:', queryStats.totalQueries);
    console.log('   Average query time:', queryStats.averageQueryTime + 'ms');
    console.log('   Success rate:', queryStats.successRate);

    // Test 6: Multiple Concurrent Queries
    console.log('\n6. Testing concurrent query execution...');
    const concurrentQueries = Array.from({ length: 10 }, (_, i) => 
      queryRunner(`SELECT ${i} as query_number`)
    );
    
    const startTime = Date.now();
    const results = await Promise.all(concurrentQueries);
    const totalTime = Date.now() - startTime;
    
    console.log('✅ Concurrent queries completed');
    console.log('   Total time:', totalTime + 'ms');
    console.log('   Average time per query:', (totalTime / 10) + 'ms');
    console.log('   Results count:', results.length);

    // Test 7: Error Handling
    console.log('\n7. Testing error handling...');
    try {
      await queryRunner('SELECT * FROM non_existent_table');
    } catch (error) {
      console.log('✅ Error handling works correctly');
      console.log('   Error type:', error.error || 'Database Error');
      console.log('   Error message:', error.message);
    }

    // Test 8: Transaction Support
    console.log('\n8. Testing transaction support...');
    const transactionQueries = [
      { query: 'SELECT 1 as step1', params: [] },
      { query: 'SELECT 2 as step2', params: [] },
      { query: 'SELECT 3 as step3', params: [] }
    ];
    
    const transactionResult = await mysqlConnection.executeTransaction(transactionQueries);
    console.log('✅ Transaction executed successfully');
    console.log('   Result:', transactionResult);

    // Test 9: Performance Under Load
    console.log('\n9. Testing performance under load...');
    const loadTestQueries = Array.from({ length: 50 }, (_, i) => 
      queryRunner(`SELECT ${i} as load_test_number`)
    );
    
    const loadStartTime = Date.now();
    const loadResults = await Promise.all(loadTestQueries);
    const loadTotalTime = Date.now() - loadStartTime;
    
    console.log('✅ Load test completed');
    console.log('   Queries executed:', loadResults.length);
    console.log('   Total time:', loadTotalTime + 'ms');
    console.log('   Average time per query:', (loadTotalTime / 50) + 'ms');
    console.log('   Queries per second:', (50 / (loadTotalTime / 1000)).toFixed(2));

    // Test 10: Final Health Check
    console.log('\n10. Final health check...');
    const finalHealth = await checkDatabaseHealth();
    const finalStats = getQueryStats();
    
    console.log('✅ Final health check completed');
    console.log('   Database status:', finalHealth.status);
    console.log('   Total queries executed:', finalStats.totalQueries);
    console.log('   Success rate:', finalStats.successRate);

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Summary:');
    console.log('   - Database connection pool is working correctly');
    console.log('   - Query execution is optimized');
    console.log('   - Error handling is robust');
    console.log('   - Health monitoring is active');
    console.log('   - Performance is acceptable for production use');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  testDatabaseImprovements()
    .then(() => {
      console.log('\n✅ Database improvements test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Database improvements test failed:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseImprovements }; 