# This code was generated by [React Native](https://www.npmjs.com/package/@react-native/gradle-plugin)
cmake_minimum_required(VERSION 3.13)
set(CMAKE_VERBOSE_MAKEFILE on)

# We set REACTNATIVE_MERGED_SO so libraries/apps can selectively decide to depend on either libreactnative.so
# or link against a old prefab target (this is needed for React Native 0.76 on).
set(REACTNATIVE_MERGED_SO true)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/" RNFastImageSpec_autolinked_build)


add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/" rnasyncstorage_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/" rnclipboard_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/" rnblurview_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/" RNDateTimePickerCGen_autolinked_build)











add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/" rnskia_autolinked_build)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/" lottiereactnative_autolinked_build)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/" Compressor_autolinked_build)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/" RNDatePickerSpecs_autolinked_build)


add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/" rngesturehandler_codegen_autolinked_build)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/" RNGoogleMobileAdsSpec_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/" RNCImageCropPickerSpec_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/" RNImagePickerSpec_autolinked_build)


add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/" pagerview_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/" RNPermissionsSpec_autolinked_build)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/" rnreanimated_autolinked_build)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/" safeareacontext_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/" rnscreens_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/" rnsvg_autolinked_build)
add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/" RNVectorIconsSpec_autolinked_build)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/" rnviewshot_autolinked_build)

add_subdirectory("F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/" RNCWebViewSpec_autolinked_build)

set(AUTOLINKED_LIBRARIES
  react_codegen_RNFastImageSpec
  
  
  react_codegen_rnasyncstorage
  react_codegen_rnclipboard
  react_codegen_rnblurview
  react_codegen_RNDateTimePickerCGen
  
  
  
  
  
  
  
  
  
  
  
  react_codegen_rnskia
  
  react_codegen_lottiereactnative
  
  react_codegen_Compressor
  
  react_codegen_RNDatePickerSpecs
  
  
  react_codegen_rngesturehandler_codegen
  
  react_codegen_RNGoogleMobileAdsSpec
  react_codegen_RNCImageCropPickerSpec
  react_codegen_RNImagePickerSpec
  
  
  react_codegen_pagerview
  react_codegen_RNPermissionsSpec
  
  react_codegen_rnreanimated
  
  react_codegen_safeareacontext
  react_codegen_rnscreens
  react_codegen_rnsvg
  react_codegen_RNVectorIconsSpec
  
  react_codegen_rnviewshot
  
  react_codegen_RNCWebViewSpec
)