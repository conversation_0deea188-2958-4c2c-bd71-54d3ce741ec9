# months
M(A)_1=ene.
M(A)_2=feb.
M(A)_3=mar.
M(A)_4=abr.
M(A)_5=may.
M(A)_6=jun.
M(A)_7=jul.
M(A)_8=ago.
M(A)_9=sept.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=dic.

# weekdays
D(n)_1=L
D(n)_2=M
D(n)_3=M
D(n)_4=J
D(n)_5=V
D(n)_6=S
D(n)_7=D

D(N)_1=l
D(N)_2=m
D(N)_3=m
D(N)_4=j
D(N)_5=v
D(N)_6=s
D(N)_7=d

# day-period-rules
T0000=morning2
T1200=evening1
T2000=night1

# day-period-translations
P(a)_am=a. m.
P(a)_noon=m.
P(a)_pm=p. m.
P(a)_morning2=de la mañana
P(a)_evening1=de la tarde
P(a)_night1=de la noche

P(w)_am=a. m.
P(w)_pm=p. m.

P(A)_am=a. m.
P(A)_noon=m.
P(A)_pm=p. m.

P(N)_am=a. m.
P(N)_noon=m.
P(N)_pm=p. m.

P(W)_am=a. m.
P(W)_pm=p. m.

# format patterns
F(m)_d=d/MM/y
F(s)_d=d/MM/yy

F(alt)=hh:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}
F_H=H
F_Hm=H:mm
F_Hms=H:mm:ss
F_MMMd=d 'de' MMM
F_yMMM=MMM 'de' y

I={0} ‘al’ {1}

# labels of elements
L_dayperiod=a. m./p. m.
