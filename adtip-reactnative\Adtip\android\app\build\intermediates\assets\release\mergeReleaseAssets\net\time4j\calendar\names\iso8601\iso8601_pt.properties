# months
M(a)_1=jan
<PERSON>(a)_2=fev
M(a)_3=mar
M(a)_4=abr
M(a)_5=mai
M(a)_6=jun
M(a)_7=jul
M(a)_8=ago
M(a)_9=set
M(a)_10=out
M(a)_11=nov
M(a)_12=dez

M(n)_1=J
M(n)_2=F
M(n)_3=M
M(n)_4=A
M(n)_5=M
M(n)_6=J
M(n)_7=J
M(n)_8=A
M(n)_9=S
M(n)_10=O
M(n)_11=N
M(n)_12=D

M(w)_1=janeiro
M(w)_2=fevereiro
M(w)_3=março
M(w)_4=abril
M(w)_5=maio
M(w)_6=junho
M(w)_7=julho
M(w)_8=agosto
M(w)_9=setembro
M(w)_10=outubro
M(w)_11=novembro
M(w)_12=dezembro

M(A)_1=jan
<PERSON>(A)_2=fev
M(A)_3=mar
M(A)_4=abr
M(A)_5=mai
M(A)_6=jun
M(A)_7=jul
M(A)_8=ago
M(A)_9=set
M(A)_10=out
M(A)_11=nov
M(A)_12=dez

M(N)_1=J
M(N)_2=F
M(N)_3=M
M(N)_4=A
M(N)_5=M
M(N)_6=J
M(N)_7=J
M(N)_8=A
M(N)_9=S
M(N)_10=O
M(N)_11=N
M(N)_12=D

M(W)_1=janeiro
M(W)_2=fevereiro
M(W)_3=março
M(W)_4=abril
M(W)_5=maio
M(W)_6=junho
M(W)_7=julho
M(W)_8=agosto
M(W)_9=setembro
M(W)_10=outubro
M(W)_11=novembro
M(W)_12=dezembro

# weekdays
D(a)_1=seg
D(a)_2=ter
D(a)_3=qua
D(a)_4=qui
D(a)_5=sex
D(a)_6=sáb
D(a)_7=dom

D(n)_1=S
D(n)_2=T
D(n)_3=Q
D(n)_4=Q
D(n)_5=S
D(n)_6=S
D(n)_7=D

D(s)_1=seg
D(s)_2=ter
D(s)_3=qua
D(s)_4=qui
D(s)_5=sex
D(s)_6=sáb
D(s)_7=dom

D(w)_1=segunda-feira
D(w)_2=terça-feira
D(w)_3=quarta-feira
D(w)_4=quinta-feira
D(w)_5=sexta-feira
D(w)_6=sábado
D(w)_7=domingo

D(A)_1=seg
D(A)_2=ter
D(A)_3=qua
D(A)_4=qui
D(A)_5=sex
D(A)_6=sáb
D(A)_7=dom

D(N)_1=S
D(N)_2=T
D(N)_3=Q
D(N)_4=Q
D(N)_5=S
D(N)_6=S
D(N)_7=D

D(S)_1=seg
D(S)_2=ter
D(S)_3=qua
D(S)_4=qui
D(S)_5=sex
D(S)_6=sáb
D(S)_7=dom

D(W)_1=segunda-feira
D(W)_2=terça-feira
D(W)_3=quarta-feira
D(W)_4=quinta-feira
D(W)_5=sexta-feira
D(W)_6=sábado
D(W)_7=domingo

# quarters
Q(a)_1=T1
Q(a)_2=T2
Q(a)_3=T3
Q(a)_4=T4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1º trimestre
Q(w)_2=2º trimestre
Q(w)_3=3º trimestre
Q(w)_4=4º trimestre

Q(A)_1=T1
Q(A)_2=T2
Q(A)_3=T3
Q(A)_4=T4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1º trimestre
Q(W)_2=2º trimestre
Q(W)_3=3º trimestre
Q(W)_4=4º trimestre

# day-period-rules
T0000=night1
T0600=morning1
T1200=afternoon1
T1900=evening1

# day-period-translations
P(a)_midnight=meia-noite
P(a)_am=AM
P(a)_noon=meio-dia
P(a)_pm=PM
P(a)_morning1=da manhã
P(a)_afternoon1=da tarde
P(a)_evening1=da noite
P(a)_night1=da madrugada

P(n)_midnight=meia-noite
P(n)_am=AM
P(n)_noon=meio-dia
P(n)_pm=PM
P(n)_morning1=da manhã
P(n)_afternoon1=da tarde
P(n)_evening1=da noite
P(n)_night1=da madrugada

P(w)_midnight=meia-noite
P(w)_am=AM
P(w)_noon=meio-dia
P(w)_pm=PM
P(w)_morning1=da manhã
P(w)_afternoon1=da tarde
P(w)_evening1=da noite
P(w)_night1=da madrugada

P(A)_midnight=meia-noite
P(A)_am=AM
P(A)_noon=meio-dia
P(A)_pm=PM
P(A)_morning1=manhã
P(A)_afternoon1=tarde
P(A)_evening1=noite
P(A)_night1=madrugada

P(N)_midnight=meia-noite
P(N)_am=AM
P(N)_noon=meio-dia
P(N)_pm=PM
P(N)_morning1=manhã
P(N)_afternoon1=tarde
P(N)_evening1=noite
P(N)_night1=madrugada

P(W)_midnight=meia-noite
P(W)_am=AM
P(W)_noon=meio-dia
P(W)_pm=PM
P(W)_morning1=manhã
P(W)_afternoon1=tarde
P(W)_evening1=noite
P(W)_night1=madrugada

# eras
E(w)_0=antes de Cristo
E(w|alt)_0=antes da Era Comum
E(w)_1=depois de Cristo
E(w|alt)_1=Era Comum

E(a)_0=a.C.
E(a|alt)_0=AEC
E(a)_1=d.C.
E(a|alt)_1=EC

# format patterns
F(f)_d=EEEE, d 'de' MMMM 'de' y
F(l)_d=d 'de' MMMM 'de' y
F(m)_d=d 'de' MMM 'de' y
F(s)_d=dd/MM/y

F(alt)=HH'H'mm'm'

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d/M
F_MMMd=d 'de' MMM
F_MMMMd=d 'de' MMMM
F_y=y
F_yM=MM/y
F_yMM=MM/y
F_yMMM=MMM 'de' y
F_yMMMM=MMMM 'de' y
F_yQQQ=QQQ 'de' y
F_yQQQQ=QQQQ 'de' y
F_yw=w'ª' 'semana' 'de' Y

I={0} - {1}

# labels of elements
L_era=era
L_year=ano
L_quarter=trimestre
L_month=mês
L_week=semana
L_day=dia
L_weekday=dia da semana
L_dayperiod=AM/PM
L_hour=hora
L_minute=minuto
L_second=segundo
L_zone=fuso horário
