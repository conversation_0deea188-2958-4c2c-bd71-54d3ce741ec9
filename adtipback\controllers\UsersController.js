let userService = require("../services/UsersService");
const dbQuery = require('../dbConfig/queryRunner');
const moment = require("moment");


///addStatus
///addStatusViewers
///getCurrentUserStatusList
///getStatusViewersList
////getOtherUserStatusList
///statusId

module.exports = {

  ping: async (req, res) => {
    try {
      const userId = req.user?.user_id;; // Extract user_id from JWT token
      await userService.updateUserActivity(userId);
      res.json({ status: true, message: "User activity recorded" });
    } catch (err) {
      res.status(err.status || 500).json({
        status: false,
        message: err.message || "Internal server error.",
        data: [],
      });
    }
  },

  getFunds: async (req, res) => {
    try {
      const userId = req.user.id; // Assuming user ID is available from auth
      const query = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      console.log("Get funds query:", query, "with params:", [userId]); // Debug log
      const [walletData] = await dbQuery.queryRunner(query, [userId]);
      const balance = walletData?.totalBalance || 0;
      res.json({ status: true, balance });
    } catch (error) {
      console.error("Error in getFunds:", error);
      res.status(400).json({ status: false, message: error.message || 'Server Error' });
    }
  },
  
  deleteStatus: (req, res, next) => {
    userService
      .deleteStatus(req.params.statusId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(500).send(err);
      });
  },
  getOtherUserStatusList: (req, res, next) => {
    userService
      .getOtherUserStatusList(req.params.userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(500).send(err);
      });
  },
  getStatusViewersList: (req, res, next) => {
    userService
      .getStatusViewersList(req.params.statusId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(500).send(err);
      });
  },
  getCurrentUserStatusList: (req, res, next) => {
    userService
      .getCurrentUserStatusList(req.params.userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(500).send(err);
      });
  },
  addStatusViewers: (req, res, next) => {
    userService
      .addStatusViewers(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(500).send(err);
      });
  },
  addStatus: (req, res, next) => {
    userService
      .addStatus(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(500).send(err);
      });
  },
  updateDeviceToken: (req, res, next) => {
    userService
      .updateUserDeviceToken(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(500).send(err);
      });
  },
  //getNotifications
  getNotifications: (req, res, next) => {
    userService
      .getNotifications(req.params.userId, req.params.page, req.params.limit)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  //saveNotifications
  saveNotifications: (req, res, next) => {
    userService
      .saveNotifications(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  //generateReferalId
  //checkReferalCodeValid
  //getTransactionsOfReferalWithdrawRequests
  getTransactionsOfReferalWithdrawRequests: (req, res, next) => {
    userService
      .getTransactionsOfReferalWithdrawRequests(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveReferWithdrawRequest: (req, res, next) => {
    userService
      .saveReferWithdrawRequest(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  checkReferalCodeValid: (req, res, next) => {
    userService
      .checkReferalCodeValid(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  generateReferalId: (req, res, next) => {
    userService
      .generateReferalId(req.params.userId)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveLoginOtp: (req, res, next) => {
    userService
      .saveLoginOtp(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  userLogout: (req, res, next) => {
    userService
      .userLogout(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  otpVerify: (req, res, next) => {
    userService
      .otpVerify(req.body)
      .then((result) => {
        result.message =
          result.message === "Fetch user successfully."
            ? "OTP verified successfully."
            : result.message;
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveUserDetails: (req, res, next) => {
    userService
      .saveUserDetails(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateUser: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: [] });
    userService
      .updateUser(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getUser: (req, res, next) => {
    userService
      .getUser(req.params.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deletemessages: (req, res, next) => {
    userService
      .deletemessages(req.body.id)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  saveticks: (req, res, next) => {
    userService
      .saveticks(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateBlockUser: (req, res, next) => {
    userService
      .updateBlockUser(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getMessages: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid data", data: req.params });
    let chattinguserid = req.params.chattinguserid
      ? req.params.chattinguserid
      : null;
    userService
      .getMessages(req.params.userid, chattinguserid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getMessage: (req, res, next) => {
    if (!req.params.loginuserid && req.params.chattinguserid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid data", data: req.params });
    userService
      .getMessages(req.params.loginuserid, req.params.chattinguserid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getMuteAndBlockUsers: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid data", data: req.params });
    userService
      .getMuteAndBlockUsers(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  sendmessage: (req, res, next) => {
    userService
      .savemessages(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  savechat: (req, res, next) => {
    userService
      .savechat(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  clearAllchat: (req, res, next) => {
    if (!req.params.loginuserid && req.params.chattinguserid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid data", data: req.params });
    userService
      .clearAllchat(req.params.loginuserid, req.params.chattinguserid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  deletechatforme: (req, res, next) => {
    if (!req.body)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid data", data: req.body });
    userService
      .deletechatforme(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  deletechatforEveryone: (req, res, next) => {
    if (!req.body)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid data", data: req.body });
    userService
      .deletechatforEveryone(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  seenAllMessage: (req, res, next) => {
    if (!req.params.loginuserid && req.params.chattinguserid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid data", data: req.params });
    userService
      .seenAllMessage(req.params.loginuserid, req.params.chattinguserid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getUnSeenMessageCount: (req, res, next) => {
    if (!req.params.loginuserid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid data", data: req.params });
    userService
      .getUnSeenMessageCount(req.params.loginuserid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  userRequestSave: (req, res, next) => {
    userService
      .userRequestSave(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getSendRequestByUserId: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.params });
    userService
      .getSendRequestByUserId(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getRecievedRequestByUserId: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.params });
    userService
      .getRecievedRequestByUserId(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateUserRequestStatus: (req, res, next) => {
    userService
      .updateUserRequestStatus(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  deleteSendRequest: (req, res, next) => {
    if (!req.body.userId || !req.body.createdBy)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    userService
      .deleteSendRequest(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  saveUserDeviceToken: (req, res, next) => {
    if (!req.body.id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    userService
      .saveUserDeviceToken(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getSentNotification: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.params });
    userService
      .getSentNotification(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getReceivedNotification: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.params });
    userService
      .getReceivedNotification(req.params.userid)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getReceivedNotificationList: (req, res, next) => {
    if (!req.params.userid)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.params });
    userService
      .getReceivedNotificationList(req.params.userid, 9)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  updateUserEmail: (req, res, next) => {
    if (!req.body.id && !req.body.email)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    userService
      .updateUserEmail(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  readNotification: (req, res, next) => {
    if (!req.body.notification_id)
      return res
        .status(400)
        .send({ status: 400, message: "Invalid request.", data: req.body });
    userService
      .readNotification(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getConfigValue: (req, res, next) => {
    userService
      .getConfigValue(req.params.configkey)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  audioCalling: (req, res, next) => {
    userService
      .audioCalling(req.body)
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getEducations: (req, res, next) => {
    userService
      .getEducations()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getLanguages: (req, res, next) => {
    userService
      .getLanguages()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },

  getInterests: (req, res, next) => {
    userService
      .getInterest()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  getProfessions: (req, res, next) => {
    userService
      .getProfessions()
      .then((result) => {
        res.status(result.status || 200).send(result);
      })
      .catch((err) => {
        res.status(err.status || 500).send({
          status: err.status || 500,
          message: err.message ? err.message : "Internal server error.",
          data: [],
        });
      });
  },
  allUsers: async (req, res) => {
    try {
      const {
        id = 0,
        page = 1,
        limit = 20,
        language,
        interest,
        user_id,
        search_by_name,
        loggined_user_id,
        sortBy,
      } = req.body;
      const result = await userService.getAllUsers({
        id: Number(id),
        page: Number(page),
        limit: Number(limit),
        language: language,
        interest: interest,
        user_id: user_id,
        loggined_user_id: loggined_user_id,
        search_by_name: search_by_name,
        sortBy: sortBy,
      });
      res.status(200).json(result);
    } catch (err) {
      res.status(err.status || 500).send({
        status: err.status || 500,
        message: err.message || "Internal server error.",
        data: [],
      });
    }
  },

  allUsersall : async (req, res) => {
    try {
      const {
        id = 0,
        page = 1,
        limit = 20,
        interest = [],
        user_id,
        search_by_name,
        loggined_user_id,
        sortBy = {},
      } = req.body;
  
      const result = await userService.getAllUsersWithoutLanguage({
        id,
        page,
        limit,
        interest,
        user_id,
        search_by_name,
        loggined_user_id,
        sortBy,
      });
  
      res.json(result);
    } catch (err) {
      console.error("Error in allUsersall:", err.message);
      res.status(500).json({
        status: false,
        message: err.message || "Internal server error",
        data: [],
      });
    }
  },

  getZegoCloudDetails: async (req, res) => {
    try {
      const result = await userService.getZegoCloudDetails();
      res.status(result.status || 200).send(result);
    } catch (err) {
      res.status(err.status || 500).send({
        status: err.status || 500,
        message: err.message || "Internal server error.",
        data: [],
      });
    }
  },
  getRazorpayDetails: async (req, res) => {
    try {
      const result = await userService.getRazorpayDetails();
      res.status(result.status || 200).send(result);
    } catch (err) {
      res.status(err.status || 500).send({
        status: err.status || 500,
        message: err.message || "Internal server error.",
        data: [],
      });
    }
  },
  getRazorpayDetailsTest: async (req, res) => {
    try {
      const result = await userService.getRazorpayDetailsTest();
      res.status(result.status || 200).send(result);
    } catch (err) {
      res.status(err.status || 500).send({
        status: err.status || 500,
        message: err.message || "Internal server error.",
        data: [],
      });
    }
  },
  blockUser: async (req, res) => {
    const { user_id, block_user_id } = req.body;

    try {
      const response = await userService.blockUser(user_id, block_user_id);
      if (response.error) {
        return res.status(400).json({ status: false, message: response.error });
      }
      return res.status(200).json({
        status: response.status,
        message: response.message,
        is_blocked: response.is_blocked,
      });
    } catch (error) {
      return res
        .status(500)
        .json({ status: false, error: "Internal Server Error" });
    }
  },
  unBlockUser: async (req, res) => {
    const { user_id, block_user_id } = req.body;

    try {
      const response = await userService.unBlockUser(user_id, block_user_id);
      if (response.error) {
        return res.status(400).json({
          status: false,
          message: response.error,
        });
      }
      return res.status(200).json({
        status: true,
        message: response.message,
      });
    } catch (error) {
      return res
        .status(500)
        .json({ status: false, error: "Internal Server Error" });
    }
  },
  blockedList: async (req, res) => {
    const { user_id } = req.body;

    try {
      const blockedUsers = await userService.blockedList(user_id);
      if (blockedUsers.length === 0) {
        return res.status(200).json({
          status: true,
          message: "No blocked users found",
          data: [],
        });
      }

      return res.status(200).json({
        status: true,
        message: "Blocked users list retrieved successfully",
        data: blockedUsers,
      });
    } catch (error) {
      res.status(500).json({ status: false, error: "Internal Server Error" });
    }
  },
  saveCallDetails: async (req, res) => {
    try {
      const { caller_user_id, receiver_user_id } = req.body;
      const saveCall = await userService.saveCallDetails(
        caller_user_id,
        receiver_user_id
      );
      return res.status(201).json({
        status: true,
        message: saveCall.message,
      });
    } catch (error) {
      return res.status(500).json({
        status: false,
        error: error.message,
      });
    }
  },
  getRecentCalls: async (req, res) => {
    try {
      const { user_id, limit = 20 } = req.body;

      // Call the service to get recent calls
      const calls = await userService.getRecentCalls(user_id, limit);

      // Respond with the recent call details
      return res.status(200).json({
        status: true,
        message: "Recent calls fetched successfully",
        data: calls,
      });
    } catch (error) {
      console.log(error);
      return res.status(500).json({
        status: false,
        error: "Error fetching recent calls",
      });
    }
  },

  /**
   * Controller to process a referral.
   * @param {Object} req - Express request object.
   * @param {Object} res - Express response object.
   */
  processReferral: async (req, res) => {
    try {
      const { referrerId, referredUserId, referredCode } = req.body;

      if (!referrerId || !referredUserId || !referredCode) {
        return res.status(400).json({
          success: false,
          message: "Referrer ID and Referred User ID are required.",
        });
      }

      const result = await userService.processReferral(
        referrerId,
        referredUserId,
        referredCode
      );
      if (!result.status) {
        return res.status(400).json(result);
      }
      return res.status(200).json(result);
    } catch (error) {
      return res.status(500).json({ status: false, message: error.message });
    }
  },

  withdrawFunds: async (req, res) => {
    const { user_id, amount, withdrawal_type, bank_details, upi_id } = req.body;

    try {
      const result = await userService.processWithdrawal(
        user_id,
        amount,
        withdrawal_type,
        bank_details,
        upi_id
      );
      if (result.statusCode != 200) {
        return res.status(400).json(result);
      }
      return res.status(200).json(result);
    } catch (error) {
      console.error("Error processing withdrawal:", error.message);
      return res.status(error.statusCode ? error.statusCode : 500).json({
        message: error.message ? error.message : "something went wrong",
      });
    }
  },

  applyCoupon: async (req, res) => {
    try {
      const { userId, couponCode, plan_id, amount } = req.body;

      if (!userId || !couponCode || !plan_id || !amount) {
        return res.status(400).json({
          success: false,
          message: "User ID and Coupon Code, plan_id, amount are required.",
        });
      }

      const result = await userService.checkCouponEligibility(
        userId,
        couponCode,
        plan_id,
        amount
      );
      if (result.statusCode == 400) {
        return res.status(400).json({
          status: false,
          is_applicable: result.is_applicable,
          message: result.message,
        });
      }
      return res.status(200).json({
        status: true,
        is_applicable: result.is_applicable,
        message: result.message,
        coupon_code: result.couponCode,
        discount: result.discount_value,
        discount_type: result.discount_type,
        description: result.description,
      });
    } catch (error) {
      return res.status(500).json({ status: false, message: error.message });
    }
  },
  getCoupons: async (req, res) => {
    try {
      const getCoupons = await userService.fetchCoupons({
        user_id: req.query.user_id ? req.query.user_id : 0,
      });
      return res.status(200).json({
        status: true,
        message: "coupons list",
        data: getCoupons,
      });
    } catch (error) {
      return res.status(500).json({
        status: false,
        error: "Error fetching coupon list",
      });
    }
  },

  /*
  upgradePremium: async (req, res) => {
    try {
      const {
        payment_status,
        user_id,
        plan_id,
        order_id,
        payment_id,
        coupon_code,
      } = req.body;
      if (!payment_status || !user_id || !plan_id || !order_id || !payment_id) {
        return res
          .status(400)
          .json({ status: false, message: "Missing required parameters" });
      }

      const result = await userService.upgradePremium({
        payment_status,
        user_id,
        plan_id,
        order_id,
        payment_id,
        coupon_code,
      });
      if (result.statusCode == 400) {
        return res.status(400).json({ status: false, message: result.message });
      }
      return res.status(200).json({ status: true, message: result.message });
    } catch (error) {
      console.error("Error upgrading premium:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  */
 
  checkPremiumStatus: async (req, res) => {
    try {
      const userId = req.params.userId;
      if (!userId) {
        return res
          .status(400)
          .json({ status: false, message: "User ID is required" });
      }

      const response = await userService.checkPremiumStatus(userId);

      console.log("response", response);
      if (response.error) {
        return res.status(400).json({
          status: false,
          message: response.error,
        });
      }
      return res.status(200).json({
        status: true,
        user_id: userId,
        plan_id: response.plan_id,
        end_time: response.end_time,
        is_premium_expired: response.is_premium_expired,
        message: response.message,
      });
    } catch (error) {
      console.error("Controller Error:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  // Controller to handle call processing
  processCall: async (req, res) => {
    try {
      const {
        callerId,
        receiverId,
        startTime = "",
        endTime = "",
        action,
        callId = undefined,
      } = req.body;

      // Validate request payload
      // if (!callerId || !receiverId || !startTime) {
      //   return res.status(400).json({
      //     status: false,
      //     message: "callerId, receiverId, startTime, and endTime are required.",
      //   });
      // }

      // Call the service function to handle the call
      const result = await userService.handleCallS({
        callerId: callerId,
        receiverId: receiverId,
        startTime: startTime,
        endTime: endTime,
        callId: callId,
        action: action,
      });

      return res.status(200).json(result);
    } catch (error) {
      console.error("Error in controller:", error.message);
      return res.status(error.statusCode ? error.statusCode : 500).json({
        status: false,
        message: error.message ? error.message : "Internal server error.",
      });
    }
  },

  initiateCall: async (req, res) => {
    try {
      const { callerId, receiverId, action, callId, callType } = req.body;

      if (!callerId || !receiverId || !action || !callType) {
        return res.status(400).json({ error: "Missing required parameters." });
      }

      if (!['voice-call', 'video-call'].includes(callType)) {
        return res.status(400).json({ error: "Invalid call type." });
      }

      // Missed call
      if (action === `missed-${callType}`) {
        const result = await userService.missedCall(callerId, receiverId, callType);
        return res.status(result.statusCode).json(result);
      }

      // Start the call
      if (action === "start") {
        const result = await userService.startCall(callerId, receiverId, callType);
        return res.status(result.statusCode).json(result);
      }

      // End the call
      if (action === "end" && callId) {
        const result = await userService.endCall(callerId, receiverId, callId);
        return res.status(result.statusCode).json(result);
      }

      return res.status(400).json({ message: "Invalid action parameter." });
    } catch (error) {
      return res.status(500).json({ message: error.message || "Internal Server Error" });
    }
  },
  /*
  initiateCall: async (req, res) => {
    try {
      const { callerId, receiverId, action, callId } = req.body;

      // Check if required parameters are provided
      if (!callerId || !receiverId || !action) {
        return res.status(400).json({ error: "Missing required parameters." });
      }

      // Missed call
      if (action === "missed-call") {
        const result = await userService.missedCall(callerId, receiverId);
        if (result.statusCode != 200) {
          return res.status(400).json(result);
        }
        return res.status(200).json(result);
      }

      // Start the call
      if (action === "start") {
        const result = await userService.startCall(callerId, receiverId);
        if (result.statusCode != 200) {
          return res.status(400).json(result);
        }
        return res.status(200).json(result);
      }

      // End the call and update the transaction details
      if (action === "end" && callId) {
        const result = await userService.endCall(callerId, receiverId, callId);
        return res.status(result.error ? 400 : 200).json(result);
      }

      return res.status(400).json({ message: "Invalid action parameter." });
    } catch (error) {
      return res
        .status(500)
        .json({ message: error.message || "Internal Server Error" });
    }
  },
  */

updateFcmToken: async (req, res) => {
  try {
    const { userId, fcmToken } = req.body;
    if (!userId || !fcmToken) {
      return res.status(400).json({ status: false, message: 'userId and fcmToken are required' });
    }

    const updateQuery = `
      UPDATE users 
      SET fcm_token = ?, fcm_token_updation_date = NOW() 
      WHERE id = ?
    `;

    await dbQuery.queryRunner(updateQuery, [fcmToken, userId]);

    return res.status(200).json({ status: true, message: 'FCM token updated successfully' });
  } catch (error) {
    console.error('Error updating FCM token:', error);
    return res.status(500).json({ status: false, message: 'Server error' });
  }
},

 updateFcmTokenofuser : async (req, res) => {
  try {
    const userId = req.params.userId;

    if (!userId) {
      return res.status(400).json({ status: false, message: 'userId is required' });
    }

    const selectQuery = `SELECT fcm_token FROM users WHERE id = ?`;
    const result = await dbQuery.queryRunner(selectQuery, [userId]);

    if (result.length === 0) {
      return res.status(404).json({ status: false, message: 'User not found' });
    }

    return res.status(200).json({ status: true, fcm_token: result[0].fcm_token });
  } catch (error) {
    console.error('Error fetching FCM token:', error);
    return res.status(500).json({ status: false, message: 'Server error' });
  }
},

 getFcmTokensOfBothUsers : async (req, res) => {
  try {
    const { userIds } = req.body;

    if (!Array.isArray(userIds) || userIds.length !== 2) {
      return res.status(400).json({ status: false, message: 'Please provide exactly two userIds' });
    }

    const query = `SELECT id, fcm_token FROM users WHERE id IN (?, ?)`;
    const results = await dbQuery.queryRunner(query, userIds);

    // Create a map for quick lookup
    const tokenMap = {};
    results.forEach(row => {
      tokenMap[row.id] = row.fcm_token;
    });

    const response = userIds.map(id => ({
      status: !!tokenMap[id],
      userId: id,
      fcm_token: tokenMap[id] || null
    }));

    res.status(200).json({ results: response });
  } catch (error) {
    console.error('Error fetching FCM tokens:', error);
    res.status(500).json({ status: false, message: 'Server error' });
  }
},

  initiateVideoCall: async (req, res) => {
    try {
      const { callerId, receiverId, action, callId } = req.body;
  
      // Check if required parameters are provided
      if (!callerId || !receiverId || !action) {
        return res.status(400).json({ error: "Missing required parameters." });
      }
  
      // Missed video call
      if (action === "missed-video-call") {
        const result = await userService.missedVideoCall(callerId, receiverId);
        if (result.statusCode !== 200) {
          return res.status(400).json(result);
        }
        return res.status(200).json(result);
      }
  
      // Start the video call
      if (action === "start") {
        const result = await userService.startVideoCall(callerId, receiverId);
        if (result.statusCode !== 200) {
          return res.status(400).json(result);
        }
        return res.status(200).json(result);
      }
  
      // End the video call and update the transaction details
      if (action === "end" && callId) {
        const result = await userService.endVideoCall(callerId, receiverId, callId);
        return res.status(result.error ? 400 : 200).json(result);
      }
  
      return res.status(400).json({ message: "Invalid action parameter." });
    } catch (error) {
      return res
        .status(500)
        .json({ message: error.message || "Internal Server Error" });
    }
  },
  
  getMissedCallList: async (req, res) => {
    try {
      const { userId } = req.params;
      console.log("userId", userId);
      const page = parseInt(req.query.page) || 1; // Default to page 1
      const limit = parseInt(req.query.limit) || 20; // Default to limit 20

      const response = await userService.getMissedCallList(
        userId ? parseInt(userId) : null,
        page,
        limit
      );

      return res.status(response.statusCode).json(response);
    } catch (error) {
      console.error("Error in getMissedCallList:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
      });
    }
  },
  // Controller for getting referral details
  getReferralDetails: async (req, res) => {
    try {
      const { userId } = req.params;

      const referralDetails = await userService.getEarnings(userId);

      return res.status(200).json({
        status: true,
        message: "Referral details fetched successfully.",
        data: referralDetails,
      });
    } catch (error) {
      console.error("Error in getReferralDetails:", error);
      return res.status(error.status || 500).json({
        status: false,
        message: error.message || "Internal Server Error",
      });
    }
  },

  isUserPremiumOrNot: async (req, res) => {
    try {
      const userId = req.params.id;
      const premium = await userService.isUserPremiumOrNot(userId);
      if (premium.statusCode == 200) {
        return res.status(200).json({
          status: true,
          message: premium.message,
          is_premium: premium.is_premium,
          premium_plan_id: premium.premium_plan_id,
        });
      } else {
        return res.status(400).json({
          status: false,
          message: premium.message,
        });
      }
    } catch (error) {
      return res.status(500).json({
        status: false,
        message: premium.message ? premium.message : "something went wrong",
      });
    }
  },

  followUser: async (req, res) => {
    try {
      if (!req.body.followingId || !req.body.followerId || !req.body.action)
        return res
          .status(400)
          .json({ status: false, message: "Invalid request.", data: [] });

      if (req.body.followingId == req.body.followerId) {
        return res.status(400).json({
          status: false,
          message: "you can't follow to yourself",
          data: [],
        });
      }
      const response = await userService.followUser({
        followingId: req.body.followingId,
        followerId: req.body.followerId,
        action: req.body.action,
      });
      if (response.error) {
        return res.status(400).json({
          status: false,
          message: response.message,
        });
      } else {
        //
        return res.status(200).json({
          status: true,
          message: response.message,
          is_following: response.is_following,
        });
      }
    } catch (error) {
      console.error("Controller Error:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  getFollowersOrFollowings: async (req, res) => {
    try {
      const { user_id, type } = req.params;

      // Validate user_id
      if (!user_id || isNaN(user_id)) {
        return res
          .status(400)
          .json({ status: false, message: "Invalid user ID" });
      }

      // Validate type (followers or followings)
      if (!["followers", "followings"].includes(type)) {
        return res.status(400).json({
          status: false,
          message: "Invalid type. Use 'followers' or 'followings'.",
        });
      }

      // Check if user exists
      // await userService.getUser(user_id);

      // Fetch followers or followings based on type
      const response =
        type === "followers"
          ? await userService.getFollowers(user_id)
          : await userService.getFollowings(user_id);

      if (response.error) {
        return res.status(400).json({ status: false, message: response.error });
      }
      return res.status(200).json({
        status: true,
        type,
        data: type === "followers" ? response.followers : response.followings,
      });
    } catch (error) {
      console.error("Error fetching follow data:", error);
      return res.status(error.status ? error.status : 500).json({
        status: false,
        message: error.message ? error.message : "Internal Server Error",
      });
    }
  },
 
  getBlockedAndUnblockedUsers: async (req, res) => {
    try {
      const { user_id, page = 1, limit = 10 } = req.body;

      // Validate user_id
      if (!user_id || isNaN(user_id)) {
        return res
          .status(400)
          .json({ status: false, message: "Invalid user ID" });
      }

      // Validate pagination parameters
      const pageNumber = parseInt(page, 10);
      const pageSize = parseInt(limit, 10);
      if (pageNumber < 1 || pageSize < 1) {
        return res
          .status(400)
          .json({ status: false, message: "Invalid pagination values" });
      }

      // Check if user exists
      await userService.getUser(user_id);
      // Fetch blocked and unblocked users
      const response = await userService.getBlockedUsers(
        user_id,
        pageNumber,
        pageSize
      );

      console.log("ress", response);

      return res.status(200).json({
        status: true,
        message: "Blocked and Unblocked User list retrieved successfully",
        data: response.list,
        total_users: response.total,
        total_pages: Math.ceil(response.total / pageSize),
        page: pageNumber,
        limit: pageSize,
      });
    } catch (error) {
      console.error("Error fetching blocked and unblocked users:", error);
      return res.status(error.status || 500).json({
        status: false,
        message: error.message || "Internal Server Error",
      });
    }
  },
  checkUserAvailability: async (req, res) => {
    try {
      const { user_id } = req.params;

      if (!user_id) {
        return res
          .status(400)
          .json({ status: false, message: "User ID is required" });
      }

      const { status, is_available, message ,dnd} =
        await userService.checkUserAvailability(user_id);

      if (!status) {
        return res.status(400).json({ status: false, message });
      }

      return res.status(200).json({
        status: true,
        message,
        is_available,
        dnd
      });
    } catch (error) {
      console.error("Internal Server Error:", error);
      return res.status(500).json({
        status: false,
        message: "Internal server error. Please try again later.",
      });
    }
  },

  // New function for /api/user/call-status
  checkUserAvailability2: async (req, res) => {
    try {
      const { receiverId, callerId } = req.query;

      if (!receiverId) {
        return res
          .status(400)
          .json({ status: false, message: "receiverId is required" });
      }

      // Optional: Validate callerId if needed
      if (!callerId) {
        return res.status(400).json({ status: false, message: "callerId is required" });
      }

      const { status, is_available, message, dnd } =
        await userService.checkUserAvailability(receiverId);

      if (!status) {
        return res.status(400).json({ status: false, message });
      }

      // Return response (adjust based on Flutter's needs)
      return res.status(200).json({
        status: true,
        message,
        is_available,
        dnd,
      });
    } catch (error) {
      console.error("Internal Server Error:", error);
      return res.status(500).json({
        status: false,
        message: "Internal server error. Please try again later.",
      });
    }
  },


// controllers/UsersController.js
  upgradePremium: async (req, res) => {
    try {
      const {
        payment_status,
        user_id,
        plan_id,
        order_id,
        payment_id,
        coupon_code,
        isCron,
      } = req.body;

      if (!payment_status || !user_id || !plan_id || !order_id || !payment_id) {
        return res
          .status(400)
          .json({ status: false, message: "Missing required parameters" });
      }

      // Validate transaction
      const transactionQuery = `
        SELECT status, payment_id, order_id
        FROM transactions
        WHERE payment_id = ? AND order_id = ? AND status = 'success'
      `;
      const transaction = await dbQuery.queryRunner(transactionQuery, [payment_id, order_id]);

      if (
        !transaction.length ||
        transaction[0].payment_id !== payment_id ||
        transaction[0].order_id !== order_id
      ) {
        return res
          .status(400)
          .json({ status: false, message: "Payment verification failed" });
      }

      const result = await userService.upgradePremium({
        payment_status,
        user_id,
        plan_id,
        order_id,
        payment_id,
        coupon_code,
      });

      if (result.statusCode === 400) {
        return res.status(400).json({ status: false, message: result.message });
      }

      // If not called by cron, update is_funds_added to 1
      if (!isCron) {
        const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
        await dbQuery.queryRunner(`
          UPDATE transactions
          SET is_funds_added = 1, updated_at = '${currentTime}'
          WHERE order_id = '${order_id}' AND payment_id = '${payment_id}'
        `);
      }

      return res.status(200).json({ status: true, message: result.message });
    } catch (error) {
      console.error("Error upgrading premium:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  getUserPremiumPlans: async (req, res) => {
    try {
      const { userId } = req.params;
      const result = await userService.getUserPremiumPlans(userId);
      if (result.statusCode === 400) {
        return res.status(400).json({ status: false, message: result.message });
      }
      return res.status(200).json({
        status: true,
        message: result.message,
        data: result.data,
      });
    } catch (error) {
      console.error("Error fetching user premium plans:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  getUserPremiumPlansCount: async (req, res) => {
    try {
      const { userId } = req.params;
      const result = await userService.getUserPremiumPlansCount(userId);
      if (result.statusCode === 400) {
        return res.status(400).json({ status: false, message: result.message });
      }
      return res.status(200).json({
        status: true,
        message: result.message,
        data: result.data,
      });
    } catch (error) {
      console.error("Error fetching user premium plans count:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  activatePremiumPlan: async (req, res) => {
    try {
      const { user_id, plan_id } = req.body;
      if (!user_id || !plan_id) {
        return res
          .status(400)
          .json({ status: false, message: "Missing user_id or plan_id" });
      }
      const result = await userService.activatePremiumPlan({ user_id, plan_id });
      if (result.statusCode === 400) {
        return res.status(400).json({ status: false, message: result.message });
      }
      return res.status(200).json({ status: true, message: result.message });
    } catch (error) {
      console.error("Error activating premium plan:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  // New method to get user by mobile number
  getUserByMobile: async (req, res) => {
    try {
      const { mobile_number } = req.params;

      if (!mobile_number) {
        return res.status(400).json({
          status: false,
          message: "Mobile number is required",
        });
      }

      const user = await userService.getUserByMobile(mobile_number);
      if (!user) {
        return res.status(404).json({
          status: false,
          message: "User not found",
        });
      }

      return res.status(200).json({
        status: true,
        data: user,
      });
    } catch (error) {
      console.error("Error in getUserByMobile:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
      });
    }
  },

  // New method to get multiple users by mobile numbers
  getMultipleUsers: async (req, res) => {
    try {
      let { mobile_numbers } = req.body;
  
      if (!mobile_numbers || !Array.isArray(mobile_numbers) || mobile_numbers.length === 0) {
        return res.status(400).json({
          status: false,
          message: "Mobile numbers must be a non-empty array",
        });
      }
  
      // Ensure each number has +91 prefix
      mobile_numbers = mobile_numbers.map((number) => {
        // Remove any spaces and ensure it starts with +91
        number = number.toString().trim();
        return number.startsWith("+91") ? number : `+91${number}`;
      });
  
      const users = await userService.getMultipleUsers(mobile_numbers);
      if (!users || users.length === 0) {
        return res.status(404).json({
          status: false,
          message: "No users found",
        });
      }
  
      return res.status(200).json({
        status: true,
        data: users,
      });
    } catch (error) {
      console.error("Error in getMultipleUsers:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
      });
    }
  },
  

  getTransactionsByUserId: async (req, res) => {
    try {
      const { user_id } = req.params;

      if (!user_id || isNaN(user_id)) {
        return res.status(400).json({
          status: false,
          message: "Valid user_id is required",
        });
      }

      const transactions = await userService.getTransactionsByUserId(parseInt(user_id));
      if (!transactions || transactions.length === 0) {
        return res.status(404).json({
          status: false,
          message: "No transactions found for this user",
        });
      }

      return res.status(200).json({
        status: true,
        data: transactions,
      });
    } catch (error) {
      console.error("Error in getTransactionsByUserId:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
      });
    }
  },

  getTransactionsByPhoneNumber: async (req, res) => {
    try {
      const { mobile_number } = req.body;

      // Validate input
      if (!mobile_number) {
        return res.status(400).json({
          status: false,
          message: "Mobile number is required",
        });
      }

      // Normalize phone number (remove +91 if present)
      const normalizedNumber = mobile_number.replace(/^\+91/, '').trim();
      
      // Validate phone number format
      if (!/^\d{10}$/.test(normalizedNumber)) {
        return res.status(400).json({
          status: false,
          message: "Invalid mobile number format. Please provide a valid 10-digit number",
        });
      }

      // Get user by phone number
      const user = await userService.getUserByPhoneNumber(normalizedNumber);
      
      if (!user) {
        return res.status(404).json({
          status: false,
          message: "No user found with this mobile number",
        });
      }

      // Get transactions by user ID
      const transactions = await userService.getTransactionsByUserId(user.id);
      
      if (!transactions || transactions.length === 0) {
        return res.status(404).json({
          status: false,
          message: "No transactions found for this user",
        });
      }

      return res.status(200).json({
        status: true,
        data: transactions,
      });
    } catch (error) {
      console.error("Error in getTransactionsByPhoneNumber:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
      });
    }
  },  

  getOrdersByUserId: async (req, res) => {
    try {
      const { user_id } = req.params;

      if (!user_id || isNaN(user_id)) {
        return res.status(400).json({
          status: false,
          message: "Valid user_id is required",
        });
      }

      const orders = await userService.getOrdersByUserId(parseInt(user_id));
      if (!orders || orders.length === 0) {
        return res.status(404).json({
          status: false,
          message: "No orders found for this user",
        });
      }

      return res.status(200).json({
        status: true,
        data: orders,
      });
    } catch (error) {
      console.error("Error in getOrdersByUserId:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
      });
    }
  },

  upgradeContentPremium: async (req, res) => {
    try {
      const {
        payment_status,
        user_id,
        plan_id,
        order_id,
        payment_id,
        coupon_code,
        isCron,
      } = req.body;

      if (!payment_status || !user_id || !plan_id || !order_id || !payment_id) {
        return res
          .status(400)
          .json({ status: false, message: "Missing required parameters" });
      }

      // Validate transaction
      const transactionQuery = `
        SELECT status, payment_id, order_id
        FROM transactions
        WHERE payment_id = ? AND order_id = ? AND status = 'success'
      `;
      const transaction = await dbQuery.queryRunner(transactionQuery, [payment_id, order_id]);

      if (
        !transaction.length ||
        transaction[0].payment_id !== payment_id ||
        transaction[0].order_id !== order_id
      ) {
        return res
          .status(400)
          .json({ status: false, message: "Payment verification failed" });
      }

      const result = await userService.upgradeContentPremium({
        payment_status,
        user_id,
        plan_id,
        order_id,
        payment_id,
        coupon_code,
      });

      if (result.statusCode === 400) {
        return res.status(400).json({ status: false, message: result.message });
      }

      // If not called by cron, update is_funds_added to 1
      if (!isCron) {
        const currentTime = moment().utcOffset(330).format("YYYY-MM-DD HH:mm:ss");
        await dbQuery.queryRunner(`
          UPDATE transactions
          SET is_funds_added = 1, updated_at = '${currentTime}'
          WHERE order_id = '${order_id}' AND payment_id = '${payment_id}'
        `);
      }

      return res.status(200).json({ status: true, message: result.message });
    } catch (error) {
      console.error("Error upgrading content premium:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },

  getContentPremiumPlans: async (req, res) => {
    try {
      const { user_id } = req.params;

      if (!user_id || isNaN(user_id)) {
        return res
          .status(400)
          .json({ status: false, message: "Invalid or missing user_id" });
      }

      const result = await userService.getContentPremiumPlans(user_id);

      if (result.statusCode === 404) {
        return res.status(404).json({ status: false, message: result.message });
      }

      return res.status(200).json({
        status: true,
        message: result.message,
        data: result.data,
      });
    } catch (error) {
      console.error("Error fetching content premium plans:", error);
      return res
        .status(500)
        .json({ status: false, message: "Internal Server Error" });
    }
  },
  
  getSingleUserName : async (req, res) => {
    const { userid } = req.body;

    if (!userid) {
        return res.status(400).json({ 
            status: false, 
            message: "User ID is required",
            data: null 
        });
    }

    try {
        const name = await userService.getUserNameById(userid);
        if (name) {
            res.status(200).json({ 
                status: true, 
                message: "User name retrieved successfully",
                data: {
                    userid: userid,
                    name: name
                }
            });
        } else {
            res.status(404).json({ 
                status: false, 
                message: "User not found",
                data: null 
            });
        }
    } catch (err) {
        console.error('Error in getSingleUserName:', err);
        res.status(500).json({ 
            status: false, 
            message: "Internal server error",
            data: null 
        });
    }
  },

// Controller for multiple users
getMultipleUserNames : async (req, res) => {
    const { userid } = req.body;

    if (!Array.isArray(userid) || userid.length === 0) {
        return res.status(400).json({ 
            status: false, 
            message: "User IDs must be a non-empty array",
            data: null 
        });
    }

    try {
        const users = await userService.getUserNamesByIds(userid);
        res.status(200).json({ 
            status: true, 
            message: "User names retrieved successfully",
            data: users 
        });
    } catch (err) {
        console.error('Error in getMultipleUserNames:', err);
        res.status(500).json({ 
            status: false, 
            message: "Internal server error",
            data: null 
        });
    }
  },

  // Search users by name with pagination
  searchUsersByName: async (req, res) => {
    try {
      const { search_by_name, page = 1, limit = 20 } = req.body;
      const loggedInUserId = req.user?.user_id;

      // Validate required parameters
      if (!search_by_name || typeof search_by_name !== 'string' || search_by_name.trim() === '') {
        return res.status(400).json({
          status: false,
          message: "search_by_name is required and must be a non-empty string"
        });
      }

      if (!loggedInUserId) {
        return res.status(401).json({
          status: false,
          message: "User authentication required"
        });
      }

      // Validate pagination parameters
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      
      if (isNaN(pageNum) || pageNum < 1 || isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        return res.status(400).json({
          status: false,
          message: "Invalid pagination parameters. page must be >= 1, limit must be between 1 and 100"
        });
      }

      const offset = (pageNum - 1) * limitNum;
      const searchTerm = search_by_name.trim();

      console.log(`[UsersController] Searching users with term: "${searchTerm}", page: ${pageNum}, limit: ${limitNum}, userId: ${loggedInUserId}`);

      // Optimized query for fast search
      const query = `
        SELECT 
          id,
          name,
          profile_image
        FROM users 
        WHERE 
          (name LIKE ? OR firstName LIKE ? OR lastName LIKE ? OR username LIKE ?)
          AND id != ?
        ORDER BY 
          CASE 
            WHEN name LIKE ? THEN 1
            WHEN firstName LIKE ? OR lastName LIKE ? THEN 2
            WHEN username LIKE ? THEN 3
            ELSE 4
          END,
          name ASC
        LIMIT ? OFFSET ?
      `;

      const searchPattern = `%${searchTerm}%`;
      const exactMatch = `${searchTerm}%`;
      
      const params = [
        searchPattern, // name LIKE
        searchPattern, // firstName LIKE
        searchPattern, // lastName LIKE
        searchPattern, // username LIKE
        loggedInUserId, // exclude current user
        exactMatch, // name exact match for ordering
        exactMatch, // firstName exact match for ordering
        exactMatch, // lastName exact match for ordering
        exactMatch, // username exact match for ordering
        limitNum,
        offset
      ];

      const users = await dbQuery.queryRunner(query, params);

      console.log(`[UsersController] Found ${users.length} users for search term: "${searchTerm}"`);

      // Get total count for pagination info
      const countQuery = `
        SELECT COUNT(*) as total
        FROM users 
        WHERE 
          (name LIKE ? OR firstName LIKE ? OR lastName LIKE ? OR username LIKE ?)
          AND id != ?
      `;

      const countParams = [searchPattern, searchPattern, searchPattern, searchPattern, loggedInUserId];
      const [countResult] = await dbQuery.queryRunner(countQuery, countParams);
      const totalUsers = countResult.total;

      return res.status(200).json({
        status: true,
        message: "Users found successfully",
        data: {
          users: users,
          pagination: {
            current_page: pageNum,
            total_pages: Math.ceil(totalUsers / limitNum),
            total_users: totalUsers,
            limit: limitNum,
            has_next: (pageNum * limitNum) < totalUsers,
            has_prev: pageNum > 1
          }
        }
      });

    } catch (error) {
      console.error("Error in searchUsersByName:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error"
      });
    }
  },

  // Search content (videos, posts, etc.) with pagination
  searchContent: async (req, res) => {
    try {
      const { search_query, page = 1, limit = 20, content_type = 'all' } = req.body;
      const loggedInUserId = req.user?.user_id;

      // Validate required parameters
      if (!search_query || typeof search_query !== 'string' || search_query.trim() === '') {
        return res.status(400).json({
          status: false,
          message: "search_query is required and must be a non-empty string"
        });
      }

      if (!loggedInUserId) {
        return res.status(401).json({
          status: false,
          message: "User authentication required"
        });
      }

      // Validate pagination parameters
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);

      if (isNaN(pageNum) || pageNum < 1 || isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
        return res.status(400).json({
          status: false,
          message: "Invalid pagination parameters. page must be >= 1, limit must be between 1 and 100"
        });
      }

      const offset = (pageNum - 1) * limitNum;
      const searchTerm = search_query.trim();

      console.log(`[UsersController] Searching content with term: "${searchTerm}", type: ${content_type}, page: ${pageNum}, limit: ${limitNum}`);

      let results = [];
      let totalCount = 0;

      // Search videos/shots
      if (content_type === 'all' || content_type === 'videos') {
        const videoQuery = `
          SELECT
            'video' as content_type,
            id,
            name as title,
            videoDesciption as description,
            video_Thumbnail as thumbnail,
            videoLink as url,
            createdby as user_id,
            createddate as created_at
          FROM shots
          WHERE
            (name LIKE ? OR videoDesciption LIKE ?)
            AND isActive = 1
          ORDER BY createddate DESC
          LIMIT ? OFFSET ?
        `;

        const searchPattern = `%${searchTerm}%`;
        const videoParams = [searchPattern, searchPattern, limitNum, offset];

        const videos = await dbQuery.queryRunner(videoQuery, videoParams);
        results = [...results, ...videos];

        // Get video count
        const videoCountQuery = `
          SELECT COUNT(*) as total
          FROM shots
          WHERE
            (name LIKE ? OR videoDesciption LIKE ?)
            AND isActive = 1
        `;
        const [videoCountResult] = await dbQuery.queryRunner(videoCountQuery, [searchPattern, searchPattern]);
        totalCount += videoCountResult.total;
      }

      // Search posts
      if (content_type === 'all' || content_type === 'posts') {
        const postQuery = `
          SELECT
            'post' as content_type,
            id,
            title,
            content as description,
            '' as thumbnail,
            '' as url,
            createdby as user_id,
            createddate as created_at
          FROM posts
          WHERE
            (title LIKE ? OR content LIKE ?)
            AND isActive = 1
          ORDER BY createddate DESC
          LIMIT ? OFFSET ?
        `;

        const searchPattern = `%${searchTerm}%`;
        const postParams = [searchPattern, searchPattern, limitNum, offset];

        const posts = await dbQuery.queryRunner(postQuery, postParams);
        results = [...results, ...posts];

        // Get post count
        const postCountQuery = `
          SELECT COUNT(*) as total
          FROM posts
          WHERE
            (title LIKE ? OR content LIKE ?)
            AND isActive = 1
        `;
        const [postCountResult] = await dbQuery.queryRunner(postCountQuery, [searchPattern, searchPattern]);
        totalCount += postCountResult.total;
      }

      console.log(`[UsersController] Found ${results.length} content items for search term: "${searchTerm}"`);

      return res.status(200).json({
        status: true,
        message: "Content found successfully",
        data: {
          content: results,
          pagination: {
            current_page: pageNum,
            total_pages: Math.ceil(totalCount / limitNum),
            total_items: totalCount,
            limit: limitNum,
            has_next: (pageNum * limitNum) < totalCount,
            has_prev: pageNum > 1
          }
        }
      });

    } catch (error) {
      console.error("Error in searchContent:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error"
      });
    }
  },

  // New method to get user data by POST request
  getUserByPost: async (req, res) => {
    try {
      const { userid } = req.body;

      // Validate userid parameter
      if (!userid) {
        return res.status(400).json({
          status: false,
          message: "userid is required in request body",
          data: null
        });
      }

      // Validate userid is a number
      const userId = parseInt(userid);
      if (isNaN(userId) || userId <= 0) {
        return res.status(400).json({
          status: false,
          message: "userid must be a valid positive number",
          data: null
        });
      }

      // Direct database query to get user data
      const query = `SELECT * FROM users WHERE id = ? LIMIT 1`;
      const result = await dbQuery.queryRunner(query, [userId]);
      
      if (result && result.length > 0) {
        return res.status(200).json({
          status: true,
          message: "User data retrieved successfully",
          data: result[0]
        });
      } else {
        return res.status(404).json({
          status: false,
          message: "User not found",
          data: null
        });
      }

    } catch (error) {
      console.error("Error in getUserByPost:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
        data: null
      });
    }
  },

  // Consolidated profile API - gets user data, posts, followers, following in one call
  getConsolidatedProfile: async (req, res) => {
    try {
      const { userId } = req.params;
      const { loggined_user_id } = req.query;

      // Validate userId parameter
      if (!userId || isNaN(userId)) {
        return res.status(400).json({
          status: false,
          message: "Valid user ID is required",
          data: null
        });
      }

      const userIdNum = parseInt(userId, 10);
      const loggedInUserIdNum = loggined_user_id ? parseInt(loggined_user_id, 10) : null;

      // Get user basic data
      const query = `SELECT * FROM users WHERE id = ? LIMIT 1`;
      const userData = await dbQuery.queryRunner(query, [userIdNum]);

      if (!userData || userData.length === 0) {
        return res.status(404).json({
          status: false,
          message: "User not found",
          data: null
        });
      }

      const user = userData[0];

      // Get user posts with thumbnails (limit to 20 for performance)
      const postService = require("../services/Post");
      console.log('[ConsolidatedProfile] Fetching posts for user:', userIdNum);
      const postsResponse = await postService.getUserPosts({
        userId: userIdNum,
        page: 1,
        limit: 20,
        loggined_user_id: loggedInUserIdNum
      });
      console.log('[ConsolidatedProfile] Posts response:', postsResponse);

      // Get followers
      console.log('[ConsolidatedProfile] Fetching followers for user:', userIdNum);
      const followersResponse = await userService.getFollowers(userIdNum);
      console.log('[ConsolidatedProfile] Followers response:', followersResponse);
      const followers = followersResponse.error ? [] : followersResponse.followers || [];

      // Get followings
      console.log('[ConsolidatedProfile] Fetching followings for user:', userIdNum);
      const followingsResponse = await userService.getFollowings(userIdNum);
      console.log('[ConsolidatedProfile] Followings response:', followingsResponse);
      const followings = followingsResponse.error ? [] : followingsResponse.followings || [];

      // Check if logged-in user is following this user
      let isFollowing = false;
      if (loggedInUserIdNum && loggedInUserIdNum !== userIdNum) {
        isFollowing = followers.some(follower => follower.id === loggedInUserIdNum);
      }

      // Check if user is blocked (placeholder for future implementation)
      let isBlocked = false;

      // Prepare consolidated response
      const consolidatedProfile = {
        user: {
          id: user.id,
          name: user.name,
          username: user.username || user.name,
          display_name: user.display_name || user.name,
          bio: user.bio,
          profile_image: user.profile_image,
          emailId: user.emailId,
          mobile_number: user.mobile_number,
          gender: user.gender,
          profession: user.profession,
          address: user.address,
          online_status: user.online_status,
          is_available: user.is_available,
          dnd: user.dnd,
          last_seen: user.last_seen,
          is_premium: user.premium || false,
          premium_expires_at: user.premium_expires_at
        },
        posts: postsResponse.error ? [] : (postsResponse.data || []),
        social_stats: {
          followers_count: followers.length,
          following_count: followings.length,
          posts_count: postsResponse.error ? 0 : (postsResponse.pagination?.total_posts || (postsResponse.data ? postsResponse.data.length : 0))
        },
        is_following: isFollowing,
        is_blocked: isBlocked,
        followers: followers,
        followings: followings
      };

      return res.status(200).json({
        status: true,
        message: "Profile data retrieved successfully",
        data: consolidatedProfile
      });

    } catch (error) {
      console.error("Error in getConsolidatedProfile:", error);
      return res.status(500).json({
        status: false,
        message: "Internal Server Error",
        data: null
      });
    }
  },

};

