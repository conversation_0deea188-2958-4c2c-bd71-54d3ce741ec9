<variant
    name="release"
    useSupportLibraryVectorDrawables="true"
    package="com.adtip.app.adtip_app"
    minSdkVersion="24"
    targetSdkVersion="35"
    mergedManifest="build\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.8.2;proguard-rules.pro;build\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.8.2;proguard-rules.pro"
    resourceConfigurations="en,xxhdpi"
    partialResultsDir="build\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="F:\R17DevTools\.gradle\caches\8.13\transforms\bf9716b63f097dbb8db4edf8dde98ede\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;build\generated\source\codegen\java;src\release\java;build\generated\autolinking\src\main\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;build\intermediates\ReactNativeVectorIcons;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <resValues>
    <resValue
        type="integer"
        name="react_native_dev_server_port"
        value="8081" />
  </resValues>
  <artifact
      classOutputs="build\intermediates\javac\release\compileReleaseJavaWithJavac\classes;build\tmp\kotlin-classes\release;build\kotlinToolingMetadata;build\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.adtip.app.adtip_app"
      generatedSourceFolders="build\generated\ap_generated_sources\release\out;build\generated\source\buildConfig\release"
      generatedResourceFolders="build\generated\res\createBundleReleaseJsAndAssets;build\generated\res\processReleaseGoogleServices;build\generated\crashlytics\res\release;build\generated\res\resValues\release"
      desugaredMethodsFiles="F:\R17DevTools\.gradle\caches\8.13\transforms\bf9716b63f097dbb8db4edf8dde98ede\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
