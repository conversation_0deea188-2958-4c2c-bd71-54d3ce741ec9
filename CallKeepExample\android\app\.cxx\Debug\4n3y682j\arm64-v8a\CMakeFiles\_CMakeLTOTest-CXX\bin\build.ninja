# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: lto-test
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin/
# =============================================================================
# Object build statements for STATIC_LIBRARY target foo


#############################################
# Order-only phony target for foo

build cmake_object_order_depends_target_foo: phony || CMakeFiles/foo.dir

build CMakeFiles/foo.dir/foo.cpp.o: CXX_COMPILER__foo_ /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/src/foo.cpp || cmake_object_order_depends_target_foo
  DEP_FILE = CMakeFiles/foo.dir/foo.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -fPIC
  OBJECT_DIR = CMakeFiles/foo.dir
  OBJECT_FILE_DIR = CMakeFiles/foo.dir


# =============================================================================
# Link build statements for STATIC_LIBRARY target foo


#############################################
# Link the static library libfoo.a

build libfoo.a: CXX_STATIC_LIBRARY_LINKER__foo_ CMakeFiles/foo.dir/foo.cpp.o
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin
  OBJECT_DIR = CMakeFiles/foo.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = libfoo.a
  TARGET_PDB = foo.a.dbg

# =============================================================================
# Object build statements for EXECUTABLE target boo


#############################################
# Order-only phony target for boo

build cmake_object_order_depends_target_boo: phony || cmake_object_order_depends_target_foo

build CMakeFiles/boo.dir/main.cpp.o: CXX_COMPILER__boo_ /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/src/main.cpp || cmake_object_order_depends_target_boo
  DEP_FILE = CMakeFiles/boo.dir/main.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin -fPIE
  OBJECT_DIR = CMakeFiles/boo.dir
  OBJECT_FILE_DIR = CMakeFiles/boo.dir


# =============================================================================
# Link build statements for EXECUTABLE target boo


#############################################
# Link the executable boo

build boo: CXX_EXECUTABLE_LINKER__boo_ CMakeFiles/boo.dir/main.cpp.o | libfoo.a || libfoo.a
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -flto=thin
  LINK_FLAGS = -static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments     -fuse-ld=gold
  LINK_LIBRARIES = libfoo.a  -latomic -lm
  OBJECT_DIR = CMakeFiles/boo.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = boo
  TARGET_PDB = boo.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ccmake -S/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/src -B/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin && /Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake --regenerate-during-build -S/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/src -B/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build foo: phony libfoo.a

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/bin

build all: phony libfoo.a boo

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/src/CMakeLists.txt /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/3.22.1-g37088a8/CMakeCXXCompiler.cmake /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/3.22.1-g37088a8/CMakeSystem.cmake /Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/_CMakeLTOTest-CXX/src/CMakeLists.txt /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake /Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/abis.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
