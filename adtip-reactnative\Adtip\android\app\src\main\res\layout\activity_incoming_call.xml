<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="#222"
    android:padding="32dp">

    <TextView
        android:id="@+id/caller_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Caller Name"
        android:textColor="#fff"
        android:textSize="28sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/call_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Call Type"
        android:textColor="#aaa"
        android:textSize="20sp"
        android:layout_marginBottom="32dp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/answer_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Answer"
            android:backgroundTint="#4CAF50"
            android:textColor="#fff"
            android:layout_marginEnd="24dp" />

        <Button
            android:id="@+id/decline_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Decline"
            android:backgroundTint="#F44336"
            android:textColor="#fff" />
    </LinearLayout>
</LinearLayout> 