current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/nkf-0.2.0/ext/nkf
/Users/<USER>/.rbenv/versions/3.2.2/bin/ruby extconf.rb
creating Makefile

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/nkf-0.2.0/ext/nkf
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-lstbtx sitelibdir\=./.gem.20250722-11947-lstbtx clean

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/nkf-0.2.0/ext/nkf
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-lstbtx sitelibdir\=./.gem.20250722-11947-lstbtx
compiling nkf.c
linking shared-object nkf.bundle
ld: warning: ignoring duplicate libraries: '-lruby.3.2'

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/nkf-0.2.0/ext/nkf
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-lstbtx sitelibdir\=./.gem.20250722-11947-lstbtx install
/usr/bin/install -c -m 0755 nkf.bundle ./.gem.20250722-11947-lstbtx

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/nkf-0.2.0/ext/nkf
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-lstbtx sitelibdir\=./.gem.20250722-11947-lstbtx clean
