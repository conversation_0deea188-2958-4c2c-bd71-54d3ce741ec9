const functions = require("firebase-functions");
const express = require("express");
const cors = require("cors");
const morgan = require("morgan");
const admin = require("firebase-admin");

const serviceAccount = require("./serviceAccountKey.json");

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

// Call API App
const callApp = express();

// Middleware for call app
callApp.use(cors());
callApp.use(express.json());
callApp.use(express.urlencoded({extended: true}));
callApp.use(morgan("dev"));

// Call routes
callApp.get("/", (req, res) => {
  res.send("Call API - Hello World!");
});
callApp.use("/api/call", require("./routes/firebaseCallRoutes"));

// Chat API App
const chatApp = express();

// Middleware for chat app
chatApp.use(cors());
chatApp.use(express.json());
chatApp.use(express.urlencoded({extended: true}));
chatApp.use(morgan("dev"));

// Chat routes
chatApp.get("/", (req, res) => {
  res.send("Chat API - Hello World!");
});
chatApp.use("/api/chat", require("./routes/firebaseChatRoutes"));

// Export Firebase Functions
exports.callApi = functions.https.onRequest(callApp);
exports.chatApi = functions.https.onRequest(chatApp);
