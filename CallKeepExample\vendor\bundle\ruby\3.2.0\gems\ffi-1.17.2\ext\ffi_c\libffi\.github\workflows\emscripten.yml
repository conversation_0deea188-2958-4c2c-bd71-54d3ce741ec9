name: <PERSON>I emscripten

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:


env:
  PYODIDE_VERSION: 0.58.4
  # PYTHON_VERSION and EMSCRIPTEN_VERSION are determined by PYODIDE_VERSION.
  # The appropriate versions can be found in the Pyodide repodata.json
  # "info" field, or in Makefile.envs:
  # https://github.com/pyodide/pyodide/blob/main/Makefile.envs#L2
  PYTHON_VERSION: 3.12.7
  EMSCRIPTEN_VERSION: 3.1.58
  EM_CACHE_FOLDER: emsdk-cache

jobs:
  setup-emsdk-cache:
    runs-on: ubuntu-22.04
    steps:
      - name: Setup cache
        uses: actions/cache@v4
        with:
          path: ${{ env.EM_CACHE_FOLDER }}
          key: ${{ env.EMSCRIPTEN_VERSION }}

      - name: Setup emsdk
        uses: mymindstorm/setup-emsdk@v14
        with:
          version: ${{ env.EMSCRIPTEN_VERSION }}
          actions-cache-folder: ${{ env.EM_CACHE_FOLDER }}

  test-dejagnu:
    runs-on: ubuntu-24.04
    needs: [setup-emsdk-cache]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Setup cache
        uses: actions/cache@v4
        with:
          path: ${{ env.EM_CACHE_FOLDER }}
          key: ${{ env.EMSCRIPTEN_VERSION }}

      - name: Setup emsdk
        uses: mymindstorm/setup-emsdk@v14
        with:
          version: ${{ env.EMSCRIPTEN_VERSION }}
          actions-cache-folder: ${{ env.EM_CACHE_FOLDER }}

      - name: Install dependencies
        run: sudo apt-get install dejagnu libltdl-dev

      - name: Run tests
        run: testsuite/emscripten/node-tests.sh

      - name: Install rlgl and run
        run: |
          wget -qO - https://rl.gl/cli/rlgl-linux-amd64.tgz | \
               tar --strip-components=2 -xvzf - ./rlgl/rlgl;
          ./rlgl l --key=0LIBFFI-0LIBFFI-0LIBFFI-0LIBFFI https://rl.gl
          ./rlgl e -l project=libffi -l sha=${GITHUB_SHA:0:7} -l CC='emcc' -l host=wasm32-unknown-linux --policy=https://github.com/libffi/rlgl-policy.git testsuite/libffi.log
          exit $?

  build:
    runs-on: ubuntu-24.04
    needs: [setup-emsdk-cache]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup cache
        uses: actions/cache@v4
        with:
          path: ${{ env.EM_CACHE_FOLDER }}
          key: ${{ env.EMSCRIPTEN_VERSION }}

      - name: Setup emsdk
        uses: mymindstorm/setup-emsdk@v12
        with:
          version: ${{ env.EMSCRIPTEN_VERSION }}
          actions-cache-folder: ${{ env.EM_CACHE_FOLDER }}

      - name: Install dependencies
        run: sudo apt-get install libltdl-dev

      - name: Build
        run: ./testsuite/emscripten/build.sh

      - name: Build tests
        run: |
          cp -r testsuite/libffi.call testsuite/libffi.call.test
          cp -r testsuite/libffi.closures testsuite/libffi.closures.test
          ./testsuite/emscripten/build-tests.sh testsuite/libffi.call.test
          ./testsuite/emscripten/build-tests.sh testsuite/libffi.closures.test

      - name: Store artifacts
        uses: actions/upload-artifact@v4
        with:
          name: built-tests
          path: ./testsuite/libffi.c*/

  test:
    strategy:
      matrix:
        browser: ["chrome"]
        # FIXME: selenium can't find gecko driver for "firefox"
    runs-on: ubuntu-24.04
    needs: [build]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: built-tests
          path: ./testsuite/

      - uses: conda-incubator/setup-miniconda@v3
        with:
          activate-environment: pyodide-env
          python-version: ${{ env.PYTHON_VERSION }}
          channels: conda-forge

      - name: Install test dependencies
        run: pip install pytest-pyodide==${{ env.PYODIDE_VERSION }}

      - name: Run tests
        run: |
          cd testsuite/emscripten/
          mkdir test-results
          pytest \
            --junitxml=test-results/junit.xml \
              test_libffi.py \
              -k ${{ matrix.browser }} \
              -s

      - name: Install rlgl and run
        if: success() || failure()
        run: |
          wget -qO - https://rl.gl/cli/rlgl-linux-amd64.tgz | \
               tar --strip-components=2 -xvzf - ./rlgl/rlgl;
          ./rlgl l --key=0LIBFFI-0LIBFFI-0LIBFFI-0LIBFFI https://rl.gl
          ./rlgl e -l project=libffi -l sha=${GITHUB_SHA:0:7} -l CC='emcc' -l host=${{ matrix.browser }} --policy=https://github.com/libffi/rlgl-policy.git testsuite/emscripten/test-results/junit.xml
          exit $?
