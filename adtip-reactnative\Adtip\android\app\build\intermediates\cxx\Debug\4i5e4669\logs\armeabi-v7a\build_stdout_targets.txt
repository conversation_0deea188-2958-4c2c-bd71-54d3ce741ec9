ninja: Entering directory `F:\A1\adtip-reactnative\Adtip\android\app\.cxx\Debug\4i5e4669\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/2] Re-running CMake...
-- Configuring done
-- Generating done
-- Build files have been written to: F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/armeabi-v7a
[0/2] Re-checking globbed directories...
[1/3] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[2/3] Building CXX object CMakeFiles/appmodules.dir/F_/A1/adtip-reactnative/Adtip/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[3/3] Linking CXX shared library F:\A1\adtip-reactnative\Adtip\android\app\build\intermediates\cxx\Debug\4i5e4669\obj\armeabi-v7a\libappmodules.so
