{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "RNFastImageSpec_autolinked_build", "jsonFile": "directory-RNFastImageSpec_autolinked_build-RelWithDebInfo-dfb63b574162c97f447f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-RelWithDebInfo-b421cdf6739ba9891687.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [13]}, {"build": "rnclipboard_autolinked_build", "jsonFile": "directory-rnclipboard_autolinked_build-RelWithDebInfo-aa66ff63432e064779b0.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni", "targetIndexes": [15]}, {"build": "rnblurview_autolinked_build", "jsonFile": "directory-rnblurview_autolinked_build-RelWithDebInfo-bb0c1bd343389a72f0c4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni", "targetIndexes": [14]}, {"build": "RNDateTimePickerCGen_autolinked_build", "jsonFile": "directory-RNDateTimePickerCGen_autolinked_build-RelWithDebInfo-ccc6f59575692787370e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "rnskia_autolinked_build", "jsonFile": "directory-rnskia_autolinked_build-RelWithDebInfo-7c212c780a529318aca6.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni", "targetIndexes": [19]}, {"build": "lottiereactnative_autolinked_build", "jsonFile": "directory-lottiereactnative_autolinked_build-RelWithDebInfo-e4b4bc29e1f4730f3d5c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni", "targetIndexes": [11]}, {"build": "Compressor_autolinked_build", "jsonFile": "directory-Compressor_autolinked_build-RelWithDebInfo-2379fc91203e5abbe7fa.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "RNDatePickerSpecs_autolinked_build", "jsonFile": "directory-RNDatePickerSpecs_autolinked_build-RelWithDebInfo-ecb9ae992fba2667324a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-RelWithDebInfo-0f7affa9a169c68c1bf8.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [16]}, {"build": "RNGoogleMobileAdsSpec_autolinked_build", "jsonFile": "directory-RNGoogleMobileAdsSpec_autolinked_build-RelWithDebInfo-03cb7747d8ab37e3c3ad.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "RNCImageCropPickerSpec_autolinked_build", "jsonFile": "directory-RNCImageCropPickerSpec_autolinked_build-RelWithDebInfo-200561aaaab0c2814a7a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "RNImagePickerSpec_autolinked_build", "jsonFile": "directory-RNImagePickerSpec_autolinked_build-RelWithDebInfo-3f606f55054b40da8a21.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni", "targetIndexes": [8]}, {"build": "pagerview_autolinked_build", "jsonFile": "directory-pagerview_autolinked_build-RelWithDebInfo-cda35eab67b27b2254a9.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni", "targetIndexes": [12]}, {"build": "RNPermissionsSpec_autolinked_build", "jsonFile": "directory-RNPermissionsSpec_autolinked_build-RelWithDebInfo-1f6ec89602826a5561b1.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni", "targetIndexes": [9]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-RelWithDebInfo-c7af50fe5a0eb47f900c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [17]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-RelWithDebInfo-8398b35f7ed692d6ae04.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [22]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-RelWithDebInfo-e6b21332386c2d36b428.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [18]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-RelWithDebInfo-47cc5cba3a72239f8d8a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [20]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-RelWithDebInfo-82075dba89b1e9fe96f3.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [10]}, {"build": "rnviewshot_autolinked_build", "jsonFile": "directory-rnviewshot_autolinked_build-RelWithDebInfo-c5f94f4631fb333d1a98.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni", "targetIndexes": [21]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-RelWithDebInfo-72ad79653ac3eea490f5.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [3]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-RelWithDebInfo-7cb00fdc735176de2aa9.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_Compressor::@408161e29a6d5b274579", "jsonFile": "target-react_codegen_Compressor-RelWithDebInfo-8eda3687be066729d199.json", "name": "react_codegen_Compressor", "projectIndex": 0}, {"directoryIndex": 12, "id": "react_codegen_RNCImageCropPickerSpec::@702b02f8d2524609d414", "jsonFile": "target-react_codegen_RNCImageCropPickerSpec-RelWithDebInfo-a9a39ff20c6002c99382.json", "name": "react_codegen_RNCImageCropPickerSpec", "projectIndex": 0}, {"directoryIndex": 22, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-RelWithDebInfo-f5cd595d891887c51d8c.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNDatePickerSpecs::@c00f981c6e74346c63d4", "jsonFile": "target-react_codegen_RNDatePickerSpecs-RelWithDebInfo-b1c1e309dd017f8c8bde.json", "name": "react_codegen_RNDatePickerSpecs", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2", "jsonFile": "target-react_codegen_RNDateTimePickerCGen-RelWithDebInfo-71fc8f0b0617392d738a.json", "name": "react_codegen_RNDateTimePickerCGen", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_RNFastImageSpec::@5f53d33017f3c907f455", "jsonFile": "target-react_codegen_RNFastImageSpec-RelWithDebInfo-030c1aeb990659f2fdfd.json", "name": "react_codegen_RNFastImageSpec", "projectIndex": 0}, {"directoryIndex": 11, "id": "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c", "jsonFile": "target-react_codegen_RNGoogleMobileAdsSpec-RelWithDebInfo-a2aaa3dfa1b909fb2f7a.json", "name": "react_codegen_RNGoogleMobileAdsSpec", "projectIndex": 0}, {"directoryIndex": 13, "id": "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4", "jsonFile": "target-react_codegen_RNImagePickerSpec-RelWithDebInfo-3e530ea88e15bdc626a4.json", "name": "react_codegen_RNImagePickerSpec", "projectIndex": 0}, {"directoryIndex": 15, "id": "react_codegen_RNPermissionsSpec::@7ad697819b753921c957", "jsonFile": "target-react_codegen_RNPermissionsSpec-RelWithDebInfo-3f419f9db7c4118e7471.json", "name": "react_codegen_RNPermissionsSpec", "projectIndex": 0}, {"directoryIndex": 20, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-RelWithDebInfo-aff8e6fc5a9567bf270d.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb", "jsonFile": "target-react_codegen_lottiereactnative-RelWithDebInfo-85540919c04468181e15.json", "name": "react_codegen_lottiereactnative", "projectIndex": 0}, {"directoryIndex": 14, "id": "react_codegen_pagerview::@7032a8921530ec438d60", "jsonFile": "target-react_codegen_pagerview-RelWithDebInfo-bade0d8904e8e7601a23.json", "name": "react_codegen_pagerview", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-RelWithDebInfo-41de553d2e4c46ec1963.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnblurview::@9a34ffec6e39b5c9049e", "jsonFile": "target-react_codegen_rnblurview-RelWithDebInfo-414583e71cb4485f08f1.json", "name": "react_codegen_rnblurview", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnclipboard::@6385240493dfcaf22ab7", "jsonFile": "target-react_codegen_rnclipboard-RelWithDebInfo-4175f36f1aed1c1e21b2.json", "name": "react_codegen_rnclipboard", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-RelWithDebInfo-3eed107748ec87f61ae3.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 16, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-RelWithDebInfo-04d6ff09c0de8e5edd55.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 18, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-RelWithDebInfo-2ae5a776cd3d867f2e06.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnskia::@376d8504f62839611b97", "jsonFile": "target-react_codegen_rnskia-RelWithDebInfo-9f00f239708f596075ef.json", "name": "react_codegen_rnskia", "projectIndex": 0}, {"directoryIndex": 19, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-RelWithDebInfo-7004ecf6bee75095d86b.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 21, "id": "react_codegen_rnviewshot::@0ba03d237e60b9258a87", "jsonFile": "target-react_codegen_rnviewshot-RelWithDebInfo-444209c685835bc44b76.json", "name": "react_codegen_rnviewshot", "projectIndex": 0}, {"directoryIndex": 17, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-RelWithDebInfo-8937603cdc50667468fb.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/RelWithDebInfo/662h3v4p/arm64-v8a", "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}