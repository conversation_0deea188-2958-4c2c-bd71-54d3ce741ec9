current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/json-2.13.0/ext/json/ext/parser
/Users/<USER>/.rbenv/versions/3.2.2/bin/ruby extconf.rb
checking for rb_enc_interned_str() in ruby/encoding.h... yes
checking for rb_hash_new_capa() in ruby.h... yes
checking for rb_hash_bulk_insert() in ruby.h... yes
checking for strnlen() in string.h... yes
checking for whether -std=c99 is accepted as CFLAGS... yes
checking for arm_neon.h... yes
checking for cpuid.h... no
creating Makefile

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/json-2.13.0/ext/json/ext/parser
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-fkzwhz sitelibdir\=./.gem.20250722-11947-fkzwhz clean

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/json-2.13.0/ext/json/ext/parser
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-fkzwhz sitelibdir\=./.gem.20250722-11947-fkzwhz
compiling parser.c
linking shared-object json/ext/parser.bundle
ld: warning: ignoring duplicate libraries: '-lruby.3.2'

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/json-2.13.0/ext/json/ext/parser
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-fkzwhz sitelibdir\=./.gem.20250722-11947-fkzwhz install
/usr/bin/install -c -m 0755 parser.bundle ./.gem.20250722-11947-fkzwhz/json/ext

current directory: /Users/<USER>/Desktop/CallKeepExample/vendor/bundle/ruby/3.2.0/gems/json-2.13.0/ext/json/ext/parser
make DESTDIR\= sitearchdir\=./.gem.20250722-11947-fkzwhz sitelibdir\=./.gem.20250722-11947-fkzwhz clean
