const dbQuery = require("../dbConfig/queryRunner");
const moment = require("moment");

class WithdrawalService {
  // Get withdrawal settings
  static async getWithdrawalSettings() {
    try {
      const query = "SELECT setting_key, setting_value, description FROM withdrawal_settings";
      const settings = await dbQuery.queryRunner(query);
      
      const settingsObj = {};
      settings.forEach(setting => {
        settingsObj[setting.setting_key] = setting.setting_value;
      });
      
      return {
        status: 200,
        message: "Withdrawal settings retrieved successfully",
        data: settingsObj
      };
    } catch (error) {
      console.error("Error getting withdrawal settings:", error);
      return {
        status: 500,
        message: "Failed to get withdrawal settings",
        data: {}
      };
    }
  }

  // Get user's withdrawal eligibility
  static async checkWithdrawalEligibility(userId, amount, withdrawalType = 'wallet') {
    try {
      // Get user's premium status
      const userQuery = "SELECT is_premium, premium_expires_at FROM users WHERE id = ?";
      const [user] = await dbQuery.queryRunner(userQuery, [userId]);
      
      if (!user) {
        return {
          status: 400,
          message: "User not found",
          eligible: false
        };
      }

      // Get withdrawal settings
      const settings = await this.getWithdrawalSettings();
      const minAmount = user.is_premium ? 
        parseFloat(settings.data.min_withdrawal_premium) : 
        parseFloat(settings.data.min_withdrawal_regular);

      // Check minimum amount
      if (amount < minAmount) {
        return {
          status: 400,
          message: `Minimum withdrawal amount is ₹${minAmount}`,
          eligible: false,
          minAmount
        };
      }

      // Get user's balance based on withdrawal type
      let availableBalance = 0;
      
      if (withdrawalType === 'wallet') {
        const walletQuery = "SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1";
        const [wallet] = await dbQuery.queryRunner(walletQuery, [userId]);
        availableBalance = wallet ? parseFloat(wallet.totalBalance) : 0;
      } else if (withdrawalType === 'referral') {
        // Get referral balance from users table or referral tracking
        const referralQuery = "SELECT available_referral_balance FROM users WHERE id = ?";
        const [userData] = await dbQuery.queryRunner(referralQuery, [userId]);
        availableBalance = userData ? parseFloat(userData.available_referral_balance || 0) : 0;
      } else if (withdrawalType === 'coupon') {
        // Get coupon balance from users table or coupon tracking
        const couponQuery = "SELECT available_coupon_balance FROM users WHERE id = ?";
        const [userData] = await dbQuery.queryRunner(couponQuery, [userId]);
        availableBalance = userData ? parseFloat(userData.available_coupon_balance || 0) : 0;
      } else if (withdrawalType === 'content_earnings') {
        // Get content creator earnings
        const contentQuery = "SELECT available_balance FROM channels WHERE createdby = ? ORDER BY id DESC LIMIT 1";
        const [channel] = await dbQuery.queryRunner(contentQuery, [userId]);
        availableBalance = channel ? parseFloat(channel.available_balance || 0) : 0;
      }

      if (amount > availableBalance) {
        return {
          status: 400,
          message: `Insufficient balance. Available: ₹${availableBalance.toFixed(2)}`,
          eligible: false,
          availableBalance
        };
      }

      return {
        status: 200,
        message: "Withdrawal eligible",
        eligible: true,
        availableBalance,
        minAmount
      };
    } catch (error) {
      console.error("Error checking withdrawal eligibility:", error);
      return {
        status: 500,
        message: "Failed to check withdrawal eligibility",
        eligible: false
      };
    }
  }

  // Process wallet withdrawal
  static async processWalletWithdrawal(withdrawalData) {
    try {
      const {
        userId,
        amount,
        transactionMethod,
        bankName,
        accountNumber,
        ifscCode,
        mobileNumber,
        upiId
      } = withdrawalData;

      // Check eligibility
      const eligibility = await this.checkWithdrawalEligibility(userId, amount, 'wallet');
      if (!eligibility.eligible) {
        return eligibility;
      }

      // Get withdrawal settings
      const settings = await this.getWithdrawalSettings();
      const chargesPercent = parseFloat(settings.data.withdrawal_charges_percent || 5);
      const charges = (amount * chargesPercent) / 100;
      const netAmount = amount - charges;

      // Insert withdrawal request
      const insertQuery = `
        INSERT INTO wallet_withdrawals (
          user_id, withdraw_req_amount, transaction_method, 
          bank_name, account_number, ifsc_code, mobile_number, upi_id,
          withdrawal_charges, net_amount
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const result = await dbQuery.queryRunner(insertQuery, [
        userId, amount, transactionMethod, bankName, accountNumber, 
        ifscCode, mobileNumber, upiId, charges, netAmount
      ]);

      if (result.insertId) {
        // Update user's withdrawal stats
        const updateUserQuery = `
          UPDATE users 
          SET total_withdrawals = total_withdrawals + ?, 
              last_withdrawal_date = NOW(),
              withdrawal_count = withdrawal_count + 1
          WHERE id = ?
        `;
        await dbQuery.queryRunner(updateUserQuery, [amount, userId]);

        // Update wallet pending withdrawals
        const updateWalletQuery = `
          UPDATE wallet 
          SET pending_withdrawals = pending_withdrawals + ?
          WHERE createdby = ?
        `;
        await dbQuery.queryRunner(updateWalletQuery, [amount, userId]);

        return {
          status: 200,
          message: "Withdrawal request submitted successfully",
          data: {
            withdrawalId: result.insertId,
            amount,
            netAmount,
            charges,
            status: 'pending'
          }
        };
      } else {
        return {
          status: 500,
          message: "Failed to create withdrawal request"
        };
      }
    } catch (error) {
      console.error("Error processing wallet withdrawal:", error);
      return {
        status: 500,
        message: "Failed to process withdrawal request"
      };
    }
  }

  // Process referral withdrawal
  static async processReferralWithdrawal(withdrawalData) {
    try {
      const {
        userId,
        amount,
        withdrawalType,
        bankName,
        bankIfsc,
        bankAccountNumber,
        upiId
      } = withdrawalData;

      // Check eligibility
      const eligibility = await this.checkWithdrawalEligibility(userId, amount, withdrawalType);
      if (!eligibility.eligible) {
        return eligibility;
      }

      // Insert referral withdrawal request
      const insertQuery = `
        INSERT INTO referral_withdrawals (
          user_id, withdrawal_type, amount, 
          bank_name, bank_ifsc, bank_account_number, upi_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      const result = await dbQuery.queryRunner(insertQuery, [
        userId, withdrawalType, amount, bankName, bankIfsc, bankAccountNumber, upiId
      ]);

      if (result.insertId) {
        // Update user's withdrawal stats
        const updateUserQuery = `
          UPDATE users 
          SET total_withdrawals = total_withdrawals + ?, 
              last_withdrawal_date = NOW(),
              withdrawal_count = withdrawal_count + 1
          WHERE id = ?
        `;
        await dbQuery.queryRunner(updateUserQuery, [amount, userId]);

        return {
          status: 200,
          message: "Referral withdrawal request submitted successfully",
          data: {
            withdrawalId: result.insertId,
            amount,
            withdrawalType,
            status: 'pending'
          }
        };
      } else {
        return {
          status: 500,
          message: "Failed to create referral withdrawal request"
        };
      }
    } catch (error) {
      console.error("Error processing referral withdrawal:", error);
      return {
        status: 500,
        message: "Failed to process referral withdrawal request"
      };
    }
  }

  // Process channel/content creator withdrawal
  static async processChannelWithdrawal(withdrawalData) {
    try {
      const {
        userId,
        channelId,
        amount,
        withdrawalType,
        bankName,
        bankIfsc,
        bankAccountNumber,
        upiId
      } = withdrawalData;

      // Check eligibility
      const eligibility = await this.checkWithdrawalEligibility(userId, amount, 'content_earnings');
      if (!eligibility.eligible) {
        return eligibility;
      }

      // Insert channel withdrawal request
      const insertQuery = `
        INSERT INTO channel_withdrawals (
          user_id, channel_id, withdrawal_type, amount, 
          bank_name, bank_ifsc, bank_account_number, upi_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const result = await dbQuery.queryRunner(insertQuery, [
        userId, channelId, withdrawalType, amount, bankName, bankIfsc, bankAccountNumber, upiId
      ]);

      if (result.insertId) {
        // Update user's withdrawal stats
        const updateUserQuery = `
          UPDATE users 
          SET total_withdrawals = total_withdrawals + ?, 
              last_withdrawal_date = NOW(),
              withdrawal_count = withdrawal_count + 1
          WHERE id = ?
        `;
        await dbQuery.queryRunner(updateUserQuery, [amount, userId]);

        return {
          status: 200,
          message: "Channel withdrawal request submitted successfully",
          data: {
            withdrawalId: result.insertId,
            amount,
            withdrawalType,
            status: 'pending'
          }
        };
      } else {
        return {
          status: 500,
          message: "Failed to create channel withdrawal request"
        };
      }
    } catch (error) {
      console.error("Error processing channel withdrawal:", error);
      return {
        status: 500,
        message: "Failed to process channel withdrawal request"
      };
    }
  }

  // Get user's withdrawal history
  static async getWithdrawalHistory(userId, withdrawalType = 'all', page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;
      let query, params;

      if (withdrawalType === 'wallet') {
        query = `
          SELECT id, withdraw_req_amount as amount, transaction_method, 
                 status, transaction_status, created_at, processed_at
          FROM wallet_withdrawals 
          WHERE user_id = ? 
          ORDER BY created_at DESC 
          LIMIT ? OFFSET ?
        `;
        params = [userId, limit, offset];
      } else if (withdrawalType === 'referral') {
        query = `
          SELECT id, amount, withdrawal_type, status, created_at, processed_at
          FROM referral_withdrawals 
          WHERE user_id = ? 
          ORDER BY created_at DESC 
          LIMIT ? OFFSET ?
        `;
        params = [userId, limit, offset];
      } else if (withdrawalType === 'channel') {
        query = `
          SELECT id, amount, withdrawal_type, status, created_at, processed_at
          FROM channel_withdrawals 
          WHERE user_id = ? 
          ORDER BY created_at DESC 
          LIMIT ? OFFSET ?
        `;
        params = [userId, limit, offset];
      } else {
        // Get all types
        query = `
          (SELECT id, withdraw_req_amount as amount, 'wallet' as type, 
                  transaction_method as method, status, created_at, processed_at
           FROM wallet_withdrawals WHERE user_id = ?)
          UNION ALL
          (SELECT id, amount, withdrawal_type as type, 
                  'N/A' as method, status, created_at, processed_at
           FROM referral_withdrawals WHERE user_id = ?)
          UNION ALL
          (SELECT id, amount, withdrawal_type as type, 
                  'N/A' as method, status, created_at, processed_at
           FROM channel_withdrawals WHERE user_id = ?)
          ORDER BY created_at DESC 
          LIMIT ? OFFSET ?
        `;
        params = [userId, userId, userId, limit, offset];
      }

      const withdrawals = await dbQuery.queryRunner(query, params);

      return {
        status: 200,
        message: "Withdrawal history retrieved successfully",
        data: withdrawals
      };
    } catch (error) {
      console.error("Error getting withdrawal history:", error);
      return {
        status: 500,
        message: "Failed to get withdrawal history",
        data: []
      };
    }
  }

  // Get withdrawal statistics for user
  static async getWithdrawalStats(userId) {
    try {
      // Get wallet withdrawal stats
      const walletStatsQuery = `
        SELECT 
          COUNT(*) as total_requests,
          SUM(withdraw_req_amount) as total_amount,
          SUM(CASE WHEN status = 'Paid' THEN withdraw_req_amount ELSE 0 END) as paid_amount,
          SUM(CASE WHEN status = 'pending' THEN withdraw_req_amount ELSE 0 END) as pending_amount
        FROM wallet_withdrawals 
        WHERE user_id = ?
      `;
      const [walletStats] = await dbQuery.queryRunner(walletStatsQuery, [userId]);

      // Get referral withdrawal stats
      const referralStatsQuery = `
        SELECT 
          COUNT(*) as total_requests,
          SUM(amount) as total_amount,
          SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as paid_amount,
          SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount
        FROM referral_withdrawals 
        WHERE user_id = ?
      `;
      const [referralStats] = await dbQuery.queryRunner(referralStatsQuery, [userId]);

      // Get channel withdrawal stats
      const channelStatsQuery = `
        SELECT 
          COUNT(*) as total_requests,
          SUM(amount) as total_amount,
          SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as paid_amount,
          SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount
        FROM channel_withdrawals 
        WHERE user_id = ?
      `;
      const [channelStats] = await dbQuery.queryRunner(channelStatsQuery, [userId]);

      return {
        status: 200,
        message: "Withdrawal statistics retrieved successfully",
        data: {
          wallet: walletStats || { total_requests: 0, total_amount: 0, paid_amount: 0, pending_amount: 0 },
          referral: referralStats || { total_requests: 0, total_amount: 0, paid_amount: 0, pending_amount: 0 },
          channel: channelStats || { total_requests: 0, total_amount: 0, paid_amount: 0, pending_amount: 0 }
        }
      };
    } catch (error) {
      console.error("Error getting withdrawal stats:", error);
      return {
        status: 500,
        message: "Failed to get withdrawal statistics",
        data: {}
      };
    }
  }
}

module.exports = WithdrawalService; 