{"cells": [{"cell_type": "markdown", "id": "ce3d64ed", "metadata": {}, "source": ["# about auto premium"]}, {"cell_type": "markdown", "id": "a02660d2", "metadata": {}, "source": ["# a### Quick Overview of Payment Flow\n", "\n", "1. **User Clicks on Premium and Buys**:\n", "   - Frontend initiates a payment request using the Razorpay API.\n", "\n", "2. **Razorpay API Flow**:\n", "   - Frontend calls `/api/razorpay-order` to create a Razorpay order (handled by `razorpayOrderCreation`).\n", "   - User completes payment via Razorpay, and the frontend receives `order_id`, `payment_id`, and `signature`.\n", "   - Frontend calls `/api/razorpay-verification` with these details to verify the payment (handled by `razorpayPaymentSignatue`).\n", "\n", "3. **Backend APIs**:\n", "   - **`/api/razorpay-verification`**:\n", "     - Verifies the payment signature and upserts the transaction in the `transactions` table.\n", "     - Creates a queued plan in `user_premium_plans` and activates it if no active plan exists.\n", "     - Updates `users.premium = 1` and `users.premium_plan_id` for new users or queues the plan for existing premium users.\n", "   - **`/api/upgradePremium`**:\n", "     - Optional endpoint, currently validates the transaction and handles coupons but doesn’t create plans (that’s done in `/razorpay-verification`).\n", "     - Not automatically called after Razor<PERSON>y; it’s a separate step if needed (e.g., for manual upgrades or coupon processing).\n", "\n", "4. **Frontend Role**:\n", "   - Frontend must call `/api/razorpay-order` first, then handle the Razorpay payment response, and finally call `/api/razorpay-verification` with the payment details.\n", "   - `/api/upgradePremium` is not automatically triggered and requires a separate frontend call if used (e.g., after coupon application).\n", "\n", "### Does `/api/upgradePremium` Automatically Update User Data?\n", "- **No**, `/api/upgradePremium` does not automatically update user data to activate premium status after a Razorpay payment.\n", "- **Yes**, `/api/razorpay-verification` handles the automatic premium activation (for new users) or queuing (for existing premium users) when the frontend calls it with valid payment details.\n", "\n", "### Correct Flow for Frontend\n", "The frontend should:\n", "1. Call `/api/razorpay-order` to get the order details.\n", "2. Process the Razorpay payment.\n", "3. Call `/api/razorpay-verification` with `order_id`, `payment_id`, `signature`, `amount`, `currency`, `user_id`, and `payment_status` to activate/queue the plan.\n", "4. (Optional) Call `/api/upgradePremium` if a coupon is involved or for manual verification.\n", "\n", "### Example Frontend Logic (Quick Pseudocode)\n", "```dart\n", "Future<void> handlePremiumPurchase(int userId, double amount) async {\n", "  // Step 1: Create Razorpay order\n", "  var orderResponse = await apiService.post('/api/razorpay-order', {'amount': amount, 'user_id': userId});\n", "  var orderId = orderResponse['data']['id'];\n", "\n", "  // Step 2: Process Razorpay payment (handled by Razorpay SDK)\n", "  var paymentResponse = await razorpayService.pay(orderId); // Returns order_id, payment_id, signature\n", "\n", "  // Step 3: Verify payment and activate/queue plan\n", "  var verifyResponse = await apiService.post('/api/razorpay-verification', {\n", "    'order_id': paymentResponse['order_id'],\n", "    'razorpay_payment_id': paymentResponse['payment_id'],\n", "    'razorpay_signature': paymentResponse['signature'],\n", "    'amount': amount * 100, // In paise\n", "    'currency': 'INR',\n", "    'user_id': userId,\n", "    'payment_status': 'success'\n", "  });\n", "\n", "  if (verifyResponse['status']) {\n", "    print('Premium activated/queued successfully');\n", "  }\n", "}\n", "```\n", "\n", "### Conclusion\n", "- `/api/razorpay-verification` is the key API that automatically updates user data (premium status or queues the plan) after a successful Razorpay payment.\n", "- Frontend must call these APIs sequentially as shown above.\n", "- `/api/upgradePremium` is not automatically invoked and is optional (e.g., for coupons); it won’t activate premium on its own.\n", "\n", "Let me know if you need help adjusting the frontend code!"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}