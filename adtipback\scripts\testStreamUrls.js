// adtipback/scripts/testStreamUrls.js
// Test Cloudflare Stream URLs to verify they work

const fetch = require('node-fetch');
const fs = require('fs');

async function testStreamUrls() {
  console.log('🧪 TESTING CLOUDFLARE STREAM URLS');
  console.log('=================================\n');

  try {
    // Load the generated mapping file
    const mappingFiles = fs.readdirSync('.').filter(f => f.startsWith('stream_mapping_') && f.endsWith('.json'));
    if (mappingFiles.length === 0) {
      throw new Error('No stream mapping file found. Please run generateStreamMigrationSQL.js first.');
    }

    const latestMappingFile = mappingFiles.sort().pop();
    console.log(`📂 Loading mapping file: ${latestMappingFile}`);
    
    const mappingData = JSON.parse(fs.readFileSync(latestMappingFile, 'utf8'));
    const streamVideos = mappingData.videos;
    
    console.log(`📊 Found ${streamVideos.length} Stream videos`);

    // Test first 10 videos
    const testVideos = streamVideos.slice(0, 10);
    console.log(`\n🔍 Testing first ${testVideos.length} videos...\n`);

    const results = [];

    for (let i = 0; i < testVideos.length; i++) {
      const video = testVideos[i];
      const manifestUrl = video.manifestUrl;
      const mp4Url = video.mp4Url;
      
      console.log(`${i + 1}. Testing ${video.uid}`);
      console.log(`   Filename: ${video.filename || 'N/A'}`);
      console.log(`   Duration: ${video.duration || 'N/A'}s`);
      console.log(`   Size: ${video.size ? (video.size / (1024 * 1024)).toFixed(2) + ' MB' : 'N/A'}`);

      // Test HLS manifest
      try {
        console.log(`   🔍 Testing HLS manifest...`);
        const manifestResponse = await fetch(manifestUrl, { 
          method: 'HEAD',
          timeout: 10000 
        });
        
        if (manifestResponse.ok) {
          console.log(`   ✅ HLS manifest accessible (${manifestResponse.status})`);
          results.push({
            uid: video.uid,
            filename: video.filename,
            manifestUrl,
            manifestStatus: manifestResponse.status,
            manifestWorking: true
          });
        } else {
          console.log(`   ❌ HLS manifest failed (${manifestResponse.status})`);
          results.push({
            uid: video.uid,
            filename: video.filename,
            manifestUrl,
            manifestStatus: manifestResponse.status,
            manifestWorking: false
          });
        }
      } catch (error) {
        console.log(`   ❌ HLS manifest error: ${error.message}`);
        results.push({
          uid: video.uid,
          filename: video.filename,
          manifestUrl,
          manifestError: error.message,
          manifestWorking: false
        });
      }

      // Test MP4 download (just HEAD request)
      try {
        console.log(`   🔍 Testing MP4 download...`);
        const mp4Response = await fetch(mp4Url, { 
          method: 'HEAD',
          timeout: 10000 
        });
        
        if (mp4Response.ok) {
          const contentLength = mp4Response.headers.get('content-length');
          const sizeInfo = contentLength ? ` (${(contentLength / (1024 * 1024)).toFixed(2)} MB)` : '';
          console.log(`   ✅ MP4 download accessible (${mp4Response.status})${sizeInfo}`);
          results[results.length - 1].mp4Status = mp4Response.status;
          results[results.length - 1].mp4Working = true;
          results[results.length - 1].mp4Size = contentLength;
        } else {
          console.log(`   ❌ MP4 download failed (${mp4Response.status})`);
          results[results.length - 1].mp4Status = mp4Response.status;
          results[results.length - 1].mp4Working = false;
        }
      } catch (error) {
        console.log(`   ❌ MP4 download error: ${error.message}`);
        results[results.length - 1].mp4Error = error.message;
        results[results.length - 1].mp4Working = false;
      }

      console.log(''); // Empty line for readability
    }

    // Summary
    console.log('\n📊 TEST RESULTS SUMMARY:');
    console.log('========================');
    
    const workingManifests = results.filter(r => r.manifestWorking).length;
    const workingMp4s = results.filter(r => r.mp4Working).length;
    
    console.log(`✅ Working HLS manifests: ${workingManifests}/${results.length} (${((workingManifests / results.length) * 100).toFixed(1)}%)`);
    console.log(`✅ Working MP4 downloads: ${workingMp4s}/${results.length} (${((workingMp4s / results.length) * 100).toFixed(1)}%)`);

    if (workingManifests > 0) {
      console.log('\n🎬 SAMPLE WORKING URLS FOR TESTING:');
      const workingVideos = results.filter(r => r.manifestWorking).slice(0, 3);
      workingVideos.forEach((video, index) => {
        console.log(`${index + 1}. ${video.uid} (${video.filename || 'No filename'})`);
        console.log(`   HLS: ${video.manifestUrl}`);
        console.log('');
      });
    }

    // Save test results
    const testResultsFile = `stream_test_results_${Date.now()}.json`;
    fs.writeFileSync(testResultsFile, JSON.stringify({
      timestamp: new Date().toISOString(),
      totalTested: results.length,
      workingManifests,
      workingMp4s,
      results
    }, null, 2));

    console.log(`💾 Test results saved to: ${testResultsFile}`);

    if (workingManifests === results.length) {
      console.log('\n🎉 ALL TESTS PASSED! Stream URLs are working correctly.');
      console.log('✅ Ready to proceed with full migration.');
    } else if (workingManifests > results.length * 0.8) {
      console.log('\n⚠️  Most tests passed. Some URLs may have temporary issues.');
      console.log('✅ Should be safe to proceed with migration.');
    } else {
      console.log('\n❌ Many tests failed. Please investigate Stream configuration.');
      console.log('⚠️  Do not proceed with migration until issues are resolved.');
    }

    return {
      success: true,
      totalTested: results.length,
      workingManifests,
      workingMp4s,
      successRate: (workingManifests / results.length) * 100
    };

  } catch (error) {
    console.error('\n❌ Stream URL testing failed:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  testStreamUrls()
    .then(result => {
      if (result) {
        console.log(`\n🎉 Testing completed! Success rate: ${result.successRate.toFixed(1)}%`);
      }
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Testing failed:', error.message);
      process.exit(1);
    });
}

module.exports = { testStreamUrls };
