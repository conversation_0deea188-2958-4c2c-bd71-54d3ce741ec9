# /* **************************************************************************
#  *                                                                          *
#  *     (C) Copyright <PERSON> 2002.
#  *     Distributed under the Boost Software License, Version 1.0. (See
#  *     accompanying file LICENSE_1_0.txt or copy at
#  *     http://www.boost.org/LICENSE_1_0.txt)
#  *                                                                          *
#  ************************************************************************** */
#
# /* Revised by <PERSON> (2020) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_SEQ_ENUM_512_HPP
# define BOOST_PREPROCESSOR_SEQ_ENUM_512_HPP
#
# define BOOST_PP_SEQ_ENUM_257(x) x, BOOST_PP_SEQ_ENUM_256
# define BOOST_PP_SEQ_ENUM_258(x) x, BOOST_PP_SEQ_ENUM_257
# define BOOST_PP_SEQ_ENUM_259(x) x, BOOST_PP_SEQ_ENUM_258
# define BOOST_PP_SEQ_ENUM_260(x) x, BOOST_PP_SEQ_ENUM_259
# define BOOST_PP_SEQ_ENUM_261(x) x, BOOST_PP_SEQ_ENUM_260
# define BOOST_PP_SEQ_ENUM_262(x) x, BOOST_PP_SEQ_ENUM_261
# define BOOST_PP_SEQ_ENUM_263(x) x, BOOST_PP_SEQ_ENUM_262
# define BOOST_PP_SEQ_ENUM_264(x) x, BOOST_PP_SEQ_ENUM_263
# define BOOST_PP_SEQ_ENUM_265(x) x, BOOST_PP_SEQ_ENUM_264
# define BOOST_PP_SEQ_ENUM_266(x) x, BOOST_PP_SEQ_ENUM_265
# define BOOST_PP_SEQ_ENUM_267(x) x, BOOST_PP_SEQ_ENUM_266
# define BOOST_PP_SEQ_ENUM_268(x) x, BOOST_PP_SEQ_ENUM_267
# define BOOST_PP_SEQ_ENUM_269(x) x, BOOST_PP_SEQ_ENUM_268
# define BOOST_PP_SEQ_ENUM_270(x) x, BOOST_PP_SEQ_ENUM_269
# define BOOST_PP_SEQ_ENUM_271(x) x, BOOST_PP_SEQ_ENUM_270
# define BOOST_PP_SEQ_ENUM_272(x) x, BOOST_PP_SEQ_ENUM_271
# define BOOST_PP_SEQ_ENUM_273(x) x, BOOST_PP_SEQ_ENUM_272
# define BOOST_PP_SEQ_ENUM_274(x) x, BOOST_PP_SEQ_ENUM_273
# define BOOST_PP_SEQ_ENUM_275(x) x, BOOST_PP_SEQ_ENUM_274
# define BOOST_PP_SEQ_ENUM_276(x) x, BOOST_PP_SEQ_ENUM_275
# define BOOST_PP_SEQ_ENUM_277(x) x, BOOST_PP_SEQ_ENUM_276
# define BOOST_PP_SEQ_ENUM_278(x) x, BOOST_PP_SEQ_ENUM_277
# define BOOST_PP_SEQ_ENUM_279(x) x, BOOST_PP_SEQ_ENUM_278
# define BOOST_PP_SEQ_ENUM_280(x) x, BOOST_PP_SEQ_ENUM_279
# define BOOST_PP_SEQ_ENUM_281(x) x, BOOST_PP_SEQ_ENUM_280
# define BOOST_PP_SEQ_ENUM_282(x) x, BOOST_PP_SEQ_ENUM_281
# define BOOST_PP_SEQ_ENUM_283(x) x, BOOST_PP_SEQ_ENUM_282
# define BOOST_PP_SEQ_ENUM_284(x) x, BOOST_PP_SEQ_ENUM_283
# define BOOST_PP_SEQ_ENUM_285(x) x, BOOST_PP_SEQ_ENUM_284
# define BOOST_PP_SEQ_ENUM_286(x) x, BOOST_PP_SEQ_ENUM_285
# define BOOST_PP_SEQ_ENUM_287(x) x, BOOST_PP_SEQ_ENUM_286
# define BOOST_PP_SEQ_ENUM_288(x) x, BOOST_PP_SEQ_ENUM_287
# define BOOST_PP_SEQ_ENUM_289(x) x, BOOST_PP_SEQ_ENUM_288
# define BOOST_PP_SEQ_ENUM_290(x) x, BOOST_PP_SEQ_ENUM_289
# define BOOST_PP_SEQ_ENUM_291(x) x, BOOST_PP_SEQ_ENUM_290
# define BOOST_PP_SEQ_ENUM_292(x) x, BOOST_PP_SEQ_ENUM_291
# define BOOST_PP_SEQ_ENUM_293(x) x, BOOST_PP_SEQ_ENUM_292
# define BOOST_PP_SEQ_ENUM_294(x) x, BOOST_PP_SEQ_ENUM_293
# define BOOST_PP_SEQ_ENUM_295(x) x, BOOST_PP_SEQ_ENUM_294
# define BOOST_PP_SEQ_ENUM_296(x) x, BOOST_PP_SEQ_ENUM_295
# define BOOST_PP_SEQ_ENUM_297(x) x, BOOST_PP_SEQ_ENUM_296
# define BOOST_PP_SEQ_ENUM_298(x) x, BOOST_PP_SEQ_ENUM_297
# define BOOST_PP_SEQ_ENUM_299(x) x, BOOST_PP_SEQ_ENUM_298
# define BOOST_PP_SEQ_ENUM_300(x) x, BOOST_PP_SEQ_ENUM_299
# define BOOST_PP_SEQ_ENUM_301(x) x, BOOST_PP_SEQ_ENUM_300
# define BOOST_PP_SEQ_ENUM_302(x) x, BOOST_PP_SEQ_ENUM_301
# define BOOST_PP_SEQ_ENUM_303(x) x, BOOST_PP_SEQ_ENUM_302
# define BOOST_PP_SEQ_ENUM_304(x) x, BOOST_PP_SEQ_ENUM_303
# define BOOST_PP_SEQ_ENUM_305(x) x, BOOST_PP_SEQ_ENUM_304
# define BOOST_PP_SEQ_ENUM_306(x) x, BOOST_PP_SEQ_ENUM_305
# define BOOST_PP_SEQ_ENUM_307(x) x, BOOST_PP_SEQ_ENUM_306
# define BOOST_PP_SEQ_ENUM_308(x) x, BOOST_PP_SEQ_ENUM_307
# define BOOST_PP_SEQ_ENUM_309(x) x, BOOST_PP_SEQ_ENUM_308
# define BOOST_PP_SEQ_ENUM_310(x) x, BOOST_PP_SEQ_ENUM_309
# define BOOST_PP_SEQ_ENUM_311(x) x, BOOST_PP_SEQ_ENUM_310
# define BOOST_PP_SEQ_ENUM_312(x) x, BOOST_PP_SEQ_ENUM_311
# define BOOST_PP_SEQ_ENUM_313(x) x, BOOST_PP_SEQ_ENUM_312
# define BOOST_PP_SEQ_ENUM_314(x) x, BOOST_PP_SEQ_ENUM_313
# define BOOST_PP_SEQ_ENUM_315(x) x, BOOST_PP_SEQ_ENUM_314
# define BOOST_PP_SEQ_ENUM_316(x) x, BOOST_PP_SEQ_ENUM_315
# define BOOST_PP_SEQ_ENUM_317(x) x, BOOST_PP_SEQ_ENUM_316
# define BOOST_PP_SEQ_ENUM_318(x) x, BOOST_PP_SEQ_ENUM_317
# define BOOST_PP_SEQ_ENUM_319(x) x, BOOST_PP_SEQ_ENUM_318
# define BOOST_PP_SEQ_ENUM_320(x) x, BOOST_PP_SEQ_ENUM_319
# define BOOST_PP_SEQ_ENUM_321(x) x, BOOST_PP_SEQ_ENUM_320
# define BOOST_PP_SEQ_ENUM_322(x) x, BOOST_PP_SEQ_ENUM_321
# define BOOST_PP_SEQ_ENUM_323(x) x, BOOST_PP_SEQ_ENUM_322
# define BOOST_PP_SEQ_ENUM_324(x) x, BOOST_PP_SEQ_ENUM_323
# define BOOST_PP_SEQ_ENUM_325(x) x, BOOST_PP_SEQ_ENUM_324
# define BOOST_PP_SEQ_ENUM_326(x) x, BOOST_PP_SEQ_ENUM_325
# define BOOST_PP_SEQ_ENUM_327(x) x, BOOST_PP_SEQ_ENUM_326
# define BOOST_PP_SEQ_ENUM_328(x) x, BOOST_PP_SEQ_ENUM_327
# define BOOST_PP_SEQ_ENUM_329(x) x, BOOST_PP_SEQ_ENUM_328
# define BOOST_PP_SEQ_ENUM_330(x) x, BOOST_PP_SEQ_ENUM_329
# define BOOST_PP_SEQ_ENUM_331(x) x, BOOST_PP_SEQ_ENUM_330
# define BOOST_PP_SEQ_ENUM_332(x) x, BOOST_PP_SEQ_ENUM_331
# define BOOST_PP_SEQ_ENUM_333(x) x, BOOST_PP_SEQ_ENUM_332
# define BOOST_PP_SEQ_ENUM_334(x) x, BOOST_PP_SEQ_ENUM_333
# define BOOST_PP_SEQ_ENUM_335(x) x, BOOST_PP_SEQ_ENUM_334
# define BOOST_PP_SEQ_ENUM_336(x) x, BOOST_PP_SEQ_ENUM_335
# define BOOST_PP_SEQ_ENUM_337(x) x, BOOST_PP_SEQ_ENUM_336
# define BOOST_PP_SEQ_ENUM_338(x) x, BOOST_PP_SEQ_ENUM_337
# define BOOST_PP_SEQ_ENUM_339(x) x, BOOST_PP_SEQ_ENUM_338
# define BOOST_PP_SEQ_ENUM_340(x) x, BOOST_PP_SEQ_ENUM_339
# define BOOST_PP_SEQ_ENUM_341(x) x, BOOST_PP_SEQ_ENUM_340
# define BOOST_PP_SEQ_ENUM_342(x) x, BOOST_PP_SEQ_ENUM_341
# define BOOST_PP_SEQ_ENUM_343(x) x, BOOST_PP_SEQ_ENUM_342
# define BOOST_PP_SEQ_ENUM_344(x) x, BOOST_PP_SEQ_ENUM_343
# define BOOST_PP_SEQ_ENUM_345(x) x, BOOST_PP_SEQ_ENUM_344
# define BOOST_PP_SEQ_ENUM_346(x) x, BOOST_PP_SEQ_ENUM_345
# define BOOST_PP_SEQ_ENUM_347(x) x, BOOST_PP_SEQ_ENUM_346
# define BOOST_PP_SEQ_ENUM_348(x) x, BOOST_PP_SEQ_ENUM_347
# define BOOST_PP_SEQ_ENUM_349(x) x, BOOST_PP_SEQ_ENUM_348
# define BOOST_PP_SEQ_ENUM_350(x) x, BOOST_PP_SEQ_ENUM_349
# define BOOST_PP_SEQ_ENUM_351(x) x, BOOST_PP_SEQ_ENUM_350
# define BOOST_PP_SEQ_ENUM_352(x) x, BOOST_PP_SEQ_ENUM_351
# define BOOST_PP_SEQ_ENUM_353(x) x, BOOST_PP_SEQ_ENUM_352
# define BOOST_PP_SEQ_ENUM_354(x) x, BOOST_PP_SEQ_ENUM_353
# define BOOST_PP_SEQ_ENUM_355(x) x, BOOST_PP_SEQ_ENUM_354
# define BOOST_PP_SEQ_ENUM_356(x) x, BOOST_PP_SEQ_ENUM_355
# define BOOST_PP_SEQ_ENUM_357(x) x, BOOST_PP_SEQ_ENUM_356
# define BOOST_PP_SEQ_ENUM_358(x) x, BOOST_PP_SEQ_ENUM_357
# define BOOST_PP_SEQ_ENUM_359(x) x, BOOST_PP_SEQ_ENUM_358
# define BOOST_PP_SEQ_ENUM_360(x) x, BOOST_PP_SEQ_ENUM_359
# define BOOST_PP_SEQ_ENUM_361(x) x, BOOST_PP_SEQ_ENUM_360
# define BOOST_PP_SEQ_ENUM_362(x) x, BOOST_PP_SEQ_ENUM_361
# define BOOST_PP_SEQ_ENUM_363(x) x, BOOST_PP_SEQ_ENUM_362
# define BOOST_PP_SEQ_ENUM_364(x) x, BOOST_PP_SEQ_ENUM_363
# define BOOST_PP_SEQ_ENUM_365(x) x, BOOST_PP_SEQ_ENUM_364
# define BOOST_PP_SEQ_ENUM_366(x) x, BOOST_PP_SEQ_ENUM_365
# define BOOST_PP_SEQ_ENUM_367(x) x, BOOST_PP_SEQ_ENUM_366
# define BOOST_PP_SEQ_ENUM_368(x) x, BOOST_PP_SEQ_ENUM_367
# define BOOST_PP_SEQ_ENUM_369(x) x, BOOST_PP_SEQ_ENUM_368
# define BOOST_PP_SEQ_ENUM_370(x) x, BOOST_PP_SEQ_ENUM_369
# define BOOST_PP_SEQ_ENUM_371(x) x, BOOST_PP_SEQ_ENUM_370
# define BOOST_PP_SEQ_ENUM_372(x) x, BOOST_PP_SEQ_ENUM_371
# define BOOST_PP_SEQ_ENUM_373(x) x, BOOST_PP_SEQ_ENUM_372
# define BOOST_PP_SEQ_ENUM_374(x) x, BOOST_PP_SEQ_ENUM_373
# define BOOST_PP_SEQ_ENUM_375(x) x, BOOST_PP_SEQ_ENUM_374
# define BOOST_PP_SEQ_ENUM_376(x) x, BOOST_PP_SEQ_ENUM_375
# define BOOST_PP_SEQ_ENUM_377(x) x, BOOST_PP_SEQ_ENUM_376
# define BOOST_PP_SEQ_ENUM_378(x) x, BOOST_PP_SEQ_ENUM_377
# define BOOST_PP_SEQ_ENUM_379(x) x, BOOST_PP_SEQ_ENUM_378
# define BOOST_PP_SEQ_ENUM_380(x) x, BOOST_PP_SEQ_ENUM_379
# define BOOST_PP_SEQ_ENUM_381(x) x, BOOST_PP_SEQ_ENUM_380
# define BOOST_PP_SEQ_ENUM_382(x) x, BOOST_PP_SEQ_ENUM_381
# define BOOST_PP_SEQ_ENUM_383(x) x, BOOST_PP_SEQ_ENUM_382
# define BOOST_PP_SEQ_ENUM_384(x) x, BOOST_PP_SEQ_ENUM_383
# define BOOST_PP_SEQ_ENUM_385(x) x, BOOST_PP_SEQ_ENUM_384
# define BOOST_PP_SEQ_ENUM_386(x) x, BOOST_PP_SEQ_ENUM_385
# define BOOST_PP_SEQ_ENUM_387(x) x, BOOST_PP_SEQ_ENUM_386
# define BOOST_PP_SEQ_ENUM_388(x) x, BOOST_PP_SEQ_ENUM_387
# define BOOST_PP_SEQ_ENUM_389(x) x, BOOST_PP_SEQ_ENUM_388
# define BOOST_PP_SEQ_ENUM_390(x) x, BOOST_PP_SEQ_ENUM_389
# define BOOST_PP_SEQ_ENUM_391(x) x, BOOST_PP_SEQ_ENUM_390
# define BOOST_PP_SEQ_ENUM_392(x) x, BOOST_PP_SEQ_ENUM_391
# define BOOST_PP_SEQ_ENUM_393(x) x, BOOST_PP_SEQ_ENUM_392
# define BOOST_PP_SEQ_ENUM_394(x) x, BOOST_PP_SEQ_ENUM_393
# define BOOST_PP_SEQ_ENUM_395(x) x, BOOST_PP_SEQ_ENUM_394
# define BOOST_PP_SEQ_ENUM_396(x) x, BOOST_PP_SEQ_ENUM_395
# define BOOST_PP_SEQ_ENUM_397(x) x, BOOST_PP_SEQ_ENUM_396
# define BOOST_PP_SEQ_ENUM_398(x) x, BOOST_PP_SEQ_ENUM_397
# define BOOST_PP_SEQ_ENUM_399(x) x, BOOST_PP_SEQ_ENUM_398
# define BOOST_PP_SEQ_ENUM_400(x) x, BOOST_PP_SEQ_ENUM_399
# define BOOST_PP_SEQ_ENUM_401(x) x, BOOST_PP_SEQ_ENUM_400
# define BOOST_PP_SEQ_ENUM_402(x) x, BOOST_PP_SEQ_ENUM_401
# define BOOST_PP_SEQ_ENUM_403(x) x, BOOST_PP_SEQ_ENUM_402
# define BOOST_PP_SEQ_ENUM_404(x) x, BOOST_PP_SEQ_ENUM_403
# define BOOST_PP_SEQ_ENUM_405(x) x, BOOST_PP_SEQ_ENUM_404
# define BOOST_PP_SEQ_ENUM_406(x) x, BOOST_PP_SEQ_ENUM_405
# define BOOST_PP_SEQ_ENUM_407(x) x, BOOST_PP_SEQ_ENUM_406
# define BOOST_PP_SEQ_ENUM_408(x) x, BOOST_PP_SEQ_ENUM_407
# define BOOST_PP_SEQ_ENUM_409(x) x, BOOST_PP_SEQ_ENUM_408
# define BOOST_PP_SEQ_ENUM_410(x) x, BOOST_PP_SEQ_ENUM_409
# define BOOST_PP_SEQ_ENUM_411(x) x, BOOST_PP_SEQ_ENUM_410
# define BOOST_PP_SEQ_ENUM_412(x) x, BOOST_PP_SEQ_ENUM_411
# define BOOST_PP_SEQ_ENUM_413(x) x, BOOST_PP_SEQ_ENUM_412
# define BOOST_PP_SEQ_ENUM_414(x) x, BOOST_PP_SEQ_ENUM_413
# define BOOST_PP_SEQ_ENUM_415(x) x, BOOST_PP_SEQ_ENUM_414
# define BOOST_PP_SEQ_ENUM_416(x) x, BOOST_PP_SEQ_ENUM_415
# define BOOST_PP_SEQ_ENUM_417(x) x, BOOST_PP_SEQ_ENUM_416
# define BOOST_PP_SEQ_ENUM_418(x) x, BOOST_PP_SEQ_ENUM_417
# define BOOST_PP_SEQ_ENUM_419(x) x, BOOST_PP_SEQ_ENUM_418
# define BOOST_PP_SEQ_ENUM_420(x) x, BOOST_PP_SEQ_ENUM_419
# define BOOST_PP_SEQ_ENUM_421(x) x, BOOST_PP_SEQ_ENUM_420
# define BOOST_PP_SEQ_ENUM_422(x) x, BOOST_PP_SEQ_ENUM_421
# define BOOST_PP_SEQ_ENUM_423(x) x, BOOST_PP_SEQ_ENUM_422
# define BOOST_PP_SEQ_ENUM_424(x) x, BOOST_PP_SEQ_ENUM_423
# define BOOST_PP_SEQ_ENUM_425(x) x, BOOST_PP_SEQ_ENUM_424
# define BOOST_PP_SEQ_ENUM_426(x) x, BOOST_PP_SEQ_ENUM_425
# define BOOST_PP_SEQ_ENUM_427(x) x, BOOST_PP_SEQ_ENUM_426
# define BOOST_PP_SEQ_ENUM_428(x) x, BOOST_PP_SEQ_ENUM_427
# define BOOST_PP_SEQ_ENUM_429(x) x, BOOST_PP_SEQ_ENUM_428
# define BOOST_PP_SEQ_ENUM_430(x) x, BOOST_PP_SEQ_ENUM_429
# define BOOST_PP_SEQ_ENUM_431(x) x, BOOST_PP_SEQ_ENUM_430
# define BOOST_PP_SEQ_ENUM_432(x) x, BOOST_PP_SEQ_ENUM_431
# define BOOST_PP_SEQ_ENUM_433(x) x, BOOST_PP_SEQ_ENUM_432
# define BOOST_PP_SEQ_ENUM_434(x) x, BOOST_PP_SEQ_ENUM_433
# define BOOST_PP_SEQ_ENUM_435(x) x, BOOST_PP_SEQ_ENUM_434
# define BOOST_PP_SEQ_ENUM_436(x) x, BOOST_PP_SEQ_ENUM_435
# define BOOST_PP_SEQ_ENUM_437(x) x, BOOST_PP_SEQ_ENUM_436
# define BOOST_PP_SEQ_ENUM_438(x) x, BOOST_PP_SEQ_ENUM_437
# define BOOST_PP_SEQ_ENUM_439(x) x, BOOST_PP_SEQ_ENUM_438
# define BOOST_PP_SEQ_ENUM_440(x) x, BOOST_PP_SEQ_ENUM_439
# define BOOST_PP_SEQ_ENUM_441(x) x, BOOST_PP_SEQ_ENUM_440
# define BOOST_PP_SEQ_ENUM_442(x) x, BOOST_PP_SEQ_ENUM_441
# define BOOST_PP_SEQ_ENUM_443(x) x, BOOST_PP_SEQ_ENUM_442
# define BOOST_PP_SEQ_ENUM_444(x) x, BOOST_PP_SEQ_ENUM_443
# define BOOST_PP_SEQ_ENUM_445(x) x, BOOST_PP_SEQ_ENUM_444
# define BOOST_PP_SEQ_ENUM_446(x) x, BOOST_PP_SEQ_ENUM_445
# define BOOST_PP_SEQ_ENUM_447(x) x, BOOST_PP_SEQ_ENUM_446
# define BOOST_PP_SEQ_ENUM_448(x) x, BOOST_PP_SEQ_ENUM_447
# define BOOST_PP_SEQ_ENUM_449(x) x, BOOST_PP_SEQ_ENUM_448
# define BOOST_PP_SEQ_ENUM_450(x) x, BOOST_PP_SEQ_ENUM_449
# define BOOST_PP_SEQ_ENUM_451(x) x, BOOST_PP_SEQ_ENUM_450
# define BOOST_PP_SEQ_ENUM_452(x) x, BOOST_PP_SEQ_ENUM_451
# define BOOST_PP_SEQ_ENUM_453(x) x, BOOST_PP_SEQ_ENUM_452
# define BOOST_PP_SEQ_ENUM_454(x) x, BOOST_PP_SEQ_ENUM_453
# define BOOST_PP_SEQ_ENUM_455(x) x, BOOST_PP_SEQ_ENUM_454
# define BOOST_PP_SEQ_ENUM_456(x) x, BOOST_PP_SEQ_ENUM_455
# define BOOST_PP_SEQ_ENUM_457(x) x, BOOST_PP_SEQ_ENUM_456
# define BOOST_PP_SEQ_ENUM_458(x) x, BOOST_PP_SEQ_ENUM_457
# define BOOST_PP_SEQ_ENUM_459(x) x, BOOST_PP_SEQ_ENUM_458
# define BOOST_PP_SEQ_ENUM_460(x) x, BOOST_PP_SEQ_ENUM_459
# define BOOST_PP_SEQ_ENUM_461(x) x, BOOST_PP_SEQ_ENUM_460
# define BOOST_PP_SEQ_ENUM_462(x) x, BOOST_PP_SEQ_ENUM_461
# define BOOST_PP_SEQ_ENUM_463(x) x, BOOST_PP_SEQ_ENUM_462
# define BOOST_PP_SEQ_ENUM_464(x) x, BOOST_PP_SEQ_ENUM_463
# define BOOST_PP_SEQ_ENUM_465(x) x, BOOST_PP_SEQ_ENUM_464
# define BOOST_PP_SEQ_ENUM_466(x) x, BOOST_PP_SEQ_ENUM_465
# define BOOST_PP_SEQ_ENUM_467(x) x, BOOST_PP_SEQ_ENUM_466
# define BOOST_PP_SEQ_ENUM_468(x) x, BOOST_PP_SEQ_ENUM_467
# define BOOST_PP_SEQ_ENUM_469(x) x, BOOST_PP_SEQ_ENUM_468
# define BOOST_PP_SEQ_ENUM_470(x) x, BOOST_PP_SEQ_ENUM_469
# define BOOST_PP_SEQ_ENUM_471(x) x, BOOST_PP_SEQ_ENUM_470
# define BOOST_PP_SEQ_ENUM_472(x) x, BOOST_PP_SEQ_ENUM_471
# define BOOST_PP_SEQ_ENUM_473(x) x, BOOST_PP_SEQ_ENUM_472
# define BOOST_PP_SEQ_ENUM_474(x) x, BOOST_PP_SEQ_ENUM_473
# define BOOST_PP_SEQ_ENUM_475(x) x, BOOST_PP_SEQ_ENUM_474
# define BOOST_PP_SEQ_ENUM_476(x) x, BOOST_PP_SEQ_ENUM_475
# define BOOST_PP_SEQ_ENUM_477(x) x, BOOST_PP_SEQ_ENUM_476
# define BOOST_PP_SEQ_ENUM_478(x) x, BOOST_PP_SEQ_ENUM_477
# define BOOST_PP_SEQ_ENUM_479(x) x, BOOST_PP_SEQ_ENUM_478
# define BOOST_PP_SEQ_ENUM_480(x) x, BOOST_PP_SEQ_ENUM_479
# define BOOST_PP_SEQ_ENUM_481(x) x, BOOST_PP_SEQ_ENUM_480
# define BOOST_PP_SEQ_ENUM_482(x) x, BOOST_PP_SEQ_ENUM_481
# define BOOST_PP_SEQ_ENUM_483(x) x, BOOST_PP_SEQ_ENUM_482
# define BOOST_PP_SEQ_ENUM_484(x) x, BOOST_PP_SEQ_ENUM_483
# define BOOST_PP_SEQ_ENUM_485(x) x, BOOST_PP_SEQ_ENUM_484
# define BOOST_PP_SEQ_ENUM_486(x) x, BOOST_PP_SEQ_ENUM_485
# define BOOST_PP_SEQ_ENUM_487(x) x, BOOST_PP_SEQ_ENUM_486
# define BOOST_PP_SEQ_ENUM_488(x) x, BOOST_PP_SEQ_ENUM_487
# define BOOST_PP_SEQ_ENUM_489(x) x, BOOST_PP_SEQ_ENUM_488
# define BOOST_PP_SEQ_ENUM_490(x) x, BOOST_PP_SEQ_ENUM_489
# define BOOST_PP_SEQ_ENUM_491(x) x, BOOST_PP_SEQ_ENUM_490
# define BOOST_PP_SEQ_ENUM_492(x) x, BOOST_PP_SEQ_ENUM_491
# define BOOST_PP_SEQ_ENUM_493(x) x, BOOST_PP_SEQ_ENUM_492
# define BOOST_PP_SEQ_ENUM_494(x) x, BOOST_PP_SEQ_ENUM_493
# define BOOST_PP_SEQ_ENUM_495(x) x, BOOST_PP_SEQ_ENUM_494
# define BOOST_PP_SEQ_ENUM_496(x) x, BOOST_PP_SEQ_ENUM_495
# define BOOST_PP_SEQ_ENUM_497(x) x, BOOST_PP_SEQ_ENUM_496
# define BOOST_PP_SEQ_ENUM_498(x) x, BOOST_PP_SEQ_ENUM_497
# define BOOST_PP_SEQ_ENUM_499(x) x, BOOST_PP_SEQ_ENUM_498
# define BOOST_PP_SEQ_ENUM_500(x) x, BOOST_PP_SEQ_ENUM_499
# define BOOST_PP_SEQ_ENUM_501(x) x, BOOST_PP_SEQ_ENUM_500
# define BOOST_PP_SEQ_ENUM_502(x) x, BOOST_PP_SEQ_ENUM_501
# define BOOST_PP_SEQ_ENUM_503(x) x, BOOST_PP_SEQ_ENUM_502
# define BOOST_PP_SEQ_ENUM_504(x) x, BOOST_PP_SEQ_ENUM_503
# define BOOST_PP_SEQ_ENUM_505(x) x, BOOST_PP_SEQ_ENUM_504
# define BOOST_PP_SEQ_ENUM_506(x) x, BOOST_PP_SEQ_ENUM_505
# define BOOST_PP_SEQ_ENUM_507(x) x, BOOST_PP_SEQ_ENUM_506
# define BOOST_PP_SEQ_ENUM_508(x) x, BOOST_PP_SEQ_ENUM_507
# define BOOST_PP_SEQ_ENUM_509(x) x, BOOST_PP_SEQ_ENUM_508
# define BOOST_PP_SEQ_ENUM_510(x) x, BOOST_PP_SEQ_ENUM_509
# define BOOST_PP_SEQ_ENUM_511(x) x, BOOST_PP_SEQ_ENUM_510
# define BOOST_PP_SEQ_ENUM_512(x) x, BOOST_PP_SEQ_ENUM_511
#
# endif
