const postService = require("../services/Post");

exports.createPost = async (req, res) => {
  try {
    const {
      user_id,
      title,
      content,
      media_url,
      media_type,
      target_min_age,
      target_max_age,
      is_promoted,
      pay_per_view,
      reach_goal,
      duration_days,
      total_pay,
      platform_fee,
      post_target_locations,
      post_target_genders,
      video_category_id,
      start_date,
      end_date,
    } = req.body;

    // Input validation
    if (!user_id || !title || !media_type) {
      return res.status(400).json({
        status: false,
        statusCode: 400,
        message: "Missing required fields: user_id, title, media_type"
      });
    }

    // For non-promoted posts, either content or media_url is required
    if (!is_promoted && !content && !media_url) {
      return res.status(400).json({
        status: false,
        statusCode: 400,
        message: "Either content or media file is required for posts"
      });
    }

    if (is_promoted && (!pay_per_view || !reach_goal || !duration_days || !total_pay)) {
      return res.status(400).json({
        status: false,
        statusCode: 400,
        message: "Missing required promotional fields: pay_per_view, reach_goal, duration_days, total_pay"
      });
    }

    // Validate promoted post targeting requirements
    if (is_promoted) {
      if (!post_target_locations || !Array.isArray(post_target_locations) || post_target_locations.length === 0) {
        return res.status(400).json({
          status: false,
          statusCode: 400,
          message: "post_target_locations is required for promoted posts and must be a non-empty array"
        });
      }

      if (!post_target_genders || !Array.isArray(post_target_genders) || post_target_genders.length === 0) {
        return res.status(400).json({
          status: false,
          statusCode: 400,
          message: "post_target_genders is required for promoted posts and must be a non-empty array"
        });
      }

      // Validate that all gender IDs are valid numbers
      for (const gender of post_target_genders) {
        const genderId = typeof gender === 'object' ? gender.id : gender;
        if (!genderId || isNaN(parseInt(genderId))) {
          return res.status(400).json({
            status: false,
            statusCode: 400,
            message: "Invalid gender_id in post_target_genders array"
          });
        }
      }
    }

    // Validate media_type
    if (!['image', 'video', 'audio'].includes(media_type)) {
      return res.status(400).json({
        status: false,
        statusCode: 400,
        message: "Invalid media_type. Must be 'image', 'video', or 'audio'"
      });
    }

    let response;

    if (is_promoted) {
      console.log("promoted");
      response = await postService.createPostPromotion({
        user_id: user_id,
        title: title,
        content: content,
        mediaUrl: media_url,
        mediaType: media_type,
        target_min_age: target_min_age,
        target_max_age: target_max_age,
        pay_per_view: pay_per_view,
        reach_goal: reach_goal,
        duration_days: duration_days,
        total_pay: total_pay,
        platform_fee: platform_fee,
        isPromoted: is_promoted,
        post_target_locations: post_target_locations,
        post_target_genders: post_target_genders,
        video_category_id: video_category_id,
        start_date: start_date,
        end_date: end_date,
      });
    } else {
      console.log("not promoted");
      response = await postService.createPost({
        user_id: user_id,
        title: title,
        content: content,
        mediaUrl: media_url,
        mediaType: media_type,
        isPromoted: is_promoted,
        video_category_id: video_category_id,
        start_date: start_date,
        end_date: end_date,
      });
    }
    if (response.statusCode == 201) {
      return res.status(200).json(response);
    } else {
      return res.status(400).json(response);
    }
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};

exports.listPostsController = async (req, res) => {
  try {
    const {
      post_id,
      category,
      userId,
      page,
      limit,
      media_type,
      loggined_user_id,
    } = req.body;

    // Default pagination values
    const pageNumber = page && page > 0 ? parseInt(page) : 1;
    const limitNumber = limit && limit > 0 ? parseInt(limit) : 20;

    // Call the service function
    const response = await postService.listPosts(
      { post_id, category, userId, media_type, loggined_user_id },
      pageNumber,
      limitNumber
    );

    if (response.error) {
      return res.status(400).json({ status: false, message: response.error });
    }
    return res.status(200).json({
      status: true,
      message: response.message,
      data: response.data,
      pagination: response.pagination,
    });
  } catch (error) {
    console.error("Controller Error:", error);
    return res
      .status(500)
      .json({ status: false, message: "Internal Server Error" });
  }
};

exports.listPremiumPostsController = async (req, res) => {
  try {
    const {
      post_id,
      category,
      page,
      limit,
      media_type,
    } = req.query;

    // Default pagination values
    const pageNumber = page && page > 0 ? parseInt(page) : 1;
    const limitNumber = limit && limit > 0 ? parseInt(limit) : 20;

    // Call the service function
    const response = await postService.listPremiumPosts(
      { 
        post_id: post_id ? parseInt(post_id) : 0, 
        category: category ? parseInt(category) : undefined, 
        media_type 
      },
      pageNumber,
      limitNumber
    );

    if (response.error) {
      return res.status(400).json({ status: false, message: response.error });
    }
    return res.status(200).json({
      status: true,
      message: response.message,
      data: response.data,
      pagination: response.pagination,
    });
  } catch (error) {
    console.error("Controller Error:", error);
    return res
      .status(500)
      .json({ status: false, message: "Internal Server Error" });
  }
};

exports.saveUserPostLike = async (req, res) => {
  try {
    if (!req.body.userId || !req.body.postId)
      return res
        .status(400)
        .json({ status: false, message: "Invalid request", data: [] });

    const response = await postService.saveUserPostLike({
      userId: req.body.userId,
      postId: req.body.postId,
      is_liked: req.body.is_liked,
    });

    console.log("response", response);
    if (!response.error) {
      return res.status(200).json({
        status: true,
        message: response.message,
        is_liked: response.is_liked,
      });
    } else {
      res.status(400).json({ status: false, message: response.error });
    }
  } catch (error) {
    console.error("Controller Error:", error);
    return res
      .status(500)
      .json({ status: false, message: "Internal Server Error" });
  }
};

exports.saveUserPostComment = async (req, res) => {
  try {
    if (!req.body.postId || !req.body.userId || !req.body.comment)
      return res
        .status(400)
        .json({ status: false, message: "Invalid request.", data: [] });

    const response = await postService.saveUserPostComment({
      postId: req.body.postId,
      userId: req.body.userId,
      comment: req.body.comment,
    });

    if (!response.error) {
      res.status(200).json({ status: true, message: response.message });
    } else {
      res.status(400).json({ status: false, message: response.error });
    }
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: error.message ? error.message : "Internal Server Error",
    });
  }
};

exports.getUserPosts = async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 3, media_type, loggined_user_id } = req.query;

    if (!userId) {
      return res
        .status(400)
        .json({ status: false, message: "User ID is required.", data: [] });
    }

    // Ensure pagination values are positive integers
    const pageNumber = Math.max(1, parseInt(page, 10));
    const limitNumber = Math.max(1, parseInt(limit, 10));

    const response = await postService.getUserPosts({
      userId,
      media_type,
      page: pageNumber,
      limit: limitNumber,
      loggined_user_id,
    });

    if (response.error) {
      return res.status(400).json({ status: false, message: response.error });
    }

    res.status(200).json({
      status: true,
      message: response.message,
      data: response.data,
      pagination: response.pagination,
    });
  } catch (error) {
    console.error("Controller Error:", error);
    res.status(500).json({ status: false, message: "Internal Server Error" });
  }
};

exports.getPostComments = async (req, res) => {
  try {
    if (!req.params.postId)
      return res
        .status(400)
        .json({ status: false, message: "Invalid request.", data: [] });

    // Accept page and limit from query params, default limit to 20 if not provided
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    console.log("limit", limit);

    const response = await postService.getPostComments({
      postId: req.params.postId,
      page: page,
      limit: limit,
    });

    if (!response.error) {
      res.status(200).json({
        status: true,
        message: response.message,
        data: response.data,
        pagination: response.pagination,
      });
    } else {
      res.status(400).json({ status: false, message: response.error });
    }
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: error.message ? error.message : "Internal Server Error",
    });
  }
};

// Get single post by ID (for deep linking)
exports.getSinglePost = async (req, res) => {
  try {
    console.log('[getSinglePost] Request received');
    console.log('[getSinglePost] Params:', req.params);
    console.log('[getSinglePost] Query:', req.query);

    const { postId } = req.params;
    const { loggined_user_id } = req.query;

    if (!postId) {
      console.log('[getSinglePost] Missing postId');
      return res.status(400).json({
        status: false,
        message: "Post ID is required",
        data: []
      });
    }

    console.log('[getSinglePost] Calling listPosts service with:', {
      post_id: parseInt(postId),
      category: 0,
      loggined_user_id: loggined_user_id ? parseInt(loggined_user_id) : 0
    });

    // Use the existing listPosts service to fetch a single post
    const response = await postService.listPosts(
      {
        post_id: parseInt(postId),
        category: 0,
        loggined_user_id: loggined_user_id ? parseInt(loggined_user_id) : 0
      },
      1,
      1
    );

    console.log('[getSinglePost] Service response:', response);

    if (response.error) {
      console.log('[getSinglePost] Service returned error:', response.error);
      return res.status(400).json({
        status: false,
        message: response.error,
        data: []
      });
    }

    console.log('[getSinglePost] Returning success response');
    return res.status(200).json({
      status: true,
      message: response.message,
      data: response.data,
      pagination: response.pagination,
    });

  } catch (error) {
    console.error("[getSinglePost] Controller Error:", error);
    return res.status(500).json({
      status: false,
      message: "Internal Server Error",
      error: error.message,
      data: []
    });
  }
};

exports.updatePost = async (req, res) => {
  try {
    const {
      post_id,
      title,
      content,
      media_type,
      video_category_id,
      is_promoted,
      promotion_details,
      post_target_genders,
      post_target_locations,
    } = req.body;

    // Validate input data
    const fieldsToUpdate = {};

    if (title) fieldsToUpdate.title = title;
    if (content) fieldsToUpdate.content = content;
    if (media_type) fieldsToUpdate.media_type = media_type;
    if (video_category_id) fieldsToUpdate.video_category_id = video_category_id;
    if (post_target_locations !== undefined)
      fieldsToUpdate.target_locations = `${JSON.stringify(
        post_target_locations
      )}`;

    if (is_promoted !== undefined) fieldsToUpdate.is_promoted = is_promoted;

    if (Object.keys(fieldsToUpdate).length === 0) {
      return res.status(400).json({
        status: false,
        message: "At least one field must be provided for update",
      });
    }

    // Call the service to update the post
    const updateResult = await postService.updatePost({
      post_id: post_id,
      fieldsToUpdate: fieldsToUpdate,
      promotion_details: promotion_details,
      post_target_genders: post_target_genders,
      post_target_locations: post_target_locations,
    });

    if (updateResult.error) {
      return res.status(updateResult.status).json(updateResult);
    }

    return res.status(200).json({
      status: true,
      message: "Post updated successfully",
    });
  } catch (error) {
    console.error("Controller Error:", error);
    return res.status(500).json({
      status: false,
      message: "Internal Server Error",
      error: error.message,
    });
  }
};

exports.deletePost = async (req, res) => {
  try {
    const { post_id } = req.body;
    const user_id = req.user?.user_id; // Get user ID from JWT token

    if (!post_id) {
      return res.status(400).json({
        status: false,
        message: "Missing required field: post_id",
      });
    }

    if (!user_id) {
      return res.status(401).json({
        status: false,
        message: "User authentication required",
      });
    }

    const result = await postService.deletePost(post_id, user_id);
    if (result.error) {
      return res.status(400).json({
        status: false,
        message: result.error,
      });
    }

    return res.status(200).json({ status: true, message: result.message });
  } catch (error) {
    return res.status(500).json({
      status: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};
