# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 1051ms
  generate-prefab-packages
    [gap of 95ms]
    exec-prefab 1011ms
    [gap of 52ms]
  generate-prefab-packages completed in 1158ms
  execute-generate-process
    [gap of 16ms]
    exec-configure 1149ms
    [gap of 520ms]
  execute-generate-process completed in 1685ms
  [gap of 30ms]
  remove-unexpected-so-files 33ms
  [gap of 75ms]
generate_cxx_metadata completed in 4093ms

