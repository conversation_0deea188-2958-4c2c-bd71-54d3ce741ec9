{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-am/values-am.xml", "map": [{"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5096,5198,5337,5459,5561,5688,5811,5919,6153,6281,6384,6529,6652,6787,6914,6974,7031", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "5193,5332,5454,5556,5683,5806,5914,6016,6276,6379,6524,6647,6782,6909,6969,7026,7097"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,283,482,664,744,825,901,986,1067,1133,1195,1279,1362,1429,1492,1553,1619,1719,1821,1922,1991,2067,2135,2201,2280,2360,2422,2487,2540,2597,2643,2704,2762,2837,2896,2958,3017,3074,3138,3201,3265,3315,3371,3441,3511", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "278,477,659,739,820,896,981,1062,1128,1190,1274,1357,1424,1487,1548,1614,1714,1816,1917,1986,2062,2130,2196,2275,2355,2417,2482,2535,2592,2638,2699,2757,2832,2891,2953,3012,3069,3133,3196,3260,3310,3366,3436,3506,3558"}, "to": {"startLines": "2,11,15,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,378,577,8751,8831,8912,8988,9073,9154,9220,9282,9366,9449,9516,9579,9640,9706,9806,9908,10009,10078,10154,10222,10288,10367,10447,10509,11187,11240,11297,11343,11404,11462,11537,11596,11658,11717,11774,11838,11901,11965,12015,12071,12141,12211", "endLines": "10,14,18,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156", "endColumns": "17,12,12,79,80,75,84,80,65,61,83,82,66,62,60,65,99,101,100,68,75,67,65,78,79,61,64,52,56,45,60,57,74,58,61,58,56,63,62,63,49,55,69,69,51", "endOffsets": "373,572,754,8826,8907,8983,9068,9149,9215,9277,9361,9444,9511,9574,9635,9701,9801,9903,10004,10073,10149,10217,10283,10362,10442,10504,10569,11235,11292,11338,11399,11457,11532,11591,11653,11712,11769,11833,11896,11960,12010,12066,12136,12206,12258"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "57,58,59,60,61,62,63,254", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4141,4234,4334,4431,4530,4626,4728,19370", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "4229,4329,4426,4525,4621,4723,4823,19466"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-am\\values-am.xml", "from": {"startLines": "59", "startColumns": "4", "startOffsets": "3436", "endColumns": "63", "endOffsets": "3495"}, "to": {"startLines": "157", "startColumns": "4", "startOffsets": "12263", "endColumns": "63", "endOffsets": "12322"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,117,175,237,297,369,432,521,602", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "112,170,232,292,364,427,516,597,663"}, "to": {"startLines": "130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10574,10636,10694,10756,10816,10888,10951,11040,11121", "endColumns": "61,57,61,59,71,62,88,80,65", "endOffsets": "10631,10689,10751,10811,10883,10946,11035,11116,11182"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,239,286,337,398,461,546,606,696,776,876,926,984,1087,1158,1195,1269,1301,1337,1383,1447,1486", "endColumns": "39,46,50,60,62,84,59,89,79,99,49,57,102,70,36,73,31,35,45,63,38,55", "endOffsets": "238,285,336,397,460,545,605,695,775,875,925,983,1086,1157,1194,1268,1300,1336,1382,1446,1485,1541"}, "to": {"startLines": "228,229,230,231,232,233,234,235,236,237,238,239,240,241,243,244,245,246,247,248,249,256", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17618,17662,17713,17768,17833,17900,17989,18053,18147,18231,18335,18389,18451,18558,18709,18750,18828,18864,18904,18954,19022,19542", "endColumns": "43,50,54,64,66,88,63,93,83,103,53,61,106,74,40,77,35,39,49,67,42,59", "endOffsets": "17657,17708,17763,17828,17895,17984,18048,18142,18226,18330,18384,18446,18553,18628,18745,18823,18859,18899,18949,19017,19060,19597"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "66", "endOffsets": "268"}, "to": {"startLines": "255", "startColumns": "4", "startOffsets": "19471", "endColumns": "70", "endOffsets": "19537"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,114", "endOffsets": "161,276"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3538,3649", "endColumns": "110,114", "endOffsets": "3644,3759"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,222,288,388,476,555,634,711,793,867,926,1013,1094,1173,1245,1333,1402,1485,1556,1622,1697,1775,1859", "endColumns": "75,90,65,99,87,78,78,76,81,73,58,86,80,78,71,87,68,82,70,65,74,77,83,88", "endOffsets": "126,217,283,383,471,550,629,706,788,862,921,1008,1089,1168,1240,1328,1397,1480,1551,1617,1692,1770,1854,1943"}, "to": {"startLines": "86,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,177,178,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7198,7397,7488,7554,7654,7742,7821,7900,7977,8059,8133,8192,8279,8360,8439,8511,8599,8668,13721,13792,13858,13933,14011,14095", "endColumns": "75,90,65,99,87,78,78,76,81,73,58,86,80,78,71,87,68,82,70,65,74,77,83,88", "endOffsets": "7269,7483,7549,7649,7737,7816,7895,7972,8054,8128,8187,8274,8355,8434,8506,8594,8663,8746,13787,13853,13928,14006,14090,14179"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6021", "endColumns": "131", "endOffsets": "6148"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,965,1029,1114,1176,1234,1319,1382,1444,1502,1568,1630,1685,1781,1838,1897,1953,2020,2125,2205,2286,2378,2463,2544,2673,2746,2817,2931,3013,3089,3140,3191,3257,3323,3396,3467,3542,3610,3683,3754,3821,3919,4004,4071,4158,4246,4320,4388,4473,4524,4602,4666,4746,4828,4890,4954,5017,5083,5178,5273,5358,5449,5504,5559,5635,5714,5789", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "256,327,395,470,552,633,722,824,901,960,1024,1109,1171,1229,1314,1377,1439,1497,1563,1625,1680,1776,1833,1892,1948,2015,2120,2200,2281,2373,2458,2539,2668,2741,2812,2926,3008,3084,3135,3186,3252,3318,3391,3462,3537,3605,3678,3749,3816,3914,3999,4066,4153,4241,4315,4383,4468,4519,4597,4661,4741,4823,4885,4949,5012,5078,5173,5268,5353,5444,5499,5554,5630,5709,5784,5855"}, "to": {"startLines": "19,52,53,54,55,56,64,65,66,87,88,158,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,242,251,252,253", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "759,3764,3835,3903,3978,4060,4828,4917,5019,7274,7333,12327,12710,12772,12830,12915,12978,13040,13098,13164,13226,13281,13377,13434,13493,13549,13616,14184,14264,14345,14437,14522,14603,14732,14805,14876,14990,15072,15148,15199,15250,15316,15382,15455,15526,15601,15669,15742,15813,15880,15978,16063,16130,16217,16305,16379,16447,16532,16583,16661,16725,16805,16887,16949,17013,17076,17142,17237,17332,17417,17508,17563,18633,19145,19224,19299", "endLines": "22,52,53,54,55,56,64,65,66,87,88,158,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,242,251,252,253", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "915,3830,3898,3973,4055,4136,4912,5014,5091,7328,7392,12407,12767,12825,12910,12973,13035,13093,13159,13221,13276,13372,13429,13488,13544,13611,13716,14259,14340,14432,14517,14598,14727,14800,14871,14985,15067,15143,15194,15245,15311,15377,15450,15521,15596,15664,15737,15808,15875,15973,16058,16125,16212,16300,16374,16442,16527,16578,16656,16720,16800,16882,16944,17008,17071,17137,17232,17327,17412,17503,17558,17613,18704,19219,19294,19365"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,250", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "920,1018,1116,1222,1308,1411,1528,1606,1682,1773,1866,1958,2052,2152,2245,2340,2433,2524,2615,2695,2795,2895,2991,3093,3193,3292,3442,19065", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "1013,1111,1217,1303,1406,1523,1601,1677,1768,1861,1953,2047,2147,2240,2335,2428,2519,2610,2690,2790,2890,2986,3088,3188,3287,3437,3533,19140"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "85,159,160,161", "startColumns": "4,4,4,4", "startOffsets": "7102,12412,12507,12613", "endColumns": "95,94,105,96", "endOffsets": "7193,12502,12608,12705"}}]}]}