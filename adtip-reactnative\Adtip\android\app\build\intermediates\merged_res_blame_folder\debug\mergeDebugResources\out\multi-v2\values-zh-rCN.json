{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "5899", "endColumns": "98", "endOffsets": "5993"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,191,258,324,399,464,529,598,669,742,814,882,953,1026,1098,1175,1251,1323,1393,1462,1540,1608,1679,1746", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "116,186,253,319,394,459,524,593,664,737,809,877,948,1021,1093,1170,1246,1318,1388,1457,1535,1603,1674,1741,1810"}, "to": {"startLines": "50,68,164,166,167,169,189,190,191,252,253,254,262,267,268,269,270,271,272,273,274,276,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3407,4967,11959,12085,12151,12285,13609,13674,13743,17975,18048,18120,18457,18825,18898,18970,19047,19123,19195,19265,19334,19513,19581,19652,19719", "endColumns": "65,69,66,65,74,64,64,68,70,72,71,67,70,72,71,76,75,71,69,68,77,67,70,66,68", "endOffsets": "3468,5032,12021,12146,12221,12345,13669,13738,13809,18043,18115,18183,18523,18893,18965,19042,19118,19190,19260,19329,19407,19576,19647,19714,19783"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,678,747,815,892,968,1022,1084,1158,1232,1294,1355,1414,1480,1568,1651,1739,1802,1869,1934,1988,2062,2135,2196,2258,2310,2368,2415,2476,2533,2595,2652,2713,2769,2824,2887,2949,3012,3061,3112,3177,3242", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "282,445,603,673,742,810,887,963,1017,1079,1153,1227,1289,1350,1409,1475,1563,1646,1734,1797,1864,1929,1983,2057,2130,2191,2253,2305,2363,2410,2471,2528,2590,2647,2708,2764,2819,2882,2944,3007,3056,3107,3172,3237,3286"}, "to": {"startLines": "2,11,15,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,545,8291,8361,8430,8498,8575,8651,8705,8767,8841,8915,8977,9038,9097,9163,9251,9334,9422,9485,9552,9617,9671,9745,9818,9879,10502,10554,10612,10659,10720,10777,10839,10896,10957,11013,11068,11131,11193,11256,11305,11356,11421,11486", "endLines": "10,14,18,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "377,540,698,8356,8425,8493,8570,8646,8700,8762,8836,8910,8972,9033,9092,9158,9246,9329,9417,9480,9547,9612,9666,9740,9813,9874,9936,10549,10607,10654,10715,10772,10834,10891,10952,11008,11063,11126,11188,11251,11300,11351,11416,11481,11530"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "59", "startColumns": "4", "startOffsets": "3166", "endColumns": "60", "endOffsets": "3222"}, "to": {"startLines": "159", "startColumns": "4", "startOffsets": "11535", "endColumns": "60", "endOffsets": "11591"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,110", "endOffsets": "156,267"}, "to": {"startLines": "51,52", "startColumns": "4,4", "startOffsets": "3473,3579", "endColumns": "105,110", "endOffsets": "3574,3685"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "58,59,60,61,62,63,64,275", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4033,4125,4226,4320,4414,4507,4601,19412", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "4120,4221,4315,4409,4502,4596,4692,19508"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5037,5138,5267,5382,5484,5589,5705,5807,5998,6106,6207,6337,6452,6556,6664,6720,6777", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "5133,5262,5377,5479,5584,5700,5802,5894,6101,6202,6332,6447,6551,6659,6715,6772,6846"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "206", "endColumns": "67", "endOffsets": "273"}, "to": {"startLines": "280", "startColumns": "4", "startOffsets": "19788", "endColumns": "71", "endOffsets": "19855"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,911,973,1050,1109,1168,1246,1307,1364,1420,1479,1537,1591,1676,1732,1790,1844,1909,2001,2075,2147,2226,2300,2376,2498,2560,2622,2721,2800,2874,2924,2975,3041,3105,3174,3245,3316,3377,3448,3515,3575,3661,3740,3807,3890,3975,4049,4114,4190,4238,4311,4375,4451,4529,4591,4655,4718,4783,4863,4939,5017,5093,5147,5202,5271,5346,5419", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "242,306,368,438,508,585,676,782,855,906,968,1045,1104,1163,1241,1302,1359,1415,1474,1532,1586,1671,1727,1785,1839,1904,1996,2070,2142,2221,2295,2371,2493,2555,2617,2716,2795,2869,2919,2970,3036,3100,3169,3240,3311,3372,3443,3510,3570,3656,3735,3802,3885,3970,4044,4109,4185,4233,4306,4370,4446,4524,4586,4650,4713,4778,4858,4934,5012,5088,5142,5197,5266,5341,5414,5484"}, "to": {"startLines": "19,53,54,55,56,57,65,66,67,89,90,160,165,168,170,171,172,173,174,175,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,251,264,265,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "703,3690,3754,3816,3886,3956,4697,4788,4894,7001,7052,11596,12026,12226,12350,12428,12489,12546,12602,12661,12719,12773,12858,12914,12972,13026,13091,13814,13888,13960,14039,14113,14189,14311,14373,14435,14534,14613,14687,14737,14788,14854,14918,14987,15058,15129,15190,15261,15328,15388,15474,15553,15620,15703,15788,15862,15927,16003,16051,16124,16188,16264,16342,16404,16468,16531,16596,16676,16752,16830,16906,16960,17906,18607,18682,18755", "endLines": "22,53,54,55,56,57,65,66,67,89,90,160,165,168,170,171,172,173,174,175,176,177,178,179,180,181,182,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,251,264,265,266", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "845,3749,3811,3881,3951,4028,4783,4889,4962,7047,7109,11668,12080,12280,12423,12484,12541,12597,12656,12714,12768,12853,12909,12967,13021,13086,13178,13883,13955,14034,14108,14184,14306,14368,14430,14529,14608,14682,14732,14783,14849,14913,14982,15053,15124,15185,15256,15323,15383,15469,15548,15615,15698,15783,15857,15922,15998,16046,16119,16183,16259,16337,16399,16463,16526,16591,16671,16747,16825,16901,16955,17010,17970,18677,18750,18820"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,242,285,332,392,453,521,581,652,724,810,860,911,980,1038,1070,1113,1143,1173,1207,1247,1279", "endColumns": "38,42,46,59,60,67,59,70,71,85,49,50,68,57,31,42,29,29,33,39,31,55", "endOffsets": "241,284,331,391,452,520,580,651,723,809,859,910,979,1037,1069,1112,1142,1172,1206,1246,1278,1334"}, "to": {"startLines": "237,238,239,240,241,242,243,244,245,246,247,248,249,250,255,256,257,258,259,260,261,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17015,17058,17105,17156,17220,17285,17357,17421,17496,17572,17662,17716,17771,17844,18188,18224,18271,18305,18339,18377,18421,19860", "endColumns": "42,46,50,63,64,71,63,74,75,89,53,54,72,61,35,46,33,33,37,43,35,59", "endOffsets": "17053,17100,17151,17215,17280,17352,17416,17491,17567,17657,17711,17766,17839,17901,18219,18266,18300,18334,18372,18416,18452,19915"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,479,557", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "106,162,220,273,345,399,474,552,611"}, "to": {"startLines": "132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9941,9997,10053,10111,10164,10236,10290,10365,10443", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "9992,10048,10106,10159,10231,10285,10360,10438,10497"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "87,161,162,163", "startColumns": "4,4,4,4", "startOffsets": "6851,11673,11765,11866", "endColumns": "82,91,100,92", "endOffsets": "6929,11760,11861,11954"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,198,257,334,410,476,546,610,679,745,798,877,944,1022,1088,1166,1228,1299,1364,1428,1497,1569,1645", "endColumns": "66,75,58,76,75,65,69,63,68,65,52,78,66,77,65,77,61,70,64,63,68,71,75,79", "endOffsets": "117,193,252,329,405,471,541,605,674,740,793,872,939,1017,1083,1161,1223,1294,1359,1423,1492,1564,1640,1720"}, "to": {"startLines": "88,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,183,184,185,186,187,188", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6934,7114,7190,7249,7326,7402,7468,7538,7602,7671,7737,7790,7869,7936,8014,8080,8158,8220,13183,13248,13312,13381,13453,13529", "endColumns": "66,75,58,76,75,65,69,63,68,65,52,78,66,77,65,77,61,70,64,63,68,71,75,79", "endOffsets": "6996,7185,7244,7321,7397,7463,7533,7597,7666,7732,7785,7864,7931,8009,8075,8153,8215,8286,13243,13307,13376,13448,13524,13604"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,945,1040,1140,1222,1319,1425,1502,1577,1668,1761,1858,1954,2048,2141,2236,2328,2419,2510,2588,2684,2779,2874,2971,3067,3165,3313,18528", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "940,1035,1135,1217,1314,1420,1497,1572,1663,1756,1853,1949,2043,2136,2231,2323,2414,2505,2583,2679,2774,2869,2966,3062,3160,3308,3402,18602"}}]}]}