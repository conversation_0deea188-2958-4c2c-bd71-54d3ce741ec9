<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Funds - Razorpay Payment</title>
</head>
<body>
    <button id="pay-btn">Add ₹200 Funds</button>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        const API_BASE_URL = 'http://localhost:7082/api';
        const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.CP2hoyHw7dOjB8A6uIifbdfNsztf0Pt1BSw8pEdM92Q'; // Replace with your auth token
        const USER_ID = 50816;
        const RAZORPAY_KEY = 'rzp_test_ojNkCSTYuUL3w9'; 

        document.getElementById('pay-btn').onclick = async function () {
            try {
                // Step 1: Create Razorpay order
                const orderResponse = await fetch(`${API_BASE_URL}/razorpay-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': AUTH_TOKEN
                    },
                    body: JSON.stringify({
                        amount: 200,
                        currency: 'INR',
                        user_id: USER_ID
                    })
                });
                const orderData = await orderResponse.json();

                if (!orderData.status) {
                    alert('Order creation failed: ' + orderData.message);
                    return;
                }

                // Step 2: Open Razorpay checkout
                const options = {
                    key: RAZORPAY_KEY,
                    amount: orderData.data.amount,
                    currency: 'INR',
                    name: 'Your Business Name',
                    description: 'Add Funds',
                    order_id: orderData.data.id,
                    handler: async function (response) {
                        console.log('Payment Successful:', response);

                        // Step 3: Verify payment with /razorpay-verification
                        try {
                            const verificationResponse = await fetch(`${API_BASE_URL}/razorpay-verification`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Authorization': AUTH_TOKEN
                                },
                                body: JSON.stringify({
                                    transaction_for: 'add_funds',
                                    order_id: response.razorpay_order_id,
                                    razorpay_payment_id: response.razorpay_payment_id,
                                    razorpay_signature: response.razorpay_signature,
                                    amount: orderData.data.amount / 100,
                                    currency: 'INR',
                                    user_id: USER_ID,
                                    payment_status: 'success'
                                })
                            });
                            const verificationData = await verificationResponse.json();

                            if (!verificationData.status) {
                                alert('Payment verification failed: ' + (verificationData.message || 'Unknown error'));
                                return;
                            }

                            // Step 4: Call /addfunds to update wallet
                            const addFundsResponse = await fetch(`${API_BASE_URL}/addfunds`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Authorization': AUTH_TOKEN
                                },
                                body: JSON.stringify({
                                    createdby: USER_ID,
                                    amount: orderData.data.amount / 100,
                                    transactionStatus: '1', // 1 for success
                                    transaction_type: 'Deposite',
                                    order_id: response.razorpay_order_id,
                                    payment_id: response.razorpay_payment_id,
                                    isCron: false
                                })
                            });
                            const addFundsData = await addFundsResponse.json();

                            if (addFundsData.status === 200) {
                                alert('Funds added successfully!');
                            } else {
                                alert('Funds addition failed: ' + (addFundsData.message || 'Unknown error'));
                            }
                        } catch (err) {
                            console.error('Error:', err);
                            alert('Error processing payment: ' + err.message);
                        }
                    },
                    prefill: {
                        name: 'Test User',
                        email: '<EMAIL>',
                        contact: '9999999999'
                    },
                    theme: {
                        color: '#0B72B5'
                    }
                };

                const rzp = new Razorpay(options);
                rzp.open();
            } catch (err) {
                console.error('Error:', err);
                alert('Error occurred: ' + err.message);
            }
        };
    </script>
</body>
</html>