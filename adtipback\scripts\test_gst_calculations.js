/**
 * Test script for GST calculations
 * Run with: node scripts/test_gst_calculations.js
 */

const { calculateGST, formatGSTForAPI, addGSTToPlans } = require('../utils/gstCalculator');

console.log('🧪 Testing GST Calculations\n');

// Test 1: Basic GST calculation
console.log('1. Basic GST Calculation:');
const testAmount = 200;
const gstResult = calculateGST(testAmount);
console.log(`   Base Amount: ₹${gstResult.base_amount}`);
console.log(`   GST Amount: ₹${gstResult.gst_amount}`);
console.log(`   Total Amount: ₹${gstResult.total_amount}`);
console.log(`   GST Percentage: ${gstResult.gst_percentage}%`);
console.log(`   Amount in Paise: ${gstResult.amount_with_gst_paise}`);
console.log('');

// Test 2: Format for API
console.log('2. Format for API:');
const apiResult = formatGSTForAPI(testAmount);
console.log(`   Base Amount: ₹${apiResult.base_amount}`);
console.log(`   GST Amount: ₹${apiResult.gst_amount}`);
console.log(`   Total Amount: ₹${apiResult.total_amount}`);
console.log(`   Amount with GST (paise): ${apiResult.amount_with_gst}`);
console.log(`   GST Amount (paise): ${apiResult.gst_amount_paise}`);
console.log('');

// Test 3: Add GST to plans
console.log('3. Add GST to Plans:');
const samplePlans = [
  {
    id: 'plan_1',
    name: 'Premium - 1 Month',
    amount: 200,
    currency: 'INR',
    period: 1,
    interval: 'monthly',
    description: 'Access to premium features for 1 month'
  },
  {
    id: 'plan_2',
    name: 'Premium - 6 Months',
    amount: 1200,
    currency: 'INR',
    period: 6,
    interval: 'monthly',
    description: 'Access to premium features for 6 months'
  },
  {
    id: 'plan_3',
    name: 'Content Creator - 1 Month',
    amount: 2500,
    currency: 'INR',
    period: 1,
    interval: 'monthly',
    description: 'Access to content creator features for 1 month'
  }
];

const plansWithGST = addGSTToPlans(samplePlans);
plansWithGST.forEach((plan, index) => {
  console.log(`   Plan ${index + 1}: ${plan.name}`);
  console.log(`     Base Amount: ₹${plan.amount}`);
  console.log(`     GST Amount: ₹${plan.gst_amount}`);
  console.log(`     Total Amount: ₹${plan.total_amount}`);
  console.log(`     Amount with GST (paise): ${plan.amount_with_gst}`);
  console.log('');
});

// Test 4: Verify calculations
console.log('4. Verification Tests:');
const testCases = [
  { base: 200, expectedGST: 36, expectedTotal: 236 },
  { base: 1200, expectedGST: 216, expectedTotal: 1416 },
  { base: 2400, expectedGST: 432, expectedTotal: 2832 },
  { base: 2500, expectedGST: 450, expectedTotal: 2950 },
  { base: 6000, expectedGST: 1080, expectedTotal: 7080 },
  { base: 12000, expectedGST: 2160, expectedTotal: 14160 },
  { base: 24000, expectedGST: 4320, expectedTotal: 28320 }
];

testCases.forEach((testCase, index) => {
  const result = calculateGST(testCase.base);
  const gstCorrect = Math.abs(result.gst_amount - testCase.expectedGST) < 0.01;
  const totalCorrect = Math.abs(result.total_amount - testCase.expectedTotal) < 0.01;
  
  console.log(`   Test ${index + 1}: Base ₹${testCase.base}`);
  console.log(`     Expected GST: ₹${testCase.expectedGST}, Actual: ₹${result.gst_amount} - ${gstCorrect ? '✅' : '❌'}`);
  console.log(`     Expected Total: ₹${testCase.expectedTotal}, Actual: ₹${result.total_amount} - ${totalCorrect ? '✅' : '❌'}`);
});

console.log('\n🎉 GST Calculation Tests Completed!');
console.log('\n📋 Summary:');
console.log('- GST percentage: 18%');
console.log('- All amounts converted to paise for Razorpay');
console.log('- Original amounts preserved for UI display');
console.log('- GST details stored in subscription notes'); 