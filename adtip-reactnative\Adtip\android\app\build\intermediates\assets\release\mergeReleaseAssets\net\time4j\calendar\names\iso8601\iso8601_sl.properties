# months
M(a)_1=jan.
M(a)_2=feb.
M(a)_3=mar.
M(a)_4=apr.
M(a)_5=maj
M(a)_6=jun.
M(a)_7=jul.
M(a)_8=avg.
M(a)_9=sep.
M(a)_10=okt.
M(a)_11=nov.
M(a)_12=dec.

M(n)_1=j
M(n)_2=f
M(n)_3=m
M(n)_4=a
M(n)_5=m
M(n)_6=j
M(n)_7=j
M(n)_8=a
M(n)_9=s
M(n)_10=o
M(n)_11=n
M(n)_12=d

M(w)_1=januar
M(w)_2=februar
M(w)_3=marec
M(w)_4=april
M(w)_5=maj
M(w)_6=junij
M(w)_7=julij
M(w)_8=avgust
M(w)_9=september
M(w)_10=oktober
M(w)_11=november
M(w)_12=december

M(A)_1=jan.
M(A)_2=feb.
M(A)_3=mar.
M(A)_4=apr.
M(A)_5=maj
M(A)_6=jun.
M(A)_7=jul.
M(A)_8=avg.
M(A)_9=sep.
M(A)_10=okt.
M(A)_11=nov.
M(A)_12=dec.

M(N)_1=j
M(N)_2=f
M(N)_3=m
M(N)_4=a
M(N)_5=m
M(N)_6=j
M(N)_7=j
M(N)_8=a
M(N)_9=s
M(N)_10=o
M(N)_11=n
M(N)_12=d

M(W)_1=januar
M(W)_2=februar
M(W)_3=marec
M(W)_4=april
M(W)_5=maj
M(W)_6=junij
M(W)_7=julij
M(W)_8=avgust
M(W)_9=september
M(W)_10=oktober
M(W)_11=november
M(W)_12=december

# weekdays
D(a)_1=pon.
D(a)_2=tor.
D(a)_3=sre.
D(a)_4=čet.
D(a)_5=pet.
D(a)_6=sob.
D(a)_7=ned.

D(n)_1=p
D(n)_2=t
D(n)_3=s
D(n)_4=č
D(n)_5=p
D(n)_6=s
D(n)_7=n

D(s)_1=pon.
D(s)_2=tor.
D(s)_3=sre.
D(s)_4=čet.
D(s)_5=pet.
D(s)_6=sob.
D(s)_7=ned.

D(w)_1=ponedeljek
D(w)_2=torek
D(w)_3=sreda
D(w)_4=četrtek
D(w)_5=petek
D(w)_6=sobota
D(w)_7=nedelja

D(A)_1=pon.
D(A)_2=tor.
D(A)_3=sre.
D(A)_4=čet.
D(A)_5=pet.
D(A)_6=sob.
D(A)_7=ned.

D(N)_1=p
D(N)_2=t
D(N)_3=s
D(N)_4=č
D(N)_5=p
D(N)_6=s
D(N)_7=n

D(S)_1=pon.
D(S)_2=tor.
D(S)_3=sre.
D(S)_4=čet.
D(S)_5=pet.
D(S)_6=sob.
D(S)_7=ned.

D(W)_1=ponedeljek
D(W)_2=torek
D(W)_3=sreda
D(W)_4=četrtek
D(W)_5=petek
D(W)_6=sobota
D(W)_7=nedelja

# quarters
Q(a)_1=1. čet.
Q(a)_2=2. čet.
Q(a)_3=3. čet.
Q(a)_4=4. čet.

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. četrtletje
Q(w)_2=2. četrtletje
Q(w)_3=3. četrtletje
Q(w)_4=4. četrtletje

Q(A)_1=1. čet.
Q(A)_2=2. čet.
Q(A)_3=3. čet.
Q(A)_4=4. čet.

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1. četrtletje
Q(W)_2=2. četrtletje
Q(W)_3=3. četrtletje
Q(W)_4=4. četrtletje

# day-period-rules
T0600=morning1
T1000=morning2
T1200=afternoon1
T1800=evening1
T2200=night1

# day-period-translations
P(a)_midnight=opoln.
P(a)_am=dop.
P(a)_noon=opold.
P(a)_pm=pop.
P(a)_morning1=zjut.
P(a)_morning2=dop.
P(a)_afternoon1=pop.
P(a)_evening1=zveč.
P(a)_night1=ponoči

P(n)_midnight=24.00
P(n)_am=d
P(n)_noon=12.00
P(n)_pm=p
P(n)_morning1=zj
P(n)_morning2=d
P(n)_afternoon1=p
P(n)_evening1=zv
P(n)_night1=po

P(w)_midnight=opolnoči
P(w)_am=dop.
P(w)_noon=opoldne
P(w)_pm=pop.
P(w)_morning1=zjutraj
P(w)_morning2=dopoldan
P(w)_afternoon1=popoldan
P(w)_evening1=zvečer
P(w)_night1=ponoči

P(A)_midnight=poln.
P(A)_am=dop.
P(A)_noon=pold.
P(A)_pm=pop.
P(A)_morning1=jut.
P(A)_morning2=dop.
P(A)_afternoon1=pop.
P(A)_evening1=zveč.
P(A)_night1=noč

P(N)_midnight=24.00
P(N)_am=d
P(N)_noon=12.00
P(N)_pm=p
P(N)_morning1=j
P(N)_morning2=d
P(N)_afternoon1=p
P(N)_evening1=v
P(N)_night1=n

P(W)_midnight=polnoč
P(W)_am=dopoldne
P(W)_noon=poldne
P(W)_pm=popoldne
P(W)_morning1=jutro
P(W)_morning2=dopoldne
P(W)_afternoon1=popoldne
P(W)_evening1=večer
P(W)_night1=noč

# eras
E(w)_0=pred Kristusom
E(w|alt)_0=pred našim štetjem
E(w)_1=po Kristusu
E(w|alt)_1=po našem štetju

E(a)_0=pr. Kr.
E(a|alt)_0=pr. n. št.
E(a)_1=po Kr.
E(a|alt)_1=po n. št.

# format patterns
F(f)_d=EEEE, dd. MMMM y
F(l)_d=dd. MMMM y
F(m)_d=d. MMM y
F(s)_d=d. MM. yy

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH'h'
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d. M.
F_MMMd=d. MMM
F_MMMMd=d. MMMM
F_y=y
F_yM=M/y
F_yMMM=MMM y
F_yMMMM=MMMM y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=w. 'teden' 'v' Y

I={0}–{1}

# labels of elements
L_era=doba
L_year=leto
L_quarter=četrtletje
L_month=mesec
L_week=teden
L_day=dan
L_weekday=dan v tednu
L_dayperiod=dop/pop
L_hour=ura
L_minute=minuta
L_second=sekunda
L_zone=časovni pas
