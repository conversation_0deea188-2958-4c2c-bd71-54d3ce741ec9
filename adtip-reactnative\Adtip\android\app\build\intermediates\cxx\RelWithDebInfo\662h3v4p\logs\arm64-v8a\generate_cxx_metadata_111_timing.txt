# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    [gap of 68ms]
    exec-prefab 813ms
    [gap of 20ms]
  generate-prefab-packages completed in 901ms
  execute-generate-process
    exec-configure 8799ms
    [gap of 351ms]
  execute-generate-process completed in 9152ms
  [gap of 12ms]
  remove-unexpected-so-files 21ms
  [gap of 17ms]
generate_cxx_metadata completed in 10129ms

