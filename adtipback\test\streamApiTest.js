// adtipback/test/streamApiTest.js
// Test script for Cloudflare Stream API integration

require('dotenv').config();
const CloudflareStreamService = require('../services/CloudflareStreamService');

async function testStreamAPI() {
  console.log('🧪 Testing Cloudflare Stream API Integration...\n');

  try {
    // Test 1: API Connection
    console.log('1️⃣ Testing API Connection...');
    const connectionTest = await CloudflareStreamService.testConnection();
    
    if (connectionTest.success) {
      console.log('✅ API Connection successful!');
      console.log('   Status:', connectionTest.status);
    } else {
      console.log('❌ API Connection failed!');
      console.log('   Status:', connectionTest.status);
      console.log('   Error:', connectionTest.error);
      console.log('   Response data:', JSON.stringify(connectionTest.data, null, 2));
      return;
    }

    // Test 2: List existing videos
    console.log('\n2️⃣ Testing List Videos...');
    const listResult = await CloudflareStreamService.listVideos({ limit: 5 });
    
    if (listResult.success) {
      console.log('✅ List Videos successful!');
      console.log('   Total videos found:', listResult.total);
      if (listResult.videos.length > 0) {
        console.log('   Sample video:', {
          uid: listResult.videos[0].uid,
          status: listResult.videos[0].status,
          created: listResult.videos[0].created
        });
      }
    } else {
      console.log('❌ List Videos failed!');
      console.log('   Error:', listResult.error);
    }

    // Test 3: URL Generation
    console.log('\n3️⃣ Testing URL Generation...');
    const testVideoId = 'test-video-id-123';
    
    const playerUrl = CloudflareStreamService.getStreamPlayerUrl(testVideoId, {
      autoplay: false,
      muted: true,
      controls: true
    });
    
    const manifestUrl = CloudflareStreamService.getManifestUrl(testVideoId, 'hls');
    const videoUrl = CloudflareStreamService.getVideoUrl(testVideoId, '720p');
    
    console.log('✅ URL Generation successful!');
    console.log('   Player URL:', playerUrl);
    console.log('   Manifest URL:', manifestUrl);
    console.log('   Direct Video URL:', videoUrl);

    // Test 4: Configuration Check
    console.log('\n4️⃣ Configuration Check...');
    console.log('✅ Configuration loaded:');
    console.log('   Account ID:', CloudflareStreamService.accountId);
    console.log('   API Token:', CloudflareStreamService.apiToken ? '***' + CloudflareStreamService.apiToken.slice(-4) : 'Not set');
    console.log('   Customer Code:', CloudflareStreamService.customerCode);
    console.log('   Base URL:', CloudflareStreamService.baseUrl);

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('   1. Run database migration: node migrations/add_stream_fields.sql');
    console.log('   2. Test video upload with a sample file');
    console.log('   3. Verify webhook endpoint is accessible');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testStreamAPI();
}

module.exports = { testStreamAPI };
