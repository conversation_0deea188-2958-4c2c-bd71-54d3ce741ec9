# Nodejs Mysql 

Adtip Backend API. 

## Tools and Technologies used

* NodeJS 10.x
* Express 4.x
* MySQL 

The server will start on port 7070.

## Heroku Command for deployment
 * heroku git:remote -a adtip-java-api
 * git push heroku main
 * heroku logs --tail
	
## Explore Rest APIs

The app defines following Adtip APIs.
    GET /api/

    POST /api/otplogin
    
    POST /api/otpverify



 node Commands
 npm install dotenv --save   