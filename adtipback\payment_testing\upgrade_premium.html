<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Razorpay Payment</title>
</head>
<body>
    <button id="pay-btn">Pay ₹200</button>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:7082/api';
        const AUTH_TOKEN = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************.CP2hoyHw7dOjB8A6uIifbdfNsztf0Pt1BSw8pEdM92Q'; // Replace with your auth token
        const USER_ID = 51951; // Ensure this matches user_id in /upgrade-premium
        const RAZORPAY_KEY = 'rzp_test_Wpj489Mi7DAY7c'; // Replace with your Razorpay Key ID
        const PLAN_ID = 1; // Replace with the appropriate plan ID

        document.getElementById('pay-btn').onclick = async function (e) {
            try {
                // Step 1: Create Razorpay order
                const orderResponse = await fetch(`${API_BASE_URL}/razorpay-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': AUTH_TOKEN
                    },
                    body: JSON.stringify({
                        amount: 200, // Amount in INR
                        currency: 'INR',
                        user_id: USER_ID
                    })
                });
                const orderData = await orderResponse.json();

                if (!orderData.status) {
                    alert('Order creation failed: ' + orderData.message);
                    console.error('Order creation failed:', orderData);
                    return;
                }

                // Step 2: Initialize Razorpay checkout
                const options = {
                    key: RAZORPAY_KEY,
                    amount: orderData.data.amount, // Amount in paise (e.g., 20000 for ₹200)
                    currency: 'INR',
                    name: 'Your Business Name',
                    description: 'Premium Plan Subscription',
                    order_id: orderData.data.id, // Razorpay order ID
                    handler: async function (response) {
                        console.log('Payment Successful:', response);

                        // Step 3: Verify payment with backend
                        const verificationResponse = await fetch(`${API_BASE_URL}/razorpay-verification`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': AUTH_TOKEN
                            },
                            body: JSON.stringify({
                                order_id: response.razorpay_order_id,
                                razorpay_payment_id: response.razorpay_payment_id,
                                razorpay_signature: response.razorpay_signature,
                                amount: orderData.data.amount / 100, // Convert paise to INR
                                currency: 'INR',
                                user_id: USER_ID,
                                payment_status: 'success'
                            })
                        });
                        const verificationData = await verificationResponse.json();

                        if (!verificationData.status) {
                            alert('Payment verification failed: ' + verificationData.message);
                            console.error('Verification failed:', verificationData);
                            return;
                        }

                        // Step 4: Upgrade to premium via /api/upgrade-premium
                        const upgradePremiumResponse = await fetch(`${API_BASE_URL}/upgrade-premium`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': AUTH_TOKEN
                            },
                            body: JSON.stringify({
                                payment_status: 'success',
                                user_id: USER_ID,
                                plan_id: PLAN_ID,
                                order_id: response.razorpay_order_id,
                                payment_id: response.razorpay_payment_id,
                                coupon_code: null // Set to a valid coupon code if applicable
                            })
                        });
                        const upgradePremiumData = await upgradePremiumResponse.json();

                        if (upgradePremiumData.status) {
                            alert('Premium upgrade successful: ' + upgradePremiumData.message);
                            console.log('Upgrade premium response:', upgradePremiumData);
                        } else {
                            alert('Failed to upgrade premium: ' + upgradePremiumData.message);
                            console.error('Upgrade premium failed:', upgradePremiumData);
                        }
                    },
                    prefill: {
                        name: 'Test User',
                        email: '<EMAIL>',
                        contact: '9999999999'
                    },
                    theme: {
                        color: '#F37254'
                    }
                };

                const rzp = new Razorpay(options);
                rzp.open();
            } catch (err) {
                console.error('Payment process error:', err);
                alert('An error occurred: ' + err.message);
            }
        };
    </script>
</body>
</html>