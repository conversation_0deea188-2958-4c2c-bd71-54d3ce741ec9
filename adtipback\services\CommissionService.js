// Commission Service - Handles platform commission calculations
const dbQuery = require('../dbConfig/queryRunner');

class CommissionService {
  
  /**
   * Get platform commission rate based on user premium status
   * @param {number} userId - User ID
   * @returns {Promise<{commissionRate: number, isPremium: boolean}>}
   */
  static async getCommissionRate(userId) {
    try {
      // Check if user has active premium subscription
      const userQuery = `
        SELECT 
          u.id,
          u.premium_plan_id,
          CASE 
            WHEN u.premium_plan_id > 0 AND us.status = 'active' AND us.current_end_at > NOW() THEN 1
            ELSE 0
          END as is_premium_active
        FROM users u
        LEFT JOIN user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
        WHERE u.id = ?
      `;
      
      const [user] = await dbQuery.queryRunner(userQuery, [userId]);
      
      if (!user) {
        throw new Error('User not found');
      }
      
      const isPremium = user.is_premium_active === 1;
      
      // Commission rates as per requirements:
      // Non-premium users: 60% platform commission
      // Premium users: 30% platform commission
      const commissionRate = isPremium ? 0.30 : 0.60;
      
      return {
        commissionRate,
        isPremium,
        userId: user.id
      };
    } catch (error) {
      console.error('[CommissionService] Error getting commission rate:', error);
      throw error;
    }
  }
  
  /**
   * Calculate platform commission for an earning transaction
   * @param {number} userId - User ID
   * @param {number} grossAmount - Gross earning amount
   * @returns {Promise<{grossAmount: number, commission: number, netAmount: number, commissionRate: number, isPremium: boolean}>}
   */
  static async calculateCommission(userId, grossAmount) {
    try {
      const { commissionRate, isPremium } = await this.getCommissionRate(userId);
      
      const commission = grossAmount * commissionRate;
      const netAmount = grossAmount - commission;
      
      return {
        grossAmount: parseFloat(grossAmount.toFixed(2)),
        commission: parseFloat(commission.toFixed(2)),
        netAmount: parseFloat(netAmount.toFixed(2)),
        commissionRate,
        isPremium
      };
    } catch (error) {
      console.error('[CommissionService] Error calculating commission:', error);
      throw error;
    }
  }
  
  /**
   * Apply commission to earning transaction and update wallet
   * @param {number} userId - User ID
   * @param {number} grossAmount - Gross earning amount
   * @param {string} transactionType - Type of transaction (e.g., 'tip-call-earning', 'ad-reward', etc.)
   * @param {string} description - Transaction description
   * @returns {Promise<{success: boolean, transactionId: number, commission: object}>}
   */
  static async processEarningWithCommission(userId, grossAmount, transactionType, description = '') {
    try {
      const commission = await this.calculateCommission(userId, grossAmount);
      
      // Get current wallet balance
      const balanceQuery = `SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1`;
      const [balanceData] = await dbQuery.queryRunner(balanceQuery, [userId]);
      const currentBalance = parseFloat(balanceData?.totalBalance || 0);
      const newBalance = currentBalance + commission.netAmount;
      
      // Insert wallet transaction with commission details
      const walletQuery = `
        INSERT INTO wallet (
          totalBalance, transaction_status, amount, transaction_date, transaction_type,
          createdby, createddate, updateddate, description,
          gross_amount, platform_commission, commission_rate
        ) VALUES (?, ?, ?, NOW(), ?, ?, NOW(), NOW(), ?, ?, ?, ?)
      `;
      
      const transactionDescription = description || 
        `${transactionType} - ${commission.isPremium ? 'Premium' : 'Regular'} user (${(commission.commissionRate * 100)}% commission)`;
      
      const result = await dbQuery.queryRunner(walletQuery, [
        newBalance,
        1, // transaction_status (success)
        commission.netAmount,
        transactionType,
        userId,
        transactionDescription,
        commission.grossAmount,
        commission.commission,
        commission.commissionRate
      ]);
      
      return {
        success: true,
        transactionId: result.insertId,
        commission,
        newBalance
      };
    } catch (error) {
      console.error('[CommissionService] Error processing earning with commission:', error);
      throw error;
    }
  }
  
  /**
   * Get commission summary for a user
   * @param {number} userId - User ID
   * @param {string} period - Period ('today', 'week', 'month', 'all')
   * @returns {Promise<{totalGross: number, totalCommission: number, totalNet: number, transactionCount: number}>}
   */
  static async getCommissionSummary(userId, period = 'all') {
    try {
      let dateFilter = '';
      
      switch (period) {
        case 'today':
          dateFilter = 'AND DATE(transaction_date) = CURDATE()';
          break;
        case 'week':
          dateFilter = 'AND transaction_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
          break;
        case 'month':
          dateFilter = 'AND transaction_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
          break;
        default:
          dateFilter = '';
      }
      
      const summaryQuery = `
        SELECT 
          COUNT(*) as transaction_count,
          COALESCE(SUM(gross_amount), 0) as total_gross,
          COALESCE(SUM(platform_commission), 0) as total_commission,
          COALESCE(SUM(amount), 0) as total_net
        FROM wallet 
        WHERE createdby = ? 
        AND gross_amount IS NOT NULL 
        AND platform_commission IS NOT NULL
        ${dateFilter}
      `;
      
      const [summary] = await dbQuery.queryRunner(summaryQuery, [userId]);
      
      return {
        totalGross: parseFloat(summary.total_gross || 0),
        totalCommission: parseFloat(summary.total_commission || 0),
        totalNet: parseFloat(summary.total_net || 0),
        transactionCount: parseInt(summary.transaction_count || 0)
      };
    } catch (error) {
      console.error('[CommissionService] Error getting commission summary:', error);
      throw error;
    }
  }
}

module.exports = CommissionService;
