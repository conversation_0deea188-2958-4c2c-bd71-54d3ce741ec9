
#ifndef BOOST_MPL_SIZE_FWD_HPP_INCLUDED
#define BOOST_MPL_SIZE_FWD_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

namespace boost { namespace mpl {

template< typename Tag > struct size_impl;
template< typename Sequence > struct size;

}}

#endif // BOOST_MPL_SIZE_FWD_HPP_INCLUDED
