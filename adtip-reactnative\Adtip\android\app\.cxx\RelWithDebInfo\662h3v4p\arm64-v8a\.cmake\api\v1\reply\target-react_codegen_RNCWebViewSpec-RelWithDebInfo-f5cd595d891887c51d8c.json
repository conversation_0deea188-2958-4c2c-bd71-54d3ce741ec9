{"artifacts": [{"path": "RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/./RNCWebViewSpec-generated.cpp.o"}, {"path": "RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/./react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o"}, {"path": "RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/./react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o"}, {"path": "RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/./react/renderer/components/RNCWebViewSpec/Props.cpp.o"}, {"path": "RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/./react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o"}, {"path": "RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/./react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o"}, {"path": "RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/./react/renderer/components/RNCWebViewSpec/States.cpp.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_include_directories", "target_link_libraries"], "files": ["F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -O2 -g -DNDEBUG -fPIC"}, {"backtrace": 2, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "includes": [{"backtrace": 3, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/."}, {"backtrace": 3, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec"}, {"backtrace": 4, "isSystem": true, "path": "F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "F:/R17DevTools/.gradle/caches/8.13/transforms/0dbfeb2f307611649a8567893eebd290/transformed/jetified-react-android-0.79.2-release/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "F:/R17DevTools/.gradle/caches/8.13/transforms/0dbfeb2f307611649a8567893eebd290/transformed/jetified-react-android-0.79.2-release/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "name": "react_codegen_RNCWebViewSpec", "paths": {"build": "RNCWebViewSpec_autolinked_build", "source": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "F:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}