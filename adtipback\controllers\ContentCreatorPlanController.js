const Razorpay = require("razorpay");
const { queryRunner } = require("../dbConfig/queryRunner");
const crypto = require('crypto');
require("dotenv").config();

const razorpayInstance = new Razorpay({
  key_id: process.env.RAZOR_PAY_KEY_ID,
  key_secret: process.env.RAZOR_PAY_KEY_SECRET,
});

// Get available content creator subscription plans
exports.getContentPremiumPlans = async (req, res) => {
  try {
    // These are the plan IDs you created in your Razorpay dashboard for content creator premium
    const planIds = [
      "plan_QngKCqLAUkhxbH", // 12 month
      "plan_QngJ4kIVdFaRUI", // 6 month
      "plan_QngId7JYoii1wE", // 3 month
      "plan_QngI8gqKZrHLbK", // 1 month
    ];

    const plans = await Promise.all(
      planIds.map((planId) => razorpayInstance.plans.fetch(planId))
    );

    const formattedPlans = plans.map((plan) => ({
      id: plan.id,
      name: plan.item.name,
      amount: plan.item.amount / 100,
      currency: plan.item.currency,
      period: plan.period,
      interval: plan.interval,
      description: plan.item.description,
    })).sort((a, b) => a.amount - b.amount); // sort by price

    res.status(200).json({ status: true, plans: formattedPlans });
  } catch (error) {
    console.error("Error fetching content premium plans:", error);
    res.status(500).json({ status: false, message: "Error fetching plans" });
  }
};

// Create a new content creator subscription
exports.createContentPremiumSubscription = async (req, res) => {
  const { plan_id, user_id } = req.body;

  if (!plan_id || !user_id) {
    return res.status(400).json({ status: false, message: "plan_id and user_id are required" });
  }

  try {
    const subscription = await razorpayInstance.subscriptions.create({
      plan_id: plan_id,
      customer_notify: 1,
      total_count: 60, // Total number of billing cycles (e.g., 5 years for a monthly plan)
      notes: {
        user_id: user_id.toString(),
        subscription_type: 'content_creator_premium'
      }
    });

    // Use subscription.start_at or fallback to current time
    const startAt = subscription.start_at || Math.floor(Date.now() / 1000);
    
    // Save the initial subscription details to your database
    const query = `
      INSERT INTO content_creator_subscriptions (user_id, razorpay_plan_id, razorpay_subscription_id, status, total_count, start_at, customer_notify, notes)
      VALUES (?, ?, ?, ?, ?, FROM_UNIXTIME(?), ?, ?)
    `;
    await queryRunner(query, [
      user_id,
      plan_id,
      subscription.id,
      subscription.status,
      subscription.total_count,
      startAt,
      subscription.customer_notify,
      JSON.stringify({ subscription_type: 'content_creator_premium' })
    ]);

    console.log('Content Creator Premium - RAZOR_PAY_KEY_ID:', process.env.RAZOR_PAY_KEY_ID);

    res.status(201).json({ 
      status: true, 
      subscription_id: subscription.id, 
      key_id: process.env.RAZOR_PAY_KEY_ID 
    });

  } catch (error) {
    console.error("Error creating content creator subscription:", error);
    res.status(500).json({ 
      status: false, 
      message: "Error creating subscription", 
      error: error.message 
    });
  }
};

// Cancel a content creator subscription
exports.cancelContentPremiumSubscription = async (req, res) => {
  const { user_id } = req.body;

  if (!user_id) {
    return res.status(400).json({ status: false, message: "user_id is required" });
  }

  try {
    // Find the active subscription for the user
    const findQuery = "SELECT razorpay_subscription_id FROM content_creator_subscriptions WHERE user_id = ? AND status = 'active'";
    const subscriptions = await queryRunner(findQuery, [user_id]);

    if (subscriptions.length === 0) {
      return res.status(404).json({ 
        status: false, 
        message: "No active content creator subscription found for this user." 
      });
    }

    const subId = subscriptions[0].razorpay_subscription_id;

    // Cancel on Razorpay. Setting 'cancel_at_cycle_end' to true is generally better UX.
    await razorpayInstance.subscriptions.cancel(subId, { cancel_at_cycle_end: true });

    // The webhook will handle the DB update to 'cancelled' state when the cycle ends.
    // Or you can update it immediately if you prefer.
    const updateQuery = "UPDATE content_creator_subscriptions SET status = 'cancelled' WHERE razorpay_subscription_id = ?";
    await queryRunner(updateQuery, [subId]);

    // Also update user's premium status
    const updateUserQuery = "UPDATE users SET content_creator_premium_status = 'expired' WHERE id = ?";
    await queryRunner(updateUserQuery, [user_id]);

    res.status(200).json({ 
      status: true, 
      message: "Content creator subscription cancellation initiated. It will be fully cancelled at the end of the current billing cycle." 
    });

  } catch (error) {
    console.error("Error cancelling content creator subscription:", error);
    res.status(500).json({ 
      status: false, 
      message: "Error cancelling subscription", 
      error: error.message 
    });
  }
};

// Get user's content creator subscription status
exports.getContentPremiumStatus = async (req, res) => {
  const { userId } = req.params;
  if (!userId) {
    return res.status(400).json({ status: false, message: "User ID is required" });
  }

  try {
    const query = `
      SELECT 
        s.status, 
        s.current_end_at,
        s.razorpay_plan_id,
        s.razorpay_subscription_id,
        s.paid_count,
        s.total_count,
        s.start_at,
        s.ended_at,
        p.name as plan_name,
        p.description as plan_description,
        p.amount,
        p.currency,
        p.billing_cycle
      FROM content_creator_subscriptions s
      LEFT JOIN content_creator_razorpay_plans p ON s.razorpay_plan_id = p.id
      WHERE s.user_id = ?
      ORDER BY s.created_at DESC
      LIMIT 1
    `;
    const result = await queryRunner(query, [userId]);
    
    if (result.length > 0) {
      // Check if subscription is still active
      const subscription = result[0];
      const isActive = subscription.status === 'active' && 
                      subscription.current_end_at && 
                      new Date(subscription.current_end_at) > new Date();
      
      res.status(200).json({ 
        status: true, 
        data: {
          ...subscription,
          is_active: isActive
        }
      });
    } else {
      res.status(404).json({ status: false, message: "No content creator subscription found" });
    }
  } catch (error) {
    console.error("Error getting content creator subscription status:", error);
    res.status(500).json({ status: false, message: "Error fetching subscription status" });
  }
};

// Handle webhook for content creator subscriptions
exports.handleContentPremiumWebhook = async (req, res) => {
  const secret = process.env.RAZORPAY_WEBHOOK_SECRET;
  const signature = req.headers["x-razorpay-signature"];

  try {
    // Verify webhook signature
    const shasum = crypto.createHmac("sha256", secret);
    shasum.update(JSON.stringify(req.body));
    const digest = shasum.digest("hex");

    if (digest !== signature) {
      return res.status(400).json({ status: "Signature is not valid" });
    }

    const event = req.body.event;
    const payload = req.body.payload;

    if (event === "subscription.charged") {
      const sub = payload.subscription.entity;
      const payment = payload.payment.entity;

      // Check if this is a content creator subscription
      if (sub.notes && sub.notes.subscription_type === 'content_creator_premium') {
        // 1. Update your subscription table
        const updateQuery = `
          UPDATE content_creator_subscriptions 
          SET status = ?, paid_count = ?, current_start_at = FROM_UNIXTIME(?), current_end_at = FROM_UNIXTIME(?), charge_at = FROM_UNIXTIME(?)
          WHERE razorpay_subscription_id = ?
        `;
        await queryRunner(updateQuery, [
          sub.status,
          sub.paid_count,
          sub.current_start,
          sub.current_end,
          sub.charge_at,
          sub.id
        ]);

        // 2. Record the transaction
        const transactionQuery = `
          INSERT INTO content_creator_subscription_transactions (user_id, subscription_id, order_id, payment_id, amount, currency, status, transaction_for, plan_id)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        await queryRunner(transactionQuery, [
          sub.notes.user_id,
          sub.id,
          payment.order_id,
          payment.id,
          payment.amount / 100,
          payment.currency,
          'success',
          'content_creator_premium_subscription',
          sub.plan_id
        ]);

        // 3. Update user's premium status
        const updateUserQuery = `
          UPDATE users 
          SET content_creator_premium_status = 'active', 
              content_creator_premium_expires_at = FROM_UNIXTIME(?)
          WHERE id = ?
        `;
        await queryRunner(updateUserQuery, [sub.current_end, sub.notes.user_id]);
      }

    } else if (event === 'subscription.cancelled' || event === 'subscription.halted' || event === 'subscription.completed') {
      const sub = payload.subscription.entity;
      
      // Check if this is a content creator subscription
      if (sub.notes && sub.notes.subscription_type === 'content_creator_premium') {
        const updateQuery = `UPDATE content_creator_subscriptions SET status = ?, ended_at = FROM_UNIXTIME(?) WHERE razorpay_subscription_id = ?`;
        await queryRunner(updateQuery, [sub.status, sub.ended_at, sub.id]);
        
        // Update user's premium status to expired
        const updateUserQuery = "UPDATE users SET content_creator_premium_status = 'expired' WHERE id = ?";
        await queryRunner(updateUserQuery, [sub.notes.user_id]);
      }
    }

    res.status(200).json({ status: "ok" });

  } catch (error) {
    console.error("Content creator webhook handling error:", error);
    res.status(500).json({ status: "error" });
  }
};

// Legacy methods for backward compatibility
exports.getPlans = exports.getContentPremiumPlans;
exports.getUserPlanStatus = exports.getContentPremiumStatus;

// Legacy createOrder method (for backward compatibility)
exports.createOrder = async (req, res) => {
  try {
    const { plan_id } = req.body;
    const user_id = req.user.id;
    
    // Use the new subscription method instead
    const subscription = await razorpayInstance.subscriptions.create({
      plan_id: plan_id,
      customer_notify: 1,
      total_count: 60,
      notes: {
        user_id: user_id.toString(),
        subscription_type: 'content_creator_premium'
      }
    });

    res.json({ 
      status: true, 
      subscription_id: subscription.id,
      key_id: process.env.RAZOR_PAY_KEY_ID 
    });
  } catch (err) {
    res.status(500).json({ status: false, message: 'Error creating subscription', error: err.message });
  }
};

// Legacy payment callback (for backward compatibility)
exports.handlePaymentCallback = async (req, res) => {
  // This method is now handled by the webhook
  res.status(200).json({ status: true, message: 'Payment callback handled by webhook' });
}; 