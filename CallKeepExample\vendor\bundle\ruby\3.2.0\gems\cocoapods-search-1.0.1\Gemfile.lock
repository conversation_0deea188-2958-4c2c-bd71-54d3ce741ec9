GIT
  remote: https://github.com/CocoaPods/CocoaPods.git
  revision: 2e285ae6be8aadf0e6319a51626d5176c47e0ede
  branch: master
  specs:
    cocoapods (1.11.0.beta.2)
      addressable (~> 2.8)
      claide (>= 1.0.2, < 2.0)
      cocoapods-core (= 1.11.0.beta.2)
      cocoapods-deintegrate (>= 1.0.3, < 2.0)
      cocoapods-downloader (>= 1.4.0, < 2.0)
      cocoapods-plugins (>= 1.0.0, < 2.0)
      cocoapods-search (= 1.0.1)
      cocoapods-trunk (>= 1.4.0, < 2.0)
      cocoapods-try (>= 1.1.0, < 2.0)
      colored2 (~> 3.1)
      escape (~> 0.0.4)
      fourflusher (>= 2.3.0, < 3.0)
      gh_inspector (~> 1.0)
      molinillo (~> 0.8.0)
      nap (~> 1.0)
      ruby-macho (>= 1.0, < 3.0)
      xcodeproj (>= 1.21.0, < 2.0)

GIT
  remote: https://github.com/CocoaPods/Core.git
  revision: 0a0394afabd9c5f0838fc044e1c817024499dace
  branch: master
  specs:
    cocoapods-core (1.11.0.beta.2)
      activesupport (>= 5.0, < 7)
      addressable (~> 2.8)
      algoliasearch (~> 1.0)
      concurrent-ruby (~> 1.1)
      fuzzy_match (~> 2.0.4)
      nap (~> 1.0)
      netrc (~> 0.11)
      public_suffix (~> 4.0)
      typhoeus (~> 1.0)

PATH
  remote: .
  specs:
    cocoapods-search (1.0.1)

GEM
  remote: https://rubygems.org/
  specs:
    CFPropertyList (3.0.3)
    activesupport (6.1.4)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    addressable (2.8.0)
      public_suffix (>= 2.0.2, < 5.0)
    algoliasearch (1.27.5)
      httpclient (~> 2.8, >= 2.8.3)
      json (>= 1.5.1)
    atomos (0.1.3)
    bacon (1.2.0)
    claide (1.0.3)
    cocoapods-deintegrate (1.0.4)
    cocoapods-downloader (1.4.0)
    cocoapods-plugins (1.0.0)
      nap
    cocoapods-trunk (1.5.0)
      nap (>= 0.8, < 2.0)
      netrc (~> 0.11)
    cocoapods-try (1.2.0)
    colored2 (3.1.2)
    concurrent-ruby (1.1.9)
    escape (0.0.4)
    ethon (0.14.0)
      ffi (>= 1.15.0)
    ffi (1.15.3)
    fourflusher (2.3.1)
    fuzzy_match (2.0.4)
    gh_inspector (1.1.3)
    httpclient (2.8.3)
    i18n (1.8.10)
      concurrent-ruby (~> 1.0)
    json (2.5.1)
    minitest (5.14.4)
    mocha (1.13.0)
    mocha-on-bacon (0.2.3)
      mocha (>= 0.13.0)
    molinillo (0.8.0)
    nanaimo (0.3.0)
    nap (1.1.0)
    netrc (0.11.0)
    prettybacon (0.0.2)
      bacon (~> 1.2)
    public_suffix (4.0.6)
    rake (13.0.6)
    rexml (3.2.5)
    ruby-macho (2.5.1)
    typhoeus (1.4.0)
      ethon (>= 0.9.0)
    tzinfo (2.0.4)
      concurrent-ruby (~> 1.0)
    xcodeproj (1.21.0)
      CFPropertyList (>= 2.3.3, < 4.0)
      atomos (~> 0.1.3)
      claide (>= 1.0.2, < 2.0)
      colored2 (~> 3.1)
      nanaimo (~> 0.3.0)
      rexml (~> 3.2.4)
    zeitwerk (2.4.2)

PLATFORMS
  ruby

DEPENDENCIES
  bacon
  bundler (~> 1.3)
  cocoapods!
  cocoapods-core!
  cocoapods-search!
  mocha-on-bacon
  prettybacon
  rake

BUNDLED WITH
   1.17.3
