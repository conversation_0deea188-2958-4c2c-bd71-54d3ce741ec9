const WithdrawalService = require("../services/WithdrawalService");

module.exports = {
  // Get withdrawal settings
  getWithdrawalSettings: async (req, res) => {
    try {
      const result = await WithdrawalService.getWithdrawalSettings();
      res.status(result.status).json(result);
    } catch (error) {
      console.error("Error in getWithdrawalSettings:", error);
      res.status(500).json({
        status: 500,
        message: "Internal server error",
        data: {}
      });
    }
  },

  // Check withdrawal eligibility
  checkWithdrawalEligibility: async (req, res) => {
    try {
      const { userId, amount, withdrawalType } = req.body;

      if (!userId || !amount || !withdrawalType) {
        return res.status(400).json({
          status: 400,
          message: "userId, amount, and withdrawalType are required",
          eligible: false
        });
      }

      const result = await WithdrawalService.checkWithdrawalEligibility(userId, amount, withdrawalType);
      res.status(result.status).json(result);
    } catch (error) {
      console.error("Error in checkWithdrawalEligibility:", error);
      res.status(500).json({
        status: 500,
        message: "Internal server error",
        eligible: false
      });
    }
  },

  // Process wallet withdrawal
  processWalletWithdrawal: async (req, res) => {
    try {
      const {
        userId,
        amount,
        transactionMethod,
        bankName,
        accountNumber,
        ifscCode,
        mobileNumber,
        upiId
      } = req.body;

      // Validate required fields
      if (!userId || !amount || !transactionMethod) {
        return res.status(400).json({
          status: 400,
          message: "userId, amount, and transactionMethod are required"
        });
      }

      // Validate transaction method
      if (!['BANK', 'UPI'].includes(transactionMethod)) {
        return res.status(400).json({
          status: 400,
          message: "transactionMethod must be 'BANK' or 'UPI'"
        });
      }

      // Validate bank details for BANK method
      if (transactionMethod === 'BANK' && (!bankName || !accountNumber || !ifscCode)) {
        return res.status(400).json({
          status: 400,
          message: "bankName, accountNumber, and ifscCode are required for BANK method"
        });
      }

      // Validate UPI details for UPI method
      if (transactionMethod === 'UPI' && (!mobileNumber || !upiId)) {
        return res.status(400).json({
          status: 400,
          message: "mobileNumber and upiId are required for UPI method"
        });
      }

      const result = await WithdrawalService.processWalletWithdrawal({
        userId,
        amount: parseFloat(amount),
        transactionMethod,
        bankName,
        accountNumber,
        ifscCode,
        mobileNumber,
        upiId
      });

      res.status(result.status).json(result);
    } catch (error) {
      console.error("Error in processWalletWithdrawal:", error);
      res.status(500).json({
        status: 500,
        message: "Internal server error"
      });
    }
  },

  // Process referral withdrawal
  processReferralWithdrawal: async (req, res) => {
    try {
      const {
        userId,
        amount,
        withdrawalType,
        bankName,
        bankIfsc,
        bankAccountNumber,
        upiId
      } = req.body;

      // Validate required fields
      if (!userId || !amount || !withdrawalType) {
        return res.status(400).json({
          status: 400,
          message: "userId, amount, and withdrawalType are required"
        });
      }

      // Validate withdrawal type
      if (!['referral', 'coupon'].includes(withdrawalType)) {
        return res.status(400).json({
          status: 400,
          message: "withdrawalType must be 'referral' or 'coupon'"
        });
      }

      // Validate bank details (at least one payment method must be provided)
      if (!bankName && !upiId) {
        return res.status(400).json({
          status: 400,
          message: "Either bank details or UPI ID must be provided"
        });
      }

      // If bank details provided, validate all required fields
      if (bankName && (!bankIfsc || !bankAccountNumber)) {
        return res.status(400).json({
          status: 400,
          message: "bankIfsc and bankAccountNumber are required when bankName is provided"
        });
      }

      const result = await WithdrawalService.processReferralWithdrawal({
        userId,
        amount: parseFloat(amount),
        withdrawalType,
        bankName,
        bankIfsc,
        bankAccountNumber,
        upiId
      });

      res.status(result.status).json(result);
    } catch (error) {
      console.error("Error in processReferralWithdrawal:", error);
      res.status(500).json({
        status: 500,
        message: "Internal server error"
      });
    }
  },

  // Process channel withdrawal
  processChannelWithdrawal: async (req, res) => {
    try {
      const {
        userId,
        channelId,
        amount,
        withdrawalType,
        bankName,
        bankIfsc,
        bankAccountNumber,
        upiId
      } = req.body;

      // Validate required fields
      if (!userId || !amount || !withdrawalType) {
        return res.status(400).json({
          status: 400,
          message: "userId, amount, and withdrawalType are required"
        });
      }

      // Validate withdrawal type
      if (!['creator_referral', 'content_earnings'].includes(withdrawalType)) {
        return res.status(400).json({
          status: 400,
          message: "withdrawalType must be 'creator_referral' or 'content_earnings'"
        });
      }

      // Validate bank details (at least one payment method must be provided)
      if (!bankName && !upiId) {
        return res.status(400).json({
          status: 400,
          message: "Either bank details or UPI ID must be provided"
        });
      }

      // If bank details provided, validate all required fields
      if (bankName && (!bankIfsc || !bankAccountNumber)) {
        return res.status(400).json({
          status: 400,
          message: "bankIfsc and bankAccountNumber are required when bankName is provided"
        });
      }

      const result = await WithdrawalService.processChannelWithdrawal({
        userId,
        channelId,
        amount: parseFloat(amount),
        withdrawalType,
        bankName,
        bankIfsc,
        bankAccountNumber,
        upiId
      });

      res.status(result.status).json(result);
    } catch (error) {
      console.error("Error in processChannelWithdrawal:", error);
      res.status(500).json({
        status: 500,
        message: "Internal server error"
      });
    }
  },

  // Get withdrawal history
  getWithdrawalHistory: async (req, res) => {
    try {
      const { userId } = req.params;
      const { type = 'all', page = 1, limit = 10 } = req.query;

      if (!userId) {
        return res.status(400).json({
          status: 400,
          message: "userId is required"
        });
      }

      const result = await WithdrawalService.getWithdrawalHistory(
        parseInt(userId),
        type,
        parseInt(page),
        parseInt(limit)
      );

      res.status(result.status).json(result);
    } catch (error) {
      console.error("Error in getWithdrawalHistory:", error);
      res.status(500).json({
        status: 500,
        message: "Internal server error",
        data: []
      });
    }
  },

  // Get withdrawal statistics
  getWithdrawalStats: async (req, res) => {
    try {
      const { userId } = req.params;

      if (!userId) {
        return res.status(400).json({
          status: 400,
          message: "userId is required"
        });
      }

      const result = await WithdrawalService.getWithdrawalStats(parseInt(userId));
      res.status(result.status).json(result);
    } catch (error) {
      console.error("Error in getWithdrawalStats:", error);
      res.status(500).json({
        status: 500,
        message: "Internal server error",
        data: {}
      });
    }
  }
}; 