-- User Premium Subscription Tables
-- This table will only store successful payments, no pre-creation

-- Table to store user subscription details (only successful payments)
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    razorpay_plan_id VARCHAR(255) NOT NULL,
    razorpay_subscription_id VARCHAR(255) NOT NULL UNIQUE,
    status ENUM('active', 'cancelled', 'expired', 'completed') DEFAULT 'active',
    total_count INT DEFAULT 60, -- Total number of billing cycles
    paid_count INT DEFAULT 1, -- Number of successful payments (starts at 1 for successful payment)
    current_start_at TIMESTAMP NULL, -- Start of current billing cycle
    current_end_at TIMESTAMP NULL, -- End of current billing cycle
    charge_at TIMESTAMP NULL, -- When the subscription was last charged
    start_at TIMESTAMP NULL, -- When subscription started
    ended_at TIMESTAMP NULL, -- When subscription ended
    customer_notify TINYINT DEFAULT 1,
    notes TEXT, -- Additional notes/metadata
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_subscription_id (razorpay_subscription_id),
    INDEX idx_status (status),
    INDEX idx_current_end_at (current_end_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Table to store Razorpay plan details (for caching)
CREATE TABLE IF NOT EXISTS razorpay_plans (
    id VARCHAR(255) PRIMARY KEY, -- Razorpay plan ID
    name VARCHAR(255) NOT NULL,
    description TEXT,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) DEFAULT 'INR',
    period INT NOT NULL, -- Number of periods
    plan_interval VARCHAR(20) NOT NULL, -- 'monthly', 'yearly', etc.
    billing_cycle VARCHAR(50), -- Human readable: '1 month', '3 months', etc.
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert the 3 user premium plans
INSERT INTO razorpay_plans (id, name, description, amount, currency, period, plan_interval, billing_cycle) VALUES
('plan_Qjrw31WPrhunxz', 'Premium - 1 Month', 'Access to premium features for 1 month', 200.00, 'INR', 1, 'monthly', '1 month'),
('plan_Qjrx1d0dlYu8dI', 'Premium - 6 Months', 'Access to premium features for 6 months', 1200.00, 'INR', 6, 'monthly', '6 months'),
('plan_QjrxzWRWoYC5bl', 'Premium - 12 Months', 'Access to premium features for 12 months', 2400.00, 'INR', 12, 'monthly', '12 months')
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    description = VALUES(description),
    amount = VALUES(amount),
    period = VALUES(period),
    plan_interval = VALUES(plan_interval),
    billing_cycle = VALUES(billing_cycle),
    updated_at = CURRENT_TIMESTAMP;

-- Add a column to users table to track premium status (if not exists)
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS premium_expires_at TIMESTAMP NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_premium_status ON users(is_premium);
CREATE INDEX IF NOT EXISTS idx_user_premium_expires_at ON users(premium_expires_at); 