# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/Desktop/CallKeepExample/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:50 (file)
# input_SRC at /Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:55 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/Desktop/CallKeepExample/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/Desktop/CallKeepExample/android/app/.cxx/Debug/4n3y682j/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()
