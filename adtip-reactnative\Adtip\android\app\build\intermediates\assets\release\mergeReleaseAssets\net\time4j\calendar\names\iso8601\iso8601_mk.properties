# months
M(a)_1=јан.
M(a)_2=фев.
M(a)_3=мар.
M(a)_4=апр.
M(a)_5=мај
M(a)_6=јун.
M(a)_7=јул.
M(a)_8=авг.
M(a)_9=септ.
M(a)_10=окт.
M(a)_11=ноем.
M(a)_12=дек.

M(n)_1=ј
M(n)_2=ф
M(n)_3=м
M(n)_4=а
M(n)_5=м
M(n)_6=ј
M(n)_7=ј
M(n)_8=а
M(n)_9=с
M(n)_10=о
M(n)_11=н
M(n)_12=д

M(w)_1=јануари
M(w)_2=февруари
M(w)_3=март
M(w)_4=април
M(w)_5=мај
M(w)_6=јуни
M(w)_7=јули
M(w)_8=август
M(w)_9=септември
M(w)_10=октомври
M(w)_11=ноември
M(w)_12=декември

M(A)_1=јан.
M(A)_2=фев.
M(A)_3=мар.
M(A)_4=апр.
M(A)_5=мај
M(A)_6=јун.
M(A)_7=јул.
M(A)_8=авг.
M(A)_9=септ.
M(A)_10=окт.
M(A)_11=ноем.
M(A)_12=дек.

M(N)_1=ј
M(N)_2=ф
M(N)_3=м
M(N)_4=а
M(N)_5=м
M(N)_6=ј
M(N)_7=ј
M(N)_8=а
M(N)_9=с
M(N)_10=о
M(N)_11=н
M(N)_12=д

M(W)_1=јануари
M(W)_2=февруари
M(W)_3=март
M(W)_4=април
M(W)_5=мај
M(W)_6=јуни
M(W)_7=јули
M(W)_8=август
M(W)_9=септември
M(W)_10=октомври
M(W)_11=ноември
M(W)_12=декември

# weekdays
D(a)_1=пон.
D(a)_2=вт.
D(a)_3=сре.
D(a)_4=чет.
D(a)_5=пет.
D(a)_6=саб.
D(a)_7=нед.

D(n)_1=п
D(n)_2=в
D(n)_3=с
D(n)_4=ч
D(n)_5=п
D(n)_6=с
D(n)_7=н

D(s)_1=пон.
D(s)_2=вто.
D(s)_3=сре.
D(s)_4=чет.
D(s)_5=пет.
D(s)_6=саб.
D(s)_7=нед.

D(w)_1=понеделник
D(w)_2=вторник
D(w)_3=среда
D(w)_4=четврток
D(w)_5=петок
D(w)_6=сабота
D(w)_7=недела

D(A)_1=пон.
D(A)_2=вто.
D(A)_3=сре.
D(A)_4=чет.
D(A)_5=пет.
D(A)_6=саб.
D(A)_7=нед.

D(N)_1=п
D(N)_2=в
D(N)_3=с
D(N)_4=ч
D(N)_5=п
D(N)_6=с
D(N)_7=н

D(S)_1=пон.
D(S)_2=вто.
D(S)_3=сре.
D(S)_4=чет.
D(S)_5=пет.
D(S)_6=саб.
D(S)_7=нед.

D(W)_1=понеделник
D(W)_2=вторник
D(W)_3=среда
D(W)_4=четврток
D(W)_5=петок
D(W)_6=сабота
D(W)_7=недела

# quarters
Q(a)_1=јан-мар
Q(a)_2=апр-јун
Q(a)_3=јул-сеп
Q(a)_4=окт-дек

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=прво тромесечје
Q(w)_2=второ тромесечје
Q(w)_3=трето тромесечје
Q(w)_4=четврто тромесечје

Q(A)_1=јан-мар
Q(A)_2=апр-јун
Q(A)_3=јул-сеп
Q(A)_4=окт-дек

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=прво тромесечје
Q(W)_2=второ тромесечје
Q(W)_3=трето тромесечје
Q(W)_4=четврто тромесечје

# day-period-rules
T0000=night1
T0400=morning1
T1000=morning2
T1200=afternoon1
T1800=evening1

# day-period-translations
P(a)_midnight=полноќ
P(a)_am=претпл.
P(a)_noon=напладне
P(a)_pm=попл.
P(a)_morning1=наутро
P(a)_morning2=претпл.
P(a)_afternoon1=попл.
P(a)_evening1=навечер
P(a)_night1=ноќе

P(n)_midnight=полн.
P(n)_am=претпл.
P(n)_noon=напл.
P(n)_pm=попл.
P(n)_morning1=утро
P(n)_morning2=претпл.
P(n)_afternoon1=попл.
P(n)_evening1=веч.
P(n)_night1=ноќе

P(w)_midnight=полноќ
P(w)_am=претпладне
P(w)_noon=напладне
P(w)_pm=попладне
P(w)_morning1=наутро
P(w)_morning2=претпладне
P(w)_afternoon1=попладне
P(w)_evening1=навечер
P(w)_night1=по полноќ

P(A)_midnight=полноќ
P(A)_am=претпл.
P(A)_noon=напладне
P(A)_pm=попл.
P(A)_morning1=наутро
P(A)_morning2=претпл.
P(A)_afternoon1=попл.
P(A)_evening1=навечер
P(A)_night1=по полноќ

P(N)_midnight=полноќ
P(N)_am=претпл.
P(N)_noon=пладне
P(N)_pm=попл.
P(N)_morning1=наутро
P(N)_morning2=претпл.
P(N)_afternoon1=попл.
P(N)_evening1=навечер
P(N)_night1=по полноќ

P(W)_midnight=на полноќ
P(W)_am=претпладне
P(W)_noon=напладне
P(W)_pm=попладне
P(W)_morning1=наутро
P(W)_morning2=претпладне
P(W)_afternoon1=попладне
P(W)_evening1=навечер
P(W)_night1=по полноќ

# eras
E(w)_0=пред нашата ера
E(w|alt)_0=пр. н.е.
E(w)_1=од нашата ера
E(w|alt)_1=нашата ера

E(a)_0=пр.н.е.
E(a|alt)_0=п.н.е.
E(a)_1=н.е.
E(a|alt)_1=CE

E(n)_0=пр.н.е.
E(n)_1=н.е.

# format patterns
F(f)_d=EEEE, dd MMMM y
F(l)_d=dd MMMM y
F(m)_d=dd.M.y
F(s)_d=dd.M.yy

F(alt)=HH:mm:ss

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} {0}
F(l)_dt={1} {0}
F(m)_dt={1} {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=HH
F_hm=h:mm a
F_Hm=HH:mm
F_hms=h:mm:ss a
F_Hms=HH:mm:ss

F_Md=d.M
F_MMMd=d MMM
F_MMMMd=d MMMM
F_y=y
F_yM=M.y
F_yMMM=MMM y 'г'.
F_yMMMM=MMMM y 'г'.
F_yQQQ=QQQ y 'г'.
F_yQQQQ=QQQQ y 'г'.
F_yw=w 'седмица' 'од' Y

I={0} - {1}

# labels of elements
L_era=ера
L_year=година
L_quarter=тромесечје
L_month=месец
L_week=седмица
L_day=ден
L_weekday=ден од неделата
L_dayperiod=претпладне/попладне
L_hour=час
L_minute=минута
L_second=секунда
L_zone=временска зона
