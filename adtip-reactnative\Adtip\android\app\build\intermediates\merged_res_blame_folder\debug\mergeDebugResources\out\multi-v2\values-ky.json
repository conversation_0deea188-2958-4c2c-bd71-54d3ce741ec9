{"logs": [{"outputFile": "com.adtip.app.adtip_app-mergeDebugResources-4:/values-ky/values-ky.xml", "map": [{"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\0cb78797bbebcc5583f5b3648f5d6ccd\\transformed\\browser-1.8.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,259,368", "endColumns": "98,104,108,105", "endOffsets": "149,254,363,469"}, "to": {"startLines": "86,160,161,162", "startColumns": "4,4,4,4", "startOffsets": "7706,13664,13769,13878", "endColumns": "98,104,108,105", "endOffsets": "7800,13764,13873,13979"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dd11809084d63593099820b405d61701\\transformed\\material-1.12.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1129,1193,1287,1357,1418,1505,1568,1632,1691,1765,1827,1881,1998,2056,2117,2171,2245,2367,2451,2530,2630,2716,2812,2944,3022,3100,3229,3318,3398,3459,3514,3580,3649,3726,3797,3878,3952,4028,4118,4191,4293,4378,4457,4547,4639,4713,4798,4888,4940,5024,5089,5174,5259,5321,5385,5448,5517,5634,5742,5842,5946,6011,6070,6152,6238,6314", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "260,343,428,513,628,738,839,980,1064,1124,1188,1282,1352,1413,1500,1563,1627,1686,1760,1822,1876,1993,2051,2112,2166,2240,2362,2446,2525,2625,2711,2807,2939,3017,3095,3224,3313,3393,3454,3509,3575,3644,3721,3792,3873,3947,4023,4113,4186,4288,4373,4452,4542,4634,4708,4793,4883,4935,5019,5084,5169,5254,5316,5380,5443,5512,5629,5737,5837,5941,6006,6065,6147,6233,6309,6392"}, "to": {"startLines": "19,52,53,54,55,56,64,65,66,88,89,159,163,166,168,169,170,171,172,173,174,175,176,177,178,179,180,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,247,256,257,258", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "771,3881,3964,4049,4134,4249,5088,5189,5330,7879,7939,13570,13984,14206,14337,14424,14487,14551,14610,14684,14746,14800,14917,14975,15036,15090,15164,15843,15927,16006,16106,16192,16288,16420,16498,16576,16705,16794,16874,16935,16990,17056,17125,17202,17273,17354,17428,17504,17594,17667,17769,17854,17933,18023,18115,18189,18274,18364,18416,18500,18565,18650,18735,18797,18861,18924,18993,19110,19218,19318,19422,19487,20778,21329,21415,21491", "endLines": "22,52,53,54,55,56,64,65,66,88,89,159,163,166,168,169,170,171,172,173,174,175,176,177,178,179,180,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,247,256,257,258", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "931,3959,4044,4129,4244,4354,5184,5325,5409,7934,7998,13659,14049,14262,14419,14482,14546,14605,14679,14741,14795,14912,14970,15031,15085,15159,15281,15922,16001,16101,16187,16283,16415,16493,16571,16700,16789,16869,16930,16985,17051,17120,17197,17268,17349,17423,17499,17589,17662,17764,17849,17928,18018,18110,18184,18269,18359,18411,18495,18560,18645,18730,18792,18856,18919,18988,19105,19213,19313,19417,19482,19541,20855,21410,21486,21569"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8e1703580f38993d5096d25ba35ecdf1\\transformed\\jetified-media3-ui-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "59", "startColumns": "4", "startOffsets": "3770", "endColumns": "66", "endOffsets": "3832"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "13503", "endColumns": "66", "endOffsets": "13565"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,143,212,295,365,432,504", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "138,207,290,360,427,499,571"}, "to": {"startLines": "67,164,165,167,187,259,260", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5414,14054,14123,14267,15776,21574,21646", "endColumns": "87,68,82,69,66,71,71", "endOffsets": "5497,14118,14201,14332,15838,21641,21713"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\ef18ad19ff26599d64ec0eff4ea7dc70\\transformed\\appcompat-1.7.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,255", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "936,1047,1156,1268,1353,1458,1575,1654,1732,1823,1916,2011,2105,2205,2298,2393,2488,2579,2670,2751,2857,2962,3060,3167,3270,3385,3546,21247", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "1042,1151,1263,1348,1453,1570,1649,1727,1818,1911,2006,2100,2200,2293,2388,2483,2574,2665,2746,2852,2957,3055,3162,3265,3380,3541,3643,21324"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\c9fc1356237c73a36541687fd82ee2b5\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,291,353,424,492,601,666,796,910,1053,1107,1166,1278,1375,1417,1492,1530,1568,1618,1683,1734", "endColumns": "41,49,61,70,67,108,64,129,113,142,53,58,111,96,41,74,37,37,49,64,50,55", "endOffsets": "240,290,352,423,491,600,665,795,909,1052,1106,1165,1277,1374,1416,1491,1529,1567,1617,1682,1733,1789"}, "to": {"startLines": "233,234,235,236,237,238,239,240,241,242,243,244,245,246,248,249,250,251,252,253,254,263", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19546,19592,19646,19712,19787,19859,19972,20041,20175,20293,20440,20498,20561,20677,20860,20906,20985,21027,21069,21123,21192,21904", "endColumns": "45,53,65,74,71,112,68,133,117,146,57,62,115,100,45,78,41,41,53,68,54,59", "endOffsets": "19587,19641,19707,19782,19854,19967,20036,20170,20288,20435,20493,20556,20672,20773,20901,20980,21022,21064,21118,21187,21242,21959"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6a4b12df2937b548059e098326cd7bcc\\transformed\\jetified-play-services-wallet-18.1.3\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "80", "endOffsets": "282"}, "to": {"startLines": "262", "startColumns": "4", "startOffsets": "21819", "endColumns": "84", "endOffsets": "21899"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\31cc88bd1a811e01477d23dfca8d3955\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "68,69,70,71,72,73,74,75,77,78,79,80,81,82,83,84,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5502,5608,5757,5886,5993,6138,6265,6380,6647,6816,6923,7073,7203,7340,7504,7568,7628", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "5603,5752,5881,5988,6133,6260,6375,6484,6811,6918,7068,7198,7335,7499,7563,7623,7701"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\f9b6829f7f71dcf7ed8c3a43ef2febe5\\transformed\\jetified-exoplayer-ui-2.19.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,676,768,858,936,1026,1123,1210,1276,1373,1471,1539,1605,1670,1740,1871,2000,2136,2208,2289,2363,2451,2545,2636,2703,2769,2822,2883,2931,2992,3065,3141,3201,3271,3329,3386,3452,3517,3583,3635,3694,3770,3846", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "280,479,671,763,853,931,1021,1118,1205,1271,1368,1466,1534,1600,1665,1735,1866,1995,2131,2203,2284,2358,2446,2540,2631,2698,2764,2817,2878,2926,2987,3060,3136,3196,3266,3324,3381,3447,3512,3578,3630,3689,3765,3841,3896"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,579,9590,9682,9772,9850,9940,10037,10124,10190,10287,10385,10453,10519,10584,10654,10785,10914,11050,11122,11203,11277,11365,11459,11550,11617,12371,12424,12485,12533,12594,12667,12743,12803,12873,12931,12988,13054,13119,13185,13237,13296,13372,13448", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "375,574,766,9677,9767,9845,9935,10032,10119,10185,10282,10380,10448,10514,10579,10649,10780,10909,11045,11117,11198,11272,11360,11454,11545,11612,11678,12419,12480,12528,12589,12662,12738,12798,12868,12926,12983,13049,13114,13180,13232,13291,13367,13443,13498"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\e4f7aaf1dd3251b595a6d29fbf072499\\transformed\\core-1.16.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "57,58,59,60,61,62,63,261", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4359,4459,4561,4664,4771,4873,4977,21718", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "4454,4556,4659,4766,4868,4972,5083,21814"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\91b5c4a59b6d6a7985119f29ba44ddae\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11683,11754,11819,11890,11961,12048,12119,12206,12290", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "11749,11814,11885,11956,12043,12114,12201,12285,12366"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\dfbd6dfbd7eb9930845c16c988ee8525\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,122", "endOffsets": "160,283"}, "to": {"startLines": "50,51", "startColumns": "4,4", "startOffsets": "3648,3758", "endColumns": "109,122", "endOffsets": "3753,3876"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\9eda951e918e947e7d9ddd1ba9b2c563\\transformed\\jetified-media3-session-1.4.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,247,329,419,508,600,679,787,885,970,1039,1148,1235,1328,1421,1535,1615,1716,1787,1856,1937,2022,2112", "endColumns": "73,117,81,89,88,91,78,107,97,84,68,108,86,92,92,113,79,100,70,68,80,84,89,93", "endOffsets": "124,242,324,414,503,595,674,782,880,965,1034,1143,1230,1323,1416,1530,1610,1711,1782,1851,1932,2017,2107,2201"}, "to": {"startLines": "87,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,181,182,183,184,185,186", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7805,8003,8121,8203,8293,8382,8474,8553,8661,8759,8844,8913,9022,9109,9202,9295,9409,9489,15286,15357,15426,15507,15592,15682", "endColumns": "73,117,81,89,88,91,78,107,97,84,68,108,86,92,92,113,79,100,70,68,80,84,89,93", "endOffsets": "7874,8116,8198,8288,8377,8469,8548,8656,8754,8839,8908,9017,9104,9197,9290,9404,9484,9585,15352,15421,15502,15587,15677,15771"}}, {"source": "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\6ee598c7bab1dd2859d5d01f2229eaf5\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "76", "startColumns": "4", "startOffsets": "6489", "endColumns": "157", "endOffsets": "6642"}}]}]}