// Rectangle Advertisement Component for Web Application
// Medium Rectangle (MREC) ad implementation

import React, { useEffect, useRef, useState, memo } from 'react';
import { trackAdLoaded, trackAdFailed, trackAdImpression, trackAdClicked } from './AdTracker';
import { logDebug, logError } from '../../utils/ProductionLogger';

interface RectangleAdProps {
  adUnitId?: string;
  className?: string;
  style?: React.CSSProperties;
  onAdLoaded?: () => void;
  onAdFailed?: (error: any) => void;
  onAdClicked?: () => void;
}

// Medium Rectangle ad size (300x250)
const AD_SIZE = { width: 300, height: 250 };

// Test ad unit ID (replace with real one in production)
const TEST_AD_UNIT = 'ca-app-pub-3940256099942544/6300978111';

const RectangleAdComponent = memo(({
  adUnitId,
  className = '',
  style,
  onAdLoaded,
  onAdFailed,
  onAdClicked,
}: RectangleAdProps) => {
  const adRef = useRef<HTMLDivElement>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [adId] = useState(() => `rectangle-ad-${Math.random().toString(36).substr(2, 9)}`);

  // Use test ad unit if none provided
  const currentAdUnitId = adUnitId || TEST_AD_UNIT;

  useEffect(() => {
    loadAd();
    
    // Set up intersection observer for viewability tracking
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !isVisible) {
            setIsVisible(true);
            trackAdImpression('rectangle', currentAdUnitId, {
              viewabilityThreshold: entry.intersectionRatio,
            });
            logDebug('RectangleAd', 'Ad impression tracked', { adUnitId: currentAdUnitId });
          }
        });
      },
      { threshold: 0.5 } // Track when 50% of ad is visible
    );

    if (adRef.current) {
      observer.observe(adRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [currentAdUnitId, isVisible]);

  const loadAd = async () => {
    try {
      logDebug('RectangleAd', 'Loading rectangle ad', { adUnitId: currentAdUnitId });

      // Check if Google AdSense is available
      if (typeof window !== 'undefined' && (window as any).adsbygoogle) {
        // Initialize AdSense ad
        try {
          // Check if this ad slot already has an ad
          const adElement = document.getElementById(adId);
          if (adElement && !adElement.getAttribute('data-ad-status')) {
            ((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
          }

          setIsLoaded(true);
          setError(null);

          trackAdLoaded('rectangle', currentAdUnitId);
          onAdLoaded?.();

          logDebug('RectangleAd', 'Rectangle ad loaded successfully', { adUnitId: currentAdUnitId, adId });
        } catch (adError) {
          throw new Error(`AdSense initialization failed: ${adError}`);
        }
      } else {
        // Fallback: Load AdSense script if not available
        await loadAdSenseScript();
        // Retry loading after script is loaded
        setTimeout(() => loadAd(), 1000);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logError('RectangleAd', 'Failed to load rectangle ad', error as Error);
      
      setError(errorMessage);
      setIsLoaded(false);
      
      trackAdFailed('rectangle', error, currentAdUnitId);
      onAdFailed?.(error);
    }
  };

  const loadAdSenseScript = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      // Check if script already exists
      if (document.querySelector('script[src*="adsbygoogle.js"]')) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.async = true;
      script.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js';
      script.crossOrigin = 'anonymous';
      
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load AdSense script'));
      
      document.head.appendChild(script);
    });
  };

  const handleAdClick = () => {
    trackAdClicked('rectangle', currentAdUnitId);
    onAdClicked?.();
    logDebug('RectangleAd', 'Rectangle ad clicked', { adUnitId: currentAdUnitId });
  };

  // Render placeholder if in development mode or if ad fails to load
  const renderPlaceholder = () => (
    <div
      className={`bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center text-gray-500 text-sm ${className}`}
      style={{
        width: AD_SIZE.width,
        height: AD_SIZE.height,
        ...style,
      }}
    >
      {import.meta.env.DEV ? (
        <div className="text-center">
          <div>Rectangle Ad Placeholder</div>
          <div className="text-xs mt-1">{AD_SIZE.width}x{AD_SIZE.height}</div>
          {error && <div className="text-red-500 text-xs mt-1">Error: {error}</div>}
        </div>
      ) : (
        <div className="text-center">
          <div>Advertisement</div>
          <div className="text-xs mt-1">300x250</div>
        </div>
      )}
    </div>
  );

  // In development, always show placeholder
  if (import.meta.env.DEV) {
    return (
      <div ref={adRef} onClick={handleAdClick} className="flex justify-center">
        {renderPlaceholder()}
      </div>
    );
  }

  // In production, show real ad or placeholder on error
  return (
    <div
      ref={adRef}
      className={`rectangle-ad-container flex justify-center ${className}`}
      style={style}
      onClick={handleAdClick}
    >
      {error ? (
        renderPlaceholder()
      ) : (
        <ins
          id={adId}
          className="adsbygoogle"
          style={{
            display: 'inline-block',
            width: AD_SIZE.width,
            height: AD_SIZE.height,
          }}
          data-ad-client={import.meta.env.VITE_GOOGLE_ADSENSE_CLIENT_ID || 'ca-app-pub-3940256099942544'}
          data-ad-slot={currentAdUnitId}
          data-ad-format="rectangle"
        />
      )}
    </div>
  );
});

RectangleAdComponent.displayName = 'RectangleAdComponent';

export default RectangleAdComponent;
