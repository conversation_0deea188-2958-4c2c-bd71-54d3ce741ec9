{"cells": [{"cell_type": "code", "execution_count": null, "id": "28c81f58", "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["mysql> show databases;\n", "+--------------------+\n", "| Database           |\n", "+--------------------+\n", "| adtip_qa           |\n", "| information_schema |\n", "| mysql              |\n", "| performance_schema |\n", "| sys                |\n", "+--------------------+\n", "5 rows in set (0.00 sec)\n", "\n", "mysql> use adtip_qa;\n", "Reading table information for completion of table and column names\n", "You can turn off this feature to get a quicker startup with -A\n", "\n", "Database changed\n", "mysql> show tables;\n", "+--------------------------------------+\n", "| Tables_in_adtip_qa                   |\n", "+--------------------------------------+\n", "| ad_details                           |\n", "| admin                                |\n", "| admin_access_master                  |\n", "| admodels                             |\n", "| admodels_master                      |\n", "| application_users                    |\n", "| bargain_product                      |\n", "| blocked_users                        |\n", "| call_transactions                    |\n", "| cart_item                            |\n", "| channel_content_creator_transactions |\n", "| channel_content_creator_wallet       |\n", "| channel_details                      |\n", "| channel_details_followers            |\n", "| channel_withdraw_request             |\n", "| channels                             |\n", "| channels_new                         |\n", "| comment_details                      |\n", "| company                              |\n", "| company_details                      |\n", "| company_post                         |\n", "| coupon_master                        |\n", "| coupons                              |\n", "| delivery_address                     |\n", "| employee                             |\n", "| error_logs                           |\n", "| gender                               |\n", "| industry_master                      |\n", "| interest                             |\n", "| languages                            |\n", "| media_files                          |\n", "| mobile_notifications                 |\n", "| notifications                        |\n", "| orders                               |\n", "| orders_item                          |\n", "| orders_products                      |\n", "| playlist                             |\n", "| popular_products                     |\n", "| post_promotions                      |\n", "| post_target_gender                   |\n", "| post_target_locations                |\n", "| posts                                |\n", "| product                              |\n", "| product_bargain                      |\n", "| product_catergory                    |\n", "| product_details                      |\n", "| product_orders                       |\n", "| product_search_history               |\n", "| qr_scan_web                          |\n", "| reel_details_like                    |\n", "| reels                                |\n", "| reels_details                        |\n", "| referal_details                      |\n", "| referral_earnings_details            |\n", "| req_demo                             |\n", "| reqeuest_ad_demo                     |\n", "| status                               |\n", "| status_viewers                       |\n", "| subscription_plans                   |\n", "| subscription_premiums                |\n", "| transaction                          |\n", "| transactions                         |\n", "| user_address                         |\n", "| user_bargain                         |\n", "| user_calls                           |\n", "| user_chat                            |\n", "| user_chat_details                    |\n", "| user_coupon                          |\n", "| user_coupon_wallet                   |\n", "| user_coupon_wallet_transactions      |\n", "| user_details                         |\n", "| user_followers                       |\n", "| user_interests                       |\n", "| user_languages                       |\n", "| user_post_comments                   |\n", "| user_post_likes                      |\n", "| user_premium_plans                   |\n", "| user_refer_wallet                    |\n", "| user_refer_wallet_transactions       |\n", "| user_referral_wallet                 |\n", "| user_referral_wallet_transactions    |\n", "| user_referrals                       |\n", "| user_requests                        |\n", "| user_social_links                    |\n", "| users                                |\n", "| video_categories                     |\n", "| video_comments                       |\n", "| video_master                         |\n", "| wallet                               |\n", "| website_message                      |\n", "| wishlist                             |\n", "+--------------------------------------+"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}