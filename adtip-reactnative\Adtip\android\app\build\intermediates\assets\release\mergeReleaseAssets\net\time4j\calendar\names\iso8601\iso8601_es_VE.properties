# months
M(a)_1=ene.
M(a)_2=feb.
M(a)_3=mar.
M(a)_4=abr.
M(a)_5=may.
M(a)_6=jun.
M(a)_7=jul.
M(a)_8=ago.
M(a)_9=sept.
M(a)_10=oct.
M(a)_11=nov.
M(a)_12=dic.

M(A)_1=ene.
M(A)_2=feb.
M(A)_3=mar.
M(A)_4=abr.
M(A)_5=may.
M(A)_6=jun.
M(A)_7=jul.
M(A)_8=ago.
M(A)_9=sept.
M(A)_10=oct.
M(A)_11=nov.
M(A)_12=dic.

# weekdays
D(s)_1=Lu
D(s)_2=Ma
D(s)_3=Mi
D(s)_4=Ju
D(s)_5=Vi
D(s)_6=Sa
D(s)_7=Do

D(S)_1=Lu
D(S)_2=Ma
D(S)_3=Mi
D(S)_4=Ju
D(S)_5=Vi
D(S)_6=Sa
D(S)_7=Do

# quarters
Q(w)_1=1er trimestre
Q(w)_2=2do trimestre
Q(w)_3=3er trimestre
Q(w)_4=4to trimestre

Q(W)_1=1er trimestre
Q(W)_2=2do trimestre
Q(W)_3=3er trimestre
Q(W)_4=4to trimestre

# day-period-translations
P(a)_am=a. m.
P(a)_pm=p. m.

P(w)_am=a. m.
P(w)_pm=p. m.

P(A)_am=a. m.
P(A)_pm=p. m.

P(N)_am=a. m.
P(N)_noon=m.
P(N)_pm=p. m.

P(W)_am=a. m.
P(W)_pm=p. m.

# format patterns
F(alt)=hh:mm:ss a

F(f)_t=h:mm:ss a zzzz
F(l)_t=h:mm:ss a z
F(m)_t=h:mm:ss a
F(s)_t=h:mm a
