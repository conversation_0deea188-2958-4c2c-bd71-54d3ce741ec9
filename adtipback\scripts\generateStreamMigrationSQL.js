// adtipback/scripts/generateStreamMigrationSQL.js
// Generate SQL migration statements for Cloudflare Stream videos

require('dotenv').config();
const fetch = require('node-fetch');
const fs = require('fs');

async function generateStreamMigrationSQL() {
  console.log('🚀 GENERATING CLOUDFLARE STREAM MIGRATION SQL');
  console.log('==============================================\n');

  const accountId = process.env.CLOUDFLARE_ACCOUNT_ID || '94e2ffe1e7d5daf0d3de8d11c55dd2d6';
  const apiToken = process.env.CLOUDFLARE_STREAM_API_TOKEN || process.env.CLOUD_FARE_TOKEN_VALUE;
  const customerCode = 'cnqr7kkcyczlbymi'; // From the thumbnail URLs we saw
  
  try {
    // Get all Stream videos
    console.log('📹 Fetching Cloudflare Stream inventory...');
    const baseUrl = `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream`;
    
    const response = await fetch(baseUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiToken}`,
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(`Failed to fetch Stream videos: ${JSON.stringify(result.errors)}`);
    }

    const allVideos = result.result;
    console.log(`✅ Found ${allVideos.length} total videos in Stream`);

    // Filter ready videos
    const readyVideos = allVideos.filter(video => video.status?.state === 'ready');
    console.log(`🎬 Found ${readyVideos.length} ready videos for migration`);

    if (readyVideos.length === 0) {
      console.log('❌ No ready videos found. Migration cannot proceed.');
      return;
    }

    // Generate SQL statements
    console.log('\n📝 Generating SQL migration statements...');
    
    const sqlStatements = [];
    const videoMappings = [];

    // Header comment
    sqlStatements.push(`-- Cloudflare Stream Migration SQL`);
    sqlStatements.push(`-- Generated on: ${new Date().toISOString()}`);
    sqlStatements.push(`-- Total ready videos: ${readyVideos.length}`);
    sqlStatements.push(`-- Customer Code: ${customerCode}`);
    sqlStatements.push(`-- Account ID: ${accountId}`);
    sqlStatements.push('');

    // Add each ready video
    readyVideos.forEach((video, index) => {
      const streamVideoId = video.uid;
      const manifestUrl = `https://customer-${customerCode}.cloudflarestream.com/${streamVideoId}/manifest/video.m3u8`;
      const mp4Url = `https://customer-${customerCode}.cloudflarestream.com/${streamVideoId}/downloads/default.mp4`;
      const thumbnailUrl = video.thumbnail || `https://customer-${customerCode}.cloudflarestream.com/${streamVideoId}/thumbnails/thumbnail.jpg`;
      
      // Create mapping entry
      videoMappings.push({
        uid: streamVideoId,
        filename: video.filename,
        duration: video.duration,
        size: video.size,
        created: video.created,
        manifestUrl,
        mp4Url,
        thumbnailUrl,
        meta: video.meta
      });

      // Generate SQL comment with video info
      sqlStatements.push(`-- Video ${index + 1}: ${video.filename || streamVideoId}`);
      sqlStatements.push(`-- UID: ${streamVideoId}`);
      sqlStatements.push(`-- Duration: ${video.duration || 'N/A'}s`);
      sqlStatements.push(`-- Size: ${video.size ? (video.size / (1024 * 1024)).toFixed(2) + ' MB' : 'N/A'}`);
      sqlStatements.push(`-- Created: ${video.created || 'N/A'}`);
      if (video.meta && Object.keys(video.meta).length > 0) {
        sqlStatements.push(`-- Metadata: ${JSON.stringify(video.meta)}`);
      }
      
      // Generate UPDATE statement
      // This is a template - you'll need to customize the WHERE clause based on your matching criteria
      sqlStatements.push(`UPDATE reels SET`);
      sqlStatements.push(`  stream_video_id = '${streamVideoId}',`);
      sqlStatements.push(`  stream_status = 'ready',`);
      sqlStatements.push(`  adaptive_manifest_url = '${manifestUrl}',`);
      sqlStatements.push(`  stream_ready_at = NOW()`);
      sqlStatements.push(`WHERE`);
      sqlStatements.push(`  -- CUSTOMIZE THIS WHERE CLAUSE FOR YOUR MATCHING CRITERIA`);
      sqlStatements.push(`  -- Examples:`);
      if (video.filename) {
        const cleanFilename = video.filename.replace(/\.[^/.]+$/, "").replace(/'/g, "\\'");
        sqlStatements.push(`  -- name LIKE '%${cleanFilename}%' OR`);
      }
      sqlStatements.push(`  -- video_link LIKE '%${streamVideoId}%' OR`);
      sqlStatements.push(`  -- id = [SPECIFIC_VIDEO_ID] OR`);
      if (video.meta && video.meta.userId) {
        sqlStatements.push(`  -- user_id = '${video.meta.userId}' OR`);
      }
      sqlStatements.push(`  id = 0; -- PLACEHOLDER - Replace with actual matching criteria`);
      sqlStatements.push('');
    });

    // Add verification queries
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('-- VERIFICATION QUERIES');
    sqlStatements.push('-- ==============================================');
    sqlStatements.push('');
    sqlStatements.push('-- Check total videos with Stream IDs');
    sqlStatements.push('SELECT COUNT(*) as total_videos_with_stream FROM reels WHERE stream_video_id IS NOT NULL;');
    sqlStatements.push('');
    sqlStatements.push('-- Check videos by Stream status');
    sqlStatements.push('SELECT stream_status, COUNT(*) as count FROM reels WHERE stream_video_id IS NOT NULL GROUP BY stream_status;');
    sqlStatements.push('');
    sqlStatements.push('-- Check recent Stream updates');
    sqlStatements.push('SELECT id, name, stream_video_id, stream_status, stream_ready_at FROM reels WHERE stream_ready_at IS NOT NULL ORDER BY stream_ready_at DESC LIMIT 10;');
    sqlStatements.push('');
    sqlStatements.push('-- Sample Stream URLs for testing');
    sqlStatements.push('SELECT id, name, adaptive_manifest_url FROM reels WHERE stream_status = "ready" LIMIT 5;');

    // Save SQL file
    const sqlContent = sqlStatements.join('\n');
    const sqlFilename = `stream_migration_${Date.now()}.sql`;
    fs.writeFileSync(sqlFilename, sqlContent);
    
    console.log(`\n💾 SQL migration file saved: ${sqlFilename}`);
    console.log(`📄 Contains ${readyVideos.length} UPDATE statements`);

    // Save JSON mapping file
    const mappingContent = {
      generated: new Date().toISOString(),
      customerCode,
      accountId,
      totalVideos: readyVideos.length,
      videos: videoMappings
    };

    const jsonFilename = `stream_mapping_${Date.now()}.json`;
    fs.writeFileSync(jsonFilename, JSON.stringify(mappingContent, null, 2));
    
    console.log(`🗺️  Stream mapping saved: ${jsonFilename}`);

    // Generate sample Stream URLs for testing
    console.log('\n🔗 SAMPLE STREAM URLS FOR TESTING:');
    readyVideos.slice(0, 5).forEach((video, index) => {
      const manifestUrl = `https://customer-${customerCode}.cloudflarestream.com/${video.uid}/manifest/video.m3u8`;
      console.log(`${index + 1}. ${video.uid}`);
      console.log(`   HLS: ${manifestUrl}`);
      console.log(`   MP4: https://customer-${customerCode}.cloudflarestream.com/${video.uid}/downloads/default.mp4`);
      console.log('');
    });

    console.log('\n📋 NEXT STEPS:');
    console.log('1. Review the generated SQL file');
    console.log('2. Update the WHERE clauses to match your videos properly');
    console.log('3. Test a few UPDATE statements manually first');
    console.log('4. Execute the SQL statements in your database');
    console.log('5. Use the verification queries to check results');
    console.log('6. Test video playback with the sample URLs above');

    console.log('\n✅ MIGRATION FILES GENERATED SUCCESSFULLY!');
    console.log(`📊 Ready videos: ${readyVideos.length}`);
    console.log(`📄 SQL file: ${sqlFilename}`);
    console.log(`🗺️  Mapping file: ${jsonFilename}`);

    return {
      success: true,
      readyVideos: readyVideos.length,
      sqlFile: sqlFilename,
      mappingFile: jsonFilename
    };

  } catch (error) {
    console.error('\n❌ Migration generation failed:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  generateStreamMigrationSQL()
    .then(result => {
      console.log('\n🎉 Success! Migration files generated.');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Failed:', error.message);
      process.exit(1);
    });
}

module.exports = { generateStreamMigrationSQL };
