/**
 * Chat Socket Service using Socket.IO
 *
 * DISABLED: This service has been completely replaced by FCM high priority notifications
 * and API-based messaging for more reliable message delivery.
 *
 * Use FCMMessageQueueService and FCM chat API endpoints instead.
 */

class ChatSocketService {
  /**
   * Initialize Socket.IO server
   * DISABLED: Socket.IO initialization disabled in favor of FCM notifications
   */
  initialize() {
    console.log('[ChatSocketService] Socket.IO initialization disabled - using FCM notifications instead');
    return; // Early return to disable Socket.IO
  }

  /**
   * Get Socket.IO instance
   * DISABLED: Returns null since Socket.IO is disabled
   */
  getIO() {
    return null;
  }

  /**
   * Get connected users count
   * DISABLED: Returns 0 since Socket.IO is disabled
   */
  getConnectedUsersCount() {
    return 0;
  }

  /**
   * Send message to specific user
   * DISABLED: No-op since Socket.IO is disabled
   */
  sendToUser() {
    console.log('[ChatSocketService] sendToUser disabled - use FCM notifications instead');
  }

  /**
   * Send message to conversation
   * DISABLED: No-op since Socket.IO is disabled
   */
  sendToConversation() {
    console.log('[ChatSocketService] sendToConversation disabled - use FCM notifications instead');
  }
}

module.exports = new ChatSocketService();
