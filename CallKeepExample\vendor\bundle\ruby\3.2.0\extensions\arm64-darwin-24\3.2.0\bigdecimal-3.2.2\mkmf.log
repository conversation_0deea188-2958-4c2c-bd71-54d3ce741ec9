have_builtin_func: checking for __builtin_clz()... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int main(int argc, char **argv)
4: {
5:   return !!argv[argc];
6: }
/* end */

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int foo;
4: int main() { __builtin_clz(0); return 0; }
/* end */

--------------------

have_builtin_func: checking for __builtin_clzl()... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int foo;
4: int main() { __builtin_clzl(0); return 0; }
/* end */

--------------------

have_builtin_func: checking for __builtin_clzll()... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: int foo;
4: int main() { __builtin_clzll(0); return 0; }
/* end */

--------------------

have_header: checking for float.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <float.h>
/* end */

--------------------

have_header: checking for math.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <math.h>
/* end */

--------------------

have_header: checking for stdbool.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <stdbool.h>
/* end */

--------------------

have_header: checking for stdlib.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <stdlib.h>
/* end */

--------------------

have_header: checking for x86intrin.h... -------------------- no

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
In file included from conftest.c:3:
In file included from /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/x86intrin.h:15:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/immintrin.h:14:2: error: "This header is only meant to be used on x86 and x64 architecture"
   14 | #error "This header is only meant to be used on x86 and x64 architecture"
      |  ^
In file included from conftest.c:3:
In file included from /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/x86intrin.h:15:
In file included from /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/immintrin.h:17:
In file included from /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/x86gprintrin.h:14:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/hresetintrin.h:42:27: error: invalid input constraint 'a' in asm
   42 |   __asm__ ("hreset $0" :: "a"(__eax));
      |                           ^
In file included from conftest.c:3:
In file included from /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/x86intrin.h:15:
In file included from /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/immintrin.h:20:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:14:2: error: "This header is only meant to be used on x86 and x64 architecture"
   14 | #error "This header is only meant to be used on x86 and x64 architecture"
      |  ^
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:56:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
   56 |     return (__m64)__builtin_ia32_vec_init_v2si(__i, 0);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:130:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  130 |     return (__m64)__builtin_ia32_packsswb((__v4hi)__m1, (__v4hi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:155:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  155 |     return (__m64)__builtin_ia32_packssdw((__v2si)__m1, (__v2si)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:180:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  180 |     return (__m64)__builtin_ia32_packuswb((__v4hi)__m1, (__v4hi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:207:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  207 |     return (__m64)__builtin_ia32_punpckhbw((__v8qi)__m1, (__v8qi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:230:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  230 |     return (__m64)__builtin_ia32_punpckhwd((__v4hi)__m1, (__v4hi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:251:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  251 |     return (__m64)__builtin_ia32_punpckhdq((__v2si)__m1, (__v2si)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:278:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  278 |     return (__m64)__builtin_ia32_punpcklbw((__v8qi)__m1, (__v8qi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:301:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  301 |     return (__m64)__builtin_ia32_punpcklwd((__v4hi)__m1, (__v4hi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:322:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  322 |     return (__m64)__builtin_ia32_punpckldq((__v2si)__m1, (__v2si)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:343:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  343 |     return (__m64)__builtin_ia32_paddb((__v8qi)__m1, (__v8qi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:364:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  364 |     return (__m64)__builtin_ia32_paddw((__v4hi)__m1, (__v4hi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:385:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  385 |     return (__m64)__builtin_ia32_paddd((__v2si)__m1, (__v2si)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:409:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  409 |     return (__m64)__builtin_ia32_paddsb((__v8qi)__m1, (__v8qi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:433:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  433 |     return (__m64)__builtin_ia32_paddsw((__v4hi)__m1, (__v4hi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/mmintrin.h:456:12: error: invalid conversion between vector type '__m64' (vector of 1 'long long' value) and integer type 'int' of different size
  456 |     return (__m64)__builtin_ia32_paddusb((__v8qi)__m1, (__v8qi)__m2);
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
fatal error: too many errors emitted, stopping now [-ferror-limit=]
20 errors generated.
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <x86intrin.h>
/* end */

--------------------

have_header: checking for intrin.h... -------------------- no

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
In file included from conftest.c:3:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include/intrin.h:12:15: fatal error: 'intrin.h' file not found
   12 | #include_next <intrin.h>
      |               ^~~~~~~~~~
1 error generated.
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <intrin.h>
/* end */

--------------------

have_func: checking for labs() in stdlib.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <stdlib.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))labs; return !p; }
/* end */

--------------------

have_func: checking for llabs() in stdlib.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <stdlib.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))llabs; return !p; }
/* end */

--------------------

have_func: checking for finite() in math.h... -------------------- no

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
conftest.c:16:57: error: call to undeclared library function 'finite' with type 'int (double)'; ISO C99 and later do not support implicit function declarations [-Wimplicit-function-declaration]
   16 | int t(void) { void ((*volatile p)()); p = (void ((*)()))finite; return !p; }
      |                                                         ^
conftest.c:16:57: note: include the header <math.h> or explicitly provide a declaration for 'finite'
1 error generated.
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <math.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))finite; return !p; }
/* end */

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
conftest.c:16:13: error: expected identifier or '('
   16 | extern void finite();
      |             ^
/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/missing.h:165:20: note: expanded from macro 'finite'
  165 | # define finite(x) isfinite(x)
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:7: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |       ^
conftest.c:16:13: error: expected ')'
/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/missing.h:165:20: note: expanded from macro 'finite'
  165 | # define finite(x) isfinite(x)
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:7: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |       ^
conftest.c:16:13: note: to match this '('
/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/missing.h:165:20: note: expanded from macro 'finite'
  165 | # define finite(x) isfinite(x)
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:5: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |     ^
conftest.c:17:15: error: expected expression
   17 | int t(void) { finite(); return 0; }
      |               ^
/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/missing.h:165:20: note: expanded from macro 'finite'
  165 | # define finite(x) isfinite(x)
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:15: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |               ^
conftest.c:17:15: error: expected expression
/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/missing.h:165:20: note: expanded from macro 'finite'
  165 | # define finite(x) isfinite(x)
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:65: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |                                                                 ^
conftest.c:17:15: error: expected expression
/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/missing.h:165:20: note: expanded from macro 'finite'
  165 | # define finite(x) isfinite(x)
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:162:15: note: expanded from macro 'isfinite'
  162 |     : sizeof(x) == sizeof(double) ? __inline_isfinited((double)(x))      \
      |               ^
conftest.c:17:15: error: expected expression
/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/missing.h:165:20: note: expanded from macro 'finite'
  165 | # define finite(x) isfinite(x)
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:162:66: note: expanded from macro 'isfinite'
  162 |     : sizeof(x) == sizeof(double) ? __inline_isfinited((double)(x))      \
      |                                                                  ^
conftest.c:17:15: error: expected expression
/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/missing.h:165:20: note: expanded from macro 'finite'
  165 | # define finite(x) isfinite(x)
      |                    ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:163:71: note: expanded from macro 'isfinite'
  163 |                                   : __inline_isfinitel((long double)(x)))
      |                                                                       ^
7 errors generated.
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <math.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: extern void finite();
17: int t(void) { finite(); return 0; }
/* end */

--------------------

have_func: checking for isfinite() in math.h... -------------------- no

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
conftest.c:16:57: error: use of undeclared identifier 'isfinite'
   16 | int t(void) { void ((*volatile p)()); p = (void ((*)()))isfinite; return !p; }
      |                                                         ^
1 error generated.
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <math.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))isfinite; return !p; }
/* end */

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
conftest.c:16:13: error: expected identifier or '('
   16 | extern void isfinite();
      |             ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:7: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |       ^
conftest.c:16:13: error: expected ')'
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:7: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |       ^
conftest.c:16:13: note: to match this '('
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:5: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |     ^
conftest.c:17:15: error: expected expression
   17 | int t(void) { isfinite(); return 0; }
      |               ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:15: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |               ^
conftest.c:17:15: error: expected expression
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:161:65: note: expanded from macro 'isfinite'
  161 |     ( sizeof(x) == sizeof(float)  ? __inline_isfinitef((float)(x))       \
      |                                                                 ^
conftest.c:17:15: error: expected expression
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:162:15: note: expanded from macro 'isfinite'
  162 |     : sizeof(x) == sizeof(double) ? __inline_isfinited((double)(x))      \
      |               ^
conftest.c:17:15: error: expected expression
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:162:66: note: expanded from macro 'isfinite'
  162 |     : sizeof(x) == sizeof(double) ? __inline_isfinited((double)(x))      \
      |                                                                  ^
conftest.c:17:15: error: expected expression
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include/math.h:163:71: note: expanded from macro 'isfinite'
  163 |                                   : __inline_isfinitel((long double)(x)))
      |                                                                       ^
7 errors generated.
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <math.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: extern void isfinite();
17: int t(void) { isfinite(); return 0; }
/* end */

--------------------

have_header: checking for ruby/atomic.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <ruby/atomic.h>
/* end */

--------------------

have_header: checking for ruby/internal/has/builtin.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <ruby/internal/has/builtin.h>
/* end */

--------------------

have_header: checking for ruby/internal/static_assert.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <ruby/internal/static_assert.h>
/* end */

--------------------

have_func: checking for rb_rational_num() in ruby.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_rational_num; return !p; }
/* end */

--------------------

have_func: checking for rb_rational_den() in ruby.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_rational_den; return !p; }
/* end */

--------------------

have_func: checking for rb_complex_real() in ruby.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_complex_real; return !p; }
/* end */

--------------------

have_func: checking for rb_complex_imag() in ruby.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_complex_imag; return !p; }
/* end */

--------------------

have_func: checking for rb_opts_exception_p() in ruby.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
conftest.c:16:57: error: use of undeclared identifier 'rb_opts_exception_p'
   16 | int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_opts_exception_p; return !p; }
      |                                                         ^
1 error generated.
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_opts_exception_p; return !p; }
/* end */

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: extern void rb_opts_exception_p();
17: int t(void) { rb_opts_exception_p(); return 0; }
/* end */

--------------------

have_func: checking for rb_category_warn() in ruby.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -o conftest -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe conftest.c  -L. -L/Users/<USER>/.rbenv/versions/3.2.2/lib -L. -fstack-protector-strong     -lruby.3.2  -lpthread  "
checked program was:
/* begin */
 1: #include "ruby.h"
 2: 
 3: #include <ruby.h>
 4: 
 5: /*top*/
 6: extern int t(void);
 7: int main(int argc, char **argv)
 8: {
 9:   if (argc > 1000000) {
10:     int (* volatile tp)(void)=(int (*)(void))&t;
11:     printf("%d", (*tp)());
12:   }
13: 
14:   return !!argv[argc];
15: }
16: int t(void) { void ((*volatile p)()); p = (void ((*)()))rb_category_warn; return !p; }
/* end */

--------------------

have_const: checking for RB_WARN_CATEGORY_DEPRECATED in ruby.h... -------------------- yes

DYLD_FALLBACK_LIBRARY_PATH=.:/Users/<USER>/.rbenv/versions/3.2.2/lib "clang -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/arm64-darwin24 -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0/ruby/backward -I/Users/<USER>/.rbenv/versions/3.2.2/include/ruby-3.2.0 -I. -D_XOPEN_SOURCE -D_DARWIN_C_SOURCE -D_DARWIN_UNLIMITED_SELECT -D_REENTRANT   -fdeclspec -O3 -fno-fast-math -ggdb3 -Wall -Wextra -Wextra-tokens -Wdeprecated-declarations -Wdivision-by-zero -Wdiv-by-zero -Wimplicit-function-declaration -Wimplicit-int -Wmisleading-indentation -Wpointer-arith -Wshorten-64-to-32 -Wwrite-strings -Wold-style-definition -Wmissing-noreturn -Wno-cast-function-type -Wno-constant-logical-operand -Wno-long-long -Wno-missing-field-initializers -Wno-overlength-strings -Wno-parentheses-equality -Wno-self-assign -Wno-tautological-compare -Wno-unused-parameter -Wno-unused-value -Wunused-variable -Wundef  -fno-common -pipe   -c conftest.c"
checked program was:
/* begin */
1: #include "ruby.h"
2: 
3: #include <ruby.h>
4: 
5: /*top*/
6: typedef int conftest_type;
7: conftest_type conftestval = (int)RB_WARN_CATEGORY_DEPRECATED;
/* end */

--------------------

