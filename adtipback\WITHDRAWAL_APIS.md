# Withdrawal APIs Documentation

This document describes all withdrawal-related APIs for both **User** and **Admin** roles.

---

## USER WITHDRAWAL APIS

### 1. Request Withdrawal
- **Endpoint:** `/withdraw`
- **Method:** `POST`
- **Auth:** User token required
- **Description:** User requests a withdrawal from wallet, referral, coupon, or channel balance.

**Request Body:**
```json
{
  "type": "wallet",
  "amount": 1500,
  "paymentMethod": "bank",
  "accountDetails": {
    "accountNumber": "**********",
    "ifsc": "SBIN0001234",
    "holderName": "<PERSON>"
  }
}
```

**Response:**
```json
{
  "status": true,
  "message": "Withdrawal request submitted successfully",
  "data": { "withdrawalId": 101 }
}
```

---

### 2. Get Withdrawal History
- **Endpoint:** `/withdrawals?type=wallet|referral|coupon|channel`
- **Method:** `GET`
- **Auth:** User token required
- **Description:** Get user's withdrawal requests by type, with pagination.

**Query Parameters:**
- `type`: `wallet` | `referral` | `coupon` | `channel` (required)
- `page`: number (optional)
- `limit`: number (optional)

**Response:**
```json
{
  "status": true,
  "message": "Withdrawals fetched",
  "data": {
    "withdrawals": [
      {
        "id": 101,
        "amount": 1500,
        "status": "pending",
        "created_at": "2024-06-01T10:00:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 1,
      "totalItems": 1,
      "itemsPerPage": 20
    }
  }
}
```

---

## ADMIN WITHDRAWAL APIS

All admin endpoints require admin authentication (`Auth.verifyToken`).

### 1. List Withdrawals (Wallet/Referral/Coupon/Channel)
- **Endpoint:** `/admin/withdrawals/{type}`
- **Method:** `GET`
- **Description:** List withdrawal requests by type, with filtering and pagination.

**Query Parameters:**
- `page`: number (optional)
- `limit`: number (optional)
- `status`: `pending` | `approved` | `rejected` | `paid` (optional)
- `userId`: number (optional)
- `fromDate`: `YYYY-MM-DD` (optional)
- `toDate`: `YYYY-MM-DD` (optional)
- `premium`: `1` (premium only) or `0` (non-premium only) (optional)

**Example Request:**
```
GET /admin/withdrawals/wallet?page=1&limit=20&status=pending
```

**Response:**
```json
{
  "status": true,
  "message": "Wallet withdrawals fetched",
  "data": {
    "withdrawals": [
      {
        "id": 101,
        "user_id": 1,
        "amount": 1500,
        "status": "pending",
        "created_at": "2024-06-01T10:00:00Z",
        "user": {
          "firstName": "John",
          "lastName": "Doe",
          "mobile_number": "9876543210",
          "is_premium": true
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 1,
      "totalItems": 1,
      "itemsPerPage": 20
    }
  }
}
```

---

### 2. Update Withdrawal Status (Approve/Reject/Paid)
- **Endpoint:** `/admin/withdrawals/{type}/{id}/status`
- **Method:** `PATCH`
- **Description:** Update the status of a withdrawal request.

**Request Body:**
```json
{
  "status": "approved",
  "adminNotes": "All details verified.",
  "adminId": 10
}
```

**Response:**
```json
{
  "status": true,
  "message": "Withdrawal status updated",
  "data": { "withdrawalId": 101, "status": "approved" }
}
```

---

### 3. Export Withdrawals (CSV)
- **Endpoint:** `/admin/withdrawals/{type}/export`
- **Method:** `GET`
- **Description:** Export withdrawal requests as CSV (filtered by status/date).

**Query Parameters:**
- `status`: (optional)
- `fromDate`: (optional)
- `toDate`: (optional)

**Response:**
- Returns a CSV file download with filename: `{type}_withdrawals_YYYY-MM-DD.csv`

---

## Supported Withdrawal Types

| Type | Description |
|------|-------------|
| `wallet` | Wallet balance withdrawals |
| `referral` | Referral earnings withdrawals |
| `coupon` | Coupon earnings withdrawals |
| `channel` | Channel/content creator withdrawals |

---

## Status Values

| Status | Description |
|--------|-------------|
| `pending` | Request submitted, awaiting admin review |
| `approved` | Admin approved, ready for payment |
| `rejected` | Admin rejected the request |
| `paid` | Payment completed |

---

## Business Rules

1. **Minimum Withdrawal Amount:** ₹1000 for premium users
2. **Premium Check:** Only premium users can withdraw
3. **Balance Validation:** User must have sufficient balance
4. **Audit Trail:** All admin actions are logged
5. **Export Support:** CSV export for all withdrawal types

---

## Error Responses

**400 Bad Request:**
```json
{
  "status": false,
  "message": "Invalid request parameters",
  "data": []
}
```

**401 Unauthorized:**
```json
{
  "status": false,
  "message": "Authentication required",
  "data": []
}
```

**500 Internal Server Error:**
```json
{
  "status": false,
  "message": "Internal server error",
  "data": []
}
```

---

## Testing Examples

### Test Admin List Withdrawals
```bash
curl -X GET "http://localhost:3000/admin/withdrawals/wallet?page=1&limit=10&status=pending" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Test Update Withdrawal Status
```bash
curl -X PATCH "http://localhost:3000/admin/withdrawals/wallet/101/status" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "approved",
    "adminNotes": "All details verified",
    "adminId": 10
  }'
```

### Test Export Withdrawals
```bash
curl -X GET "http://localhost:3000/admin/withdrawals/wallet/export?status=pending" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  --output wallet_withdrawals.csv
```

---

For more details, see the backend code or contact the development team. 