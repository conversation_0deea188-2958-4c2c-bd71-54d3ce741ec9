CLANG_CXX_LANGUAGE_STANDARD = c++20
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
CONFIGURATION_BUILD_DIR = ${PODS_CONFIGURATION_BUILD_DIR}/React-Core
DEFINES_MODULE = YES
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal" "${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built" "$(PODS_CONFIGURATION_BUILD_DIR)/React-hermes"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 RCT_METRO_PORT=${RCT_METRO_PORT}
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Private" "${PODS_ROOT}/Headers/Private/React-Core" "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/DoubleConversion" "${PODS_ROOT}/Headers/Public/RCT-Folly" "${PODS_ROOT}/Headers/Public/React-Core" "${PODS_ROOT}/Headers/Public/React-callinvoker" "${PODS_ROOT}/Headers/Public/React-cxxreact" "${PODS_ROOT}/Headers/Public/React-debug" "${PODS_ROOT}/Headers/Public/React-featureflags" "${PODS_ROOT}/Headers/Public/React-hermes" "${PODS_ROOT}/Headers/Public/React-jsi" "${PODS_ROOT}/Headers/Public/React-jsiexecutor" "${PODS_ROOT}/Headers/Public/React-jsinspector" "${PODS_ROOT}/Headers/Public/React-jsinspectorcdp" "${PODS_ROOT}/Headers/Public/React-jsinspectornetwork" "${PODS_ROOT}/Headers/Public/React-jsinspectortracing" "${PODS_ROOT}/Headers/Public/React-jsitooling" "${PODS_ROOT}/Headers/Public/React-logger" "${PODS_ROOT}/Headers/Public/React-oscompat" "${PODS_ROOT}/Headers/Public/React-perflogger" "${PODS_ROOT}/Headers/Public/React-performancetimeline" "${PODS_ROOT}/Headers/Public/React-rendererconsistency" "${PODS_ROOT}/Headers/Public/React-rendererdebug" "${PODS_ROOT}/Headers/Public/React-runtimeexecutor" "${PODS_ROOT}/Headers/Public/React-runtimescheduler" "${PODS_ROOT}/Headers/Public/React-timing" "${PODS_ROOT}/Headers/Public/React-utils" "${PODS_ROOT}/Headers/Public/Yoga" "${PODS_ROOT}/Headers/Public/boost" "${PODS_ROOT}/Headers/Public/fast_float" "${PODS_ROOT}/Headers/Public/fmt" "${PODS_ROOT}/Headers/Public/glog" "${PODS_ROOT}/Headers/Public/hermes-engine" $(PODS_TARGET_SRCROOT)/ReactCommon ${PODS_ROOT}/Headers/Public/FlipperKit $(PODS_ROOT)/Headers/Public/ReactCommon $(PODS_ROOT)/Headers/Public/React-hermes $(PODS_ROOT)/Headers/Public/hermes-engine "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectorcdp/jsinspector_moderncdp.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation/RCTDeprecation.framework/Headers" $(PODS_ROOT)/glog $(PODS_ROOT)/boost $(PODS_ROOT)/DoubleConversion $(PODS_ROOT)/fast_float/include $(PODS_ROOT)/fmt/include $(PODS_ROOT)/SocketRocket $(PODS_ROOT)/RCT-Folly
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/SocketRocket/SocketRocket.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/fmt/fmt.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_cdp/React-jsinspectorcdp.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_network/React-jsinspectornetwork.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_tracing/React-jsinspectortracing.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_runtime/React-jsitooling.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_DEVELOPMENT_LANGUAGE = ${DEVELOPMENT_LANGUAGE}
PODS_ROOT = ${SRCROOT}
PODS_TARGET_SRCROOT = ${PODS_ROOT}/../../node_modules/react-native
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
PRODUCT_BUNDLE_IDENTIFIER = org.cocoapods.${PRODUCT_NAME:rfc1034identifier}
SKIP_INSTALL = YES
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
