GIT
  remote: https://github.com/CocoaPods/CLAide.git
  revision: b5ced9cc141df732e8027078543eb92fc6447567
  branch: master
  specs:
    claide (1.0.3)

GIT
  remote: https://github.com/CocoaPods/CocoaPods.git
  revision: c75c4a6dd226c45e0ad876caa926bff51a3f00d9
  branch: master
  specs:
    cocoapods (1.9.1)
      activesupport (>= 4.0.2, < 5)
      claide (>= 1.0.2, < 2.0)
      cocoapods-core (= 1.9.1)
      cocoapods-deintegrate (>= 1.0.3, < 2.0)
      cocoapods-downloader (>= 1.2.2, < 2.0)
      cocoapods-plugins (>= 1.0.0, < 2.0)
      cocoapods-search (>= 1.0.0, < 2.0)
      cocoapods-trunk (>= 1.4.0, < 2.0)
      cocoapods-try (= 1.2.0)
      colored2 (~> 3.1)
      escape (~> 0.0.4)
      fourflusher (>= 2.3.0, < 3.0)
      gh_inspector (~> 1.0)
      molinillo (~> 0.6.6)
      nap (~> 1.0)
      ruby-macho (~> 1.4)
      xcodeproj (>= 1.14.0, < 2.0)

GIT
  remote: https://github.com/CocoaPods/Core.git
  revision: 8923c0cdca68d4bc7126cd64106a5fc1e9217ced
  branch: master
  specs:
    cocoapods-core (1.9.1)
      activesupport (>= 4.0.2, < 6)
      addressable (~> 2.6)
      algoliasearch (~> 1.0)
      concurrent-ruby (~> 1.1)
      fuzzy_match (~> 2.0.4)
      nap (~> 1.0)
      netrc (~> 0.11)
      public_suffix (~> 2.0)
      typhoeus (~> 1.0)

PATH
  remote: .
  specs:
    cocoapods-try (1.2.0)

GEM
  remote: https://rubygems.org/
  specs:
    CFPropertyList (3.0.2)
    activesupport (********)
      i18n (~> 0.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    algoliasearch (1.27.1)
      httpclient (~> 2.8, >= 2.8.3)
      json (>= 1.5.1)
    ast (2.4.0)
    atomos (0.1.3)
    bacon (1.2.0)
    cocoapods-deintegrate (1.0.4)
    cocoapods-downloader (1.3.0)
    cocoapods-plugins (1.0.0)
      nap
    cocoapods-search (1.0.0)
    cocoapods-trunk (1.4.1)
      nap (>= 0.8, < 2.0)
      netrc (~> 0.11)
    colored2 (3.1.2)
    concurrent-ruby (1.1.6)
    escape (0.0.4)
    ethon (0.12.0)
      ffi (>= 1.3.0)
    ffi (1.12.2)
    fourflusher (2.3.1)
    fuzzy_match (2.0.4)
    gh_inspector (1.1.3)
    httpclient (2.8.3)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    json (2.3.0)
    minitest (5.8.4)
    mocha (1.11.2)
    mocha-on-bacon (0.2.3)
      mocha (>= 0.13.0)
    molinillo (0.6.6)
    nanaimo (0.2.6)
    nap (1.1.0)
    netrc (0.11.0)
    parallel (1.10.0)
    parser (*******)
      ast (~> 2.4.0)
    powerpack (0.1.2)
    prettybacon (0.0.2)
      bacon (~> 1.2)
    public_suffix (2.0.5)
    rainbow (2.2.2)
      rake
    rake (10.5.0)
    rubocop (0.50.0)
      parallel (~> 1.10)
      parser (>= 2.3.3.1, < 3.0)
      powerpack (~> 0.1)
      rainbow (>= 2.2.2, < 3.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (~> 1.0, >= 1.0.1)
    ruby-macho (1.4.0)
    ruby-progressbar (1.10.1)
    thread_safe (0.3.6)
    typhoeus (1.3.1)
      ethon (>= 0.9.0)
    tzinfo (1.2.7)
      thread_safe (~> 0.1)
    unicode-display_width (1.7.0)
    xcodeproj (1.16.0)
      CFPropertyList (>= 2.3.3, < 4.0)
      atomos (~> 0.1.3)
      claide (>= 1.0.2, < 2.0)
      colored2 (~> 3.1)
      nanaimo (~> 0.2.6)

PLATFORMS
  ruby

DEPENDENCIES
  bacon
  bundler (~> 1.3)
  claide!
  cocoapods!
  cocoapods-core!
  cocoapods-try!
  mocha
  mocha-on-bacon
  prettybacon
  rake (~> 10.0)
  rubocop (= 0.50.0)

BUNDLED WITH
   1.17.2
