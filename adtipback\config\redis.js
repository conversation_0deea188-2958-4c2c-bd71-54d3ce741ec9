const Redis = require('ioredis');

const redisClient = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  retryStrategy: (times) => {
    // Retry connection with exponential backoff
    return Math.min(times * 50, 2000); // Retry every 50ms, max 2 seconds
  },
});

redisClient.on('connect', () => {
  console.log(`Connected to Redis at ${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`);
});

redisClient.on('error', (err) => {
  console.error('Redis connection error:', err.message);
  // Optionally, implement a fallback or exit the process
  if (err.code === 'ECONNREFUSED') {
    console.error('Redis server not found. Please check REDIS_HOST and REDIS_PORT.');
    process.exit(1); // Exit if Redis is critical
  }
});

redisClient.on('reconnecting', (delay) => {
  console.log(`Reconnecting to Redis in ${delay}ms...`);
});

module.exports = redisClient;