{"logs": [{"outputFile": "com.callkeepexample.app-mergeDebugResources-27:/values-lt/values-lt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "30,31,32,33,34,35,36,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2947,3045,3155,3254,3357,3468,3578,5367", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3040,3150,3249,3352,3463,3573,3693,5463"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/625ed137c6a3f5343b71917adedb437c/transformed/appcompat-1.7.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,4632", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,4711"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,213,286,357,444,514,582,660,742,824,905,979,1062,1146,1224,1307,1390,1466,1542,1616,1713,1788,1870,1943", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "123,208,281,352,439,509,577,655,737,819,900,974,1057,1141,1219,1302,1385,1461,1537,1611,1708,1783,1865,1938,2018"}, "to": {"startLines": "29,37,38,39,40,41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2874,3698,3783,3856,3927,4014,4084,4152,4230,4312,4394,4475,4549,4716,4800,4878,4961,5044,5120,5196,5270,5468,5543,5625,5698", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "2942,3778,3851,3922,4009,4079,4147,4225,4307,4389,4470,4544,4627,4795,4873,4956,5039,5115,5191,5265,5362,5538,5620,5693,5773"}}]}]}