const { google } = require("googleapis");
const axios = require("axios");
const SERVICE_ACCOUNT_KEY_FILE = "../firebase_service_account.json";

const SCOPES = "https://www.googleapis.com/auth/firebase.messaging";

const getAccessToken = () => {
  return new Promise(function (resolve, reject) {
    const key = require("../firebase_service_account.json");
    const jwtClient = new google.auth.JWT(
      key.client_email,
      null,
      key.private_key,
      SCOPES,
      null
    );

    jwtClient.authorize(function (err, tokens) {
      if (err) {
        reject(err);
        return;
      }
      resolve(tokens.access_token);
    });
  });
};

const AxiosConfig = async (token, notification) => {
  try {
    let config = {
      method: "post",
      url: "https://fcm.googleapis.com/v1/projects/adtip-3873c/messages:send",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      data: notification,
    };

    const response = await axios(config);

    return response;
  } catch (error) {
    console.error("Error sending notification:", error.message);
    throw error;
  }
};

async function sendNotification(data) {
  try {
    const access_token = await getAccessToken();

    // Enhanced notification structure for chat messages
    var notification = JSON.stringify({
      message: {
        token: data.token,
        notification: {
          body: data.body,
          title: data.title,
        },
        android: {
          priority: "high",
          notification: {
            channelId: "chat_messages",
            priority: "high",
            defaultSound: true,
            defaultVibrateTimings: true,
            clickAction: "FLUTTER_NOTIFICATION_CLICK",
            icon: "ic_notification",
            color: "#FF6B35"
          }
        },
        apns: {
          headers: {
            "apns-priority": "10",
          },
          payload: {
            aps: {
              sound: "default",
              badge: 1,
              "content-available": 1,
              category: "CHAT_MESSAGE"
            },
          },
        },
        data: {
          type: data.type || "chat_message",
          conversationId: data.conversationId || "",
          senderId: data.senderId || "",
          timestamp: data.timestamp || new Date().toISOString(),
          senderName: data.senderName || "",
          ...data.data // Allow additional custom data
        },
      },
    });

    let response = await AxiosConfig(access_token, notification);
    return {
      status: 200,
      message: "User notification sent",
      data: response,
    };
  } catch (error) {
    console.error("Error in sendNotification:", error.message);

    // Check for invalid token errors
    if (error.response && error.response.status === 404) {
      return {
        status: 404,
        message: "Invalid FCM token",
        shouldRemoveToken: true,
        data: error,
      };
    }

    throw {
      status: 500,
      message: "Failed to send user notification",
      data: error,
    };
  }
}
async function sendNotificationToAll(data) {
  try {
    const access_token = await getAccessToken();
    var notification = JSON.stringify({
      message: {
        topic: "adtip",
        notification: {
          body: data.body,
          title: data.title,
          //imageUrl: data.imageUrl,
        },
        apns: {
          headers: {
            "apns-priority": "10",
          },
          payload: {
            aps: {
              sound: "default",
            },
          },
        },
      },
    });

    let response = await AxiosConfig(access_token, notification);
    return {
      status: 200,
      message: "User notification sent",
      data: response,
    };
  } catch (error) {
    console.error("Error in sendNotification:", error.message);
    throw {
      status: 500,
      message: "Failed to send user notification",
      data: error,
    };
  }
}

// Enhanced function specifically for chat notifications
async function sendChatNotification(recipientToken, senderName, messageContent, conversationId, senderId) {
  const notificationData = {
    token: recipientToken,
    title: senderName,
    body: messageContent.length > 100 ? messageContent.substring(0, 100) + '...' : messageContent,
    type: 'chat_message',
    conversationId: conversationId.toString(),
    senderId: senderId.toString(),
    senderName: senderName,
    timestamp: new Date().toISOString()
  };

  return await sendNotification(notificationData);
}

// Function to send notifications to multiple tokens
async function sendMulticastNotification(tokens, title, body, data = {}) {
  try {
    const access_token = await getAccessToken();

    // Send to each token individually (FCM HTTP v1 doesn't support multicast directly)
    const results = [];
    for (const token of tokens) {
      try {
        const result = await sendNotification({
          token,
          title,
          body,
          ...data
        });
        results.push({ token, success: true, result });
      } catch (error) {
        results.push({ token, success: false, error });
      }
    }

    return {
      status: 200,
      message: "Multicast notifications processed",
      results: results
    };
  } catch (error) {
    console.error("Error in sendMulticastNotification:", error.message);
    throw {
      status: 500,
      message: "Failed to send multicast notifications",
      data: error,
    };
  }
}

module.exports = {
  sendNotification,
  sendNotificationToAll,
  sendChatNotification,
  sendMulticastNotification
};
