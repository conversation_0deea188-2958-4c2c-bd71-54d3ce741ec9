{"logs": [{"outputFile": "com.callkeepexample.app-mergeDebugResources-27:/values-sl/values-sl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/625ed137c6a3f5343b71917adedb437c/transformed/appcompat-1.7.0/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,4548", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,4627"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/1d221509c521a46e74f6572064742339/transformed/react-android-0.80.1-debug/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,212,283,352,433,504,571,641,724,807,889,961,1035,1117,1194,1276,1358,1434,1512,1589,1673,1747,1829,1901", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "123,207,278,347,428,499,566,636,719,802,884,956,1030,1112,1189,1271,1353,1429,1507,1584,1668,1742,1824,1896,1978"}, "to": {"startLines": "29,37,38,39,40,41,42,43,44,45,46,47,48,50,51,52,53,54,55,56,57,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2845,3641,3725,3796,3865,3946,4017,4084,4154,4237,4320,4402,4474,4632,4714,4791,4873,4955,5031,5109,5186,5371,5445,5527,5599", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "2913,3720,3791,3860,3941,4012,4079,4149,4232,4315,4397,4469,4543,4709,4786,4868,4950,5026,5104,5181,5265,5440,5522,5594,5676"}}, {"source": "/Users/<USER>/.gradle/caches/8.14.1/transforms/a2623a5004a759cf5201db987dfa0e58/transformed/core-1.13.1/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "30,31,32,33,34,35,36,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2918,3015,3117,3215,3319,3422,3524,5270", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3010,3112,3210,3314,3417,3519,3636,5366"}}]}]}