
#ifndef BOOST_MPL_INSERT_RANGE_FWD_HPP_INCLUDED
#define BOOST_MPL_INSERT_RANGE_FWD_HPP_INCLUDED

// Copyright Aleksey Gurtovoy 2000-2004
//
// Distributed under the Boost Software License, Version 1.0. 
// (See accompanying file LICENSE_1_0.txt or copy at 
// http://www.boost.org/LICENSE_1_0.txt)
//
// See http://www.boost.org/libs/mpl for documentation.

// $Id$
// $Date$
// $Revision$

namespace boost { namespace mpl {

template< typename Tag > struct insert_range_impl;
template< typename Sequence, typename Pos, typename Range > struct insert_range;

}}

#endif // BOOST_MPL_INSERT_RANGE_FWD_HPP_INCLUDED
