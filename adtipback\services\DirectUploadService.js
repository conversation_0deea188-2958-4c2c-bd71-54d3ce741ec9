// adtipback/services/DirectUploadService.js
// Cloudflare Stream Direct Creator Uploads Implementation
// Reference: https://developers.cloudflare.com/stream/uploading-videos/direct-creator-uploads/

const CloudflareStreamService = require('./CloudflareStreamService');
const dbQuery = require('../dbConfig/queryRunner');

class DirectUploadService {
  /**
   * Create a direct upload URL for frontend video uploads
   * This allows users to upload directly to Cloudflare Stream without going through our backend
   * @param {Object} options - Upload configuration options
   * @returns {Promise<Object>} Upload URL and video ID
   */
  static async createDirectUploadUrl(options = {}) {
    try {
      const {
        maxDurationSeconds = 300, // 5 minutes default for TipShorts
        allowedOrigins = ['theadtip.in', 'localhost:3000'],
        requireSignedURLs = false,
        watermark = null,
        thumbnailTimestampPct = 0.5,
        metadata = {}
      } = options;

      console.log('[DirectUpload] Creating direct upload URL with options:', {
        maxDurationSeconds,
        allowedOrigins,
        requireSignedURLs,
        metadata
      });

      const streamService = CloudflareStreamService;
      const response = await streamService.createDirectUploadUrl({
        maxDurationSeconds,
        allowedOrigins,
        requireSignedURLs,
        watermark,
        thumbnailTimestampPct,
        meta: metadata
      });

      if (response.success) {
        const { uploadURL, uid } = response.result;
        
        console.log('[DirectUpload] Successfully created upload URL:', {
          uid,
          uploadURL: uploadURL.substring(0, 50) + '...'
        });

        return {
          success: true,
          data: {
            uploadURL,
            videoId: uid,
            maxDurationSeconds,
            allowedOrigins
          }
        };
      } else {
        console.error('[DirectUpload] Failed to create upload URL:', response.error);
        return {
          success: false,
          error: response.error || 'Failed to create upload URL'
        };
      }

    } catch (error) {
      console.error('[DirectUpload] Error creating upload URL:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a direct upload URL specifically for TipShorts
   * @param {Object} userInfo - User information for metadata
   * @returns {Promise<Object>} Upload URL and video ID
   */
  static async createTipShortsUploadUrl(userInfo = {}) {
    const metadata = {
      type: 'tipshorts',
      userId: userInfo.userId || '',
      userName: userInfo.userName || '',
      channelId: userInfo.channelId || '',
      uploadedAt: new Date().toISOString()
    };

    return await DirectUploadService.createDirectUploadUrl({
      maxDurationSeconds: 60, // 1 minute for shorts
      allowedOrigins: ['theadtip.in', 'localhost:3000'],
      requireSignedURLs: false,
      thumbnailTimestampPct: 0.3, // Thumbnail at 30% for shorts
      metadata
    });
  }

  /**
   * Create a direct upload URL specifically for TipTube videos
   * @param {Object} userInfo - User information for metadata
   * @returns {Promise<Object>} Upload URL and video ID
   */
  static async createTipTubeUploadUrl(userInfo = {}) {
    const metadata = {
      type: 'tiptube',
      userId: userInfo.userId || '',
      userName: userInfo.userName || '',
      channelId: userInfo.channelId || '',
      uploadedAt: new Date().toISOString()
    };

    return await DirectUploadService.createDirectUploadUrl({
      maxDurationSeconds: 1800, // 30 minutes for long-form videos
      allowedOrigins: ['theadtip.in', 'localhost:3000'],
      requireSignedURLs: false,
      thumbnailTimestampPct: 0.1, // Thumbnail at 10% for long videos
      metadata
    });
  }

  /**
   * Pre-register a video in our database before upload
   * This creates a placeholder record that will be updated via webhook
   * @param {Object} videoData - Video information
   * @returns {Promise<Object>} Database record result
   */
  static async preRegisterVideo(videoData) {
    try {
      const {
        streamVideoId,
        name,
        description,
        categoryId,
        createdBy,
        videoChannel,
        isShot = true,
        thumbnailUrl = null
      } = videoData;

      console.log('[DirectUpload] Pre-registering video:', {
        streamVideoId,
        name,
        isShot
      });

      const sql = `
        INSERT INTO reels (
          stream_video_id,
          name,
          video_desciption,
          category_id,
          createdby,
          video_channel,
          is_shot,
          stream_status,
          video_Thumbnail,
          total_views,
          total_likes,
          createddate
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'uploading', ?, 0, 0, NOW())
      `;

      const params = [
        streamVideoId,
        name,
        description || '',
        categoryId || 1,
        createdBy,
        videoChannel,
        isShot ? 1 : 0,
        thumbnailUrl || ''
      ];

      const result = await dbQuery.queryRunner(sql, params);

      if (result.insertId) {
        console.log('[DirectUpload] Successfully pre-registered video:', result.insertId);
        return {
          success: true,
          data: {
            id: result.insertId,
            streamVideoId
          }
        };
      } else {
        throw new Error('Failed to insert video record');
      }

    } catch (error) {
      console.error('[DirectUpload] Error pre-registering video:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get upload status for a video
   * @param {string} videoId - Stream video ID
   * @returns {Promise<Object>} Upload status
   */
  static async getUploadStatus(videoId) {
    try {
      const streamService = CloudflareStreamService;
      const response = await streamService.getVideoStatus(videoId);

      if (response.success) {
        return {
          success: true,
          data: response.result
        };
      } else {
        return {
          success: false,
          error: response.error
        };
      }

    } catch (error) {
      console.error('[DirectUpload] Error getting upload status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Complete the upload process after webhook confirmation
   * @param {string} streamVideoId - Stream video ID
   * @param {Object} streamData - Data from Stream webhook
   * @returns {Promise<Object>} Completion result
   */
  static async completeUpload(streamVideoId, streamData) {
    try {
      const {
        duration,
        meta,
        playback,
        thumbnail
      } = streamData;

      console.log('[DirectUpload] Completing upload for:', streamVideoId);

      // Update database with final video information
      const sql = `
        UPDATE reels 
        SET 
          stream_status = 'ready',
          stream_ready_at = NOW(),
          stream_duration = ?,
          adaptive_manifest_url = ?,
          video_Thumbnail = COALESCE(video_Thumbnail, ?),
          video_link = ?
        WHERE stream_video_id = ?
      `;

      const params = [
        duration || null,
        playback?.hls || null,
        thumbnail || null,
        playback?.hls || null, // Use HLS as fallback video_link
        streamVideoId
      ];

      const result = await dbQuery.queryRunner(sql, params);

      if (result.affectedRows > 0) {
        console.log('[DirectUpload] Successfully completed upload for:', streamVideoId);
        return {
          success: true,
          data: {
            streamVideoId,
            affectedRows: result.affectedRows
          }
        };
      } else {
        throw new Error('No records updated - video not found');
      }

    } catch (error) {
      console.error('[DirectUpload] Error completing upload:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = DirectUploadService;
