const { queryRunner } = require('../dbConfig/queryRunner');
const moment = require('moment');

exports.manualAddFunds = async (userId, amount, note) => {
  // 1. Get previous balance
  const getBalanceQuery = 'SELECT totalBalance FROM wallet WHERE createdby = ? ORDER BY id DESC LIMIT 1';
  const prevRows = await queryRunner(getBalanceQuery, [userId]);
  const prevBalance = prevRows.length > 0 ? parseFloat(prevRows[0].totalBalance) : 0;
  const newBalance = prevBalance + parseFloat(amount);
  const now = moment().utcOffset(330).format('YYYY-MM-DD HH:mm:ss');

  // 2. Insert new wallet row (like a deposit)
  const insertQuery = `
    INSERT INTO wallet (amount, transaction_type, createdby, paid_status, call_transaction_id, createddate, totalBalance, order_id, payment_id, description)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;
  const result = await queryRunner(insertQuery, [
    amount,
    'Deposite',
    userId,
    'Paid',
    null,
    now,
    newBalance,
    null,
    null,
    note || 'Manual credit',
  ]);
  return { userId, amount, prevBalance, newBalance, walletId: result.insertId, note };
}; 