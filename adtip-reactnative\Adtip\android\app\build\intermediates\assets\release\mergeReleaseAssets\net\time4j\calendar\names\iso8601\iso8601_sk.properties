# months
M(a)_1=jan
<PERSON>(a)_2=feb
M(a)_3=mar
M(a)_4=apr
M(a)_5=máj
M(a)_6=jún
M(a)_7=júl
M(a)_8=aug
M(a)_9=sep
M(a)_10=okt
M(a)_11=nov
M(a)_12=dec

M(n)_1=j
M(n)_2=f
M(n)_3=m
M(n)_4=a
M(n)_5=m
M(n)_6=j
M(n)_7=j
M(n)_8=a
M(n)_9=s
M(n)_10=o
M(n)_11=n
M(n)_12=d

M(w)_1=januára
M(w)_2=februára
M(w)_3=marca
M(w)_4=apríla
M(w)_5=mája
M(w)_6=júna
M(w)_7=júla
M(w)_8=augusta
M(w)_9=septembra
M(w)_10=októbra
M(w)_11=novembra
M(w)_12=decembra

M(A)_1=jan
M(A)_2=feb
M(A)_3=mar
M(A)_4=apr
M(A)_5=máj
M(A)_6=jún
M(A)_7=júl
M(A)_8=aug
M(A)_9=sep
M(A)_10=okt
M(A)_11=nov
M(A)_12=dec

M(N)_1=j
M(N)_2=f
M(N)_3=m
M(N)_4=a
M(N)_5=m
M(N)_6=j
M(N)_7=j
M(N)_8=a
M(N)_9=s
M(N)_10=o
M(N)_11=n
M(N)_12=d

M(W)_1=január
M(W)_2=február
M(W)_3=marec
M(W)_4=apríl
M(W)_5=máj
M(W)_6=jún
M(W)_7=júl
M(W)_8=august
M(W)_9=september
M(W)_10=október
M(W)_11=november
M(W)_12=december

# weekdays
D(a)_1=po
D(a)_2=ut
D(a)_3=st
D(a)_4=št
D(a)_5=pi
D(a)_6=so
D(a)_7=ne

D(n)_1=p
D(n)_2=u
D(n)_3=s
D(n)_4=š
D(n)_5=p
D(n)_6=s
D(n)_7=n

D(s)_1=po
D(s)_2=ut
D(s)_3=st
D(s)_4=št
D(s)_5=pi
D(s)_6=so
D(s)_7=ne

D(w)_1=pondelok
D(w)_2=utorok
D(w)_3=streda
D(w)_4=štvrtok
D(w)_5=piatok
D(w)_6=sobota
D(w)_7=nedeľa

D(A)_1=po
D(A)_2=ut
D(A)_3=st
D(A)_4=št
D(A)_5=pi
D(A)_6=so
D(A)_7=ne

D(N)_1=p
D(N)_2=u
D(N)_3=s
D(N)_4=š
D(N)_5=p
D(N)_6=s
D(N)_7=n

D(S)_1=po
D(S)_2=ut
D(S)_3=st
D(S)_4=št
D(S)_5=pi
D(S)_6=so
D(S)_7=ne

D(W)_1=pondelok
D(W)_2=utorok
D(W)_3=streda
D(W)_4=štvrtok
D(W)_5=piatok
D(W)_6=sobota
D(W)_7=nedeľa

# quarters
Q(a)_1=Q1
Q(a)_2=Q2
Q(a)_3=Q3
Q(a)_4=Q4

Q(n)_1=1
Q(n)_2=2
Q(n)_3=3
Q(n)_4=4

Q(w)_1=1. štvrťrok
Q(w)_2=2. štvrťrok
Q(w)_3=3. štvrťrok
Q(w)_4=4. štvrťrok

Q(A)_1=Q1
Q(A)_2=Q2
Q(A)_3=Q3
Q(A)_4=Q4

Q(N)_1=1
Q(N)_2=2
Q(N)_3=3
Q(N)_4=4

Q(W)_1=1. štvrťrok
Q(W)_2=2. štvrťrok
Q(W)_3=3. štvrťrok
Q(W)_4=4. štvrťrok

# day-period-rules
T0400=morning1
T0900=morning2
T1200=afternoon1
T1800=evening1
T2200=night1

# day-period-translations
P(a)_midnight=o poln.
P(a)_noon=napol.
P(a)_morning1=ráno
P(a)_morning2=dopol.
P(a)_afternoon1=popol.
P(a)_evening1=večer
P(a)_night1=v noci

P(n)_midnight=o poln.
P(n)_noon=nap.
P(n)_morning1=ráno
P(n)_morning2=dop.
P(n)_afternoon1=pop.
P(n)_evening1=več.
P(n)_night1=v n.

P(w)_midnight=o polnoci
P(w)_noon=napoludnie
P(w)_morning1=ráno
P(w)_morning2=dopoludnia
P(w)_afternoon1=popoludní
P(w)_evening1=večer
P(w)_night1=v noci

P(A)_midnight=poln.
P(A)_am=AM
P(A)_noon=pol.
P(A)_pm=PM
P(A)_morning1=ráno
P(A)_morning2=dopol.
P(A)_afternoon1=popol.
P(A)_evening1=večer
P(A)_night1=noc

P(N)_midnight=poln.
P(N)_am=AM
P(N)_noon=pol.
P(N)_pm=PM
P(N)_morning1=ráno
P(N)_morning2=dop.
P(N)_afternoon1=pop.
P(N)_evening1=več.
P(N)_night1=noc

P(W)_midnight=polnoc
P(W)_am=AM
P(W)_noon=poludnie
P(W)_pm=PM
P(W)_morning1=ráno
P(W)_morning2=dopoludnie
P(W)_afternoon1=popoludnie
P(W)_evening1=večer
P(W)_night1=noc

# eras
E(w)_0=pred Kristom
E(w|alt)_0=pred naším letopočtom
E(w)_1=po Kristovi
E(w|alt)_1=nášho letopočtu

E(a)_0=pred Kr.
E(a|alt)_0=pred n. l.
E(a)_1=po Kr.
E(a|alt)_1=n. l.

# format patterns
F(f)_d=EEEE d. MMMM y
F(l)_d=d. MMMM y
F(m)_d=d. M. y
F(s)_d=d. M. y

F(alt)=H:mm:ss

F(f)_t=H:mm:ss zzzz
F(l)_t=H:mm:ss z
F(m)_t=H:mm:ss
F(s)_t=H:mm

F(f)_dt={1}, {0}
F(l)_dt={1}, {0}
F(m)_dt={1}, {0}
F(s)_dt={1} {0}

F_Bh=h B
F_Bhm=h:mm B
F_Bhms=h:mm:ss B
F_h=h a
F_H=H
F_hm=h:mm a
F_Hm=H:mm
F_hms=h:mm:ss a
F_Hms=H:mm:ss

F_Md=d. M.
F_MMMd=d. M.
F_MMMMd=d. MMMM
F_y=y
F_yM=M/y
F_yMMM=M/y
F_yMMMM=LLLL y
F_yQQQ=QQQ y
F_yQQQQ=QQQQ y
F_yw=w. 'týždeň' 'roka' Y

I={0} – {1}

# labels of elements
L_era=letopočet
L_year=rok
L_quarter=štvrťrok
L_month=mesiac
L_week=týždeň
L_day=deň
L_weekday=deň týždňa
L_dayperiod=AM/PM
L_hour=hodina
L_minute=minúta
L_second=sekunda
L_zone=časové pásmo
