const express = require("express");
// eslint-disable-next-line new-cap
const router = express.Router();
const {v4: uuidv4} = require("uuid");
const admin = require("firebase-admin");

router.post("/initiate-call", async (req, res) => {
  const {calleeInfo, callerInfo, videoSDKInfo} = req.body;

  // Validate request body
  if (!calleeInfo || !calleeInfo.token || !callerInfo || !videoSDKInfo) {
    return res.status(400).json({error: "Missing required fields"});
  }

  const info = JSON.stringify({
    callerInfo,
    videoSDKInfo,
    type: "CALL_INITIATED",
    uuid: uuidv4(),
  });

  const message = {
    data: {
      info,
    },
    token: calleeInfo.token,
  };

  // Platform-specific configuration
  if (calleeInfo.platform === "ANDROID") {
    message.android = {
      priority: "high",
    };
  } else if (calleeInfo.platform === "IOS") {
    message.apns = {
      headers: {
        "apns-priority": "10",
      },
      payload: {
        aps: {
          badge: 1,
          sound: "default",
        },
      },
    };
  } else {
    return res.status(400).json({error: "Unsupported platform"});
  }

  try {
    // Send message
    const response = await admin.messaging().send(message);
    res.status(200).json({messageId: response});
  } catch (error) {
    console.error("FCM Error:", error);
    if (error.code === "messaging/invalid-registration-token" ||
        error.code === "messaging/registration-token-not-registered") {
      return res.status(400).json({error: "Invalid or unregistered FCM token"});
    }
    res.status(500).json({error: error.message});
  }
});

router.post("/update-call", async (req, res) => {
  const {callerInfo, type} = req.body;

  // Validate request body
  if (!callerInfo || !callerInfo.token || !type) {
    return res.status(400).json({error: "Missing required fields"});
  }

  const info = JSON.stringify({
    callerInfo,
    type,
  });

  const message = {
    data: {
      info,
    },
    token: callerInfo.token,
  };

  // Platform-specific configuration
  if (callerInfo.platform === "IOS") {
    message.apns = {
      headers: {
        "apns-priority": "10",
      },
      payload: {
        aps: {
          badge: 1,
          sound: "default",
        },
      },
    };
  } else if (callerInfo.platform === "ANDROID") {
    message.android = {
      priority: "high",
    };
  } else {
    return res.status(400).json({error: "Unsupported platform"});
  }

  try {
    // Send message
    const response = await admin.messaging().send(message);
    res.status(200).json({messageId: response});
  } catch (error) {
    console.error("FCM Error:", error);
    if (error.code === "messaging/invalid-registration-token" ||
        error.code === "messaging/registration-token-not-registered") {
      return res.status(400).json({error: "Invalid or unregistered FCM token"});
    }
    res.status(500).json({error: error.message});
  }
});

module.exports = router;
