// adtipback/scripts/executeStreamMigration.js
// Execute the Stream migration with intelligent video matching

require('dotenv').config();
const dbQuery = require('../dbConfig/queryRunner');
const fs = require('fs');

async function executeStreamMigration() {
  console.log('🚀 EXECUTING CLOUDFLARE STREAM MIGRATION');
  console.log('========================================\n');

  try {
    // Load the generated mapping file
    const mappingFiles = fs.readdirSync('.').filter(f => f.startsWith('stream_mapping_') && f.endsWith('.json'));
    if (mappingFiles.length === 0) {
      throw new Error('No stream mapping file found. Please run generateStreamMigrationSQL.js first.');
    }

    const latestMappingFile = mappingFiles.sort().pop();
    console.log(`📂 Loading mapping file: ${latestMappingFile}`);
    
    const mappingData = JSON.parse(fs.readFileSync(latestMappingFile, 'utf8'));
    const streamVideos = mappingData.videos;
    
    console.log(`📊 Found ${streamVideos.length} ready Stream videos to migrate`);

    // Get current database videos
    console.log('\n📋 Fetching database videos...');
    const dbVideos = await dbQuery.queryRunner(`
      SELECT 
        id,
        name,
        video_link,
        video_Thumbnail,
        video_desciption,
        total_views,
        total_likes,
        video_channel,
        stream_video_id,
        stream_status,
        createddate,
        is_shot
      FROM reels 
      WHERE is_active = 1 
        AND (stream_video_id IS NULL OR stream_status != 'ready')
      ORDER BY total_views DESC, createddate DESC
      LIMIT 500
    `);

    console.log(`📊 Found ${dbVideos.length} database videos needing Stream IDs`);

    // Match videos intelligently
    console.log('\n🔍 Matching videos...');
    const matches = [];
    const usedStreamIds = new Set();

    for (const dbVideo of dbVideos) {
      const match = findBestMatch(dbVideo, streamVideos, usedStreamIds);
      if (match) {
        matches.push({
          dbVideo,
          streamVideo: match.streamVideo,
          confidence: match.confidence,
          reason: match.reason
        });
        usedStreamIds.add(match.streamVideo.uid);
        
        console.log(`✅ Match found: DB ${dbVideo.id} -> Stream ${match.streamVideo.uid} (${match.confidence}% - ${match.reason})`);
      }
    }

    console.log(`\n🎯 Found ${matches.length} confident matches`);

    if (matches.length === 0) {
      console.log('❌ No matches found. Cannot proceed with migration.');
      return;
    }

    // Ask for confirmation
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const question = (query) => new Promise(resolve => rl.question(query, resolve));

    console.log('\n⚠️  MIGRATION CONFIRMATION:');
    console.log(`📊 Ready to update ${matches.length} videos`);
    console.log('🔍 Sample matches:');
    matches.slice(0, 5).forEach((match, index) => {
      console.log(`${index + 1}. DB Video "${match.dbVideo.name}" -> Stream "${match.streamVideo.filename || match.streamVideo.uid}"`);
    });

    const proceed = await question('\nProceed with migration? (yes/no): ');
    rl.close();

    if (proceed.toLowerCase() !== 'yes') {
      console.log('❌ Migration cancelled by user');
      return;
    }

    // Execute migration
    console.log('\n🔄 Executing migration...');
    let successCount = 0;
    let errorCount = 0;

    for (const match of matches) {
      try {
        const { dbVideo, streamVideo } = match;
        const manifestUrl = `https://customer-${mappingData.customerCode}.cloudflarestream.com/${streamVideo.uid}/manifest/video.m3u8`;
        
        await dbQuery.queryRunner(`
          UPDATE reels SET 
            stream_video_id = ?,
            stream_status = 'ready',
            adaptive_manifest_url = ?,
            stream_ready_at = NOW()
          WHERE id = ?
        `, [streamVideo.uid, manifestUrl, dbVideo.id]);

        successCount++;
        console.log(`✅ Updated video ${dbVideo.id} with Stream ID ${streamVideo.uid}`);

      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to update video ${match.dbVideo.id}:`, error.message);
      }
    }

    console.log('\n📊 MIGRATION RESULTS:');
    console.log(`✅ Successfully updated: ${successCount} videos`);
    console.log(`❌ Failed updates: ${errorCount} videos`);
    console.log(`📈 Success rate: ${((successCount / matches.length) * 100).toFixed(1)}%`);

    // Verify results
    console.log('\n🔍 Verifying migration...');
    const verificationResults = await dbQuery.queryRunner(`
      SELECT 
        COUNT(*) as total_with_stream,
        SUM(CASE WHEN stream_status = 'ready' THEN 1 ELSE 0 END) as ready_count
      FROM reels 
      WHERE stream_video_id IS NOT NULL
    `);

    console.log(`📊 Total videos with Stream IDs: ${verificationResults[0].total_with_stream}`);
    console.log(`✅ Ready Stream videos: ${verificationResults[0].ready_count}`);

    console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    return {
      success: true,
      updated: successCount,
      failed: errorCount,
      totalStreamVideos: verificationResults[0].ready_count
    };

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    throw error;
  }
}

/**
 * Find best match between database video and stream videos
 */
function findBestMatch(dbVideo, streamVideos, usedStreamIds) {
  let bestMatch = null;
  let bestScore = 0;
  let bestReason = '';

  for (const streamVideo of streamVideos) {
    if (usedStreamIds.has(streamVideo.uid)) continue;

    // Check if video_link contains the stream UID
    if (dbVideo.video_link && dbVideo.video_link.includes(streamVideo.uid)) {
      return {
        streamVideo,
        confidence: 100,
        reason: 'Stream UID found in video_link'
      };
    }

    // Check filename matching
    if (streamVideo.filename && dbVideo.name) {
      const streamFilename = streamVideo.filename.replace(/\.[^/.]+$/, ''); // Remove extension
      const dbName = dbVideo.name;
      
      if (streamFilename === dbName) {
        return {
          streamVideo,
          confidence: 95,
          reason: 'Exact filename match'
        };
      }

      if (dbName.includes(streamFilename) || streamFilename.includes(dbName)) {
        const score = 85;
        if (score > bestScore) {
          bestScore = score;
          bestMatch = streamVideo;
          bestReason = 'Partial filename match';
        }
      }
    }

    // Check metadata name matching
    if (streamVideo.meta && streamVideo.meta.name && dbVideo.name) {
      const metaName = streamVideo.meta.name.replace(/\.[^/.]+$/, '');
      if (metaName === dbVideo.name) {
        const score = 90;
        if (score > bestScore) {
          bestScore = score;
          bestMatch = streamVideo;
          bestReason = 'Metadata name match';
        }
      }
    }

    // Check if numeric IDs match (for files like "1000011515.mp4")
    if (streamVideo.filename) {
      const streamNumeric = streamVideo.filename.match(/\d+/);
      const dbNumeric = dbVideo.name ? dbVideo.name.match(/\d+/) : null;
      
      if (streamNumeric && dbNumeric && streamNumeric[0] === dbNumeric[0] && streamNumeric[0].length > 6) {
        const score = 80;
        if (score > bestScore) {
          bestScore = score;
          bestMatch = streamVideo;
          bestReason = 'Numeric ID match';
        }
      }
    }
  }

  // Only return matches with confidence >= 75%
  if (bestScore >= 75) {
    return {
      streamVideo: bestMatch,
      confidence: bestScore,
      reason: bestReason
    };
  }

  return null;
}

// Run if called directly
if (require.main === module) {
  executeStreamMigration()
    .then(result => {
      if (result) {
        console.log('\n🎉 Migration completed successfully!');
        console.log(`📊 Updated ${result.updated} videos`);
        console.log(`📈 Total Stream videos: ${result.totalStreamVideos}`);
      }
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = { executeStreamMigration };
