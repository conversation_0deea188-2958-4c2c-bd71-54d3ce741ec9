{"name": "React-jsinspectornetwork", "version": "0.80.1", "summary": "Network inspection for React Native DevTools", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.1"}, "source_files": "*.{cpp,h}", "header_dir": "jsinspector-modern/network", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectorcdp/jsinspector_moderncdp.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "DEFINES_MODULE": "YES"}, "dependencies": {"React-jsinspectorcdp": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}