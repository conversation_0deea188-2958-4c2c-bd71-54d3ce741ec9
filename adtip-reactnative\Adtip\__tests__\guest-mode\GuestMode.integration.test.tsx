import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthProvider } from '../../src/contexts/AuthContext';
import { ThemeProvider } from '../../src/contexts/ThemeContext';
import OnboardingScreen from '../../src/screens/auth/OnboardingScreen';
import HomeScreen from '../../src/screens/home/<USER>';

// Mock dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

jest.mock('../../src/services/ApiService', () => ({
  getListPremiumPosts: jest.fn(),
  getPublicVideos: jest.fn(),
  getPublicShots: jest.fn(),
  verifyOtp: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    replace: jest.fn(),
    reset: jest.fn(),
  }),
  useRoute: () => ({
    name: 'Home',
  }),
  useFocusEffect: jest.fn(),
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
  SafeAreaProvider: ({ children }: any) => children,
  SafeAreaView: ({ children }: any) => children,
}));

// Mock other required modules
jest.mock('../../src/contexts/WalletContext', () => ({
  useWallet: () => ({
    balance: '0.00',
    isLoading: false,
    isPremium: false,
  }),
}));

jest.mock('../../src/contexts/SidebarContext', () => ({
  useSidebar: () => ({
    toggleSidebar: jest.fn(),
  }),
}));

jest.mock('../../src/hooks/useQueries', () => ({
  useGuestPosts: () => ({
    data: {
      data: [
        {
          id: 1,
          title: 'Test Guest Post',
          content: 'Test content',
        },
      ],
    },
    isLoading: false,
    error: null,
    refetch: jest.fn(),
  }),
  usePosts: () => ({
    data: { pages: [] },
    isLoading: false,
    error: null,
    refetch: jest.fn(),
    fetchNextPage: jest.fn(),
    hasNextPage: false,
  }),
  useCategories: () => ({
    data: [],
    isLoading: false,
    error: null,
  }),
  useLikeMutation: () => ({
    mutate: jest.fn(),
  }),
  useFollowMutation: () => ({
    mutate: jest.fn(),
  }),
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider>
    <AuthProvider>
      {children}
    </AuthProvider>
  </ThemeProvider>
);

describe('Guest Mode Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
    (AsyncStorage.removeItem as jest.Mock).mockResolvedValue(undefined);
  });

  describe('Guest Mode Activation Flow', () => {
    it('should allow user to enter guest mode from onboarding', async () => {
      const mockNavigation = {
        navigate: jest.fn(),
        replace: jest.fn(),
      };

      const { getByText } = render(
        <TestWrapper>
          <OnboardingScreen navigation={mockNavigation as any} route={{} as any} />
        </TestWrapper>
      );

      // Wait for component to load
      await waitFor(() => {
        expect(getByText('Try Now')).toBeTruthy();
      });

      // Click Try Now button
      fireEvent.press(getByText('Try Now'));

      // Verify guest mode was activated (no longer persisted to AsyncStorage)
      await waitFor(() => {
        // Guest mode should be activated in memory but not persisted
        expect(AsyncStorage.setItem).not.toHaveBeenCalledWith('@guest_mode', 'true');
      });
    });
  });

  describe('Guest Mode Content Access', () => {
    it('should show guest content in HomeScreen', async () => {
      // Since guest mode is no longer persisted, we need to manually set guest state
      // This test would need to be restructured to test guest mode within a session

      const mockApiService = require('../../src/services/ApiService');
      mockApiService.getListPremiumPosts.mockResolvedValue({
        data: [
          {
            id: 1,
            title: 'Guest Post 1',
            content: 'Guest content 1',
          },
        ],
      });

      const { getByText } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Wait for guest content to load
      await waitFor(() => {
        expect(mockApiService.getListPremiumPosts).toHaveBeenCalled();
      });
    });
  });

  describe('Guest Mode Restrictions', () => {
    it('should show login prompt when guest tries to like a post', async () => {
      // Mock guest mode
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === '@guest_mode') return Promise.resolve('true');
        return Promise.resolve(null);
      });

      const { getByText, queryByText } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Wait for component to load
      await waitFor(() => {
        // Component should be rendered
        expect(queryByText('Login to unlock all features')).toBeFalsy();
      });

      // Note: In a real test, you would simulate clicking a like button
      // and verify that the LoginPromptModal appears
    });
  });

  describe('Guest Mode State Persistence', () => {
    it('should persist guest mode across app restarts', async () => {
      // Mock guest mode in storage
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === '@guest_mode') return Promise.resolve('true');
        return Promise.resolve(null);
      });

      const { rerender } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Simulate app restart by re-rendering
      rerender(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Verify guest mode is still active
      await waitFor(() => {
        expect(AsyncStorage.getItem).toHaveBeenCalledWith('@guest_mode');
      });
    });
  });

  describe('Guest Mode Exit Flow', () => {
    it('should exit guest mode and navigate to login', async () => {
      // Mock guest mode
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === '@guest_mode') return Promise.resolve('true');
        return Promise.resolve(null);
      });

      const { getByText } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Wait for component to load and show login prompt
      await waitFor(() => {
        // In a real scenario, you would trigger a restricted action
        // that shows the LoginPromptModal, then click the Login button
      });

      // Verify guest mode cleanup
      // expect(AsyncStorage.removeItem).toHaveBeenCalledWith('@guest_mode');
    });
  });

  describe('API Integration', () => {
    it('should call guest APIs without authentication headers', async () => {
      // Mock guest mode
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === '@guest_mode') return Promise.resolve('true');
        return Promise.resolve(null);
      });

      const mockApiService = require('../../src/services/ApiService');
      mockApiService.getListPremiumPosts.mockResolvedValue({
        data: [],
      });

      render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockApiService.getListPremiumPosts).toHaveBeenCalled();
      });
    });

    it('should handle API errors gracefully in guest mode', async () => {
      // Mock guest mode
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === '@guest_mode') return Promise.resolve('true');
        return Promise.resolve(null);
      });

      const mockApiService = require('../../src/services/ApiService');
      mockApiService.getListPremiumPosts.mockRejectedValue(new Error('API Error'));

      const { queryByText } = render(
        <TestWrapper>
          <HomeScreen />
        </TestWrapper>
      );

      // Component should still render even if API fails
      await waitFor(() => {
        expect(queryByText('Home')).toBeTruthy();
      });
    });
  });
});
