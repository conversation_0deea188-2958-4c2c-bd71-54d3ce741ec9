# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    [gap of 92ms]
    exec-prefab 2097ms
    [gap of 203ms]
  generate-prefab-packages completed in 2392ms
  execute-generate-process
    [gap of 40ms]
    exec-configure 17323ms
    [gap of 223ms]
  execute-generate-process completed in 17586ms
  [gap of 32ms]
  remove-unexpected-so-files 42ms
  [gap of 127ms]
  write-metadata-json-to-file 19ms
  [gap of 17ms]
generate_cxx_metadata completed in 20300ms

