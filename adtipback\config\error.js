exports.unexpectedErrorHandler = () => {
  process.on("uncaughtException", (error) => {
    console.error(`uncaughtException: ${error.message}`)
  })

  process.on("unhandledRejection", (error) => {
    console.error(`unhandledRejection: ${error.message}`)
  })
}

exports.apiErrorHandler = (err, req, res, next) => {
  const {
    status = err.status || 500,
    statusText = err.message || err || "Internal Server Error",
    code = err.code || undefined,
  } = err?.response || {}

  console.error(`(${status}) => ${statusText}`)

  res.status(status).send({
    message: (err instanceof Error) ? "Internal Server Error" : statusText,
    status: code || status,
    data: null,
  })
}

