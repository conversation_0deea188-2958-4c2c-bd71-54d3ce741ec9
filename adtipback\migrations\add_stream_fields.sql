-- Migration script: Add Cloudflare Stream fields to reels table
-- Date: 2025-01-25
-- Purpose: Enable dual storage (R2 + Stream) for video optimization

-- Add Stream-related columns to reels table
ALTER TABLE reels ADD COLUMN stream_video_id VARCHAR(255) NULL COMMENT 'Cloudflare Stream video ID';
ALTER TABLE reels ADD COLUMN stream_status ENUM('pending', 'uploading', 'ready', 'error') DEFAULT 'pending' COMMENT 'Stream video processing status';
ALTER TABLE reels ADD COLUMN adaptive_manifest_url VARCHAR(500) NULL COMMENT 'HLS/DASH manifest URL for adaptive streaming';
ALTER TABLE reels ADD COLUMN stream_created_at TIMESTAMP NULL COMMENT 'When video was uploaded to Stream';
ALTER TABLE reels ADD COLUMN stream_ready_at TIMESTAMP NULL COMMENT 'When video encoding completed';
ALTER TABLE reels ADD COLUMN stream_duration DECIMAL(10,2) NULL COMMENT 'Video duration in seconds from Stream';
ALTER TABLE reels ADD COLUMN stream_size_bytes BIGINT NULL COMMENT 'Video file size in bytes';

-- Add indexes for efficient Stream queries
CREATE INDEX idx_reels_stream_video_id ON reels(stream_video_id);
CREATE INDEX idx_reels_stream_status ON reels(stream_status);
CREATE INDEX idx_reels_stream_ready ON reels(stream_status, stream_ready_at);

-- Add index for hybrid queries (R2 + Stream)
CREATE INDEX idx_reels_video_sources ON reels(stream_video_id, video_link, is_active);

-- Update existing records to have 'pending' stream status
UPDATE reels SET stream_status = 'pending' WHERE stream_status IS NULL;

-- Verify the migration
SELECT 
    COUNT(*) as total_reels,
    COUNT(stream_video_id) as with_stream_id,
    COUNT(CASE WHEN stream_status = 'ready' THEN 1 END) as stream_ready,
    COUNT(CASE WHEN stream_status = 'pending' THEN 1 END) as stream_pending
FROM reels;

-- Show sample of updated structure
DESCRIBE reels;
