# months
M(w)_1=janeiro

# weekdays
D(a)_1=segunda
D(a)_2=terça
D(a)_3=quarta
D(a)_4=quinta
D(a)_5=sexta
D(a)_6=sábado
D(a)_7=domingo

D(n)_1=S
D(n)_2=T
D(n)_3=Q
D(n)_4=Q
D(n)_5=S
D(n)_6=S
D(n)_7=D

D(A)_1=segunda
D(A)_2=terça
D(A)_3=quarta
D(A)_4=quinta
D(A)_5=sexta
D(A)_6=sábado
D(A)_7=domingo

D(N)_1=S
D(N)_2=T
D(N)_3=Q
D(N)_4=Q
D(N)_5=S
D(N)_6=S
D(N)_7=D

# quarters
Q(w)_1=1.º trimestre
Q(w)_2=2.º trimestre
Q(w)_3=3.º trimestre
Q(w)_4=4.º trimestre

Q(W)_1=1.º trimestre
Q(W)_2=2.º trimestre
Q(W)_3=3.º trimestre
Q(W)_4=4.º trimestre

# day-period-translations
P(a)_am=a.m.
P(a)_noon=meio-dia
P(a)_pm=p.m.

P(n)_midnight=meia-noite
P(n)_am=a.m.
P(n)_noon=meio-dia
P(n)_pm=p.m.
P(n)_morning1=manhã
P(n)_afternoon1=tarde
P(n)_evening1=noite
P(n)_night1=madrugada

P(w)_am=da manhã
P(w)_noon=meio-dia
P(w)_pm=da tarde
P(w)_morning1=da manhã
P(w)_afternoon1=da tarde
P(w)_evening1=da noite

P(A)_am=a.m.
P(A)_pm=p.m.

P(N)_am=a.m.
P(N)_pm=p.m.

P(W)_am=manhã
P(W)_pm=tarde

# eras
E(a|alt)_0=a.E.C.
E(a|alt)_1=E.C.

# format patterns
F(m)_d=dd/MM/y
F(s)_d=dd/MM/yy

F(alt)=HH'H'mm'm'

F(f)_t=HH:mm:ss zzzz
F(l)_t=HH:mm:ss z
F(m)_t=HH:mm:ss
F(s)_t=HH:mm

F(f)_dt={1} 'às' {0}
F(l)_dt={1} 'às' {0}
F(m)_dt={1}, {0}
F(s)_dt={1}, {0}

F_Md=dd/MM
F_MMMd=d/MM
F_yMMM=MM/y
F_yQQQ=QQQQ 'de' y
F_yw=w.'ª' 'semana' 'de' Y

# labels of elements
L_dayperiod=am/pm
