/**
 * This code was generated by [React Native](https://www.npmjs.com/package/@react-native/gradle-plugin).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 */

#include "autolinking.h"
#include <RNFastImageSpec.h>
#include <react/renderer/components/RNFastImageSpec/ComponentDescriptors.h>
#include <rnasyncstorage.h>
#include <rnclipboard.h>
#include <rnblurview.h>
#include <react/renderer/components/rnblurview/ComponentDescriptors.h>
#include <RNDateTimePickerCGen.h>
#include <rnskia.h>
#include <react/renderer/components/rnskia/ComponentDescriptors.h>
#include <lottiereactnative.h>
#include <react/renderer/components/lottiereactnative/ComponentDescriptors.h>
#include <Compressor.h>
#include <RNDatePickerSpecs.h>
#include <react/renderer/components/RNDatePickerSpecs/ComponentDescriptors.h>
#include <rngesturehandler_codegen.h>
#include <react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.h>
#include <RNGoogleMobileAdsSpec.h>
#include <react/renderer/components/RNGoogleMobileAdsSpec/ComponentDescriptors.h>
#include <RNCImageCropPickerSpec.h>
#include <RNImagePickerSpec.h>
#include <pagerview.h>
#include <react/renderer/components/pagerview/ComponentDescriptors.h>
#include <RNPermissionsSpec.h>
#include <rnreanimated.h>
#include <safeareacontext.h>
#include <react/renderer/components/safeareacontext/ComponentDescriptors.h>
#include <rnscreens.h>
#include <react/renderer/components/rnscreens/ComponentDescriptors.h>
#include <rnsvg.h>
#include <react/renderer/components/rnsvg/ComponentDescriptors.h>
#include <RNVectorIconsSpec.h>
#include <rnviewshot.h>
#include <RNCWebViewSpec.h>
#include <react/renderer/components/RNCWebViewSpec/ComponentDescriptors.h>

namespace facebook {
namespace react {

std::shared_ptr<TurboModule> autolinking_ModuleProvider(const std::string moduleName, const JavaTurboModule::InitParams &params) {
auto module_RNFastImageSpec = RNFastImageSpec_ModuleProvider(moduleName, params);
if (module_RNFastImageSpec != nullptr) {
return module_RNFastImageSpec;
}
auto module_rnasyncstorage = rnasyncstorage_ModuleProvider(moduleName, params);
if (module_rnasyncstorage != nullptr) {
return module_rnasyncstorage;
}
auto module_rnclipboard = rnclipboard_ModuleProvider(moduleName, params);
if (module_rnclipboard != nullptr) {
return module_rnclipboard;
}
auto module_rnblurview = rnblurview_ModuleProvider(moduleName, params);
if (module_rnblurview != nullptr) {
return module_rnblurview;
}
auto module_RNDateTimePickerCGen = RNDateTimePickerCGen_ModuleProvider(moduleName, params);
if (module_RNDateTimePickerCGen != nullptr) {
return module_RNDateTimePickerCGen;
}
auto module_rnskia = rnskia_ModuleProvider(moduleName, params);
if (module_rnskia != nullptr) {
return module_rnskia;
}
auto module_lottiereactnative = lottiereactnative_ModuleProvider(moduleName, params);
if (module_lottiereactnative != nullptr) {
return module_lottiereactnative;
}
auto module_Compressor = Compressor_ModuleProvider(moduleName, params);
if (module_Compressor != nullptr) {
return module_Compressor;
}
auto module_RNDatePickerSpecs = RNDatePickerSpecs_ModuleProvider(moduleName, params);
if (module_RNDatePickerSpecs != nullptr) {
return module_RNDatePickerSpecs;
}
auto module_rngesturehandler_codegen = rngesturehandler_codegen_ModuleProvider(moduleName, params);
if (module_rngesturehandler_codegen != nullptr) {
return module_rngesturehandler_codegen;
}
auto module_RNGoogleMobileAdsSpec = RNGoogleMobileAdsSpec_ModuleProvider(moduleName, params);
if (module_RNGoogleMobileAdsSpec != nullptr) {
return module_RNGoogleMobileAdsSpec;
}
auto module_RNCImageCropPickerSpec = RNCImageCropPickerSpec_ModuleProvider(moduleName, params);
if (module_RNCImageCropPickerSpec != nullptr) {
return module_RNCImageCropPickerSpec;
}
auto module_RNImagePickerSpec = RNImagePickerSpec_ModuleProvider(moduleName, params);
if (module_RNImagePickerSpec != nullptr) {
return module_RNImagePickerSpec;
}
auto module_pagerview = pagerview_ModuleProvider(moduleName, params);
if (module_pagerview != nullptr) {
return module_pagerview;
}
auto module_RNPermissionsSpec = RNPermissionsSpec_ModuleProvider(moduleName, params);
if (module_RNPermissionsSpec != nullptr) {
return module_RNPermissionsSpec;
}
auto module_rnreanimated = rnreanimated_ModuleProvider(moduleName, params);
if (module_rnreanimated != nullptr) {
return module_rnreanimated;
}
auto module_safeareacontext = safeareacontext_ModuleProvider(moduleName, params);
if (module_safeareacontext != nullptr) {
return module_safeareacontext;
}
auto module_rnscreens = rnscreens_ModuleProvider(moduleName, params);
if (module_rnscreens != nullptr) {
return module_rnscreens;
}
auto module_rnsvg = rnsvg_ModuleProvider(moduleName, params);
if (module_rnsvg != nullptr) {
return module_rnsvg;
}
auto module_RNVectorIconsSpec = RNVectorIconsSpec_ModuleProvider(moduleName, params);
if (module_RNVectorIconsSpec != nullptr) {
return module_RNVectorIconsSpec;
}
auto module_rnviewshot = rnviewshot_ModuleProvider(moduleName, params);
if (module_rnviewshot != nullptr) {
return module_rnviewshot;
}
auto module_RNCWebViewSpec = RNCWebViewSpec_ModuleProvider(moduleName, params);
if (module_RNCWebViewSpec != nullptr) {
return module_RNCWebViewSpec;
}
  return nullptr;
}

std::shared_ptr<TurboModule> autolinking_cxxModuleProvider(const std::string moduleName, const std::shared_ptr<CallInvoker>& jsInvoker) {

  return nullptr;
}

void autolinking_registerProviders(std::shared_ptr<ComponentDescriptorProviderRegistry const> providerRegistry) {
providerRegistry->add(concreteComponentDescriptorProvider<FastImageViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<BlurViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<AndroidBlurViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<VibrancyViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<SkiaPictureViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<LottieAnimationViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNDatePickerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGestureHandlerButtonComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGestureHandlerRootViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGoogleMobileAdsBannerViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGoogleMobileAdsMediaViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNGoogleMobileAdsNativeViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCViewPagerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCSafeAreaProviderComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCSafeAreaViewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSFullWindowOverlayComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenContainerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenNavigationContainerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackHeaderConfigComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackHeaderSubviewComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenStackComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSSearchBarComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenFooterComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSScreenContentWrapperComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSModalScreenComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGCircleComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGClipPathComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGDefsComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGFeBlendComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGFeColorMatrixComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGFeCompositeComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGFeFloodComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGFeGaussianBlurComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGFeMergeComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGFeOffsetComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGFilterComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGEllipseComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGForeignObjectComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGGroupComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGImageComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGLinearGradientComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGLineComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGMarkerComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGMaskComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGPathComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGPatternComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGRadialGradientComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGRectComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGSvgViewAndroidComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGSymbolComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGTextComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGTextPathComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGTSpanComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNSVGUseComponentDescriptor>());
providerRegistry->add(concreteComponentDescriptorProvider<RNCWebViewComponentDescriptor>());
  return;
}

} // namespace react
} // namespace facebook