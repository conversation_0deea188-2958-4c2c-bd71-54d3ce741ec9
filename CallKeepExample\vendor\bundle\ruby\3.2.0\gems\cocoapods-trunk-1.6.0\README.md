# CocoaPods::Trunk

[![Build Status](https://img.shields.io/github/workflow/status/CocoaPods/cocoapods-trunk/Specs)](https://github.com/CocoaPods/cocoapods-trunk/actions)
[![Maintainability](https://api.codeclimate.com/v1/badges/157b8b7f7b73976f3edf/maintainability)](https://codeclimate.com/github/CocoaPods/cocoapods-trunk/maintainability)

CocoaPods plugin for trunk.

## Installation

Add this line to your application's Gemfile:

    gem 'cocoapods-trunk'

And then execute:

    $ bundle

Or install it yourself as:

    $ gem install cocoapods-trunk

## Usage

With a local install of `trunk.cocoapods.org` up and running:

    $ env TRUNK_SCHEME_AND_HOST=http://localhost:4567 bundle exec pod trunk --help

## Contributing

1. Fork it
2. Create your feature branch (`git checkout -b my-new-feature`)
3. Commit your changes (`git commit -am 'Add some feature'`)
4. Push to the branch (`git push origin my-new-feature`)
5. Create new Pull Request

